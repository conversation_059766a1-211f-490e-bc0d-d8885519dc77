stages:
  - build
  - test
  - publish
  - container-scan
  - dev-server-spinup
  - set-cleanup-countdown
  - dev-server-setup
  - deploy
  - smoke-tests
  - notify

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == 'push' || $CI_PIPELINE_SOURCE == 'web'

variables:
  DEV_TOKEN: fjp7DoXUOaSSqZPkxIPIxUkw0ooCpDqki5ncv1OVFdCiXPTdPIpFCUnmlbDHMMMv
  IMAGE: registry.octimine.de:5000/$CI_PROJECT_NAME:$CI_PIPELINE_ID
  IMAGE_COPY_LOCAL: registry.octimine.de:5000/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME
  IMAGE_COPY_GITLAB: $CI_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME
  FULL_NAME: $CI_PROJECT_NAME--$CI_PIPELINE_ID--$CI_COMMIT_REF_NAME
  UPSTREAM_PIPELINE_ID: $CI_PIPELINE_ID
  SLACK_URL: $SLACK_DEV_INFRASTRUCTURE_WEBHOOK
  SECRET_DETECTION_REPORT_FILE: "gl-secret-detection-report.json"
  SAST_REPORT_FILE: "gl-sast-report.json"
  CONTAINER_SCANNING_REPORT_FILE: "gl-container-scanning-report.json"

include:
  # Including secret detection check
  - template: Security/Secret-Detection.gitlab-ci.yml
  # Including SemGrep check
  - template: Security/SAST.gitlab-ci.yml
  # Including container image check
  - template: Security/Container-Scanning.gitlab-ci.yml


default:
  before_script:
    - docker login -u $CI_DEPLOY_USER -p $CI_DEPLOY_PASSWORD $CI_REGISTRY

# IMAGE WORKAROUNDS
build-image:
  stage: build
  retry: 2
  tags:
    - frontend
  script:
    - export CONFIG="staging" && [[ $CI_COMMIT_REF_NAME == master ]] && export CONFIG="prod"
    - docker build --build-arg configuration=${CONFIG} --build-arg=CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME} --build-arg=CI_COMMIT_SHA=${CI_COMMIT_SHA} --target build-stage -t ${IMAGE}_test .
    - docker build --build-arg configuration=${CONFIG} --build-arg=CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME} --build-arg=CI_COMMIT_SHA=${CI_COMMIT_SHA} --target prod-stage -t $IMAGE .
  only:
    - staging
    - master

build-dev-image:
  stage: build
  retry: 2
  tags:
    - frontend
  script:
    - sed -i "s/pipeline_id/${CI_PIPELINE_ID}/g" ./src/settings/dev/settings.json
    - docker build --build-arg configuration=dev --build-arg=CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME} --build-arg=CI_COMMIT_SHA=${CI_COMMIT_SHA} --target build-stage -t ${IMAGE}_test .
    - docker build --build-arg configuration=dev --build-arg=CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME} --build-arg=CI_COMMIT_SHA=${CI_COMMIT_SHA} --target prod-stage -t $IMAGE .
  except:
    refs:
      - master
      - staging

lint:
  stage: test
  tags:
    - frontend
  before_script:
    - ''
  script:
    - docker run --entrypoint npm ${IMAGE}_test run lint
  rules:
    - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "staging" || $SKIP_TESTS == null

unit-test:
  stage: test
  tags:
    - frontend
  before_script:
    - ''
  script:
    - docker run --entrypoint npm ${IMAGE}_test run test-headless
  rules:
    - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "staging" || $SKIP_TESTS == null

e2e-test:
  stage: test
  tags:
    - frontend
  before_script:
    - ''
  script:
    - docker run --entrypoint npm ${IMAGE}_test run e2e-cy-headless
  rules:
    - if: $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "staging" || $SKIP_TESTS == null

# This stage evaluates usage of plain text secrets
secret_detection:
  stage: test
  tags:
    - infrastructure-docker
  before_script:
    - apk add --no-cache jq
  rules:
    - if: $CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "staging" && $CI_PIPELINE_SOURCE != "merge_request_event" && $SKIP_TESTS == null
  allow_failure:
    exit_codes:
      - 80
  script:
    - /analyzer run
    # check if '{ "vulnerabilities": [], ..' is empty in the report file if it exists
    - |
      if [ -f "$SECRET_DETECTION_REPORT_FILE" ]; then
        if [ "$(jq "[.vulnerabilities[].severity] | any(index(\"High\"))" $SECRET_DETECTION_REPORT_FILE)" = true ]; then
          echo "High vulnerabilities detected. Please analyze the artifact $SECRET_DETECTION_REPORT_FILE produced by the 'secret_detection' job."
          exit 80
        fi
        if [ "$(jq "[.vulnerabilities[].severity] | any(index(\"Critical\"))" $SECRET_DETECTION_REPORT_FILE)" = true ]; then
          echo "Critical vulnerabilities detected. Please analyze the artifact $SECRET_DETECTION_REPORT_FILE produced by the 'secret_detection' job."
          exit 1
        fi
      else
        echo "Artifact $SECRET_DETECTION_REPORT_FILE does not exist. The 'secret_detection' job likely didn't create one. Hence, no evaluation can be performed."
        exit 80
      fi

# This stage evaluates project code for different languages based on available SAST analysers
sast:
  stage: test
  rules:
    - when: never

semgrep-sast:
  stage: test
  tags:
    - infrastructure-docker
  before_script:
    - apk add --no-cache jq
  rules:
    - if: $CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "staging" && $CI_PIPELINE_SOURCE != "merge_request_event" && $SKIP_TESTS == null
  allow_failure:
    exit_codes:
      - 80
  script:
    - /analyzer run
    # check if '{ "vulnerabilities": [], ..' is empty in the report file if it exists
    - |
      if [ -f "$SAST_REPORT_FILE" ]; then
        if [ "$(jq "[.vulnerabilities[].severity] | any(index(\"High\"))" $SAST_REPORT_FILE)" = true ]; then
          echo "High vulnerabilities detected. Please analyze the artifact $SAST_REPORT_FILE produced by the 'semgrep-sast' job."
          exit 80
        fi
        if [ "$(jq "[.vulnerabilities[].severity] | any(index(\"Critical\"))" $SAST_REPORT_FILE)" = true ]; then
          echo "Critical vulnerabilities detected. Please analyze the artifact $SAST_REPORT_FILE produced by the 'semgrep-sast' job."
          exit 1
        fi
      else
        echo "Artifact $SAST_REPORT_FILE does not exist. The 'semgrep-sast' job likely didn't create one. Hence, no evaluation can be performed."
        exit 80
      fi

publish-dev-image:
  stage: publish
  tags:
    - infrastructure
  script:
    - docker push $IMAGE
  except:
    refs:
      - master
      - staging

publish-image:
  stage: publish
  tags:
    - infrastructure
  script:
    - docker tag $IMAGE $IMAGE_COPY_GITLAB
    - docker tag $IMAGE $IMAGE_COPY_LOCAL
    - docker push $IMAGE_COPY_GITLAB
    - docker push $IMAGE_COPY_LOCAL
  only:
    - staging
    - master

# This stage evaluates project container image for different languages based on available SAST analysers
container_scanning:
  stage: container-scan
  tags:
    - infrastructure-docker
  before_script:
    - sudo apt-get update -y && sudo apt-get install -y jq
  variables:
    # Image to be scanned
    CS_IMAGE: $IMAGE
    CS_REGISTRY_USER: $OCTIMINE_CI_REGISTRY_USER
    CS_REGISTRY_PASSWORD: "$OCTIMINE_CI_REGISTRY_PASSWORD"
    CS_SEVERITY_THRESHOLD: HIGH
    CS_QUIET: true
    CS_IGNORE_UNFIXED: true
  rules:
    - if: $CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "staging" && $CI_PIPELINE_SOURCE != "merge_request_event"
  allow_failure:
    exit_codes:
      - 80
  script:
    - gtcs scan
    # check if '{ "vulnerabilities": [], ..' is empty in the report file if it exists
    - |
      if [ -f "$CONTAINER_SCANNING_REPORT_FILE" ]; then
        if [ "$(jq "[.vulnerabilities[].severity] | any(index(\"High\") or index(\"Critical\"))" $CONTAINER_SCANNING_REPORT_FILE)" = true ]; then
          echo "High or Critical vulnerabilities detected. Please analyze the artifact $CONTAINER_SCANNING_REPORT_FILE produced by the 'container_scanning' job."
          exit 80
        fi
      else
        echo "Artifact $CONTAINER_SCANNING_REPORT_FILE does not exist. The 'container_scanning' job likely didn't create one. Hence, no evaluation can be performed."
        exit 80
      fi

# TERRAFORMING
terraforming-infrastructure:
  stage: dev-server-spinup
  trigger:
    project: octimine/operations/dev-infrastructure
    strategy: depend
  variables:
    STAGE: spinup
  except:
    refs:
      - master
      - staging

set-cleanup-countdown:
  stage: set-cleanup-countdown
  trigger:
    project: octimine/operations/dev-infrastructure
  variables:
    STAGE: cleanup
  except:
    refs:
      - master
      - staging


# SETUP DEV SERVER
setup-dev-server:
  stage: dev-server-setup
  retry: 2
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook -i /tmp/${CI_PIPELINE_ID} --skip-tags deploy,nginx,ssl,tests dev.yml
  except:
    refs:
      - master
      - staging

# DEPLOY
deploy-dev-env:
  stage: deploy
  retry: 2
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook -i /tmp/${CI_PIPELINE_ID} -e "project=${CI_PROJECT_NAME}" -e "version=${CI_PIPELINE_ID}" --tags deploy,nginx,ssl,docker_status --skip-tags smoke_tests dev.yml
  except:
    refs:
      - master
      - staging

deploy-staging:
  stage: deploy
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook -e "project=${CI_PROJECT_NAME}" --tags deploy_no_smoke,nginx,docker_status staging.yml
  only:
    - staging

deploy-production:
  stage: deploy
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook -e "project=${CI_PROJECT_NAME}" --tags deploy_no_smoke prod-worker-node.yml
  only:
    - master


# SMOKE TESTS
smoke-test-dev:
  stage: smoke-tests
  retry: 2
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook -i /tmp/${CI_PIPELINE_ID} -e "version=${CI_PIPELINE_ID}" --tags tests dev.yml
  rules:
    - if: $CI_COMMIT_BRANCH != "master" && $CI_COMMIT_BRANCH != "staging" && $SKIP_TESTS == null

smoke-test-staging:
  stage: smoke-tests
  retry: 2
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook --tags tests staging.yml
  only:
    - staging

smoke-test-prod:
  stage: smoke-tests
  retry: 2
  tags:
    - infrastructure
  before_script:
    - cd ~/ansible
    - git pull
  script:
    - ansible-playbook prod-worker-smoke-tests.yml
  only:
    - master

# Notify
slack-notify:
  stage: notify
  retry: 2
  tags:
    - infrastructure
  before_script:
    - sed -i -e "s/dev_user/${GITLAB_USER_NAME}/g" -e "s,dev_pipeline,${CI_PIPELINE_URL},g" /tmp/${CI_PIPELINE_ID}_slack.json
  script:
    - curl -X POST -H "Content-type:application/json" --data @/tmp/${CI_PIPELINE_ID}_slack.json $SLACK_URL
  except:
    refs:
      - master
      - staging
