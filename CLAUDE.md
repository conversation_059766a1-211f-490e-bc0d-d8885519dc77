# OCTIMINE Frontend Development Guidelines

## Build/Lint/Test Commands
- Start dev server: `npm start` or `ng serve`
- Build for staging: `npm run build:staging`
- Build for production: `npm run build:prod`
- Lint: `npm run lint`
- Run tests: `npm test`
- Run single test: `ng test --include=src/path/to/file.spec.ts`
- Run e2e tests: `npm run e2e-cy-headless`

## Code Style Guidelines
- **Components**: Use kebab-case for filenames, prefix with 'app-', suffix with type (.component.ts)
- **Imports**: Use path aliases (@core/*, @shared/*, etc.) defined in tsconfig.json
- **Naming**: 
  - Components/Directives: Use camelCase for properties, methods, kebab-case for selectors
  - Services: PascalCase with 'Service' suffix
- **Structure**: Follow Angular module pattern with feature-based directory organization
- **Style**: Use SCSS for component styles (each component has its own .scss file)
- **Types**: Always use strict typing, avoid 'any' type
- **Error Handling**: Use services/interceptors for global error handling