# Stage 0, Build stage
FROM registry.gitlab.com/octimine/shared_registry/octimine-nodejs:v22 AS build-stage

WORKDIR /app
COPY package*.json /app/
COPY .npmrc /app/
RUN npm ci --unsafe-perm
COPY ./ /app/
ARG configuration=staging
ARG CI_COMMIT_REF_NAME=""
ARG CI_COMMIT_SHA=""
ENV CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME}
ENV CI_COMMIT_SHA=${CI_COMMIT_SHA}
RUN npm run build:$configuration

# Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1.27.5 AS prod-stage
COPY --from=build-stage /app/dist/octimine-webapp/ /usr/share/nginx/html/
COPY src/error.html /usr/share/nginx/html/
# Copy the default nginx.conf
COPY ./nginx-docker.conf /etc/nginx/conf.d/default.conf
