# Jest Testing Framework Setup

This document describes the Jest testing framework configuration for the Angular project.

## Overview

Jest has been configured as an alternative testing framework alongside the existing Karma + Jasmine setup. This allows you to choose between different testing approaches based on your needs.

## Configuration Files

### 1. `jest.config.js`
Main Jest configuration file that includes:
- Angular preset configuration
- Module name mapping for TypeScript paths
- Coverage settings
- Transform configurations
- Test file patterns

### 2. `setup-jest.ts`
Jest setup file that includes:
- Angular testing environment setup
- Global mocks for browser APIs
- Custom matchers
- Test utilities

### 3. `src/tsconfig.spec.json`
Updated TypeScript configuration for tests that includes Jest types.

## Available Scripts

### Jest Testing Scripts
- `npm run test:jest` - Run Jest tests once
- `npm run test:jest:watch` - Run Jest tests in watch mode
- `npm run test:jest:coverage` - Run Jest tests with coverage report
- `npm run test:jest:ci` - Run Jest tests in CI mode (no watch, with coverage)

### Existing Karma Scripts (still available)
- `npm run test` - Run Karma tests with Chrome
- `npm run test-headless` - Run Karma tests headless

## Writing Tests

### Jest Test Files
Create test files with `.jest.spec.ts` extension or use `.test.ts` for Jest-specific tests.

Example:
```typescript
import { TestBed } from '@angular/core/testing';
import { MyComponent } from './my-component';

describe('MyComponent (Jest)', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [MyComponent]
    }).compileComponents();
  });

  it('should create', () => {
    const fixture = TestBed.createComponent(MyComponent);
    const component = fixture.componentInstance;
    expect(component).toBeTruthy();
  });
});
```

### Test Utilities
Use the Jest utilities from `src/testing/jest-utils.ts`:

```typescript
import { getElement, clickElement, setInputValue } from '../testing/jest-utils';

it('should handle user interaction', () => {
  const fixture = TestBed.createComponent(MyComponent);
  
  // Set input value
  setInputValue(fixture, 'input[name="username"]', 'testuser');
  
  // Click button
  clickElement(fixture, 'button[type="submit"]');
  
  // Assert result
  const result = getElement(fixture, '.result');
  expect(result?.textContent).toContain('Success');
});
```

## Mocking

### HTTP Requests
```typescript
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';

beforeEach(() => {
  TestBed.configureTestingModule({
    imports: [HttpClientTestingModule],
    // ...
  });
});
```

### Services
```typescript
const mockService = {
  getData: jest.fn().mockReturnValue(of(mockData)),
  saveData: jest.fn().mockReturnValue(of({}))
};

beforeEach(() => {
  TestBed.configureTestingModule({
    providers: [
      { provide: MyService, useValue: mockService }
    ]
  });
});
```

### Components
```typescript
import { createMockComponent } from '../testing/jest-utils';

const MockChildComponent = createMockComponent('app-child', ['input'], ['output']);

beforeEach(() => {
  TestBed.configureTestingModule({
    declarations: [ParentComponent, MockChildComponent]
  });
});
```

## Coverage

Coverage reports are generated in the `coverage/` directory when running:
```bash
npm run test:jest:coverage
```

Coverage includes:
- HTML report: `coverage/lcov-report/index.html`
- LCOV format: `coverage/lcov.info`
- Text summary in console

## Best Practices

1. **File Naming**: Use `.jest.spec.ts` for Jest-specific tests to distinguish from Karma tests
2. **Setup**: Use `beforeEach` for test setup and `afterEach` for cleanup
3. **Mocking**: Mock external dependencies and browser APIs
4. **Assertions**: Use Jest matchers for better error messages
5. **Async Testing**: Use `async/await` for asynchronous operations

## Troubleshooting

### Common Issues

1. **Module Resolution**: If modules aren't found, check the `moduleNameMapper` in `jest.config.js`
2. **Transform Errors**: Ensure all file types are covered in the `transform` configuration
3. **Timeout Issues**: Increase `testTimeout` in `jest.config.js` if needed
4. **Mock Issues**: Check that all required browser APIs are mocked in `setup-jest.ts`

### Debug Mode
Run Jest with debug information:
```bash
npx jest --verbose --no-cache
```

## Migration from Karma

To migrate existing Karma tests to Jest:

1. Copy the test file and rename with `.jest.spec.ts`
2. Update imports if needed
3. Replace Jasmine-specific syntax with Jest equivalents
4. Update mocking syntax
5. Run the test to ensure it passes

## Integration with CI/CD

For continuous integration, use:
```bash
npm run test:jest:ci
```

This runs tests once with coverage and no watch mode, suitable for CI environments.
