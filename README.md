# OctimineWebapp

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 6.2.1.

## Development server

Run `npm run start` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `npm run build:{env}` to build the project. The build artifacts will be stored in the `dist/` directory.

```bash
# Local environment
$ npm run build

# Staging environment
$ npm run build:staging

# Production environment
$ npm run build:prod
```

## Running unit tests

Run `ng test` or `npm run test` to execute the unit tests via [Karma](https://karma-runner.github.io) with default browser Firefox.

You could run using a different browser, like Chrome with this `ng test --browsers=Chrome` or in headless (no browser window) mode using this `ng test --browsers ChromeHeadless`.

You could also run a single test file using this `ng test --include='**/your-file.spec.ts'`.

## Running end-to-end tests

Run `npx cypress run --project ./e2e --config video=false` or `npx cypress run --project ./e2e --spec ./e2e/cypress/e2e/6-custom-tags.cy.ts` to execute the end-to-end tests via [Cypress](https://docs.cypress.io/guides/guides/command-line).

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI README](https://github.com/angular/angular-cli/blob/master/README.md).
