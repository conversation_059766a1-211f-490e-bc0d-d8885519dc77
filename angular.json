{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "cli": {
    "analytics": false,
    "schematicCollections": [
      "@angular-eslint/schematics"
    ]
  },
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "octimine-webapp": {
      "root": "",
      "sourceRoot": "src",
      "projectType": "application",
      "prefix": "app",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        }
      },
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/octimine-webapp",
            "index": "src/index.html",
            "main": "src/main.ts",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "src/tsconfig.app.json",
            "assets": [
              "src/favicon.ico",
              "src/assets",
              {
                "glob": "**/*",
                "input": "node_modules/ngx-extended-pdf-viewer/assets/",
                "output": "/assets/"
              }
            ],
            "styles": [
              "node_modules/@ng-select/ng-select/themes/default.theme.css",
              "node_modules/flag-icons/sass/flag-icons.scss",
              "src/scss/main.scss"
            ],
            "stylePreprocessorOptions": {
              "includePaths": ["src/scss"]
            },
            "scripts": [
              "node_modules/xlsx/dist/xlsx.mini.min.js",
              "node_modules/jquery/dist/jquery.min.js",
              "node_modules/bootstrap/dist/js/bootstrap.bundle.js",
              "node_modules/@fortawesome/fontawesome-pro/js/all.js",
              "node_modules/ace-builds/src-noconflict/ace.js",
              "node_modules/ace-builds/src-noconflict/ext-language_tools.js"
            ],
            "allowedCommonJsDependencies": [
              "rxjs",
              "highcharts",
              "lodash",
              "jquery",
              "core-js",
              "jszip",
              "mark.js",
              "copy-to-clipboard",
              "ace-builds",
              "pluralize",
              "sanitize-html"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true
          },
          "configurations": {
            "production": {
              "budgets": [
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "80kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.prod.ts"
                }
              ],
              "assets": [
                "src/favicon.ico",
                "src/assets",
                {
                  "input": "src/settings/production",
                  "output": "/assets",
                  "glob": "*.json"
                },
                {
                  "glob": "**/*",
                  "input": "node_modules/ngx-extended-pdf-viewer/assets/",
                  "output": "/assets/"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true
            },
            "staging": {
              "budgets": [
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "80kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.staging.ts"
                }
              ],
              "assets": [
                "src/favicon.ico",
                "src/assets",
                {
                  "input": "src/settings/staging",
                  "output": "/assets",
                  "glob": "*.json"
                },
                {
                  "glob": "**/*",
                  "input": "node_modules/ngx-extended-pdf-viewer/assets/",
                  "output": "/assets/"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true
            },
            "dev": {
              "budgets": [
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "80kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.dev.ts"
                }
              ],
              "assets": [
                "src/favicon.ico",
                "src/assets",
                {
                  "input": "src/settings/dev",
                  "output": "/assets",
                  "glob": "*.json"
                },
                {
                  "glob": "**/*",
                  "input": "node_modules/ngx-extended-pdf-viewer/assets/",
                  "output": "/assets/"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true
            }
          },
          "defaultConfiguration": ""
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "buildTarget": "octimine-webapp:build"
          },
          "configurations": {
            "production": {
              "buildTarget": "octimine-webapp:build:production"
            }
          }
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "octimine-webapp:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "src/test.ts",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "src/tsconfig.spec.json",
            "karmaConfig": "src/karma.conf.js",
            "styles": [
              "node_modules/flag-icons/sass/flag-icons.scss",
              "src/scss/main.scss"
            ],
            "stylePreprocessorOptions": {
              "includePaths": ["src/scss"]
            },
            "scripts": [
              "node_modules/jquery/dist/jquery.min.js",
              "node_modules/bootstrap/dist/js/bootstrap.bundle.min.js",
              "node_modules/@fortawesome/fontawesome-pro/js/all.js",
            ],
            "assets": [
              "src/favicon.ico",
              "src/assets"
            ]
          }
        },
        "test:jest": {
          "builder": "@angular-builders/jest:run",
          "options": {
            "configPath": "./jest.config.js"
          }
        },
        "lint": {
          "builder": "@angular-eslint/builder:lint",
          "options": {
            "lintFilePatterns": [
              "src/**/*.ts"
            ]
          }
        }
      }
    },
    "octimine-webapp-e2e": {
      "root": "e2e/",
      "projectType": "application",
      "architect": {
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "e2e/protractor.conf.js",
            "devServerTarget": "octimine-webapp:serve"
          },
          "configurations": {
            "production": {
              "devServerTarget": "octimine-webapp:serve:production"
            }
          }
        }
      }
    }
  },
  "schematics": {
    "@angular-eslint/schematics:application": {
      "setParserOptionsProject": true
    },
    "@angular-eslint/schematics:library": {
      "setParserOptionsProject": true
    }
  }
}
