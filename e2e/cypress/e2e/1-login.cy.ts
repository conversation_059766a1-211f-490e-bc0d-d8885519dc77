import { PASSWORD, USER } from '../utils/data';
import { LoginPage } from '../pages/login.page';

describe('Login page tests', () => {
  let page: LoginPage;

  beforeEach(() => {
    page = new LoginPage();
    page.navigateTo();
  });

  it('should redirect to login page', () => {
    cy.go('forward');
    cy.get('#email').should('be.visible');
  });

  it('should redirect to launch pad after successful login', () => {
    page.getLoginForm().should('be.visible');
    page.login(USER, PASSWORD, true, true, true);
    cy.url().should('include', 'launchpad');
    page.logout();
  });

  it('should display message for invalid credentials', () => {
    page.login('<EMAIL>', 'invalid_password', false, true);
    cy.get('.alert').get('.alert-danger').should('be.visible');
  });

  it('should display message for invalid email', () => {
    page.login('invalid_user', 'invalid_password', false, false);
    cy.get('.invalid-feedback').should('be.visible');
  });

  it('should display message for empty credentials', () => {
    page.login(' ', ' ', false, false);
    cy.get('.invalid-feedback').should('be.visible');
  });
});

