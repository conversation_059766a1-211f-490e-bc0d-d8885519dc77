import { PASSWORD, BOOLEAN_QUERY_1, BOOLEAN_QUERY_2, USER } from '../utils/data';
import { BooleanSearchPage } from '../pages/boolean-search.page';
import { LoginPage } from '../pages/login.page';

describe('Boolean Search Tests', () => {
  let page: BooleanSearchPage;

  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new BooleanSearchPage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    loginPage.waitUserSignInQueryComplete();
    page.booleanSearchInput.should('be.visible');
  });

  it('should display search results and charts with simple mode', () => {
    page.clearBooleanInputBtn.click();
    page.advModeBtn.should('have.text', 'Switch to advanced mode >>>');
    page.searchButton.should('be.disabled');
    page.booleanSearchAdvanceInput.should('not.exist');
    page.advancedSearchButton.should('not.exist');

    page.booleanSearch(BOOLEAN_QUERY_1);
    page.resultTitle.should('contain', /Showing top [0-9]+ out of [0-9]+ total results/);
    page.countCharts.should('eq', 5);
  });

  it('should display search results and charts with advance mode', () => {
    page.toAdvancedMode();
    page.advModeBtn.should('have.text', 'Switch to simple mode >>>');
    page.booleanSearchAdvanceInput.should('be.visible');
    page.booleanSearchInput.should('not.exist');
    page.searchButton.should('not.exist');
    page.advancedSearchButton.should('be.disabled');

    page.booleanAdvanceSearch(BOOLEAN_QUERY_2);
    page.resultTitle.should('contain', /Showing [0-9]+ results/);
    page.gotoChartMode();
    page.countCharts.should('eq', 5);
  });

  it.skip('should display previous mode when going back from patent viewer',
    { defaultCommandTimeout: 120000 }, () => {
    page.displaySimpleModeFromPatentViewer();
    page.displayAdvancedModeFromPatentViewer();
    page.displaySimpleModeFromPatentViewer();
    page.displayAdvancedModeFromPatentViewer();
  });
});
