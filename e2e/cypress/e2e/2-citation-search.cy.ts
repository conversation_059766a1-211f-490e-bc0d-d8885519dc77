import { PASSWORD, PATENT_NUMBERS, USER } from '../utils/data';
import { CitationPage } from '../pages/citation-search.page';
import { LoginPage } from '../pages/login.page';

describe('Citation search tests', () => {
    let page: CitationPage;

    beforeEach(() => {
        const loginPage = new LoginPage();
        page = new CitationPage();
        page.navigateTo();
        loginPage.login(USER, PASSWORD, false);
        loginPage.waitUserSignInQueryComplete();
        page.searchInput.should('be.visible');
    });

    it('should display citations and charts', () => {
        page.search(PATENT_NUMBERS.join(', '));
        page.resultsTable.should('exist');
        page.countResults.should('eq', 24);
        page.gotoChartMode();
        page.charts.should('exist');
        page.allCharts.should('eq', 8);
    });

    it('should filter citation results', () => {
        page.level_2.click();
        page.references_only.click();
        page.search(PATENT_NUMBERS.join(', '));
        page.countResults.should('eq', 25);
        page.totalItems.should('have.text', '28');
        page.allCharts.should('eq', 8);
    });

});
