import { PASSWORD, SEARCH_QUERY_1, USER } from '../utils/data';
import { SearchPage } from '../pages/semantic-search.page';
import { LoginPage } from '../pages/login.page';

describe('Semantic search tests', () => {
  let page: SearchPage;

  beforeEach(() => {
    cy.mockUserSettings();
    const loginPage = new LoginPage();
    page = new SearchPage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    loginPage.waitUserSignInQueryComplete();
    page.searchInput.should('be.visible');
  });

  it('should display results, charts, paginate and sort results', () => {
    page.search(SEARCH_QUERY_1, true);
    page.resultsTable.should('exist');
    page.focalTable.should('not.exist');
    page.countResults.should('eq', 25);
    page.countPages.should('eq', 10);

    page.goToPage('2');
    page.sortByColumn('Title');
    page.sortByColumn('Title');
    page.sortByColumn('Publ. No.');
    page.countResults.should('eq', 25);
    page.countPages.should('eq', 10);

    page.gotoChartMode();
    page.goToChartSection('Classification');
    page.classificationCharts.should('exist');
    page.getCountCharts(page.classificationCharts).should('eq', 8);
  });

  it('should search by patent numbers', () => {
    page.searchInput.type('EP5677 ' + SEARCH_QUERY_1 + ' EP5678.', { delay: 0 });
    page.patentNumberInput.should('be.visible');
    page.patentNumberInput.should('have.text', 'EP5677 EP5678');
    page.cleanTextButton.click();
    page.searchButton.click();
    page.resultsTable.should('exist');
    page.focalTable.should('exist');
  });

  it('should be able to search with filters', () => {
    page.filter.click(); // Expand filters
    page.setInputValue('earliest_priority_date', '01/01/2009');
    page.setInputValue('latest_priority_date', '31/12/2019');
    page.setInputValue('earliest_publication_date', '01/01/2009');
    page.setInputValue('latest_publication_date', '31/12/2019');
    page.setInputValue('applicants_plus', 'SEARETE, TERRAPOWER');
    page.setInputValue('applicants_minus', 'SIEMENS');
    page.setSelectValue('cpc_minus', 'B41B');
    page.setSelectValue('ipc_minus', 'A24C');
    page.setSelectValue('ipc_plus', 'G21C');
    page.setInputValue('quantity_cut_off', '500');
    page.setInputValue('similarity_cut_off', '120');
    page.setInputValue('inclusion_and', 'thorium');
    page.setInputValue('inclusion_or', 'neutron, proton');
    page.setInputValue('exclusion_and', 'hydrogen');
    page.setInputValue('exclusion_or', 'calcium, helium');
    page.buttonConfirmFilters.click();
    page.search(SEARCH_QUERY_1);
    page.resultsTable.should('exist');
    page.countResults.should('be.gt', 0);
    page.gotoChartMode();
    page.goToChartSection('Classification');
    page.getCountCharts(page.classificationCharts).should('eq', 8);
  });

  it('should be able to search with boost factors', () => {
    page.boost.click(); // Expand boost
    page.setSelectValue('keywords', 'neutronic magnesium');
    page.setSelectValue('ipcCodes', 'G21C1/22');
    page.setSelectValue('cpcCodes', 'Y02E30/30');
    page.search(SEARCH_QUERY_1);
    page.resultsTable.should('exist');
    page.countResults.should('be.gt', 0);
    page.gotoChartMode();
    page.goToChartSection('Classification');
    page.getCountCharts(page.classificationCharts).should('eq', 8);
  });
});

describe('Limited account tests', () => {
  let page: SearchPage;

  beforeEach(() => {
    cy.mockUserSettings();
    const loginPage = new LoginPage();
    page = new SearchPage();
    page.navigateTo();
    loginPage.login('<EMAIL>', 'Abcd+12345', false);
    loginPage.waitUserSignInQueryComplete();
  });

  it('should display results and charts', () => {
    page.searchInput.should('be.visible');
    page.search(SEARCH_QUERY_1);
    page.resultsTable.should('exist');
    page.focalTable.should('not.exist');
  });
});
