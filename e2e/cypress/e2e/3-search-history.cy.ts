import { PASSWORD, SEARCH_QUERY_2, USER } from '../utils/data';
import { SearchPage } from '../pages/semantic-search.page';
import { SearchHistoryPage } from '../pages/search-history.page';
import { LoginPage } from '../pages/login.page';

describe('Search History tests', () => {
  let page: SearchHistoryPage;
  let searchPage: SearchPage;
  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new SearchHistoryPage();
    searchPage = new SearchPage();
    page.navigateToHistory();
    loginPage.login(USER, PASSWORD, false);
  });

  it('should display search history', () => {
    page.countResultsHistorySave.should('be.gt', 0);
  });

  it('should filter search history', () => {
    page.filter('Molten');
    page.countResultsHistorySave.should('eq', 25);

    page.filter('****');
    page.resultsHistorySave.should('not.exist');
  });

  it('should be able to paginate', () => {
    page.goToPage('2');
    page.countResultsHistorySave.should('eq', 25);
  });


});

describe('Search request test', () => {
  let page: SearchHistoryPage;
  let searchPage: SearchPage;

  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new SearchHistoryPage();
    searchPage = new SearchPage();
    searchPage.navigateTo();
    loginPage.login(USER, PASSWORD, false);
  });

  it('should log and load search', () => {
    searchPage.search(SEARCH_QUERY_2);

    page.navigateToHistory();

    page.loadSearch();
    page.resultsTable.should('exist');
  });
});
