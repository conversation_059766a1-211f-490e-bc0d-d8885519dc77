import { BooleanSearchPage } from "../pages/boolean-search.page";
import { LoginPage } from "../pages/login.page";
import { ResultCollectorPage } from "../pages/result-collector.page";
import { BOOLEAN_QUERY_2, PASSWORD, USER } from "../utils/data";

describe('Patent control bar tests', () => {
  let loginPage: LoginPage;
  let resultCollectorPage: ResultCollectorPage;
  let page;
  let collectionName: string;

  beforeEach(() => {
    loginPage = new LoginPage();
    resultCollectorPage = new ResultCollectorPage();
    page = new BooleanSearchPage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
  });

  it('should save search results', () => {
    page.toAdvancedMode();
    page.booleanSearchAdvanceInput.should('be.visible');
    page.booleanAdvanceSearch(BOOLEAN_QUERY_2);
    page.resultTitle.should('contain', /Showing [0-9]+ results/);

    const [fn, cm] = page.saveIntoCollection('Boolean Search', false, true);
    collectionName = cm;
    resultCollectorPage.navigateTo();
    resultCollectorPage.getCollectionTitleByName(collectionName).should('exist');
    resultCollectorPage.removeFoldersAndCollections([], [collectionName]);
    for (const name of [collectionName]) {
      resultCollectorPage.filter(name);
      resultCollectorPage.noCollectionFilterAlert.should('exist');
    }
  });
});
