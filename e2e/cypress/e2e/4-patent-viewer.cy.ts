import { PatentViewerPage } from '../pages/patent-viewer.page';
import { PASSWORD, PATENT_NUMBERS, USER } from '../utils/data';
import { SearchPage } from '../pages/semantic-search.page';
import { LoginPage } from '../pages/login.page';

describe('Patent viewer tests', () => {
  let patentViewerPage: PatentViewerPage;

  beforeEach(() => {
    const loginPage = new LoginPage();
    patentViewerPage = new PatentViewerPage();
    patentViewerPage.navigateToPatent();
    loginPage.login(USER, PASSWORD, false);
  });

  it('should display patent viewer page', () => {
    patentViewerPage.patentTitle.should('be.visible');
    patentViewerPage.backButton.should('not.exist');

    patentViewerPage.checkPatentFamilyDropdown();
  });

  it('should display patent viewer comment section', () => {
    patentViewerPage.interceptGetRatingsQuery();
    patentViewerPage.patentTitle.should('be.visible');
    patentViewerPage.backButton.should('not.exist');

    patentViewerPage.commentsButton.should('be.visible');
    patentViewerPage.waitGetRatingsQueryComplete();
    patentViewerPage.openComments();
    patentViewerPage.commentsSection.should('be.visible');
    patentViewerPage.checkComments();
  });

  it('should display patent viewer highlight section', () => {
    patentViewerPage.interceptGetRatingsQuery();
    patentViewerPage.patentTitle.should('be.visible');
    patentViewerPage.backButton.should('not.exist');

    patentViewerPage.highlightsButton.should('be.visible');
    patentViewerPage.waitGetRatingsQueryComplete();
    patentViewerPage.openHighlights();
    patentViewerPage.highlightsSection.should('be.visible');
    patentViewerPage.checkHighlights();
  });

  it('should display patent viewer ratings section', () => {
    patentViewerPage.patentTitle.should('be.visible');
    patentViewerPage.backButton.should('not.exist');

    patentViewerPage.ratingsButton.should('be.visible');

    cy.wait(3000);
    cy.get('#button-patent-ratings').then(($button) => {
      if (!$button.hasClass('active')) {
        patentViewerPage.openRatings();
      }
    });
    patentViewerPage.ratingsSection.should('be.visible');
    patentViewerPage.checkRatings();
  });


});

describe('Patent viewer tests', () => {
  let patentViewerPage: PatentViewerPage;
  let searchPage: SearchPage;

  beforeEach(() => {
    const loginPage = new LoginPage();
    searchPage = new SearchPage();
    searchPage.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    searchPage.searchInput.should('be.visible');
    searchPage.search(PATENT_NUMBERS[0]);

    patentViewerPage = new PatentViewerPage();
  });

  it('should display patent viewer current list section', () => {
    searchPage.firstResult.should('be.visible');
    searchPage.navigateToFirstResult();

    patentViewerPage.currentListButton.should('be.visible');
    patentViewerPage.currentListButton.click();
    patentViewerPage.currentListSection.should('be.visible');
    patentViewerPage.checkCurrentList()

    patentViewerPage.backToSearch();
    searchPage.firstResult.should('be.visible');
  });

  it('should display patent viewer relevant part section', () => {
    searchPage.firstResult.should('be.visible');
    searchPage.navigateToFirstResult();

    patentViewerPage.relevantPartsButton.should('be.visible');
    patentViewerPage.relevantPartsButton.click();
    patentViewerPage.relevantPartsSection.should('be.visible');
    patentViewerPage.checkRelevantPart()
  });
});
