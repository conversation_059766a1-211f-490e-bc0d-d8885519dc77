import { PASSWORD, PATENT_NUMBERS, USER, USER_TEAM_MEMBER } from '../utils/data';
import { LoginPage } from '../pages/login.page';
import { ResultCollectorPage } from '../pages/result-collector.page';
import { generateUniqueName } from '../utils/util';

describe('Result Collector tests', () => {
  let resultCollectorPage: ResultCollectorPage;
  let loginPage: LoginPage;
  let collectionNameReference: string;
  let collectionShareLink: string;

  beforeEach(() => {
    cy.intercept('GET', /\/api\/v2.0\/auth\/user\/([0-9]+)\/image$/, { statusCode: 404})
    loginPage = new LoginPage();
    resultCollectorPage = new ResultCollectorPage();
    resultCollectorPage.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    resultCollectorPage.actionsBtn.contains('NEW LIST').should('be.visible');
  });

  it('should create new collection', () => {
    const collectionName = generateUniqueName('Collection');
    resultCollectorPage.createCollection(collectionName);
    resultCollectorPage.inputName.should('not.exist');
    resultCollectorPage.getCollectionTitleByName(collectionName).should('be.visible');
    resultCollectorPage.removeCollectionsByNames([collectionName]);
    resultCollectorPage.filter(collectionName);
    resultCollectorPage.noCollectionFilterAlert.should('exist');
  });

  it('should create new folder and new collection inside folder', () => {

    const folderName = generateUniqueName('Folder');
    const collectionName = generateUniqueName('Collection');

    resultCollectorPage.createFolder(folderName);
    resultCollectorPage.inputName.should('not.exist');
    resultCollectorPage.getFolderTitleByName(folderName).should('be.visible');
    resultCollectorPage.gotoFolder(folderName);
    resultCollectorPage.createCollection(collectionName);
    resultCollectorPage.getCollectionTitleByName(collectionName).should('be.visible');

    resultCollectorPage.goBack();
    resultCollectorPage.removeFoldersByNames([folderName]);
    resultCollectorPage.filter(folderName);
    resultCollectorPage.noCollectionFilterAlert.should('exist');
  });

  it('should share collection', () => {
    collectionNameReference = generateUniqueName('Collection');
    resultCollectorPage.createCollection(collectionNameReference, PATENT_NUMBERS.join());
    resultCollectorPage.getCollectionTitleByName(collectionNameReference).should('exist');

    resultCollectorPage.openCollectionByName(collectionNameReference);
    resultCollectorPage.getControlBarOptionByName('Share').should('exist');

    // share with team member
    resultCollectorPage.shareCollectionWith(USER_TEAM_MEMBER);
    // share as a link
    resultCollectorPage.shareCollectionViaLink('Shared');
    resultCollectorPage.getControlBarOptionByName('Shared').should('exist');


    // check share collection link
    loginPage.logout();
    loginPage.getLoginForm().should('be.visible');
    cy.then(function () { collectionShareLink = this.sharedLink; cy.visit(collectionShareLink); })

    resultCollectorPage.getCollectionPageTitle(collectionNameReference).should('exist')

    // share collection link login as other user whom e2e has shard collection
    loginPage.navigateTo();
    loginPage.getLoginForm().should('be.visible');
    loginPage.login(USER_TEAM_MEMBER, PASSWORD);
    resultCollectorPage.navigateTo();
    resultCollectorPage.actionsBtn.contains('NEW LIST').should('be.visible');
    resultCollectorPage.getSharedListDot().should('exist');

    resultCollectorPage.getCollectionTitleByName(collectionNameReference).should('exist');
  });

  it('should unshare collection', () => {
      resultCollectorPage.filter(collectionNameReference);
      resultCollectorPage.getSharedIconOnCard().should('exist');

      resultCollectorPage.removeCollectionsByNames([collectionNameReference]);
      resultCollectorPage.filter(collectionNameReference);
      resultCollectorPage.noCollectionFilterAlert.should('exist');

      // share link should not work
      cy.visit(collectionShareLink);
      cy.url({timeout: 20000}).should('include', '/error/403');
      resultCollectorPage.getCollectionPageTitle(collectionNameReference).should('not.exist');

      // login as other user whom e2e has shard collection
      loginPage.navigateTo();
      loginPage.login(USER_TEAM_MEMBER, PASSWORD, false);
      resultCollectorPage.navigateTo();
      resultCollectorPage.getCollectionPageTitle(collectionNameReference).should('not.exist');
  });

  it('should display annotated documents', () => {
    resultCollectorPage.interceptAnnotationDocumentsQuery();
    resultCollectorPage.annotatedTab.should('be.visible').click();
    resultCollectorPage.waitAnnotationDocumentsQueryComplete();
    resultCollectorPage.countAnnotatedDocuments.should('eq', 3);
  });

  it('should display opened documents', () => {
    resultCollectorPage.interceptReadDocumentsQuery();
    resultCollectorPage.openedTab.should('be.visible').click();
    resultCollectorPage.waitReadDocumentsQueryComplete();
    resultCollectorPage.countOpenedDocuments.should('eq', 25);
  });
});
