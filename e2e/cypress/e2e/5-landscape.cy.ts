import { LandscapePage } from '../pages/landscape.page';
import { generateUniqueName } from '../utils/util';
import { LoginPage } from '../pages/login.page';
import { PASSWORD, PATENT_NUMBERS, USER } from '../utils/data';

describe('Landscape Tests', () => {
  let page: LandscapePage;
  let profileName = generateUniqueName('');

  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new LandscapePage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
  });

  it('should create landscape profile from patent number', () => {
    page.navigateToNewProfileForm();
    page.checkFormTitle('CREATE LANDSCAPE');

    page.form.submit()
    cy.get('.invalid-feedback').its('length').should('eq', 2);

    page.createNewLandscapeProfile(profileName, PATENT_NUMBERS.join(', '));
    cy.get('.card-title', {timeout: 20000}).should('contain.text', profileName).should('be.visible');
    
    // compute profile
    page.computeButton.should('be.visible');
    page.computeButton.click();
    cy.url({timeout: 20000}).should('contain', '/landscape/profile/');
    page.documentsTable.should('be.visible');
    page.documentsPagination.should('exist');
    page.chartsTitle.should('contain.text', 'Visual analysis');
    page.countLandscapeCharts().should('eq', 5);

    // check chart filter
    page.gotoChartMode();
    cy.get('.box-bibliographic .btn-highly-cited').click();
    cy.get('app-filters-bar').should('exist');
    cy.get('app-filters-bar .filter-item .remove-filter-button').click()
    cy.get('app-filters-bar').should('not.visible');
  });

  it('should edit landscape profile', () => {
    const profile = page.getLandscapeProfileByName(profileName);

    page.clickEditMenu(profile);
    page.checkFormTitle('UPDATE LANDSCAPE');

    cy.mockUserSettings();
    page.addSemanticQuery();
    page.addBooleanQuery();
    cy.get('.landscape-profile-form').submit();
  });

  it('should delete landscape profile', () => {
    const profile = page.getLandscapeProfileByName(profileName);
    page.deleteProfile(profile, profileName);
  });
});
