import { MonitorPage } from '../pages/monitor.page';
import { LoginPage } from '../pages/login.page';
import { PASSWORD, SEARCH_QUERY_2, USER, PATENT_NUMBERS } from '../utils/data';
import { generateUniqueName } from '../utils/util';
import { ModalDialogEle, ModalDialogOptions } from '../utils/modal-dialog.ele';

describe('Monitoring tests', () => {
  let page: MonitorPage;
  const profileName = generateUniqueName('');

  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new MonitorPage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    loginPage.waitUserSignInQueryComplete();
  });

  it('should create monitor profile', () => {
    page.newProfileBtn.should('be.visible');
    page.navigateToNewProfile();
    page.createNewMonitorProfile(profileName);
  });

  it('should edit monitor profile with semantic, Boolean and Deeplearning query', () => {
    page.newProfileBtn.should('be.visible');
    page.getMonitorProfileByName(profileName).should('be.visible');
    page.openProfile(profileName);
    page.goToMonitoringMethod();
    page.profileMethods.should('be.visible');
    page.profileMethods.contains('a', 'Semantic').click();
    page.semantic.find('app-semantic-input').should('be.visible');

    cy.mockUserSettings();
    page.addSemanticQuery(SEARCH_QUERY_2);
    page.semantic.find('app-patent-table').should('be.visible');
    page.semantic.find('app-semantic-input').should('be.visible');
    page.semantic.find('#searchText').should('be.visible');
    page.semantic.contains('button', 'SETUP MONITORING').should('be.visible');


    page.profileMethods.contains('a', 'Boolean').click();
    page.boolean.find('app-boolean-input').should('be.visible');
    page.addBooleanQuery();
    page.boolean.find('app-patent-table').should('be.visible');
    page.boolean.contains('button', 'SETUP MONITORING').should('be.enabled');


    page.profileMethods.contains('a', 'Deep learning').click();
    page.deeplearning.find('app-patent-list-input').should('be.visible');
    page.addDeeplearningQuery(PATENT_NUMBERS.join(', '));
    page.deeplearning.find('app-patent-table').should('be.visible');
    page.deeplearning.find('app-patent-list-input').should('be.visible');
    page.deeplearning.contains('button', 'SETUP MONITORING').should('be.visible');
  });

  it.skip('should have monitor results', () => {
    page.getMonitorProfileByName(profileName).find('.to-semantic').click();
    page.semantic.find('label.method-state').click();
    page.selectMethod('Boolean');
    page.boolean.contains('button', 'VIEW INPUT').click();
    page.boolean.contains('button', 'SETUP MONITORING', {timeout: 20000}).click();
    ModalDialogEle.confirmYes({clickableButtonText: 'Results'} as ModalDialogOptions);
    page.mainTabs.contains('a', '3. Analyze result').click();
    page.countMonitorRun.should('eq', 6);
  });

  // it('should clear all monitor profile methods', () => {
  //   page.getMonitorProfileByName(profileName).find('.to-deeplearning').click();
  //   page.deeplearning.should('exist');
  //   page.semantic.should('exist');
  //   page.boolean.should('exist');

  //   page.selectMethod('Deep learning');
  //   page.deeplearning.find('button.clear-profile-method').click();
  //   ModalDialogEle.confirmYes();
  //   page.deeplearning.contains('button', 'SETUP MONITORING').should('be.disabled');
  //   page.deeplearning.find('button.clear-profile-method').should('be.enabled');

  //   page.selectMethod('Semantic');
  //   page.semantic.find('button.clear-profile-method').click();
  //   ModalDialogEle.confirmYes();
  //   page.semantic.contains('button', 'SETUP MONITORING').should('be.disabled');
  //   page.semantic.find('button.clear-profile-method').should('be.enabled');

  //   page.selectMethod('Boolean');
  //   page.boolean.find('button.clear-profile-method').click();
  //   ModalDialogEle.confirmYes();
  //   page.boolean.contains('button', 'SETUP MONITORING').should('be.disabled');
  //   page.boolean.find('button.clear-profile-method').should('be.enabled');
  // });

  it('should remove new created monitor profile', () => {
    page.removeProfile(page.getMonitorProfileByName(profileName));
    page.checkNotExistingMonitorProfile(profileName);
  });
});
