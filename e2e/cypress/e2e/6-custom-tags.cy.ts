import { PASSWORD, SEARCH_QUERY_2, USER } from "../utils/data";
import { SearchPage } from "../pages/semantic-search.page";
import { LoginPage } from "../pages/login.page";

describe('Custom tags tests', () => {
  let page: SearchPage;
  const tagName: string = `Tag ${Date.now()}`;
  const maxTags = 3;

  beforeEach(() => {
    const loginPage = new LoginPage();
    page = new SearchPage();
    page.navigateTo();
    loginPage.login(USER, PASSWORD, false);
    page.searchInput.should('be.visible');
  });

  it('should be able to assging multiple tags in result list', () => {
    page.search(SEARCH_QUERY_2);
    page.resultsTable.should('exist');

    page.resultsTable.get('tbody > tr').each((el, index) => {
      if (index < maxTags) {
        cy.wrap(el).find('.checkbox').first().click({force: true});
      }
    });

    const addTagButton = cy.get('app-patent-control-bar .icon-add-tag');
    addTagButton.should('exist');
    addTagButton.click();

    cy.get('.tags-select-patent-control-bar #tags-select-search-input').type(tagName);
    cy.get('.tags-select-patent-control-bar .tags-select-actions').contains('button', 'Create').click();

    cy.wait(5000);

    page.showMoreTags();
    page.checkNumberOfTags(tagName, maxTags);
  });

  it('should be able to unassign a tag from result list', () => {
    page.search(SEARCH_QUERY_2);
    page.resultsTable.should('exist');

    cy.wait(2000);

    page.showMoreTags();
    page.checkNumberOfTags(tagName, maxTags);
    page.deleteTags(tagName, maxTags);
  });

  it('should be able remove a tag', () => {
    cy.visit('tags');
    cy.get('#filter-term').should('exist').click().type(tagName);
    cy.get('#filter-button').should('exist').click();
    cy.contains('tbody > tr', tagName).should('exist');

    cy.get('thead > tr > th > .checkbox').click({force: true});
    cy.get('.tools-bar .icon-delete').click();

    const modal = cy.get('.modal-dialog');
    modal.should('exist');
    modal.get('.button-destructive-primary').click();

    cy.wait(1000);
    cy.get('.alert-info').should('exist');
  });
});
