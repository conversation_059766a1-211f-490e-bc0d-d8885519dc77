import { generateUniqueName } from '../utils/util';

export abstract class BaseSearchPage {
  get saveIntoCollectionButton() {
    return cy.contains('app-patent-control-bar .icon-save-list', 'Save');
  }

  get resultsTable() {
    return cy.get('app-patent-table');
  }

  saveIntoCollection(prefix: string, hasFolder: boolean, saveSelectedDocuments: boolean) {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/web/result_collections',
    }).as('createCollectionQuery');
    const folderName = hasFolder ? generateUniqueName(prefix) : null;
    const collectionName = generateUniqueName(prefix);
    this.saveIntoCollectionButton.should('be.exist');
    this.selectDocuments(saveSelectedDocuments);
    this.saveIntoCollectionButton.click();

    if (hasFolder) {
      this.createFolder(folderName);
    }
    this.createCollection(collectionName);
    cy.wait('@createCollectionQuery');

    return [folderName, collectionName];
  }

  viewFirstPatent() {
    cy.get('app-patent-table', {timeout: 20000}).find('table .view-patent-icon').first().click();
    cy.url({timeout: 20000}).should('include', '/patent/view');
    cy.get('app-patent-viewer .patent-title', {timeout: 20000}).should('be.visible');
  }

  openSideBar() {
    cy.get('.sidebar-header .btn').first().click();
    cy.get('app-patent-viewer .button-back').should('be.visible');
  }

  goBackFromPatentViewer(callbackFunc = null) {
    cy.get('app-patent-viewer .button-back').click().then(() => {
      if (callbackFunc) {
        callbackFunc();
      }
    });
  }

  showMoreTags() {
    this.resultsTable.within(($table) => {
      const $buttons = $table.find('.tags-container .tag-item.tag-item-show-more').toArray();
      if ($buttons.length === 0) {
        return;
      }

      $buttons.forEach(($button) => {
        cy.wrap($button).click({force: true});
      });

      cy.wait(5000);
    });
  }

  checkNumberOfTags(tagName: string, maxTags: number) {
    this.resultsTable.within(() => {
      cy.get(`.tags-container .tag-item.tag-custom:contains("${tagName}")`, {timeout: 5000})
        .should('have.length', maxTags);
    });
  }

  deleteTags(tagName: string, maxTags: number) {
    this.resultsTable.within(() => {
      cy.get(`.tags-container .tag-item.tag-custom:contains("${tagName}") .tag-unassign`, {timeout: 5000})
        .should('have.length', maxTags)
        .then((tags) => {
          tags.each((index, el) => {
            cy.wrap(el).click({force: true});
          });
          cy.wait(1000);
        });

      cy.get(`.tags-container .tag-item.tag-custom:contains("${tagName}")`).should('not.exist');
    });
  }

  private selectDocuments(selectedDocuments: boolean) {
    this.resultsTable.get('thead .patents-selection').then((_) => {
      this.resultsTable.get('thead .patents-selection-menu .toggle-visible-patents').then((toggleBtn) => {
        if ((selectedDocuments && toggleBtn.text() === 'Select visible') ||
          (!selectedDocuments && toggleBtn.text() === 'Deselect visible')) {
          this.resultsTable.get('thead .patents-selection-menu .toggle-visible-patents').click({force: true});
        }
      });
    });
  }

  private createFolder(folderName: string) {
    cy.get('app-add-to-collection .atc-add-folder').click();
    cy.get('.ff-form #name').type(folderName);
    cy.get('.ff-footer').contains('.btn', 'Save').click();
    cy.contains('app-add-to-collection .atc-collections .clt-folder-row', folderName).click();
  }

  private createCollection(collectionName: string) {
    cy.get('app-add-to-collection .atc-collection-name').type(collectionName);
    cy.get('app-add-to-collection .atc-save-collection').click();
  }
}
