import { BaseSearchPage } from './base-search.page';
import { BOOLEAN_QUERY_1, BOOLEAN_QUERY_2 } from '../utils/data';

export class BooleanSearchPage extends BaseSearchPage {
  get addConditionButton() {
    return cy.get('app-boolean-input .btn-add');
  }

  get booleanSearchInput() {
    return cy.get('app-boolean-input');
  }

  get booleanSearchAdvanceInput() {
    return cy.get('app-boolean-advanced-mode');
  }

  get searchButton() {
    return cy.get('#search-button');
  }

  get advancedSearchButton() {
    return cy.get('#advanced-search-button');
  }

  get advModeBtn() {
    return cy.get('.advance-mode-btn');
  }

  get clearBooleanInputBtn() {
    return cy.get('#clear-boolean-input-button');
  }

  get resultTitle() {
    return cy.get('#boolean-search-results-container .search-results-container .bsr-results-title');
  }

  get countCharts() {
    return cy.get('app-base-card-chart').its('length');
  }

  get getResultsTable() {
    return cy.get('#boolean-search-results-container');
  }

  get booleanInputEle() {
    return cy.get('app-boolean-input > div:nth-last-child(2) .dropdown .big-input .form-control');
  }

  get lengthClauses() {
    return cy.get('app-boolean-input .bls-input').its('length');
  }

  fieldBtn(index) {
    return cy.get('app-boolean-input .bls-input-field-dropdown input').eq(index);
  }

  opBtn(index) {
    return cy.get('app-boolean-input .bls-input-operator-dropdown input').eq(index);
  }

  fieldList(field, index) {
    return cy.get('app-boolean-input .bls-input-field-dropdown a').contains(field).eq(index);
  }

  opList(operator, index) {
    return cy.get('app-boolean-input .bls-input-field-dropdown a').contains(operator).eq(index);
  }

  navigateTo() {
    cy.visit('boolean');
  }

  gotoChartMode() {
    cy.get('.visual-analysis-nav-icon.nav-link').click();
  }

  booleanSearch(conditions: Array<{ field: string, operator: string, value: string | { parent: string, child: string }, type: string }>) {
    let count = 0;
    this.lengthClauses.then( countClauses => {
      const countConditions = conditions.length;
      for (const field of conditions) {
        this.addField(field, count);
        count += 1;
        if (count > countClauses - 1 && count < countConditions) {
          this.addConditionButton.click();
        } else {
          this.interceptParseBooleanQuery();
        }
      }

      this.waitParseBooleanQueryComplete();
      this.searchButton.should('be.enabled').click();
    });
  }

  booleanAdvanceSearch(query: string) {
    this.interceptParseBooleanQuery();
    this.booleanSearchAdvanceInput.should('be.visible');
    this.booleanSearchAdvanceInput.get('.ace_text-input', {timeout: 10000}).first()
      .focus().type('{selectall}{backspace}{selectall}{backspace}');
    this.booleanSearchAdvanceInput.get('.ace_text-input', {timeout: 10000}).first()
      .focus().type(query).then(() => {
      this.waitParseBooleanQueryComplete();
      this.advancedSearchButton.should('be.enabled').click();
    });
  }

  addField(field: { field: string, operator: string, value: string | { parent: string, child: string }, type: string }, index: number = 0) {
    this.booleanSearchInput.then(element => {
      this.fieldBtn(index).click().then(() => {
        this.selectInputOption(field.field);
        cy.wait(150);
        this.opBtn(index).click().then(() => {
          this.selectInputOption(field.operator);
          
          const valueEle = field.type != 'tree' ? cy.get('app-boolean-input .bls-input-value .big-input .form-control').eq(index) : null;

          switch (true) {
            case field.type === 'dropdown':
              valueEle.click().then(() => {
                this.selectInputOption(field.value);
              });
              break;
            case field.type === 'tree':
              this.selectTreeOption(field.value as { parent: string, child: string }, index + 1);
              break;
            default:
              valueEle.focus().type(String(field.value));
              break;
          }
        });
      });

    });
  }

  displaySimpleModeFromPatentViewer() {
    this.toSimpleMode();
    this.booleanSearch(BOOLEAN_QUERY_1);
    this.viewFirstPatent();
    this.openSideBar();
    this.goBackFromPatentViewer(() => {
      this.validateSimpleMode();
    });
  }

  displayAdvancedModeFromPatentViewer() {
    this.toAdvancedMode();
    this.booleanAdvanceSearch(BOOLEAN_QUERY_2);
    this.viewFirstPatent();
    this.openSideBar();
    this.goBackFromPatentViewer(() => {
      this.validateAdvancedMode();
    });
  }

  toSimpleMode() {
    this.switchMode(true);
  }

  toAdvancedMode() {
    this.switchMode(false);
  }

  private switchMode(toSimpleMode: boolean) {
    const text = toSimpleMode ? 'Switch to simple mode' : 'Switch to advanced mode';
    const btn = cy.get('a.advance-mode-btn').then((ele) => {
      if (ele && ele.is(':visible') && ele.text().includes(text)) {
        btn.click().then((btn) => {
          cy.get('body').then(($body) => {
            if ($body.find('ngb-modal-window .modal-footer button:contains("Yes"):visible').length) {
              cy.contains('ngb-modal-window .modal-footer button', 'Yes', {timeout: 1000}).click();
            }
          });
        });
      }
    });
  }

  private selectTreeOption(value: { parent: string, child: string }, index) {
    cy.get('app-technology-field-browser .more-icon').click();
    cy.get('.check-level-1').eq(3).click();
    cy.get('button:contains("Select")').click();
  }

  private selectInputOption(textValue) {
    cy.get('app-boolean-input .dropdown-menu.show .dropdown-item').contains(textValue, {timeout: 2000}).click();
  }

  private validateSimpleMode() {
    this.booleanSearchInput.should('be.visible');
    this.booleanSearchAdvanceInput.should('not.exist');
  }

  private validateAdvancedMode() {
    this.booleanSearchAdvanceInput.should('be.visible');
    this.booleanSearchInput.should('not.exist');
  }

  private interceptParseBooleanQuery() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/search/boolean/parse',
    }).as('parseBooleanQuery');
  }

  private waitParseBooleanQueryComplete() {
    cy.wait('@parseBooleanQuery');
  }
}
