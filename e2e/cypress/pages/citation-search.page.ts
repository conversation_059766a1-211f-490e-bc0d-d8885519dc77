import { BaseSearchPage } from './base-search.page';

export class CitationPage extends BaseSearchPage {


    get searchInput() { return cy.get('#patentNumbers'); }
    get searchButton() { return cy.get('#search-button'); }
    get totalItems() { return cy.get('#total-items'); }
    get allCharts() { return cy.get('highcharts-chart').its('length'); }

    get resultsTable() { return cy.get('app-patent-table'); }
    get focalTable() { return cy.get('app-focal-table'); }
    get pagination() { return cy.get('.ngx-pagination'); }
    get charts() { return cy.get('app-citation-charts'); }
    get countResults() { return this.resultsTable.find('table tbody tr').its('length'); }
    get countPages() { return this.pagination.find('li.ng-star-inserted').its('length'); }
    get level_2() { return cy.get('#level_2'); }
    get references_only() { return cy.get('#references_only'); }
    navigateTo() { cy.visit('citation'); }

    gotoChartMode() {
      cy.get('.visual-analysis-nav-icon.nav-link').click();
    }

    search(text: string) {
        this.searchInput.type(text, { delay: 0 });
        this.searchButton.click();
    }
}
