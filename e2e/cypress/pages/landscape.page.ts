import { SEARCH_QUERY_2 } from '../utils/data';
import { ModalDialogEle } from '../utils/modal-dialog.ele';

export class LandscapePage {
  get form() {
    return cy.get('form.landscape-profile-form');
  }

  get confirmRemoveButton() {
    return cy.contains('button', 'Yes');
  }

  get computeButton() {
    return cy.get('#draft-documents-section .btn--compute');
  }

  get charts() {
    return cy.get('app-charts-container', {timeout: 10000});
  }

  get chartsTitle() {
    return cy.get('app-charts-container .tabs-title.section-title-text', {timeout: 10000});
  }

  get documentsPagination() {
    return cy.get('app-profile app-pagination', {timeout: 10000});
  }

  get documentsTable() {
    return cy.get('app-profile app-patent-table');
  }

  checkFormTitle(title: string) {
    return cy.contains('form .section-title h3', title).should('be.visible');
  }

  navigateTo() {
    return cy.visit('/landscape');
  }

  gotoChartMode() {
    cy.get('.visual-analysis-nav-icon.nav-link').click();
  }

  countLandscapeCharts() {
    cy.get('.combined-chart-section app-charts-container', {timeout: 10000}).should('exist');
    return cy.get('.combined-chart-section app-base-card-chart', {timeout: 10000}).its('length');
  }

  getLandscapeProfileByName(name: string) {
    return cy.contains('app-landscape-profile-card.landscape-profile-card', name).should('be.visible');
  }

  clickEditMenu(profileEle) {
    this.clickMenuItem(profileEle, '.dropdown-item.btn--edit');
  }

  clickOpenMenu(profileEle, waitForComputing: boolean) {
    this.clickMenuItem(profileEle, '.dropdown-item.btn--open', () => {
      if (waitForComputing) {
        this.computeButton.then((ele) => {
          if (ele.is(':visible')) {
            this.computeButton.click();
          }
          cy.url({timeout: 20000}).should('contain', '/landscape/profile/');
        });
      }
    });
  }

  createNewLandscapeProfile(name = '', patentNumbers = '') {
    cy.get('.landscape-profile-form input[formcontrolname=name]').type(name, { delay: 0 });
    cy.get('.landscape-profile-form textarea.patent-numbers').type(patentNumbers, { delay: 0 });
    cy.get('.landscape-profile-form input[formcontrolname=category]').type('category', { delay: 0 });
    this.interceptExtractPatentListQuery();
    cy.get('.landscape-profile-form').submit();
    this.waitExtractPatentListQueryComplete();
  }

  addBooleanQuery() {
    cy.contains('.landscape-profile-form .nav-link', 'Boolean').click();

    cy.get('app-boolean-input > div:nth-last-child(2) .dropdown input').first().click();
    cy.get('app-boolean-input > div:nth-last-child(2) .dropdown-menu.show').contains('a', 'Title').click();
    cy.get('app-boolean-input > div:nth-last-child(2) .bls-input-value .big-input .form-control').type('audi', { delay: 0 });
  }

  addSemanticQuery() {
    cy.contains('.landscape-profile-form .nav-link', 'Semantic').click();
    cy.get('app-semantic-input #searchText').type(SEARCH_QUERY_2);
  }

  deleteProfile(profileEle, profileName) {
    this.clickMenuItem(profileEle, 'a.btn--remove', () => {
      ModalDialogEle.confirmYes();
      cy.waitUntilAngularStable();
      cy.get('.page-content').should('be.visible');
      cy.contains('app-landscape-profile-card.landscape-profile-card', profileName).should('not.exist');
    });
  }

  navigateToNewProfileForm() {
    const newProfileButton = cy.contains('.analysis-step-top .btn', 'NEW LANDSCAPE').then((ele) => {
      if (ele.is(':visible')) {
        newProfileButton.click();
      } else {
        cy.contains('.page-content .btn-landscape', 'NEW LANDSCAPE').click();
      }
    });
  }

  private interceptExtractPatentListQuery() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/web/result_collections/extract/patent_list',
    }).as('extractPatentListQuery');
  }

  private waitExtractPatentListQueryComplete() {
    cy.wait('@extractPatentListQuery');
  }

  private clickMenuItem(profileEle, menuItemSelector: string, func = null) {
    const dropdown = profileEle.find('.profile-dropdown-option');
    dropdown.click().then(() => {
      dropdown.parent().find(menuItemSelector).click().then(() => {
        if (func) {
          func();
        }
      });
    });
  }
}
