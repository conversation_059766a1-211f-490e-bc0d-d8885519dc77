export class LoginPage  {
  navigateTo() {
    cy.visit('auth/login');
  }
  getLoginForm() {
    return cy.get('app-root app-login form');
  }
  login(user: string, password: string, expectSuccess: boolean = true, expectSignupStep: boolean = true, delayType: boolean = false) {
    this.interceptUserSignInQuery();
    this.interceptGetLoginMethodRequest();
    cy.get('#email').type(user, delayType? {}: { delay: 0 });

    cy.get('button').contains('Continue').click({force: true}).then(() => {
      if (expectSignupStep) {
        this.waitGetLoginMethodRequestComplete().then(() => {
          this.loginByPassword(password, expectSuccess);
        });
      }
    });
  }

  logout() {
    cy.get('app-header #dropdownProfile').click();
    cy.get('app-header .dropdown-settings').invoke('show');
    cy.get('app-header .dropdown-settings .btn-sign-out').click();
  }

  private loginByPassword(password: string, expectSuccess: boolean = true, delayType: boolean = true) {
    cy.get('#password').type(password, delayType? {}: { delay: 0 });
    cy.get('button').contains('Sign in').click();
    if (expectSuccess) {
      cy.get('.launchpad-user-title').should('be.visible');
    }
  }

  private interceptUserSignInQuery() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/auth/login',
    }).as('UserSignInQuery');
  }

  public waitUserSignInQueryComplete() {
    cy.wait('@UserSignInQuery');
  }

  private interceptGetLoginMethodRequest() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/auth/login/method',
    }).as('GetLoginMethodRequest');
  }

  public waitGetLoginMethodRequestComplete() {
    return cy.wait('@GetLoginMethodRequest').then(() => {});
  }
}
