import { ModalDialogEle } from '../utils/modal-dialog.ele';

export class MonitorPage {
  get newProfileBtn() {
    return cy.get('.profile-single.create-profile-box');
  }

  get profileSettingForm() {
    return cy.get('#formMonitoringProfile', { timeout: 5000 });
  }

  get breadcrumb() {
    return cy.get('ol.breadcrumb');
  }

  get mainTabs() {
    return cy.get('.nav.main-tabs');
  }

  get profileMethods(){
    return cy.get('app-monitoring-profile');
  }

  get deeplearning() {
    return cy.get('app-monitor-machine-learning');
  }

  get boolean() {
    return cy.get('app-monitor-boolean');
  }

  get semantic() {
    return cy.get('app-monitor-semantic');
  }

  get charts() {
    return cy.get('app-monitor-charts');
  }

  get monitorRuns() {
    return cy.get('table.table.monitor-run-table');
  }

  get countMonitorRun() {
    return cy.get('table.table.monitor-run-table tr.monitor-run').its('length');
  }

  navigateTo() {
    return cy.visit('/monitor');
  }

  navigateToNewProfile() {
    cy.get('.profile-single.create-profile-box').click();
    cy.get('#formMonitoringProfile', { timeout: 10000 }).should('exist');
  }

  setInputValue(inputName: string, inputValue: string, eltype = 'input') {
    this.profileSettingForm.find(eltype + '.' + inputName).should('be.visible');
    this.profileSettingForm.find(eltype + '.' + inputName).type(inputValue);
  }

  createNewMonitorProfile(profileName: string) {
    this.setInputValue('profile-name', profileName);
    this.setInputValue('profile-description', '2e test description', 'textarea');
    this.profileSettingForm.find('label.delivery-PDF').click(); // check PDF option
    this.profileSettingForm.find('label.occurrence-weekly').click(); // check weekly option
    this.profileSettingForm.contains('button', 'NEXT').click(); // submit form
    cy.contains('button', 'SETUP MONITORING', { timeout: 10000 }).should('be.visible');
  }

  addDeeplearningQuery(query) {
    this.deeplearning.find('#patent_numbers').type(query);
    this.deeplearning.contains('button', 'VIEW INPUT').click();
  }

  addSemanticQuery(query) {
    this.semantic.find('#searchText').type(query);
    this.semantic.contains('button', 'VIEW INPUT').click();
  }

  addBooleanQuery() {
    cy.get('app-boolean-input > div:nth-last-child(2) .dropdown input').first().click();
    cy.get('app-boolean-input > div:nth-last-child(2) .dropdown-menu.show').contains('a', 'Title').click();
    cy.get('app-boolean-input > div:nth-last-child(2) .bls-input-value .big-input .form-control').type('Audi');

    this.boolean.contains('button', 'VIEW INPUT').click();
  }

  removeProfile(profileEle) {
    const dropdown = profileEle.find('.profile-dropdown-option');
    dropdown.click().then(() => {
      dropdown.parent().find('.profile-remove').click().then(() => {
        ModalDialogEle.confirmYes();
      });
    });
  }

  getMonitorProfileByName(name: string) {
    return cy.get('.single-profile-card').contains('.single-profile-card', name, {matchCase: false});
  }

  checkNotExistingMonitorProfile(name: string) {
    return cy.contains('.single-profile-card .profile-title', name, {matchCase: false}).should('not.exist');
  }

  selectMethod(name: string) {
    cy.contains('#Monitoring-profile .monitor-methods-tab a', name).click();
  }

  goToMonitoringMethod() {
    cy.contains('.monitor-profile-tabs a.nav-link', 'Monitoring method').click();
  }

  openProfile(profileName:string){
    this.interceptGetMonitorProfileQuery();
    this.interceptGetMonitorRunsQuery();
    cy.contains('.single-profile-card .single-card-header', profileName, {matchCase: false}).click();
    this.waitGetMonitorProfileQueryComplete();
    this.waitGetMonitorRunsQuery();
  }

  interceptGetMonitorRunsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/monitor/run?*',
    }).as('GetMonitorRunsQuery');
  }

  private waitGetMonitorRunsQuery() {
    cy.wait('@GetMonitorRunsQuery');
  }
  
  interceptGetMonitorProfileQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/monitor/profile/*',
    }).as('GetMonitorProfileQuery');
  }

  private waitGetMonitorProfileQueryComplete() {
    cy.wait('@GetMonitorProfileQuery');
  }
}
