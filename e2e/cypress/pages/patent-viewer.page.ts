export class PatentViewerPage {
  get patentViewerContent() { return cy.get('app-patent-view'); }
  get patentTitle() { return cy.get('app-patent-view #sec-title-info'); }
  get backButton() { return cy.get('#button-patent-viewer-back'); }
  get patentFamilyDropdownButton() { return cy.get('#button-patent-family-dropdown'); }
  get patentFamilyDropdownBody() { return cy.get('#body-patent-family-dropdown'); }
  get patentContent() { return cy.get('#patent-result'); }

  get ratingsButton() { return cy.get('#button-patent-ratings'); }
  get commentsButton() { return cy.get('#button-patent-comments'); }
  get highlightsButton() { return cy.get('#button-patent-highlights'); }
  get currentListButton() { return cy.get('#button-patent-current-list'); }
  get relevantPartsButton() { return cy.get('#button-patent-relevant-parts'); }

  get previousPatentButton() { return cy.get('app-patent-list #button-patent-previous'); }
  get nextPatentButton() { return cy.get('app-patent-list #button-patent-next'); }

  get ratingsSection() { return cy.get('app-patent-ratings'); }
  get commentsSection() { return cy.get('app-patent-comments'); }
  get currentListSection() { return cy.get('app-patent-list'); }
  get highlightsSection() { return cy.get('app-patent-highlights-section'); }
  get relevantPartsSection() { return cy.get('app-patent-relevance'); }

  backToSearch() { this.backButton.click(); }
  openComments() { this.commentsButton.click(); }
  openHighlights() { this.highlightsButton.click(); cy.get('app-patent-highlights .open-all-highlights-title').click()}
  openRatings() { this.ratingsButton.click(); }
  navigateToPatent() { cy.visit('patent/view/35483741'); }

  nextPatent() {
    this.nextPatentButton.then(val => {
      if (val) {
        this.nextPatentButton.click();
      }
    });
  }

  previousPatent() {
    this.previousPatentButton.then(val => {
      if (val) {
        this.previousPatentButton.click();
      }
    });
  }

  checkPatentFamilyDropdown(){
    this.patentFamilyDropdownButton.contains('Family representative').should('be.visible');
    this.patentFamilyDropdownBody.should('not.visible');
    this.patentFamilyDropdownButton.click();
    this.patentFamilyDropdownButton.should('be.visible');
    this.patentFamilyDropdownBody.should('be.visible');
    this.patentContent.should('be.visible');

    this.patentFamilyDropdownBody.get('.figma-dropdown-item').contains('EP1769992A2').click();

    this.patentFamilyDropdownButton.contains('EP1769992A2').should('be.visible');

    this.patentTitle.should('be.visible');
  }

  checkComments(){
    this.commentsSection.get('.comments-no-comment').should('be.visible');
    this.commentsSection.get('.tab-pill').contains('On section').should('be.visible');
    this.commentsSection.get('.tab-pill').contains('On section').click();
    this.commentsSection.get('app-patent-comment-box').should('be.visible');
  }

  checkRatings(){
    this.ratingsSection.get('app-patent-side-section-header').should('contain.text', 'Ratings');
  }

  checkHighlights(){
    this.highlightsSection.get('.highlight-section').should('be.visible');
  }

  checkRelevantPart(){
    this.relevantPartsSection.get('.relevance-section-content').should('be.visible');
  }

  checkCurrentList(){
    this.currentListSection.get('.current-list-document').should('be.visible');
    this.nextPatentButton.should('be.visible');
    this.previousPatentButton.should('not.exist');

    this.nextPatent();
    this.nextPatentButton.should('be.visible');
    this.previousPatentButton.should('be.visible');

    this.previousPatent();
    this.previousPatentButton.should('not.exist');
  }

  interceptGetRatingsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/tasks?*',
    }).as('getRatingsQuery');
  }

  waitGetRatingsQueryComplete() {
    cy.wait('@getRatingsQuery');
  }
}
