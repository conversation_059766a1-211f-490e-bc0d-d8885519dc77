import { ModalDialogEle } from '../utils/util';

export class ResultCollectorPage {
  get actionsBtn() { return  cy.get('.clt-actions .btn'); }
  get inputName() { return cy.get('app-collection-form #name'); }
  get noCollectionAlert() { return cy.contains('.alert', 'You do not have any lists yet.'); }
  get noCollectionFilterAlert() { return cy.contains('.alert', 'No list match your search filter.'); }
  get annotatedTab() { return cy.contains('.nav-link', 'Comments & highlights'); }
  get resultsAnnotatedDocuments() { return cy.get('app-annotated-documents'); }
  get countAnnotatedDocuments() { return this.resultsAnnotatedDocuments.find('tbody').its('length'); }
  get openedTab() { return cy.contains('.nav-link', 'Recently opened'); }
  get resultsOpenedDocuments() { return cy.get('app-opened-documents'); }
  get countOpenedDocuments() { return this.resultsOpenedDocuments.find('tbody').its('length'); }

  link;
  navigateTo() {
    cy.visit('/collections');
  }

  createCollection(name: string, patentNumbers: string = null) {
    this.interceptCreateCollectionQuery();
    if (patentNumbers) {
      this.interceptExtractPatentListQuery();
    }
    this.actionsBtn.contains('NEW LIST').click();
    this.inputName.type(name);
    if (patentNumbers) {
      cy.get('app-collection-form #patent_numbers').type(patentNumbers);
      this.waitExtractPatentListQueryComplete();
    }

    const date = new Date();
    date.setDate(date.getDate() + 1);
    const month = date.getMonth() + 1;
    const monthStr = month < 10 ? `0${month}` : month;
    const expiredDate = `${date.getFullYear()}-${monthStr}-${date.getDate()}`;
    cy.get('app-collection-form #expires_date').type(expiredDate);
    cy.get('.clt-cf-footer').contains('.btn', 'Save').click();
    this.waitCreateCollectionQueryComplete();
  }

  filter(name: string) {
    cy.get('app-page-bar .filter-input').clear().type(name);
    cy.get('app-page-bar .filter-button').click();
  }

  getCollectionTitleByName(name: string) {
    return cy.contains('app-collection-card .single-card-header .card-title', name);
  }

  removeCollectionsByNames(collectionNames: Array<string>) {
    if (!collectionNames || collectionNames.length === 0) {
      return;
    }
    cy.waitUntilAngularStable();
    const collectionName = collectionNames.pop();
    const collectionEle = cy.get('app-collection-card:contains("' + collectionName + '")');
    collectionEle.find('.profile-dropdown-option').click();
    collectionEle.get('.remove-collection:visible').click();
    ModalDialogEle.confirmYes();
    this.removeCollectionsByNames(collectionNames);
  }

  createFolder(name: string) {
   this.actionsBtn.contains('NEW FOLDER').click();
   cy.get('app-folder-form #name').type(name);
   cy.get('.ff-footer').contains('.btn', 'Save').click();
  }

  getFolderTitleByName(name: string) {
    cy.waitUntilAngularStable();
    return cy.contains('app-folder-card .single-card-header a.card-title', name);
  }

  removeFoldersByNames(folderNames: Array<string>) {
    if (!folderNames || folderNames.length === 0) {
      return;
    }
    cy.waitUntilAngularStable();
    const folderName = folderNames.pop();
    const folderEle = cy.get('app-folder-card:contains("' + folderName + '")');
    folderEle.find('.profile-dropdown-option').click();
    folderEle.get('.remove-folder:visible').click();
    ModalDialogEle.confirmYes();
    this.removeFoldersByNames(folderNames);
  }

  removeFoldersAndCollections(folderNames: Array<string>, collectionNames: Array<string>) {
    this.removeFoldersByNames(folderNames);
    this.removeCollectionsByNames(collectionNames);
  }

  gotoFolder(name: string) {
    this.getFolderTitleByName(name).click();
  }

  goBack() {
    cy.waitUntilAngularStable();
    cy.contains('.clt-actions .btn', 'Back').click();
  }

  openCollectionByName(collectionName: string) {
    this.getCollectionTitleByName(collectionName).click();
  }

  getControlBarOptionByName(optionName: string) {
    return cy.contains('app-patent-control-bar .item-bar', optionName, {timeout: 5000});
  }

  shareCollectionWith(USER_TEAM_MEMBER: string) {
    this.interceptTeamsQuery();
    this.getControlBarOptionByName('Share').click();
    this.waitTeamsQueryComplete()
    cy.contains('app-share-dialog .users-team .user-team', USER_TEAM_MEMBER).then( ($user) => {
      cy.wait(500)
      cy.wrap($user).find('label').click();
      cy.contains('app-share-dialog .modal-footer .btn-share-with-team', 'Share').click();
    });
  }

  getSharedListDot() {
    return cy.get('.shared-collections-section .collection-shared-with-me .new-notifications');
  }

  getSharedIconOnCard() {
    return cy.contains('app-collection-card .check-on-off', 'SHARED');
  }

  getCardSharedBtn(text: string) {
    cy.waitUntilAngularStable();
    return cy.contains('app-collection-card .check-on-off', text);
  }

  getCollectionPageTitle(collectionName: string) {
    return cy.contains('app-collection .clt-patents-title', collectionName);
  }

  shareCollectionViaLink(btnText: string = 'SHARE') {
    this.interceptShareAsLinkQuery();
    this.getControlBarOptionByName(btnText).click();
    cy.contains('app-share-dialog .modal-hint a', 'Share outside your team as a link').click();
    cy.get('app-share-dialog textarea.shared-link-text').should('have.attr', 'readonly', 'readonly');
    cy.get('app-share-dialog textarea.shared-link-text').invoke('val').as('sharedLink')
    cy.contains('app-share-dialog .modal-footer .btn-share-by-link', 'Save').click();
    this.waitShareAsLinkQueryComplete();
    cy.contains('.confirmation-dialog.show .modal-dialog button', 'OK').click();
  }

  private interceptCreateCollectionQuery() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/web/result_collections',
    }).as('createCollectionQuery');
  }

  private interceptExtractPatentListQuery() {
    cy.intercept({
      method: 'POST',
      url: '/api/v2.0/web/result_collections/extract/patent_list',
    }).as('extractPatentListQuery');
  }

  interceptAnnotationDocumentsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/annotations/documents?*',
    }).as('annotationDocumentsQuery');
  }

  interceptReadDocumentsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/read_document?*',
    }).as('readDocumentsQuery');
  }

  private waitCreateCollectionQueryComplete() {
    cy.wait('@createCollectionQuery');
  }

  private waitExtractPatentListQueryComplete() {
    cy.wait('@extractPatentListQuery');
  }

  waitAnnotationDocumentsQueryComplete() {
    cy.wait('@annotationDocumentsQuery');
  }

  waitReadDocumentsQueryComplete() {
    cy.wait('@readDocumentsQuery');
  }

  private interceptGetCollectionsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/web/result_collections*',
    }).as('GetCollectionsQuery');
  }

  private interceptShareAsLinkQuery() {
    cy.intercept({
      method: 'PATCH',
      url: '/api/v2.0/web/result_collections/*',
    }).as('ShareAsLinkQuery');
  }

  private waitShareAsLinkQueryComplete() {
    cy.wait('@ShareAsLinkQuery');
  }

  private waitGetCollectionsQueryComplete() {
    cy.wait('@GetCollectionsQuery');
  }

  private interceptTeamsQuery() {
    cy.intercept({
      method: 'GET',
      url: '/api/v2.0/auth/users/team?load_all=1',
    }).as('teamsQuery');
  }

  private waitTeamsQueryComplete() {
    cy.wait('@teamsQuery');
  }
}
