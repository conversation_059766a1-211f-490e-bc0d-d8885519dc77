export class SearchHistoryPage {
  get resultsHistorySave() { return cy.get('#histories-table'); }
  get filterTerm() { return cy.get('#filter-term'); }
  get filterButton() { return cy.get('#filter-button'); }
  get pagination() { return cy.get('.ngx-pagination'); }
  get resultsTable() { return cy.get('app-patent-table'); }
  get countResultsHistorySave() { return this.resultsHistorySave.find('tbody').its('length'); }
  navigateTo() { cy.visit('history-saves'); }
  navigateToHistory() { cy.visit('history-saves/history'); }
  navigateToDocuments() { cy.visit('history-saves/documents'); }
  filter(text: string) {
    this.filterTerm.type(text, { delay: 0 });
    this.filterButton.click();
  }
  goToPage(page: string) { this.pagination.contains('li a span', page).click(); }
  loadSearch() { this.resultsHistorySave.get('#histories-table .load-search').first().click(); }
}
