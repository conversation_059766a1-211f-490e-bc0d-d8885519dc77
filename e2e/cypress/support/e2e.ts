// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// When a command from ./commands is ready to use, import with `import './commands'` syntax
import './commands';

const logs = [];
before(() => {
  cy.log('BEFORE ALL');
  Cypress.on('fail',  (error, runnable) => {
    const test = {
      message: `${error.name}: ${error.message}`,
      stack: error.stack
    };
    logs.push(test);
    throw error;
  });
});

after( () => {
  cy.log('AFTER ALL');
  logs.forEach( log => {
    const txt = `${new Date().toISOString()} - ${log.stack} \n`;
    cy.writeFile('cypress/log/log.txt', txt, { flag: 'a+' });
  });
});
