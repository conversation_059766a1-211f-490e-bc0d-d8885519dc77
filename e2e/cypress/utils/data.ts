export const USER = '<EMAIL>';
export const USER_TEAM_MEMBER = '<EMAIL>';
export const PASSWORD = 'Abcd+12345';
export const SEARCH_QUERY_1 = 'Molten salt thorium reactors with lining to increase neutron absorbtion on ' + new Date().toLocaleString();
export const SEARCH_QUERY_2 = 'Molten salt thorium reactors';
export const PATENT_NUMBERS = ['EP5454', 'DE2745607']; // EP687643 ??
export const BOOLEAN_QUERY_1 = [
  {field: 'Title', operator: 'CONTAINS', value: 'machine', type: 'text'},
  {field: 'Priority date', operator: '<', value: '01/08/2020', type: 'date'},
  {
    field: 'Patent value',
    operator: '=',
    value: 'Top 10% most valuable patents in their technological fields',
    type: 'dropdown'
  },
  {field: 'Priority date', operator: '>', value: '01/01/1980', type: 'date'},
  {
    field: 'Technology area',
    operator: 'EQUALS',
    value: {parent: 'Mechanical Engineering', child: 'Textile and Paper Machines'},
    type: 'tree'
  },

];
export const BOOLEAN_QUERY_2 = '(TITLE=Molten salt thorium)';
