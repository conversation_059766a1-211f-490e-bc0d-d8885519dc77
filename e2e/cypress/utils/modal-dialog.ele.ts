export interface ModalDialogOptions {
  dialogSelector: string;
  clickableButtonText: string;
  waitToBeVisibleTime: number;
}

export class ModalDialogEle {
  static confirmYes(options = {} as ModalDialogOptions) {
    options = {
      dialogSelector: '.confirmation-dialog.show .modal-dialog',
      clickableButtonText: 'Yes',
      ...(options || {} as ModalDialogOptions)
    } as ModalDialogOptions;

    cy.get(options.dialogSelector).contains('button', options.clickableButtonText).click();
  }
}
