export class ModalDialogEle {
  static confirmYes(modalClass = '.confirmation-dialog.show .modal-dialog', yesTitle = 'Yes') {
    cy.get(modalClass).contains('button', yesTitle).click();
  }
}

export function generateUniqueName(prefix: string, length: number = 25): string {
  const randomNumber = Math.floor(Math.random() * Math.floor(1000000));
  return `${prefix}${(new Date()).getTime() + randomNumber}`.slice(0, length);
}
