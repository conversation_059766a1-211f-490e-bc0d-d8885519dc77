const git = require('git-rev-sync');
const { writeFileSync } = require('fs');

const gitInfo = {commit: null, branch: null, version: process.env.npm_package_version};
if (process.env.CI_COMMIT_REF_NAME && process.env.CI_COMMIT_SHA) {
  gitInfo.branch = process.env.CI_COMMIT_REF_NAME;
  gitInfo.commit = process.env.CI_COMMIT_SHA;
} else {
  gitInfo.branch = git.branch();
  gitInfo.commit = git.long();
}
writeFileSync('version.json', JSON.stringify(gitInfo), {encoding: 'utf8'});
