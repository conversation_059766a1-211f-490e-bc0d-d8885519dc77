const { pathsToModuleNameMapper } = require('ts-jest');
const { compilerOptions } = require('./tsconfig.json');

module.exports = {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
  globalSetup: 'jest-preset-angular/global-setup',
  testEnvironment: 'jsdom',
  
  // Module resolution
  moduleNameMapper: {
    ...pathsToModuleNameMapper(compilerOptions.paths || {}, {
      prefix: '<rootDir>/src/',
    }),
    // Additional path mappings for the project structure
    '^app/(.*)$': '<rootDir>/src/app/$1',
    '^@api/(.*)$': '<rootDir>/src/app/api/$1',
    '^@applicant-aliases/(.*)$': '<rootDir>/src/app/applicant-aliases/$1',
    '^@auth/(.*)$': '<rootDir>/src/app/auth/$1',
    '^@boolean/(.*)$': '<rootDir>/src/app/boolean/$1',
    '^@citation/(.*)$': '<rootDir>/src/app/citation/$1',
    '^@collections/(.*)$': '<rootDir>/src/app/collections/$1',
    '^@core$': '<rootDir>/src/app/core',
    '^@core/(.*)$': '<rootDir>/src/app/core/$1',
    '^@error/(.*)$': '<rootDir>/src/app/error/$1',
    '^@help/(.*)$': '<rootDir>/src/app/help/$1',
    '^@history-saves/(.*)$': '<rootDir>/src/app/history-saves/$1',
    '^@landscape/(.*)$': '<rootDir>/src/app/landscape/$1',
    '^@monitor/(.*)$': '<rootDir>/src/app/monitor/$1',
    '^@patent/(.*)$': '<rootDir>/src/app/patent/$1',
    '^@profile/(.*)$': '<rootDir>/src/app/profile/$1',
    '^@search/(.*)$': '<rootDir>/src/app/search/$1',
    '^@subscription/(.*)$': '<rootDir>/src/app/subscription/$1',
    '^@users/(.*)$': '<rootDir>/src/app/users/$1',
    '^@shared/(.*)$': '<rootDir>/src/app/shared/$1',
    // Handle lightgallery module
    '^lightgallery/angular$': '<rootDir>/src/testing/lightgallery-mock.js',
    // Handle other problematic modules
    '^copy-to-clipboard$': '<rootDir>/src/testing/copy-to-clipboard-mock.js',
    // Handle CSS and SCSS imports
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    // Handle image imports
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/src/testing/file-mock.js',
  },
  
  // Test file patterns
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(ts|js)',
    '<rootDir>/src/**/*.(test|spec).(ts|js)'
  ],
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.d.ts',
    '!src/main.ts',
    '!src/polyfills.ts',
    '!src/test.ts',
    '!src/environments/**',
    '!src/**/*.module.ts',
    '!src/**/*.config.ts',
    '!src/**/*.stories.ts'
  ],
  
  coverageDirectory: 'coverage',
  coverageReporters: ['html', 'text-summary', 'lcov'],
  
  // Transform configuration
  transform: {
    '^.+\\.(ts|mjs|js|html)$': [
      'jest-preset-angular',
      {
        tsconfig: 'src/tsconfig.spec.json',
        stringifyContentPathRegex: '\\.(html|svg)$',
      },
    ],
  },
  
  // File extensions Jest will process
  moduleFileExtensions: ['ts', 'html', 'js', 'json', 'mjs'],
  
  // Ignore patterns
  transformIgnorePatterns: [
    'node_modules/(?!.*\\.mjs$|@angular|@ng-bootstrap|@ng-select|ngx-|angular-mentions|angularx-qrcode)'
  ],
  
  // Test timeout
  testTimeout: 10000,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Verbose output
  verbose: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Cache directory
  cacheDirectory: '<rootDir>/node_modules/.cache/jest',
};
