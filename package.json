{"name": "octimine-webapp", "version": "25.10.0", "scripts": {"ng": "ng", "postinstall": "webdriver-manager update --gecko false --standalone true", "prestart": "ts-node git.version.ts", "start": "ng serve --open", "dev": "lite-server --baseDir=\"dist/octimine-webapp\"", "prebuild": "ts-node git.version.ts", "build": "ng build --watch=true --aot --output-hashing all --source-map false --optimization --named-chunks false --build-optimizer --vendor-chunk", "prebuild:staging": "ts-node git.version.ts", "build:staging": "ng build --configuration=staging", "prebuild:prod": "ts-node git.version.ts", "build:prod": "ng build --configuration=production", "prebuild:dev": "ts-node git.version.ts", "build:dev": "ng build --configuration=dev", "lint": "ng lint", "test": "ng test --browsers=Chrome", "test-headless": "ng test --watch=false --browsers=FirefoxHeadless", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:coverage": "jest --coverage", "test:jest:ci": "jest --ci --coverage --watchAll=false", "e2e-cy-open": "concurrently \"ng serve --host 127.0.0.1\" \"wait-on http-get://127.0.0.1:4200 && cypress open --project ./e2e\"  --kill-others --success first", "e2e-cy-headless": "concurrently \"ng serve --host 127.0.0.1\" \"wait-on http-get://127.0.0.1:4200 && cypress run --project ./e2e --config video=false\" --kill-others --success first"}, "private": true, "dependencies": {"@ali-hm/angular-tree-component": "^17.0.1", "@angular-slider/ngx-slider": "^17.0.2", "@angular/animations": "^17.3.12", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^17.3.12", "@angular/localize": "^17.3.12", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "@fontsource/open-sans": "^5.0.28", "@fortawesome/fontawesome-pro": "^6.4.2", "@highcharts/map-collection": "^2.0.1", "@ng-bootstrap/ng-bootstrap": "^16.0.0", "@ng-select/ng-select": "^12.0.7", "@popperjs/core": "^2.11.8", "@types/jquery": "^3.5.16", "ace-builds": "^1.36.4", "angular-mentions": "1.5.0", "angularx-qrcode": "^17.0.1", "bootstrap": "^5.3.3", "canvg": "^4.0.1", "copy-to-clipboard": "^3.3.3", "core-js": "^3.38.1", "file-saver": "^2.0.5", "highcharts": "^10.3.3", "highcharts-angular": "^3.1.2", "html-to-image": "^1.11.11", "jquery": "^3.7.1", "jspdf": "^2.5.2", "jszip": "^3.10.1", "lightgallery": "^2.8.3", "lodash": "^4.17.15", "mark.js": "^8.11.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "ngx-color-picker": "^14.0.0", "ngx-extended-pdf-viewer": "^21.4.6", "ngx-image-cropper": "^8.1.0", "ngx-mask": "^17.1.8", "ngx-matomo-client": "^6.3.1", "ngx-pagination": "^6.0.3", "pluralize": "^8.0.0", "rxjs": "^7.8.0", "sanitize-html": "^2.10.0", "text-mask-addons": "^3.8.0", "ts-mixer": "^6.0.3", "tslib": "^2.8.0", "vanilla-text-mask": "^5.1.1", "xlsx": "^0.18.5", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "17.3.17", "@angular-devkit/core": "17.3.17", "@angular-eslint/builder": "17.5.3", "@angular-eslint/eslint-plugin": "17.5.3", "@angular-eslint/eslint-plugin-template": "17.5.3", "@angular-eslint/schematics": "17.5.3", "@angular-eslint/template-parser": "17.5.3", "@angular/cli": "^17.3.11", "@angular/compiler-cli": "^17.3.12", "@angular/language-service": "^17.3.12", "@jest/globals": "^30.0.0-beta.3", "@types/bootstrap": "^5.2.6", "@types/d3": "^5.7.2", "@types/jasmine": "~3.10.0", "@types/jasminewd2": "^2.0.8", "@types/jest": "^29.5.14", "@types/node": "^20.17.6", "@types/pluralize": "^0.0.33", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "concurrently": "^7.6.0", "cypress": "^12.17.4", "eslint": "^8.57.0", "flag-icons": "^7.2.3", "git-rev-sync": "^3.0.2", "jasmine-core": "~4.6.0", "jasmine-spec-reporter": "~7.0.0", "jest": "^29.7.0", "jest-preset-angular": "^14.6.0", "karma": "^6.3.20", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "lite-server": "^2.5.4", "ts-jest": "^29.3.4", "ts-node": "~10.9.1", "typescript": "<5.5", "wait-on": "^7.0.1", "webdriver-manager": "12.1.9"}}