{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", "docker:disable"], "lockFileMaintenance": {"enabled": true, "schedule": "before 4am on friday"}, "packageRules": [{"matchUpdateTypes": ["minor"], "groupName": "Minor dependencies", "groupSlug": "minor"}, {"matchUpdateTypes": ["patch"], "groupName": "Patch dependencies", "groupSlug": "patch"}], "major": {"dependencyDashboardApproval": true}, "rangeStrategy": "update-lockfile"}