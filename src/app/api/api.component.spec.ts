import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { ApiComponent } from '@api/api.component';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';


describe('ApiComponent', () => {
  let component: ApiComponent;
  let fixture: ComponentFixture<ApiComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ApiComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ApiComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
