import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {path: '', redirectTo: 'launchpad', pathMatch: 'full'},
  {path: 'signup', redirectTo: 'auth/signup', pathMatch: 'full'},
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'launchpad',
    loadChildren: () => import('./launchpad/launchpad.module').then(m => m.LaunchpadModule)
  },
  {
    path: 'search',
    loadChildren: () => import('./search/search.module').then(m => m.SearchModule)
  },
  {
    path: 'npl-search',
    loadChildren: () => import('./npl-search/npl-search.module').then(m => m.NPLSearchModule)
  },
  {
    path: 'citation',
    loadChildren: () => import('./citation/citation.module').then(m => m.CitationModule)
  },
  {
    path: 'boolean',
    loadChildren: () => import('./boolean/boolean.module').then(m => m.BooleanModule)
  },
  {
    path: 'boolean-tooltip',
    loadChildren: () => import('./boolean-tooltip/boolean-tooltip.module').then(m => m.BooleanTooltipModule)
  },
  {
    path: 'history-saves',
    loadChildren: () => import('./history-saves/history-saves.module').then(m => m.HistorySavesModule)
  },
  {
    path: 'monitor',
    loadChildren: () => import('./monitor/mointor.module').then(m => m.MonitorModule)
  },
  {
    path: 'landscape',
    loadChildren: () => import('./landscape/landscape.module').then(m => m.LandscapeModule)
  },
  {
    path: 'api',
    loadChildren: () => import('./api/api.module').then(m => m.ApiModule)
  },
  {
    path: 'data-coverage',
    loadChildren: () => import('./help/help.module').then(m => m.HelpModule)
  },
  {
    path: 'applicant-aliases',
    loadChildren: () => import('./applicant-aliases/applicant-aliases.module').then(m => m.ApplicantAliasesModule)
  },
  {
    path: 'profile',
    loadChildren: () => import('./profile/profile.module').then(m => m.ProfileModule)
  },
  {path: 'collections', loadChildren: () => import('./collections/collections.module').then(m => m.CollectionsModule)},
  {path: 'patent', loadChildren: () => import('./patent/patent.module').then(m => m.PatentModule)},
  {path: 'error', loadChildren: () => import('./error/error.module').then(m => m.ErrorModule)},
  {path: 'users', loadChildren: () => import('./users/users.module').then(m => m.UsersModule)},
  {path: 'tags', loadChildren: () => import('./tags/tags.module').then(m => m.TagsModule)},
  { path: 'privacy-policy', loadChildren: () => import('./privacy-policy/privacy-policy.module').then(m => m.PrivacyPolicyModule) },
  { path: 'tasks', loadChildren: () => import('./tasks/tasks.module').then(m => m.TasksModule) },
  { path: 'ratings', loadChildren: () => import('./ratings/ratings.module').then(m => m.RatingsModule) },
  { path: 'groups', loadChildren: () => import('./users/groups.module').then(m => m.GroupsModule) },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { anchorScrolling: 'enabled' })],
  exports: [RouterModule]
})
export class AppRoutingModule {

}
