import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { AppComponent } from './app.component';
import { UserService } from './core/services/user/user.service';
import { RoutingHistoryService } from './core/services/routing-history/routing-history.service';
import { NgbTooltipConfig, NgbPopoverConfig } from '@ng-bootstrap/ng-bootstrap';

describe('AppComponent (Jest)', () => {
  let mockUserService: jest.Mocked<Partial<UserService>>;
  let mockRoutingHistoryService: jest.Mocked<Partial<RoutingHistoryService>>;

  beforeEach(async () => {
    mockUserService = {
      populate: jest.fn()
    };

    mockRoutingHistoryService = {
      subscribeHistories: jest.fn(),
      unsubscribeHistories: jest.fn()
    };

    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule
      ],
      declarations: [
        AppComponent
      ],
      providers: [
        { provide: UserService, useValue: mockUserService },
        { provide: RoutingHistoryService, useValue: mockRoutingHistoryService },
        NgbTooltipConfig,
        NgbPopoverConfig
      ]
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it('should have isIE property', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.isIE).toBeDefined();
  });

  it('should call subscribeHistories on init', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();
    expect(mockRoutingHistoryService.subscribeHistories).toHaveBeenCalled();
  });
});
