import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, NavigationStart, Router } from '@angular/router';
import { RoutingHistoryService, UserService } from './core';
import { filter, take } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { NgbTooltipConfig } from '@ng-bootstrap/ng-bootstrap';
import { NgbPopoverConfig } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html'
})
export class AppComponent implements OnInit, OnDestroy {
  public isIE;

  private subscriptions = new Subscription();

  constructor(
    tooltipConfig: NgbTooltipConfig,
    popoverConfig: NgbPopoverConfig,
    private userService: UserService,
    private routingHistoryService: RoutingHistoryService,
    private router: Router
  ) {
    tooltipConfig.container = "body";
    popoverConfig.container = "body";
  }

  ngOnInit() {
    this.populateUserProfile();
    this.routingHistoryService.subscribeHistories();
    // Internet Explorer 6-11
    this.isIE = /msie\s|trident\//i.test(window.navigator.userAgent);
  }

  ngOnDestroy() {
    this.routingHistoryService.unsubscribeHistories();
    this.subscriptions.unsubscribe();
  }

  private populateUserProfile() {
    const router$ = this.router.events
      .pipe(
        filter((event) => event instanceof NavigationStart),
        take(1)
      )
      .subscribe({
        next: (event: NavigationStart) => {
          const urlsCanBeAccessedWithoutToken = ['/collections/shared/', '/patent/view/shared/', '/auth/'];
          if (urlsCanBeAccessedWithoutToken.findIndex((url) => event.url.includes(url)) === -1) {
            this.userService.populate();
          }
        }
      });
    this.subscriptions.add(router$);
  }
}
