import { APP_INITIALIZER, <PERSON>rror<PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { SharedModule } from '@shared/shared.module';
import { AppComponent } from './app.component';
import { HelpModule } from '@help/help.module';
import { SettingsHttpService } from '@core/services';
import { AppErrorHandler } from '@core/interceptors';
import { CoreModule } from '@core/core.module';
import { IntercomComponent } from './intercom/intercom.component';
import { provideMatomo, withRouter } from 'ngx-matomo-client';
import { environment } from '../environments/environment';

export function initializeSettings(settingsHttpService: SettingsHttpService) {
  return () => settingsHttpService.initializeSettings();
}

@NgModule({
  declarations: [
    AppComponent,
    IntercomComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    CoreModule,
    HelpModule,
  ],
  providers: [
    {provide: APP_INITIALIZER, useFactory: initializeSettings, deps: [SettingsHttpService], multi: true},
    provideMatomo({
      siteId: environment.matomoTracker.siteId,
      trackerUrl: environment.matomoTracker.trackerUrl,
      disabled: !environment.enableTracking
    },
    withRouter({
      exclude: [/^\/auth\/login$/]
    })),
    {
      provide: ErrorHandler,
      useClass: AppErrorHandler
    }
  ],
  bootstrap: [AppComponent]
})

export class AppModule {
}
