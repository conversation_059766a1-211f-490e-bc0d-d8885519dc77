import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { ApplicantAliasesComponent } from './applicant-aliases.component';
import { AuthGuard } from '@core/guards';

const routes: Routes = [
  {path: '', component: ApplicantAliasesComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ApplicantAliasesRoutingModule {
}
