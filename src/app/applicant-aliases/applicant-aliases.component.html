<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>
  <app-workspace-menu workspaceTab="applicant-aliases"></app-workspace-menu>

  <div class="flex-fill page-content container justify-content-start mb-4">
    <div class="d-flex flex-column">

      <app-page-bar pageTitle="Applicant aliases">
        <app-filter-term-input leftSide placeHolder="Search for applicant aliases" (termChanged)="filterAlias($event)"></app-filter-term-input>
        <ng-container rightSide>
          <a href="javascript:void(0)" class="item-bar icon-delete" [ngClass]="{'disabled': !hasSelectedAliases}"
             (click)="deleteSelectedAliases()" ngbTooltip="Delete selected applicant aliases" container="body">
            Delete
          </a>
        </ng-container>
      </app-page-bar>

      <div class="overflow-auto flex-fill">
        <div *ngIf="!loading else loadingAliasesTemplate">
          <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
          <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>

          <table class="table table-condensed publication-table w-100 table-hover" *ngIf="aliases.length">
            <thead class="w-100">
                <tr>
                    <th width="30px">
                      <label class="checkbox m-0 p-0">
                        <input type="checkbox" (change)="selectAllAliases($event)" [checked]="selectedAliases.length === aliases.length">
                        <span class="no-text">&nbsp;</span>
                      </label>
                    </th>
                    <th>Alias</th>
                    <th width="160px">No. of applicants</th>
                    <th>Applicant names</th>
                    <th width="70px"></th>
                </tr>
            </thead>

            <tbody *ngFor="let al of aliases| paginate: paginationParams; index as i"
                   [ngClass]="{'has-detail-bg': openedRows.has(al.id), 'preview-border': openedRows.has(al.id) && (i === 0 || !openedRows.has(aliases[i-1].id)),
                   'preview-border-vertical': openedRows.has(al.id) && i > 0 && openedRows.has(aliases[i-1].id)}">
                <tr [ngClass]="{'detailed open-sans-semi-bold' : openedRows.has(al.id)}">
                  <td>
                    <label class="checkbox m-0 p-0">
                      <input type="checkbox" (change)="selectAlias($event, al)" [checked]="isAliasSelected(al)">
                      <span class="no-text">&nbsp;</span>
                    </label>
                  </td>
                  <td nowrap="nowrap" (click)="openDetail(al.id)">{{ al.alias_name }}</td>
                  <td (click)="openDetail(al.id)" align="center">{{ al.applicants.length }}</td>
                  <td (click)="openDetail(al.id)">
                    <div class="text-ellipsis text-ellipsis-1">
                      {{ getApplicantNames(al) }}
                    </div>
                  </td>
                  <td class="table-actions">
                    <div class="d-flex align-items-center justify-content-center gap-spacing-sm">
                      <div class="cursor-pointer button-main-secondary-grey button-medium button-square" ngbTooltip="Delete the alias" (click)="deleteAlias(al)">
                        <i class="fa-regular fa-fw fa-trash-alt"></i>
                      </div>

                      <div class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square" [ngClass]="{'preview-close': openedRows.has(al.id)}"
                           (click)="openDetail(al.id)">
                        <span *ngIf="!openedRows.has(al.id)" class="caret-down"><i class="fa-regular fa-angle-down"></i></span>
                        <span *ngIf="openedRows.has(al.id)" class="caret-up"><i class="fa-regular fa-times"></i></span>
                      </div>
                    </div>
                  </td>
                </tr>

                <div class="d-table-row patent-detail-container has-detail-bg" *ngIf="openedRows.has(al.id)">
                  <td colspan="5" class="patent-detail px-1">
                    <div class="d-flex justify-content-between">
                      <div style="width: 30px" class="p-0 m-0"></div>
                      <div class="patent-detail-title px-3 py-2 flex-fill">Applicant names</div>
                      <div style="width: 70px" class="p-0 m-0"></div>
                    </div>

                    <div class="patent-detail-content">
                      <div *ngFor="let app of al.applicants; let last = last;" class="d-flex justify-content-between">
                        <div style="width: 30px" class="p-0 m-0"></div>
                        <div class="ps-4 pe-2 py-2 flex-fill bg-white row-border-top" [ngClass]="{'row-border-bottom': last}">{{ app.applicant }}</div>
                        <div class="p-spacing-md bg-white d-flex justify-content-between align-items-center row-border-top"
                             [ngClass]="{'row-border-bottom': last}">
                          <div ngbTooltip="Delete this applicant" class="button-main-secondary-grey button-small button-square"
                               (click)="deleteApplicant(al, app)">
                              <i class="fa-regular fa-fw fa-trash-alt"></i>
                          </div>
                        </div>
                        <div class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square invisible"></div>
                      </div>
                    </div>
                  </td>
                </div>
            </tbody>
          </table>

          <div class="d-flex justify-content-between mt-2 align-items-center" *ngIf="!errors?.length && aliases?.length">
            <app-page-size [pageSize]="pageSize" (changeSize)="onPageSizeChanged($event)" [pageOptions]="pageOptions"></app-page-size>

            <div class="flex-fill">
              <pagination-controls *ngIf="pagination?.last_page > 1" id="applicant-pagination" class="d-flex justify-content-end"
                                   (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #loadingAliasesTemplate>
  <app-spinner></app-spinner>
</ng-template>
