@import 'scss/layout2021/variables';
@import 'scss/components/input-group';
@import 'scss/components/publication-table';

$icons: ("add-applicant-alias": "icon-plus", "delete": "icon-delete", "edit": "icon-rename");

.icon {
  @each $name, $icon in $icons {
    &-#{$name} {
      padding-left: 25px;
      background-position: 0;
      background-position-y: 7px;
      background-size: 15px;
      background-repeat: no-repeat;
      transition: all 0.2s ease;
      background-image:  url('/assets/images/layout2022/#{$icon}.svg');
      &:hover,&.active{
        background-image:  url('/assets/images/layout2022/#{$icon}-hover.svg');
      }
      &.disabled {
        background-image: url('/assets/images/layout2022/#{$icon}-disabled.svg');
      }
    }
  }
}
