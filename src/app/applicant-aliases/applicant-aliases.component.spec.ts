import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ApplicantAliasesComponent } from '@applicant-aliases/applicant-aliases.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';


describe('ApplicantAliasesComponent', () => {
  let component: ApplicantAliasesComponent;
  let fixture: ComponentFixture<ApplicantAliasesComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ApplicantAliasesComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ApplicantAliasesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
