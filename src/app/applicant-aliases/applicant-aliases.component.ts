import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { PaginationMetadata } from '@core';
import { Alias, ApplicantAlias, ApplicantsAliasesService, ConfirmationDialogService } from '@core/services';
import { BehaviorSubject, forkJoin, Observable, Subscription, switchMap, tap, timer } from "rxjs";
import { catchError, debounce, filter } from 'rxjs/operators';

@Component({
  selector: 'app-applicant-aliases',
  templateUrl: './applicant-aliases.component.html',
  styleUrls: ['./applicant-aliases.component.scss'],
})

export class ApplicantAliasesComponent implements OnInit, OnDestroy {

  aliases: Array<Alias> = [];
  filterTerm = '';
  pageSize = 25;
  pagination = {current_page: 1, page_size: this.pageSize, last_page: 0, total_hits: 0} as PaginationMetadata;
  pageOptions = [25, 50, 100];

  loading = false;
  openedRows: Set<number> = new Set<number>();
  successMessage: string;
  errors: Array<string> = [];

  selectedAliases: Array<Alias> = [];

  private loadAliasesSubject = new BehaviorSubject<{ delay: number, term: string }>({delay: 0, term: null});
  private subscriptions = new Subscription();

  constructor(
    private applicantsAliasesService: ApplicantsAliasesService,
    private confirmationDialogService: ConfirmationDialogService,
  ) {
  }

  get hasSelectedAliases() {
    return this.selectedAliases.length > 0;
  }

  get paginationParams(): any {
    return {
      itemsPerPage: this.pagination.page_size,
      id: 'applicant-pagination',
      currentPage: this.pagination.current_page,
      totalItems: this.pagination.total_hits
    };
  }

  ngOnInit() {
    const loadAliases$ = this.loadAliasesSubject.asObservable()
      .pipe(
        filter((val)  => !!val),
        debounce(({delay}) => timer(delay)),
        switchMap(({term}) => this.loadAliases(term))
      )
      .subscribe();
    this.subscriptions.add(loadAliases$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  filterAlias(term: string) {
    this.pagination.current_page = 1;
    this.filterTerm = term.trim();
    this.reloadAliases(1000);
  }

  deleteAlias(alias: Alias) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${alias.alias_name}</span> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        const obs = alias.applicants.map(applicant => this.deleteApplicantAlias(alias, applicant.id));
        const forkJoin$ = forkJoin(obs).subscribe({
          next: response => {
            this.successMessage = `The alias ${alias.alias_name} was successfully deleted!`;
          }, error: err => {
            console.log(err);
            this.errors = [`The alias ${alias.alias_name} could not be deleted!`];
          }
        });
        this.subscriptions.add(forkJoin$);
      }
    });
  }

  deleteApplicant(alias: Alias, applicant: ApplicantAlias) {

    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${applicant.applicant}</span>
        <div>of alias <b>${alias.alias_name}</b></div>?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        const delete$ = this.applicantsAliasesService.delete(applicant.id).subscribe({
          next: response => {
            const index = alias.applicants.findIndex(appl => appl.id === applicant.id);
            alias.applicants.splice(index, 1);
            if (alias.applicants.length === 0) {
              this.reloadAliases();
              this.openedRows.delete(alias.id);
              this.reloadAllApplicantAliases();
            }
            this.successMessage = `The applicant ${applicant.applicant} of alias ${alias.alias_name} was successfully deleted!`;
          }, error: err => {
            console.log(err);
            this.errors = [`The applicant ${applicant.applicant} of alias ${alias.alias_name} could not be deleted!`];
          }
        });
        this.subscriptions.add(delete$);
      }
    });
  }

  openDetail(alId): void {
    if (this.openedRows.has(alId)) {
      this.openedRows.delete(alId);
    } else {
      this.openedRows.add(alId);
    }
  }

  deleteSelectedAliases() {
    const subject = this.selectedAliases.length > 1 ? 'aliases' : 'alias';
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <br/> <b>delete ${this.selectedAliases.length} selected ${subject}</b> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        const obs = [];

        for (const alias of this.selectedAliases) {
          for (const applicant of alias.applicants) {
            obs.push(this.deleteApplicantAlias(alias, applicant.id));
          }
        }

        const forkJoin$ = forkJoin(obs).subscribe({
          next: response => {
            this.successMessage = `The ${subject}: ${this.selectedAliases.map((al) => al.alias_name).join(', ')} ${this.selectedAliases.length > 1 ? 'have' : 'has'} been successfully deleted!`;
            this.selectedAliases = [];
          }, error: err => {
            console.log(err);
            this.errors = [`The selected ${subject} could not be deleted!`];
            this.selectedAliases = [];
          }
        });
        this.subscriptions.add(forkJoin$);
      }
    });
  }

  selectAlias(event: Event, al: Alias) {
    if (event.target['checked']) {
      if (!this.selectedAliases.some((a) => a.id === al.id)) {
        this.selectedAliases.push(al);
      }
    } else {
      this.selectedAliases = this.selectedAliases.filter((a) => a.id !== al.id);
    }
  }

  selectAllAliases(event: Event) {
    if (event.target['checked']) {
      this.selectedAliases = this.aliases;
    } else {
      this.selectedAliases = [];
    }
  }

  isAliasSelected(al: Alias): boolean {
    return this.selectedAliases.some((a) => a.id === al.id);
  }

  getApplicantNames(al: Alias): string {
    return al.applicants.map((a) => a.applicant).join(', ');
  }

  onPageSizeChanged(size: number) {
    this.pageSize = size;
    this.pagination.current_page = 1;
    this.reloadAliases();
  }

  onPageChange(page: number) {
    this.pagination.current_page = page;
    this.reloadAliases();
  }

  private loadAliases(term: string) {
    this.aliases = [];
    this.errors = [];
    this.loading = true;

    const payload = {
      page_size: this.pageSize,
      page: this.pagination.current_page
    };

    if (term) {
      payload['alias'] = `like:${term}`;
    }

    return this.applicantsAliasesService.getApplicantAliases(payload).pipe(
      tap(({applicant_aliases, page}) => {
        if (!applicant_aliases.length) {
          this.errors = ['You do not have any aliases yet.'];
          this.loading = false;
          return;
        }
        this.pagination = page;
        this.aliases = this.applicantsAliasesService.applicantsToAliases(applicant_aliases);
        this.loading = false;
      }),
      catchError((error) => {
        this.loading = false;
        this.errors = ['An error occurred while loading aliases'];
        console.error(error);
        throw error;
      })
    );
  }

  private deleteApplicantAlias(alias: Alias, applicantAliasId: number): Observable<any> {
    return this.applicantsAliasesService.delete(applicantAliasId).pipe(
      tap(() => {
        const index = alias.applicants.findIndex(appl => appl.id === applicantAliasId);
        alias.applicants.splice(index, 1);

        if (alias.applicants.length === 0) {
          this.reloadAliases();
          this.openedRows.delete(alias.id);
          this.reloadAllApplicantAliases();
        }
      })
    );
  }

  private reloadAliases(delay: number = 500) {
    this.loadAliasesSubject.next({delay: delay, term: this.filterTerm});
  }

  private reloadAllApplicantAliases() {
    const obs$ = this.applicantsAliasesService.getAllApplicantAliases(true).subscribe();
    this.subscriptions.add(obs$);
  }
}
