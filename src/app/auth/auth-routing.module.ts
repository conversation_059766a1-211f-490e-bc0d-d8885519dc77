import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TermsAndConditionsComponent } from '@auth/terms-and-conditions/terms-and-conditions.component';
import { SignupComponent } from '@auth/signup/signup.component';
import { IPLoungeComponent } from '@auth/ip-lounge/ip-lounge.component';
import { SignupConfirmationComponent } from '@auth/signup-confirmation/signup-confirmation.component';
import { LoginComponent } from '@auth/login/login.component';
import { ForgotPasswordComponent } from '@auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from '@auth/reset-password/reset-password.component';
import { ChangePasswordComponent } from '@auth/change-password/change-password.component';
import { ResendMailConfirmationComponent } from '@auth/resend-mail-confirmation/resend-mail-confirmation.component';
import {
  TermsAndConditionsDeeplComponent
} from '@auth/terms-and-conditions-deepl/terms-and-conditions-deepl.component';

const routes: Routes = [
  {path: '', redirectTo: 'login', pathMatch: 'full'},
  {path: 'login', component: LoginComponent},
  {path: 'signup', component: SignupComponent},
  {path: 'forgot-password', component: ForgotPasswordComponent},
  {path: 'reset-password', component: ResetPasswordComponent},
  {path: 'resend-mail-confirmation', component: ResendMailConfirmationComponent},
  {path: 'signup-confirmation', component: SignupConfirmationComponent},
  {path: 'terms-and-conditions', component: TermsAndConditionsComponent},
  {path: 'terms-and-conditions-deepl', component: TermsAndConditionsDeeplComponent},
  {path: 'change-password', component: ChangePasswordComponent},
  {path: 'ip-lounge', component: IPLoungeComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthRoutingModule {
}
