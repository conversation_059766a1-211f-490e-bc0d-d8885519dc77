import { NgModule } from '@angular/core';
import { TermsAndConditionsComponent } from '@auth/terms-and-conditions/terms-and-conditions.component';
import { SharedModule } from '@shared/shared.module';
import { SignupComponent } from '@auth/signup/signup.component';
import { IPLoungeComponent } from '@auth/ip-lounge/ip-lounge.component';
import { AuthRoutingModule } from '@auth/auth-routing.module';
import { SignupConfirmationComponent } from '@auth/signup-confirmation/signup-confirmation.component';
import { LoginComponent } from '@auth/login/login.component';
import { ForgotPasswordComponent } from '@auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from '@auth/reset-password/reset-password.component';
import { ChangePasswordComponent } from '@auth/change-password/change-password.component';
import { ResendMailConfirmationComponent } from '@auth/resend-mail-confirmation/resend-mail-confirmation.component';
import {
  TermsAndConditionsDeeplComponent
} from '@auth/terms-and-conditions-deepl/terms-and-conditions-deepl.component';

@NgModule({
  imports: [
    SharedModule,
    AuthRoutingModule
  ],
  declarations: [
    SignupComponent,
    LoginComponent,
    ForgotPasswordComponent,
    ResendMailConfirmationComponent,
    SignupConfirmationComponent,
    ResetPasswordComponent,
    ChangePasswordComponent,
    TermsAndConditionsComponent,
    TermsAndConditionsDeeplComponent,
    IPLoungeComponent
  ]
})
export class AuthModule {
}
