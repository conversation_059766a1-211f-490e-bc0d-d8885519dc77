<div class='auth-container d-flex flex-column justify-content-start align-items-center'
     *ngIf="!loadingToken else loader">
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="auth-form">
      <div class="mb-3">
        <input id="password" formControlName="password" type="password" class="form-control"
               [ngClass]="{ 'is-invalid' :validate('password')}" placeholder="Current password" appAutofocus/>
        <div class="invalid-feedback">
          Please enter current password.
        </div>
      </div>

      <div class="mb-3">
        <input id="new-password" formControlName="new_password" type="password" class="form-control"
               [ngClass]="{ 'is-invalid' :validate('new_password')}" placeholder="New password"/>
        <div class="invalid-feedback">
          Please enter new password.
        </div>
      </div>

      <div class="mb-3 last">
        <input id="confirm-new-password" formControlName="confirm_new_password" type="password" class="form-control"
               [ngClass]="{ 'is-invalid' :validate('confirm_new_password')}" placeholder="Confirm new password"/>
        <div class="invalid-feedback">
          Please enter confirm new password.
        </div>
      </div>

      <div class="mb-2 p-0" *ngIf="showGdpr">
        <label class="checkbox">
          <input class="form-check-input" type="checkbox" formControlName="gdpr" >
          <span class="form-check-label">I agree to GDPR - Consent to communication</span>
        </label>

        <div class="caption-2 mb-0">
          With the submission of this form, I give my consent to receiving information on Intellectual Property matters,
          services provided by Dennemeyer, and invitations to Dennemeyer's events. "Dennemeyer" means the group of entities to
          which belong Dennemeyer Octimine GmbH, Dennemeyer S.A., Dennemeyer &amp; Associates S.A., Dennemeyer Consulting
          GmbH, and their respective affiliates. I am aware that I can withdraw my consent at any time, and that I can do so
          by clicking a link in any email I will receive.
        </div>
      </div>

      <div class="mb-3 last" *ngIf="showToc">
        <label class="checkbox mb-0">
          <input class="form-check-input" type="checkbox" formControlName="terms_and_conditions" [ngClass]="validateToc()">
          <span class="form-check-label">I agree to Octimine's</span>
        </label>
        <a (click)="onTocClicked(tocModalContent)" class="ms-2 cursor-pointer">Terms and Conditions</a>
      </div>

      <div class="d-flex justify-content-end align-items-center">
        <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="loading">
        <button type="submit" class="btn btn-secondary btn-lg" [disabled]="loading">Change password</button>
      </div>
    </form>
  </div>
</div>

<ng-template #loader>
  <div class="d-flex justify-content-center">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</ng-template>

<ng-template #tocModalContent let-modal>
  <div class="modal-header">
    <div class="modal-title">Octimine's terms and conditions</div>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')" tabindex="-1"></button>
  </div>
  <div class="modal-body p-0 m-0">
    <div class="terms-and-conditions">
      <app-editable-page pageName="terms_and_conditions"></app-editable-page>
    </div>
  </div>
</ng-template>
