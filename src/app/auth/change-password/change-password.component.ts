import { RoutingHistoryService, UserService } from '@core/services';
import { COUNTRIES, User } from '@core/models';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Component, OnDestroy, OnInit, TemplateRef } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Subscription } from 'rxjs';
import { first } from 'rxjs/operators';


@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class ChangePasswordComponent implements OnInit, OnDestroy {

  form: UntypedFormGroup;
  errors: Array<string> = [];
  loading = false;
  loadingToken = false;
  showGdpr = false;
  showToc = false;
  modal: NgbActiveModal = null;

  private subscriptions = new Subscription();

  private user: User = null;
  private isFormInvalid = false;
  private listCountries = [];

  constructor(
    private router: Router,
    private userService: UserService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private routingHistoryService: RoutingHistoryService
  ) {
  }

  ngOnInit() {
    this.form = new UntypedFormGroup({
      password: new UntypedFormControl('', [Validators.required]),
      new_password: new UntypedFormControl('', [Validators.required]),
      confirm_new_password: new UntypedFormControl('', [Validators.required]),
      gdpr: new UntypedFormControl(false),
      terms_and_conditions: new UntypedFormControl(false, [Validators.required]),
    });

    for (const c of Object.keys(COUNTRIES)) {
      this.listCountries = this.listCountries.concat(COUNTRIES[c]);
    }

    const user$ = this.userService.user.subscribe({
      next: (user) => {
        this.user = user;
        this.showTocAndGdpr(user);
      }
    });
    this.subscriptions.add(user$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onSubmit() {
    this.resetErrors();

    if (this.isDataValid()) {
      this.doChangePassword();
    }
  }

  validate(field: string) {
    const control = this.form.get(field);
    return (!control.valid && control.touched);
  }

  validateToc() {
    const control = this.form.get('terms_and_conditions');
    return !control.value && this.isFormInvalid ? 'is-invalid' : '';
  }

  onTocClicked(modalContent: TemplateRef<any>) {
    this.modal = this.modalService.open(modalContent, {size: 'lg'});
  }

  private showTocAndGdpr(user: User) {
    const profile = user && user.profile ? user.profile : null;

    if (profile) {
      const country = this.listCountries.find(o => o.name === profile.country);
      this.showGdpr = !profile.gdpr && profile.must_change_password && country && country.gdpr;
      this.showToc = !profile.terms_and_conditions && profile.must_change_password;
    } else {
      this.showGdpr = false;
      this.showToc = false;
    }
  }

  private resetErrors(): void {
    this.errors = [];
    this.isFormInvalid = false;

    Object.keys(this.form.controls).forEach(field => {
      this.form.get(field).markAsTouched({onlySelf: true});
    });
  }

  private isDataValid() {

    if (!this.user) {
      this.router.navigate(['auth/login']);
      return false;
    }

    const {controls} = this.form;

    if (!controls.password.value.length || !controls.new_password.value.length
      || !controls.confirm_new_password.value.length) {
      return false;
    }

    if (controls.password.value === controls.new_password.value) {
      this.errors = ['New password must be different from current password.'];
      return false;
    }

    if (controls.new_password.value !== controls.confirm_new_password.value) {
      this.errors = ['Confirm new password should be same as new password.'];
      return false;
    }

    if ((this.showToc && !controls.terms_and_conditions.value)) {
      this.isFormInvalid = true;
      return false;
    }

    return true;
  }

  private doChangePassword() {
    const {controls} = this.form;
    this.loading = true;

    const changePassword$ = this.userService.changePassword({
      email: this.user.profile.email,
      current_password: controls.password.value,
      new_password: controls.new_password.value,
      terms_and_conditions: this.showToc ? controls.terms_and_conditions.value : this.user.profile.terms_and_conditions,
      gdpr: this.showGdpr ? controls.gdpr.value : this.user.profile.gdpr
    })
      .pipe(first())
      .subscribe({
        next: (user: User) => {
          const signin$ = this.userService
            .signin({
              username: this.user.profile.email,
              password: controls.new_password.value
            })
            .subscribe({
              next: () => this.router.navigateByUrl(this.routingHistoryService.getReferralUrl()),
              error: (error) => {
                console.log(error);
                this.loading = false;
                const {message} = error.error;
                this.errors = [message];
              }
            });
          this.subscriptions.add(signin$);
        },
        error: (error)=> {
          console.log(error);
          this.loading = false;

          if (!(error instanceof HttpErrorResponse)) {
            return;
          }

          const {message} = error.error;

          if (/Validation error/gi.test(message)) {
            this.errors = message.replace('Validation error:', '').split('\n');
          } else {
            this.errors = [message];
          }
        }
      });

    this.subscriptions.add(changePassword$);
  }
}
