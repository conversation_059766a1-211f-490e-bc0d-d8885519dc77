<div class='auth-container d-flex flex-column justify-content-start align-items-center'>
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">

    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="auth-form">
      <app-alert type="success" *ngIf="success" message="Email sent successfully, check your inbox."></app-alert>
      <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

      <div class="mb-3">
        <label for="email" class="form-label">Enter your registered e-mail below to receive the password retrieval instructions:</label>
        <input #emailEle id="email" formControlName="email" type="email" class="form-control" placeholder="Email"
               [ngClass]="validate('email')">
        <div class="invalid-feedback">
          Please enter a valid email.
        </div>
      </div>

      <div class="d-flex justify-content-between align-items-center">
        <a routerLink="/auth/login" class="cursor-pointer">
          Back to the sign in form
        </a>

        <div class="d-flex justify-content-end align-items-center">
          <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="isLoading">
          <input type="submit" class="btn btn-secondary btn-lg" [disabled]="isLoading" value="Send instructions">
        </div>
      </div>
    </form>
  </div>
</div>
