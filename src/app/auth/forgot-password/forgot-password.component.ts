import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { SettingsService, UserService } from '@core/services';
import { finalize, first } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';


@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class ForgotPasswordComponent implements OnInit, AfterViewInit, OnDestroy {
  public form: UntypedFormGroup;
  public success = false;
  isLoading = false;
  errors: Array<string> = [];

  @ViewChild('emailEle') emailEle: ElementRef;
  private subscriptions = new Subscription();

  constructor(
    private userService: UserService,
    private settingsService: SettingsService
  ) {
  }

  ngOnInit() {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngAfterViewInit() {
    if (this.emailEle) {
      this.emailEle.nativeElement.focus();
    }
  }

  validate(field: string) {
    const control = this.form.get(field);
    return {'is-invalid': !control.valid && control.touched && control.dirty && control.value};
  }

  onSubmit(): void {
    if (this.form.invalid) {
      Object.keys(this.form.controls).forEach(field => {
        this.form.get(field).markAsTouched({onlySelf: true});
        this.form.get(field).markAsDirty({onlySelf: true});
      });
      return;
    }

    const payload = {
      ...this.form.value
    };

    this.success = false;
    this.errors = [];
    this.isLoading = true;

    const forgotPassword$ = this.userService.forgotPassword(payload)
      .pipe(
        first(),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: () => {
          this.success = true;
          this.resetForm();
        },
        error: (error) => {
          this.resetForm();

          if (!(error instanceof HttpErrorResponse)) {
            return;
          }

          if (error.status === 0) {
            this.errors = ['The server cannot be reached. Please check your internet connection.'];
            return;
          }

          const message = error.error?.message || error.message;
          this.errors = [message];
        }
      });
    this.subscriptions.add(forgotPassword$);
  }

  private buildForm(): void {
    this.form = new UntypedFormGroup({
      email: new UntypedFormControl('', [Validators.required, Validators.email])
    });
  }

  private resetForm(): void {
    const {controls} = this.form;

    controls.email.setValue('');
  }

}
