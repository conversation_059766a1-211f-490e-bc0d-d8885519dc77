<div class='auth-container d-flex flex-column justify-content-start align-items-center'>
    <img src="assets/images/logo.png" alt="logo" class="logo">
  
    <div class="auth-form-wrapper">
  
      <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
  
      <div class="text-center align-items-center" *ngIf="loading">
          <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner">
      </div>
      <div *ngIf="errors && !loading" class="mb-3">
        <button type="button" class="btn btn-secondary btn-lg" (click)="goBack()">&lt; Go back</button>
      </div>
  
    </div>
  </div>
  
  <ng-template #loader>
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
  </ng-template>
  