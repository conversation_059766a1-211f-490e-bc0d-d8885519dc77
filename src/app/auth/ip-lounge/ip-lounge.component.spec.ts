import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IPLoungeComponent } from './ip-lounge.component';
import { provideMatomo } from 'ngx-matomo-client';
import { RouterModule } from '@angular/router';

describe('IPLoungeComponent', () => {
  let component: IPLoungeComponent;
  let fixture: ComponentFixture<IPLoungeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [IPLoungeComponent],
      imports: [ReactiveFormsModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(IPLoungeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
