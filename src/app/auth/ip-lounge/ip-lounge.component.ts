import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { UserService } from '@core/services';
import { HttpErrorResponse } from '@angular/common/http';
import { User } from '@core/models';
import { Subscription } from 'rxjs';

/**
 * This component is used for IP Lounge integration.
 * When IP Lounge user is forwarded to Octimine, the URL looks like 'http://octimine.com/auth/ip-lounge?token=...&from=...&url=...'
 * The token here is JWT token from IP Lounge.
 * We extract the token from params and ask backend to validate it.
 */
@Component({
  selector: 'app-ip-lounge',
  templateUrl: './ip-lounge.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class IPLoungeComponent implements OnInit, OnDestroy {

  public loading = true;
  public errors: Array<string> = [];
  private originalUrl: string;
  private redirectTo: string = 'launchpad';
  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    private userService: UserService,
    private route: ActivatedRoute,
    private location: Location,
  ) {
  }

  ngOnInit(): void {
    const queryParams$ = this.route.queryParams.subscribe({
      next: (params) => {
        this.originalUrl = params.from;
        if (params.url) {
          this.redirectTo = decodeURIComponent(params.url);
        }
        if (params.token) {
          this.loginIPLounge(params.token);
        } else {
          this.errors = ['No access token provided. Please contact the administrator of the system.'];
          this.loading = false;
        }
      }
    });
    this.subscriptions.add(queryParams$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  goBack(): void {
    if (this.originalUrl) {
      window.location.href = this.originalUrl;
    } else {
      this.location.back();
    }
  }

  private loginIPLounge(token: string): void {
    this.loading = true;
    const loginIPLounge$ = this.userService.loginIPLounge(token)
      .subscribe({
        next: (user: User) => {
          this.errors = [];
          this.loading = false;
          this.router.navigateByUrl(this.redirectTo);
        },
        error: (error)=> {
          this.loading = false;
          console.log(error);
          if (!(error instanceof HttpErrorResponse)) {
            this.errors = ['Unexpected error. Please contact your administrator.'];
          } else if (error.status === 0) {
            this.errors = ['The server cannot be reached. Please check your internet connection.'];
          } else {
            this.errors = error.error?.message ? [error.error.message] : [error.message];
          }
        }
      });
    this.subscriptions.add(loginIPLounge$);
  }
}
