<div class='auth-container d-flex flex-column justify-content-start align-items-center' *ngIf="!loadingToken else loader">
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">

    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

    <div class="mb-3" *ngIf="missingMailConfirmation">
      <a routerLink="/auth/resend-mail-confirmation">
        Click here to resend the mail confirmation link.
      </a>
    </div>

    <h4 class="text-center font-open-sans-bold mb-4">Welcome</h4>

    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="auth-form auth-signin-form">
      <div *ngIf="!is2FA">
        <div class="text-center font-open-sans-regular mb-3">
          <ng-container *ngIf="loginMethod === loginMethodEnum.SAML">Sign in with your email to continue to Octimine
          </ng-container>
          <ng-container *ngIf="loginMethod === loginMethodEnum.PASSWORD">{{ form.get('email').value }}</ng-container>
        </div>

        <app-alert *ngIf="wasInvited" type="success" message="You have been invited to join a team.<br>Log in to continue."></app-alert>

        <div class="control-row" [hidden]="loginMethod === loginMethodEnum.PASSWORD">
          <input #emailEle appAutofocus id="email" formControlName="email" type="email" class="form-control"
                 [ngClass]="{'is-invalid': isInvalidField('email')}" placeholder="Email" autocomplete="email"
                 [autoFocusEnabled]="loginMethod === loginMethodEnum.SAML"/>
          <div class="invalid-feedback">
            Please enter a valid email.
          </div>
        </div>

        <div class="control-row last" [hidden]="loginMethod === loginMethodEnum.SAML">
          <input #passwordEle appAutofocus id="password" formControlName="password" type="password" class="form-control"
                 [ngClass]="{'is-invalid': isInvalidField('password')}" placeholder="Password" tabindex="2"
                 [autoFocusEnabled]="loginMethod === loginMethodEnum.PASSWORD"/>
          <div class="invalid-feedback">
            Please enter a password.
          </div>
        </div>

        <div class="d-flex justify-content-between align-items-center">
          <div>
            <a routerLink="/auth/forgot-password" class="forgot-password-link">Forgot password?</a>
          </div>

          <div class="d-flex justify-content-end align-items-center">
            <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="loading || checkingMethod">

            <button *ngIf="loginMethod === loginMethodEnum.SAML" id="getLoginMethodBtn" type="submit" class="btn btn-secondary btn-lg"
                    [disabled]="checkingMethod || !form.dirty || !emailEle.value">
              Continue
            </button>

            <ng-container *ngIf="loginMethod === loginMethodEnum.PASSWORD">
              <div *ngIf="!loading" class="font-open-sans-regular pe-3 cursor-pointer" (click)="backToEmailField()">Back</div>
              <button type="submit" class="btn btn-secondary btn-lg" [disabled]="loading">Sign in</button>
            </ng-container>
          </div>
        </div>
      </div>

      <div *ngIf="is2FA">
        <div class="text-center font-open-sans-regular mb-3">
          Please enter your two-factor authentication code
        </div>
        <div class="control-row last">
          <input appAutofocus #codeEle id="code" formControlName="code" class="form-control" placeholder="Code"
                 [ngClass]="{'is-invalid': isInvalidField('code')}" maxlength="6"
                 [autoFocusEnabled]="is2FA"/>
          <div class="invalid-feedback">
            Please enter a valid code.
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-center">
          <a href="javascript:void(0)" class="forgot-password-link" (click)="backSign()">Back to the sign in form</a>

          <div class="d-flex justify-content-end align-items-center">
            <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="loading">
            <button type="submit" class="btn btn-secondary btn-lg" [disabled]="loading">Verify</button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="auth-signin-link">
    <span>Not yet a user? </span><a routerLink="/auth/signup"> Sign up for free!</a>
  </div>
</div>

<ng-template #loader>
  <div class="d-flex justify-content-center">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</ng-template>
