import { AuthService, RecaptchaService, RoutingHistoryService, UserService } from '@core/services';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';
import { first, tap } from 'rxjs/operators';
import { LoginMethodEnum, User } from '@core/models';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class LoginComponent implements OnInit, AfterViewInit, OnDestroy {
  public form: UntypedFormGroup;
  public errors: Array<string> = [];
  public loading = false;
  public loadingToken = false;
  public missingMailConfirmation: boolean = false;
  public is2FA = false;
  loginMethod = LoginMethodEnum.SAML;
  loginMethodEnum = LoginMethodEnum;
  checkingMethod = false;

  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    private userService: UserService,
    private route: ActivatedRoute,
    private routingHistoryService: RoutingHistoryService,
    private recaptchaService: RecaptchaService,
    private authService: AuthService,
  ) {
  }

  ngOnInit() {
    this.form = new UntypedFormGroup({
      email: new UntypedFormControl('', [Validators.email, Validators.required]),
      password: new UntypedFormControl('', [Validators.required]),
      code: new UntypedFormControl('', [Validators.minLength(6), Validators.maxLength(6)])
    });
    const queryParams$ = this.route.queryParams.subscribe({
      next: (params) => {
        if (params.error) {
          this.errors = [params.error]
        } else if (params.access_token) {
          this.loginWithToken(params.access_token);
        }
      }
    });
    this.subscriptions.add(queryParams$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngAfterViewInit() {
    this.recaptchaService.clear();
  }

  public onSubmit() {
    this.resetErrors();

    if (this.loginMethod === LoginMethodEnum.SAML) {
      this.getLoginMethod();
      return;
    }

    if (this.form.invalid) {
      Object.keys(this.form.controls).forEach(field => {
        this.form.get(field).markAsTouched({onlySelf: true});
        this.form.get(field).markAsDirty({onlySelf: true});
      });
      return;
    }

    const {controls} = this.form;
    const payload = {
      username: controls.email.value,
      password: controls.password.value
    };
    if (this.is2FA) {
      if (!this.validateCode2FA()) {
        return;
      }
      payload['verification_code'] = controls.code.value;
    }

    this.loading = true;
    const signin$ = this.userService.signin(payload)
      .pipe(first())
      .subscribe({
        next: (user: User) => {
          if (user.profile.must_change_password) {
            this.router.navigate(['/auth/change-password']).then();
          } else {
            this.router.navigateByUrl(this.routingHistoryService.getReferralUrl());
          }
        },
        error: error => {
          this.loading = false;
          if (!(error instanceof HttpErrorResponse)) {
            return;
          }

          if (error.status === 0) {
            this.errors = ['The server cannot be reached. Please check your internet connection.'];
            return;
          }

          if (error.status === 418) {
            this.is2FA = true;
            return;
          }

          const {message} = error.error;

          if (error.status === 429) {
            this.errors = [message];
            return;
          }

          if (error.status !== 401 && error.status !== 403) {
            this.errors = [error.message];
            return;
          }

          if (this.is2FA) {
            this.errors = ['Provided code is incorrect.'];
            this.resetCode2FA();
            return;
          }
          if (/Invalid|The account is pending for deletion/gi.test(message)) {
            this.errors = ['Provided e-mail or password incorrect.'];
          } else if (/Email confirmation is required/gi.test(message)) {
            this.errors = [message];
            this.missingMailConfirmation = true;
          } else if (/No seats available/gi.test(message)) {
            this.errors = ['There are no more user seats available at the moment.'];
          } else {
            this.errors = message;
          }
        }
      });
    this.subscriptions.add(signin$);
  }

  private loginWithToken(access_token: string): void {
    this.loadingToken = true;
    this.userService.setToken(access_token).then((user) => {
      this.resetErrors();
      this.resetForm();
      if (user) {
        this.router.navigateByUrl(this.routingHistoryService.getReferralUrl());
      } else {
        this.router.navigate(['auth/login']);
        this.loadingToken = false;
      }
    });
  }

  private resetErrors(): void {
    this.errors = [];
  }

  private resetForm(): void {
    const {controls} = this.form;

    controls.email.setValue('');
    controls.password.setValue('');
  }

  isInvalidField(field: string): boolean {
    const control = this.form.get(field);
    return control.invalid && control.touched && control.dirty;
  }

  private validateCode2FA(): boolean {
    const codeControl = this.form.get('code');
    if (!codeControl.value) {
      codeControl.markAsTouched();
      codeControl.markAsDirty({onlySelf: true});
      codeControl.setErrors({'incorrect': true});
      return false;
    }

    return true;
  }

  private resetCode2FA(): void {
    this.form.get('code').setValue('');
  }

  backSign(): void {
    this.loading = false;
    this.is2FA = false;
    this.loginMethod = LoginMethodEnum.PASSWORD;
    this.checkingMethod = false;
    this.resetCode2FA();
  }

  get wasInvited(): boolean {
    return this.routingHistoryService.getHistoryUrls().some(url => url.includes('accept-invitation'));
  }

  getLoginMethod() {
    if (this.isInvalidField('email')) {
      return;
    }

    const {email} = this.form.value;
    const payload = {email: email};
    this.checkingMethod = true;
    const getLoginMethod$ = this.authService.getLoginMethod(payload)
      .pipe(
        tap((resp: { method: LoginMethodEnum, sso_url?: string }) => {
          this.checkingMethod = false;
          this.loginMethod = resp.method;
          if (this.loginMethod === LoginMethodEnum.SAML) {
            window.location.href = resp.sso_url;
          }
        })
      )
      .subscribe({
        error: (err) => {
          console.warn(err);
          this.loginMethod = LoginMethodEnum.PASSWORD;
          this.checkingMethod = false;
        }
      });
    this.subscriptions.add(getLoginMethod$);
  }

  backToEmailField() {
    this.loginMethod = LoginMethodEnum.SAML;
  }
}
