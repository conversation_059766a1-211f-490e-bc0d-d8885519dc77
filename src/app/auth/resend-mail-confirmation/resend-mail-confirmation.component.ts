import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ResendMailConfirmationRequest, SettingsService, UserService } from '@core/services';
import { finalize, first } from 'rxjs/operators';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-resend-mail-confirmation',
  templateUrl: './resend-mail-confirmation.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class ResendMailConfirmationComponent implements OnInit, OnDestroy {
  form: UntypedFormGroup;
  success = false;
  errors: Array<string> = [];
  isLoading = false;

  private subscriptions = new Subscription();

  constructor(
    private userService: UserService,
    private settingsService: SettingsService
  ) {
  }

  ngOnInit() {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  public onSubmit(): void {
    this.resetErrors();

    if (!this.form.valid) {
      Object.keys(this.form.controls).forEach(field => {
        this.form.get(field).markAsTouched({onlySelf: true});
      });
      return;
    }

    this.isLoading = true;

    const payload: ResendMailConfirmationRequest = {
      ...this.form.value
    };

    const resendMailConfirmation$ = this.userService.resendMailConfirmation(payload)
      .pipe(
        first(),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: () => {
          this.resetForm();
          this.success = true;
        },
        error: error => {
          const {message} = error.error;
          this.errors = [message];
        }
      });
    this.subscriptions.add(resendMailConfirmation$);
  }

  validate(field: string) {
    const control = this.form.get(field);
    return {'is-invalid': !control.valid && control.touched};
  }

  private buildForm(): void {
    this.form = new UntypedFormGroup({
      email: new UntypedFormControl('', [Validators.required, Validators.email])
    });
  }

  private resetErrors(): void {
    this.errors = [];
  }

  private resetForm(): void {
    const {controls} = this.form;
    this.form.markAsUntouched();
    controls.email.setValue('');
  }
}
