<div class='auth-container d-flex flex-column justify-content-start align-items-center'>
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">
    <div *ngIf="success; else passwordResetForm">
      <app-alert type="success" message="Your password has been successfully reset."></app-alert>

      <a routerLink="/auth/login" class="float-end">
        Continue to Login >
      </a>
    </div>

    <ng-template #passwordResetForm>
      <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

      <div class="mb-3"  *ngIf="!token.valid">
        <a routerLink="/auth/forgot-password">
          Request a new password reset link >
        </a>
      </div>

      <form *ngIf="token.valid" [formGroup]="form" (ngSubmit)="onSubmit()" class="auth-form">
        <div class="mb-3">
          <label for="newPassword" class="form-label">Choose your new password:</label>
          <input id="newPassword" formControlName="newPassword" type="password" class="form-control" placeholder="Password"
                 [ngClass]="{ 'is-invalid' :validate('newPassword')}" appAutofocus>
          <div class="invalid-feedback">
            Please enter a password.
          </div>
        </div>

        <div class="mb-3 last">
          <input formControlName="repeatPassword" type="password" class="form-control" placeholder="Confirm password"
                 [ngClass]="{ 'is-invalid' :validate('repeatPassword')}">
          <div class="invalid-feedback">
            Please enter a confirm password.
          </div>
        </div>

        <div class="mb-2 p-0" *ngIf="form.contains('gdpr')">
          <label class="checkbox">
            <input class="form-check-input" type="checkbox" formControlName="gdpr">
            <span class="form-check-label">I agree to GDPR - Consent to communication</span>
          </label>

          <div class="caption-2 mb-0">
            With the submission of this form, I give my consent to receiving information on Intellectual Property matters,
            services provided by Dennemeyer, and invitations to Dennemeyer's events. "Dennemeyer" means the group of entities to
            which belong Dennemeyer Octimine GmbH, Dennemeyer S.A., Dennemeyer &amp; Associates S.A., Dennemeyer Consulting
            GmbH, and their respective affiliates. I am aware that I can withdraw my consent at any time, and that I can do so
            by clicking a link in any email I will receive.
          </div>
        </div>

        <div class="form-group last" *ngIf="form.contains('termsAndConditions')">
          <label class="checkbox mb-0">
            <input class="form-check-input" type="checkbox" formControlName="termsAndConditions" [ngClass]="{ 'is-invalid' :validate('termsAndConditions')}">
            <span class="form-check-label">I agree to Octimine's</span>&nbsp;
          </label>
          <a (click)="onTocClicked(tocModalContent)" class="ml-2 cursor-pointer">Terms and Conditions</a>
        </div>

        <div class="d-flex justify-content-end align-items-center">
          <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="isSubmitting">
          <input [disabled]="isSubmitting"
                 type="submit" class="btn btn-secondary btn-lg" value="Reset password">
        </div>
      </form>
    </ng-template>

    <ng-template #tocModalContent let-modal>
      <div class="modal-header">
        <div class="modal-title">Octimine's terms and conditions</div>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')" tabindex="-1"></button>
      </div>
      <div class="modal-body p-0 m-0">
        <div class="terms-and-conditions px-5">
          <app-editable-page pageName="terms_and_conditions"></app-editable-page>
        </div>
      </div>
    </ng-template>
  </div>
</div>
