import { Activated<PERSON>oute, Router } from '@angular/router';
import { Component, On<PERSON><PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { ConfirmationToken, RoutingHistoryService, UserService } from '@core/services';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { COUNTRIES } from '@core/models';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class ResetPasswordComponent implements OnInit, OnDestroy {
  public form: UntypedFormGroup;
  public errors: Array<string> = [];
  public success = false;
  showToc = false;
  showGdpr = false;
  modal: NgbActiveModal = null;
  isSubmitting = false;
  token: ConfirmationToken = {value: '', valid: true};
  private email: string = null;
  private listCountries = [];
  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private userService: UserService,
    private modalService: NgbModal,
    private routingHistoryService: RoutingHistoryService
  ) {
    for (const c of Object.keys(COUNTRIES)) {
      this.listCountries = this.listCountries.concat(COUNTRIES[c]);
    }
  }

  ngOnInit() {
    const queryParams$ = this.route.queryParams.subscribe({
      next: ({token, email, showToc, country}) => {
        this.token.value = token;
        this.email = email;
        this.showToc = showToc;
        this.showGdpr = showToc && this.listCountries.find(c => c.name == country);
      }
    });
    this.subscriptions.add(queryParams$);

    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  validate(field: string) {
    const control = this.form.get(field);
    return (!control.valid && control.touched);
  }

  onTocClicked(modalContent: TemplateRef<any>) {
    this.modal = this.modalService.open(modalContent, {size: 'lg'});
  }

  onSubmit(): void {
    if (this.form.invalid) {
      if (this.form.errors) {
        this.errors = ['Passwords do not match'];
      }
      Object.keys(this.form.controls).forEach(field => {
        this.form.get(field).markAsTouched({onlySelf: true});
      });
      return;
    }

    const {newPassword, repeatPassword, termsAndConditions, gdpr} = this.form.value;
    const payload = {
      email: this.email,
      new_password: this.form.value.newPassword,
      token: this.token.value
    };
    if (this.form.value.termsAndConditions) {
      payload['terms_and_conditions'] = this.form.value.termsAndConditions;
    }
    if (this.form.value.gdpr) {
      payload['gdpr'] = this.form.value.gdpr;
    }

    this.isSubmitting = true;

    const resetPassword$ = this.userService.resetPassword(payload).subscribe({
      next: () => {
        const signinPayload = {
          username: payload.email,
          password: payload.new_password
        };
        const signin$ = this.userService.signin(signinPayload)
          .pipe(
            finalize(() => this.isSubmitting = false)
          )
          .subscribe({
            next: () => this.router.navigateByUrl(this.routingHistoryService.getReferralUrl()),
            error: (error) => {
              console.log(error);
              const {message} = error.error;
              this.errors = [message];
            }
          });
        this.subscriptions.add(signin$);
      },
      error: ({error}) => {
        this.isSubmitting = false;
        this.success = false;
        const message = error.message.replace('Validation error: ', '');

        this.errors = [message];

        if (/invalid|expired/gi.test(message)) {
          this.token.valid = false;
        }
      }
    });
    this.subscriptions.add(resetPassword$);
  }

  private buildForm(): void {
    const PasswordMatchValidator: ValidatorFn = (fg: UntypedFormGroup) => {
      const newPassword = fg.get('newPassword').value;
      const repeatPassword = fg.get('repeatPassword').value;
      return newPassword === repeatPassword ? null : {mismatch: true};
    };

    const formFields = {
      newPassword: new UntypedFormControl('', [Validators.required]),
      repeatPassword: new UntypedFormControl('', [Validators.required]),
    }

    if (this.showGdpr) {
      formFields['gdpr'] = new UntypedFormControl(false, []);
    }

    if (this.showToc) {
      formFields['termsAndConditions'] = new UntypedFormControl(false, [Validators.requiredTrue]);
    }

    this.form = new UntypedFormGroup(formFields, {
      validators: [PasswordMatchValidator]
    });
  }

  private resetForm(): void {
    const {controls} = this.form;
    controls.repeatPassword.setValue('');
    controls.newPassword.setValue('');
    if (controls.termsAndConditions) {
      controls.termsAndConditions.setValue(false);
    }
    if (controls.gdpr) {
      controls.gdpr.setValue(false);
    }
  }
}
