@import "scss/layout2021/mixins";
@import "scss/layout2021/variables";

$background-primary: #01526F;
$background-secondary: #379884;

.auth-container {
  position: relative;
  height: 100%;
  min-height: 100vh;
  width: 100%;
  padding-top: 90px;
  background: $background-primary;
  background: -webkit-linear-gradient(135deg, $background-primary, $background-secondary);
  background: -o-linear-gradient(135deg, $background-primary, $background-secondary);
  background: -moz-linear-gradient(135deg, $background-primary, $background-secondary);
  background: linear-gradient(135deg, $background-primary, $background-secondary);

  .logo {
    width: 256px;
    margin-bottom: 1.875rem;
  }


  .auth-form-wrapper {
    width: 536px;
    background: #FFFFFF 0 0 no-repeat padding-box;
    box-shadow: 0 10px 15px #00000029;
    border-radius: 10px;
    padding: 30px 60px 40px;

    .auth-form {
      padding: 0;

      .control-row {
        margin-bottom: 0;
        padding-bottom: 2.5rem;

        &.last {
          padding-bottom: 1.875rem;
        }
      }

      .form-control {
        border-color: #CBDCE2;

        @include placeholder() {
          color: #698A95;
        }
      }

      .dropdown {
        .dropdown-toggle {
          text-align: left;
          border: 1px solid #CBDCE2;
          color: #698A95;
          font-size: 1rem;

          &:after {
            float: right;
            margin-top: 10px;
          }
        }
      }

      .loading-spinner {
        width: 2.25rem;
      }
    }

    .auth-signin-form {
      .forgot-password-link {
        font-size: 1rem;
      }
    }
    .back-to-sign-in-link{
      font-size: .875rem;
    }

    .auth-signup-form {
      .control-row {
        margin-bottom: 0;
        padding-bottom: 1.5rem;

        &.last {
          padding-bottom: 1rem;
        }
      }
    }
  }

  .auth-signin-link, .auth-signup-link {
    padding-top: 1.875rem;
    padding-bottom: 2.5rem;
    color: #FFFFFF;
    font-size: 1.125rem;

    a {
      color: #FF6700;
      font-family: $font-open-sans-bold;
      font-size: 1.125rem;
    }
  }
}

.terms-and-conditions {
  height: 85vh;
  width: 100%;
  overflow-y: auto;
}
