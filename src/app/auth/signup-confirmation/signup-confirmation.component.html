<div class='auth-container d-flex flex-column justify-content-start align-items-center'>
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">
    <div *ngIf="loading" class="text-center">
      Verifying account...
    </div>

    <div *ngIf="!loading">
      <app-alert type="success" message="Your account is now active." *ngIf="!errors.length"></app-alert>

      <ng-container *ngIf="errors.length">
        <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

        <a routerLink="/auth/resend-mail-confirmation" *ngIf="!isConfirmed">
          Request new confirmation link
        </a>
      </ng-container>

      <a *ngIf="isConfirmed" routerLink="/auth/login">
        Back to the sign in form
      </a>
    </div>

  </div>
</div>
