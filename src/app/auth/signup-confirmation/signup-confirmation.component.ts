import { ActivatedRoute } from '@angular/router';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UserService } from '@core/services';
import { Subscription } from 'rxjs';


@Component({
  selector: 'app-signup-confirmation',
  templateUrl: './signup-confirmation.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class SignupConfirmationComponent implements OnInit, OnDestroy {
  public errors: Array<string> = [];
  public loading = true;
  public isConfirmed = false;
  private subscriptions = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private userService: UserService
  ) {
  }

  ngOnInit() {
    const queryParams$ = this.route.queryParams.subscribe({
      next: params => {
        const {token} = params;

        const confirmSignup$ = this.userService.confirmSignup({token}).subscribe({
          next: ({data}) => this.handleSuccess(data),
          error: ({error}) => this.handleError(error)
        });
        this.subscriptions.add(confirmSignup$);
      }
    });
    this.subscriptions.add(queryParams$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private handleSuccess(data): void {
    this.loading = false;
    this.isConfirmed = true;
    if ('reset_password_url' in data) {
      window.location.href = data['reset_password_url'];
    }
  }

  private handleError(error): void {
    this.loading = false;
    this.errors = [error.message];

    if (/already activated/gi.test(error.message)) {
      this.isConfirmed = true;
    }
  }
}
