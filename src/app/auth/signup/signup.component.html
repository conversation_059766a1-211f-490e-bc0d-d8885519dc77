<div class='auth-container d-flex flex-column justify-content-start align-items-center'>
  <img src="assets/images/logo.png" alt="logo" class="logo">

  <div class="auth-form-wrapper">
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
    <app-alert type="success" *ngIf="success" [message]='"Your account has been created successfully. Please check your inbox (or spam folder) for the confirmation e-mail. Click <a href=\"" + referralUrl + "\">here</a> to start using the application."'>
    </app-alert>

    <form [formGroup]="form" (ngSubmit)="onSubmit()" class="auth-form auth-signup-form">
      <div class="control-row">
        <input type="text" formControlName="first_name" class="form-control" [ngClass]="{'is-invalid': validate('first_name')}"
               placeholder="First name *" appAutofocus>
        <div class="invalid-feedback">
          Please enter first name.
        </div>
      </div>

      <div class="control-row">
        <input type="text" formControlName="last_name" class="form-control" [ngClass]="{'is-invalid': validate('last_name')}"
               placeholder="Last name *">
        <div class="invalid-feedback">
          Please enter last name.
        </div>
      </div>

      <div class="control-row">
        <input formControlName="email" type="email" class="form-control" [ngClass]="{'is-invalid': validate('email')}"
               placeholder="Email *">
        <div class="invalid-feedback">
          Please enter a valid email.
        </div>
      </div>

      <div class="control-row">
        <input type="text" formControlName="company_name" class="form-control" [ngClass]="{'is-invalid': validate('company_name')}"
               placeholder="Company name *" autocomplete="organization">
        <div class="invalid-feedback">
          Please enter a company name.
        </div>
      </div>

      <div class="control-row">
        <input formControlName="password" type="password" class="form-control" [ngClass]="{'is-invalid': validate('password')}"
               placeholder="Password *" autocomplete="new-password">
        <div class="invalid-feedback">
          Please enter a password.
        </div>
      </div>

      <div class="control-row">
        <input type="password" formControlName="password_confirmation" class="form-control" [ngClass]="{'is-invalid': validate('password_confirmation') || passwordMismatch}"
               placeholder="Confirm password *" autocomplete="new-password">
        <div class="invalid-feedback">
          <span>
            {{ passwordMismatch? 'Passwords do not match' : 'Please confirm the password' }}
          </span>
        </div>
      </div>

      <div class="control-row">
        <div ngbDropdown>
          <div ngbDropdownToggle id="countries-dropdown">
            <input formControlName="country" (focus)="openCountries($event)" (click)="openCountries($event)"
              [ngClass]="{'is-invalid': validate('country')}" type="text" autocomplete="chrome-off"
              placeholder="Please select country" (keydown.control.enter)="onSubmit()"
              class="form-control"/>
          </div>

          <div ngbDropdownMenu aria-labelledby="countries-dropdown">
            <a class="dropdown-item" (click)="selectCountry(null)" [class.active]="selectedCountry === null">Please select country</a>
            <a class="dropdown-item" *ngFor="let c of filterCountry()"
               [class.active]="selectedCountry?.name === c.name" (click)="selectCountry(c)">
              {{ c.name }}
            </a>
          </div>

          <div class="invalid-feedback">
            Please select a country.
          </div>
        </div>
      </div>

      <div class="mb-2 p-0" *ngIf="showGdpr">
        <label class="checkbox">
          <input class="form-check-input" type="checkbox" formControlName="gdpr" >
          <span class="form-check-label">I agree to GDPR - Consent to communication</span>
        </label>

        <div class="caption-2 mb-0">
          With the submission of this form, I give my consent to receiving information on Intellectual Property matters,
          services provided by Dennemeyer, and invitations to Dennemeyer's events. "Dennemeyer" means the group of entities to
          which belong Dennemeyer Octimine GmbH, Dennemeyer S.A., Dennemeyer &amp; Associates S.A., Dennemeyer Consulting
          GmbH, and their respective affiliates. I am aware that I can withdraw my consent at any time, and that I can do so
          by clicking a link in any email I will receive.
        </div>
      </div>

      <div class="control-row last">
        <label class="checkbox mb-0">
          <input class="form-check-input" type="checkbox" formControlName="terms_and_conditions" [ngClass]="validateToc()">
          <span class="form-check-label">I agree to Octimine's</span>
        </label>
        <a (click)="onTocClicked(tocModalContent)" class="ms-2 cursor-pointer">Terms and Conditions</a>
      </div>

      <div class="d-flex justify-content-end align-items-center">
        <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-spinner" *ngIf="isSubmitting">
        <input [disabled]="isSubmitting"
               type="submit" class="btn btn-secondary btn-lg" value="Sign up for free">
      </div>
    </form>

  </div>

  <div class="auth-signup-link">
    <span>Already a user? </span><a routerLink="/auth/login"> Sign in here!</a>
  </div>
</div>


<ng-template #tocModalContent let-modal>
  <div class="modal-header">
    <div class="modal-title">Octimine's terms and conditions</div>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')" tabindex="-1"></button>
  </div>
  <div class="modal-body p-0 m-0">
    <div class="terms-and-conditions px-5">
      <app-editable-page pageName="terms_and_conditions"></app-editable-page>
    </div>
  </div>
</ng-template>
