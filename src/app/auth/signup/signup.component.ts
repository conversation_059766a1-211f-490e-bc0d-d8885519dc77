import { first, skip } from 'rxjs/operators';
import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { RecaptchaService, RoutingHistoryService, SettingsService, SignupRequest, UserService } from '@core/services';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { COUNTRIES } from '@core/models';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';

declare var $: any;

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['../shared/shared.scss']
})
export class SignupComponent implements OnInit, AfterViewInit, <PERSON><PERSON><PERSON>roy {
  form: UntypedFormGroup;

  errors: Array<string> = [];
  success = false;
  passwordMismatch: boolean;

  modal: NgbActiveModal = null;

  listCountries = [];
  showGdpr = false;
  isSubmitting = false;
  selectedCountry: any;
  private isFormInvalid = false;
  private subscriptions = new Subscription();

  get referralUrl(): string {
    return this.routingHistoryService.getReferralUrl();
  }

  constructor(
    private userService: UserService,
    private modalService: NgbModal,
    private recaptchaService: RecaptchaService,
    private router: Router,
    private routingHistoryService: RoutingHistoryService,
    private settingsService: SettingsService
  ) {
  }

  ngOnInit() {
    for (const c of Object.keys(COUNTRIES)) {
      this.listCountries = this.listCountries.concat(COUNTRIES[c]);
    }
    this.buildForm();

    const token$ = this.recaptchaService.token.pipe(skip(1)).subscribe({
      next: data => {
        this.submitSignupData(data);
      }
    });
    this.subscriptions.add(token$);

    const isAuthenticated$ = this.userService.isAuthenticated.subscribe({
      next: data => {
        if (data) {
          this.recaptchaService.clear();
        }
      }
    });
    this.subscriptions.add(isAuthenticated$);
  }

  ngAfterViewInit() {
    this.recaptchaService.addJs();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  public onSubmit(): void {
    this.resetErrors();
    this.success = false;

    if (this.form.invalid) {
      Object.keys(this.form.controls).forEach(field => {
        this.form.get(field).markAsTouched({onlySelf: true});
      });
      this.isFormInvalid = true;
      return;
    }

    if (!this.form.get('terms_and_conditions').value) {
      this.isFormInvalid = true;
      return;
    }
    if(this.form.get('password').value !== this.form.get('password_confirmation').value){
      this.isFormInvalid = true;
      this.passwordMismatch = true;
      return
    }

    this.isFormInvalid = false;
    this.passwordMismatch = false;
    this.isSubmitting = true;

    this.recaptchaService.getToken();
  }

  selectCountry(c: any) {
    this.selectedCountry = c;
    this.showGdpr = c ? c.gdpr : false;
    this.form.get('country').setValue(c ? c.name : null);
    $('#countries-dropdown').dropdown('toggle');
  }

  validate(field: string) {
    const control = this.form.get(field);
    return (!control.valid && control.touched);
  }

  validateToc() {
    const control = this.form.get('terms_and_conditions');
    return !control.value && this.isFormInvalid ? 'is-invalid' : '';
  }

  onTocClicked(modalContent: TemplateRef<any>) {
    this.modal = this.modalService.open(modalContent, {size: 'lg'});
  }

  filterCountry() {
    if (!this.form.get('country').value) {
      return this.listCountries;
    }
    return this.listCountries.filter(item => item.name.toLowerCase().startsWith(this.form.get('country').value.toLowerCase()));
  }

  existsCountry(listCountries): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null =>
      listCountries.findIndex(item => control.value?.toLowerCase() === item.name.toLowerCase()) > -1
        ? null : {country: control.value};
  }

  private submitSignupData(recaptchaToken: string) {
    this.success = false;

    if (!recaptchaToken) {
      this.isSubmitting = false;
      this.errors = ['The reCAPTCHA is not valid'];
      return;
    }

    const payload: SignupRequest = {
      ...this.form.value,
      token: recaptchaToken
    };

    delete payload['password_confirmation'];

    this.isSubmitting = true;

    const signup$ = this.userService.signup(payload)
      .pipe(first())
      .subscribe({
        next: () => {
          this.resetForm();

          this.success = true;
          this.isSubmitting = false;
        },
        error: ({error})=> {
          this.success = false;
          this.isSubmitting = false;

          const {message} = error;
          const toJson = (msg: string) => JSON.parse(msg.replace(/'/g, '"'));

          if (/Validation error\:/ig.test(message)) {
            const errors = toJson(
              message.replace('Validation error: ', '')
            );

            this.errors = Object.values(errors) || []
              .reduce((curr, next) => [...curr, ...next]);
          }

          if (/(Email already exists)|(The reCAPTCHA is invalid)/gi.test(message)) {
            this.errors = [message];
          }
        }
      });
    this.subscriptions.add(signup$);
  }

  private buildForm(): void {
    this.form = new UntypedFormGroup({
      first_name: new UntypedFormControl('', [Validators.required]),
      last_name: new UntypedFormControl('', Validators.required),
      email: new UntypedFormControl('', [Validators.email, Validators.required]),
      company_name: new UntypedFormControl('', Validators.required),
      password: new UntypedFormControl('', Validators.required),
      password_confirmation: new UntypedFormControl('', Validators.required),
      country: new UntypedFormControl('', [Validators.required, this.existsCountry(this.listCountries)]),
      gdpr: new UntypedFormControl(false),
      terms_and_conditions: new UntypedFormControl(false, [Validators.required]),
    });
  }

  private resetErrors(): void {
    this.errors = [];
  }

  private resetForm(): void {
    Object.keys(this.form.controls).forEach(key => {
      this.form.controls[key].setValue('');
      this.form.controls[key].setErrors(null);
    });

    this.buildForm();
  }

  openCountries(event): any {
    const isOpened = $('.dropdown-menu').hasClass('show');
    if (event.type !== 'focus' && isOpened) {
      event.stopPropagation();
      return;
    }
    if (event.type === 'focus' && !isOpened) {
      $('#countries-dropdown').dropdown('toggle');
      event.stopPropagation();
    }
  }
}
