import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  TermsAndConditionsDeeplComponent
} from '@auth/terms-and-conditions-deepl/terms-and-conditions-deepl.component';
import { provideMatomo } from 'ngx-matomo-client';


describe('TermsAndConditionsComponent', () => {
  let component: TermsAndConditionsDeeplComponent;
  let fixture: ComponentFixture<TermsAndConditionsDeeplComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TermsAndConditionsDeeplComponent],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TermsAndConditionsDeeplComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
