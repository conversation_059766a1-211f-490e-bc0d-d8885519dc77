import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';
import { BooleanTooltipComponent } from './boolean-tooltip.component';


const routes: Routes = [
  {path: '', component: BooleanTooltipComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BooleanTooltipRoutingModule {
}
