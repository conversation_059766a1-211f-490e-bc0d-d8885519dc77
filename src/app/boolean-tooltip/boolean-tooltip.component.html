<div *ngIf="!isLoadingMenuItems else loadingSpinner" class="boolean-tooltip-container d-flex scrollbar-2024" appScreenSizeDetector>
  <div class="navigation" [ngClass]="{'float-menu': isFloatMenuOpened, 'sticky-menu': isStickyMenuOpened}">
    <div class="header-container">
      <ng-container *ngIf="!isStickyMenuOpened">
        <div class="header p-y-spacing-md p-x-spacing-sm">
          <div class="figma-dropdown d-inline-block">
            <div class="button-main-tertiary-grey button-square button-small" [hidden]="!showDefaultMenuButton"
                 appDropdownToggle [closeOnClick]="true" [showOnHover]="true" [(dropdownMenuState)]="isFloatMenuOpened"
                 (toggleEvent)="onDefaultMenuButtonToggled($event)"
                 ngbTooltip="Content" tooltipClass="white-tooltip">
              <i class="figma-dropdown-icon fa-regular fa-bars invert-icon"></i>
            </div>
            <div *ngIf="showExpandMenuButton" class="button-main-tertiary-grey button-square button-small"
                 (click)="onExpandMenuButtonClicked()"
                 ngbTooltip="Expand sidebar" tooltipClass="white-tooltip">
              <i class="fa-regular fa-chevrons-right"></i>
            </div>
            <div class="figma-dropdown-content w-max-content radius-big p-0">
              <div class="d-flex flex-column h-100 overflow-auto">
                <div class="content-heading-h6 content-color-tertiary p-x-spacing-big p-t-spacing-x-big p-b-spacing-big">
                  Content
                </div>
                <div class="horizontal-divider">&nbsp;</div>
                <div class="flex-fill overflow-auto p-r-spacing-sm m-l-spacing-sm m-r-spacing-xxx-s">
                  <div class="figma-dropdown-item p-y-spacing-x-s"
                       *ngFor="let item of navigationItems; let last = last;" (click)="navigateToItem(item, $event)"
                       [ngClass]="{
                            'content-heading-h7 p-x-spacing-sm group-item': item.is_group,
                            'content-heading-h6 p-x-spacing-md clickable-item': !item.is_group,
                            'selected-item': item === currentNavigationItem,
                            'm-b-spacing-xx-big': last
                            }">
                    {{ item.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="horizontal-divider">&nbsp;</div>
      </ng-container>

      <div *ngIf="isStickyMenuOpened" class="d-flex flex-column h-100 overflow-auto d-inline-block">
        <div class="p-x-spacing-sm p-t-spacing-md p-b-spacing-sm">
          <div class="navigation-header d-flex align-items-center">
            <div class="content-heading-h6 content-color-tertiary p-spacing-sm flex-fill">Content</div>

            <div class="button-main-tertiary-grey button-square button-xsmall m-x-spacing-sm collapse-button"
                 (click)="onCollapseMenuButtonClicked()"
                 ngbTooltip="Collapse sidebar" tooltipClass="white-tooltip">
              <i class="fa-regular fa-chevrons-left"></i>
            </div>
          </div>
        </div>
        <div class="horizontal-divider">&nbsp;</div>
        <div class="flex-fill overflow-auto p-r-spacing-sm m-l-spacing-sm m-r-spacing-xxx-s">
          <div class="navigation-item p-y-spacing-x-s m-b-spacing-xx-s"
               *ngFor="let item of navigationItems; let last = last;" (click)="navigateToItem(item, $event)"
               [ngClass]="{
                      'content-heading-h7 p-x-spacing-sm group-item': item.is_group,
                      'content-heading-h6 p-x-spacing-md clickable-item': !item.is_group,
                      'selected-item': item === currentNavigationItem,
                      'm-b-spacing-xx-big': last
                      }">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="main-content flex-fill d-flex flex-column">
    <div class="header-container">
      <div class="header content-heading-h4 content-color-primary d-flex align-items-center flex-column">
        <div class="content-fix-width">{{ currentNavigationItem?.name }}</div>
      </div>
      <div class="horizontal-divider">&nbsp;</div>
    </div>

    <div class="flex-fill overflow-auto m-r-spacing-xxx-s">
      <div class="h-100">
        <div id="boolean-tooltip-content" class="d-flex align-items-center flex-column">
          <app-editable-page [pageName]="currentNavigationItem?.cockpit_name"
                             (loadingEvent)="onLoadingContent($event)"
                             class="content-fix-width">
          </app-editable-page>

          <ng-container *ngIf="navigationItems?.length > 1 && !isLoadingContent">
            <div class="content-fix-width horizontal-divider m-t-spacing-xxx-big">&nbsp;</div>

            <div class="content-fix-width p-x-spacing-xxx-big p-t-spacing-xxx-big p-b-spacing-big d-flex align-items-center justify-content-between">
              <span class="button-main-secondary-grey button-large content-label-large"
                    [ngClass]="{'invisible': !canPrevious}" (click)="goPreviousItem()">
                <i class="fa-regular fa-arrow-left"></i>
                <span class="p-x-spacing-xx-s">Previous</span>
              </span>
              <span class="button-main-secondary-grey button-large content-label-large"
                    [ngClass]="{'invisible': !canNext}" (click)="goNextItem()">
                <i class="fa-regular fa-arrow-right"></i>
                <span class="p-x-spacing-xx-s">Next</span>
              </span>
            </div>

            <div class="p-b-spacing-xx-lg">&nbsp;</div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #loadingSpinner>
  <div class="text-center align-items-center">
    <img src="/assets/images/octimine_blue_spinner.gif" class="mt-2 loading-spinner">
  </div>
</ng-template>
