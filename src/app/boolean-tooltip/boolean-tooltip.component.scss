@import 'scss/figma2023/index';
@import "scss/layout2021/mixins";

$content-width: 51.25rem;

.boolean-tooltip-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .header-container {
    .header {
      height: $spacing-system-spacing-xxx-lg;
    }
  }

  .navigation {
    &.float-menu {
      .header-container {
        .figma-dropdown {
          .figma-dropdown-content {
            width: max-content;
            min-width: 13.5rem;
            height: 70vh;
            left: -0.55rem;
            top: ($spacing-system-spacing-xxx-lg - $spacing-system-spacing-md + 0.1rem);
            @include border-left-radius(0);

            .figma-dropdown-item {
              color: $colours-content-content-tertiary !important;

              &.group-item {
                color: $colours-content-content-quartary !important;
                cursor: default;
              }

              &.clickable-item {
                cursor: pointer;

                &:hover, &.selected-item {
                  color: $colours-content-content-primary !important;
                  background-color: $colours-background-bg-tertiary;
                  @include border-radius($radius-sm);
                }
              }
            }
          }
        }
      }
    }

    &.sticky-menu {
      width: max-content;
      min-width: 13.5rem;
      height: 100vh;
      overflow: auto;
      background-color: $colours-background-bg-secondary;
      border-right: 0.5px solid $colours-border-subtle;

      .header-container {
        height: 100%;

        .navigation-header {
          &:hover {
            color: $colours-content-content-tertiary !important;
            background-color: $colours-background-bg-tertiary;
            @include border-radius($radius-sm);

            .collapse-button {
              cursor: pointer;
              background-color: $colour-grey-300;
              color: $colours-content-content-secondary;
            }
          }
        }

        .navigation-item {
          color: $colours-content-content-tertiary !important;

          &.group-item {
            color: $colours-content-content-quartary !important;
            cursor: default;
          }

          &.clickable-item {
            cursor: pointer;

            &:hover, &.selected-item {
              color: $colours-content-content-primary !important;
              background-color: $colours-background-bg-tertiary;
              @include border-radius($radius-sm);
            }
          }
        }
      }
    }
  }

  .main-content {
    max-height: 100vh;

    .header-container {
      .header {
        padding: $spacing-system-spacing-md $spacing-system-spacing-x-big;
      }
    }

    #boolean-tooltip-content {
      padding: $spacing-system-spacing-xx-big $spacing-system-spacing-xxx-big;
    }

    .content-fix-width {
      width: $content-width;
    }
  }

  &.screen-size-xs, &.screen-size-sm, &.screen-size-md {
    .main-content {
      .header-container {
        .header {
          padding: $spacing-system-spacing-md $spacing-system-spacing-big !important;
        }
      }

      #boolean-tooltip-content {
        padding: $spacing-system-spacing-xx-big $spacing-system-spacing-big !important;
      }

      .content-fix-width {
        width: 100% !important;
      }
    }
  }
}

::ng-deep {
  .boolean-tooltip-container {
    .main-content {
      a {
        @include add-properties(map-get(map-get($typography, 'body'), 'medium'), true);
        color: $colours-content-content-enable !important;

        &:hover {
          color: $colours-content-content-hover !important;
          text-decoration-line: underline !important;
        }
      }

      .content-blocks {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-system-spacing-xxx-big;
        align-self: stretch;
      }

      .block-title {
        @include add-properties(map-get(map-get($typography, 'heading'), 'h5'), true);
        margin-bottom: $spacing-system-spacing-md;
      }

      .block-text {
        @include add-properties(map-get(map-get($typography, 'body'), 'medium'), true);
      }

      .operator-list {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-system-spacing-md;
        align-self: stretch;
        margin: 0 !important;
      }

      .operator-item {
        display: flex;
        padding: 0;
        align-items: center;
        gap: $spacing-system-spacing-xx-s;
        align-self: flex-start;
      }

      .code-tag {
        color: $colours-content-content-active;
        padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-x-s 0 $spacing-system-spacing-x-s;
        border-radius: $radius-sm;
        border: 1px solid $colours-border-brand;
        background: $colours-background-bg-brand;
        @include add-properties(map-get(map-get($typography, 'code'), '01'), true);
      }

      .example-list {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-system-spacing-xxx-big;
        align-self: stretch;
        margin: 0 !important;
      }

      .example-item {
        display: flex;
        padding: 0 $spacing-system-spacing-md;
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-system-spacing-md;
        align-self: stretch;
      }

      .example-subtitle {
        @include add-properties(map-get(map-get($typography, 'heading'), 'h6'), true);
      }

      .example-card {
        display: flex;
        padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
        align-items: center;
        gap: $spacing-system-spacing-md;
        align-self: stretch;
        border-radius: $radius-sm;
        background: $colours-background-bg-secondary;
      }

      .example-text {
        color: $colours-content-content-primary;
        flex: 1 1 auto;
        @include add-properties(map-get(map-get($typography, 'body'), 'medium'), true);
      }

      .search-icon {
        color: $colours-content-content-quartary;
      }

      .usage-list {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-system-spacing-md;
        align-self: stretch;
        margin: 0 !important;
      }
    }
  }
}


