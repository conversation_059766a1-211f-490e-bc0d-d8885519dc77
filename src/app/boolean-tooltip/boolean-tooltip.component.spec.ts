import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BooleanTooltipComponent } from './boolean-tooltip.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { provideMatomo } from 'ngx-matomo-client';

describe('BooleanTooltipComponent', () => {
  let component: BooleanTooltipComponent;
  let fixture: ComponentFixture<BooleanTooltipComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BooleanTooltipComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
        BrowserAnimationsModule
      ],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BooleanTooltipComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
