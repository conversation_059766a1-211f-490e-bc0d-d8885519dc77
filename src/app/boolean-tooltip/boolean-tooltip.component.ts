import { Component, HostListener, OnDestroy, OnInit } from '@angular/core';
import { CockpitService } from '@core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { finalize, tap } from 'rxjs/operators';

interface BooleanTooltipNavigationItem {
  name: string;
  is_group: boolean;
  cockpit_name?: string;
}

@Component({
  selector: 'app-boolean-tooltip',
  templateUrl: './boolean-tooltip.component.html',
  styleUrls: ['./boolean-tooltip.component.scss']
})
export class BooleanTooltipComponent implements OnInit, OnDestroy {
  navigationItems: BooleanTooltipNavigationItem[] = [];
  currentNavigationItem: BooleanTooltipNavigationItem = null;
  showDefaultMenuButton: boolean = true;
  showExpandMenuButton: boolean = false;
  showCollapseMenuButton: boolean = false;
  isFloatMenuOpened: boolean = false;
  isStickyMenuOpened: boolean = false;
  isLoadingMenuItems: boolean = false;
  isLoadingContent: boolean = false;

  private currentBooleanTooltipField: string = null;
  private resizeTimeoutId: number = null;
  private subscriptions = new Subscription();

  constructor(
    private cockpitService: CockpitService,
    private route: ActivatedRoute,
    private router: Router
  ) {
  }

  get cockpitName(): string {
    return `boolean_tooltip_${this.currentBooleanTooltipField}`;
  }

  get canNext(): boolean {
    const items = this.nonGroupNavigationItems;
    return items.indexOf(this.currentNavigationItem) < items.length - 1;
  }

  get canPrevious(): boolean {
    return this.nonGroupNavigationItems.indexOf(this.currentNavigationItem) > 0;
  }

  get nonGroupNavigationItems(): BooleanTooltipNavigationItem[] {
    return this.navigationItems.filter(item => !item.is_group);
  }

  get minWindowWidth(): number {
    return this.isStickyMenuOpened ? 570 : 400;
  }

  @HostListener('window:resize')
  onWindowResize() {
    this.resizeWindow();
  }

  ngOnInit(): void {
    const getMenuItems$ = this.cockpitService.getObject('boolean_tooltip_fields')
      .pipe(
        tap(() => this.isLoadingMenuItems = true),
        finalize(() => this.isLoadingMenuItems = false)
      )
      .subscribe(data => {
        this.navigationItems = data as BooleanTooltipNavigationItem[];
        if (this.currentBooleanTooltipField) {
          this.currentNavigationItem = this.navigationItems.find(item => item.cockpit_name === this.cockpitName);
        } else {
          this.currentNavigationItem = this.navigationItems.filter(item => !item.is_group)[0];
        }
      });
    this.subscriptions.add(getMenuItems$);

    const queryParams$ = this.route.queryParams.subscribe({
      next: (params) => {
        if (params.field) {
          this.currentBooleanTooltipField = params.field;
          this.currentNavigationItem = this.navigationItems.find(item => item.cockpit_name === this.cockpitName);
        }
      }
    });
    this.subscriptions.add(queryParams$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  navigateToItem(item: BooleanTooltipNavigationItem, event: MouseEvent = null) {
    if (item.is_group && event) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    const field = item.cockpit_name.replace('boolean_tooltip_', '');
    this.router.navigate(['boolean-tooltip'], {queryParams: {field: field}});
  }

  onDefaultMenuButtonToggled(isOpened: boolean) {
    if (isOpened) {
      this.showDefaultMenuButton = false;
      this.showExpandMenuButton = true;
      this.showCollapseMenuButton = false;
    } else {
      this.showDefaultMenuButton = true;
      this.showExpandMenuButton = false;
      this.showCollapseMenuButton = false;
    }
  }

  onExpandMenuButtonClicked() {
    this.showDefaultMenuButton = false;
    this.showExpandMenuButton = false;
    this.showCollapseMenuButton = true;
    this.isFloatMenuOpened = false;
    this.isStickyMenuOpened = true;

    this.resizeWindow(50);
  }

  onCollapseMenuButtonClicked() {
    this.showDefaultMenuButton = true;
    this.showExpandMenuButton = false;
    this.showCollapseMenuButton = false;
    this.isFloatMenuOpened = false;
    this.isStickyMenuOpened = false;
  }

  goNextItem() {
    const items = this.nonGroupNavigationItems;
    const currentIndex = items.indexOf(this.currentNavigationItem);
    if (currentIndex < items.length - 1) {
      this.navigateToItem(items[currentIndex + 1]);
    }
  }

  goPreviousItem() {
    const items = this.nonGroupNavigationItems;
    const currentIndex = items.indexOf(this.currentNavigationItem);
    if (currentIndex > 0) {
      this.navigateToItem(items[currentIndex - 1]);
    }
  }

  onLoadingContent(isLoading: boolean) {
    this.isLoadingContent = isLoading;
    if (!isLoading) {
      const element = document.getElementById('boolean-tooltip-content');
      if (element) {
        element.scrollIntoView({behavior: 'smooth', block: 'start'});
      }
    }
  }

  private resizeWindow(timeout = 150) {
    if (this.resizeTimeoutId) {
      window.clearTimeout(this.resizeTimeoutId);
      this.resizeTimeoutId = null;
    }

    if (window.outerWidth < this.minWindowWidth) {
      this.resizeTimeoutId = window.setTimeout(() => {
        window.resizeTo(this.minWindowWidth, window.outerHeight);
      }, timeout);
    }
  }
}
