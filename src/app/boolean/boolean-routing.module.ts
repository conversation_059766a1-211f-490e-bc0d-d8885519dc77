import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';

import { BooleanSearchComponent } from './boolean-search/boolean-search.component';

const routes: Routes = [
  {path: '', component: BooleanSearchComponent, canActivate: [AuthGuard]},
  {path: 'advancedMode', component: BooleanSearchComponent, canActivate: [AuthGuard]},

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BooleanRoutingModule {
}
