<div class="d-flex flex-column justify-content-start min-vh-100">

  <app-header></app-header>

  <app-search-menu searchTab="boolean"></app-search-menu>

  <div class="flex-fill">
    <div class="page-content container boolean-search-container" data-intercom-target="Boolean search">
      <app-alert type="warning" [message]="messageCollaboratorQuotas" [headline]="'Collaborator’s user rights'" version="figma" *ngIf="messageCollaboratorQuotas"></app-alert>

      <app-search-title headline="Search the patent database">
        <app-tooltip class="ms-2" id='patent_search'
                     tooltipTitle='Search the patent database' [tooltipText]='tooltipText'
                      tooltipIconSize="sm"></app-tooltip>
        <app-boolean-query-options class="ms-auto" [advancedMode]="showAdvancedMode"></app-boolean-query-options>
      </app-search-title>

      <div class="mt-3" *ngIf="validateMessage || messageAlert">
        <app-alert type="danger" [message]="validateMessage" *ngIf="validateMessage" (closed)="validateMessage = null"></app-alert>
        <app-alert type="danger" [message]="messageAlert" *ngIf="messageAlert" (closed)="messageAlert = null"></app-alert>
      </div>

      <form id="searchForm" (ngSubmit)="onSubmit()">

        <app-boolean-input *ngIf="!showAdvancedMode" [booleanSearchService]="searchService"
                           [storeService]="booleanSearchStoreService" [clearEmptyClause]="true"
                           (doSubmit)="onSubmit()" (changeQuery)="onSaveQuery()"></app-boolean-input>
        <app-boolean-advanced-mode *ngIf="showAdvancedMode" [booleanSearchService]="searchService" [storeService]="booleanSearchStoreService" 
                                   (doSubmit)="onSubmit()" (changeQuery)="onSaveQuery()"></app-boolean-advanced-mode>
        <div class="d-flex">
          <a href="javascript:void(0)" (click)="switchMode()" class="advance-mode-btn">
            <span *ngIf="!showAdvancedMode">Switch to advanced mode >>></span>
            <span *ngIf="showAdvancedMode">Switch to simple mode >>></span>
          </a>
        </div>

        <div class="m-0 d-flex justify-content-between align-items-center">
          <div class="color-4 d-flex align-items-end">
            <ng-container>
              <div class="me-2">Publication search</div>
              <label class="check-on-off color-4 align-middle" ngbTooltip="Enable/disable boolean search with publication">
                <input type="checkbox" [checked]="isPublications" class="left" (click)="onSearchType($event)"/>
              </label>
            </ng-container>
          </div>

          <div class="m-0 d-flex flex-column justify-content-end">
            <div *ngIf="showAdvancedMode" class="m-0 d-flex justify-content-end align-items-center search-hint-container">
              <button id="clear-boolean-input-button" (click)="onReset()" class="btn btn-primary-outline btn-sm btn-icon me-3" ngbTooltip="Reset search form" type="button"
                [disabled]="disableAdvancedSearchButton()">
                <i class="fa fa-times"></i>
              </button>

              <button id="advanced-search-button" type="submit" [disabled]="disableAdvancedSearchButton()" class="btn btn-secondary btn-xl">
                <i class="fa fa-search icon-left"></i>Search
              </button>
            </div>

            <div *ngIf="!showAdvancedMode" class="m-0 d-flex justify-content-end align-items-center search-hint-container">
              <button id="clear-boolean-input-button" (click)="onReset()" class="btn btn-primary-outline btn-sm btn-icon me-3" ngbTooltip="Reset search form" type="button"
                [disabled]="disableResetButton()">
                <i class="fa fa-times"></i>
              </button>

              <button id="search-button" type="submit" [disabled]="disableSearchButton()" class="btn btn-secondary btn-xl"
                data-intercom-target="search-button">
                <i class="fa fa-search icon-left"></i>Search
              </button>
            </div>

            <div class="mt-2 d-flex justify-content-end search-hint">
              You can also press&nbsp;<span class="fw-bold">Ctrl</span>&nbsp;+&nbsp;<span class="fw-bold">Enter</span>&nbsp;to begin the search.
            </div>
          </div>
        </div>
      </form>
    </div>

    <div *ngIf="!searchStarting() && !submitting else loader">
      <app-patent-list-layout [storeService]="booleanSearchStoreService"
                              [documents]="searchService.documents | async"
                              [isLoading]="searchStarting()"
                              [showDashboardActionBar]="true">
        <ng-container documentsControlBar [ngTemplateOutlet]="booleanControlBar"></ng-container>

        <ng-container documentsTable [ngTemplateOutlet]="booleanList"></ng-container>

        <ng-container documentsVisual>
          <app-charts-container id="charts" [isColumnLayout]="isCombinedMode" [chartDisclaimer]="chartDisclaimer"
                                [storeService]="booleanSearchStoreService" [@fadeIn]>
          </app-charts-container>
        </ng-container>

        <ng-container alertMessages *ngIf="!(searchService.documents | async).length && hasFilter">
          <div appNoResults content="It seems we can’t find any result based on your selection.<br>Try to clear or change your filters."></div>
        </ng-container>        
      </app-patent-list-layout>

      <app-filters-bar class="pe-2 flex-fill" [alwaysBeSticky]="true" [storeService]="booleanSearchStoreService"  (clearAll)="clearAllFilters()"></app-filters-bar>
    </div>

  </div>

  <app-footer></app-footer>
</div>

<ng-template #booleanControlBar>
  <div class="bsr-control-bar sticky-top" [@fadeInOut]>
    <div class="container-fluid d-flex justify-content-between align-items-center" data-intercom-target="control-bar">
      <app-patent-control-bar [columnsToShow]="columnsToShow"
                              [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                              [searchService]="searchService"
                              [hasSortBySimilarity]="true" (sortBySimilarity)="onSortBySimilarity($event)"
                              [hasHarmonizeControl]="true"
                              [hasAddToSearchControl]="false"
                              [hasTemporaryLinkControl]="true"
                              [hasOctiAIControl]="true"
                              [saveSearchTextInput]="booleanQuery()"
                              [hasExportControl]="!isFreeUser"
                              [hasSaveToCollectionControl]="true"
                              [taskShowCreationButton]="true"
                              (taskSaved)="onTaskSaved($event)"
                              [storeService]="booleanSearchStoreService"
                              [hasMonitor]="isMonitorOwner"
                              [patentListScope]="isPublications ? patentListScopeEnum.PUBLICATION : patentListScopeEnum.FAMILY"
                              (monitor)="onSetupAlert()">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #booleanFooter>
  <div class="py-4">
    <div class="bsr-pagination-controls d-flex justify-content-end align-items-center" [hidden]="searching">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" class="me-3"></app-page-size>
      <pagination-controls id="search-pagination" *ngIf="!isFreeUser"
                          (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true"
                          [ngClass]="searching ? 'invisible' : 'visible'">
      </pagination-controls>
    </div>
  </div>
</ng-template>

<ng-template #booleanList>
  <div [hidden]="!(searchService.documents | async).length" [@fadeInOut]
       [ngClass]="{'container-fluid': !isCombinedMode, 'pe-0': isCombinedMode}">
    <div class="d-flex justify-content-between align-items-center">
      <div class="bsr-results-title new-layout-headline" data-intercom-target="Result title">

        <span *ngIf="pagination.origin_total_hits <= MAX_RESULTS">Found </span>
        <span *ngIf="pagination.origin_total_hits > MAX_RESULTS">Showing </span>

        <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{pagination?.total_hits}}
        <span *ngIf="!isPublications">
          {{ 'patent family' | pluralize: pagination?.total_hits }}
          ({{pagination?.total_publications}} {{ 'publication' | pluralize: pagination?.total_publications }})
        </span>
        <span *ngIf="isPublications">
          {{ 'patent publication' | pluralize: pagination?.total_publications }}
        </span>
        <span *ngIf="pagination.origin_total_hits > MAX_RESULTS">
          out of {{ pagination.origin_total_hits }} total matches
        </span>

      </div>
      <div class="tools-bar expand-all">
        <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()" data-intercom-target="expand-all">
          {{ patentTable.openedPatent.length === searchService.getDocuments().length ? 'Collapse all': 'Expand all'}}
          <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < searchService.getDocuments().length"></i>
          <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === searchService.getDocuments().length"></i>
        </a>
      </div>
    </div>

    <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()" [message]="collectionsStoreService.getSaveToCollectionSuccess()"></app-alert>
    <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages"></app-alert>

    <div class="bsr-patent-table">
      <app-patent-table #patentTable [patents]="searchService.documents | async" [pagination]="pagination"
                        (sort)="onSort($event)" [hasLinksToBooleanSearch]="true" pathUrl="/patent"
                        [linkData]="linkData" backButtonTitle="Back to search" [storeService]="booleanSearchStoreService"
                        [showHighlight]="true"
                        [showHighlightSearchHash]="originalBooleanSearchHash"
                        [showSmartHighlight]="!!sortBySimilaritySearchHash"
                        [showSmartHighlightSearchHash]="sortBySimilaritySearchHash">
      </app-patent-table>
    </div>

    <ng-container [ngTemplateOutlet]="booleanFooter"></ng-container>
  </div>
</ng-template>

<ng-template #loader>
  <app-spinner class="py-5 d-block"></app-spinner>
</ng-template>
