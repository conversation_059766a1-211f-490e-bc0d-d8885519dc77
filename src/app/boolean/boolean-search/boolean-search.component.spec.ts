import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { BooleanSearchComponent } from './boolean-search.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import {
  ApplicantsComponent,
  TechnologyTimeLineComponent,
  TopCpcCodeComponent,
  TopIpcCodeComponent
} from '@shared/charts';
import { provideMatomo } from 'ngx-matomo-client';

describe('BooleanSearchComponent', () => {
  let component: BooleanSearchComponent;
  let fixture: ComponentFixture<BooleanSearchComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        BooleanSearchComponent,
        ApplicantsComponent,
        TechnologyTimeLineComponent,
        TopIpcCodeComponent,
        TopCpcCodeComponent
      ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
        BrowserAnimationsModule,
        NgbModule,
        HighchartsChartModule,
        NgxSliderModule,
      ],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BooleanSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
