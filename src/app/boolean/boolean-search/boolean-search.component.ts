import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Emitter, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { TaskModel, UserProfile } from '@core/models';
import { finalize, skip, take } from 'rxjs/operators';
import {
  AdvancedFilterService,
  ApplicantsAliasesService,
  BooleanSearchRequest,
  BooleanSearchService,
  BooleanTemplateService,
  Clause,
  ClauseConjunctionEnum,
  ClauseOperatorEnum,
  ConfirmationDialogService,
  Field,
  MatomoService,
  PaginationMetadata,
  PatentTableService,
  SearchHistoryService,
  SearchQueryParams,
  SemanticSearchRequest,
  SemanticSearchService,
  TaskService,
  UserService,
  ChartsService,
  PatentListScopeEnum, BOOLEAN_DEFAULT_CLAUSES, BooleanFieldEnum,
  ToastService,
} from '@core/services';
import { ActivatedRout<PERSON>, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { MonitorDialogComponent, PatentTableComponent } from '@shared/components';
import { BooleanSearchStoreService, CollectionStoreService, SortParams } from '@core/store';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { columnsToShow, publicationColumnsToShow, selectedColumnsCombinedMode } from './columns';
import { ViewModeTypeEnum } from '@search/patent/types';
import { ActionType } from '@shared/charts/chart-dashboard/types';

@Component({
  selector: 'app-boolean-search',
  templateUrl: './boolean-search.component.html',
  styleUrls: ['./boolean-search.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      state('true', style({opacity: 0})),
      transition('true => *', animate(500)),
    ]),
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ])
  ]
})
export class BooleanSearchComponent implements OnInit, DoCheck, OnDestroy {

  payload: BooleanSearchRequest;
  sorting: SortParams = {field: null, order: null} as SortParams;
  searching = false;

  @ViewChild('patentTable') patentTable: PatentTableComponent;
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;
  showFilters = false;
  saveLink = false;
  showAdvancedMode = false;
  savedTaskMessages: string[];
  submitting = false;
  user: UserProfile;
  patentListScopeEnum = PatentListScopeEnum;

  private combinedModePosition: number;
  private MAX_RESULTS = 10000;
  private CACHE_SAVED_QUERY = 'boolean_search_query';
  private clausesEventEmitter = new EventEmitter<Array<Clause>>(true);
  private validateMessageEventEmitter = new EventEmitter<string>(true);
  private messageAlertEventEmitter = new EventEmitter<string>(true);
  private showFiltersEventEmitter = new EventEmitter<boolean>(true);
  private subscriptions = new Subscription();
  private debounce: any;
  private canSaveSearchHistory: boolean = true;

  constructor(
    private elRef: ElementRef,
    public booleanSearchStoreService: BooleanSearchStoreService,
    public searchService: BooleanSearchService,
    private modalService: NgbModal,
    private chartService: ChartsService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    public collectionsStoreService: CollectionStoreService,
    private userService: UserService,
    private toastService: ToastService,
    private confirmationDialogService: ConfirmationDialogService,
    private semanticService: SemanticSearchService,
    private advancedFilterService: AdvancedFilterService,
    private booleanTemplateService: BooleanTemplateService,
    private taskService: TaskService,
    private patentTableService: PatentTableService,
    private matomoService: MatomoService,
    private applicantsAliasesService: ApplicantsAliasesService,
    private searchHistoryService: SearchHistoryService
  ) {
  }

  get columnsToShow(){
    return this.booleanSearchStoreService.isPublications ? publicationColumnsToShow : columnsToShow;
  }

  get tooltipText(): string {
    return this.searchService.getTooltipSearchInput(this.showAdvancedMode);
  }

  get messageAlert(): string | Array<string> {
    return this.searchService.messageAlert;
  }

  set messageAlert(value: string | Array<string>) {
    this.searchService.messageAlert = value;
  }

  get validateMessage(): string | Array<string> {
    return this.searchService.validateMessage;
  }

  set validateMessage(value: string | Array<string>) {
    this.searchService.validateMessage = value;
  }

  get filteredClauses(): Clause[] {
    return this.searchService.filterClauses;
  }

  get linkData() {
    return this.searchService.linkData;
  }

  get sortBySimilarity() {
    return this.booleanSearchStoreService.sortBySimilarity;
  }

  get isPublications(): boolean {
    return this.booleanSearchStoreService.isPublications;
  }

  get totalSelectedPatents(): number {
    return this.booleanSearchStoreService.selectedPatentIds.length;
  }

  get chartDisclaimer(): string {
    if (this.pagination?.origin_total_hits > this.MAX_RESULTS) {
      return `To prevent performance problems caused by too many documents, the Visual Analysis shows only the first ${this.pagination?.total_hits?.toString()} results.`;
    }
    return null;
  }

  get isFreeUser(): boolean {
    return this.userService.isFreeUser();
  }

  get isAdmin(): boolean{
    return this.userService.isAdmin();
  }

  /**
   * check if user have right to create monitor profile
   */
  get isMonitorOwner(): boolean {
    return this.userService.hasMonitor && !this.userService.isFreeUser();
  }

  get viewMode(): ViewModeTypeEnum {
    return this.booleanSearchStoreService.patentListViewMode
  }

  get isListVisible(): boolean {
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get isChartVisible(): boolean {
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get isCombinedMode(): boolean {
    return this.booleanSearchStoreService.isCombinedMode;
  }

  get originalBooleanSearchHash(): string {
    return this.booleanSearchStoreService.originalBooleanSearchHash;
  }

  get sortBySimilaritySearchHash(): string {
    return this.booleanSearchStoreService.sortBySimilaritySearchHash;
  }

  get messageCollaboratorQuotas(): string {
    return this.userService.messageCollaboratorQuotas;
  }

  get isCollaboratorQuotasExceeded(): boolean {
    return this.userService.isCollaboratorQuotasExceeded;
  }

  get hasFilter(): boolean {
    return this.booleanSearchStoreService.getAppliedFiltersQuery()?.length > 0;
  }

  get pageSize(): number {
    return this.booleanSearchStoreService.pageSize;
  }

  set pageSize(value: number) {
    this.booleanSearchStoreService.pageSize = value;
  }

  get pagination(): PaginationMetadata {
    return this.booleanSearchStoreService.pagination || {} as PaginationMetadata;
  }

  set pagination(value: PaginationMetadata) {
    this.booleanSearchStoreService.pagination = value;
  }

  ngOnInit() {
    this.getUserStatistics();
    const allApplicantAliases$ = this.applicantsAliasesService.getAllApplicantAliases().subscribe();
    this.subscriptions.add(allApplicantAliases$);

    this.setBooleanInputMode();
    this.clearAdvancedFilter();
    this.user = this.userService.getUser().profile;
    this.subscribeServices();
    if (this.isFreeUser) {
      this.MAX_RESULTS = this.userService.getSubscriptionMaxLimit();
    }
    this.initializeParams();
  }

  ngOnDestroy() {
    this.unsubscribeServices();
    this.patentTableService.selectedLegalStatus = [];
    this.toastService.clear();
  }

  ngDoCheck() {
    if (!this.showAdvancedMode && (this.searchService.isInitialClauses() || this.areClausesValid())) {
      this.validateMessage = null;
    }
  }

  setBooleanInputMode(): void {
    const url = this.router.url;
    this.showAdvancedMode = url.indexOf('advancedMode') > -1;
  }

  refreshSearch(): void {
    this.booleanSearchStoreService.setSortBySimilarity(undefined);
    this.searchService.resetDocuments();

    this.pagination = {} as PaginationMetadata;
    this.sorting = {field: null, order: null} as SortParams;
    this.booleanSearchStoreService.searchHash = null;
    this.messageAlertEventEmitter.emit(null);
    this.validateMessageEventEmitter.emit(null);
    this.booleanSearchStoreService.searchingEvent.emit(false);
    this.booleanSearchStoreService.patentTableSort = {field: null, order: null} as SortParams;

    this.booleanSearchStoreService.originalBooleanSearchHash = null;
    this.booleanSearchStoreService.sortBySimilaritySearchHash = null;
    this.canSaveSearchHistory = true;
  }

  onReset(): void {
    this.booleanTemplateService.resetTemplate();
    this.searchService.search_input = '';
    this.searchService.reset();
    this.refreshSearch();
    this.resetStoreService();
    this.resetErrorOfClauses();
    this.removeSavedQuery();
    this.setDefaultClause();
  }

  onSubmit(): void {
    if (this.isCollaboratorQuotasExceeded) {
      return;
    }
    this.submitting = true;
    this.booleanSearchStoreService.shouldCalculateCharts = true;
    this.refreshSearch();
    this.resetStoreService(true);
    this.resetErrorOfClauses();
    this.booleanSearchStoreService.setSortBySimilarity(undefined);
    this.patentTableService.selectedLegalStatus = [];
    this.search();
    this.matomoService.booleanSearchButton();
  }

  onPageChange(page): void {
    const {current_page} = this.pagination;
    if (current_page === page) {
      return;
    }

    this.pagination.current_page = page;

    this.booleanSearchStoreService.shouldCalculateCharts = false;
    this.canSaveSearchHistory = false;
    this.search();
  }

  onSort(val: SortParams): void {
    this.sorting = val;
    this.booleanSearchStoreService.shouldCalculateCharts = false;
    this.canSaveSearchHistory = false;
    this.search();
  }

  searchStarting(): boolean {
    return !this.booleanSearchStoreService.searchHash && this.searching;
  }

  disableAdvancedSearchButton() {
    return !this.searchService.search_input || this.searchService.disableAdvancedModeSearch || this.searchStarting() || this.isCollaboratorQuotasExceeded;
  }

  disableResetButton() {
    return this.searchService.isInputEmpty() || this.searchStarting();
  }

  disableSearchButton() {
    return this.searchStarting() ||
      (!this.showAdvancedMode && (this.searchService.areClausesEmpty() ||
       !this.searchService.areClausesValid(this.booleanSearchStoreService.isPublications)) || this.isCollaboratorQuotasExceeded);
  }

  onChangePageSize(event: number): void {
    this.booleanSearchStoreService.shouldCalculateCharts = false;
    this.pageSize = event;
    this.pagination.current_page = 1;
    this.canSaveSearchHistory = false;
    this.search();
  }

  switchMode(): void {
    if (!this.showAdvancedMode) {
      this.removeSavedQuery();
      this.searchService.search_input = this.searchService.buildBooleanQuery(false);
      this.onSaveQuery();
      this.router.navigate(['boolean/advancedMode']);
    } else {
      if (this.searchService.search_input) {
        const title = 'Advanced mode';
        const message = `Warning: The query you entered will be lost. Do you really want to return to simple mode?`;
        const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
        modalRef.then(val => {
          if (val) {
            this.searchService.search_input = '';
            this.booleanSearchStoreService.searchInput = '';
            this.removeSavedQuery();
            this.refreshSearch();
            this.showAdvancedMode = false;
            this.router.navigate(['boolean']);
          }
        });
        return;
      }
      this.router.navigate(['boolean']);
    }
    this.booleanTemplateService.resetTemplate();
  }

  booleanQuery(): string {
    if (this.showAdvancedMode) {
      return this.searchService.search_input;
    }
    return this.searchService.buildBooleanQuery();
  }

  onSetupAlert() {
    const query = this.showAdvancedMode ? this.buildPayloadAdvancedMode() : this.buildPayload();
    const modal = this.modalService.open(MonitorDialogComponent);
    modal.componentInstance.boolean_query = query;
    modal.componentInstance.isPublications = this.booleanSearchStoreService.isPublications;
    modal.result.then(message => {
      // successfully performed the event and closed the model
    }, reason => {
      // model closed
    });
  }

  onSaveQuery(): void {
    if (!this.searchService.areClausesValid(this.booleanSearchStoreService.isPublications)) {
      return;
    }
    if (this.debounce) {
      clearTimeout(this.debounce);
    }

    this.debounce = setTimeout(() => {
      this.filteredClauses.length = 0;
      const booleanQuerySearch = window.localStorage.getItem(this.CACHE_SAVED_QUERY);
      const query = this.showAdvancedMode ? this.buildPayloadAdvancedMode() : this.buildPayload();

      if (query === booleanQuerySearch) {
        return;
      }

      window.localStorage.setItem(this.CACHE_SAVED_QUERY, JSON.stringify(query));
    }, 1000);
  }

  onSortBySimilarity(event) {
    if (event) {
      this.sorting = this.booleanSearchStoreService.patentTableSort;
    } else {
      this.booleanSearchStoreService.patentTableSort = {} as SortParams;
      this.sorting = {} as SortParams;
    }
    this.submitting = true;
    this.booleanSearchStoreService.setSortBySimilarity(event);
    this.booleanSearchStoreService.shouldCalculateCharts = true;
    this.pagination.current_page = 1;
    this.search();
  }

  onTaskSaved(data: { message: string; payload: TaskModel; savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessages = this.taskService.getTaskCreationSuccessMessage(data, 'search_results');
    }
  }

  private setDocuments(data) {
    data.page.origin_total_hits = data.page.total_hits;
    data.page.total_hits = Math.min(data.page.total_hits, this.MAX_RESULTS);

    this.searchService.setDocuments(data.documents);
    this.booleanSearchStoreService.pagination = data.page;
    this.pagination = data.page;
    if(this.booleanSearchStoreService.shouldCalculateCharts){
      this.booleanSearchStoreService.searchHash = data.search_info.search_hash;
      this.booleanSearchStoreService.chartPayload = this.payload;
      this.booleanSearchStoreService.shouldCalculateCharts = false;
    }
  }

  private buildPayloadAdvancedMode(): BooleanSearchRequest {
    return this.searchService.buildPayloadAdvancedMode(this.booleanSearchStoreService.filters);
  }

  private getSimilarityResults() {
    this.booleanSearchStoreService.searchingEvent.emit(true);

    const payload = this.booleanSearchStoreService.getSortBySimilarityPayload(this.booleanSearchStoreService.getAppliedFiltersQuery()) as SemanticSearchRequest;
    const query = this.buildQueryParams();
    // Stop saving the search history for similarity results
    query.save_history = 0;
    this.booleanSearchStoreService.sortBySimilaritySearchHash = null;

    const search$ = this.semanticService.search(payload, query, false)
      .pipe(
        take(1),
        finalize(() => {
          this.submitting = false;
          this.booleanSearchStoreService.searchingEvent.emit(false);
        })
      )
      .subscribe({
        next: ({data}) => {
          if (!data.documents || data.documents.length === 0) {
            this.refreshSearch();
            this.messageAlertEventEmitter.emit('<p>Your sort by similarity search returned no results</p>');
          } else {
            this.booleanSearchStoreService.sortBySimilaritySearchHash = data.search_info.search_hash;
            this.setDocuments(data);
          }
        }
      });
    this.subscriptions.add(search$);
  }

  private unsubscribeServices() {
    this.subscriptions.unsubscribe();
  }

  private search(scrollPage = true): void {
    this.messageAlertEventEmitter.emit(null);

    if (this.sortBySimilarity) {
      this.getSimilarityResults();
      return;
    }

    if (this.showAdvancedMode) {
      if (!this.searchService.search_input) {
        this.submitting = false;
        return;
      }
      this.booleanSearchStoreService.searchingEvent.emit(true);
      this.payload = this.buildPayloadAdvancedMode();
    } else {
      if(!this.searchService.areClausesEmpty()){
        this.searchService.cleanBooleanClauses();
      }
      if (!this.areClausesValid()) {
        this.submitting = false;
        this.validateMessageEventEmitter.emit(`Please correct the errors below.`);
        return;
      }

      if (this.searching) {
        return;
      }

      this.validateMessageEventEmitter.emit(null);
      this.messageAlertEventEmitter.emit(null);
      this.booleanSearchStoreService.searchingEvent.emit(true);

      this.payload = this.buildPayload();
      this.searchService.closeMismatchedBrackets();

    }

    if (this.isPublications) {
      this.payload.search_document_type = PatentListScopeEnum.PUBLICATION;
    }
    const queryParams = this.buildQueryParams();
    if (this.booleanSearchStoreService.shouldCalculateCharts) {
      this.booleanSearchStoreService.isCalculatingCharts = true;
    }

    this.storeScrollCombinedModePosition();

    this.booleanSearchStoreService.originalBooleanSearchHash = null;
    this.booleanSearchStoreService.sortBySimilaritySearchHash = null;
    this.canSaveSearchHistory = true;

    const search$ = this.searchService.search(this.payload, queryParams)
      .pipe(
        take(1),
        finalize(() => {
          this.submitting = false;
          this.booleanSearchStoreService.searchingEvent.emit(false);
        })
      ).subscribe({
      next: ({data}) => {
        if (!this.searchService.getDocuments() || this.searchService.getDocuments().length === 0) {
          this.refreshSearch();
          if (!this.hasFilter) {
            this.messageAlertEventEmitter.emit('<p>Your search returned no results</p>');
          }
          return;
        }
        this.getUserStatistics();

        this.booleanSearchStoreService.originalBooleanSearchHash = data.search_info.search_hash;

        data.page.origin_total_hits = data.page.total_hits;
        data.page.total_hits = Math.min(data.page.total_hits, this.MAX_RESULTS);

        this.pagination = data.page;
        const values = {
          queryParams,
          payload: this.payload,
          searchHash: data.search_info.search_hash,
          totalHits: this.pagination.total_hits,
          pagination: this.pagination,
          clauses: this.searchService.clauses
        };

        this.removeSavedQuery();

        this.storeValues(values);

        if (scrollPage) {
          this.scrollToResults();
        }
      },
      error: (err) => {
        console.error(err);

        let errorMessage = `<p>Your search query is not formatted properly. Please review it and try again.</p>`;

        if (err.status === 0) {
          errorMessage = `<p>The server cannot be reached. Please check your internet connection.</p>`;
        }

        if (err.status >= 500) {
          errorMessage = '<p>Your search could not be processed, please try again later</p>';
        }

        this.messageAlertEventEmitter.emit(this.searchService.parseErrorMessage(err.error, errorMessage, true));
      }
    });
    this.subscriptions.add(search$);
  }

  private resetErrorOfClauses() {
    this.searchService.resetErrorOfClauses();
  }

  private subscribeServices() {
    const searchingEvent$ = this.booleanSearchStoreService.searchingEvent.subscribe({
      next: val => {
        this.searching = val;
      }
    });
    this.subscriptions.add(searchingEvent$);

    const clausesEventEmitter$ = this.clausesEventEmitter.subscribe({
      next: data => {
        this.searchService.setClauses(data);

        setTimeout(() => {
          this.elRef.nativeElement.querySelectorAll('textarea').forEach(function (el) {
            el.dispatchEvent(new Event('input'));
          });
          this.search();
        });
      }
    });
    this.subscriptions.add(clausesEventEmitter$);

    const validateMessageEventEmitter$ = this.validateMessageEventEmitter.subscribe({
      next: data => {
        if (!data) {
          this.validateMessage = null;
          return;
        }
        this.validateMessage = data.toString();
      }
    });
    this.subscriptions.add(validateMessageEventEmitter$);

    const messageAlertEventEmitter$ = this.messageAlertEventEmitter.subscribe({
      next: data => {
        this.messageAlert = data?.toString();
      }
    });
    this.subscriptions.add(messageAlertEventEmitter$);

    const showFiltersEventEmitter$ = this.showFiltersEventEmitter.subscribe({
      next: data => {
        this.showFilters = data;
      }
    });
    this.subscriptions.add(showFiltersEventEmitter$);

    const filters$ = this.booleanSearchStoreService.filters$.pipe(skip(1)).subscribe({
      next: filters => {
        if (!this.submitting) {
          this.submitting = true;
          this.showFiltersEventEmitter.emit(filters.length > 0);
          this.booleanSearchStoreService.shouldCalculateCharts = true;
          this.booleanSearchStoreService.isCalculatingCharts = true;
          this.filtersToClauses(filters);
        }
      }
    });
    this.subscriptions.add(filters$);

    const isCalculatingCharts$ = this.booleanSearchStoreService.isCalculatingCharts$.subscribe({
      next: value => {
        if (!value) {
          this.scrollCombinedModePosition();
        }
      }
    });
    this.subscriptions.add(isCalculatingCharts$);

    const changeApplicantsEvent$ = this.applicantsAliasesService.changeApplicantsEvent.subscribe({
      next: (res) => {
        this.booleanSearchStoreService.shouldCalculateCharts = true;
        this.canSaveSearchHistory = false;
        this.search();
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);

    const searchHash$ = this.booleanSearchStoreService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);

    const chartDashboardAction$ = this.booleanSearchStoreService.chartDashboardAction$.subscribe({
      next: msg => {
        this.booleanSearchStoreService.customChartCategories = this.userService.getChartCategories(this.booleanSearchStoreService.chartDashboardType);

        if (msg.action === ActionType.Add) {
          this.booleanSearchStoreService.searchHash = this.booleanSearchStoreService.searchHash;
        }
      }
    });
    this.subscriptions.add(chartDashboardAction$);
  }

  private areClausesValid() {
    return this.searchService.areClausesValid(this.booleanSearchStoreService.isPublications);
  }

  private initializeParams(): void {
    if (this.searchBySemanticSearch()) {
      return;
    }
    if (this.canLoadSavedSearch()) {
      if(!this.booleanSearchStoreService.savedSearch){
        const searchHistoryService$ = this.searchHistoryService.get(this.route.snapshot.queryParams['search']).subscribe({
            next: (data) => {
              this.booleanSearchStoreService.savedSearch = data;
              this.searchBySavedSearch();
            }
          });
        this.subscriptions.add(searchHistoryService$);
      } else {
        this.searchBySavedSearch();
      }
      return;
    }
    if (this.searchByExternalLink()) {
      return;
    }
    if (this.booleanSearchStoreService.backPatentSearch) {
      this.booleanSearchStoreService.backPatentSearch = false;
      this.setFieldsAfterBack();
      this.booleanSearchStoreService.scrollToPatentFromPatentViewer();
      return;
    }
    if (this.recoveryQuery()) {
      return;
    }

    this.refreshSearch();
    this.resetStoreService();
    this.setDefaultClause();
  }

  private setFieldsAfterBack() {
    this.searchService.setClauses(this.booleanSearchStoreService.booleanSearchClauses);
    this.searchService.search_input = this.booleanSearchStoreService.searchInput;
    this.showAdvancedMode = this.booleanSearchStoreService.searchInput?.length > 0;
    if (this.booleanSearchStoreService.patentTableSort) {
      this.sorting = {...this.booleanSearchStoreService.patentTableSort};
    }
  }

  private storeValues(values) {
    this.booleanSearchStoreService.state = {...this.booleanSearchStoreService.state, total_hits: values.totalHits};
    if (this.booleanSearchStoreService.shouldCalculateCharts) {
      this.booleanSearchStoreService.searchHash = values.searchHash;
      this.booleanSearchStoreService.shouldCalculateCharts = false;
    }
    this.booleanSearchStoreService.pagination = values.pagination;
    this.booleanSearchStoreService.search = {payload: values.payload, params: values.queryParams};
    this.booleanSearchStoreService.booleanSearchClauses = this.searchService.clauses;
    this.booleanSearchStoreService.searchInput = this.searchService.search_input;
  }

  private resetStoreService(updateFilter = false) {
    this.booleanSearchStoreService.state = {...this.booleanSearchStoreService.state, total_hits: 0};
    this.booleanSearchStoreService.searchHash = '';
    this.booleanSearchStoreService.pagination = {} as PaginationMetadata;
    this.booleanSearchStoreService.searchingEvent.next(false);
    this.booleanSearchStoreService.setFilters([], updateFilter);
    this.booleanSearchStoreService.selectedPublications = [];
    this.booleanSearchStoreService.searchInput = '';
    this.booleanSearchStoreService.setSortBySimilarity(undefined);
    this.booleanSearchStoreService.patentListViewMode = ViewModeTypeEnum.COMBINED;
  }

  private setDefaultClause(){
    if(this.showAdvancedMode || !this.searchService.areClausesEmpty()){
      return;
    }
    this.searchService.setDefaultClause(BOOLEAN_DEFAULT_CLAUSES);
  }

  private buildPayload(): BooleanSearchRequest {
    return this.searchService.buildPayload(
      this.booleanSearchStoreService.isPublications,
      this.booleanSearchStoreService.filters,
      this.booleanSearchStoreService.selectedOwnersOfClauses
    );
  }

  private buildQueryParams(): SearchQueryParams {
    if (!this.sorting) {
      this.sorting = {field: null, order: null} as SortParams;
    }

    const sorting = {
      ...this.sorting.field && {
        sort_by: this.sorting.field,
        ...this.sorting.order && {
          sort_order: this.sorting.order
        }
      }
    };

    return {
      page: this.pagination.current_page || 1,
      page_size: this.pageSize,
      show_analytics: 1,
      show_general: 1,
      show_bibliographic: 1,
      show_fulltext: 0,
      show_tags: 1,
      save_history: this.canSaveSearchHistory ? 1 : 0,
      prefetch_images: 1,
      ...sorting
    };
  }

  private scrollToResults(elementId = '') {
    if (!elementId) {
      elementId = 'boolean-search-results-container';
    }
    setTimeout(() => {
      const ele = document.getElementById(elementId);
      if (ele) {
        ele.scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
      }
    }, 500);
  }

  private searchBySemanticSearch(): boolean {
    const param = this.route.snapshot.queryParams['search'];
    if (param === 'semantic') {
      this.location.replaceState('/boolean');
      if (this.booleanSearchStoreService.searchInput) {
        this.searchService.search_input = this.booleanSearchStoreService.searchInput;
        this.showAdvancedMode = true;
        return true;
      }
    }

    return false;
  }

  private canLoadSavedSearch(): boolean {
    return this.route.snapshot.queryParams['search'] && this.route.snapshot.queryParams['search'] > 0;
  }

  private searchBySavedSearch() {
    const param = this.route.snapshot.queryParams['search'];
    if (param && this.booleanSearchStoreService.savedSearch) {
      this.canSaveSearchHistory = false;
      this.booleanSearchStoreService.setSortBySimilarity(undefined);
      const searchDocumentType = this.booleanSearchStoreService.savedSearch.search_document_type as PatentListScopeEnum;
      this.booleanSearchStoreService.isPublications = searchDocumentType === PatentListScopeEnum.PUBLICATION;

      const searchFields = this.booleanSearchStoreService.savedSearch.search_fields;
      if (searchFields) {
        const clausesTmp = BooleanSearchService.makeClausesFromSearchFields(searchFields);
        if (clausesTmp.length > 0) {
          this.clausesEventEmitter.emit(clausesTmp);
          this.searchHistoryService.restoreSavedValues(this.booleanSearchStoreService.savedSearch);
          this.searchService.setClauses(clausesTmp);
          this.searchService.recoveredClausesFromHistory = [clausesTmp, this.booleanSearchStoreService.savedSearch];
          this.booleanSearchStoreService.savedSearch = null;
          this.location.replaceState('/boolean');
          return true;
        }
      }

      if (this.booleanSearchStoreService.savedSearch.search_input) {
        this.searchService.search_input = this.booleanSearchStoreService.savedSearch.search_input;
        this.showAdvancedMode = true;
        this.searchHistoryService.restoreSavedValues(this.booleanSearchStoreService.savedSearch);
        this.booleanSearchStoreService.savedSearch = null;
        this.location.replaceState('/boolean/advancedMode');
        return true;
      }
    }

    return false;
  }

  private searchByExternalLink() {
    const fieldText = this.route.snapshot.queryParams['field'];
    const field = Field.getFieldByName(fieldText as BooleanFieldEnum);
    const value = this.route.snapshot.queryParams['value'];

    if (field && value) {
      const clausesTmp = this.makeClausesFromValues(field, value);

      if (clausesTmp.length > 0) {
        if (field.name === BooleanFieldEnum.OWNER_IDS) {
          const labels = this.route.snapshot.queryParams['label'];
          const owners = value.split(',').map((value, index) => {
            return {id: value.trim(), name: labels.split(',')[index].trim()};
          });
          window.localStorage.setItem('selected_owners_boolean_search', JSON.stringify(owners));
          this.searchService.setClauses(clausesTmp);
        }
        this.clausesEventEmitter.emit(clausesTmp);

        this.location.replaceState('/boolean');

        return true;
      }
    }

    if (field || value) {
      this.location.replaceState('/boolean');
    }

    return false;
  }

  private makeClausesFromValues(field: Field, value: string): Array<Clause> {
    const valueParts = value.split(/,/);
    const clausesTmp = [];

    for (const valuePart of valueParts) {
      const clause = new Clause();

      clause.setField(field);
      clause.setValueByText(valuePart.trim());
      clause.conjunction = ClauseConjunctionEnum.AND;
      clause.cleanValue();

      clausesTmp.push(clause);
    }

    return clausesTmp;
  }

  private findClause(clauses: Array<Clause>, operator: ClauseOperatorEnum, conjunction: ClauseConjunctionEnum,
                     value: string, field: Field): Clause {
    return clauses.find(o => {
      return o.operator === operator && o.conjunction === conjunction &&
        o.value === value && o.field && o.field.name === field.name;
    });
  }

  private clausesByTopApplicantsFilter(originClauses, filter): Array<Clause> {
    const newClauses = new Array<Clause>();

    const applicantsField = Field.getFieldByName(BooleanFieldEnum.APPLICANTS);
    let filterValue = [filter.value];
    if (this.applicantsAliasesService.allApplicantAliases?.length > 0 && this.booleanSearchStoreService.applicantsByHash?.length > 0) {
      const alias = this.applicantsAliasesService.allApplicantAliases.filter(o => o.alias === filter.value && this.booleanSearchStoreService.applicantsByHash.indexOf(o.applicant) > -1);
      if (alias.length > 0) {
        filterValue = alias.map(o => o.applicant);
      }
    }

    const applicants = filter.othersNames && filter.othersNames.length > 0 ? [...new Set(filter.othersNames)] : filterValue;

    for (const applicant of applicants) {
      const first = applicants[0] === applicant;
      const last = applicants[applicants.length - 1] === applicant;
      const existedClause = this.findClause(
        originClauses, ClauseOperatorEnum.EQUAL, first ? ClauseConjunctionEnum.AND : ClauseConjunctionEnum.OR,
        applicant.toString(), applicantsField
      );

      if (!existedClause) {
        const clause = new Clause();
        clause.setField(applicantsField);
        clause.setValueByText(applicant.toString());
        clause.openBrackets = first ? 1 : 0;
        clause.closeBrackets = last ? 1 : 0;
        clause.operator = filter.othersNames.length ? ClauseOperatorEnum.NOT_EQUAL : ClauseOperatorEnum.EQUAL;
        clause.conjunction = first ? ClauseConjunctionEnum.AND : ClauseConjunctionEnum.OR;
        clause.filterItem = filter;
        newClauses.push(clause);
      }
    }

    return newClauses;
  }

  private buildClausesFromFilter(originClauses, filter): Array<Clause> {
    switch (true) {
      case filter.chart === 'basic_top_applicants':
        return this.clausesByTopApplicantsFilter(originClauses, filter);
      case filter.chart === 'basic_technology_timeline':
        return this.clausesByBaseFilter(originClauses, filter, BooleanFieldEnum.PRIORITY_DATE);
      case filter.chart === 'classification_top_ipc':
        return this.clausesByBaseFilter(originClauses, filter, BooleanFieldEnum.IPC);
      case filter.chart === 'classification_top_cpc':
        return this.clausesByBaseFilter(originClauses, filter, BooleanFieldEnum.CPC);
      case filter.type === 'result-table':
        return this.clausesByBaseFilter(originClauses, filter, BooleanFieldEnum.LEGAL_STATUS);
    }

    return [];
  }

  private filtersToClauses(filters: Array<any>) {
    const originClauses = this.searchService.clauses.filter(o => !o.filterItem).map(o => o);
    this.filteredClauses.length = 0;

    for (const filter of filters) {
      this.filteredClauses.push(...this.buildClausesFromFilter(originClauses, filter));
    }

    if (originClauses.length === 0 && this.filteredClauses.length === 0) {
      this.filteredClauses.push(new Clause());
    }

    this.search(false);
  }

  private clausesByBaseFilter(originClauses, filter, fieldName: BooleanFieldEnum): Array<Clause> {
    const field = Field.getFieldByName(fieldName);

    if (fieldName === BooleanFieldEnum.LEGAL_STATUS) {
      return this.searchService.legalStatusFilterToClauses(filter, field);
    }

    const existedClause = this.findClause(
      originClauses, ClauseOperatorEnum.CONTAINS, ClauseConjunctionEnum.AND,
      filter.value, field
    );

    if (!existedClause) {
      if (fieldName === BooleanFieldEnum.PRIORITY_DATE) {
        return this.clausesPriorityDateFilterByYear(filter, field);
      }
      const clause = new Clause();
      clause.setField(field);
      clause.setValueByText(filter.value);
      clause.operator = ClauseOperatorEnum.CONTAINS;
      clause.conjunction = ClauseConjunctionEnum.AND;
      clause.filterItem = filter;
      clause.cleanValue();

      return [clause];
    }

    return [];
  }

  private clausesPriorityDateFilterByYear(filter, field): Array<Clause> {
    const clauses: Array<Clause> = [];

    let yearsStart: string;
    let yearsEnd: string;

    if (typeof filter.value === 'string') {
      const date = filter.value.split('-');

      yearsStart = date[0];
      yearsEnd = date[1];
    } else {
      yearsStart = filter.value;
    }

    const clauseStarYear = new Clause();
    clauseStarYear.setField(field);
    clauseStarYear.value = yearsStart;
    clauseStarYear.operator = yearsEnd ? ClauseOperatorEnum.GREATER_THAN : ClauseOperatorEnum.EQUAL;
    clauseStarYear.conjunction = ClauseConjunctionEnum.AND;
    clauseStarYear.filterItem = filter;
    clauses.push(clauseStarYear);

    if (yearsEnd) {
      const clauseEndYear = new Clause();
      clauseEndYear.setField(field);
      clauseEndYear.value = yearsEnd;
      clauseEndYear.operator = ClauseOperatorEnum.LESS_THAN;
      clauseEndYear.conjunction = ClauseConjunctionEnum.AND;
      clauseEndYear.filterItem = filter;
      clauses.push(clauseEndYear);
    }

    return clauses;
  }

  private clausesLegalStatusFilter(filter, field): Array<Clause> {
    const clauses: Array<Clause> = [];

    const values = filter.value.split('AND');
    values.forEach(value => {
      const val = value.split('=');
      const clause = new Clause();
      clause.setField(field);
      clause.value = val[1];
      clause.operator = ClauseOperatorEnum.EQUAL;
      clause.conjunction = ClauseConjunctionEnum.AND;
      clause.filterItem = filter;
      clauses.push(clause);
    });

    return clauses;
  }

  private recoveryQuery(): boolean {
    if (this.searchService.clauses.length > 0) {
      return false;
    }
    if (!window.localStorage.getItem(this.CACHE_SAVED_QUERY)) {
      return false;
    }

    const booleanQuerySearch = JSON.parse(window.localStorage.getItem(this.CACHE_SAVED_QUERY));

    if (booleanQuerySearch.search_fields) {
      const clausesTmp = BooleanSearchService.makeClausesFromSearchFields(booleanQuerySearch.search_fields);
      if (clausesTmp.length > 0) {
        setTimeout(() => {
          this.searchService.setClauses(clausesTmp);
          this.searchService.recoveredClausesFromStorage = clausesTmp;
        });
        return true;
      }
    }

    if (booleanQuerySearch.search_input) {
      this.searchService.search_input = booleanQuerySearch.search_input;
      this.showAdvancedMode = true;
      return true;
    }
    return false;
  }

  private removeSavedQuery(): void {
    window.localStorage.removeItem(this.CACHE_SAVED_QUERY);
  }

  private clearAdvancedFilter() {
    this.advancedFilterService.reset();
    this.booleanSearchStoreService.resetAdvancedFilter();
  }

  private storeScrollCombinedModePosition(): void {
    if (!this.isCombinedMode) {
      return;
    }
    const chartsContainer = document.querySelector(".charts-container");
    if (!chartsContainer) {
      return;
    }
    this.combinedModePosition = chartsContainer.scrollTop;
  }

  private scrollCombinedModePosition(): void {
    if (!this.isCombinedMode) {
      return;
    }
    const chartsContainer = document.querySelector(".charts-container");
    if (!chartsContainer) {
      return;
    }
    setTimeout(() => {
      chartsContainer.scroll(0, this.combinedModePosition);
    }, 100);
  }
  private buildChartPayload() {
    const payload = {
      charts: this.booleanSearchStoreService.getChartActiveNames(),
      'parameters': {}
    };

    const freeTextQuery = this.booleanSearchStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }
    return payload;
  }

  private getCharts() {
    this.booleanSearchStoreService.isCalculatingCharts = true;
    const calculate$ = this.chartService.calculate(this.buildChartPayload(), this.booleanSearchStoreService.searchHash)
      .pipe(
        take(1),
        finalize(() => this.booleanSearchStoreService.isCalculatingCharts = false)
      )
      .subscribe({
        next: ({ charts }) => { },
        error: (err) => {
          console.error(err);
          this.refreshSearch();
        }
      });
    this.subscriptions.add(calculate$);
  }

  onSearchType(event){
    this.booleanSearchStoreService.isPublications = event.target.checked;
    this.refreshSearch();
    this.searchService.validateClauses(this.booleanSearchStoreService.isPublications);
  }

  clearAllFilters(): void {
    this.booleanSearchStoreService.setFilters([]);
  }

  private getUserStatistics() {
    if (!this.userService.isCollaboratorUser()) {
      return;
    }

    const userStatistics$ = this.userService.getStatistics().subscribe();
    this.subscriptions.add(userStatistics$);
  }
}
