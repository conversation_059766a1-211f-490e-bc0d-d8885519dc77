export const tooltipTextBasic =
  `<p>With the Boolean Search you can search our database using English keywords or filtering by
    any attributes such as IPC/CPC codes, Applicants and many others.</p><br>
    <p>Simply click 'Select a field' to open the dropdown menu of fields you can choose from. The
    fields you can search are split up into three groups: 'Text fields', 'Bibliographic Information'
    and 'Analytics'. For every field you select, there is an individual infobox on the right which
    gives you instructions on how to use your selected field with the different operators and rules
    </p>
    <p>You can enter multiple rules by clicking on the plus icon. This will add another line to the
    end of your current list.</p>
    <p>At the beginning and end of every line you can find bracket buttons. These allow you to open
    and close brackets spanning multiple lines. You can open a bracket by clicking on the open
    bracket button. If you want to open multiple brackets, click on the bracket button up to three
    times. The button will then show up to three open brackets. By clicking a fourth time, you can
    remove the open brackets again. Once you open a bracket it remains open until you close it and
    the line will be indented to signal which bracket level you are currently working on. Adding
    further lines using the plus symbol will add them at the same level as the currently open bracket.
    </p>
    <p>Please note that we currently only fully support English keywords.</p>`;

export const tooltipTextAdvanced =
  `<p>With the Boolean Search you can search our database using English keywords or filtering by
    any attributes such as IPC/CPC codes, Applicants and many others.</p>
    <p>In <b>advanced mode</b> you can type or copy/paste the query yourself.</p>
    <p>Some examples of advanced queries:
    <ul>
        <li>PUBLICATION_DATE>2010-01-01 AND (PUBLICATION_AUTHORITY<>CN OR IPC=A01C* OR IPC=A02E*)</li>
        <li>TEXT=agricultural irrigation AND APPLICANTS=(BOSCH* OR JOHN DEERE* OR SIEMENS*)</li>
        <li>TITLE="diffusion membrane"~5 AND (MARKET_COVERAGE>1 OR AUTHORITIES=DE) AND PRIORITY_DATE>=2000</li>
        <li>CPC=(C05B1/02* OR C05B3/00* OR C05B9/00*) AND (<br/>
            APPLICANTS=(BAYER* OR BASF* OR DUPONT*) OR CLAIMS=Fertilizers nitrogen) AND CITATION_FORWARD_COUNT>1</li>
    </ul>
    </p>
    <p>Fields which can be used in advanced queries are:
    <ul>
        <li>TITLE</li>
        <li>ABSTRACT</li>
        <li>CLAIMS</li>
        <li>DESCRIPTION</li>
        <li>TEXT</li>
        <li>PRIORITY_DATE</li>
        <li>PUBLICATION_DATE</li>
        <li>CPC</li>
        <li>IPC</li>
        <li>ALSO_PUBLISHED_AS</li>
        <li>APPLICANTS</li>
        <li>INVENTORS</li>
        <li>TECH_FIELDS</li>
        <li>PUBLICATION_AUTHORITY</li>
        <li>AUTHORITIES</li>
        <li>IMPACT</li>
        <li>RISK</li>
        <li>CITATION_BACKWARD_COUNT</li>
        <li>CITATION_FORWARD_COUNT</li>
        <li>NUMBER_OF_AUTHORITIES</li>
        <li>PUBLICATION_KIND</li>
        <li>APPLICATION_NUMBERS</li>
        <li>LEGAL_STATUS</li>
        <li>TAG</li>
    </ul>
    </p>`;
