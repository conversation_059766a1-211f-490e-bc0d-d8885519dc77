import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BooleanSearchComponent } from './boolean-search/boolean-search.component';
import { BooleanRoutingModule } from './boolean-routing.module';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { ChartDashboardModule } from '@shared/charts';
import { ChartsModule } from '@shared/charts/charts.module';

@NgModule({
  declarations: [
    BooleanSearchComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    ChartsModule,
    ChartDashboardModule,
    NgbModule,
    HighchartsChartModule,
    NgxSliderModule,
    BooleanRoutingModule
  ],
  exports: [
    ChartsModule,
    ChartDashboardModule
  ]
})
export class BooleanModule {
}
