import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CitationSearchComponent, NplTableComponent } from './citation-search';
import { CitationChartsComponent } from './citation-charts/citation-charts.component';
import { CitationRoutingModule } from './citation-routing.module';
import { SharedModule } from '@shared/shared.module';
import { BasicChartsModule, ClassificationChartsModule, CitationChartsModule } from '@shared/charts';

@NgModule({
  declarations: [
    CitationSearchComponent,
    CitationChartsComponent,
    NplTableComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    CitationRoutingModule,
    BasicChartsModule, ClassificationChartsModule,
    CitationChartsModule
  ]
})
export class CitationModule {
}
