<div class="modal-header">
  <div class="modal-title">
    {{getTitle()}}
  </div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>

<div class="modal-body">
  <form [formGroup]="form" class="d-flex flex-column justify-content-center align-items-stretch w-100">
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

    <label class="float-start">Name of the new combined list:</label>

    <div>
      <input [ngClass]="{'is-invalid': isFieldInvalid('name')}" class="form-control" formControlName="name"
             required/>
      <div *ngIf="form.get('name').touched && form.get('name').hasError('required')"
           class="invalid-feedback">Please enter name of the new combined list
      </div>
    </div>

    <h5 class="mt-3 mb-2 float-start">Select aggregation mode:</h5>

    <div class="row">
      <div class="col-4">
        <input formControlName="operation" id="option-1" name="operation" type="radio" value="UNION">
        <label for="option-1">
          <img class="aggregate-selector-image" src="assets/images/aggregate-union.png">
          <p  class="mb-1 mt-2">Unifying</p>
          <small >All results except the duplicates</small>
        </label>
      </div>
      <div class="col-4">
        <input formControlName="operation" id="option-2" name="operation" type="radio" value="INTERSECTION">
        <label for="option-2">
          <img class="aggregate-selector-image" src="assets/images/aggregate-intersection.png">
          <p class="mb-1 mt-2">Intersection</p>
          <small >Only publications that are common for all searches</small>
        </label>
      </div>
      <div class="col-4">
        <input formControlName="operation" id="option-3" name="operation" type="radio" value="DIFF">
        <label for="option-3">
          <img class="aggregate-selector-image" src="assets/images/aggregate-simetric.png">
          <p  class="mb-1 mt-2">Unique</p>
          <small >Only publications that are unique in each search</small>
        </label>
      </div>
    </div>
    <!-- <table class="table table-striped">
      <tbody>
      <tr>
        <td class="aggregate-option-row">
          <input formControlName="operation" id="option-1" name="operation" type="radio" value="UNION">
          <label for="option-1">
            <h5>
              Unifying<br>
              <small>All results except the duplicates</small></h5>
            <img class="aggregate-selector-image" src="assets/images/aggregate-union.png">
          </label>
        </td>
      </tr>
      <tr>
        <td class="aggregate-option-row">
          <input formControlName="operation" id="option-2" name="operation" type="radio" value="INTERSECTION">
          <label for="option-2">
            <h5>
              Intersection<br>
              <small>Only publications that are common for all searches</small></h5>
            <img class="aggregate-selector-image" src="assets/images/aggregate-intersection.png">
          </label>
        </td>
      </tr>
      <tr>
        <td class="aggregate-option-row">
          <input formControlName="operation" id="option-3" name="operation" type="radio" value="DIFF">
          <label for="option-3">
            <h5>
              Unique<br>
              <small>Only publications that are unique in each search</small></h5>
            <img class="aggregate-selector-image" src="assets/images/aggregate-simetric.png">
          </label>
        </td>
      </tr>
      </tbody>
    </table> -->
  </form>
</div>

<div class="modal-footer">
  <button (click)="activeModal.dismiss('Cancel')" class="btn btn-primary-outline btn-md" type="button">Cancel</button>
  <button (click)="aggregate()" [disabled]="!form.dirty || form.invalid || isSaving" class="btn btn-primary btn-md" type="button">Aggregate</button>
</div>
