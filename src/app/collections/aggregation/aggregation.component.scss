@import 'scss/layout2021/variables';
input[type="radio"]{
  display: none;
  &+label{
    cursor: pointer;
    border: 1px solid $border-default;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    text-align: center;
    padding: 1rem;
    color: $color-text-03;
    transition: .3s ease all;
    &:hover{
      border-color: $brand-green;
      box-shadow: 0px 15px 30px #0F2C3519;
    }
    p{
      color: $brand-green;
    }
  }
  &:checked+label{
    background-color: $brand-green-pressed;
    border-color: $brand-green-pressed;
    small, p{
      color: $color-text-01;
    }
  }
}
