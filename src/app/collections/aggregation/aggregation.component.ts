import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AggregationRequest, Collection, CollectionService } from '@core/services';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { CollectionStoreService } from '@core/store';
import { take } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-aggregation',
  templateUrl: './aggregation.component.html',
  styleUrls: ['./aggregation.component.scss']
})
export class AggregationComponent implements OnInit, OnDestroy {
  folderId: number;
  selectedCollections: Array<Collection> = [];
  form: UntypedFormGroup;
  isSaving = false;
  errors: string[];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private collectionService: CollectionService,
    private collectionStoreService: CollectionStoreService
  ) {
  }

  ngOnInit() {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getTitle(): string {
    return `Aggregate ${this.selectedCollections.length} selected Lists`;
  }

  aggregate() {
    if (this.form.valid) {
      const payload = this.form.value as AggregationRequest;
      payload.folder_id = this.folderId;
      payload.collection_ids = this.selectedCollections.map(o => o.id);

      this.form.disable();
      this.isSaving = true;

      const aggregateCollections$ = this.collectionService.aggregateCollections(payload)
        .pipe(take(1))
        .subscribe({
          next: data => {
            this.collectionStoreService.setRefreshCollections(true);
            this.activeModal.close();
          }, error: ({error}) => {
            console.error(error);
            this.isSaving = false;
            this.form.enable();
            this.errors = error.message || ['Error when combining the list'];
          }
        });
      this.subscriptions.add(aggregateCollections$);
    }
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return field && field.touched && field.invalid;
  }

  private buildForm() {
    this.form = new UntypedFormGroup({
      name: new UntypedFormControl('', [Validators.required]),
      operation: new UntypedFormControl('UNION', [Validators.required]),
    });
  }
}
