<div class="flex-fill page-content d-flex flex-column justify-content-start">
  <div class="d-flex flex-column">

    <app-page-bar pageTitle="Comments & highlights">
      <app-filter-term-input leftSide placeHolder="Search for comments" (termChanged)="onFilterChanged($event)"></app-filter-term-input>
      <ng-container rightSide>
        <a href="javascript:void(0)" class="item-bar icon icon-delete" [ngClass]="{'disabled': selectedDocuments.size === 0}"
           (click)="deleteAnnotationsForSelectedDocuments()" ngbTooltip="Delete comments & highlights for selected documents" container="body">
          Delete comments & highlights for {{selectedDocuments.size}} selected
        </a>
      </ng-container>
    </app-page-bar>

    <div class="overflow-auto flex-fill">
      <div *ngIf="!loading else loadingTemplate">
        <app-alert type="danger" [message]="errorMessage" *ngIf="errorMessage"></app-alert>
        <app-alert type="warning" message="No comments & highlights found" *ngIf="!documents.length && filterTerm?.length && !errorMessage"></app-alert>

        <table id="annotated-documents-table" class="table table-condensed publication-table w-100 table-hover" *ngIf="documents.length">
          <thead class="w-100">
              <tr>
                  <th width="30px">
                    <label class="checkbox m-0 p-0">
                      <input type="checkbox" (change)="selectAllDocuments($event)" [checked]="areDocumentsSelected">
                      <span class="no-text">&nbsp;</span>
                    </label>
                  </th>
                  <th width="60px" class="text-center">
                    Legal status
                  </th>
                  <th>
                    Title
                  </th>
                  <th width="210px">
                    Publ. no.
                  </th>
                  <th>
                    Applicants
                  </th>
                  <th width="100px" class="text-center">
                    Highlights
                  </th>
                  <th width="105px" class="text-center">
                    Comments
                  </th>
                  <th width="120px"></th>
              </tr>
          </thead>

          <tbody *ngFor="let d of documents | paginate:
          {
            itemsPerPage: pagination?.page_size,
            id: 'annotated-documents-pagination',
            currentPage: pagination?.current_page,
            totalItems: pagination?.total_hits
          }; index as i;"
                 [ngClass]="{'has-detail-bg': openedRows.has(getDocId(d)),
                 'preview-border': openedRows.has(getDocId(d)) && (i === 0 || !openedRows.has(getDocId(documents[i-1]))),
                 'preview-border-vertical': openedRows.has(getDocId(d)) && i > 0 && openedRows.has(getDocId(documents[i-1]))}"
                 [id]="d.document.general?.docdb_family_id">
              <tr [ngClass]="{'open-sans-semi-bold detailed' : openedRows.has(getDocId(d))}">
                <td>
                  <label class="checkbox m-0 p-0">
                    <input type="checkbox" (change)="selectDocument($event, d)" [checked]="isDocumentSelected(d)">
                    <span class="no-text">&nbsp;</span>
                  </label>
                </td>
                <td (click)="openDetail(getDocId(d))" style="width: 100px; min-width: 100px; max-width: 100px">
                  <div class="d-flex flex-column justify-content-center align-items-stretch gap-spacing-x-s">
                    <span *ngFor="let ls of legalStatus[getDocId(d)]" class="d-flex justify-content-center badge-{{ls.name | lowercase}} content-label-small"
                        [ngbTooltip]="ls.tooltip" tooltipClass="white-tooltip">{{ ls.name | titlecase}}</span>
                  </div>
                </td>
                <td class="tag-selector-hover">
                  <span (click)="openDetail(getDocId(d))">{{ d.document?.bibliographic?.title }}</span>&nbsp;
                  <div #containerTags class="w-100" [ngClass]="!hasTags(d.document) ? 'd-inline' : 'd-flex align-items-center'" *ngIf="userService.hasTagFeature()">
                    <app-tags-display [patent]="d.document" [container]="containerTags"
                                   [canManageTags]="userService.isNotExternalUser()"
                                   [showIcon]="false" [updateTagsOnResize]="true"
                                   [storeService]="storeService"
                                   [collapsedDisplayRows]="1"
                                   [expandedDisplayRows]="3"
                                   (tagsChange)="onPatentTagsChanged($event, d.document)"
                                   (click)="$event.stopPropagation()">
                    </app-tags-display>
                  </div>
                </td>
                <td class="publication-number-col">
                  <a [routerLink]="['/patent/view', getDocId(d)]" [queryParams]="patentViewerQueryParams" [innerHTML]="getPublicationNumber(d)" (click)="onOpenViewer(d.document)"></a>
                </td>
                <td (click)="openDetail(getDocId(d))">{{ patentTableService.getApplicants(d.document) }}</td>
                <td (click)="openDetail(getDocId(d))" class="text-center">{{ d.labels.length }}</td>
                <td (click)="openDetail(getDocId(d))" class="text-center">{{ d.comments.length }}</td>
                <td class="table-actions">
                  <div class="d-flex align-items-center justify-content-center gap-spacing-sm">
                    <div class="button-main-secondary-grey button-medium button-square" ngbTooltip="Delete all comments & highlights" [ngClass]="{'invisible disabled': !hasDeletableAnnotations(d)}"
                         (click)="deleteCommentsAndHighlights(d)">
                      <i class="fa-regular fa-fw fa-trash-alt"></i>
                    </div>

                    <a [routerLink]="['/patent/view', getDocId(d)]" [queryParams]="patentViewerQueryParams(d)" (click)="onOpenViewer(d.document)"
                       target="_self" class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square" ngbTooltip="Open patent">
                      <i class="fa-regular fa-file-magnifying-glass"></i>
                    </a>

                    <div class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square" [ngClass]="{'preview-close': openedRows.has(getDocId(d))}"
                         (click)="openDetail(getDocId(d))">
                      <span *ngIf="!openedRows.has(getDocId(d))" class="caret-down"><i class="fa-regular fa-chevron-down"></i></span>
                      <span *ngIf="openedRows.has(getDocId(d))" class="caret-up"><i class="fa-regular fa-chevron-up"></i></span>
                    </div>
                  </div>
                </td>
              </tr>

              <div class="d-table-row patent-detail-container has-detail-bg" *ngIf="openedRows.has(getDocId(d))">
                  <td colspan="8" class="patent-detail px-1">
                    <div class="d-flex justify-content-between">
                      <div class="patent-detail-title m-2 flex-fill open-sans-bold"><i class="far fa-message pe-2"></i> Comments</div>
                    </div>

                    <div class="d-flex justify-content-between mx-2" *ngIf="d.comments?.length">
                      <ng-container [ngTemplateOutlet]="commentHeaderTemplate"></ng-container>
                    </div>

                    <div class="patent-detail-content mx-2">
                      <ng-container *ngIf="d.comments?.length">
                        <ng-container *ngFor="let cm of getParentComments(d); let lastCm = last;">
                          <ng-container [ngTemplateOutlet]="commentRowTemplate"
                                        [ngTemplateOutletContext]="{document: d, comment: cm, last: lastCm && getReplies(d, cm).length == 0, hasReplies: getReplies(d, cm).length > 0}">
                          </ng-container>

                          <ng-container *ngFor="let cm of getReplies(d, cm); let lastReplies = last;"
                                        [ngTemplateOutlet]="commentRowTemplate"
                                        [ngTemplateOutletContext]="{document: d, comment: cm, last: lastReplies, hasReplies: false}">
                          </ng-container>
                        </ng-container>
                      </ng-container>

                      <div *ngIf="!d.comments?.length" class="d-flex justify-content-start">
                        <div class="text-center">No comments</div>
                      </div>
                    </div>

                    <div class="d-flex justify-content-between mt-3">
                      <div class="patent-detail-title m-2 flex-fill open-sans-bold"><i class="far fa-bookmark pe-2"></i> Highlights</div>
                    </div>

                    <div class="d-flex justify-content-between mx-2" *ngIf="d.labels?.length">
                      <ng-container [ngTemplateOutlet]="highlightHeaderTemplate"></ng-container>
                    </div>

                    <div class="patent-detail-content mx-2">
                      <ng-container *ngIf="d.labels?.length">
                        <ng-container *ngFor="let lb of d.labels; let last = last;"
                                      [ngTemplateOutlet]="highlightRowTemplate"
                                      [ngTemplateOutletContext]="{document: d, label: lb, last: last}">
                        </ng-container>
                      </ng-container>

                      <div *ngIf="!d.labels?.length" class="d-flex justify-content-start">
                        <div class="text-center">No highlights</div>
                      </div>
                    </div>
                  </td>
                </div>
          </tbody>
        </table>

        <div class="d-flex justify-content-between mt-4 align-items-center" *ngIf="!errorMessage">
          <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" [pageOptions]="pageOptions"></app-page-size>

          <div class="flex-fill">
            <pagination-controls *ngIf="pagination?.last_page > 1" id="annotated-documents-pagination" class="d-flex justify-content-end"
                                 (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<ng-template #loadingTemplate>
  <app-spinner></app-spinner>
</ng-template>

<ng-template #commentHeaderTemplate>
  <div class="py-2 px-2 row-border-bottom fw-bold" style="width: 120px;min-width: 120px;">
    Field
  </div>
  <div class="ps-4 pe-2 py-2 flex-grow-1 row-border-bottom fw-bold">
    Comment
  </div>
  <div class="ps-4 pe-2 py-2 row-border-bottom fw-bold" style="width: 180px; min-width: 180px">
    Date
  </div>
  <div class="ps-4 pe-2 py-2 row-border-bottom fw-bold" style="width: 150px; min-width: 150px">
    Created by
  </div>
  <div style="width: 35px; min-width: 35px" class="py-2 px-0 m-0 d-flex justify-content-between align-items-center row-border-top row-border-bottom">
  </div>
</ng-template>

<ng-template #commentRowTemplate let-d="document" let-cm="comment" let-last="last" let-hasReplies="hasReplies">
  <div class="d-flex justify-content-between">
    <div class="py-2 px-2 bg-white row-border-top" style="width: 120px;min-width: 120px;" [ngClass]="{'row-border-bottom': last}">
      <ng-container *ngIf="!cm.parent_comment_id">{{ cm.field | titlecase }}</ng-container>
    </div>
    <div class="ps-4 pe-2 py-2 flex-grow-1 bg-white row-border-top d-flex justify-content-start" [ngClass]="{'row-border-bottom': last}">
      <div *ngIf="cm.parent_comment_id" class="adc-replied-comment"></div>
      <div class="adc-comment">
        <i class="fa-regular fa-lock  m-r-spacing-sm" *ngIf="cm.private"></i>
        <span [innerHTML]="cm.comment_text_only | truncate: maxDisplayText :true :'' | bypassSecurity: 'html'"
              *ngIf="cm.comment_text_only?.length > maxDisplayText && !expandedComments.has(cm.id)"></span>
        <span [innerHTML]="cm.parsed_comment | bypassSecurity: 'html'"
              *ngIf="cm.comment_text_only?.length <= maxDisplayText || expandedComments.has(cm.id)"></span>
        <ng-container *ngIf="cm.comment_text_only?.length > maxDisplayText">
          <a href="javascript:void(0)" *ngIf="expandedComments.has(cm.id)" (click)="collapseComment(cm.id)"> ...less</a>
          <a href="javascript:void(0)" *ngIf="!expandedComments.has(cm.id)" (click)="expandComment(cm.id)"> ...more</a>
        </ng-container>
      </div>
    </div>
    <div class="ps-4 pe-2 py-2 bg-white row-border-top" style="width: 180px; min-width: 180px" [ngClass]="{'row-border-bottom': last}">
      {{ cm.created_at | date :'MMM d, y' }}
    </div>
    <div class="ps-4 pe-2 py-2 bg-white row-border-top" style="width: 150px; min-width: 150px; word-break: break-all;" [ngClass]="{'row-border-bottom': last}">
      {{ cm.user | userTitle }}
    </div>
    <div class="p-spacing-md bg-white d-flex justify-content-between align-items-start row-border-top" [ngClass]="{'row-border-bottom': last}">
      <div *ngIf="canDeleteAnnotation(cm.user_id)" [ngbTooltip]="cm.parent_comment_id ? 'Delete this reply' : 'Delete this comment' + (hasReplies ? ' and all related replies' : '')"
           class="button-main-secondary-grey button-small button-square" (click)="deleteComment(d, cm)">
          <i class="fa-regular fa-fw fa-trash-alt"></i>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #highlightHeaderTemplate>
  <div class="py-2 row-border-bottom fw-bold text-center" style="width: 60px;min-width: 60px;">
    Color
  </div>
  <div class="ps-4 pe-2 py-2 row-border-bottom fw-bold" style="width: 120px;min-width: 120px;">
    Field
  </div>
  <div class="ps-4 pe-2 py-2 flex-grow-1 row-border-bottom fw-bold">
    Selected text
  </div>
  <div class="ps-4 pe-2 py-2 row-border-bottom fw-bold" style="width: 180px; min-width: 180px">
    Date
  </div>
  <div class="ps-4 pe-2 py-2 row-border-bottom fw-bold" style="width: 150px; min-width: 150px">
    Created by
  </div>
  <div class="py-2 px-0 m-0 d-flex justify-content-between align-items-center row-border-bottom" style="width: 35px; min-width: 35px">
  </div>
</ng-template>

<ng-template #highlightRowTemplate let-d="document" let-lb="label" let-last="last">
  <div class="d-flex justify-content-between">
    <div class="py-2 bg-white row-border-top text-center" style="width: 60px;min-width: 60px;" [ngClass]="{'row-border-bottom': last}">
      <div class="adc-comment-box-color" [style.background-color]="'#'+lb.label.color"></div>
    </div>
    <div class="ps-4 pe-2 py-2 bg-white row-border-top" style="width: 120px;min-width: 120px;" [ngClass]="{'row-border-bottom': last}">
      {{ lb.field | titlecase }}
    </div>
    <div class="ps-4 pe-2 py-2 flex-grow-1 bg-white row-border-top" [ngClass]="{'row-border-bottom': last}">
      <span *ngIf="lb.text?.length > maxDisplayText && !expandedLabels.has(lb.id)">{{ lb.text | truncate: maxDisplayText :true :'' }}</span>
      <span *ngIf="lb.text?.length <= maxDisplayText || expandedLabels.has(lb.id)">{{ lb.text }}</span>
      <ng-container *ngIf="lb.text?.length > maxDisplayText">
        <a href="javascript:void(0)" *ngIf="expandedLabels.has(lb.id)" (click)="collapseLabel(lb.id)"> ...less</a>
        <a href="javascript:void(0)" *ngIf="!expandedLabels.has(lb.id)" (click)="expandLabel(lb.id)"> ...more</a>
      </ng-container>
    </div>
    <div class="ps-4 pe-2 py-2 bg-white row-border-top" style="width: 180px; min-width: 180px" [ngClass]="{'row-border-bottom': last}">
      {{ lb.created_at | date :'MMM d, y' }}
    </div>
    <div class="ps-4 pe-2 py-2 bg-white row-border-top" style="width: 150px; min-width: 150px; word-break: break-all;" [ngClass]="{'row-border-bottom': last}">
      {{ lb.user | userTitle }}
    </div>
    <div class="p-spacing-md bg-white d-flex justify-content-between align-items-start row-border-top"
         [ngClass]="{'row-border-bottom': last}">
      <div *ngIf="canDeleteAnnotation(lb.user_id)" ngbTooltip="Delete this highlight" class="button-main-secondary-grey button-small button-square"
           (click)="deleteLabel(d, lb)">
          <i class="fa-regular fa-fw fa-trash-alt"></i>
      </div>
    </div>
  </div>
</ng-template>
