@import 'scss/layout2021/variables';
@import 'scss/components/input-group';
@import 'scss/components/publication-table';

$icons: ("delete": "icon-delete");

.icon {
  @each $name, $icon in $icons {
    &-#{$name} {
      padding-left: 25px;
      background-position: 0;
      background-position-y: 7px;
      background-size: 15px;
      background-repeat: no-repeat;
      transition: all 0.2s ease;
      background-image:  url('/assets/images/layout2022/#{$icon}.svg');
      &:hover,&.active{
        background-image:  url('/assets/images/layout2022/#{$icon}-hover.svg');
      }
      &.disabled {
        background-image: url('/assets/images/layout2022/#{$icon}-disabled.svg');
      }
    }
  }
}

.adc-comment-box-color {
  width: 18px;
  height: 18px;
  display: inline-block;
  text-align: center;
  margin: 0 auto;
  border: 1px solid #A0A0A0;
  border-radius: 3px;
}

.adc-replied-comment {
  width: 30px;
  min-width: 30px;
  height: 50%;
  border-left: 1px solid #CBDCE2;
  border-bottom: 1px solid #CBDCE2;
  margin-right: 10px;
  border-radius: 2px;
}

::ng-deep {
  .adc-comment {
    .lcd-tagged-user, .lcd-tagged-group {
      margin: 1px;
      border: none;
      border-radius: 3px;
      background: $brand-green;
      color: white;
      padding: 0 2px;
    }
  }
}
