import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AnnotatedDocumentsComponent } from './annotated-documents.component';
import {SafeHtmlPipe, SemanticSearchStoreService, TagParserPipe} from '@core';
import { provideMatomo } from 'ngx-matomo-client';
import { RouterModule } from '@angular/router';

describe('AnnotatedDocumentsComponent', () => {
  let component: AnnotatedDocumentsComponent;
  let fixture: ComponentFixture<AnnotatedDocumentsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AnnotatedDocumentsComponent ],
      imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
      providers: [
        SemanticSearchStoreService,
        SafeHtmlPipe,
        TagParserPipe,
        provideMatomo({siteId: '7', trackerUrl: 'https://stats.dennemeyer.digital/', disabled: true })
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AnnotatedDocumentsComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
