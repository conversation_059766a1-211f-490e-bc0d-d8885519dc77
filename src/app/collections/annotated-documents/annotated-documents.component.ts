import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import {Router} from '@angular/router';
import {
  AnnotationService,
  BaseStoreService,
  ConfirmationDialogService,
  DocumentAnnotation,
  DocumentLabel,
  DocumentsResponse,
  FullDocumentAnnotationResponse,
  PaginationMetadata,
  Patent,
  PatentService,
  PatentTableService,
  PatentViewModeService,
  SafeHtmlPipe,
  TagParserPipe,
  TagService,
  UserService
} from '@core';
import { BehaviorSubject, forkJoin, Observable, of, Subscription, switchMap, timer } from 'rxjs';
import { catchError, concatMap, debounce, map, take, tap } from 'rxjs/operators';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';
import { TagModel } from '@core/models/tag.model';

@Component({
  selector: 'app-annotated-documents',
  templateUrl: './annotated-documents.component.html',
  styleUrls: ['./annotated-documents.component.scss']
})
export class AnnotatedDocumentsComponent implements OnInit, OnDestroy {
  @Input() storeService: BaseStoreService;

  filterTerm = '';
  errorMessage: string;

  documents: Array<FullDocumentAnnotationResponse> = [];
  payload: any;
  pagination: PaginationMetadata;
  loading = false;
  pageSize = 25;
  pageOptions = [25, 50, 100];
  selectedDocuments: Set<string> = new Set<string>();
  openedRows: Set<string> = new Set<string>();

  legalStatus = {};

  sortBy = 'created_at';
  sortOrder: 'asc' | 'desc' = 'desc';

  maxDisplayText = 150;
  expandedComments: Set<number> = new Set<number>();
  expandedLabels: Set<number> = new Set<number>();

  protected readonly PatentViewModeService = PatentViewModeService;
  private searchDocumentsSubject = new BehaviorSubject<string>(null);
  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    public patentService: PatentService,
    public patentTableService: PatentTableService,
    public userService: UserService,
    public tagService: TagService,
    private annotationService: AnnotationService,
    private confirmationDialogService: ConfirmationDialogService,
    private safeHtmlPipe: SafeHtmlPipe,
    private tagParser: TagParserPipe,
  ) {
  }

  get areDocumentsSelected(): boolean {
    return this.documents.every((d) => this.selectedDocuments.has(this.getDocId(d)));
  }

  ngOnInit() {
    const searchDocuments$ = this.searchDocumentsSubject.asObservable()
      .pipe(
        tap((val) => {
          this.loading = true;
        }),
        map((val) => val?.trim()),
        debounce((val: string) => timer(val?.length > 0 ? 1000 : 0)),
        concatMap((val) => this.searchDocuments(val)),
        tap(() => {
          this.loading = false;
        })
      )
      .subscribe();
    this.subscriptions.add(searchDocuments$);
  }

  patentViewerQueryParams(document) {
    return {
      backButtonTitle: 'Back to Annotated',
      previousUrl: 'collections/documents/annotated',
      mode: document.comments.length > 0 ? PatentSideBarViewModeEnum.MODE_COMMENTS : PatentSideBarViewModeEnum.MODE_HIGHLIGHTS,
    };
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  openDetail(docId): void {
    if (this.openedRows.has(docId)) {
      this.openedRows.delete(docId);
    } else {
      this.openedRows.add(docId);
    }
  }

  onPageChange($event: number) {
    this.onPageChangeDocuments($event);
  }

  sort(field): void {
    if (field === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }

    this.searchDocumentsSubject.next(this.filterTerm);
  }

  onChangePageSize(event: number): void {
    this.pageSize = event;
    this.resetCurrentPage();
    this.searchDocumentsSubject.next(this.filterTerm);
  }

  selectAllDocuments(event: Event) {
    if (event.target['checked']) {
      this.selectedDocuments = new Set([...this.selectedDocuments, ...this.documents.map((d) => this.getDocId(d))]);
    } else {
      const docIds = this.documents.map((d) => this.getDocId(d));
      this.selectedDocuments = new Set([...this.selectedDocuments].filter((id) => !docIds.includes(id)));
    }
  }

  selectDocument(event: Event, d: FullDocumentAnnotationResponse) {
    const docId = this.getDocId(d);
    if (event.target['checked']) {
      this.selectedDocuments.add(docId);
    } else {
      this.selectedDocuments.delete(docId);
    }
  }

  isDocumentSelected(d: FullDocumentAnnotationResponse): boolean {
    return this.selectedDocuments.has(this.getDocId(d));
  }

  deleteLabel(d: FullDocumentAnnotationResponse, lb: DocumentLabel) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want<br/> <b>to delete the highlight <span class="text-green">${lb.label.name}</span></b><br/> related to<br/> <div class="mt-2">${this.getPublicationNumber(d)}</div></div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const deleteDocumentLabel$ = this.annotationService.deleteDocumentLabel(Number(this.getDocId(d)), lb.id)
          .pipe(take(1))
          .subscribe({
            next: (val) => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.areDocumentsSelected);
              this.selectedDocuments.clear();
              this.searchDocumentsSubject.next(this.filterTerm);
            },
            error: (error) => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(deleteDocumentLabel$);
      }
    });
  }

  deleteComment(d: FullDocumentAnnotationResponse, cm: DocumentAnnotation) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const hasReplies = this.getReplies(d, cm).length > 0;
    const subject = cm.parent_comment_id ? 'reply' : 'comment';
    const message = `<div class="text-center">Are you sure you want<br/> <b>to delete the ${subject}${hasReplies ? ' and all related replies' : ''}</b><br/> related to<br/> <div class="mt-2">${this.getPublicationNumber(d)}</div></div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const deleteDocumentComment$ = this.annotationService.deleteDocumentComment(Number(this.getDocId(d)), cm.id)
          .pipe(take(1))
          .subscribe({
            next: (val) => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.areDocumentsSelected);
              this.selectedDocuments.clear();
              this.searchDocumentsSubject.next(this.filterTerm);
            },
            error: (error) => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(deleteDocumentComment$);
      }
    });
  }

  deleteCommentsAndHighlights(d: FullDocumentAnnotationResponse) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want<br/> <b>to delete all comments & highlights related to</b><br/> <div class="mt-2">${this.getPublicationNumber(d)}</div></div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const docId = this.getDocId(d);
        const deleteDocumentComment$ = this.annotationService.deleteLabelsAndComments(Number(docId))
          .pipe(take(1))
          .subscribe({
            next: () => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.documents.length === 1);
              this.searchDocumentsSubject.next(this.filterTerm);
              this.openedRows.delete(docId);
            }, error: error => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(deleteDocumentComment$);
      }
    });
  }

  deleteAnnotationsForSelectedDocuments() {
    const pluralNoun = this.selectedDocuments.size > 1 ? 'documents' : 'document';
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want<br/> <b>to delete all comments & highlights related to</b><br/>${this.selectedDocuments.size} selected ${pluralNoun}</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const obs = [...this.selectedDocuments].map((id) => {
          return this.annotationService.deleteLabelsAndComments(Number(id))
        });
        const obs$ = forkJoin(obs).subscribe({
          next: (val) => {
            this.loading = false;
            this.resetCurrentPageAfterDeleting(this.areDocumentsSelected);
            this.selectedDocuments.clear();
            this.searchDocumentsSubject.next(this.filterTerm);
          },
          error: (error) => {
            this.loading = false;
            console.log(error);
          }
        });
        this.subscriptions.add(obs$);
      }
    });
  }

  onFilterChanged(event: string) {
    this.filterTerm = event;
    this.resetCurrentPage();
    this.searchDocumentsSubject.next(event);
  }

  getDocId(d: FullDocumentAnnotationResponse) {
    return d.document.general.docdb_family_id;
  }

  getLegalStatusIcon(patent: Patent) {
    return this.patentService.getFamilyLegalStatusIcon(patent.bibliographic.legal_status);
  }

  hasTags(patent: Patent): boolean {
    return patent?.custom_tags?.length > 0;
  }

  collapseComment(id: number) {
    this.expandedComments.delete(id);
  }

  expandComment(id: number) {
    this.expandedComments.add(id);
  }

  collapseLabel(id: number) {
    this.expandedLabels.delete(id);
  }

  expandLabel(id: number) {
    this.expandedLabels.add(id);
  }

  canDeleteAnnotation(userId: number) {
    return userId === this.userService.getUser().profile.id || this.userService.isManager();
  }

  hasDeletableAnnotations(d: FullDocumentAnnotationResponse): boolean {
    return d.labels.some(o => this.canDeleteAnnotation(o.user_id)) || d.comments.some(o => this.canDeleteAnnotation(o.user_id));
  }

  getPublicationNumber(d: FullDocumentAnnotationResponse): string {
    const publicationNumber = this.patentTableService.getPublicationNumber(d.document).toString();
    const flagCss = this.patentService.getFlagCssByPublication(publicationNumber, FlagSizeEnum.MD).toLowerCase();
    return `<span class="ms-1 ${flagCss}"></span> ${publicationNumber}`;
  }

  getParentComments(d: FullDocumentAnnotationResponse): DocumentAnnotation[] {
    return d.comments.filter(o => !o.parent_comment_id);
  }

  getReplies(d: FullDocumentAnnotationResponse, parentComment: DocumentAnnotation): DocumentAnnotation[] {
    return d.comments.filter(o => o.parent_comment_id === parentComment.id);
  }

  private searchDocuments(filterTerm: string): Observable<DocumentsResponse> {
    const page = this.pagination ? this.pagination.current_page : 1;
    const payload = this.buildPayload(page, filterTerm);

    this.errorMessage = null;
    this.documents = [];
    this.pagination = null;
    this.openedRows.clear();

    return this.annotationService.getAnnotatedDocuments(payload)
      .pipe(
        switchMap((data) => this.loadRelatingUsers(data)),
        switchMap((data) => this.loadTags(data)),
        tap(({documents, page}) => {
          this.documents = documents;
          this.pagination = page;

          this.documents.forEach((doc) => {
            doc.comments?.forEach((comment) => {
              const safeComment = this.safeHtmlPipe.transform(comment.comment, null, null, {allowedTags: []});
              comment.parsed_comment = this.tagParser.transform(safeComment, '16px/24px', true);
              comment.comment_text_only = this.tagParser.transform(safeComment, '16px/24px', false);
            });
          });

          if (this.documents.length === 0 && !this.filterTerm) {
            this.errorMessage = 'No comments & highlights found';
          }
          if (this.storeService.docdbFamilyIdFromPatentViewer) {
            this.scrollToPatentFromPatentViewer();
          }
          this.setLegalStatuses();
        }),
        catchError((error) => {
          console.log(error);
          this.loading = false;
          this.errorMessage = 'An error occurred while loading documents';
          throw error;
        })
      )
      ;
  }

  private buildPayload(page: number, filterTerm: string): any {
    const payload = {
      page_size: this.pageSize,
      page: page
    };
    if (filterTerm) {
      payload['filter_comment'] = filterTerm;
    }
    return payload;
  }

  private loadRelatingUsers(data: DocumentsResponse): Observable<DocumentsResponse> {
    if (!data?.documents?.length) {
      return of(data);
    }

    let userIds: number[] = [];

    for (let doc of data.documents) {
      if (doc.comments) {
        userIds = [...doc.comments.map(o => o.user_id), ...userIds];
      }

      if (doc.labels) {
        userIds = [...doc.labels?.map(o => o.user_id), ...userIds];
      }
    }

    userIds = [...new Set(userIds)];

    if (userIds.length < 1) {
      return;
    }

    const payload = {id: 'in:' + userIds.join(','), 'load_all': 1};
    return this.userService.getTeamUsers(payload)
      .pipe(
        tap(({users}) => {
          const userList = Object.fromEntries(users.map(u => [u.id, u]))
          userList[this.userService.getUser().profile.id] = this.userService.getUser().profile
          for (let doc of data.documents) {
            if (doc.comments) {
              for (const cm of doc.comments) {
                cm.user = userList[cm.user_id];
              }
            }
            if (doc.labels) {
              for (const lb of doc.labels) {
                lb.user = userList[lb.user_id];
              }
            }
          }
        }),
        map(() => data)
      );
  }

  private loadTags(data: DocumentsResponse): Observable<DocumentsResponse> {
    if (!data?.documents?.length) {
      return of(data);
    }

    return this.tagService.getTags({page_size: 250})
      .pipe(
        map(() => data)
      );
  }

  private onPageChangeDocuments($event): void {
    const {current_page} = this.pagination;

    if (current_page === $event) {
      return;
    }

    this.pagination.current_page = $event;
    this.searchDocumentsSubject.next(this.filterTerm);
  }

  private resetCurrentPage() {
    if (this.pagination) {
      this.pagination.current_page = 1;
    }
  }

  private resetCurrentPageAfterDeleting(shouldDecreaseCurrentPage = false) {
    if (this.pagination) {
      if (this.pagination.current_page > 1 && this.pagination.current_page === this.pagination.last_page && shouldDecreaseCurrentPage) {
        this.pagination.current_page--;
      }
    }
  }

  onOpenViewer(document) {
    this.storeService.docdbFamilyIdFromPatentViewer = document.general.docdb_family_id;
  }

  private scrollToPatentFromPatentViewer() {
    setTimeout(() => {
      const patentRow = document.getElementById(this.storeService.docdbFamilyIdFromPatentViewer);
      if (patentRow) {
        window.scrollTo(0, patentRow.getBoundingClientRect().top);
        this.storeService.docdbFamilyIdFromPatentViewer = null;
      }
    });
  }
  private setLegalStatuses() {
    this.documents.forEach(a => {
      this.legalStatus[this.getDocId(a)] = this.patentService.getLegalStatuses(a.document).map(ls => {
        const asDeadAndUnknown = a.document.bibliographic.legal_status.includes('invalid') && a.document.bibliographic.legal_status.includes('unknown');
        return this.patentService.getFamilyLegalStatusIcon(ls, asDeadAndUnknown);
      });
    })
  }

  
  onPatentTagsChanged(tags: TagModel[], patent: Patent) {
    if (!patent.custom_tags) {
      patent.custom_tags = [];
      }
  
      patent.custom_tags = tags;
    }
}
