<div #cardEle class="h-100 d-flex flex-column cursor-pointer" (click)="viewCollectionOrPatent()"
     appSelectableTooltip [selectableTooltipPopover]="t1"
     #t1="ngbPopover" [ngbPopover]="dropdown?.isOpen() ? null : cardTooltip" triggers="manual"
     [autoClose]="'outside'" popoverClass="popover-md">
  <ng-container *ngIf="!taggedCollection else containerTag">
    <div *ngIf="hasReadWritePermission() || userOwnCollection()" ngbDropdown class="abs"  placement="bottom-end" (click)="$event.stopPropagation()" #dropdown="ngbDropdown">
      <span aria-expanded="false" aria-haspopup="true" class="icon profile-dropdown-option" ngbDropdownToggle>
        <i class="fas fa-ellipsis-v"></i>
      </span>
      <ul class=" shadow-lg" ngbDropdownMenu>
        <li class="dropdown-item"><a (click)="viewCollectionOrPatent()" href="javascript:void(0)"><i class="fa fa-eye fa-fw"></i>Open</a></li>
        <li class="dropdown-item" *ngIf="hasReadWritePermission() && userOwnCollection()"><a (click)="moveCollection()" href="javascript:void(0)"><i class="fa fa-arrows-alt fa-fw"></i>Move</a></li>
        <li class="dropdown-item" *ngIf="hasReadWritePermission()"><a (click)="editCollection()" href="javascript:void(0)"><i class="fa fa-edit fa-fw"></i>Edit</a></li>
        <li class="dropdown-item" *ngIf="userOwnCollection()"><a (click)="duplicateCollection()" href="javascript:void(0)"><i class="fa fa-copy fa-fw"></i>Duplicate</a></li>
        <li class="dropdown-item" *ngIf="hasReadWritePermission()"><a (click)="deleteCollection()" href="javascript:void(0)" class="remove-collection"><i class="fa fa-trash fa-fw"></i>Remove</a></li>
      </ul>
    </div>
    <div class="single-card-header">
      <div class="sc-checkbox-container">
        <img [src]="isSelected ? 'assets/images/list-alt-green.gif' : 'assets/images/list-alt-orange.gif'" width="70%">
        <label class="checkbox position-absolute" *ngIf="!collaboration && userOwnCollection()" (click)="$event.stopPropagation()"
               [class.checked]="isSelected">
          <input #cb (click)="onSelectCollectionChanged(cb.checked)" [checked]="isSelected" type="checkbox"/>
          <span class="no-text">&nbsp;</span>
        </label>
      </div>
      <a href="javascript:void(0)" class="card-title ps-3 ms-4">
        <i *ngIf="isNewCollaboration()" class="fas fa-circle new-notifications"></i>
        {{cardName}}
      </a>
    </div>
  </ng-container>
  <ng-template #containerTag>
    <div class="single-card-header">

      <app-tag-item
        [tag]="taggedCollection"
        size="medium"
        (tagDeleted)="onTagDeleted($event)">
      </app-tag-item>
    </div>
  </ng-template>
  <div class="single-card-footer d-flex justify-content-between">
    <div class="bottom-left">
        <span><img alt="" src="assets/images/landscape/schedule-24px.svg">
          {{(collection?.created_at || taggedCollection?.created_at || collaboration?.shared_at) | dateFormat: 'ShortDate'}}
        </span>
    </div>

    <div class="bottom-left clt-shared">
      <span [ngbTooltip]="sharedTooltip? shareButtonTooltip : null" *ngIf="!taggedCollection" (shown)="t1.close()">
        <label class="check-on-off" *ngIf="!collaboration && this.userOwnCollection()" (click)="$event.stopPropagation(); toggleShareCollection($event);">
          <span *ngIf="haveActiveSharedLink || isCollectionShared || !hasPermission('share') else share">SHARED</span>
          <ng-template #share><span>SHARE</span></ng-template>
          <input [disabled]="isProcessing || !hasPermission('share')" type="checkbox" class=" right" [checked]="haveActiveSharedLink || isCollectionShared || !hasPermission('share')">
        </label>

        <div *ngIf="((collaboration && collaboration.shared_by) || collection.collaboration)" class="d-flex flex-row justify-content-end align-items-center" (click)="toggleShareCollection($event);">
          <div class="clt-collaboration-share-title">SHARED BY</div>
          <app-user-avatar [user]="(collaboration || collection.collaboration).shared_by" [hasSubTitle]="false" class="clt-text-avatar" [avatarSize]="20" [avatarFontSize]="10"></app-user-avatar>
        </div>
      </span>

      <div *ngIf="taggedCollection && taggedCollection.user" class="d-flex flex-row justify-content-end align-items-center">
        <div class="me-1">Created by:</div>
        <app-user-avatar [user]="taggedCollection.user" [hasSubTitle]="false" class="clt-text-avatar" [avatarSize]="20" [avatarFontSize]="10"></app-user-avatar>
      </div>

    </div>
  </div>
</div>
<ng-template #tagEditTemplate>
  <app-tag-edit [tag]="taggedCollection">
  </app-tag-edit>
</ng-template>

<ng-template #shareButtonTooltip>
  <span [innerHTML]="sharedTooltip"></span>
</ng-template>

<ng-template #cardTooltip>
  <div class="d-flex content-heading-h6 p-b-spacing-sm text-break" *ngIf="!taggedCollection && isCardTitleTruncated()">{{cardName}}</div>
  <div class="d-flex p-b-spacing-sm text-break" *ngIf="!taggedCollection && cardDescription?.length">{{cardDescription}}</div>
  <div class="d-flex justify-content-between gap-spacing-md">
    <div class="d-flex justify-content-start gap-spacing-xx-s">
      <ng-container *ngIf="isCollection">
        <span class="open-sans-bold text-orange">{{collection.results_count}}</span>
        <span class="single-card-text">PATENT{{collection.results_count === 1 ? '' : 'S' }}</span>
      </ng-container>
      <ng-container *ngIf="taggedCollection">
        <span class="open-sans-bold text-orange">{{taggedCollection.documents_count}}</span>
        <span class="single-card-text">PATENT{{ taggedCollection.documents_count === 1 ? '' : 'S' }}</span>
      </ng-container>
    </div>

    <app-user-avatars *ngIf="canShowAvatar"
                      [users]="collection.users" [groups]="collection.groups" [numberDisplayedUsers]="numberDisplayedUsers"
                      [numberDisplayedGroups]="numberDisplayedGroups" [distanceBetweenAvatars]="15" avatarsTooltipPrefix="Shared with"
                      (click)="toggleShareCollection($event)" class="cursor-pointer">
    </app-user-avatars>
  </div>
</ng-template>
