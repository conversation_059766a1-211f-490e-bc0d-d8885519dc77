import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CollectionCardComponent } from './collection-card.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { Collection } from '@core/services';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { UserTitlePipe } from '@core/pipes';
import { provideMatomo } from 'ngx-matomo-client';

describe('CollectionCardComponent', () => {
  let component: CollectionCardComponent;
  let fixture: ComponentFixture<CollectionCardComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        CollectionCardComponent
      ],
      imports: [
        ReactiveFormsModule, SharedModule, HttpClientTestingModule,
        RouterModule.forRoot([]), NgbModule
      ],
      providers: [ UserTitlePipe, provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CollectionCardComponent);
    component = fixture.componentInstance;
    component.collection = {
      id: 123,
      name: 'Test',
      description: 'Description',
      results_count: 1234
    } as Collection;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
