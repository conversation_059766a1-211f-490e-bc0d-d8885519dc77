import { Component, ElementRef, EventEmitter, Input, OnDestroy, Output, ViewChild } from '@angular/core';
import {
  AvatarService,
  Collection,
  CollectionService,
  ConfirmationDialogService,
  Folder,
  TagService,
  UserService
} from '@core/services';
import { Ngb<PERSON>ropdown, NgbModal, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { Collaboration, CollaborationPermissionEnum, CollaborationStatusEnum, PatentResource } from '@core/models';
import { CollectionFormComponent } from '@collections/collection-form/collection-form.component';
import { FoldersComponent } from '@collections/folders/folders.component';
import { TagModel } from '@core/models/tag.model';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';
import { take } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-collection-card',
  templateUrl: './collection-card.component.html',
  styleUrls: ['./collection-card.component.scss']
})
export class CollectionCardComponent implements OnDestroy {
  @Input() collection: Collection;
  @Input() taggedCollection?: TagModel;
  @Input() isSelected = false;
  @Input() collaboration: Collaboration = null;
  @Input() collectionLink: string;
  @Input() parentFolder?: Folder;
  @Output() selected = new EventEmitter<boolean>(true);
  @Output() deleted = new EventEmitter<boolean>(true);
  @Output() shareCollection = new EventEmitter<Collection>(null);

  @ViewChild('dropdown') dropdown: NgbDropdown;

  isProcessing: boolean = false;

  numberDisplayedUsers = 2;
  numberDisplayedGroups = 2;

  private subscriptions = new Subscription();

  constructor(
    private collectionService: CollectionService,
    private confirmationDialogService: ConfirmationDialogService,
    private modalService: NgbModal,
    private router: Router,
    private userService: UserService,
    public avatarService: AvatarService,
    public tagService: TagService,
    private elementRef: ElementRef
  ) {
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get haveActiveSharedLink(): boolean {
    if (this.collection && this.collection.share_code && this.collection.expires_at) {
      const now = new Date();
      const valid_to = new Date(this.collection.expires_at);
      if (now.getTime() < valid_to.getTime()) {
        return true;
      }
    }
    return false;
  }


  get patentResource(): PatentResource {
    return this.collaboration.resource as PatentResource;
  }

  get cardName(): string {
    if (this.collection) {
      return this.collection.name;
    }
    return this.patentResource.publication_number;
  }

  get cardDescription(): string {
    if (this.collection) {
      return this.collection.description;
    }
    return this.taggedCollection ? '' : this.patentResource.title;
  }

  get isCollection(): boolean {
    return !!(this.collection);
  }

  get canShowAvatar(): boolean{
    return this.hasWorkflowFeature() && this.collection && this.userOwnCollection() && this.avatarService.hasUsersAndGroups(this.collection.users, this.collection.groups)
  }

  get isCollectionShared(): boolean{
    return this.avatarService.hasUsersAndGroups(this.collection.users, this.collection.groups) || (this.collection.folder_collaboration && this.avatarService.hasUsersAndGroups(this.collection.folder_collaboration.users, this.collection.folder_collaboration.groups))
  }

  get sharedTooltip(): string{
    if (!this.parentFolder) {
      return null;
    }
    if (!this.hasPermission('share')) {
      return 'This list seems to be shared via a folder up in the hierarchy. To make changes, open that folder’s sharing settings.';
    }
    return null;
  }

  onSelectCollectionChanged(checked: boolean) {
    this.selected.emit(checked);
  }

  deleteCollection() {
    if (this.collection) {
      const title = '<i class="fas fa-trash-alt fa-2x"></i>';
      const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${this.collection.name}</span> ?</div>`;

      const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');

      modalRef.then(val => {
        if (val) {
          const deleteCollection$ = this.collectionService.deleteCollection(this.collection.id).pipe(take(1)).subscribe({
            next: data => {
              this.selected.emit(false);
              this.deleted.emit(true);
            }, error: error => {
              console.error(error);
            }
          });
          this.subscriptions.add(deleteCollection$);
        }
      }, rejected => {
      });
    }
  }

  editCollection() {
    if (this.collection) {
      this.modalService.dismissAll();
      const modal = this.modalService.open(CollectionFormComponent, {size: 'xl'});
      modal.componentInstance.collection = this.collection;
    }
  }

  duplicateCollection() {
    if (this.collection) {
      this.modalService.dismissAll();
      const modal = this.modalService.open(CollectionFormComponent, {size: 'xl'});
      modal.componentInstance.collection = this.collection;
      modal.componentInstance.isDuplicated = true;
    }
  }

  /**
   * moveCollection
   *
   * Event Listener for move collection click
   */
  moveCollection(): void {
    this.modalService.dismissAll();
    const modal = this.modalService.open(FoldersComponent, {size: 'xl'});
    modal.componentInstance.resource = this.collection;
    modal.result.then(data => {
      if (data) {
        this.selected.emit(false);
      }
    }, reason => {
    });
  }

  viewCollectionOrPatent() {
    if (this.isCollection || this.taggedCollection) {
      this.gotoCollection();
    } else {
      this.gotoPatentViewer();
    }
  }

  toggleShareCollection(event) {
    if (event !== undefined && this.hasPermission('share')) {
      event.preventDefault();
      event.stopPropagation();
      this.shareCollection.emit(this.collection);
    }
  }

  isNewCollaboration(): boolean {
    if (!this.collection.tag && this.collection.collaboration && this.collection.collaboration.resource_id === this.collection.id) {
      return this.collection.collaboration?.status === CollaborationStatusEnum.NEW;
    }
    return false;
  }

  hasWorkflowFeature() {
    return this.userService.canUseWorkflowFeature();
  }

  hasReadWritePermission(): boolean {
    return this.userOwnCollection() || this.collection?.permissions?.includes('write');
  }

  hasPermission(permission: string): boolean {
    return this.collection.permissions?.includes(permission)
  }

  userOwnCollection(): boolean {
    return this.userService.isResourceOwner(this.collection?.user_id);
  }

  isCardTitleTruncated(): boolean {
    const cardTitleEle = (this.elementRef.nativeElement as HTMLElement).querySelector<HTMLElement>('.card-title');
    if (!cardTitleEle) {
      return false;
    }
    return cardTitleEle.scrollHeight > cardTitleEle.clientHeight + 1;
  }

  private gotoCollection() {
    this.router.navigate([this.collectionLink]);
  }

  private gotoPatentViewer() {
    const patentViewerQueryParams = {
      backButtonTitle: 'Back to list',
      previousUrl: this.router.url,
      mode: PatentSideBarViewModeEnum.MODE_CONTENT
    };
    this.router.navigate(['patent/view', this.patentResource.id], {queryParams: patentViewerQueryParams});
  }

  onTagDeleted(tagId: number) {
    this.deleted.emit(true);
  }
}
