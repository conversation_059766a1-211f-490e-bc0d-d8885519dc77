<div class="modal-header">
  <div class="modal-title">
    {{getTitle()}}
  </div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>
<div class="modal-body flex-grow-1 overflow-y-auto">
  <div class="d-flex flex-column h-100">
    <app-alert type="success" [message]="successMessage" *ngIf="successMessage && !form.dirty"></app-alert>
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

    <form [formGroup]="form" (ngSubmit)="saveCollection()" class="flex-grow-1 overflow-y-auto">
      <div class="d-flex flex-column h-100 overflow-hidden">
        <div class="row" *ngIf="!addMorePatent">
          <div class="col-8">
            <div class="mb-3">
              <label for="name" class="form-label">Name*</label>
              <input [ngClass]="{'is-invalid': isFieldInvalid('name')}" autocomplete="off" class="form-control" formControlName="name" id="name"
                     maxlength="255" name="name" placeholder="Please give your list a name" required
                     type="text"/>

              <div *ngIf="form.get('name').touched && form.get('name').hasError('required')"
                   class="invalid-feedback">
                Please enter name of list
              </div>

              <div *ngIf="fieldErrors['name']" class="invalid-feedback">{{fieldErrors['name'].join(' ')}}</div>
            </div>
          </div>
          <div class="col-4">
            <div class="mb-3">
              <label for="expires_date" class="form-label">Expiration date</label>
              <div class="row">
                <div [ngClass]="form.controls.expires_date.value ? 'col-8 pe-0' : 'col-12'" class="me-0">
                  <input appMaskDate #dp="ngbDatepicker" (click)="dp.open()"
                         [ngClass]="{'is-invalid': isFieldInvalid('expires_date')}"
                         autocomplete="off" [minDate]="minExpirationDate" class="form-control" formControlName="expires_date"
                         id="expires_date" name="expires_date" ngbDatepicker type="text"/>
                  <div class="invalid-feedback">
                    Please enter valid date
                  </div>
                </div>
                <div class="col-4 ms-0 ps-2">
                  <input #expiresTime (click)="expiresTime.select()" *ngIf="form.controls.expires_date.value"
                         mask="Hh:m0" [dropSpecialCharacters]="false" [leadZeroDateTime]=true autocomplete="off" class="form-control" formControlName="expires_time"
                         id="expires_time" name="expires_time" placeholder="HH:MM" type="text"
                         [ngClass]="{'is-invalid': isFieldInvalid('expires_time')}"/>

                  <div *ngIf="form?.get('expires_time')?.errors?.expired" class="invalid-feedback">
                    Minimum hour {{ form?.get('expires_time')?.errors['expired'] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-3" *ngIf="!addMorePatent">
          <label for="description" class="form-label">Description</label>
          <textarea [ngClass]="{'is-invalid': isFieldInvalid('description')}"
                    autocomplete="off" class="form-control" formControlName="description"
                    id="description" name="description" placeholder="Description of list" rows="2"></textarea>
          <div *ngIf="fieldErrors['description']" class="invalid-feedback">{{fieldErrors['description'].join(' ')}}</div>
        </div>

        <div *ngIf="!isFromSearchResults && (!isEditing() || addMorePatent)" class="mb-3">
          <label for="patent_numbers"  class="form-label">
            Patent numbers
          </label>
          <div class="patent-list-box" [ngClass]="{'is-invalid': fieldErrors['document_ids']}">
            <app-patent-list-input [(patent_numbers)]="form.value.patent_numbers" (update)="onPatentNumbersChanged($event)"
              [(patentNumbersType)]="form.value.patentNumbersType" [storeService]="collectionStoreService">
            </app-patent-list-input>
          </div>
          <div *ngIf="fieldErrors['document_ids']" class="invalid-feedback">{{fieldErrors['document_ids'].join(' ')}}</div>
        </div>

        <div *ngIf="!isLoadingPatents else loader" class="w-100 mt-3 flex-grow-1">
          <div class="d-flex flex-column h-100">
            <div class="fw-bold" [innerHTML]="getSelectedPatentsTitle()"></div>

            <app-patent-table *ngIf="patents.length > 0"
                              [hasLinksToBooleanSearch]="false" [pagination]="pagination" [patents]="patents"
                              [showAnalytics]="false" [showAnnotatedStatus]="false"
                              [showOpenPatentIcon]="false" [hasSorting]="false"
                              pathUrl="/patent" [linkData]="linkData"
                              [openAnnotationNewTab]="true"
                              backButtonTitle="Back to list"
                              [storeService]="addPatentsStoreService"
                              [showRank]="false" class="flex-grow-1 d-block">
            </app-patent-table>

            <app-pagination *ngIf="pagination"  [pagination]="pagination" (navigatePage)="onPageChanged($event)"
                                 class="d-flex justify-content-end mt-2">
            </app-pagination>

            <app-alert type="danger" *ngIf="!isLoadingPatents && patents.length === 0 && getPatentsErrors.length > 0"
                              [message]="getPatentsErrors"></app-alert>

          </div>
        </div>
      </div>

      <input type="submit" hidden>
    </form>
  </div>
</div>

<div class="modal-footer clt-cf-footer d-flex justify-content-between">
  <div class="text-black-50"><small>Press <span class="fw-bold">Enter</span> to save</small></div>
  <div class="d-flex justify-content-end">
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
    <button (click)="activeModal.dismiss('Cancel')" class="btn btn-ghost btn-md">Cancel</button>
    <button (click)="saveCollection()" data-intercom-target="Save button" class="btn btn-primary btn-md"
            [disabled]="!form.dirty || form.invalid || addMorePatentsInvalid || isSaving">
      {{getButtonTitle()}}
    </button>
  </div>
</div>

<ng-template #loader>
  <div class="d-flex justify-content-center">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</ng-template>
