import { AfterContentChecked, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { debounceTime, finalize, take } from 'rxjs/operators';
import {
  Collection,
  CollectionService, DocumentService,
  Folder,
  PaginationMetadata, PatentListScopeEnum, PatentNumberSearchRequest, PatentNumberSearchTypeEnum,
  PatentNumberService,
  PatentQueryParams, PublicationService,
  UserService
} from '@core/services';
import { Patent } from '@core/models';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AddPatentsStoreService, CollectionStoreService } from '@core/store';
import _ from 'lodash';
import { PatentNumberTypeEnum, PluralizePipe } from '@core';

@Component({
  selector: 'app-collection-form',
  templateUrl: './collection-form.component.html',
  styleUrls: ['./collection-form.component.scss']
})
export class CollectionFormComponent implements OnInit, OnDestroy, AfterContentChecked {

  patents: Array<Patent> = [];
  pagination: PaginationMetadata;
  isLoadingPatents = false;
  form: UntypedFormGroup;
  getPatentsErrors: Array<string> = [];
  errors: string[] = [];
  isSaving = false;
  fieldErrors = {};
  _collection = {} as Collection;
  folder = {} as Folder;
  successMessage: string = null;

  isFromSearchResults = false;

  addMorePatent?: boolean = false;
  isDuplicated = false;

  minExpirationDate: any;

  private patentNumbersSubject = new BehaviorSubject<string>(null);
  private subscriptions = new Subscription();

  constructor(
    private patentNumberService: PatentNumberService,
    private collectionService: CollectionService,
    public collectionStoreService: CollectionStoreService,
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private changeDetectorRef: ChangeDetectorRef,
    public addPatentsStoreService: AddPatentsStoreService,
    private documentService: DocumentService,
    private publicationService: PublicationService,
    private pluralizePipe: PluralizePipe
  ) {
  }

  get collection(): Collection {
    return this._collection;
  }

  set collection(value: Collection) {
    this._collection = value;
    this.addPatentsStoreService.isPublications = this.isPublicationCollection;
  }

  public get linkData() {
    return this.collectionService.linkData;
  }

  get selectedPatentIds(): number[] {
    return this.addPatentsStoreService.selectedPatentIds;
  }

  get selectedPublicationNumbers(): string[] {
    return this.addPatentsStoreService.selectedPublications;
  }

  get addMorePatentsInvalid(): boolean {
    return (this.addMorePatent && this.selectedPatentIds.length === 0 && this.selectedPublicationNumbers.length === 0);
  }

  get isPublicationCollection(): boolean {
    return this.collection?.collection_type === PatentListScopeEnum.PUBLICATION;
  }

  get isNewCollection(): boolean {
    return !this.collection?.id && !this.addMorePatent;
  }

  ngOnInit() {
    const today = new Date();
    this.minExpirationDate = {year: today.getFullYear(), month: today.getMonth() + 1, day: today.getDate()}

    this.buildForm();
    this.getPatents();
  }

  ngAfterContentChecked(): void {
    this.changeDetectorRef.detectChanges();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.addPatentsStoreService.selectedPatentIds = [];
    this.addPatentsStoreService.selectedPublications = [];
  }

  onPatentNumbersChanged(value: {patent_numbers : string, patentNumbersType: string}) {
    this.patentNumbersSubject.next(value.patent_numbers);
    this.form.get('patent_numbers').setValue(value.patent_numbers);
    this.form.get('patentNumbersType').setValue(value.patentNumbersType);
    this.addPatentsStoreService.isPublications = value.patentNumbersType === PatentNumberTypeEnum.APPLICATION_NUMBER || this.isPublicationCollection;
    this.form.markAsDirty();
  }

  clearGetPatentsErrors() {
    this.getPatentsErrors = [];
  }

  saveCollection() {
    this.clearErrors();

    if (this.validateForm()) {
      this.isSaving = true;
      this.form.disable();

      const getActionButton$ = this.getActionButtonObservable()
        .pipe(
          take(1),
          finalize(() => {
            this.isSaving = false;
            this.addPatentsStoreService.selectedPatentIds = [];
            this.addPatentsStoreService.selectedPublications = [];
          })
        )
        .subscribe({
          next: (data) => {
            this.collectionStoreService.setRefreshCollections(true);
            this.activeModal.close(data);
          },
          error: ({error}) => {
            console.error(error);
            this.form.enable();
            this.errors = [error.message];
            this.fieldErrors = error.details || {};
          }
        });
      this.subscriptions.add(getActionButton$);
    } else {
      this.form.markAllAsTouched();
    }
  }

  onPageChanged(page: number) {
    this.pagination.current_page = page;

    if (!this.isEditing() || this.addMorePatent) {
      this.callGetPatentsApi(this.form.get('patent_numbers').value);
    }
  }

  isEditing(): boolean {
    return this.collection && !!(this.collection.id);
  }

  getSelectedPatentsTitle() {
    if (!this.pagination) {
      return '';
    }

    if (this.pagination.total_hits === 0) {
      return 'No documents were found';
    }

    const subject = this.isPublicationCollection ? 'publication' : 'patent family';

    const totalHits = this.pagination.total_hits;
    const totalResultsTitle = `<code>${totalHits}</code> ${this.pluralizePipe.transform(subject, totalHits)} ${this.pluralizePipe.transform('have', totalHits)} been found`;

    const noSelectedPatents = this.isPublicationCollection ? this.selectedPublicationNumbers.length : this.selectedPatentIds.length;
    let selectedPatentsTitle;

    if (noSelectedPatents === 0) {
      selectedPatentsTitle = `You have not selected any ${this.pluralizePipe.transform(subject, 2)} yet`;
    } else {
      selectedPatentsTitle = `You have selected <code>${noSelectedPatents}</code> ${this.pluralizePipe.transform(subject, noSelectedPatents)}`;
    }

    return `${totalResultsTitle}. ${selectedPatentsTitle}.`;
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return this.fieldErrors[name] || (field && field.touched && field.invalid);
  }

  getTitle(): string {
    if (this.isFromSearchResults) {
      const countTitle = this.selectedPatentIds.length > 0 ?
        this.selectedPatentIds.length + (this.selectedPatentIds.length > 1 ? ' patents' : ' patent') : 'all patents';
      const prefix = this.selectedPatentIds.length === 1 ? 'this' : 'these';
      const suffix = this.folder && this.folder.id ? ` into folder *${this.folder.name}*` : '';
      return `Create new list for ${prefix} ${countTitle}${suffix}`;
    } else {
      if (this.addMorePatent) {
        return `Add more patents to list *${this.collection.name}*`;
      } else {
        if (this.isDuplicated) {
          return `Duplicate list from *${this.collection.name}*`
        }

        return this.isEditing() ? `Edit list *${this.collection.name}*` :
          `Create new list${this.folder && this.folder.id ? ` inside folder *${this.folder.name}*` : ''}`;
      }
    }
  }

  getButtonTitle(): string {
    switch (true) {
      case this.addMorePatent:
        return 'Add';
      case this.isDuplicated:
        return 'Duplicate';
      default:
        return 'Save';
    }
  }

  private getActionButtonObservable(): Observable<any> {
    if (this.addMorePatent) {
      return this.collectionService.updateDocuments(this.collection.id, this.buildDocumentsPayload());
    }

    const payload = this.buildCollectionPayload();

    if (this.isDuplicated) {
      return this.collectionService.cloneCollection(this.collection.id, payload);
    }

    if (this.isEditing()) {
      return this.collectionService.updateCollection(this.collection.id, payload);
    }

    return this.collectionService.createCollection(payload);
  }

  private clearSelectedPatents() {
    this.addPatentsStoreService.selectedPatentIds = [];
    this.addPatentsStoreService.selectedPublications = [];
  }

  private clearErrors() {
    this.errors = [];
    this.fieldErrors = {};
  }

  private validateForm(): boolean {
    return this.form.valid;
  }

  private buildCollectionPayload(): Collection {
    const data = this.form.value;

    const payload = {
      name: data.name
    } as Collection;

    if (data.description) {
      payload.description = data.description;
    }

    if (data.expires_date) {
      const month = data.expires_date.month.toString().padStart(2, '0');
      const day = data.expires_date.day.toString().padStart(2, '0');
      const time = data.expires_time ? data.expires_time.replace(/_/g, '0') : '00:00';
      payload.expires_at = new Date(`${data.expires_date.year}-${month}-${day}T${time}:00`).toISOString();
    }

    if (!this.isEditing()) {
      if (this.isPublicationCollection || this.addPatentsStoreService.isPublications) {
        if (this.selectedPublicationNumbers.length > 0) {
          payload.publication_numbers = this.selectedPublicationNumbers;
          payload.collection_type = PatentListScopeEnum.PUBLICATION;
        }
      } else {
        if (this.selectedPatentIds.length > 0) {
          payload.document_ids = this.selectedPatentIds.map(x => x.toString());
        }
      }

      if (this.folder && this.folder.id) {
        payload.folder_id = this.folder.id;
      }
    }

    return payload;
  }

  /**
   * buildDocumentsPayload
   */
  private buildDocumentsPayload() {
    const results = {add: {}};

    if (this.isPublicationCollection) {
      results.add['publication_numbers'] = this.selectedPublicationNumbers;
    } else {
      results.add['document_ids'] = this.selectedPatentIds;
    }

    return results;
  }

  private clearPatentsData() {
    this.pagination = null;
    this.patents = [];
  }

  private buildForm() {
    const expiresDateTime = this.collection?.expires_at ? new Date(this.collection.expires_at + 'Z') : null;
    const expiresDateStruct = expiresDateTime ? {
      year: expiresDateTime.getFullYear(),
      month: expiresDateTime.getMonth() + 1,
      day: expiresDateTime.getDate()
    } : null;
    const expiresTime =  expiresDateTime ? expiresDateTime.getHours().toString().padStart(2, '0') + ':' +
                                           expiresDateTime.getMinutes().toString().padStart(2, '0') : '00:00';

    this.form = new UntypedFormGroup({
      name: new UntypedFormControl(this.collection.name, [
        Validators.required, Validators.maxLength(255)
      ]),
      description: new UntypedFormControl(this.collection.description, [Validators.maxLength(255)]),
      expires_date: new UntypedFormControl(expiresDateStruct),
      patentNumbersType: new UntypedFormControl('mixed'),
      expires_time: new UntypedFormControl(expiresTime, [this.validateTime()])
    });

    if (!this.isFromSearchResults) {
      if (this.isEditing()) {
        if (this.addMorePatent) {
          this.form.addControl('patent_numbers', new UntypedFormControl('', [Validators.required]));
        }
      } else {
        this.form.addControl('patent_numbers', new UntypedFormControl(''));
      }
    }

    if (this.isDuplicated) {
      this.form.markAsDirty();
    }

    const expiresDateChanges$ = this.form.get('expires_date').valueChanges.subscribe({
      next: _ => {
        setTimeout(() => {
          this.form?.get('expires_time').updateValueAndValidity();
        }, 100);
      }
    });
    this.subscriptions.add(expiresDateChanges$);
  }

  private getPatents() {
    if (!this.addMorePatent && (this.isFromSearchResults || this.isEditing())) {
      return;
    }

    const patentNumbers$ = this.patentNumbersSubject.asObservable()
      .pipe(debounceTime(500))
      .subscribe({
        next: val => {
          this.addPatentsStoreService.selectedPatentIds = [];
          this.addPatentsStoreService.selectedPublications = [];
          this.clearPatentsData();
          this.clearGetPatentsErrors();
          this.clearSelectedPatents();
          this.callGetPatentsApi(val);
        }
      });
    this.subscriptions.add(patentNumbers$);
  }

  private callGetPatentsApi(patentNumbersVal: string) {
    if (!patentNumbersVal || this.isLoadingPatents) {
      return;
    }

    const patentNumbers = patentNumbersVal.split(/[,;\n\r\s]/gi)
      .filter(o => o && o.trim().length > 0)
      .map(o => o.trim());

    if (patentNumbers.length === 0) {
      return;
    }

    this.getPatentsErrors = [];

    if (patentNumbers.length > 10000) {
      this.getPatentsErrors = ['You can only search up to 10000 patent numbers'];
      return;
    }

    this.isLoadingPatents = true;
    const page = this.pagination ? this.pagination.current_page : 1;

    this.clearPatentsData();
    this.addPatentsStoreService.selectedPatentIds = [];
    this.addPatentsStoreService.selectedPublications = [];

    const params = {page} as PatentQueryParams;
    const payload = {
      patent_numbers: patentNumbers,
      patent_numbers_type: this.form.value.patentNumbersType,
      search_type: this.isPublicationCollection ? PatentNumberSearchTypeEnum.PUBLICATION : PatentNumberSearchTypeEnum.FAMILY,
      skip_invalid: 1
    } as PatentNumberSearchRequest;

    const getPatents$ = this.patentNumberService.getPatents(payload, params)
      .pipe(
        take(1),
        finalize(() => {
          this.isLoadingPatents = false;
        })
      )
      .subscribe({
        next: ({data}) => {
          if (this.isPublicationCollection) {
            data.publications.forEach((p, index) => {
              p.general.publication_number = p.general.original_number_normalized || p.general.original_number;
            });
            this.patents = this.publicationService.publicationsToDocuments(data.publications);
          } else {
            this.patents = data.documents.map((o, index) => {
              o.general.raw_publication_number = o.general.original_number;
              return o;
            });
          }
          this.pagination = data.page;
          this.addPatentsStoreService.searchHash = data.search_info.search_hash;
          this.addPatentsStoreService.selectAllDocuments = true;
        },
        error: ({error}) => {
          this.processGetPatentsError(error);
        }
      });
    this.subscriptions.add(getPatents$);
  }

  private processGetPatentsError(error) {
    console.error(error);
    const details = error.details || {};
    const titles = {
      patent_numbers: 'Patent numbers',
      patent_numbers_type: 'Patent number type'
    };

    if (Object.keys(details).length > 0) {
      let errorMessages = 'Some errors occurred when submitting the patent numbers. Please review and try again.<br/><ul class="ps-3">';
      errorMessages += Object.keys(details).map(field => {
        let msg = details[field];
        if (!_.isString(msg)) {
          msg = (_.isArray(msg) ? msg : Object.values(msg)).map(m => `<ul><li>${m}</li></ul>`).join('');
        }
        return `<li>${titles[field] || field}:</li>` + msg;
      }).join('');
      errorMessages += '</ul>';
      this.getPatentsErrors = [errorMessages];
    } else {
      this.getPatentsErrors = ['You have entered some invalid patent numbers. Please check again.'];
    }
  }

  private validateTime(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      if (_.isEqual(this.form?.value?.expires_date, this.minExpirationDate)) {
        const expiresTime = control.value.split(':');
        const currentTime =  new Date();
        if (parseInt(expiresTime[0], 10) <= currentTime.getHours()) {
          if (!control.touched) {
            control.markAsTouched({onlySelf: true});
          }
          return {expired: (currentTime.getHours() + 1).toString().padStart(2, '0') + ':00'};
        }
      }
      return null;
    };
  }

}
