@import 'scss/layout2021/variables';
@import 'scss/components/right-side-bar';
@import 'scss/figma2023/variables';
@import 'scss/figma2023/mixins';
@import "scss/components/tag_item";

.btn-360{
  min-width: 360px;
}

.bg-filter{
  background-color: $chart-section-background;
}

.clt-patents-title{
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.clt-input-desc{
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  max-width: 380px;
  &.show-all{
    max-width: unset;
    display: block;
  }
}

.collection-content-container {
  .sidebar {
    position: absolute;
    top: 0;
    height: 100%;
    right: 0;
    bottom: 0;
    display: block;

    &-manu {
      position: sticky;
      top: 0;
      right: 0;
      width: 50px;
      height: 100vh;
      z-index: 1022;
      background: #FFFFFF 0% 0% no-repeat padding-box;
      box-shadow: 0px 3px 12px #00000029;
      opacity: 0.9;
    }

    .btn {
      &:hover {
        background-color: $brand-green-hover;
      }
      &.active {
        background-color: $brand-green-pressed;
      }
      [class^='btn-'] {
        width: 1.6rem;
        height: 1.6rem;
      }
      .btn-task {
        background-image:  url(/assets/images/tool-icon-task-default.svg) !important;
      }
      &:hover, &.active {
        .btn-task {
          background-image: url(/assets/images/tool-icon-task-white.svg) !important;
        }
      }
    }
  }

  &.collection-show-tasks {
    display: inline-block;
    position: relative;
    width: 100%;
    transition: padding ease-in-out .5s;
    padding-right: 50px;
  }

  .right-side-container {
    width: 360px !important;
    max-width: 360px !important;
    min-width: 360px !important;
  }
}

.tools-bar {
  background-color: transparent;
}

.sticky-top {
  background-color: #FFF;
}

::ng-deep {
  .collection-content-container {
    &.collection-show-tasks {
      .publication-table {
        overflow: unset !important;
      }

      .combined-mode-layout {
        max-width: unset;
      }
    }
  }
}

// 2024 layout css
.collection {
  &-info {
    display: flex;
    padding: $spacing-system-spacing-xxx-big;
    background-color: $colours-background-bg-secondary;
    &-left {
      width: 50%;
    }
    &-right {
      width: 50%;
    }
    .history-container{
      padding: $spacing-system-spacing-md;
      background-color: $colours-background-bg-primary;
      border-radius: $radius-big;
    }
  }
}

.history{
  &-header{
    border-bottom: 1px solid $colours-border-subtle;
  }
  &-table{
    max-height: 324px;
    overflow: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    &-icon{
      position: relative;
      margin-right: $spacing-system-spacing-big;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      &::before{
        content: "";
        width: 8px;
        height: 8px;
        background-color: $colours-border-subtle;
        border-radius: $radius-xx-huge;
        z-index: 5;
      }
      &::after{
        content: "";
        height: 100%;
        border-left: 1px solid $colours-border-subtle;
        position: absolute;
        left: 47%;
        top: 0;
      }
      &.last-row::after{
        height: 48%;
      }
      &.first-row::after{
        height: 48%;
        top:unset;
        bottom: 0;
      }
    }
  }
  &-row{
    display: flex;
    border-radius: $radius-sm;
    padding: 0 $spacing-system-spacing-xx-big;
    border-left: $radius-sm solid transparent;
    &:hover{
      cursor: pointer;
      background-color: $colours-background-bg-secondary;
      .history-table-icon::before{
        width: 24px;
        height: 24px;
        background-color: white;
        border: 2px solid $colours-border-subtle;
      }
    }
    &.not-clickable{
      cursor: not-allowed;
      pointer-events: none;
    }
    &.active-row{
      background-color: $colours-background-bg-brand;
      border-left-color: $colours-content-content-active;
      .history-table-icon::before{
        width: unset;
        height: unset;
        background-color: unset;
        border: unset;
        font-family: "Font Awesome 6 Pro";
        box-sizing: border-box;
        font-size: 24px;
        font-weight: 400;
        color: $colours-content-content-active;
        content: "\f192";
      }
    }
  }
  &-user-avatar {
    display: flex;
    align-items: center;
  }
  &-time{
    margin-top: $spacing-system-spacing-xxx-s;
  }
}
.collection-info{
  &-table, &-row, &-label, &-value{
    display: flex;
  }
  &-table{
    flex-direction: column;
  }
  &-row{
    margin-top: $spacing-system-spacing-x-s;
  }
  &-label{
    min-width: 182px;
    align-items: center;
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-xx-s;
    margin-right: $spacing-system-spacing-big;
    color: $colours-content-content-tertiary;
    @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
  }
  &-value{
    @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-sm;
    color: $colours-content-content-secondary;
    align-items: center;
    word-break: break-all;
    &-disabled{
      color: $colours-content-content-disabled;
      @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
    }
  }
  &-input{
    outline: none;
    resize: none;
    overflow: hidden;
    width: 100%;
    border-radius: $radius-sm;
    border: 1px solid $colours-border-contrast;
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-sm;
    min-height: $spacing-system-spacing-xxx-big;
    gap: $spacing-system-spacing-xx-s;
    background-color: $colours-background-bg-primary;
    @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
    color: $colours-content-content-secondary;
    &:focus, &:focus-visible, &:focus-within{
      border: 1px solid $colours-border-contrast !important;
    }
  }
}
:host::ng-deep{
  .ta-user{
    width: 1.5rem !important;
    .ta-avatar{
      width: 1.5rem !important;
      height: 1.5rem !important;
      font-size: .875rem !important;
    }
  }
}
