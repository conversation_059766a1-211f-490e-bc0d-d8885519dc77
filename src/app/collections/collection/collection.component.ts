import { Component, ElementRef, HostListener, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { filter, finalize, take } from 'rxjs/operators';
import { columnsToShow, publicationColumnsToShow, selectedColumnsCombinedMode, publicationColumnsToShowTagged, columnsToShowTagged } from './columns';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PatentTableComponent } from '@shared/components';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';
import {
  AdvancedFilterService,
  CollaborationService,
  Collection,
  CollectionService,
  ConfirmationDialogService,
  MonitorService,
  PaginationMetadata, PatentListScopeEnum, PatentResultsResponse, PublicationService,
  SearchHistoryService,
  SearchQueryParams,
  SemanticSearchService,
  TaskService,
  UserService
} from '@core/services';
import {
  Collaboration,
  CollaborationPermissionEnum,
  CollaborationResourceTypeEnum,
  CollaborationStatusEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TaskStatusEnum,
  TeamUser,
} from '@core/models';
import { CollectionFormComponent } from '@collections/collection-form/collection-form.component';
import { CollectionStoreService, SortParams } from '@core/store';
import { ProfileDialogComponent } from '@collections/profile-dialog/profile-dialog.component';
import {
  ApplicantsAliasesService,
  AvatarService,
  ChartsService,
  Folder,
  NotificationsService,
  PatentTableService,
  SourceFilterOption,
  TagService
} from '@core';
import { TagModel } from '@core/models/tag.model';
import { ViewModeTypeEnum } from '@search/patent/types';
import { ActionType } from '@shared/charts/chart-dashboard/types';
import { CollectionsCategory, showIntermediateFolders } from '@collections/shared/types';

enum CollectionSourceTypeEnum {
  MONITOR = 'monitor',
  BOOLEAN = 'boolean',
  SEMANTIC = 'semantic',
  UNKNOWN = 'unknown',
  MANUAL = 'manual'
}

@Component({
  selector: 'app-collection',
  templateUrl: './collection.component.html',
  styleUrls: ['./collection.component.scss']
})
export class CollectionComponent implements OnInit, OnDestroy {
  conversionInProcess = false;
  hasLinksToBooleanSearch = true;
  original_pagination: PaginationMetadata;
  sorting: SortParams = {field: null, order: null} as SortParams;
  isLoadingDocuments = false;
  showFilters = false;
  collectionNotFound = false;
  collaboration: Collaboration;
  taskResourceTypeEnum = TaskResourceTypeEnum;
  sharedType: 'collection' | 'link' | '' = '';
  savedTaskMessage: string[];
  showTasks = false;
  showAllInput = false;
  currentTask: TaskModel = null;
  expandSidebar = false;
  customTag: TagModel;
  isFiltering = false;
  historySortOption = ['Recently added', 'Oldest added']
  historySort: string;
  showHistory = true;
  saveSearchHeadline:string = 'Share your collection';
  parentFolder: Folder;

  processSources: boolean;
  collectionCollaborators: TeamUser[];
  collectionSourceUsers: { [k: string]: TeamUser; };
  collectionSourceOption: SourceFilterOption[];

  collectionSourceTypeEnum = CollectionSourceTypeEnum;
  descriptionText: string;
  editingDescription = false;
  isSaving = false;

  @ViewChild('patentTable') patentTable: PatentTableComponent;
  @ViewChild('description') descriptionTextarea: ElementRef;

  private subscriptions = new Subscription();
  private enableTaskFeature: boolean = false;
  private isBackFromPatentViewer: boolean = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public userService: UserService,
    private modalService: NgbModal,
    private chartService: ChartsService,
    public collectionStoreService: CollectionStoreService,
    private collaborationService: CollaborationService,
    private confirmationDialogService: ConfirmationDialogService,
    public collectionService: CollectionService,
    private searchHistoryService: SearchHistoryService,
    private monitorService: MonitorService,
    private semanticService: SemanticSearchService,
    public avatarService: AvatarService,
    private advancedFilterService: AdvancedFilterService,
    private taskService: TaskService,
    private notificationsService: NotificationsService,
    private applicantsAliasesService: ApplicantsAliasesService,
    public tagService: TagService,
    private publicationService: PublicationService,
    private patentTableService: PatentTableService
  ) {
    this.hasLinksToBooleanSearch = this.userService.isNotExternalUser();
  }

  get linkData() {
    return this.collectionService.linkData;
  }

  get isLoadingCollection() {
    return this.collectionStoreService.collectionsLoading;
  }

  set isLoadingCollection(state) {
    this.collectionStoreService.collectionsLoading = state;
  }

  get activePage() {
    return this.collectionStoreService.collectionActivePage;
  }

  set activePage(state) {
    this.collectionStoreService.collectionActivePage = state;
  }

  get collection(): Collection {
    return this.collectionStoreService.collection;
  }

  set collection(collection) {
    this.collectionStoreService.collection = collection;
  }

  get documents() {
    return this.collectionStoreService.collectionDocuments;
  }

  set documents(documents) {
    this.collectionStoreService.collectionDocuments = documents;
  }

  get isPatentSelected(): boolean {
    return this.collectionStoreService.selectedPatentIds.length > 0;
  }

  get totalSelectedPatents(): number {
    return this.collectionStoreService.selectedPatentIds.length;
  }

  get isShared(): boolean {
    return this.collection?.users?.length > 0 || this.collection?.groups?.length > 0 || this.hasTemporaryLink as boolean ||
    (this.collection.folder_collaboration && (this.collection.folder_collaboration.users?.length > 0 || this.collection.folder_collaboration.groups?.length > 0));
  }

  get shareSuccessMessage(): string {
    return this.collectionStoreService.getSaveToCollectionSuccess();
  }

  get hasTemporaryLink(): boolean {
    if (this.collection && this.collection.share_code && this.collection.expires_at) {
      const now = new Date();
      const valid_to = new Date(this.collection.expires_at);
      if (now.getTime() < valid_to.getTime()) {
        return true;
      }
    }
    return false;
  }

  get sortBySimilarity() {
    return this.collectionStoreService.sortBySimilarity;
  }

  get noDocumentMsg(): string {
    if (this.sortBySimilarity) {
      return 'No documents returned from sort by similarity search';
    }

    if (this.isTasksMode && this.showTasks) {
      return 'Please select a task to view its documents';
    }

    return 'No documents in this list.';
  }

  get paramCollectionId(): number {
    return +this.activatedRoute.snapshot.params.collection_id;
  }

  get isTasksMode(): boolean {
    if (!this.enableTaskFeature) {
      return false;
    }
    return this.activatedRoute.snapshot.queryParams.mode === 'tasks';
  }

  get searchHashForDocuments(): string {
    return this.collectionStoreService.searchHash;
  }

  get isTagCollection(): boolean {
    return !!this.collection.tag;
  }

  private get queryTaskId(): number {
    if (!this.enableTaskFeature) {
      return null;
    }
    return +this.activatedRoute.snapshot.queryParams.task_id;
  }

  get haveDocuments(): boolean{
    return this.documents && this.documents.length > 0;
  }

  get viewMode (): ViewModeTypeEnum{
    return this.collectionStoreService.patentListViewMode
  }
  get isListVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }
  get isChartVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get isCombinedMode(): boolean{
    return this.collectionStoreService.isCombinedMode;
  }

  get isAnalysisMode(): boolean{
    return this.collectionStoreService.isAnalysisMode;
  }

  get selectedColumnsCombinedMode(){
    if(this.showTasks && this.expandSidebar){
      return [];
    }
    return selectedColumnsCombinedMode;
  }

  get isPublicationsScope(): boolean {
    return this.collection?.collection_type === PatentListScopeEnum.PUBLICATION;
  }

  get hasFilters(): boolean {
    return this.collectionStoreService.getAppliedFiltersQuery()?.length > 0 || this.filteredSources.length >0 ||
    this.collectionStoreService.getAppliedCollectionSourceTypeFilter()?.length>0 || this.collectionStoreService.getAppliedCollectionSourceUserFilter()?.length>0;
  }

  get columnsToShow() {
    if(this.isTagCollection){
      return this.isPublicationsScope ? publicationColumnsToShowTagged : columnsToShowTagged;
    } else {
      return this.isPublicationsScope ? publicationColumnsToShow : columnsToShow;
    }
  }

  get canManageTag(): boolean {
    return this.userService.isNotExternalUser();
  }

  get filteredSources(): any[] {
    return this.collectionStoreService.filteredSources;
  }

  set filteredSources(val: any[]) {
    this.collectionStoreService.filteredSources = val;
  }

  get pageSize(): number {
    return this.collectionStoreService.pageSize;
  }

  set pageSize(value: number) {
    this.collectionStoreService.pageSize = value;
  }

  get pagination(): PaginationMetadata {
    return this.collectionStoreService.pagination || {} as PaginationMetadata;
  }

  set pagination(value: PaginationMetadata) {
    this.collectionStoreService.pagination = value;
  }

  @HostListener('window:scroll', ['$event'])
  onScroll($event: any) {
    const pageTop = $(window).scrollTop();
    const rightSideContainer = $('.collection-content-container .right-side-container');

    if (pageTop > 96) {
      rightSideContainer.addClass('was-sticky');
    } else {
      rightSideContainer.removeClass('was-sticky');
    }
  }

  ngOnInit() {
    if (this.collectionStoreService.backPatentSearch) {
      this.setFieldsAfterBack();
      this.collectionStoreService.backPatentSearch = false;
      this.isBackFromPatentViewer = true;
    } else {
      this.clearStoredValues();

      this.collectionStoreService.selectedColumnsToShow = [];
      this.collectionStoreService.setFilters([], false);
      this.collectionStoreService.selectedPublications = [];
      this.collectionStoreService.patentListViewMode = ViewModeTypeEnum.LIST;
    }

    this.initializeByCollectionId();
    this.initializeByShareCode();

    const filters$ = this.collectionStoreService.filters$.subscribe({
      next: filters => {
        this.showFilters = filters.length > 0;
        if (this.collectionStoreService.searchHash) {
          if (!this.isBackFromPatentViewer) {
            this.activePage = 1;
            this.isBackFromPatentViewer = false;
          }
          this.isFiltering = true;
          this.getDocuments(this.queryTaskId);
        }
      }
    });
    this.subscriptions.add(filters$);

    const removeFilter$ = this.collectionStoreService.filterRemoved$.subscribe({
      next: (filter) => {
        if (filter['collectionSource'] === 'sourceUser') {
          for (const [k, u] of Object.entries(this.collectionSourceUsers)) { u['isSelected'] = false; }
          this.collectionStoreService.setFilters(this.collectionStoreService.filters.filter((item) => item.collectionSource !== filter['collectionSource']));
        } else if (filter['collectionSource'] === 'sourceType') {
          this.collectionSourceOption.forEach(option => option.selected = false);
          this.collectionStoreService.setFilters(this.collectionStoreService.filters.filter((item) => item.collectionSource !== filter['collectionSource']));
        }
      }
    });
    this.subscriptions.add(removeFilter$);

    const queryParams$ = this.activatedRoute.queryParams.subscribe({
      next: (value) => {
        if (history.state.collectionId) {
          this.initializeByCollectionId(history.state.collectionId);
        }
        this.getDocuments(+value.task_id);
      }
    });
    this.subscriptions.add(queryParams$);

    const searchHash$ = this.collectionStoreService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);

    const changeApplicantsEvent$ = this.applicantsAliasesService.changeApplicantsEvent.subscribe({
      next: (res) => {
        if (this.collection) {
          this.activePage = 1;
          this.getDocuments(this.queryTaskId);
        }
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);

    const chartDashboardAction$ = this.collectionStoreService.chartDashboardAction$.subscribe({
      next: msg => {
        this.collectionStoreService.customChartCategories = this.userService.getChartCategories(this.collectionStoreService.chartDashboardType);

        if (msg.action === ActionType.Add) {
          this.collectionStoreService.searchHash = this.collectionStoreService.searchHash;
        }
      }
    });
    this.subscriptions.add(chartDashboardAction$);

    const tagChanged$ = this.tagService.tagChanged$
      .pipe(
        filter(tag => !!tag?.id && tag.id === this.customTag?.id),
      )
      .subscribe({
        next: (tag) => {
          this.customTag = {...this.customTag, ...tag};
        }
      });
    this.subscriptions.add(tagChanged$);

    if (this.activatedRoute.snapshot.queryParams.task_id && this.enableTaskFeature) {
      this.expandSidebar = true;
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.collectionStoreService.setFilters([], false);
    this.chartService.resetCharts();
    this.collectionStoreService.purgeCollection();
    this.patentTableService.selectedLegalStatus = [];
  }
  private collectionReset(){
    this.collection = null;
    this.original_pagination = null;
    this.historySort = null;
  }

  private buildChartPayload() {
    const payload = {
      charts: this.collectionStoreService.getChartActiveNames(),
      'parameters': {}
    };

    const freeTextQuery = this.collectionStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }
    return payload;
  }

  /**
   * get chart data source form the Microservice
   */
   private getCharts() {
    if(!this.collectionStoreService.getActiveDefaultCategoryCharts()){
      return
    }
    this.collectionStoreService.isCalculatingCharts = true;
    const payload = this.buildChartPayload();

    const calculate$ = this.chartService.calculate(payload, this.collectionStoreService.searchHash)
      .pipe(
        take(1),
        finalize(() => this.collectionStoreService.isCalculatingCharts = false)
      )
      .subscribe({
        next: ({charts}) => {},
        error: (err) => {
          console.log(err);
          throw err;
        }
      });
    this.subscriptions.add(calculate$);
  }

  backToCollections() {
    const category = (this.activatedRoute.snapshot.params.category as CollectionsCategory) ?? CollectionsCategory.DEFAULT;
    const folderId = this.activatedRoute.snapshot.params.folder_id;
    const commands = ['collections'];
    if (folderId) {
      commands.push(category);
      commands.push(folderId);
    } else {
      if (showIntermediateFolders(category)) {
        commands.push(category);
      }
    }
    this.router.navigate(commands);
  }

  /**
   * onChangePageSize
   *
   * Event listener for page size component
   * @param pageSize patent table size
   */
  onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.activePage = 1;
    this.getDocuments(this.queryTaskId);
  }

  navigate(page: number) {
    this.activePage = page;
    this.getDocuments(this.queryTaskId);
  }

  editPatentsInCollection() {
    if (this.isPatentSelected && this.hasReadWritePermission()) {
      const subject = this.totalSelectedPatents > 1 ? 'patents' : 'patent';
      const title = '<i class="fas fa-trash-alt fa-2x"></i>';
      const message = `<div class="text-center">Are you sure you want to <br/><b>remove ${this.totalSelectedPatents} selected ${subject}</b><br/> from this list ?</div>`;

      this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg').then(
        confirmation => {
          if (confirmation) {
            this.isLoadingCollection = true;
            const payload = {
              remove: {}
            };

            if (this.collection.collection_type === PatentListScopeEnum.PUBLICATION) {
              payload.remove['publication_numbers'] = this.collectionStoreService.selectedPublications;
            } else {
              payload.remove['document_ids'] = this.collectionStoreService.selectedPatentIds;
            }

            const updateDocuments$ = this.collectionService.updateDocuments(this.collection.id, payload)
              .pipe(
                take(1),
                finalize(() => this.isLoadingCollection = false)
              )
              .subscribe({
                next: (data) => {
                  this.initializeByCollectionId();
                  this.getDocuments(this.queryTaskId);
                  this.collectionStoreService.selectedPatentIds = [];
                  this.collectionStoreService.selectedPublications = [];
                }
              });
            this.subscriptions.add(updateDocuments$);
          }
        }
      ).catch(error => {
      });
    } else {
      this.collectionStoreService.selectedPatentIds = [];
      this.collectionStoreService.selectedPublications = [];
      this.modalService.dismissAll();
      const modal = this.modalService.open(CollectionFormComponent, {size: 'xl'});
      modal.componentInstance.collection = this.collection;
      modal.componentInstance.addMorePatent = true;
      modal.result.then(() => {
        this.initializeByCollectionId();
        this.getDocuments(this.queryTaskId);
      }, () => {
        this.getDocuments(this.queryTaskId);
      });
    }
  }

  collectionSearchSources(){
    return this.collection?.collection_sources?.filter(s => !!s.search_history_id);
  }

  collectionMonitorSources(){
    return this.collection?.collection_sources?.filter(s => !!s.monitor_run_id);
  }

  isFromSearch(): boolean {
    return this.collectionSearchSources().length > 0;
  }

  isViewByShareCode(): boolean {
    return !!(this.activatedRoute.snapshot.params.share_code);
  }

  userOwnCollection(): boolean {
    return this.collection?.user_id === this.userService.getUser()?.profile?.id;
  }

  /**
   * save
   *
   * Event listener for opening create link component
   */
  onSharePatents(event) {
    this.loadCollaboration();
    setTimeout(() => {
      document.getElementById('collection').focus();
    });
  }

  /**
   * closeSave
   *
   * Event listener for closing create link component
   */
  public onCloseShare(event) {
    this.loadCollaboration();
  }

  /**
   * onCollectionConversion
   * @param type conversion Type as LANDSCAPE or MONITOR
   */
  onCollectionConversion(type: string) {
    if (!this.userService.hasFeature(type)) {
      this.confirmationDialogService.alert(`Collection conversion to *${type}*`,
        this.getFeatureText(type));
      return;
    }
    if (type === 'LANDSCAPE') {
      const modal = this.modalService.open(ProfileDialogComponent, {size: 'md'});
      modal.result.then((data) => {
        this.doCollectionConversion(type, data);
      }, reason => {
        console.log(reason);
      });
    } else {
      this.doCollectionConversion(type);
    }
  }

  onSortPatents(val: SortParams) {
    this.sorting = val;
    this.getDocuments(this.queryTaskId);
  }

  onFilterRemoved(filter) { }

  hasReadWritePermission(): boolean {
    return (this.userOwnCollection() || this.collection.permissions?.includes('write'));
  }

  canAnalyzeList(): boolean {
    return this.userOwnCollection() || !!(this.collaboration?.permission);
  }

  hasWorkflowFeature() {
    return this.userService.canUseWorkflowFeature();
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.getDocuments(this.queryTaskId);
    }
  }

  onSortBySimilarity(event) {
    if (event) {
      this.sorting = this.collectionStoreService.patentTableSort;
    } else {
      this.collectionStoreService.patentTableSort = {} as SortParams;
      this.sorting = {} as SortParams;
    }
    this.activePage = 1;
    this.collectionStoreService.setSortBySimilarity(event);
    this.getDocuments(this.queryTaskId);
  }

 private getSimilarityResults() {
    const payload = this.collectionStoreService.getSortBySimilarityPayload(this.collectionStoreService.getAppliedFiltersQuery());
    const query = this.buildQuery(null) as SearchQueryParams;
    const search$ = this.semanticService.search(payload, query, false)
      .pipe(
        take(1),
        finalize(() => {
          this.isLoadingDocuments = false;
        })
      )
      .subscribe({
        next: ({data}) => {
          const docIds = data['documents'].map(doc => doc['general'].docdb_family_id);
          if (docIds.length > 0) {
            const freeTextQuery = `DOCDB_FAMILY_ID=(${docIds.join(' OR ')})`;
            const query = {
              filters: freeTextQuery,
              show_general: 0,
              show_analytics:  0,
              show_bibliographic: 0,
              show_fulltext: 0,
              show_tags: 0,
            };
            const getCollectionDocuments$ = this.getCollectionDocuments(query)
              .subscribe({
                next: (resp: PatentResultsResponse) => {
                  data['documents'].forEach(doc => {
                    doc.result = resp.documents.find(cDoc => cDoc['general'].docdb_family_id === doc['general'].docdb_family_id)?.result;
                  });
                  this.setDocuments(data, this.buildQuery(this.queryTaskId));
                },
                error: (error) => {
                  this.setDocuments(data, this.buildQuery(this.queryTaskId));
                }
              });
            this.subscriptions.add(getCollectionDocuments$);
          }
        }
      });
    this.subscriptions.add(search$);
  }

  private setDocuments(data: PatentResultsResponse, query) {
    if (data) {
      if(!this.original_pagination){
        if(this.filteredSources?.length>0){
          const subscription = this.collectionService.getCollectionDocuments(this.collection.id, {})
            .pipe(take(1))
            .subscribe({
              next: ({page}) => {
                this.original_pagination = page as PaginationMetadata;
              }, error: (error) => {
              }
            });
          this.subscriptions.add(subscription);
        } else {
          this.original_pagination = data['page'] as PaginationMetadata;
        }
      }
      this.pagination = data['page'] as PaginationMetadata;
      if (this.isPublicationsScope) {
        data['documents'] = this.publicationService.publicationsToDocuments(data['publications']);
      }
      this.documents = data['documents'];
      this.collectionStoreService.searchHash = data['search_info'].search_hash;
      this.loadCustomTag();
      this.setCollectionSourceToPatent();
    } else {
      this.pagination = null;
      this.documents = [];
      this.collectionStoreService.searchHash = null;
    }

    this.collectionStoreService.searchingEvent.emit(false);
    this.collectionStoreService.search= {params: Object.assign({}, query)};

    if (this.collectionStoreService.docdbFamilyIdFromPatentViewer) {
      this.collectionStoreService.scrollToPatentFromPatentViewer();
    }
  }
  private setCollectionSourceToPatent(){
    if(this.processSources || this.documents.length<1){ return; }
    if(!this.collectionSourceOption){
      const source = [...new Set(this.collection.collection_sources.map(s => this.getSourceType(s)))]
      this.collectionSourceOption = [...source.map(s=> { return {name: `${s}`, value: s}}), {name: 'multiple sources', value: 'multiple'}];
      if(this.collectionSourceOption.every(o => o.value !== 'unknown')){
        this.collectionSourceOption.push({name: 'unknown', value: 'unknown'});
      }
    }
    this.processSources = true;
    if(this.collection.collection_sources.length> 0){
      const sourcesByID = new Map();
      this.collection.collection_sources.forEach(s => {
        sourcesByID.set(s.id, s)
      })
      this.documents.forEach(p=> {
        if(p['result'] && p['result']?.scores?.length >0 ){
          let collectionSource = [];
          p['result']?.scores.forEach(sc=> {
            const sou = sourcesByID.get(sc.collection_source_id);
            if(sou){
              if(!!sou.monitor_run_id && sou.monitor_run){
                collectionSource.push(sou.monitor_run);
              } else if(!!sou.search_history_id && sou.search_history){
                collectionSource.push(sou.search_history);
              } else {
                collectionSource.push(sou);
              }
            }
          });
          p['collection_source']= collectionSource;
        }
      });
    }
    this.processSources = false;
  }

  sortCustomTags(): void {
    if (!this.isTagCollection || !this.customTag || !this.documents?.length) {
      return;
    }

    this.documents.forEach((doc) => {
      const index = doc['custom_tags'].findIndex((tag) => tag.id === this.customTag.id);
      const elementToMove = doc['custom_tags'].splice(index, 1)[0];
      doc['custom_tags'].unshift(elementToMove);
    });
  }

  resetSimilaritySort() {
    this.collectionStoreService.setSortBySimilarity(undefined);
    this.getDocuments(this.queryTaskId);
  }

  onTaskSaved(data: { message: string, payload: TaskModel, savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessage = this.taskService.getTaskCreationSuccessMessage(data, 'collection');

      setTimeout(() => {
        this.router.navigate(['/ratings', data.savedTasks[0].id]);
      }, 3000);
    }
  }

  onTaskSelected(data: TaskModel) {
    this.currentTask = data;
  }

  toggleSidebar() {
    this.expandSidebar = !this.expandSidebar;
  }

  private doCollectionConversion(type: string, profile?: { profile_name: string, profile_category?: string }): void {
    this.conversionInProcess = true;
    this.isLoadingCollection = true;
    this.collectionService.collectionConversion(this.collection.id, type, profile).then(
      res => {
        this.conversionInProcess = false;
        this.isLoadingCollection = false;
        this.confirmationDialogService.confirm('Profile created',
          'Do you want to redirect to the newly created ' + type.toLowerCase() + ' profile?', 'Yes', 'Cancel', 'lg').then(
          confirmation => {
            if (confirmation) {
              let redirectTo = '';
              switch (type) {
                case 'LANDSCAPE':
                  redirectTo = `landscape/draft/${res['id']}`;
                  break;
                case 'MONITOR':
                  redirectTo = `monitor/profile/${res['id']}/deeplearning`;
                  break;
              }
              this.router.navigateByUrl(redirectTo);
            }
          }
        ).catch(error => {
        });
      }
    ).catch(err => {
      console.error(err);
      this.conversionInProcess = false;
      this.isLoadingCollection = false;
      if (err.error.message) {
        this.confirmationDialogService.alert('Error', err.error.message);
      } else {
        throw err;
      }
    });
  }

  private initializeByCollectionId(id: number = null) {
    const collectionId = this.paramCollectionId || id;
    if (collectionId > 0) {
      this.sharedType = 'collection';
      if (!this.isBackFromPatentViewer) {
        this.collectionStoreService.resetCollection();
      }
      this.collectionReset();
      const params = {};
      if (this.queryTaskId) {
        params['task_id'] = this.queryTaskId;
      }
      const getCollectionObs = this.collectionService.getCollection(collectionId, params)
        .pipe(
          finalize(() => {
            this.checkOpenTasks(collectionId);
          })
        );
      this.getCollection(getCollectionObs, this.queryTaskId);
      const markAsReadForResource$ = this.notificationsService.markAsReadForResource(collectionId, 'COLLECTION')
        .pipe(take(1))
        .subscribe();
      this.subscriptions.add(markAsReadForResource$);
    }
  }

  private initializeByShareCode() {
    const shareCode = this.activatedRoute.snapshot.params.share_code;
    if (shareCode && !this.paramCollectionId) {
      this.sharedType = 'link';
      this.collectionStoreService.resetCollection();
      this.getCollection(this.collectionService.collectionByShareCode, null);
    }
  }

  private getCollection(observable: Observable<Collection>, taskId: number) {
    if (observable) {
      this.isLoadingCollection = true;
      this.collectionNotFound = false;
      const obs$ = observable.pipe(take(1))
        .subscribe({next: (data) => {
          if (data) {
            this.collection = data;
            this.descriptionText = data.description ?? '';
            this.collectionStoreService.isPublications = this.isPublicationsScope;
            this.sortHistory('Recently added');
            this.getDocuments(taskId);
            this.loadCollaboration();
            this.loadSourceUsers();
            this.loadSharedMonitorProfiles();
            if(!this.userOwnCollection()){
              this.saveSearchHeadline = 'Share collection';
            }
          } else {
            this.collection = null;
            this.setDocuments(null, this.buildQuery(taskId));
          }

          this.isLoadingCollection = false;
          this.collectionNotFound = !(this.collection?.id);
        }, error: error => {
          this.collection = null;
          this.isLoadingCollection = false;
          this.collectionNotFound = true;
          this.setDocuments(null, this.buildQuery(taskId));
        }});
      this.subscriptions.add(obs$);
    } else {
      this.isLoadingCollection = false;
      this.collectionNotFound = true;
      this.setDocuments(null, this.buildQuery(taskId));
    }
  }

  loadSourceUsers(){
    if(this.collection.collection_sources && this.collection.collection_sources.length > 0){
      const userIds = [...new Set(this.collection.collection_sources.map(s => s.user_id))];
      if (userIds.length) {
        const payload = {id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1};
        const getUsers$ = this.userService.getTeamUsers(payload)
          .subscribe({
            next: ({users}) => {
              this.collectionCollaborators = users
              const userList = Object.fromEntries(users.map(o => [o.id, o]));
              this.collectionSourceUsers = userList;
              const profile = this.userService.getUser()?.profile;
              if (profile) {
                userList[profile?.id] = profile;
              }
              this.collection.collection_sources.forEach(s => s.user = userList[s.user_id]);
            }
          });
        this.subscriptions.add(getUsers$);
      }
    }
  }

  loadCustomTag(): void {
    if (!this.isTagCollection) {
      return;
    }
    const getTag$ = this.tagService.getTag(this.collection.tag.id)
      .pipe(take(1))
      .subscribe({
        next: (data) => {
          this.customTag = data;
          this.sortCustomTags();
        }
      });
    this.subscriptions.add(getTag$);
  }

  private getDocuments(taskId: number) {
    if (!this.collection) {
      if (this.paramCollectionId && this.isTasksMode && taskId) {
        this.initializeByCollectionId();
      }
      return;
    }

    this.isLoadingDocuments = true;
    if (this.sortBySimilarity) {
      this.getSimilarityResults();
      return;
    }
    const query = this.buildQuery(taskId);
    this.collectionStoreService.setRefreshCollections(false);
    const getCollectionDocuments$ = this.getCollectionDocuments(query)
      .subscribe({
        next: (data) => {
          this.setDocuments(data, query);
          if (taskId) {
            this.notificationsService.markAsReadForResource(taskId, 'TASK').subscribe();
          }
        },
        error: (error) => {
          this.setDocuments(null, query);
        }
      });
    this.subscriptions.add(getCollectionDocuments$);
  }

  private getCollectionDocuments(query: {}): Observable<PatentResultsResponse> {
    return this.collectionService.getCollectionDocuments(this.collection.id, query)
      .pipe(
        take(1),
        finalize(() => {
          this.isLoadingDocuments = false;
          this.isFiltering = false;
        })
      );
  }

  /**
   * build query param for document request
   */
  private buildQuery(taskId: number): {} {
    const query = {};
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;
    query['show_general'] = 1;
    query['show_tags'] = 1;

    if (this.sorting.order) {
      query['sort_order'] = this.sorting.order;
    }

    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
    }

    if (taskId && this.enableTaskFeature) {
      query['task_id'] = taskId;
    }

    const filterQuery = this.collectionStoreService.getAppliedFiltersQuery();

    if (filterQuery) {
      query['filters'] = filterQuery;
    }
    if (this.filteredSources.length>0) {
      query['collection_source_ids'] = this.filteredSources.map(s=>s.id).join(',');
    }

    const collectionSourceTypeFilter = this.collectionStoreService.getAppliedCollectionSourceTypeFilter();
    if (collectionSourceTypeFilter) {
      query['collection_source_types'] = collectionSourceTypeFilter['value'];
    }

    const collectionSourceUserFilter = this.collectionStoreService.getAppliedCollectionSourceUserFilter();
    if (collectionSourceUserFilter) {
      query['collection_source_user_ids'] = collectionSourceUserFilter['value'];
    }
    return query;
  }

  private getFeatureText(type: string): Array<string> {
    if (type === 'LANDSCAPE') {
      return [`You don't seem to have access to Octimine Landscape yet.`, `<br>`,
        `If you want to find out more on how to visualize and make sense of large sets of patents, e.g. portfolios or research fields, please get in touch with us at <a href="mailto:<EMAIL>"><EMAIL></a>`];
    } else if (type === 'MONITOR') {
      return [`You don't seem to have access to Octimine Monitor yet.`, `<br>`,
        `If you want to find out more on how to stay up to date on the newest patents in your interest fields, please get in touch with us at <a href="mailto:<EMAIL>"><EMAIL></a>`];
    }
    return [];
  }
  getSharedTooltip(): string{
    if(this.collectionService.isCollectionSharedViaFolder(this.collection) && this.parentFolder)
      return this.collectionService.sharedFolderMessage(this.parentFolder);
    return null;
  }

  private loadCollaboration() {
    if (this.userService.isNotExternalUser()) {
      if(this.collection.folder_id){
        this.subscriptions.add(this.collaborationService.getCollaboration(this.collection.folder_id, CollaborationResourceTypeEnum.FOLDER)
        .subscribe({ next: (data) => {
          if(data.users.length> 0 || data.groups.length> 0 ) {
            this.collection.folder_collaboration = data;
          }
        }}));
        this.subscriptions.add(this.collectionService.getFolder(this.collection.folder_id).subscribe({ next: (folder) => { this.parentFolder = folder; }}));
      }
      if (!this.userOwnCollection()) {
        const payload = {resource_id: this.collection.id, resource_type: CollaborationResourceTypeEnum.COLLECTION};
        const getSharedWithMe$ = this.collaborationService.getSharedWithMe(payload)
          .pipe(take(1))
          .subscribe({
            next: (val) => {
              if (val.collaborations?.length > 0) {
                this.collaboration = val.collaborations[0];
                this.collaboration.status = CollaborationStatusEnum.READ;
                this.subscriptions.add(this.collaborationService.markAsRead(this.collection.id, CollaborationResourceTypeEnum.COLLECTION).subscribe());
              }
            }
          });
        this.subscriptions.add(getSharedWithMe$);
        if(this.collection.folder_id){
          this.subscriptions.add(this.collaborationService.getSharedWithMe({resource_id: this.collection.folder_id,
            resource_type: CollaborationResourceTypeEnum.FOLDER}).subscribe({
              next: (val) => {
                if (val.collaborations?.length > 0) {
                  this.collaboration = val.collaborations[0];
                }
              }
            }));
        }
      } else if (this.hasWorkflowFeature()) {
        this.subscriptions.add(this.collaborationService.getCollaboration(this.collection.id, CollaborationResourceTypeEnum.COLLECTION).subscribe({
            next: ({users, groups}) => {
              this.collection.users = users;
              this.collection.groups = groups;
            }
          }));
      }
    }
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.collectionStoreService.resetAdvancedFilter();
    this.collectionStoreService.setFilters([], true);
    this.collectionStoreService.setSortBySimilarity(undefined);
    this.collectionStoreService.patentTableSort = {field: null, order: null} as SortParams;
  }

  private checkOpenTasks(collectionId: number) {
    return;

    if (collectionId && this.userService.hasTaskFeature()) {
      const payload = {
        resource_id: collectionId,
        resource_type: TaskResourceTypeEnum.COLLECTION,
        status: 'in:' + [TaskStatusEnum.NEW, TaskStatusEnum.OPEN, TaskStatusEnum.DONE].join(','),
        page_size: 1
      };

      this.showTasks = false;

      const getTasks$ = this.taskService.getTasks(payload).pipe(take(1))
        .subscribe({
          next: (res) => {
            this.showTasks = res.page.total_hits > 0;
            if (this.showTasks) {
              this.expandSidebar = true;
            }
            this.showSidebarTooltip();
          }
        });
      this.subscriptions.add(getTasks$);
    }
  }

  private showSidebarTooltip() {
    setTimeout(() => {
      $('[data-toggle="tooltip"]').tooltip();
    }, 100);
  }

  private setFieldsAfterBack() {
    if (this.collectionStoreService.patentTableSort) {
      this.sorting = {...this.collectionStoreService.patentTableSort};
    }
  }

  getAlertMessage(): string {
    if (this.sortBySimilarity) {
      return 'No documents returned from sort by similarity. Try to clear or change your similarity query.';
    }

    if (this.hasFilters) {
      return 'It seems we can’t find any result based on your selection.<br>Try to clear or change your filters.';
    }

    return 'This collection does not have any documents yet. Try to add some documents to this collection.';
  }

  clearAllFilters() {
    this.collectionStoreService.advancedFilterAppliedQuery = '';
    this.advancedFilterService.reset();
    this.collectionStoreService.resetAdvancedFilter();
    this.collectionSourceOption.forEach(option => option.selected = false);
    for(let u in this.collectionSourceUsers){
      this.collectionSourceUsers[u].isSelected = false;
    }
    this.collectionStoreService.setFilters([]);
  }

  getSideBarViewMode(): PatentSideBarViewModeEnum {
    return this.showTasks ? PatentSideBarViewModeEnum.MODE_TASKS : PatentSideBarViewModeEnum.MODE_CONTENT;
  }

  sortHistory(sort_by: string){
    if(sort_by === this.historySort || !this.collection?.collection_sources || this.historySortOption.indexOf(sort_by) === -1) return;

    this.collection.collection_sources = this.collection.collection_sources.sort((a, b) => {
      if (sort_by === this.historySortOption[0]) {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      if (sort_by === this.historySortOption[1]) {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }
    });
    this.historySort = sort_by;
  }
  isSourceFiltered(source){
    return this.filteredSources.findIndex(s=> s.id === source.id)
  }

  onFilterSource(source){
    if(source.is_remove_event){ return; }
    const index = this.isSourceFiltered(source);
    const filteredSources = this.filteredSources;
    if(index > -1){
      filteredSources.splice(index, 1);
    } else {
      filteredSources.push(source);
    }
    this.filteredSources = filteredSources;
    this.getDocuments(this.queryTaskId);
  }

  getSourceType(source): CollectionSourceTypeEnum{
    if(source.monitor_run_id){
      if(source.monitor_run)
        return CollectionSourceTypeEnum.MONITOR;
      return CollectionSourceTypeEnum.UNKNOWN;
    }
    if(source.search_history_id){
      if(source.search_history?.search_type)
        return source.search_history.search_type?.toLowerCase() as CollectionSourceTypeEnum;
      return CollectionSourceTypeEnum.UNKNOWN;
    }
    return CollectionSourceTypeEnum.MANUAL;
  }

  loadSource(event, source, newTab?: boolean){
    event.stopPropagation();
    if(!!source.search_history_id && source.search_history){
      this.searchHistoryService.loadSavedSearch(source.search_history, newTab);
    } else if (!!source.monitor_run && source.monitor_run.can_read) {
      const isOwner = Number(source.monitor_run.user_id) == this.userService.getUser()?.profile?.id;
      const monitorRunUrl = this.monitorService.getMonitorRunUrl(source.monitor_run.profile_id, source.monitor_run.legal_status_active, source.monitor_run.id, isOwner);
      if(newTab){
        window.open(monitorRunUrl, '_blank');
      } else {
        this.router.navigateByUrl(monitorRunUrl);
      }
    }
  }

  canLoadSource(source): boolean {
    return (!!source.search_history_id && source.search_history) || (!!source.monitor_run && source.monitor_run.can_read);
  }

  get collectionSources() {
    if (this.collection?.collection_sources?.length > 0) {
      const sourceStats = {
        [CollectionSourceTypeEnum.SEMANTIC]: 0,
        [CollectionSourceTypeEnum.BOOLEAN]: 0,
        [CollectionSourceTypeEnum.MONITOR]: 0,
        [CollectionSourceTypeEnum.MANUAL]: 0
      };
      const sources = [];
      this.collection.collection_sources.filter(s=> !s.is_remove_event).forEach(s => {
        const source = this.getSourceType(s);
        sourceStats[source]++;
        if (!sources.includes(source)) {
          sources.push(source);
        }
      });
      return sources.filter(src => sourceStats[src] > 0)
        .map(src => `${sourceStats[src]} ${src}`)
        .join('<i class="fa-duotone fa-solid fa-circle-small m-x-spacing-xx-s fa-2xs"></i>');
    }

    return null;
  }

  onLoadSearchSource(searchHistory){
    if(searchHistory?.source){
      this.searchHistoryService.loadSavedSearch(searchHistory.source, searchHistory?.newTab);
    }
  }

  onLoadMonitorSource(source){
    if (source.monitorRun?.can_read) {
      const isOwner = Number(source.monitorRun.user_id) == this.userService.getUser()?.profile?.id;
      const monitorUrl = this.monitorService.getMonitorRunUrl(source.monitorRun.profile_id, source.monitorRun.legal_status_active, source.monitorRun.id, isOwner)
      if (!source.newTab) {
        this.router.navigateByUrl(monitorUrl);
      } else {
        window.open(monitorUrl, '_blank');
      }
    }
  }

  getSourceQuery(source){
    if(!!source.monitor_run_id && source.monitor_run){
      return this.collectionStoreService.getSourceDescription(source.monitor_run);
    } else if(!!source.search_history_id && source.search_history){
      return this.collectionStoreService.getSourceDescription(source.search_history);
    }
    return ''
  }

  editDescription() {
    if (this.isSaving) {
      return;
    }
    this.editingDescription = true;
    setTimeout(() => {
      const textarea = this.descriptionTextarea.nativeElement;
      textarea.focus();
      this.adjustTextareaHeight();
    });
  }

  adjustTextareaHeight() {
    const textarea = this.descriptionTextarea.nativeElement;
    textarea.style.height = '32px'; // Altura inicial
    if (textarea.scrollHeight > 32) {
      textarea.style.height = textarea.scrollHeight + 'px';
    }
  }
  saveDescription(event: KeyboardEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.descriptionText === this.collection.description) {
      this.editingDescription = false;
      return;
    }
    if (this.isSaving) {
      return;
    }
    this.isSaving = true;

    const payload: Collection = {
      name: this.collection.name,
      description: this.descriptionText || null,
      collection_type: this.collection.collection_type
    }
    const updateCollection$ = this.collectionService.updateCollection(this.collection.id, payload).pipe(
      finalize(() => {
        this.editingDescription = false;
        this.isSaving = false;
      })
    ).subscribe({
      next: (data) => {
        this.collection.description = data.description || '';
      },
      error: ({error}) => {
        this.descriptionText = this.collection.description;
        console.error(error);
      }
    })
    this.subscriptions.add(updateCollection$);
  }

  cancelEditingDescription() {
    this.editingDescription = false;
    this.descriptionText = this.collection.description;
  }
  getSourceTitle(source){
    if(source.monitor_run_id){
      return source.user_id == this.userService.getUser()?.profile?.id ? 'View monitor results' : 'Monitor results'
    } else {
      return 'Reload search';
    }
  }
  getCollectionPermission(){
    if(!this.userOwnCollection()){
      return this.collaboration?.permission;
    }

    return CollaborationPermissionEnum.READ_WRITE;
  }

  private loadSharedMonitorProfiles() {
    if (!this.userService.hasMonitor) {
      return;
    }

    const userId = this.userService.getUser()?.profile?.id;
    const sources = this.collection?.collection_sources || [];
    sources.forEach((cs) => {
      if (cs.monitor_run) {
        cs.monitor_run.can_read = Number(cs.monitor_run.user_id) === userId;
      }
    });

    const monitorProfileIds = [...new Set(
      sources.filter((cs) => cs.monitor_run)
        .filter((cs) => Number(cs.monitor_run.user_id) !== userId)
        .map((cs) => cs.monitor_run.profile_id)
    )];

    if (monitorProfileIds.length === 0) {
      return;
    }

    const params = {
      id: `in:${monitorProfileIds.join(',')}`,
      page_size: monitorProfileIds.length
    };

    const loadSharedProfiles$ = this.monitorService.loadSharedProfiles(params)
      .subscribe({
        next: ({profiles, page}) => {
          const sharedProfileIds = profiles.map((p) => p.id);
          this.collection.collection_sources.forEach((cs) => {
            if (cs.monitor_run && cs.monitor_run.profile_id) {
              cs.monitor_run.can_read = sharedProfileIds.includes(cs.monitor_run.profile_id);
            }
          });
        },
        error: (err) => {
        }
      });
    this.subscriptions.add(loadSharedProfiles$);
  }
}
