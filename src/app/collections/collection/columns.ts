export const columnsToShow = [
  {
    property: 'legal_status',
    label: 'Legal status',
    default: true
  }, {
    property: 'ipc4',
    label: 'IPC 4',
    default: false
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'owners',
    label: 'Owners',
    default: false
  }, {
    property: 'ultimate_owners',
    label: 'Ultimate owners',
    default: false
  }, {
    property: 'priority_date',
    label: 'Priority date',
    default: false
  }, {
    property: 'impact',
    label: 'Patent Value',
    default: false
  }, {
    property: 'collection_source_user',
    label: 'Added by',
    default: true
  }, {
    property: 'collection_source',
    label: 'Source',
    default: true
  }, {
    property: 'ratings',
    label: 'Ratings',
    default: true,
    requireCompany: true
  }
];


export const publicationColumnsToShow = [
  {
    property: 'legal_status',
    label: 'Legal status',
    default: true
  }, {
    property: 'ipc4',
    label: 'IPC 4',
    default: false
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'owners',
    label: 'Owners',
    default: false
  }, {
    property: 'ultimate_owners',
    label: 'Ultimate owners',
    default: false
  }, {
    property: 'publication_date',
    label: 'Publication date',
    default: false
  }, {
    property: 'collection_source_user',
    label: 'Added by',
    default: true
  }, {
    property: 'collection_source',
    label: 'Source',
    default: true
  }, {
    property: 'ratings',
    label: 'Ratings',
    default: true,
    requireCompany: true
  }
];
export const selectedColumnsCombinedMode = ['legal_status', 'applicants'];

export const columnsToShowTagged = [
  {
    property: 'legal_status',
    label: 'Legal status',
    default: true
  }, {
    property: 'ipc4',
    label: 'IPC 4',
    default: false
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'owners',
    label: 'Owners',
    default: false
  }, {
    property: 'ultimate_owners',
    label: 'Ultimate owners',
    default: false
  }, {
    property: 'priority_date',
    label: 'Priority date',
    default: false
  }, {
    property: 'impact',
    label: 'Patent Value',
    default: false
  }, {
    property: 'collection_source_user',
    label: 'Added by',
    default: true
  }, {
    property: 'collection_source',
    label: 'Source',
    default: true
  }, {
    property: 'ratings',
    label: 'Ratings',
    default: true,
    requireCompany: true
  }
];


export const publicationColumnsToShowTagged = [
  {
    property: 'legal_status',
    label: 'Legal status',
    default: true
  }, {
    property: 'ipc4',
    label: 'IPC 4',
    default: false
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'owners',
    label: 'Owners',
    default: false
  }, {
    property: 'ultimate_owners',
    label: 'Ultimate owners',
    default: false
  }, {
    property: 'publication_date',
    label: 'Publication date',
    default: false
  }, {
    property: 'collection_source_user',
    label: 'Added by',
    default: true
  }, {
    property: 'collection_source',
    label: 'Source',
    default: true
  }, {
    property: 'ratings',
    label: 'Ratings',
    default: true,
    requireCompany: true
  }
];
