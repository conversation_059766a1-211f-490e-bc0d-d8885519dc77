import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';
import { CollectionComponent } from './collection/collection.component';
import { CollectionsComponent } from './collections/collections.component';
import { SharedGuard } from '@core/guards/shared.guard';

const routes: Routes = [
  {path: '', component: CollectionsComponent, canActivate: [AuthGuard]},
  {path: 'documents/annotated', component: CollectionsComponent, canActivate: [AuthGuard]},
  {path: 'documents/opened', component: CollectionsComponent, canActivate: [AuthGuard]},
  {path: 'shared/:share_code', component: CollectionComponent, canActivate: [SharedGuard]},
  {path: ':category', component: CollectionsComponent, canActivate: [AuthGuard]},
  {path: ':category/:folder_id', component: CollectionsComponent, canActivate: [AuthGuard]},
  {path: ':category/collection/:collection_id', component: CollectionComponent, canActivate: [AuthGuard]},
  {path: ':category/:folder_id/collection/:collection_id', component: CollectionComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CollectionsRoutingModule {
}
