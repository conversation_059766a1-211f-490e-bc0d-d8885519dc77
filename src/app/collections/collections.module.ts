import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { CollectionsRoutingModule } from './collections-routing.module';
import { ChartsModule } from '@shared/charts/charts.module';
import {
  NgbActiveModal,
  NgbDatepickerModule,
  NgbDropdownModule,
  NgbModalModule,
  NgbTooltipModule
} from '@ng-bootstrap/ng-bootstrap';
import { CollectionComponent } from './collection/collection.component';
import { CollectionsComponent } from './collections/collections.component';
import { CollectionCardComponent } from './collection-card/collection-card.component';
import { FolderCardComponent } from './folder-card/folder-card.component';
import { AggregationComponent } from './aggregation/aggregation.component';
import { FoldersComponent } from './folders/folders.component';
import { ProfileDialogComponent } from './profile-dialog/profile-dialog.component';
import { AnnotatedDocumentsComponent } from './annotated-documents/annotated-documents.component';
import { OpenedDocumentsComponent } from './opened-documents/opened-documents.component';
import { ChartDashboardModule } from '@shared/charts';
import { SharedModule } from '@shared/shared.module';


@NgModule({
  declarations: [
    CollectionComponent,
    CollectionsComponent,
    CollectionCardComponent,
    FolderCardComponent,
    AggregationComponent,
    FoldersComponent,
    ProfileDialogComponent,
    AnnotatedDocumentsComponent,
    OpenedDocumentsComponent,
  ],
  exports: [],
  imports: [
    CommonModule,
    CollectionsRoutingModule,
    SharedModule,
    ChartsModule,
    NgbDropdownModule,
    NgbModalModule,
    NgbDatepickerModule,
    ChartDashboardModule,
    NgbTooltipModule
  ],
  providers: [NgbActiveModal, DatePipe]
})
export class CollectionsModule {
}
