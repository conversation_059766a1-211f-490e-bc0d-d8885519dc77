<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="bg-gray">
    <!-- Nav tabs -->
    <ul ngbNav [(activeId)]="collectionsTab" class="nav-tabs main-tabs nav-1 container mb-0 pb-0 d-flex justify-content-center" (navChange)="onChangeTab($event.nextId)">
      <li [ngbNavItem]="'collections'">
        <a ngbNavLink>My collections</a>
      </li>
      <li [ngbNavItem]="'tagged'" data-intercom-target="tags-tab">
        <a ngbNavLink>Tags</a>
      </li>
      <li [ngbNavItem]="'annotated'" data-intercom-target="comments-highlights-tab">
        <a ngbNavLink>Comments & highlights</a>
      </li>
      <li [ngbNavItem]="'opened'" data-intercom-target="recently-opened-tab">
        <a ngbNavLink>Recently opened</a>
      </li>
    </ul>
    <!-- /Nav tabs -->

  </div>

  <div class="flex-fill container page-content pb-4 container clt-container">
    <div *ngIf="collectionsTab === 'collections' || collectionsTab === 'tagged'" >
      <div *ngIf="!currentCollectionId else appCollection">
        <ng-container *ngIf="!showNotFound">
        <app-page-bar [pageTitle]="pageTitle">
          <div leftSide>
            <div class="d-flex justify-content-start align-items-center" [hidden]="isTaggedPage">
              <div class="input-group justify-content-between align-items-end col-5 ps-0 me-3" >
                <input type="text" class="form-control border-end-0 flex-fill filter-input" [placeholder]="'Search for an existing '+ (isTagCollection ? 'tag': 'list')"
                       [(ngModel)]="filter" (ngModelChange)="onChangeFilter()">
                <button ngbTooltip="Search for a term" class="btn border-start-0 filter-button" (click)="filterCollections()">
                  <i class="fa fa-search icon"></i>
                </button>
              </div>

              <span >SORT BY:</span>
              <div ngbDropdown class="position-relative mx-2">
                <div ngbDropdownToggle tabindex="2" class=" caret-off">
                  <input class="form-control" [value]="getSortLabel()" readonly style="cursor: pointer;">
                  <div class="dropdown-icon"></div>
                </div>
                <div ngbDropdownMenu>
                  <a *ngFor="let item of listSort" class="dropdown-item" [ngClass]="item.value === sortBy ? 'active' : ''"
                     (click)="onSortBy(item.value)">{{ item.label }}</a>
                </div>
              </div>

              &nbsp;
              <a (click)="onSortOrder(sortOrder!='desc' ? 'desc' : 'asc')"  class="sort-order " href="javascript:void(0)">
                <i class="fas fa-sort-amount-down-alt" [hidden]="sortOrder!='desc'"></i>
                <i class="fas fa-sort-amount-up-alt" [hidden]="sortOrder!='asc'"></i>
              </a>
            </div>
          </div>

          <ng-container rightSide>
            <span class="d-flex align-content-center flex-wrap count-stat color-2" [hidden]="alertMessage">
              {{ !currentFolderId? paginatingInformation : (pagination | countStat) }}
            </span>
          </ng-container>
        </app-page-bar>

        <app-spinner *ngIf="loading"></app-spinner>

        <div *ngIf="!loading">
          <div *ngIf="!isTagCollection" class="row mx-0">
            <hr style="background: #BCCACE" class="w-100"/>

            <div class="d-flex w-100 p-0" [ngClass]="currentFolder || isSharedFolder ? 'justify-content-between' : 'justify-content-end'">
              <div class="d-flex flex-wrap w-100 justify-content-between clt-actions p-0 ml-0">
                <div *ngIf="currentFolder || isSharedFolder" class="d-flex justify-content-end align-items-center me-auto">
                  <a (click)="backToCollections()" class="btn btn-primary-outline btn-md">
                    <i class="fas fa-long-arrow-alt-left"></i> Back
                  </a>
                  <i class="fas fa-folder fa-3x me-2 ms-3 text-orange"></i>
                  <div class="open-sans-bold text-green">
                    {{getTitle()}}
                  </div>
                </div>

                <div class="d-flex">
                  <button (click)="createNewCollection()" *ngIf="canWriteCurrentFolder" class="btn btn-primary btn-md mb-2 me-2"
                    data-intercom-target="new-list-button">
                    <i class="fas fa-plus"></i> NEW LIST
                  </button>
                  <button (click)="createNewFolder()" *ngIf="canWriteCurrentFolder" class="btn btn-primary btn-md ms-2 mb-2">
                    <i class="fas fa-folder-plus"></i> NEW FOLDER
                  </button>
                </div>

                <div class="p-0 text-end" *ngIf="!isSharedFolderId && (collections.length > 0 || folders.length > 0)">
                  <div class="d-flex justify-content-start">
                    <button (click)="onCombineSelectedClick()" [disabled]="selectedCollections.length < 2"
                      class="btn btn-primary-outline btn-md me-2 mb-2">
                      <i class="far fa-object-group"></i> COMBINE SELECTED
                      <span *ngIf="selectedCollections.length > 1">({{selectedCollections.length}})</span>
                    </button>
                    <button (click)="onDeleteSelectedClick()" [disabled]="selectedCollections.length === 0"
                      class="btn btn-primary-outline btn-md me-2 mb-2">
                      <i class="far fa-trash-alt"></i> DELETE SELECTED
                      <span *ngIf="selectedCollections.length > 0">({{selectedCollections.length}})</span>
                    </button>
                    <button class="btn btn-primary-outline btn-md me-2" *ngIf="selectedCollections.length > 0"
                      (click)="onClearSelectedCollectionsClicked()">Clear selected</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
          <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>

          <div class="cards-wrap">
            <div class="col-12" *ngIf="alertMessage && !loading && !isFiltering">
              <div *ngIf="!isTagCollection" class="text-center alert">
                <h4 class="text-gray">{{alertMessage}}</h4>
              </div>
              <app-alert *ngIf="tags?.length === 0" type="info" [message]="alertMessage" [version]="userService.hasTagFeature() ? 'current' : 'figma'"
              [headline]="userService.hasTagFeature() ? 'No tagged patents.' : 'No tags have been shared with you yet.'" >
              </app-alert>
            </div>
            <ng-container *ngIf="!isTagCollection else tagsContainer">

              <ng-container *ngIf="isMonitorResults">
                <app-folder-card *ngFor="let p of monitorResultProfiles"
                                 class="single-card monitor-profile-folder"
                                 [folder]="p" [folderLink]="buildFolderLink(p.id, category)">
                </app-folder-card>
              </ng-container>

              <ng-container *ngIf="isHomePage || isDefaultFolder">
                <app-folder-card *ngFor="let f of folders"
                                 class="single-card collections-folder"
                                 [folder]="f"
                                 [parentFolder]="currentFolder"
                                 (deleted)="onFolderDeleted($event, f)"
                                 (shareFolder)="onShareFolder($event)"
                                 [folderLink]="buildFolderLink(f.id, category)">
                </app-folder-card>
              </ng-container>

              <ng-container *ngIf="isFolderSharedWithMe && currentFolderId">
                <app-folder-card *ngFor="let f of foldersSharedWithMe" class="single-card shared-folder"
                                 [folder]="f"
                                 [parentFolder]="currentFolder"
                                 [readonlyFolder]="!canWriteFolder(f)"
                                 (shareFolder)="onShareFolder($event)"
                                 (deleted)="onFolderDeleted($event, f)"
                                 [folderLink]="buildFolderLink(f.id, collectionsCategory.SHARED_WITH_ME)">
                </app-folder-card>
              </ng-container>

              <app-collection-card class="single-card my-collection" *ngFor="let c of displayedCollections"
                                   (deleted)="onCollectionDeleted($event, c)"
                                   (selected)="onCollectionSelected($event, c)"
                                   (shareCollection)="onOpenShareCollectionForm($event)"
                                   [parentFolder]="currentFolder"
                                   [collection]="c" [isSelected]="isCollectionSelected(c)"
                                   [collectionLink]="buildCollectionLink(c.id, category)">
              </app-collection-card>
            </ng-container>
            <ng-template #tagsContainer>
                <app-collection-card class="single-card tagged-collection cursor-pointer"
                                     *ngFor="let t of tags" [taggedCollection]="t"
                                     [collectionLink]="buildCollectionLink(t.collection_id, collectionsCategory.TAGGED)"
                                     (deleted)="onTagDeleted()">
                </app-collection-card>
            </ng-template>
            <div class="col-12">
              <app-pagination *ngIf="!loading" [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
            </div>
        </div>
        </div>
        <div *ngIf="!hideShareContent" class="shared-collections-section">
          <app-page-bar pageTitle="Shared with me" *ngIf="!loadingSharedCollaborations">
            <div leftSide></div>
            <ng-container rightSide>
              <span class="d-flex align-content-center flex-wrap count-stat color-2" [hidden]="sharedContentAlertMessage">
                {{ !currentFolderId ? sharedPaginationInformation : (sharedPagination | countStat) }}
              </span>
            </ng-container>
          </app-page-bar>
          <app-spinner *ngIf="loadingSharedCollaborations && !loading"></app-spinner>
          <div *ngIf="!loadingSharedCollaborations">
            <div class="cards-wrap">
              <div class="col-12" *ngIf="!isTagCollection && sharedContentAlertMessage && !loadingSharedCollaborations">
                <div class="text-center alert">
                  <h4 class="text-gray">{{sharedContentAlertMessage}}</h4>
                </div>
              </div>
              <ng-container *ngIf="countFoldersSharedWithMe && sharedPagination?.current_page === 1">
                <app-folder-card *ngFor="let f of foldersSharedWithMe" class="single-card shared-folder"
                                 [folder]="f" (shareFolder)="onShareFolder($event)"
                                 [folderLink]="buildFolderLink(f.id, collectionsCategory.SHARED_WITH_ME)">
                </app-folder-card>
              </ng-container>
              <app-collection-card *ngFor="let c of collectionsSharedWithMe"
                                   class="single-card collection-shared-with-me"
                                   [collaboration]="c.collaboration" [collection]="c"
                                   (shareCollection)="onOpenShareCollectionForm($event, c.collaboration)"
                                   [collectionLink]="buildCollectionLink(c.id, collectionsCategory.SHARED_WITH_ME)">
              </app-collection-card>
            </div>
            <div class="col-12">
              <app-pagination  [pagination]="sharedPagination" (navigatePage)="navigateShared($event)"></app-pagination>
            </div>
          </div>
        </div>
        </ng-container>
        <div class="container-fluid mt-5 mb-5" *ngIf="showNotFound">
          <app-alert type="warning" mainTitle="Not found !" [hideCloseBtn]="true" message="Requested list could not be found">
          </app-alert>
        </div>
      </div>

      <ng-template #appCollection>
        <div [hidden]="loading">
          <app-collection></app-collection>
        </div>
      </ng-template>
    </div>

    <div class="page-content pb-0" *ngIf="collectionsTab === 'annotated'">
        <app-annotated-documents [storeService]="collectionStoreService"></app-annotated-documents>
    </div>

    <div class="page-content pb-0" *ngIf="collectionsTab === 'opened'">
      <app-opened-documents></app-opened-documents>
    </div>
  </div>

  <app-footer></app-footer>
</div>
