import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { of } from 'rxjs';
import {
  CollaborationService,
  Collection,
  CollectionService,
  CollectionsResponse,
  PatentListScopeEnum
} from '@core/services';
import { CollectionsComponent } from '@collections/collections/collections.component';
import { CollaborationsResponse } from '@core/models';
import { CollectionCardComponent } from '@collections/collection-card/collection-card.component';
import { CollectionComponent } from '@collections/collection/collection.component';
import { FolderCardComponent } from '@collections/folder-card/folder-card.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('CollectionsComponent', () => {
  let component: CollectionsComponent;
  let fixture: ComponentFixture<CollectionsComponent>;
  let helper: CollectionsHelper;
  let collectionServiceMock: any;
  let collaborationServiceMock: any;

  beforeEach(waitForAsync(() => {
    helper = new CollectionsHelper();
    collectionServiceMock = jasmine.createSpyObj('CollectionService', ['getCollections', 'getFolders', 'isCollectionSharedViaFolder']);
    collectionServiceMock.getCollections.and.returnValue(of(helper.getCollections(10)));
    collectionServiceMock.getFolders.and.returnValue(of([]));
    collectionServiceMock.isCollectionSharedViaFolder.and.returnValue(false);

    collaborationServiceMock = jasmine.createSpyObj('CollaborationService', ['setCollaborations',
      'getSharedWithMe', 'purgeCollaborations']);
    collaborationServiceMock.getSharedWithMe.and.returnValue(of(helper.getCollaborations()));

    TestBed.configureTestingModule({
      declarations: [
        CollectionComponent,
        CollectionsComponent,
        CollectionCardComponent,
        FolderCardComponent
      ],
      imports: [
        ReactiveFormsModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([]),
        NgbModule, BrowserAnimationsModule
      ],
      providers: [
        {provide: CollectionService, useValue: collectionServiceMock},
        {provide: CollaborationService, useValue: collaborationServiceMock},
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CollectionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should create filter bar', () => {
    component.currentCollectionId = null;
    fixture.detectChanges();
    const element = fixture.debugElement.query(By.css('.filter-input'));
    expect(element).toBeTruthy();
  });

  xit('should show 10 collections', () => {
    fixture.detectChanges();
    const collections = fixture.debugElement.queryAll(By.css('app-collection-card'));
    expect(collections.length).toEqual(10);
  });

  it('should show a create collection form', () => {
    component.createNewCollection();
    fixture.detectChanges();
    const formNewCollection = fixture.debugElement.query(By.css('.modal-header'));
    expect(formNewCollection).toBeTruthy();


  });

  it('should show a combined collection form', () => {
    component.onCombineSelectedClick();
    fixture.detectChanges();
    const formCombineCollection = fixture.debugElement.query(By.css('.modal-header'));
    expect(formCombineCollection).toBeTruthy();
  });

  xit('should add a collection', () => {
    component.collections.push(helper.generateCollections(1)[0]);
    fixture.detectChanges();
    const collections = fixture.debugElement.queryAll(By.css('app-collection-card'));
    expect(collections.length).toEqual(11);
  });


  xit('should remove a collection', () => {
    component.collections.splice(0, 1);
    fixture.detectChanges();
    const collections = fixture.debugElement.queryAll(By.css('app-collection-card'));
    expect(collections.length).toEqual(9);
  });

});

class CollectionsHelper {

  generateCollections(amount: number): Collection[] {
    const collections: Collection[] = [];
    for (let i = 0; i < amount; i++) {
      collections.push(
        {
          created_at: '2021-03-16T13:15:38',
          description: null,
          expires_at: '2021-03-26T13:17:44',
          folder_id: null,
          id: i,
          name: 'Collection ' + i,
          results_count: 2001,
          type: 'DEFAULT',
          updated_at: '2021-03-16T13:17:45',
          user_id: 30022,
          document_ids: [],
          collection_type: PatentListScopeEnum.FAMILY
        }
      );
    }
    return collections;
  }

  getCollections(amount: number): CollectionsResponse {
    return {
      result_collections: this.generateCollections(amount),
      page: {
        current_page: 0,
        last_page: 0,
        page_size: 0,
        total_hits: 0,
        origin_total_hits: 0,
      }
    };
  }

  getCollaborations(): CollaborationsResponse {
    return {
      collaborations: [],
      page: {
        current_page: 0,
        last_page: 0,
        page_size: 0,
        total_hits: 0,
        origin_total_hits: 0,
      }
    };
  }
}
