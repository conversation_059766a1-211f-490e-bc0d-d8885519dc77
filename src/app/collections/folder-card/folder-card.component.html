<div class="h-100 d-flex flex-column cursor-pointer" (click)="viewFolder()" appSelectableTooltip
     [selectableTooltipPopover]="t1" #t1="ngbPopover" [ngbPopover]="dropdown?.isOpen() ? null : cardTooltip" triggers="manual"
     [autoClose]="'outside'" popoverClass="popover-md">
  <div *ngIf="hasReadWritePermission() || isFolderOwner" ngbDropdown class="abs"  placement="bottom-end" (click)="$event.stopPropagation()" #dropdown="ngbDropdown">
    <span aria-expanded="false" aria-haspopup="true" class="icon profile-dropdown-option" ngbDropdownToggle>
      <i class="fas fa-ellipsis-v"></i>
    </span>
    <ul class=" shadow-lg" ngbDropdownMenu>
      <li class="dropdown-item"><a (click)="viewFolder()" href="javascript:void(0)"><i class="fa fa-eye fa-fw"></i>Open</a></li>
      <li class="dropdown-item" *ngIf="isFolderOwner"><a (click)="moveFolder()" href="javascript:void(0)"><i class="fa fa-arrows-alt fa-fw"></i>Move</a></li>
      <li class="dropdown-item" *ngIf="hasReadWritePermission()"><a (click)="editFolder()" href="javascript:void(0)"><i class="fa fa-edit fa-fw"></i>Edit</a></li>
      <li class="dropdown-item" *ngIf="isFolderOwner"><a (click)="deleteFolder()" href="javascript:void(0)" class="remove-folder"><i class="fa fa-trash fa-fw"></i>Remove</a>
      </li>
    </ul>
  </div>
  <div class="single-card-header d-flex flex-row">
    <span class="fa-stack">
      <i class="fas fa-folder fa-stack-2x text-orange "></i>
      <i *ngIf="isSharedFolder || !folder.id" class="fas fa-link fa-stack-1x text-white"></i>
      <i *ngIf="isNewCollaboration()" class="fas fa-circle new-notifications"></i>
    </span>

    <a href="javascript:void(0)" class="card-title ps-1 flex-grow-1">
      {{getTitle()}}
    </a>
  </div>
  <div class="single-card-footer d-flex justify-content-between">
    <div class="bottom-left" *ngIf="folder?.id">
        <span><img alt="" src="assets/images/landscape/schedule-24px.svg">
          {{folder?.created_at | dateFormat: 'ShortDate'}}
        </span>
    </div>

    <div class="bottom-left clt-shared">
      <span [ngbTooltip]="sharedTooltip? shareButtonTooltip : null" (shown)="t1.close()">
        <label class="check-on-off" *ngIf="isFolderOwner && hasWorkflowFeature() && !readonlyFolder" (click)="$event.stopPropagation(); toggleShareFolder($event);">
          <span>{{avatarService.hasUsersAndGroups(folder?.collaborators?.users, folder?.collaborators?.groups) || !hasPermission('share') ? 'SHARED' : 'SHARE'}}</span>
          <input [disabled]="isProcessing || !hasPermission('share')" type="checkbox" class=" right" [checked]="avatarService.hasUsersAndGroups(folder?.collaborators?.users, folder?.collaborators?.groups) || !hasPermission('share')">
        </label>

        <div *ngIf="(folder?.collaboration && folder?.user)" class="d-flex flex-row justify-content-end align-items-center" (click)="toggleShareFolder($event);">
          <div class="clt-collaboration-share-title">SHARED BY</div>
          <app-user-avatar [user]="folder.user" [hasSubTitle]="false" class="clt-text-avatar" [avatarSize]="20" [avatarFontSize]="10"></app-user-avatar>
        </div>
      </span>
    </div>
  </div>
</div>

<ng-template #shareButtonTooltip>
  <span [innerHTML]="sharedTooltip"></span>
</ng-template>

<ng-template #cardTooltip>
  <div class="d-flex content-heading-h6 p-b-spacing-sm text-break" *ngIf="isCardTitleTruncated()">{{getTitle()}}</div>
  <div class="d-flex p-b-spacing-sm text-break" *ngIf="!isSharedFolder && folder && folder.description?.length" [ngbTooltip]="folder.description">{{folder.description}}</div>
  <div class="d-flex justify-content-between gap-spacing-md">
    <div class="d-flex justify-content-start gap-spacing-xx-s">
      <ng-container *ngIf="folderContainsCollections && (folder?.collaboration || isFolderOwner)">
        <span class="open-sans-bold  text-orange">{{countItems}}</span>
        <span class="single-card-text">{{countText}}{{countItems === 1 ? '' : 'S' }}</span>
      </ng-container>

      <ng-container *ngIf="!folderContainsCollections">
        <span class="open-sans-bold  text-orange">{{countItems}}</span>
        <span class="single-card-text">PATENT{{countItems === 1 ? '' : 'S' }}</span>
      </ng-container>
    </div>
    <app-user-avatars *ngIf="hasWorkflowFeature() && folder?.collaborators && avatarService.hasUsersAndGroups(folder.collaborators.users, folder.collaborators.groups)"
        [users]="folder.collaborators.users" [groups]="folder.collaborators.groups" [numberDisplayedUsers]="numberDisplayedUsers"
        [numberDisplayedGroups]="numberDisplayedGroups" [distanceBetweenAvatars]="25" avatarsTooltipPrefix="Shared with"
        (click)="toggleShareFolder($event)" class="cursor-pointer">
    </app-user-avatars>
  </div>
</ng-template>
