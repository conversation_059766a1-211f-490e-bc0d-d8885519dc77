import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FolderCardComponent } from './folder-card.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { Folder } from '@core/services';
import { provideMatomo } from 'ngx-matomo-client';

describe('FolderCardComponent', () => {
  let component: FolderCardComponent;
  let fixture: ComponentFixture<FolderCardComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        FolderCardComponent
      ],
      imports: [ReactiveFormsModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FolderCardComponent);
    component = fixture.componentInstance;
    component.folder = {
      id: 123,
      name: 'Test',
      description: 'Description'
    } as Folder;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
