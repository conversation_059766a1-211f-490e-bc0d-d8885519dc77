import { Component, ElementRef, EventEmitter, Input, OnDestroy, Output, ViewChild } from '@angular/core';
import { NgbDropdown, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { FolderFormComponent } from '@collections/folder-form/folder-form.component';
import { AvatarService, CollectionService, ConfirmationDialogService, Folder, NotificationsService, UserService } from '@core/services';
import { Collaboration, CollaborationResourceTypeEnum, CollaborationStatusEnum } from '@core/models';
import { take } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { FoldersComponent } from '@collections/folders/folders.component';

@Component({
  selector: 'app-folder-card',
  templateUrl: './folder-card.component.html',
  styleUrls: ['./folder-card.component.scss']
})
export class FolderCardComponent implements OnD<PERSON>roy {
  @Input() folder: Folder;
  @Input() parentFolder: Folder;
  numberDisplayedUsers = 2;
  numberDisplayedGroups = 2;

  @Input() collaborations: Array<Collaboration>;

  @Input() collaborationResourceType: CollaborationResourceTypeEnum;
  @Input() readonlyFolder: boolean = false;
  @Input() folderLink: string;

  @Input() countText: string = 'LIST';

  @Input() totalItems: number;
  @Output() shareFolder = new EventEmitter<Folder>(null);

  @Output() deleted = new EventEmitter<boolean>(true);

  @ViewChild('dropdown') dropdown: NgbDropdown;

  private subscriptions = new Subscription();

  constructor(
    private modalService: NgbModal,
    private userService: UserService,
    public avatarService: AvatarService,
    private confirmationDialogService: ConfirmationDialogService,
    private collectionService: CollectionService,
    private router: Router,
    private notificationsService: NotificationsService,
    private elementRef: ElementRef
  ) {
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get countItems(): number {
    if (this.isSharedFolder) {
      return this.totalItems || this.collaborations?.length;
    }
    if (this.collaborations) {
      return this.collaborations.length;
    }
    return this.folder.collections_count;
  }

  get isSharedFolder(): boolean {
    return !!(this.collaborationResourceType) || this.collaborations?.length > 0;
  }

  get folderContainsCollections(): boolean {
    return !!(this.folder) || this.isCollectionResourceType;
  }

  get isCollectionResourceType(): boolean {
    return this.collaborationResourceType === CollaborationResourceTypeEnum.COLLECTION;
  }

  get isFolderOwner(): boolean {
    return this.folder && this.userService.isResourceOwner(this.folder.user_id);
  }

  hasPermission(permission: string): boolean {
    return this.folder?.permissions?.includes(permission)
  }

  isNewCollaboration(): boolean{
    return this.folder?.collaboration?.status === CollaborationStatusEnum.NEW || (this.isSharedFolder && this.hasNewCollaborations())
  }

  viewFolder() {
    this.router.navigate([this.folderLink]).then();
  }

  editFolder() {
    if (this.folder) {
      this.modalService.dismissAll();
      const modal = this.modalService.open(FolderFormComponent, {size: 'lg'});
      modal.componentInstance.folder = this.folder;
    }
  }

  hasWorkflowFeature() {
    return this.userService.canUseWorkflowFeature();
  }

  deleteFolder() {
    if (this.folder) {
      const title = '<i class="fas fa-trash-alt fa-2x"></i>';
      const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${this.folder.name}</span> ?</div>`;
      const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');

      modalRef.then(val => {
        if (val) {
          const deleteFolder$ = this.collectionService.deleteFolder(this.folder.id).pipe(take(1)).subscribe({next: data => {
            this.deleted.emit(true);
          }, error: error => { console.error(error); }});
          this.subscriptions.add(deleteFolder$);
        }
      }, rejected => {
      });
    }
  }

  hasNewCollaborations(): boolean {
    if(this.folder?.collaboration){
      return this.folder.collaboration.status === CollaborationStatusEnum.NEW;
    }
    return this.collaborations.some(co =>
            this.notificationsService.notifications
                .find(nt => nt.resource_id === co.resource_id &&
                                 nt.resource_type === co.resource_type)?.status === CollaborationStatusEnum.NEW);
  }

  getTitle(): string {
    if (!this.isSharedFolder && this.folder) {
      return this.folder.name;
    }

    if (this.collaborationResourceType === CollaborationResourceTypeEnum.COLLECTION) {
      return 'Lists shared with me';
    }

    if (this.collaborationResourceType === CollaborationResourceTypeEnum.PATENT) {
      return 'Patents shared with me';
    }

    return 'Monitor Results';
  }

  get sharedTooltip(): string{
    if (!this.parentFolder) {
      return null;
    }
    if (!this.hasPermission('share')) {
      return 'This folder seems to be shared via a folder up in the hierarchy. To make changes, open that folder’s sharing settings.';
    }
    return null;
  }

  toggleShareFolder(event) {
    if (event !== undefined && this.hasPermission('share')) {
      event.preventDefault();
      event.stopPropagation();
      this.shareFolder.emit(this.folder);
    }
  }
  moveFolder(){
      this.modalService.dismissAll();
      const  modal = this.modalService.open(FoldersComponent, {size: 'xl'});
      modal.componentInstance.resource = this.folder;
  }

  hasReadWritePermission(): boolean {
    return this.isFolderOwner || this.folder?.permissions?.includes('write');
  }

  isCardTitleTruncated(): boolean {
    const cardTitleEle = (this.elementRef.nativeElement as HTMLElement).querySelector<HTMLElement>('.card-title');
    if (!cardTitleEle) {
      return false;
    }
    return cardTitleEle.scrollHeight > cardTitleEle.clientHeight + 1;
  }
}
