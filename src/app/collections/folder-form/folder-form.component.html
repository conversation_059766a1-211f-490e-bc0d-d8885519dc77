<div class="modal-header">
  <div class="modal-title">
    {{isEditing() ? 'Edit folder *' + folder.name + '*' : 'Create new folder'}}
  </div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>
<div class="modal-body">
  <app-alert type="success" *ngIf="successMessage && !form.dirty" [message]="successMessage"></app-alert>

  <form [formGroup]="form" class="ff-form" (ngSubmit)="saveFolder()">
    <div *ngIf="hasParentFolder" class="mb-3 d-flex flex-column align-items-start gap-spacing-sm">
      <label>Parent folder</label>
      <div class="d-flex align-items-center gap-spacing-sm m-l-spacing-sm">
        <span class="fa-stack fa-xs content-color-tertiary">
          <i class="fa-thin fa-folder fa-stack-2x"></i>
          <i class="fa-regular fa-folder-tree fa-stack-1x fa-xs"></i>
        </span>
        <div class="content-label-medium  content-color-primary">{{ parentFolder.name }}</div>
      </div>
    </div>

    <div class="mb-3">
      <label for="name">Name*</label>
      <input [ngClass]="{'is-invalid': isFieldInvalid('name')}" autocomplete="off" class="form-control"
             formControlName="name" id="name" maxlength="255" name="name"
             placeholder="Name of folder" required type="text"/>

      <div *ngIf="form.get('name').touched && form.get('name').hasError('required')"
           class="invalid-feedback">
        Please enter name of folder
      </div>

      <div *ngIf="fieldErrors['name']" class="invalid-feedback">{{fieldErrors['name'].join(' ')}}</div>
    </div>

    <div [ngClass]="{'has-error': false}" class="mb-3">
      <label for="description">Description</label>
      <textarea [ngClass]="{'is-invalid': isFieldInvalid('description')}"
                autocomplete="off" class="form-control" formControlName="description"
                id="description" name="description" placeholder="Description of folder" rows="2"></textarea>
      <div *ngIf="fieldErrors['description']" class="invalid-feedback">{{fieldErrors['description'].join(' ')}}</div>
    </div>

    <input type="submit" hidden>
  </form>
</div>
<div class="modal-footer ff-footer d-flex justify-content-between">
  <div class="text-black-50"><small>Press <span class="fw-bold">Enter</span> to save</small></div>
  <div class="d-flex justify-content-end">
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
    <button (click)="activeModal.dismiss('Cancel')" class="btn btn-ghost btn-md">Cancel</button>
    <button [disabled]="!form.dirty || form.invalid || isSaving" (click)="saveFolder()" class="btn btn-primary  btn-md">Save</button>
  </div>
</div>
