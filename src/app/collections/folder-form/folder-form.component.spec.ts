import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FolderFormComponent } from './folder-form.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { Folder } from '@core/services';
import { provideMatomo } from 'ngx-matomo-client';

describe('FolderFormComponent', () => {
  let component: FolderFormComponent;
  let fixture: ComponentFixture<FolderFormComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      providers: [NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
      declarations: [],
      imports: [
        ReactiveFormsModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([]),
        NgbModalModule
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FolderFormComponent);
    component = fixture.componentInstance;
    component.folder = {
      id: 123,
      name: 'Test',
      description: 'Description'
    } as Folder;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
