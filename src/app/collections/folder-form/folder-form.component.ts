import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { finalize, take } from 'rxjs/operators';
import { CollectionService, Folder } from '@core/services';
import { CollectionStoreService } from '@core/store';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-folder-form',
  templateUrl: './folder-form.component.html',
  styleUrls: ['./folder-form.component.scss']
})
export class FolderFormComponent implements OnInit, OnDestroy {
  parentFolder: Folder = null;
  folder = {} as Folder;
  form: UntypedFormGroup;
  successMessage: string;
  fieldErrors = {};
  isSaving = false;

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private collectionService: CollectionService,
    private collectionsStoreService: CollectionStoreService,
  ) {
  }

  get hasParentFolder(): boolean {
    return !!this.parentFolder?.id;
  }

  ngOnInit() {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  saveFolder() {
    this.form.clearValidators();
    this.fieldErrors = {};

    if (this.form.valid) {
      const payload = this.buildPayload();

      const observable = this.isEditing() ?
        this.collectionService.updateFolder(this.folder.id, payload) :
        this.collectionService.createFolder(payload);

      this.isSaving = true;
      this.form.disable();

      const obs$ = observable
        .pipe(
          take(1),
          finalize(() => this.isSaving = false)
        )
        .subscribe({
          next: (data) => {
            this.collectionsStoreService.setRefreshFolders(true);
            this.activeModal.close();
          },
          error: ({error}) => {
            console.error(error);
            this.form.enable();
            this.fieldErrors = error.details || {};

            if (error.message.includes('Duplicate entry') || error.message.includes('You already have a folder')) {
              if (!this.fieldErrors['name']) {
                this.fieldErrors['name'] = [];
              }

              this.fieldErrors['name'].push(`You already have a folder with name "${this.form.get('name').value}"`);
            }
          }
        });
      this.subscriptions.add(obs$);
    } else {
      this.form.markAllAsTouched();
    }
  }

  isEditing(): boolean {
    return this.folder && !!(this.folder.id);
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return this.fieldErrors[name] || (field && field.touched && field.invalid);
  }

  private buildPayload(): Folder {
    const data = this.form.value;

    const payload = {
      name: data.name,
      parent_id: this.parentFolder?.id
    } as Folder;

    if (data.description) {
      payload.description = data.description;
    }

    return payload;
  }

  private buildForm() {
    this.form = new UntypedFormGroup({
      name: new UntypedFormControl(this.folder.name, [
        Validators.required, Validators.maxLength(255)
      ]),
      description: new UntypedFormControl(this.folder.description, [Validators.maxLength(255)]),
    });
  }
}
