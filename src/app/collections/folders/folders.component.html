<div class="modal-header">
    <div class="modal-title">
        Move {{isFolder? 'folder': 'list'}} *{{resource?.name}}* to *Root/@for (item of selectedList; track item.name) { {{ item.name}}/ }*
    </div>
    <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
        type="button"></button>
</div>
<div class="modal-body">
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
    <div class="folders-list p-spacing-big" *ngIf="!isLoading">
        <div class="w-100 d-flex align-items-center m-b-spacing-big" *ngIf="selected">
            <a (click)="onBack()" class="btn btn-primary-outline btn-md"><i class="fas fa-long-arrow-alt-left"></i> Back </a>
            <i class="fas fa-folder fa-3x m-l-spacing-big m-r-spacing-xx-big text-orange"></i>
            <span class="open-sans-bold text-green">{{selected.name}}</span>
        </div>
        <div class="d-flex gap-spacing-md">
            <div class="folder" [ngClass]="{'selected': !selected}" (click)="selectFolder(null)" *ngIf="!selected">
                <i class="fas fa-folder fa-3x"></i>
                <p>Root</p>
            </div>
            <ng-container *ngFor="let folder of folders">
                <div class="folder" [ngClass]="{'selected': folder?.id === selected?.id}" (click)="selectFolder(folder)">
                    <i class="fas fa-folder fa-3x"></i>
                    <p>{{folder.name | truncate: 20: true}}</p>
                </div>
            </ng-container>
        </div>
        <div class="d-flex justify-content-center w-100" *ngIf="selected && folders.length === 0" >
            <app-alert type="info" message="This folder does not have any subfolders." version="figma"></app-alert>
        </div>
    </div>
    <app-spinner *ngIf="isLoading"></app-spinner>
</div>
<div class="modal-footer">
    <button [disabled]="isLoading" (click)="activeModal.dismiss('Cancel')" class="button-main-secondary-grey">Cancel</button>
    <button [disabled]="isLoading" (click)="onSave()" class="button-main-primary">Save</button>
</div>
