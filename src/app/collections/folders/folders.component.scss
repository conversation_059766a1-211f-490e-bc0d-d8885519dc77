@use 'scss/layout2021/variables' as variables;

.folders-list{
  display: -ms-flex;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}
.folder{
    color: variables.$brand-green;
    padding: 10px;
    border-radius: 10px;
    width: 80px;
    text-align: center;
    cursor: pointer;
    transition: all ease .3s;
    &.selected{
        background: variables.$brand-green-pressed;
        color: variables.$brand-orange;
    }
    p{
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }
}
