import { Component, OnDestroy, OnInit } from '@angular/core';
import { CollectionStoreService } from '@core/store';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize, take } from 'rxjs/operators';
import { Collection, CollectionService, Folder } from '@core/services';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-folders',
  templateUrl: './folders.component.html',
  styleUrls: ['./folders.component.scss']
})
export class FoldersComponent implements OnInit, OnDestroy {

  folders: Array<Folder> = [];
  isLoading = false;
  resource: Collection | Folder;
  selected: Folder;
  selectedList: Folder[] = [];
  errors: string[] = [];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private collectionService: CollectionService,
    private collectionsStoreService: CollectionStoreService,
  ) {
  }

  get isFolder(): boolean{
    return this.resource && 'parent_id' in this.resource;
  }

  ngOnInit() {
    this.loadFolders(null);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  public selectFolder(folder: Folder) {
    if(folder){
      this.selectedList.push(folder);
    }
    this.selected = folder ? folder : null;
    this.loadFolders(folder?folder.id: null);
  }

  public onSave() {
    let ob;
    this.errors = [];
    if (this.isFolder) {
      ob = this.collectionService.updateFolder(this.resource.id, { parent_id: this.selected ? this.selected.id : null })
    } else {
      ob = this.collectionService.updateCollection(this.resource.id, { folder_id: this.selected ? this.selected.id : null } as Collection);
    }
    this.subscriptions.add(ob.subscribe({
      next: data => {
        this.collectionsStoreService.setRefreshCollections(true);
        this.collectionsStoreService.setRefreshFolders(true);
        this.activeModal.close(data);
      }, error: ({error}) => {
        console.error(error.message);
        this.errors = [error.message];
      }
    }));
  }

  private loadFolders(folderId: number) {
    this.isLoading = true;

    const getFolders$ = this.collectionService.getFolders({folder_id: folderId}).pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: ({folders, page}) => {
          this.folders = this.isFolder ? folders.filter(f=> f.id !== this.resource.id) :folders;
        },
        error: error => { console.error(error); }
      });
    this.subscriptions.add(getFolders$);
  }

  onBack(){
    this.selectedList.pop();
    const lastElement = this.selectedList.length>0? this.selectedList.pop(): null;
    this.selectFolder(lastElement);
  }
}
