<div *ngIf="!isLoading else loader">
  <div class="" *ngIf="listPublications.length else noDocuments">
    <div class="d-flex flex-column">
      <app-page-bar pageTitle="Opened documents">
        <div leftSide></div>
        <ng-container rightSide>
          <a href="javascript:void(0)" (click)="markSelectedAsUnread()" class="item-bar icon"
             [ngClass]="{'disabled': !hasSelectedPatents()}">
            Mark {{ storeService.selectedPatentIds.length }} selected as not opened
          </a>

          <a href="javascript:void(0)" (click)="markAllAsUnread()" class="item-bar icon">
            Mark all as not opened
          </a>
        </ng-container>
      </app-page-bar>

      <app-patent-table #patentTable [patents]="listPublications" [pagination]="pagination"
        [hasLinksToBooleanSearch]="true"
        [linkData]="linkData" pathUrl="/patent" backButtonTitle="Back to opened documents"
        [storeService]="storeService" [isSearch]="false"
        idPagination="opened-documents-pagination"
        [allowSelectAllDocuments]="false"
        [showIndexColumn]="false"
        [allowFilteringByLegalStatus]="false"
        (markAsUnread)="markAsUnread($event)"
        (sort)="onSortEvent($event)">
      </app-patent-table>

      <div class="d-flex justify-content-between mt-4 align-items-center">
        <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" [pageOptions]="pageOptions"></app-page-size>

        <div class="flex-fill">
          <pagination-controls id="opened-documents-pagination" class="d-flex justify-content-end"
                                (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noDocuments>
    <div class="d-flex justify-content-center">
      <h5>No documents have been recently opened by you</h5>
    </div>
  </ng-template>
</div>
<ng-template #loader>
  <div class="d-flex justify-content-center">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</ng-template>
