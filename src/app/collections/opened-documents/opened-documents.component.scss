@import 'scss/layout2021/variables';

.publication-table {
    th, td {
        .caret-down {
          color: #389A85;
        }

        .caret-up {
          color: #236B5B;
        }
    }
}

.green-dot {
    color: #389A85;
}
.grey-dot {
    color: #ccc;
}

.item-bar {
  padding-left: 25px;
  background-position: 0;
  background-size: 15px;
  background-repeat: no-repeat;
  transition: all 0.2s ease;

  &.icon {
    background-image:  url('/assets/images/layout2022/icon-delete.svg');
    &:hover,&.active {
      background-image:  url('/assets/images/layout2022/icon-delete-hover.svg');
    }
    &.disabled {
      background-image:  url('/assets/images/layout2022/icon-delete-disabled.svg');
    }
  }

}
