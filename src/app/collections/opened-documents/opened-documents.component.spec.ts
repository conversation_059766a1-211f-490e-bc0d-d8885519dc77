import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbAlertModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';

import { OpenedDocumentsComponent } from './opened-documents.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('OpenedDocumentsComponent', () => {
  let component: OpenedDocumentsComponent;
  let fixture: ComponentFixture<OpenedDocumentsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OpenedDocumentsComponent],
      imports: [
        SharedModule,
        NgbAlertModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OpenedDocumentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
