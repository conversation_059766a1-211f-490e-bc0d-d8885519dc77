import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  ConfirmationDialogService,
  PaginationMetadata,
  PatentViewModeService,
  ReadDocumentsService
} from '@core/services';
import { columnsToShow } from './columns';
import { take } from 'rxjs/operators';
import { OpenedDocumentStoreService } from '@core/store/opened-document-store/opened-document-store.service';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-opened-documents',
  templateUrl: './opened-documents.component.html',
  styleUrls: ['./opened-documents.component.scss']
})
export class OpenedDocumentsComponent implements OnInit, OnDestroy {

  public patentViewModeAnnotation: string = PatentSideBarViewModeEnum.MODE_OPENED_DOCUMENTS;

  listPublications = [];
  isLoading = true;
  pageSize = 25;
  pageOptions = [25, 50, 100];
  pagination: PaginationMetadata = {} as PaginationMetadata;
  paginationSaved: PaginationMetadata;
  paginationHistory: PaginationMetadata;

  private subscriptions = new Subscription();

  constructor(
    public readDocumentsService: ReadDocumentsService,
    private router: Router,
    private confirmationDialogService: ConfirmationDialogService,
    public storeService: OpenedDocumentStoreService,

  ) {
  }

  ngOnInit(): void {
    this.storeService.selectedColumnsToShow = columnsToShow;
    this.loadDocuments();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  queryParamsForPatentViewer() {
    return {
      mode: this.patentViewModeAnnotation,
      previousUrl: this.router.url,
      backButtonTitle: 'Back to opened documents'
    };
  }

  markAsUnread(docdb_family_id: number) {
    if (!docdb_family_id) {
      return;
    }
    const markAsUnread$ = this.readDocumentsService.markAsUnread(docdb_family_id)
      .pipe(take(1))
      .subscribe({next: () => { this.loadDocuments(); }, error: err => { console.log(err); }});
    this.subscriptions.add(markAsUnread$);
  }

  markAllAsUnread() {
    const title = 'Mark documents as not opened';
    const message = `Are you sure you want to mark all documents as not opened?`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        const markAsUnread$ = this.readDocumentsService.markAllAsUnread()
          .pipe(take(1))
          .subscribe({next: () => { this.loadDocuments(); }, error: err => { console.log(err); }});
        this.subscriptions.add(markAsUnread$);
      }
    });
  }

  markSelectedAsUnread() {
    const markSelectedAsUnread$ = this.readDocumentsService.markSelectedAsUnread(this.storeService.selectedPatentIds)
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.storeService.selectedPatentIds = [];
          this.loadDocuments();
        },
        error: err => {
          console.log(err)
        }
      });
    this.subscriptions.add(markSelectedAsUnread$);
  }

  onPageChange($event: number) {
    const {current_page} = this.pagination;

    if (current_page === $event) {
      return;
    }

    this.pagination.current_page = $event;
    this.loadDocuments();
  }

  onChangePageSize(event: number): void {
    this.pageSize = event;
    this.pagination.current_page = 1;
    this.loadDocuments();
  }

  private loadDocuments() {
    this.isLoading = true;
    const payload = {
      page_size: this.pageSize,
      page: this.pagination.current_page ? this.pagination.current_page : 1,
      show_general: 1,
    };
    if (this.storeService.patentTableSort) {
      payload['sort_by'] = this.storeService.patentTableSort.field;
      payload['sort_order'] = this.storeService.patentTableSort.order;
    }
    const getDocuments$ = this.readDocumentsService.getDocuments(payload)
      .pipe(take(1))
      .subscribe({
        next: response => {
          this.pagination = response.data.page;
          this.listPublications = response.data.documents;
          this.isLoading = false;
        if (this.storeService.docdbFamilyIdFromPatentViewer) {
        this.scrollToPatentFromPatentViewer();
      }}, error: err => {
          this.isLoading = false;
          console.log(err);
        }
      });
    this.subscriptions.add(getDocuments$);
  }

  private scrollToPatentFromPatentViewer() {
    setTimeout(() => {
      const patentRow = document.getElementById(this.storeService.docdbFamilyIdFromPatentViewer);
      if (patentRow) {
        window.scrollTo(0, patentRow.getBoundingClientRect().top);
        this.storeService.docdbFamilyIdFromPatentViewer = null;
      }
    });
  }

  hasSelectedPatents(): boolean {
    return !!(this.storeService.selectedPatentIds.length) ;
  }

  onSortEvent(params: any): void {
    this.loadDocuments();
  }
}
