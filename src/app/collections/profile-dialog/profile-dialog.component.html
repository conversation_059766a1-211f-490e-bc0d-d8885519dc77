<div class="modal-header">
    <div class="modal-title">Create new landscape profile</div>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
    <app-alert type="danger" [message]="errorMessage" *ngIf="errorMessage"></app-alert>

    <div class="d-flex flex-column justify-content-between">
        <label class="label2">Name</label>
        <input class="form-control mb-2" [(ngModel)]="profileName"/>

        <label class="label2">Category</label>
        <input class="form-control" [(ngModel)]="profileCategory"/>
    </div>

</div>
<div class="modal-footer">
    <button (click)="activeModal.dismiss('Cancel')" class="btn btn-primary-outline btn-md" type="button">Cancel</button>
    <button (click)="activeModal.close({'profile_name': profileName, 'profile_category': profileCategory})" [disabled]="!profileName"
        class="btn btn-primary btn-md" type="button">Proceed to landscape</button>
</div>
