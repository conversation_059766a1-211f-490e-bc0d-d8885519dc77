import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { animate, group, state, style, transition, trigger } from '@angular/animations';

export enum FilterOperatorEnum {
  TEXT_EQ = 'eq',
  TEXT_LIKE = 'like',
  TEXT_NE = 'ne',

  COMPARABLE_EQ = 'eq',
  COMPARABLE_LT = 'lt',
  COMPARABLE_LTE = 'lte',
  COMPARABLE_GT = 'gt',
  COMPARABLE_GTE = 'gte',
  COMPARABLE_NE = 'ne',
}

export enum FilterOperatorTypeEnum {
  TEXT,
  DATE,
  NUMBER
}

export class FilterParam {
  name: string;
  operator: FilterOperatorEnum;
  value: string | NgbDate;
  type: FilterOperatorTypeEnum;

  constructor(name: string, operator: FilterOperatorEnum, value: string, type: FilterOperatorTypeEnum) {
    this.name = name;
    this.operator = operator;
    this.value = value;
    this.type = type;
  }

  getFormattedValue(): string {
    if (!this.value) {
      return null;
    }

    if (this.type === FilterOperatorTypeEnum.TEXT) {
      return (this.value?.toString()).trim();
    }

    if (this.type === FilterOperatorTypeEnum.DATE) {
      const val = this.value as NgbDate;
      const month = val.month.toString().padStart(2, '0');
      const day = val.day.toString().padStart(2, '0');
      return `${val.year}-${month}-${day}T00:00:00`;
    }

    return this.value.toString();
  }

  buildQuery(): string {
    const val = this.getFormattedValue();

    if (!val) {
      return null;
    }

    switch (true) {
      case [FilterOperatorEnum.TEXT_LIKE].includes(this.operator):
        return `${this.operator}:%${val}%`;
      default:
        return `${this.operator}:${val}`;
    }
  }
}

export const SlideInOutAnimations = [
  trigger('slideInOut', [
    state('in', style({
      'max-height': '500px', 'opacity': '1', 'visibility': 'visible'
    })),
    state('out', style({
      'max-height': '0px', 'opacity': '0', 'visibility': 'hidden'
    })),
    transition('in => out', [group([
        animate('400ms ease-in-out', style({
          'opacity': '0'
        })),
        animate('600ms ease-in-out', style({
          'max-height': '0px'
        })),
        animate('700ms ease-in-out', style({
          'visibility': 'hidden'
        }))
      ]
    )]),
    transition('out => in', [group([
        animate('1ms ease-in-out', style({
          'visibility': 'visible'
        })),
        animate('600ms ease-in-out', style({
          'max-height': '500px'
        })),
        animate('800ms ease-in-out', style({
          'opacity': '1'
        }))
      ]
    )])
  ])
];

export enum CollectionsCategory {
  DEFAULT = 'default',
  SHARED_WITH_ME = 'shared_with_me',
  MONITOR_RESULTS = 'monitor_results',
  TAGGED = 'tagged'
}

export const showIntermediateFolders = (c: CollectionsCategory) => {
  return [CollectionsCategory.DEFAULT, CollectionsCategory.MONITOR_RESULTS, CollectionsCategory.TAGGED].includes(c);
}

export const isCategoryNotDefined = (c: CollectionsCategory) => {
  return !Object.values(CollectionsCategory).includes(c);
}
