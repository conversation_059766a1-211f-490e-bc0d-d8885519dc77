import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AuthGuard } from './guards';
import { HttpTokenInterceptor } from './interceptors';
import { ApiService, JwtService } from '@core/services';
import { RouterModule } from '@angular/router';
import { FlexLayoutModule } from '@angular/flex-layout';

export const CUSTOM_BREAKPOINTS = [
  {
    alias: 'xs',
    mediaQuery: 'screen and (max-width: 359px)',
    overlapping: true,
    priority: 1001
  },
  {
    alias: 'sm',
    mediaQuery: 'screen and (min-width: 360px) and (max-width: 833px)',
    overlapping: true,
    priority: 1001
  },
  {
    alias: 'md',
    mediaQuery: 'screen and (min-width: 834px) and (max-width: 949px)',
    overlapping: true,
    priority: 1001
  },
  {
    alias: 'lg',
    mediaQuery: 'screen and (min-width: 950px) and (max-width: 1023px)',
    overlapping: true,
    priority: 1001
  },
  {
    alias: 'xl',
    mediaQuery: 'screen and (min-width: 1024px) and (max-width: 1227px)',
    overlapping: true,
    priority: 1001
  },
  {
    alias: 'xxl',
    mediaQuery: 'screen and (min-width: 1228px)',
    overlapping: true,
    priority: 1001
  },
];

@NgModule({
  imports: [
    CommonModule,
    HttpClientModule,
    BrowserAnimationsModule,
    RouterModule,
    FlexLayoutModule.withConfig({disableDefaultBps: true}, CUSTOM_BREAKPOINTS)
  ],
  providers: [
    {provide: HTTP_INTERCEPTORS, useClass: HttpTokenInterceptor, multi: true},
    AuthGuard,
    ApiService,
    JwtService
  ]
})
export class CoreModule {
}
