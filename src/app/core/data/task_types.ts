import { TaskTypeEnum, TaskTypeItem } from '@core/models';

export const TASK_TYPES = [
  {
    value: TaskTypeEnum.STAR_RATING,
    label: 'Star rating',
    icon: 'fas fa-star-half-alt',
    description: 'Requires star rating'
  } as TaskTypeItem,
  {
    value: TaskTypeEnum.TEXT_REPLY,
    label: 'Reply',
    icon: 'fas fa-reply',
    description: 'Requires reply'
  } as TaskTypeItem,
  {
    value: TaskTypeEnum.LABELS,
    label: 'Tags',
    icon: 'fas fa-pen-fancy',
    description: 'Requires to add tags'
  } as TaskTypeItem,
  {
    value: TaskTypeEnum.YES_NO_ANSWER,
    label: 'Yes / no answer',
    icon: 'far fa-hand-point-up',
    description: 'Requires YES / NO answer'
  } as TaskTypeItem,
];
