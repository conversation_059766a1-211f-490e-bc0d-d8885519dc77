export const translatableLanguages = [
  {code: 'E<PERSON>', name: 'English'},
  {code: 'B<PERSON>', name: 'български'},
  {code: '<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>'},
  {code: 'D<PERSON>', name: '<PERSON><PERSON>'},
  {code: '<PERSON>', name: '<PERSON><PERSON><PERSON>'},
  {code: 'E<PERSON>', name: 'Ελληνικά'},
  {code: 'ES', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'},
  {code: 'ET', name: '<PERSON><PERSON><PERSON>'},
  {code: 'FI', name: '<PERSON><PERSON>'},
  {code: 'FR', name: 'Fran<PERSON>'},
  {code: 'HU', name: '<PERSON><PERSON><PERSON>'},
  {code: 'ID', name: 'Bahasa Indonesia'},
  {code: 'IT', name: 'Italiano'},
  {code: 'J<PERSON>', name: '日本語'},
  {code: 'KO', name: '한국어'},
  {code: 'LT', name: '<PERSON><PERSON><PERSON><PERSON>'},
  {code: 'L<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'},
  {code: 'NB', name: 'Nor<PERSON>'},
  {code: '<PERSON>', name: 'Nederlands'},
  {code: 'P<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>'},
  {code: '<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'},
  {code: 'RO', name: 'Română'},
  {code: 'RU', name: 'Русский'},
  {code: 'SK', name: 'Slovenčina'},
  {code: 'SL', name: 'Slovenščina'},
  {code: 'SV', name: 'Svenska'},
  {code: 'TR', name: 'Türkçe'},
  {code: 'UK', name: 'Українська'},
  {code: 'ZH', name: '中文'}
];

export const ENGLISH_CODE = 'EN';
export const ENGLISH_LANG = translatableLanguages.find(lang => lang.code === ENGLISH_CODE);
export const ASIAN_ENGLISH_CODES = ['JA', 'KO', 'ZH'];

let sortedTranslatableLanguages = translatableLanguages.sort((a, b) => a.name.localeCompare(b.name));
sortedTranslatableLanguages = sortedTranslatableLanguages.filter(lang => lang.code !== ENGLISH_CODE);
sortedTranslatableLanguages.unshift(ENGLISH_LANG);
export {sortedTranslatableLanguages};

