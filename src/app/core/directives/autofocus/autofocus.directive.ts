import { AfterViewInit, Directive, ElementRef, Input } from '@angular/core';

@Directive({
  selector: '[appAutofocus]'
})
export class AutofocusDirective implements AfterViewInit {
  constructor(private el: ElementRef) {
  }

  private _autoFocusEnabled: boolean = true;

  get autoFocusEnabled(): boolean {
    return this._autoFocusEnabled;
  }

  @Input() set autoFocusEnabled(val) {
    this._autoFocusEnabled = val;
    this.focus();
  };

  ngAfterViewInit() {
    this.focus();
  }

  private focus() {
    const element = this.el.nativeElement;
    if (this.autoFocusEnabled && element) {
      setTimeout(() => {
        element.focus();
      }, 100);
    }
  }
}
