import { Directive, ElementRef, HostListener, OnInit } from '@angular/core';

@Directive({
  selector: '[appTextareaAutoresize]'
})
export class TextareaAutoresizeDirective implements OnInit {

  private defaultHeight = '38px';

  constructor(private elementRef: ElementRef) {
  }

  @HostListener(':input')
  onInput() {
    this.resize();
  }

  ngOnInit() {
    setTimeout(() => this.resize());
  }

  resize() {
    if (this.elementRef.nativeElement.scrollHeight) {
      this.elementRef.nativeElement.style.height = '0';
      this.elementRef.nativeElement.style.height = (this.elementRef.nativeElement.scrollHeight + 2) + 'px';
    } else {
      this.elementRef.nativeElement.style.height = this.defaultHeight;
    }
  }
}
