import {AfterViewInit, Directive, ElementRef, Input} from '@angular/core';

@Directive({
  selector: '[appBeta]'
})
export class BetaDirective implements AfterViewInit {

  @Input() betaPadding = '0px 7px 2px 7px';
  @Input() betaMargin = '2px 0px 0px 10px';
  @Input() betaBackgroundColor = '#c91515';
  @Input() betaColor = 'white';
  @Input() betaCustomCss = '';

  constructor(private elementRef: ElementRef) {}

  ngAfterViewInit(): void {
    this.addBetaTag();
  }

  addBetaTag() {
    const tag = document.createElement('div');
    const text = document.createElement('span');

    const lineHeight = window.getComputedStyle(this.elementRef.nativeElement).fontSize;
    const style = `background-color: ${this.betaBackgroundColor}; padding: ${this.betaPadding}; font-size: 10px;
                          color: ${this.betaColor}; margin: ${this.betaMargin}; border-radius: 5px; line-height: ${lineHeight}; ${this.betaCustomCss}`;
    tag.setAttribute('style', style);

    text.innerHTML = 'BETA';
    tag.classList.add('d-flex', 'align-items-center');
    tag.appendChild(text);

    this.elementRef.nativeElement.classList.add('d-flex', 'align-items-center');
    this.elementRef.nativeElement.appendChild(tag);
  }

}
