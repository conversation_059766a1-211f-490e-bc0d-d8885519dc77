import { Directive, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';

@Directive({
  selector: '[appClickOutside]'
})
export class ClickOutsideDirective implements OnInit, OnDestroy {
  @Input() excludedClasses: string[] = [];
  @Input() clickOutsideDisabled: boolean = false;
  @Output() clickOutside = new EventEmitter<MouseEvent>();

  private readonly handleClick: (event: MouseEvent) => void;

  constructor() {
    this.handleClick = this.onClick.bind(this);
  }

  ngOnInit() {
    if (!this.clickOutsideDisabled) {
      document.addEventListener('click', this.handleClick);
    }
  }

  ngOnDestroy() {
    if (!this.clickOutsideDisabled) {
      document.removeEventListener('click', this.handleClick);
    }
  }

  private onClick(event: MouseEvent) {
    for (let i = 0; i < event.composedPath().length; i++) {
      const ele = event.composedPath()[i] as HTMLElement;
      if (this.excludedClasses.some((cls) => ele.classList?.contains(cls))) {
        return;
      }
    }

    this.clickOutside.emit(event);
  }
}
