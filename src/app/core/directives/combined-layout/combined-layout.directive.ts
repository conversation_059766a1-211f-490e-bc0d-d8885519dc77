import { AfterViewInit, Directive, ElementRef, HostListener } from '@angular/core';

declare var $: any;

@Directive({
  selector: '[appCombinedLayout]'
})
export class CombinedLayoutDirective implements AfterViewInit  {

  constructor(private elementRef: ElementRef) { }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll($event: any) {
    this.adjustChartSection();
  }

  ngAfterViewInit(): void {
    this.adjustChartSection();
  }

  adjustChartSection(){
    const listHeight = $(".combined-mode-layout .results-table-container").height();
    if(listHeight){
      $('.combined-mode-layout .combined-chart-section .charts-container').css({"maxHeight":listHeight+"px"});
    }
  }
}
