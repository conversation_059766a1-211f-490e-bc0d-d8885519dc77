import { AfterViewInit, Directive, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';

@Directive({
  selector: '[appDropdownToggle]'
})
export class DropdownToggleDirective implements AfterViewInit {
  @Input() closeOnClick: boolean = false;
  @Input() showOnHover: boolean = false;
  @Output() toggleEvent = new EventEmitter<boolean>();
  @Output() dropdownMenuStateChange = new EventEmitter<boolean>();

  private hideDropdownMenuTimeoutId = null;
  private hideDropdownMenuTimeout = 700;

  constructor(public el: ElementRef) {
  }

  @Input()
  set dropdownMenuState(val: boolean) {
    this.clearHideDropdownMenuTimeout();

    if (val) {
      this.el.nativeElement.parentNode.classList.add('show');
    } else {
      this.el.nativeElement.parentNode.classList.remove('show');
    }
  }

  ngAfterViewInit() {
    this.hideDropdownMenuOnMouseleave();
  }

  @HostListener('click')
  onClick() {
    this.toggle();
  }

  @HostListener('mouseover')
  onMouseOver() {
    if (this.isOpen() || !this.showOnHover) {
      return;
    }
    this.toggle();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const element = event.target as HTMLElement;
    const isItemClicked = element.classList.contains('figma-dropdown-item') || element.closest('.figma-dropdown-item');
    if (this.closeOnClick && isItemClicked) {
      this.el.nativeElement.parentNode.classList.remove('show');
      this.toggleEvent.emit(this.isOpen());
      this.dropdownMenuStateChange.emit(this.isOpen());
    } else {
      const clickedInside = this.el.nativeElement.parentNode.contains(event.target);
      if (!clickedInside && this.isOpen()) {
        this.el.nativeElement.parentNode.classList.toggle('show');
        this.toggleEvent.emit(this.isOpen());
        this.dropdownMenuStateChange.emit(this.isOpen());
      }
    }
  }

  private toggle() {
    this.clearHideDropdownMenuTimeout();

    this.el.nativeElement.parentNode.classList.toggle('show');
    this.toggleEvent.emit(this.isOpen());
    this.dropdownMenuStateChange.emit(this.isOpen());
  }

  private isOpen(): boolean {
    return this.el.nativeElement.parentNode.classList.contains('show');
  }

  private hideDropdownMenuOnMouseleave() {
    if (!this.showOnHover) {
      return;
    }

    const dropdownContentEle = this.el.nativeElement.parentNode.querySelector('.figma-dropdown-content') as HTMLElement;
    if (dropdownContentEle) {
      dropdownContentEle.removeEventListener('mouseleave', () => {
      });
      dropdownContentEle.removeEventListener('mouseover', () => {
      });
      dropdownContentEle.addEventListener('mouseover', (event: MouseEvent) => {
        this.clearHideDropdownMenuTimeout();
      });
      dropdownContentEle.addEventListener('mouseleave', (event: MouseEvent) => {
        this.clearHideDropdownMenuTimeout();

        this.hideDropdownMenuTimeoutId = setTimeout(() => {
          this.el.nativeElement.parentNode.classList.remove('show');
          this.toggleEvent.emit(this.isOpen());
          this.dropdownMenuStateChange.emit(this.isOpen());
        }, this.hideDropdownMenuTimeout);
      });
    }
  }

  private clearHideDropdownMenuTimeout() {
    if (this.hideDropdownMenuTimeoutId) {
      clearTimeout(this.hideDropdownMenuTimeoutId);
      this.hideDropdownMenuTimeoutId = null;
    }
  }
}
