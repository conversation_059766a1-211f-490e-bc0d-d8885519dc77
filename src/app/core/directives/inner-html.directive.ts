import { Directive, ElementRef, Input, OnChanges, SimpleChanges, ViewContainerRef } from '@angular/core';
import { Location } from '@angular/common';
import { SmartHighlightItem } from '@core/services';
import copy from 'copy-to-clipboard';
import { TextUtil } from '@core/utils';
import { SmartHighlightIconComponent } from '@shared/components/smart-highlight-icon/smart-highlight-icon.component';

@Directive({
  selector: '[appInnerHtml]'
})
export class InnerHtmlDirective implements OnChanges {

  @Input('appInnerHtml') textHtml: string;
  @Input() displayHeadingSections = false;
  @Input() displaySectionCollapses = true;
  @Input() displaySectionAnchors = false;
  @Input() sectionsQuerySelector = 'section';
  @Input() headingsQuerySelector = 'h1, h2, h3';
  @Input() sectionCollapseClass = '';
  @Input() smartHighlightItem: SmartHighlightItem = null;
  @Input() smartHighlightItems: SmartHighlightItem[] = [];
  @Input() highlightInformationContainerCssSelector: string = null;
  @Input() figmaClass: boolean = false;

  private readonly headingTags = new Set(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']);

  constructor(
    private el: ElementRef,
    private location: Location,
    public viewContainerRef: ViewContainerRef
  ) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ('textHtml' in changes) {
      const changedTextHtml = changes.textHtml.currentValue || '';
      const textNode = document.createElement('div');
      textNode.innerHTML = changedTextHtml;

      this.el.nativeElement.replaceChildren(textNode);

      if (changedTextHtml.trim().length > 0) {
        this.decorateHeadingSections(textNode);
        this.setInnerStyleForHeadingSections(textNode);
      }

      this.decorateHighlights(
        this.el.nativeElement,
        this.smartHighlightItems, this.smartHighlightItem
      );
    }

    if ('smartHighlightItems' in changes) {
      this.decorateHighlights(
        this.el.nativeElement,
        changes.smartHighlightItems.currentValue, null
      );
    }

    if ('smartHighlightItem' in changes) {
      this.decorateHighlights(
        this.el.nativeElement,
        null,
        changes.smartHighlightItem.currentValue
      );
    }
  }

  private decorateHeadingSections(textNode: HTMLElement) {
    if (this.displayHeadingSections) {
      textNode.querySelectorAll(this.sectionsQuerySelector).forEach((sectionEle) => {
        this.decorateSectionElement(sectionEle as HTMLElement);
      });
      textNode.querySelectorAll(this.headingsQuerySelector).forEach((headingEle) => {
        this.decorateHeadingElement(headingEle as HTMLElement);
      });
    }
  }

  private decorateSectionElement(sectionEle: HTMLElement) {
    const firstChild = sectionEle.childNodes?.length ? sectionEle.childNodes[0] as HTMLElement : null;

    if (!firstChild) {
      return;
    }

    if (firstChild.nodeType === Node.TEXT_NODE || !this.headingTags.has(firstChild.tagName.toLowerCase())) {
      const wrapperEle = document.createElement('div');
      wrapperEle.className = 'desc-paragraph p-t-spacing-xx-big';

      sectionEle.dataset.originId = sectionEle.id;
      sectionEle.id = 'section-' + TextUtil.genRandomString();
      sectionEle.childNodes.forEach((childNode) => {
        wrapperEle.appendChild(childNode.cloneNode(true));
      });

      sectionEle.replaceChildren(wrapperEle);
    }
  }

  private decorateHeadingElement(headingEle: HTMLElement) {
    headingEle.id = 'section-heading-' + headingEle.innerText.replace(/\W/g, '').toLowerCase();
    headingEle.className = 'd-flex justify-content-between align-items-center';

    if (headingEle.innerText?.length > 0) {
      let headingTitle = headingEle.innerText || ' ';
      const titleEle = document.createElement('div');
      titleEle.innerHTML = `<div ${!this.figmaClass ? 'class="ps-1"': ''}>` + headingTitle + '</div>';
      titleEle.className = `${!this.figmaClass ? 'ms-1' : ''} heading-title d-flex justify-content-start align-items-center`;
      titleEle.id = 'section-title-' + TextUtil.genRandomString();

      headingEle.replaceChildren(titleEle);
    }

    const collapseIconEle = this.buildCollapseIconElement(headingEle);
    headingEle.insertBefore(collapseIconEle, headingEle.firstChild);

    if (this.displaySectionAnchors) {
      const anchorIconEle = this.buildAnchorIconElement(headingEle);

      headingEle.appendChild(anchorIconEle);
    }
  }

  private setInnerStyleForHeadingSections(textNode: HTMLElement): void {
    const headingTags = ['section', 'h1', 'h2', 'h3'];
    textNode.querySelectorAll(this.headingsQuerySelector).forEach((headingEle) => {
      let sectionContentEle = headingEle.nextElementSibling;

      if (!sectionContentEle || headingTags.includes(sectionContentEle.tagName.toLowerCase())) {
        sectionContentEle = headingEle.closest('section')?.nextElementSibling;
      }

      while (sectionContentEle && !headingTags.includes(sectionContentEle.tagName.toLowerCase())) {
        if (sectionContentEle.tagName.toLowerCase() !== 'h1') {
          sectionContentEle = sectionContentEle as HTMLElement;
          const nextElementSibling = sectionContentEle.nextElementSibling;

          if (sectionContentEle.classList.contains('desc-paragraph')) {
            const replacementSectionEle = sectionContentEle.cloneNode(true) as HTMLElement;
            const wrappedSectionContainerEle = document.createElement('div');
            wrappedSectionContainerEle.id = 'section-content-tmp-' + TextUtil.genRandomString();
            wrappedSectionContainerEle.className = this.getFigmaClass() + 'section-content';
            wrappedSectionContainerEle.append(replacementSectionEle);

            sectionContentEle.parentNode.replaceChild(wrappedSectionContainerEle, sectionContentEle);
          } else {
            sectionContentEle.classList.add(this.getFigmaClass() + 'section-content');
          }

          sectionContentEle = nextElementSibling;
        }
      }
    });
  }

  private buildAnchorIconElement(headingEle: HTMLElement): HTMLElement {
    if (!this.displaySectionAnchors) {
      return null;
    }

    const anchorIconEleId = 'section-anchor-' + headingEle.innerText.replace(/\W/g, '').toLowerCase();
    const anchorIconEle = document.createElement('i');
    anchorIconEle.className = 'fa fa-link cursor-pointer me-3 d-none heading-actions';
    anchorIconEle.title = 'Click to copy to clipboard';
    anchorIconEle.id = anchorIconEleId;

    this.el.nativeElement.addEventListener('click', (evt) => {
      if ((evt.target as HTMLElement).id === anchorIconEleId) {
        const url = new URL(window.location.href);
        url.searchParams.set('anchorId', headingEle.id);
        copy(url.href);
        this.location.replaceState(url.href.replace(window.location.protocol + '//' + window.location.host, ''));
      }
    }, false);

    return anchorIconEle;
  }

  private buildCollapseIconElement(headingEle: HTMLElement): Element {
    if (!this.displaySectionCollapses) {
      return null;
    }

    const collapseIconEleId = 'section-collapse-' + TextUtil.genRandomString();
    const collapseIconEle = document.createElement('i');
    const iconType = this.figmaClass ? 'fa-solid' : 'fa';
    const iconDown = this.figmaClass ? 'fa-caret-down' : 'fa-angle-down';
    const iconRight = this.figmaClass ? 'fa-caret-right' : 'fa-angle-right';

//    collapseIconEle.className = `${iconType} ${iconDown} cursor-pointer section-collapse ms-2 ${!this.figmaClass ? 'me-1' : ''}  ${this.sectionCollapseClass}`;
    collapseIconEle.className = `${iconType} ${iconDown} cursor-pointer section-collapse ${!this.figmaClass ? 'me-1' : ''}  ${this.sectionCollapseClass}`;
    collapseIconEle.id = collapseIconEleId;
    return collapseIconEle;
  }

  private decorateHighlights(rootNode: HTMLDivElement,
                             smartHighlightItems: SmartHighlightItem[],
                             smartHighlightItem: SmartHighlightItem) {
    if (smartHighlightItems?.length > 0 || smartHighlightItem) {
      rootNode.querySelectorAll('.desc-paragraph, .claims .claim .claim-wrap').forEach((paragraphEle: HTMLElement, i) => {
        const existingHighlightBoxEle = paragraphEle.querySelector('.highlight-paragraph');
        if (!existingHighlightBoxEle) {
          const shItem = smartHighlightItem || smartHighlightItems.find((w) => {
            return w.paragraph.id === paragraphEle.id || paragraphEle.closest('.claim')?.id === w.paragraph.id;
          });

          if (shItem) {
            const highlightBoxesEle = this.generateHighlightBoxes(shItem);
            const highlightInformationEle = this.generateHighlightInformation(shItem);

            const paragraphContentContainerEle = document.createElement('div');
            paragraphContentContainerEle.innerHTML = paragraphEle.innerHTML
              .replace(/^[\n\s]*(\<br\s*\/?\>)+/gi, '')
              .replace(/(\<br\s*\/?\>)+[\n\s]*$/gi, '');
            paragraphContentContainerEle.className = 'highlight-paragraph flex-fill';

            const mainContainerEle = document.createElement('div');
            mainContainerEle.className = this.getFigmaClass() + `highlight-container d-flex justify-content-start align-items-start`;
            if (this.figmaClass && document.getElementsByClassName('claims-tree').length > 0) {
              mainContainerEle.className += ' no-tree-claim';
            }
            const highlightBoxId = 'highlight-box-' + shItem.doc_field + '-' + shItem.paragraph.id;
            if (smartHighlightItem) {
              mainContainerEle.dataset.highlightBoxId = highlightBoxId;
            } else {
              mainContainerEle.id = highlightBoxId;
            }
            if (!this.figmaClass) {
              mainContainerEle.append(highlightBoxesEle);
            }
            mainContainerEle.append(paragraphContentContainerEle);
            if (this.figmaClass) {
              const targetEle = document.createElement('div');
              targetEle.className = 'target';
              const componentRef = this.viewContainerRef.createComponent(SmartHighlightIconComponent);
              targetEle.appendChild(componentRef.location.nativeElement);

              mainContainerEle.append(targetEle);
            } else {
              if (this.highlightInformationContainerCssSelector) {
                const highlightInformationContainerEle = rootNode.closest(this.highlightInformationContainerCssSelector);
                if (highlightInformationContainerEle) {
                  highlightInformationContainerEle.append(highlightInformationEle);
                }
              } else {
                mainContainerEle.append(highlightInformationEle);
              }
            }

            if (paragraphEle.className?.length > 0) {
              const replacementContainerEle = document.createElement('div');
              replacementContainerEle.className = paragraphEle.className;
              replacementContainerEle.append(mainContainerEle);

              paragraphEle.parentNode.replaceChild(replacementContainerEle, paragraphEle);
            } else {
              paragraphEle.parentNode.replaceChild(mainContainerEle, paragraphEle);
            }
          }
        }
      });
    }
  }

  /**
   * TODO: Will use this after fine-tune the relevant score rank
   * @param smartHighlightItem
   * @private
   */
  private getRelevantLevel(smartHighlightItem: SmartHighlightItem): number {
    const label = smartHighlightItem.label.toLowerCase();
    switch (label) {
      case 'highly relevant':
        return 3;
      case 'relevant':
        return 2;
      case 'slightly relevant':
        return 1;
    }
    return 0;
  }

  private generateHighlightBoxes(smartHighlightItem: SmartHighlightItem): HTMLElement {
    const countHighlightBoxes = 5;

    const highlightBoxesEle = document.createElement('div');
    highlightBoxesEle.className = 'highlight-box-container d-flex flex-column justify-content-between align-items-center';

    for (let i = countHighlightBoxes; i > 0; i--) {
      const highlightBoxEle = document.createElement('div');
      highlightBoxEle.className = 'highlight-box highlight-' + i;
      highlightBoxesEle.append(highlightBoxEle);
    }

    return highlightBoxesEle;
  }

  /**
   * TODO: Label of weightItem will be used after fine-tune
   * @param weightItem
   * @private
   */
  private generateHighlightInformation(weightItem: SmartHighlightItem): HTMLElement {
    const informationBoxEle = document.createElement('div');
    informationBoxEle.className = 'highlight-information';
    informationBoxEle.innerText = 'Relevant part';

    return informationBoxEle;
  }

  private getFigmaClass(): string {
    return this.figmaClass ? 'figma-' : '';
  }
}
