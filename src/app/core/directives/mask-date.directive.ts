import { Directive, ElementRef, Input, OnDestroy } from '@angular/core';
import * as textMask from 'vanilla-text-mask/dist/vanillaTextMask.js';
import { createAutoCorrectedDatePipe } from 'text-mask-addons/dist/textMaskAddons';
import { UserService } from '@core/services';

@Directive({
  selector: '[appMaskDate]'
})
export class MaskDateDirective implements OnDestroy {

  @Input() mask: { mask: any[]; keepCharPositions: boolean; pipe: any; };

  private maskedInputController;
  private defaultDateFormat = 'DD-MM-YYYY';

  get dateFormat(): string {
    return this.userService.getLocale()?.dateFormatting || this.defaultDateFormat;
  }

  constructor(private element: ElementRef, private userService: UserService ) {
    if (!this.mask) {
      this.setMask();
    }
    this.element.nativeElement.setAttribute("placeholder", this.dateFormat.toUpperCase());
    this.maskedInputController = textMask.maskInput({
      inputElement: this.element.nativeElement,
      mask: this.mask
    });
  }

  private setMask() {
    const separator = this.dateFormat.match(/[-./]/)[0];
    const dateMarkParts = this.dateFormat.split(/[-./]/).map(part => {
      part = part.toLowerCase();
      if (part === 'yyyy') {
        return [/[1-9]/, /\d/, /\d/, /\d/];
      }
      if (part === 'mm') {
        return [/\d/, /\d/];
      }
      if (part === 'dd') {
        return [/\d/, /\d/];
      }
    });
    const mask = [];
    dateMarkParts.forEach((part, index) => {
      mask.push(...part);
      if (index < dateMarkParts.length - 1) {
        mask.push(separator);
      }
    });

    this.mask = {
      mask: mask,
      keepCharPositions: true,
      pipe: createAutoCorrectedDatePipe(this.dateFormat.toLowerCase())
    }
  }

  ngOnDestroy() {
    this.maskedInputController.destroy();
  }
}
