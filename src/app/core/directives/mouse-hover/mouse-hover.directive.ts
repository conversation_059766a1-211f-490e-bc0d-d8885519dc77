import { Directive, ElementRef, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[appMouseHover]'
})
export class MouseHoverDirective {
  @Input() hoverClass: string = 'hovered'; // Default class name if none provided
  @Input() hoverDisabled: boolean = false;

  constructor(private elementRef: ElementRef) {
  }

  @HostListener('mouseenter')
  onMouseEnter() {
    if (this.hoverDisabled) {
      return;
    }

    this.hoverClass.split(' ').forEach((className) => {
      this.elementRef.nativeElement.classList.add(className);
    });
  }

  @HostListener('mouseleave')
  onMouseLeave() {
    if (this.hoverDisabled) {
      return;
    }

    this.hoverClass.split(' ').forEach((className) => {
      this.elementRef.nativeElement.classList.remove(className);
    });
  }
}
