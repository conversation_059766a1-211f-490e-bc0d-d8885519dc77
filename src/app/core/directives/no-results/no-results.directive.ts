import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appNoResults]'
})
export class NoResultsDirective implements OnInit {

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @Input() title = 'No results found.';
  @Input() content: string;

  ngOnInit() {
    const noResultsHtml = `
      <div class="d-flex flex-column align-items-center m-y-spacing-xxx-lg">
        <div class="feature-container feature-xl m-y-spacing-xx-big">
          <i class="feature-icon fa-light fa-magnifying-glass"></i>
        </div>
        <span class="content-heading-h3 d-block p-b-spacing-sm">${this.title}</span>
        <div class="content-body-medium content-color-tertiary text-center">
          <span>${this.content}</span>
        </div>
      </div>
    `;
    this.renderer.setProperty(this.el.nativeElement, 'innerHTML', noResultsHtml);
  }

}
