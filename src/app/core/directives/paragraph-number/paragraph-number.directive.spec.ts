import { Component, ElementRef, <PERSON><PERSON><PERSON> } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ParagraphNumberDirective } from './paragraph-number.directive';
import { BrowserModule } from '@angular/platform-browser';
import { TextHighlightService } from '@core/services';

@Component({
  template: `
    <div appParagraphNumber>
      <!-- Test cases here -->
    </div>
  `
})
class TestHostComponent {
}

describe('ParagraphNumberDirective', () => {
  let fixture: ComponentFixture<TestHostComponent>;
  let hostElement: HTMLElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestHostComponent, ParagraphNumberDirective]
    }).compileComponents();

    fixture = TestBed.createComponent(TestHostComponent);
    hostElement = fixture.nativeElement;
  });

  describe('Basic Functionality', () => {
    it('should create directive instance', () => {
      expect(fixture).toBeTruthy();
    });

    it('should handle empty content', fakeAsync(() => {
      updateHostTemplate('<div data-origin-num="1"></div>');
      tick(100);
      expect(getDisplayedNumberByParagraphNumber('1')).toBeFalsy();
    }));
  });

  describe('Number Insertion', () => {
    it('should insert paragraph number before first text node', fakeAsync(() => {
      updateHostTemplate('<p data-origin-num="1">Text content</p>');
      tick(100);

      const element = getDisplayedNumberByParagraphNumber('1');
      expect(element).toBeTruthy();
      expect(element.textContent).toBe('[1]');
      expect(element.nextSibling.textContent).toBe('Text content');
    }));

    it('should handle multiple paragraphs', fakeAsync(() => {
      updateHostTemplate(`
        <p data-origin-num="1">First</p>
        <p data-origin-num="2">Second</p>
      `);
      tick(100);

      expect(getDisplayedNumberByParagraphNumber('1')).toBeTruthy();
      expect(getDisplayedNumberByParagraphNumber('2')).toBeTruthy();
    }));
  });

  describe('Scanned Numbers', () => {
    it('should handle scanned numbers at start', fakeAsync(() => {
      updateHostTemplate('<p data-origin-num="1">[123] Text</p>');
      tick(100);

      const displayedNumber = getDisplayedNumberByOriginNumber('1');
      expect(displayedNumber.textContent).toBe('[123]');
      expect(displayedNumber.nextSibling.textContent).toBe(' Text');
    }));

    it('should handle multiple scanned numbers', fakeAsync(() => {
      updateHostTemplate('<p data-origin-num="1">[123] Text [456] More</p>');
      tick(100);

      const displayedNumbers = getAllDisplayedNumbers('1');
      expect(displayedNumbers.length).toBe(1);
      expect(displayedNumbers[0].textContent).toBe('[123]');
    }));
  });

  describe('Dynamic Updates', () => {
    it('should handle content changes', fakeAsync(() => {
      // Initial setup
      updateHostTemplate('<p data-origin-num="1">Initial</p>');
      fixture.detectChanges();
      tick(100);

      // Verify initial state
      let element = getDisplayedNumberByParagraphNumber('1');
      expect(element).toBeTruthy();
      expect(element.nextSibling.textContent).toBe('Initial');

      // Update content in a way that preserves the number element
      const para = hostElement.querySelector('[data-origin-num="1"]');
      const newTextNode = document.createTextNode('Updated');
      para.replaceChild(newTextNode, para.lastChild);

      fixture.detectChanges();
      tick(100);

      // Verify updated state
      element = getDisplayedNumberByParagraphNumber('1');
      expect(element).toBeTruthy();
      expect(element.nextSibling.textContent).toBe('Updated');
    }));

    it('should cleanup removed paragraphs', fakeAsync(() => {
      updateHostTemplate('<p data-origin-num="1">Text</p>');
      tick(100);

      updateHostTemplate('');
      tick(100);

      expect(getDisplayedNumberByParagraphNumber('1')).toBeFalsy();
    }));
  });

  describe('Edge Cases', () => {
    describe('Complex DOM Structures', () => {
      it('should handle deeply nested content', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="1">
          <div>
            <span>
              <b>[123]</b>
              Text
              <i>[456]</i>
            </span>
          </div>
        </div>
      `);
        tick(100);

        const displayedNumbers = getAllDisplayedNumbers('1');
        expect(displayedNumbers.length).toBe(2);
        expect(displayedNumbers[0].textContent).toBe('[123]');
        expect(displayedNumbers[1].textContent).toBe('[456]');
      }));

      it('should handle mixed content nodes', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="1">
          Text
          <span>More</span>
          [123]
          <b>Bold</b>
          <br/>
          [456]
        </div>
      `);
        tick(100);

        const displayedNumbers = getAllDisplayedNumbers('1');
        expect(displayedNumbers.length).toBe(2);
      }));
    });

    describe('Content Edge Cases', () => {
      it('should handle zero-width spaces', fakeAsync(() => {
        updateHostTemplate(`<p data-origin-num="1">\u200B[123] Text</p>`);
        tick(100);

        const displayed = getDisplayedNumberByOriginNumber('1');
        expect(displayed).toBeTruthy();
      }));

      it('should handle multiple adjacent numbers', fakeAsync(() => {
        updateHostTemplate(`<p data-origin-num="1">[123][456][789]Text</p>`);
        tick(100);

        const displayedNumbers = getAllDisplayedNumbers('1');
        expect(displayedNumbers.length).toBe(1);
      }));

      it('should handle malformed numbers', fakeAsync(() => {
        updateHostTemplate(`
        <p data-origin-num="1">[12.3]Text</p>
        <p data-origin-num="2">[1a2]Text</p>
        <p data-origin-num="3">[1,234]Text</p>
      `);
        tick(100);

        const displayed1 = getDisplayedNumberByOriginNumber('1');
        expect(displayed1).toBeTruthy();
        expect(displayed1.textContent).toBe('[1]');
        expect(displayed1.nextSibling.textContent).toBe('[12.3]Text');

        const displayed2 = getDisplayedNumberByOriginNumber('2');
        expect(displayed2).toBeTruthy();
        expect(displayed2.textContent).toBe('[2]');
        expect(displayed2.nextSibling.textContent).toBe('[1a2]Text');

        const displayed3 = getDisplayedNumberByOriginNumber('3');
        expect(displayed3).toBeTruthy();
        expect(displayed3.textContent).toBe('[3]');
        expect(displayed3.nextSibling.textContent).toBe('[1,234]Text');
      }));

      it('should find and number deeply nested text', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="001">
          <div>
            <span>
              <em>Deeply nested text</em>
            </span>
          </div>
          <ul>
            <li>
              <span>Deeply nested text 1</span>
            </li>
          </ul>
          <div data-origin-num="002">Skip this</div>
          <ul>
            <li data-origin-num="003">
              <span>Deeply nested text 2</span>
            </li>
          </ul>
        </div>
      `);
        tick(100);

        const displayed1 = getDisplayedNumberByOriginNumber('001');
        expect(displayed1).toBeTruthy();
        expect(displayed1.textContent).toBe('[001]');
        expect(displayed1.nextSibling.textContent).toBe('Deeply nested text');

        const displayed2 = getDisplayedNumberByOriginNumber('002');
        expect(displayed2).toBeTruthy();
        expect(displayed2.textContent).toBe('[002]');
        expect(displayed2.nextSibling.textContent).toBe('Skip this');

        const displayed3 = getDisplayedNumberByOriginNumber('003');
        expect(displayed3).toBeTruthy();
        expect(displayed3.textContent).toBe('[003]');
        expect(displayed3.nextSibling.textContent).toBe('Deeply nested text 2');
      }));

      it('should handle multiple nested elements', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="003">
          <div>
            <span>First span</span>
            <strong>
              <em>Target text</em>
            </strong>
          </div>
          <p data-origin-num="004">Nested para</p>
        </div>
      `);
        tick(100);

        const displayed1 = getDisplayedNumberByOriginNumber('003');
        expect(displayed1).toBeTruthy();
        expect(displayed1.textContent).toBe('[003]');
        expect(displayed1.nextSibling.textContent).toBe('First span');

        const displayed2 = getDisplayedNumberByOriginNumber('004');
        expect(displayed2).toBeTruthy();
        expect(displayed2.textContent).toBe('[004]');
        expect(displayed2.nextSibling.textContent).toBe('Nested para');
      }));

      it('should skip empty nodes and find valid text', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="005">
          <div>
            <span></span>
            <em>   </em>
            <div>&nbsp;</div>
            <div>       </div>
            <strong>Valid text</strong>
          </div>
        </div>
      `);
        tick(100);

        const displayed1 = getDisplayedNumberByOriginNumber('005');
        expect(displayed1).toBeTruthy();
        expect(displayed1.textContent).toBe('[005]');
        expect(displayed1.nextSibling.textContent).toBe('Valid text');
      }));

      it('should skip empty nodes and find valid text', fakeAsync(() => {
        updateHostTemplate(`
        <div data-origin-num="006">
          <div>
            <span></span>
            <div>
              <p data-origin-num="007">Skip this</p>
              <span>
                <em>Target text</em>
              </span>
            </div>
          </div>
        </div>
      `);
        tick(100);

        const displayed1 = getDisplayedNumberByOriginNumber('007');
        expect(displayed1).toBeTruthy();
        expect(displayed1.textContent).toBe('[007]');
        expect(displayed1.nextSibling.textContent).toBe('Skip this');

        const displayed2 = getDisplayedNumberByOriginNumber('006');
        expect(displayed2).toBeFalsy();
      }));

    });

    describe('Scanned Number Priority', () => {
      it('should prefer scanned number over attribute number', fakeAsync(() => {
        updateHostTemplate(`<p data-origin-num="1">[123] Text</p>`);
        tick(100);

        const element = getDisplayedNumberByParagraphNumber('1');
        expect(element).toBeFalsy(); // Should not insert attribute number
        const displayed = getDisplayedNumberByParagraphNumber('123');
        expect(displayed.textContent).toBe('[123]');
      }));

      it('should affect all paragraphs if any has scanned number', fakeAsync(() => {
        updateHostTemplate(`
      <p data-origin-num="1">[123] Text</p>
      <p data-origin-num="2">No scanned number</p>
    `);
        tick(100);

        expect(getDisplayedNumberByParagraphNumber('1')).toBeFalsy();
        expect(getDisplayedNumberByParagraphNumber('2')).toBeFalsy();
        expect(getDisplayedNumberByOriginNumber('1')).toBeTruthy();
      }));

      it('should handle mixed scanned and non-scanned content', fakeAsync(() => {
        updateHostTemplate(`
      <p data-origin-num="1">Normal text</p>
      <p data-origin-num="2">[123] Text</p>
      <p data-origin-num="3">More text</p>
    `);
        tick(100);

        // When any paragraph has scanned number, all should use scanning mode
        [1, 2, 3].forEach(num => {
          expect(getDisplayedNumberByParagraphNumber(num.toString())).toBeFalsy();
        });
        expect(getDisplayedNumberByOriginNumber('2')).toBeTruthy();
      }));
    });
  });

  // Helper functions
  function updateHostTemplate(template: string): void {
    const container = hostElement.querySelector('[appParagraphNumber]');
    container.innerHTML = template;
    fixture.detectChanges();
  }

  function getDisplayedNumberByParagraphNumber(num: string): HTMLElement {
    return hostElement.querySelector(`#para-num-${num}`);
  }

  function getDisplayedNumberByOriginNumber(num: string): HTMLElement {
    const para = hostElement.querySelector(`[data-origin-num="${num}"]`);
    return para ? para.querySelector(`#${para.getAttribute('data-para-num-id')}`) : null;
  }

  function getAllDisplayedNumbers(num: string): HTMLElement[] {
    const para = hostElement.querySelector(`[data-origin-num="${num}"]`);
    return Array.from(para.querySelectorAll('.displayed-paragraph-number'));
  }
});
