import { Directive, ElementRef, Input, <PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TextHighlightService } from '@core/services';


interface TextNodePosition {
  textNode: Text | null;
  insertBeforeNode: Node | null;
  parentElement: HTMLElement | null;
}


/**
 * Directive that manages paragraph numbering in a document.
 * Supports both automatic number insertion and processing of pre-existing scanned numbers.
 *
 * Features:
 * - Automatically adds paragraph numbers in specified format
 * - Handles pre-existing scanned numbers
 * - Maintains number consistency across updates
 * - Performance optimized with RequestAnimationFrame
 * - Supports accessibility with ARIA labels
 */
@Directive({
  selector: '[appParagraphNumber]'
})
export class ParagraphNumberDirective implements OnInit, OnDestroy {
  @Input() paragraphNumberClass = 'displayed-paragraph-number content-color-primary content-body-medium content-style-semi-bold';
  @Input() paragraphNumberDisplayTemplate = '[{{num}}]';

  private readonly paragraphNumberTemplate = '{{num}}';
  private readonly paragraphNumberDataAttribute = 'data-origin-num';
  private readonly paragraphNumberRegExp = /^[\s\u00A0\u2000-\u200B\u202F\u205F\u3000\uFEFF\&nbsp;]*(\[\d{2,6}\])/i;
  private readonly paragraphNumberMarginClass = 'm-r-spacing-xx-s';
  private readonly paragraphNumberElementIdPrefix = 'para-num-';

  private updateQueue = new Map<string, () => void>();
  private insertedParagraphs = new Map<string, HTMLElement>();

  constructor(
    private el: ElementRef<HTMLElement>,
    private ngZone: NgZone
  ) {
  }

  ngOnInit() {
    this.ngZone.runOutsideAngular(() => {
      this.displayParagraphNumbers();
    });
  }

  ngOnDestroy() {
    this.cleanup();
  }

  /**
   * @summary
   * Main orchestrator method for displaying paragraph numbers.
   * Uses RequestAnimationFrame for performance optimization.
   *
   * @algorithm
   * 1. Creates map of existing paragraphs with numbers
   * 2. Removes stale number elements
   * 3. Processes new paragraphs in next animation frame
   * 4. Executes queued updates in subsequent frame
   *
   * @example
   * Input:  <p data-origin-num="001">Text</p>
   * Output: <p data-origin-num="001"><span class="paragraph-number">[001]</span>Text</p>
   */
  private displayParagraphNumbers() {
    try {
      const paragraphMap = new Map(
        Array.from(this.el.nativeElement.querySelectorAll<HTMLElement>(`[${this.paragraphNumberDataAttribute}]`))
          .map(p => [this.getParagraphNumberValue(p), p])
      );

      this.cleanupStaleNodes(paragraphMap);

      // Wait for a frame before processing new numbers
      requestAnimationFrame(() => {
        this.processNewParagraphs(paragraphMap);

        // Process the update queue in the next frame
        requestAnimationFrame(() => {
          this.updateQueue.forEach(update => update());
          this.updateQueue.clear();
        });
      });
    } catch (error) {
      console.error('Error processing paragraphs:', error);
    }
  }

  /**
   * @summary
   * Removes paragraph number elements that no longer exist in the document.
   *
   * @algorithm
   * 1. Identifies numbers in insertedParagraphs not in current paragraphMap
   * 2. Removes DOM elements for stale numbers
   * 3. Updates internal tracking
   *
   * @example
   * Before: insertedParagraphs has [001, 002, 003], paragraphMap has [001, 002]
   * After:  span[003] removed from DOM, insertedParagraphs has [001, 002]
   */
  private cleanupStaleNodes(paragraphMap: Map<string, HTMLElement>) {
    const staleNodes = Array.from(this.insertedParagraphs.keys())
      .filter(num => !paragraphMap.has(num));

    staleNodes.forEach(num => {
      const element = this.insertedParagraphs.get(num);
      if (element?.parentNode) {
        element.parentNode.removeChild(element);
      }
      this.insertedParagraphs.delete(num);
    });
  }

  /**
   * @summary
   * Processes paragraphs and inserts numbers before the first text node,
   * traversing deeply through elements without data-origin-num.
   *
   * @algorithm
   * 1. Checks for existing scanned numbers
   * 2. For each paragraph:
   *    - Finds first text node by deep traversal
   *    - Skips elements with data-origin-num
   *    - Processes numbers at found position
   */
  private processNewParagraphs(paragraphMap: Map<string, HTMLElement>) {
    const hasScannedNumber = this.checkForScannedNumbers(paragraphMap);

    for (const [number, paragraph] of paragraphMap) {
      if (!number) {
        continue;
      }

      // Remove from insertedParagraphs if content changed
      const existingNumber = this.insertedParagraphs.get(number);
      if (existingNumber && !paragraph.contains(existingNumber)) {
        this.insertedParagraphs.delete(number);
      }

      // Find the first text node position through deep traversal
      const {textNode, insertBeforeNode} = this.findFirstInsertableTextNode(paragraph);
      if (!textNode || !insertBeforeNode) {
        continue;
      }

      if (hasScannedNumber) {
        this.processScannedNumber(paragraph);
      } else if (!this.insertedParagraphs.has(number)) {
        this.insertNewNumber(paragraph, number, insertBeforeNode);
      }
    }
  }

  /**
   * @summary
   * Finds the first text node that can be used for paragraph number insertion by traversing the DOM tree,
   * including deeply nested structures.
   *
   * @algorithm
   * 1. Use TreeWalker to find first non-empty text node
   * 2. Find or create a containing span
   * 3. Determine correct insertion point
   *
   * @returns Text node for insertion
   */
  private findFirstInsertableTextNode(element: HTMLElement): TextNodePosition {
    const treeWalker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node: Node) => {
          return node.nodeType === Node.TEXT_NODE && node.textContent?.trim()
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_SKIP;
        }
      }
    );

    const textNode = treeWalker.nextNode() as Text;

    if (!textNode) {
      return {textNode: null, insertBeforeNode: null, parentElement: null};
    }

    // Check if node is within an element with data-origin-num
    let parent = textNode.parentElement;
    while (parent && parent !== element) {
      if (parent.hasAttribute(this.paragraphNumberDataAttribute)) {
        return {textNode: null, insertBeforeNode: null, parentElement: null};
      }
      parent = parent.parentElement;
    }

    // Find or create a containing span for the text
    let containingSpan: HTMLSpanElement;
    if (textNode.parentElement?.tagName?.toUpperCase() === 'SPAN') {
      containingSpan = textNode.parentElement;
    } else {
      // Create a new span and wrap the text node
      containingSpan = document.createElement('span');
      textNode.parentNode?.insertBefore(containingSpan, textNode);
      containingSpan.appendChild(textNode);
    }

    return {
      textNode,
      insertBeforeNode: containingSpan,
      parentElement: containingSpan.parentElement
    };
  }


  /**
   * @summary
   * Detects if any paragraphs contain pre-existing scanned number patterns.
   *
   * @algorithm
   * 1. Creates TreeWalker for text node traversal
   * 2. Checks each text node against number pattern
   * 3. Returns true if any matches found
   *
   * @example
   * Returns true:  <p data-origin-num="001">[001] Text</p>
   * Returns false: <p data-origin-num="001">Text</p>
   */
  private checkForScannedNumbers(paragraphMap: Map<string, HTMLElement>): boolean {
    for (const paragraph of paragraphMap.values()) {
      const walker = document.createTreeWalker(paragraph, NodeFilter.SHOW_TEXT);
      while (walker.nextNode()) {
        if (this.extractFormattedParagraphNumber(walker.currentNode as Text)) {
          return true;
        }
      }
    }
    return false;
  }

  private hasInnerParagraphs(paragraph: HTMLElement): boolean {
    return paragraph.querySelector(`[${this.paragraphNumberDataAttribute}]`) !== null;
  }

  private findFirstNotEmptyNode(paragraph: HTMLElement): Node | null {
    const walker = document.createTreeWalker(paragraph, NodeFilter.SHOW_TEXT);
    while (walker.nextNode()) {
      const node = walker.currentNode;
      if (node.textContent?.trim()) {
        return node;
      }
    }
    return null;
  }

  /**
   * @summary
   * Processes and reformats pre-existing scanned paragraph numbers.
   *
   * @algorithm
   * 1. Uses TreeWalker to find text nodes
   * 2. Tracks processed nodes to avoid duplicates
   * 3. Skip node if no scanned number found
   * 4. Creates formatted number fragments
   * 5. Queues replacement operations
   *
   * @example
   * Input:  Text node containing "[001] Some text"
   * Output: <span class="number">[001]</span><span>Some text</span>
   */
  private processScannedNumber(paragraph: HTMLElement) {
    const walker = document.createTreeWalker(paragraph, NodeFilter.SHOW_TEXT);
    const processedNodes = new WeakSet<Node>();

    while (walker.nextNode()) {
      const node = walker.currentNode as Text;
      if (processedNodes.has(node)) {
        continue;
      }

      processedNodes.add(node);
      const foundNumber = this.extractFormattedParagraphNumber(node);
      if (!foundNumber) {
        continue;
      }

      const fragment = this.createNumberFragment(node, foundNumber);
      if (fragment) {
        this.updateQueue.set(`replace-${foundNumber}`, () => {
          this.updateParagraphAttribute(paragraph, foundNumber);
          node.replaceWith(fragment);
        });
      }
    }
  }

  private updateParagraphAttribute(paragraph: HTMLElement, number: string) {
    const extractedNumber = this.extractParagraphNumber(number);
    paragraph.setAttribute('data-para-num-id', this.buildParagraphNumberElementId(extractedNumber));
  }

  /**
   * @summary
   * Inserts a new paragraph number element at the appropriate position.
   *
   * @algorithm
   * 1. Creates formatted number element
   * 2. Determines insertion position
   * 3. Queues insert operation
   * 4. Updates tracking state
   *
   * @example
   * Input position: Before first non-empty text node
   * Created element: <span class="number">[001]</span>
   * Fallback: Prepend to paragraph if no suitable position found
   */
  private insertNewNumber(paragraph: HTMLElement, number: string, insertBeforeNode: Node) {
    const formattedNumber = this.formatParagraphNumber(number);
    const element = this.createNumberElement(formattedNumber, false);

    this.updateQueue.set(`insert-${number}`, () => {
      try {
        if (insertBeforeNode && insertBeforeNode.parentElement) {
          insertBeforeNode.parentElement.insertBefore(element, insertBeforeNode);
        } else {
          // Fallback to beginning of paragraph if insertion point is invalid
          paragraph.insertBefore(element, paragraph.firstChild);
        }
        this.insertedParagraphs.set(number, element);
        this.updateParagraphAttribute(paragraph, number);
      } catch (error) {
        console.warn('Error inserting paragraph number:', error);
        try {
          // Final fallback
          paragraph.insertBefore(element, paragraph.firstChild);
          this.insertedParagraphs.set(number, element);
          this.updateParagraphAttribute(paragraph, number);
        } catch (fallbackError) {
          console.error('Failed to insert paragraph number:', fallbackError);
        }
      }
    });
  }

  private getParagraphNumberValue(element: HTMLElement): string | null {
    const value = element.getAttribute(this.paragraphNumberDataAttribute)?.trim();
    return value && !this.isZeroOnlyNumber(value) ? value : null;
  }

  private isZeroOnlyNumber(value: string): boolean {
    return /^0+$/.test(value);
  }

  private extractFormattedParagraphNumber(node: Text): string | null {
    const match = (node.textContent || '').match(this.paragraphNumberRegExp);
    return match?.length > 1 && match[1].length > 2 ? match[1] : null;
  }

  /**
   * @summary
   * Creates a document fragment containing formatted number and surrounding text.
   *
   * @algorithm
   * 1. Locates number in text content
   * 2. Splits text into before/after segments
   * 3. Creates spans for text segments
   * 4. Assembles complete fragment
   *
   * @example
   * Input:  Text node with "  [001] Some text"
   * Output: Fragment containing:
   *         <span>  </span>
   *         <span class="number">[001]</span>
   *         <span> Some text</span>
   */
  private createNumberFragment(node: Text, number: string): DocumentFragment | null {
    try {
      const text = node.textContent || '';
      const index = text.indexOf(number);
      if (index === -1) {
        return null;
      }

      const fragment = document.createDocumentFragment();
      const before = text.substring(0, index);
      const after = text.substring(index + number.length);

      if (before) {
        fragment.appendChild(this.createTextSpan(before));
      }
      fragment.appendChild(this.createNumberElement(number, true));
      if (after) {
        fragment.appendChild(this.createTextSpan(after));
      }

      return fragment;
    } catch {
      return null;
    }
  }

  private createTextSpan(text: string): HTMLSpanElement {
    const span = document.createElement('span');
    span.textContent = text;
    return span;
  }

  private extractParagraphNumber(number: string): string {
    return number.replace(/[^\d]/g, '');
  }

  private buildParagraphNumberElementId(number: string): string {
    return `${this.paragraphNumberElementIdPrefix}${number}`;
  }

  /**
   * @summary
   * Creates a styled paragraph number element with accessibility attributes.
   *
   * @algorithm
   * 1. Creates span element
   * 2. Applies styling classes
   * 3. Sets accessibility attributes
   * 4. Configures number display format
   *
   * @example
   * Input:  number="001", isScanned=false
   * Output: <span id="para-num-001"
   *               class="paragraph-number m-r-spacing-xx-s"
   *               role="text"
   *               aria-label="Paragraph 001">
   *           [001]
   *         </span>
   */
  private createNumberElement(number: string, isScanned: boolean): HTMLElement {
    const element = document.createElement('span');
    const extractedNumber = this.extractParagraphNumber(number);

    element.id = this.buildParagraphNumberElementId(extractedNumber);
    element.className = [
      this.paragraphNumberClass,
      isScanned ? '' : this.paragraphNumberMarginClass,
      isScanned ? '' : TextHighlightService.HIGHLIGHT_TEXT_NON_SELECT_CSS_CLASS
    ].filter(Boolean).join(' ');
    element.textContent = number.replace(/\[.*?(\d+).*?\]/, '[$1]');
    element.setAttribute('role', 'text');
    element.setAttribute('aria-label', `Paragraph ${extractedNumber}`);

    return element;
  }

  private formatParagraphNumber(number: string): string {
    return this.paragraphNumberDisplayTemplate.replace(this.paragraphNumberTemplate, number);
  }

  private cleanup(): void {
    this.insertedParagraphs.forEach(element => element.remove());
    this.insertedParagraphs.clear();
    this.updateQueue.clear();
  }
}
