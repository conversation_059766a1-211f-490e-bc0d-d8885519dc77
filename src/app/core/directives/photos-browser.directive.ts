import { Directive, EventEmitter, HostListener, Input, Output } from '@angular/core';

export interface PhotosBrowserData {
  selectedUrls: { data: string, size: number }[];
  selectedFiles: { data: Blob, size: number }[];
}

@Directive({selector: '[appPhotosBrowser]'})
export class PhotosBrowserDirective {

  @Input()
  multiple = false;

  @Input()
  fileTypes = 'image/png,image/jpeg';

  @Output()
  photosSelected = new EventEmitter<PhotosBrowserData>(true);

  constructor() {
  }

  @HostListener('click')
  onClick() {
    this.browsingPhotos();
  }

  createImageUrl(data: Blob | File): string {
    const creator = window.URL || window.webkitURL;
    return creator.createObjectURL(data);
  }

  private browsingPhotos() {
    const inputElementId = 'app-file-browser';

    const existedInput = document.getElementById(inputElementId);

    if (existedInput) {
      existedInput.remove();
    }

    const input = document.createElement('input');
    input.setAttribute('id', inputElementId);
    input.setAttribute('type', 'file');
    input.setAttribute('multiple', this.multiple?.toString());
    input.setAttribute('accept', this.fileTypes);
    input.setAttribute('style', 'position: absolute; bottom: 0; opacity: 0; overflow: hidden;');

    input.onchange = (event) => {
      const files = input.files;
      const selectedUrls = [];
      const selectedFiles = [];

      for (let i = 0; i < files.length; i++) {
        const file = files.item(i);

        if (!file.type.match(/image.*/)) {
          continue;
        }

        selectedUrls.push({data: this.createImageUrl(file), size: file.size});
        selectedFiles.push({data: file, size: file.size});
      }

      this.photosSelected.emit({selectedUrls, selectedFiles} as PhotosBrowserData);

      if (input) {
        input.value = null;
        input.remove();
      }
    };

    if (input) {
      document.body.appendChild(input);

      if (document.createEvent) {
        const evt = document.createEvent('MouseEvents');
        evt.initEvent('click', true, false);
        input.dispatchEvent(evt);
      } else {
        input.click();
      }
    }
  }
}
