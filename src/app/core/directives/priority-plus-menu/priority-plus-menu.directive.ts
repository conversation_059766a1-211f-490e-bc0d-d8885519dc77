import { AfterViewInit, Directive, ElementRef, HostListener} from '@angular/core';

declare var $: any;

@Directive({
  selector: '[appPriorityPlusMenu]'
})
export class PriorityPlusMenuDirective implements AfterViewInit  {

  timeout = null;

  constructor(public el: ElementRef) {
  }
  ngAfterViewInit(): void {
    setTimeout(() => this.updatePriorityMenu());
  }

  @HostListener('window:resize', ['$event'])
  onResizeBrowser(event) {
    clearTimeout(this.timeout);
    this.timeout = setTimeout(this.updatePriorityMenu, 500);
  }

  updatePriorityMenu() {
    const menus = document.getElementsByClassName('priority-menu-bar');
    for(var index=0;index < menus.length;index++){
      const bar = menus[index];
      const menu = bar.querySelector(`.priority-menu`);
      const moreBtn = bar.querySelector(`.priority-menu-more`);
      const dropdown = bar.querySelector(`.priority-menu-dropdown`);
      if(menu.scrollWidth > menu.clientWidth){
        moreBtn.removeAttribute("hidden");
        while(menu.scrollWidth > menu.clientWidth){
          dropdown.prepend(menu.lastElementChild);
        }
      } else {
        while (dropdown.firstElementChild && !(menu.scrollWidth > menu.clientWidth)) {
          menu.appendChild(dropdown.firstElementChild);
        }
        if(menu.scrollWidth > menu.clientWidth) dropdown.prepend(menu.lastElementChild);

        if (dropdown.children.length === 0) moreBtn.setAttribute("hidden", 'true');
      }
    }
  }
}
