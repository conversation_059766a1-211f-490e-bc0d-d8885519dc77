import { Directive, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MediaObserver } from '@angular/flex-layout';
import { Subscription } from 'rxjs';

@Directive({
  selector: '[appScreenSizeDetector]'
})
export class ScreenSizeDetectorDirective implements OnInit, OnDestroy {
  private subscriptions = new Subscription();

  constructor(
    private mediaObserver: MediaObserver,
    private el: ElementRef
  ) {
  }

  ngOnInit(): void {
    this.detectScreenSize();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private detectScreenSize() {
    const prefix = 'screen-size-';
    const mediaObserver$ = this.mediaObserver
      .asObservable()
      .subscribe({
        next: (change) => {
          change.forEach((item) => {
            for (const cl of this.el.nativeElement.classList) {
              if (cl.startsWith(prefix)) {
                this.el.nativeElement.classList.remove(cl);
              }
            }
            this.el.nativeElement.classList.add(`${prefix}${item.mqAlias}`);
          });
        }
      });
    this.subscriptions.add(mediaObserver$);
  }
}
