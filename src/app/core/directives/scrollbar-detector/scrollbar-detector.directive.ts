import { Directive, ElementRef, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

@Directive({
  selector: '[appScrollbarDetector]'
})
export class ScrollbarDetectorDirective implements OnInit, OnDestroy {
  @Input() scrollbarCss: string = 'm-r-spacing-xx-s';
  @Input() removingScrollbarCss: string = null;
  private resizeObserver: ResizeObserver = null;

  constructor(
    private el: ElementRef
  ) {
  }

  ngOnInit() {
    this.detectScrollbar();
  }

  ngOnDestroy() {
    this.destroyResizeObserver();
  }

  private detectScrollbar() {
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const target = entry.target as HTMLElement;
        const hasScrollbar = target.scrollHeight > target.clientHeight;
        if (hasScrollbar) {
          this.removeCssClass(target, this.removingScrollbarCss);
          this.addCssClass(target, this.scrollbarCss);
        } else {
          this.removeCssClass(target, this.scrollbarCss);
          this.addCssClass(target, this.removingScrollbarCss);
        }
      }
    });

    this.resizeObserver.observe(this.el.nativeElement);
  }

  private addCssClass(ele: HTMLElement, cssClass: string) {
    if (!cssClass) {
      return;
    }
    cssClass.split(' ').forEach((val) => {
      if (val) {
        ele.classList.add(val);
      }
    });
  }

  private removeCssClass(ele: HTMLElement, cssClass: string) {
    if (!cssClass) {
      return;
    }
    cssClass.split(' ').forEach((val) => {
      if (val) {
        ele.classList.remove(val);
      }
    });
  }

  private destroyResizeObserver() {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.el.nativeElement);
      this.resizeObserver.disconnect();
    }
  }
}
