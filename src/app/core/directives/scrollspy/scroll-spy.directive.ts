import { AfterViewInit, Directive, ElementRef, HostListener, Input } from '@angular/core';

declare var $: any;

@Directive({
  selector: '[appScrollSpy]'
})
export class ScrollSpyDirective implements AfterViewInit {
  @Input() navItemClass: string;
  @Input() sectionClass: string;

  constructor(private el: ElementRef) {
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll($event: any) {
    this.highlightNavigationLink();
  }

  ngAfterViewInit() {
    this.highlightNavigationLink();
    this.onNavLinkClickEvent();
  }

  private onNavLinkClickEvent() {
    const navLinks = document.getElementsByClassName(this.navItemClass);
    const navRect = this.el.nativeElement.getBoundingClientRect();
    for (let i = 0; i < navLinks.length; i++) {
      const navLink = navLinks[i] as HTMLElement;
      navLink.addEventListener('click', ev => {
        ev.stopPropagation();
        ev.preventDefault();
        const sectionId = navLink.getAttribute('data-section-id');
        const sectionOffset = $(`#${sectionId}`).offset();
        if (sectionOffset && sectionOffset.top) {
          const top = sectionOffset.top - navRect.height;
          $('html, body').animate({scrollTop: top}, 300);
        }
      });
    }
  }

  private highlightNavigationLink() {
    const navLinks = document.getElementsByClassName(this.navItemClass);
    for (let i = 0; i < navLinks.length; i++) {
      navLinks[i].classList.remove('active');
    }

    const navRect = this.el.nativeElement.getBoundingClientRect();
    const scrollTop = (window.pageYOffset || document.documentElement.scrollTop) + navRect.height + 10;
    const sections = document.getElementsByClassName(this.sectionClass);

    for (let i = 0; i < sections.length; i++) {
      const section = sections[i] as HTMLElement;
      const navElem = document.querySelector('[data-section-id="' + section.id + '"]') as HTMLElement;
      if (navElem) {
        const sectionOffset = $(`#${section.id}`).offset();
        const rect = section.getBoundingClientRect();
        const sectionTop = sectionOffset && sectionOffset.top ? sectionOffset.top : section.offsetTop;
        const sectionBottom = sectionTop + rect.height;
        if (scrollTop >= sectionTop && scrollTop <= sectionBottom) {
          navElem.classList.add('active');
        }
      }
    }
  }
}
