import { Directive, HostListener, Input, OnInit, Renderer2 } from '@angular/core';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { TooltipService } from '@core/services';

@Directive({
  selector: '[appSelectableTooltip]'
})
export class SelectableTooltipDirective implements OnInit {
  @Input() selectableTooltipPopover: NgbPopover;
  @Input() selectableTooltipCloseTimeout = 500;
  @Input() selectableTooltipOpenTimeout = 0;
  @Input() parentPopover: NgbPopover;
  @Input() classPopover: string;
  @Input() triggerPopover: 'hover' | 'click' = 'hover';
  @Input() autoCloseTooltip: boolean = true;

  private selectableTooltipCloseTimeoutId = null;
  private selectableTooltipOpenTimeoutId = null;
  private isMouseover = false;
  private isClicked = false;

  constructor(
    private renderer: Renderer2,
    private tooltipService: TooltipService
  ) {
  }

  ngOnInit() {
    this.tooltipService.closeParentPopover$.subscribe((popover) => {
      if (this.selectableTooltipPopover == popover && !this.isMouseover) {
        this.delayClose(this.selectableTooltipCloseTimeout);
      }
    });
  }

  @HostListener('click', ['$event'])
  onMouseClick(event: MouseEvent) {
    if (this.triggerPopover === 'click') {
      this.tooltipService.openTooltip(this.selectableTooltipPopover, this.parentPopover);
    }
  }

  @HostListener('mouseleave', ['$event'])
  onMouseLeave(event: MouseEvent) {
    this.clearOpenTimeout();

    if (this.autoCloseTooltip) {
      this.delayClose(this.selectableTooltipCloseTimeout);
    }
  }

  @HostListener('mouseover', ['$event'])
  onMouseOver(event: MouseEvent) {
    this.clearCloseTimeout();
    if (this.triggerPopover === 'hover') {
      this.clearOpenTimeout();
      this.selectableTooltipOpenTimeoutId = setTimeout(() => {
        this.tooltipService.openTooltip(this.selectableTooltipPopover, this.parentPopover);
      }, this.selectableTooltipOpenTimeout);
    }
    this.selectableTooltipPopover?.shown?.subscribe(() => {
      const popoverElement = document.querySelector((this.classPopover ? `.${this.classPopover} ` : '') + '.popover-body').parentElement;
      if (popoverElement) {
        this.renderer.listen(popoverElement, 'click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
          this.clearCloseTimeout();
          this.isClicked = true;
        });

        this.renderer.listen(popoverElement, 'mouseenter', (event) => {
          if (!this.parentPopover) {
            this.clearOpenTimeout();
          }

          if (this.selectableTooltipPopover == this.tooltipService.parentPopover) {
            this.tooltipService.parentPopover = null;
            this.clearCloseTimeout();
          }
          if (this.parentPopover) {
            this.tooltipService.parentPopover = this.parentPopover;
            this.clearCloseTimeout();
          }
        });

        this.renderer.listen(popoverElement, 'mouseover', (event) => {
          this.isMouseover = true;
          this.isClicked = false;
          this.clearCloseTimeout();

          if (!this.parentPopover) {
            this.clearOpenTimeout();
          }
        });

        this.renderer.listen(popoverElement, 'mouseleave', (event) => {
          if (this.selectableTooltipPopover != this.tooltipService.parentPopover && !this.isClicked && this.autoCloseTooltip) {
            this.delayClose(this.selectableTooltipCloseTimeout);
          }
          this.isMouseover = false;
          this.isClicked = false;
        });

        const closeIconEle = popoverElement.querySelector('.popover-close-icon');
        if (closeIconEle) {
          this.renderer.listen(closeIconEle, 'click', (event) => {
            this.tooltipService.closeTooltip(this.selectableTooltipPopover);
          });
        }
      }
    });
  }

  private clearCloseTimeout() {
    if (this.selectableTooltipCloseTimeoutId) {
      clearTimeout(this.selectableTooltipCloseTimeoutId);
      this.selectableTooltipCloseTimeoutId = null;
    }
  }

  private clearOpenTimeout() {
    if (this.selectableTooltipOpenTimeoutId) {
      clearTimeout(this.selectableTooltipOpenTimeoutId);
      this.selectableTooltipOpenTimeoutId = null;
    }
  }

  private delayClose(timeout: number) {
    this.clearCloseTimeout();
    this.selectableTooltipCloseTimeoutId = setTimeout(() => {
      if (this.selectableTooltipPopover != this.tooltipService.parentPopover) {
        this.tooltipService.closeTooltip(this.selectableTooltipPopover);
      }
    }, timeout);
  }
}
