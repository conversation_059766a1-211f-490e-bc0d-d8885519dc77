import { Directive, ElementRef, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

@Directive({
  selector: '[appTableSortIcon]'
})
export class TableSortIconDirective implements OnInit, OnChanges {

  @Input() sortingColumn: string;
  @Input() sortingOrder: 'asc' | 'desc';
  @Input() sortColumn: string;
  @Input() sortColumns: string[] = [];
  @Input() sortVisible: boolean = true;

  constructor(
    private el: ElementRef
  ) {
  }

  ngOnInit(): void {
    this.decorateElement();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.sortingColumn || changes.sortingOrder || changes.sortColumn || changes.sortColumns) {
      this.decorateElement();
    }
  }

  private buildSortImageEle() {
    const imgEle = document.createElement('img');
    imgEle.setAttribute('src', this.getSortIcon());
    return imgEle;
  }

  private getSortIcon(): string {
    if (this.sortingColumn === this.sortColumn || this.sortColumns?.includes(this.sortingColumn)) {
      const sortIcon = this.sortingOrder ? this.sortingOrder : 'active';
      return `assets/images/sort-${sortIcon}.svg`;
    }
    return 'assets/images/sort.svg';
  }

  private decorateElement() {
    if (!this.sortVisible) {
      return;
    }

    if (!this.el.nativeElement.dataset.originText) {
      this.el.nativeElement.dataset.originText = this.el.nativeElement.innerText.trim();
    }

    const sortIconContainerEle = document.createElement('div');
    sortIconContainerEle.className = 'sort-icon sort-icon-container';
    sortIconContainerEle.appendChild(this.buildSortImageEle());

    const textEle = document.createElement('div');
    textEle.className = 'pe-2 text-nowrap';
    textEle.innerText = this.el.nativeElement.dataset.originText;

    const mainContainerEle = document.createElement('div');
    mainContainerEle.className = 'w-100 cursor-pointer d-flex justify-content-start align-items-center';
    mainContainerEle.appendChild(textEle);
    mainContainerEle.appendChild(sortIconContainerEle);

    this.el.nativeElement.replaceChildren(mainContainerEle);
  }
}
