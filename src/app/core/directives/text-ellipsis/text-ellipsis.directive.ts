import { AfterViewInit, Directive, ElementRef, Input, OnDestroy, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appTextEllipsis]'
})
export class TextEllipsisDirective implements OnInit, AfterViewInit, OnDestroy {
  @Input() toggleCssClass: string = 'ellipsis-text-3';
  @Input() showMoreButtonCssClass: string = 'p-x-spacing-none';
  textOverFlow: boolean;
  isTextExpanded: boolean;
  showMoreEl;
  clickListener;

  constructor(private el: ElementRef, private renderer: Renderer2) {
  }

  ngOnInit() {
    this.showMoreEl = this.renderer.createElement("span");
    this.renderer.addClass(this.showMoreEl, 'd-inline-block');
    this.renderer.addClass(this.showMoreEl, 'button-main-ghost');
    this.renderer.addClass(this.showMoreEl, 'button-small');
    this.renderer.addClass(this.showMoreEl, 'content-label-small');

    (this.showMoreButtonCssClass || '').split(' ').forEach(cssClass => {
      if (cssClass?.length) {
        this.renderer.addClass(this.showMoreEl, cssClass);
      }
    });
  }

  ngAfterViewInit() {
    const container = this.el.nativeElement;
    if (container.scrollHeight > container.clientHeight) {
      this.showMoreEl.innerHTML = this.getShowMoreHtml();
      this.renderer.appendChild(container.parentNode, this.showMoreEl);
      this.clickListener = this.renderer.listen(this.showMoreEl, 'click', (evt: MouseEvent) => {
        evt.preventDefault();
        evt.stopPropagation();

        this.el.nativeElement.classList.toggle(this.toggleCssClass);
        this.isTextExpanded = !this.isTextExpanded;
        this.showMoreEl.innerHTML = this.getShowMoreHtml();
      });
      this.textOverFlow = true;
    }
  }

  getShowMoreHtml(): string {
    const icon = this.isTextExpanded ? 'fa-chevron-up' : 'fa-chevron-down';
    const text = this.isTextExpanded ? 'Show less' : 'Show more';
    return `${text} <i class="fa-regular ${icon}"></i>`;
  }

  ngOnDestroy() {
    if (this.clickListener) {
      this.clickListener();
    }
  }
}
