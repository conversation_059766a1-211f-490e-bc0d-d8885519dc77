import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { TooltipService } from '@core/services';

@Directive({
  selector: '[appTruncatableTooltip]'
})
export class TruncatableTooltipDirective {

  @Input() truncatableTooltipPopover: NgbPopover;
  @Input() truncatableTextCssSelector: string;
  @Input() truncatableTooltipCloseTimeout = 500;

  private shouldShowTooltip: boolean = undefined;

  constructor(
    private el: ElementRef,
    private tooltipService: TooltipService
  ) {
  }

  @HostListener('mouseleave', ['$event'])
  onMouseLeave(event: MouseEvent) {
    this.tooltipService.closeTooltip(this.truncatableTooltipPopover);
  }

  @HostListener('mouseover', ['$event'])
  onMouseOver(event: MouseEvent) {
    if (this.truncatableTooltipPopover && !this.truncatableTooltipPopover.isOpen() && this.canShowTooltip()) {
      this.tooltipService.openTooltip(this.truncatableTooltipPopover);
    }
  }

  private canShowTooltip(): boolean {
    const tagNameEle = this.el.nativeElement.querySelector(this.truncatableTextCssSelector) as HTMLElement;
    if (tagNameEle) {
      // Create a temporary element with the same content and styling
      const clone = document.createElement(tagNameEle.tagName);
      const computedStyles = window.getComputedStyle(tagNameEle);

      // Copy text content and essential styles
      clone.textContent = tagNameEle.textContent || '';
      clone.style.cssText = `
        position: absolute;
        visibility: hidden;
        white-space: ${computedStyles.whiteSpace};
        font-family: ${computedStyles.fontFamily};
        font-size: ${computedStyles.fontSize};
        font-weight: ${computedStyles.fontWeight};
        letter-spacing: ${computedStyles.letterSpacing};
        text-transform: ${computedStyles.textTransform};
        padding: ${computedStyles.padding};
      `;

      // Insert clone into DOM temporarily
      document.body.appendChild(clone);

      // Get measurements
      const originalWidth = tagNameEle.getBoundingClientRect().width;
      const fullTextWidth = clone.getBoundingClientRect().width;
      this.shouldShowTooltip = fullTextWidth - originalWidth > 0.1;

      // Clean up
      document.body.removeChild(clone);

      return this.shouldShowTooltip;
    }

    return false;
  }
}
