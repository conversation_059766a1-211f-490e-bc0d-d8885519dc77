import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

import { RoutingHistoryService, UserService } from '../services';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard  {
  constructor(
    private router: Router,
    private userService: UserService,
    private routingHistoryService: RoutingHistoryService
  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if (!this.userService.getUser().profile) {
      this.routingHistoryService.saveHistoryUrl(state.url);
    }

    return this.userService.isAuthenticated
      .pipe(take(1))
      .pipe(map((authenticated) => {
        if (!authenticated) {
          this.routingHistoryService.saveHistoryUrl(state.url);
          this.router.navigate(['auth/login']);
        }

        if (this.userService.isExternalUser()) {
          this.router.navigate(['error/403']);
          return false;
        }
        return authenticated;
      }));
  }
}
