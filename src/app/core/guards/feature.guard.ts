import { from, map, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

import { TOAST_ERROR_403, ToastService, UserService } from '../services';

@Injectable({
  providedIn: 'root'
})
export class FeatureGuard  {
  constructor(
    private router: Router,
    private userService: UserService,
    private toastService: ToastService
  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if (this.userService.getUser()?.profile) {
      return this.checkFeature(route, state);
    }

    return from(this.userService.populate()).pipe(
      map(() => this.checkFeature(route, state))
    ).toPromise();
    
  }

  private checkFeature(route, state): boolean {
    if (!this.checkCollaboratorAccess(route, state)) {
      this.router.navigate(['/']);
      this.toastService.show(TOAST_ERROR_403);
      return false;
    }

    if (!this.isFeatureAccessible(state)) {
      this.router.navigate(['/']);
      this.toastService.show(TOAST_ERROR_403);
      return false;
    }
    return true;
  }

  private checkCollaboratorAccess(route, state): boolean {
    const id = route.params.id;
    const url = state.url;
    if (url.includes('/profile/')  && id == 'new' && this.userService.isCollaboratorUser()) {
      return false;
    }
    return true;
  }

  private isFeatureAccessible(state): boolean {
    const url = state.url;
    const featureChecks = [
      { path: '/tags', check: () => this.userService.hasTagFeature() },
      { path: '/tasks', check: () => this.userService.hasTaskFeature() },
      { path: '/ratings', check: () => this.userService.hasTaskFeature() },
      { path: '/users', check: () => this.userService.canUseWorkflowFeature() },
      { path: '/groups', check: () => this.userService.canManageGroup() },
      { path: '/monitor', check: () => this.userService.hasFeature('monitor') },
      { path: '/landscape', check: () => this.userService.hasFeature('landscape') }
    ];

    for (const featureCheck of featureChecks) {
        if (url.includes(featureCheck.path) && !featureCheck.check()) {
            return false;
        }
    }
    return true;
  }
}
