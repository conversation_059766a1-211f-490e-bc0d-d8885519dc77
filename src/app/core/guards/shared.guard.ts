import { Observable, of } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

import { CollectionService, UserService } from '@core/services';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SharedGuard  {
  constructor(
    private router: Router,
    private userService: UserService,
    private collectionService: CollectionService,
  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if (this.isShareCollection(route, state)) {
      return this.shareCollection(route);
    }

    if (this.isSharePatent(state)) {
      return true;
    }

    this.router.navigate(['auth/login']);
    return false;
  }

  private isShareCollection(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const shareCode = route.params['share_code'];
    return state.url.startsWith('/collections/shared') && shareCode && shareCode.length > 0;
  }

  private isSharePatent(state: RouterStateSnapshot): boolean {
    return state.url.startsWith('/patent/view/shared');
  }

  private setExternalUser(resp): boolean {
    const token = resp?.auth?.access_token;
    if (token && token.length > 0) {
      this.userService.createExternalUser(token);
      return true;
    }

    return false;
  }

  private shareCollection(route: ActivatedRouteSnapshot): Observable<boolean> {
    return this.collectionService.getCollectionByShareCode(route.params['share_code']).pipe(
      map(resp => {
        let state = true;
        if(!this.userService.userAuthenticated()){
          state = this.setExternalUser(resp)
        }
        this.collectionService.setCollectionByShareCode(resp.data);
        return state;
      }),
      catchError(err => {
        this.router.navigate(['error/403']);
        return of(null);
      }));
  }
}
