// errors-handler.ts
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Injectable, Injector, isDevMode } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { ErrorService, SettingsService } from '@core/services';

@Injectable()
export class AppErrorHandler implements ErrorHandler {
  constructor(
    private injector: Injector,
    private settingsService: SettingsService
  ) {
  }

  handleError(error: Error | HttpErrorResponse) {
    const errorService = this.injector.get(ErrorService);
    if (error instanceof HttpErrorResponse) {
      // Server or connection error happened
      if (!navigator.onLine) {
        errorService.addError('Cannot reach Octimine server, please check your internet connection');
      } else if (error.status >= 500) {
        errorService.addError(error.message);
      } else if (error.status === 410) {
        errorService.addError('Search expired. To continue, please run the search again.');
      }
    } else {
      if (isDevMode() && !(error.message && error.message.startsWith('ExpressionChangedAfterItHasBeenCheckedError'))) {
        errorService.addError(error);
      }
      this.shareError(error);
    }
    // Log the error anyway
    console.error('Error occurred: ', error);
  }

  shareError(error) {
    error = this.unpackError(error);
  }

  unpackError(error) {
    if (error && error.ngOriginalError) {
      error = error.ngOriginalError;
    }
    if (typeof error === 'string' || error instanceof Error) {
      return error;
    }
    return null;
  }
}
