import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpParams,
  HttpRequest
} from '@angular/common/http';

import { JwtService, TOAST_ERROR_403, ToastService, ToastTypeEnum } from '../services';
import { catchError } from 'rxjs/operators';

@Injectable()
export class HttpTokenInterceptor implements HttpInterceptor {
  constructor(
    private jwtService: JwtService,
    private toastService: ToastService
  ) {
  }

  public intercept(
    req: HttpRequest<any>, next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const isFormData = req.body instanceof FormData;
    const redirectTo403Page = req.params.get('redirectTo403Page')?.toString() === '1';
    const excludeParams = ['redirectTo403Page'];

    const setHeaders = {};
    if (!isFormData) {
      if (!req.headers.get('accept')) {
        setHeaders['Accept'] = 'application/json';
      }

      if (!req.headers.get('Content-Type')) {
        setHeaders['Content-Type'] = 'application/json';
      }
    }

    const token = req.url.endsWith('auth/refresh') ? this.jwtService.getRefreshToken() : this.jwtService.getAccessToken();
    if (token) {
      setHeaders['Authorization'] = `Bearer ${token}`;
    }

    if (req.url.endsWith('monitor/ml_training')) {
      setHeaders['Refresh'] = 'Bearer ' + this.jwtService.getRefreshToken();
    }

    const setParams = {};
    for (const paramKey of req.params.keys()) {
      if (!excludeParams.includes(paramKey)) {
        setParams[paramKey] = req.params.get(paramKey);
      }
    }

    const params = new HttpParams({fromObject: setParams});

    const request = req.clone({params, setHeaders});
    return next.handle(request).pipe(
      catchError((error: Error | HttpErrorResponse) => {
        if (error instanceof HttpErrorResponse) {
          if (error.status === 403 && !error.url.endsWith('/users/invite') && !error.url.includes('/auth/signup_confirmation') && redirectTo403Page) {
            this.toastService.show(TOAST_ERROR_403);
            return;
          }
        }
        return throwError(error);
      })
    );
  }
}
