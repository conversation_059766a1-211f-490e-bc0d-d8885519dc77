import { Collection, Folder, PaginationMetadata } from '@core/services';
import { Feature, UserGroup, UserSubscription } from '@core/models/user.model';
import { AssigneeTypeEnum } from '@core/models/task.model';

export enum CollaborationResourceTypeEnum {
  COLLECTION = 'COLLECTION',
  FOLDER = 'FOLDER',
  PATENT = 'PATENT',
  LANDSCAPE = 'LANDSCAPE',
  PATENT_COMMENT = 'PATENT_COMMENT',
  PATENT_TAG = 'PATENT_TAG',
  REPLY_ON_TAGGED_COMMENT = 'REPLY_ON_TAGGED_COMMENT',
  COPIED_MONITOR = 'COPIED_MONITOR'
}

export enum CollaborationPermissionEnum {
  READONLY = 'READONLY',
  READ_WRITE = 'READ_WRITE'
}

export enum CollaborationStatusEnum {
  NEW = 'NEW',
  READ = 'READ',
  UNSHARED = 'UNSHARED',
}

export enum CollaboratorTypeEnum {
  USER = 'USER',
  GROUP = 'GROUP',
  COMPANY = 'COMPANY',
}

export enum UserStatusEnum {
  ACTIVE = 'Active',
  REGISTERED = 'Registered',
  BLOCKED = 'Blocked',
  DELEGATED = 'Delegated',
}

export interface PatentResource {
  id: number;
  title: string;
  publication_number: string;
}

export interface LandscapeResource {
  landscape_profile: {
    id: number,
    name: string,
    matched_documents_count: number
  };
}

export interface Collaboration {
  id: number;
  permission: CollaborationPermissionEnum;
  status: CollaborationStatusEnum;
  shared_at: string;
  collaborator_id: number;
  collaborator_type: CollaboratorTypeEnum;
  resource: Collection | Folder | PatentResource | LandscapeResource;
  resource_type: CollaborationResourceTypeEnum;
  resource_id: number;
  shared_by_id?: number;
  shared_by?: TeamUser;
  owner?: TeamUser;
  user?: TeamUser;
}

export enum TeamUserTypeEnum {
  USER = 'USER',
  GROUP = 'GROUP'
}

export interface TeamUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  display_name?: string;
  status?: UserStatusEnum | string;
  is_admin?: boolean;
  is_sales?: boolean;
  is_manager?: boolean;
  collaboration?: Collaboration;
  subscription?: UserSubscription;
  groups?: UserGroup[];
  features?: Feature[];
  more_users?: boolean;
  isSelected?: boolean;
  type?: TeamUserTypeEnum;
  is_system?: boolean;
}

export interface Collaborator {
  id: number;
  type: string;
  name: string;
}

export interface CollaborationsResponse {
  collaborations: Array<Collaboration>;
  page: PaginationMetadata;
}
