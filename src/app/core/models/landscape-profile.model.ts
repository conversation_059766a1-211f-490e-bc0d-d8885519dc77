import { BooleanSearchRequest, SemanticSearchRequest } from '@core/services';
import { TeamUser, UserGroup } from './';

export class LandscapeProfile {
  public name: string;
  public category: string;
  public created_at: string;
  public matched_documents_count: number;
  public unmapped_patent_numbers_count: number;
  public id: number;
  public submitted_boolean_results_count?: number;
  public submitted_semantic_results_count?: number;
  public submitted_patent_numbers_count?: number;
  public user_id: number;
  public covered_authorities_count: number;
  public top3_cpc_codes: Array<string>;
  public most_frequent_cpc_codes: Array<string>;
  public mean_patent_age_in_years: number;
  public high_risk_patents_count: number;
  public high_impact_patents_count: number;
  public high_recency_patents_count: number;
  public highly_cited_patents_count: number;
  public highly_cited_patents_threshold: number;
  public families_count?: number;
  public publications_count?: number;
  public updated_at: string;
  public oldest_patent: string;
  public newest_patent: string;
  public processed_input: ProcessedInput;
  public newest_document: number;
  public newest_priority_date: string;
  public oldest_document: number;
  public oldest_priority_date: string;
  public environmental_patents_count: number;
  public environmental_classification_codes: Array<string>;
  public active_patents_count: number;
  public inactive_patents_count: number;
  public unknown_legal_status_patents_count?: number;
  public users?: Array<TeamUser>;
  public groups?: Array<UserGroup>;
  public shared_by?: TeamUser;
  public status?: string;
  public competitive_profile_id?: number;
  public is_competitive_profile?: boolean;
  public charts?: any;
}

class ProcessedInput {
  public invalid?: Array<string>;
  public not_found?: Array<string>;
  public valid?: Array<string>;
  public semantic_query?: SemanticQuery;
  public boolean_query?: BooleanQuery;
  public patent_numbers_type?: string;
}

interface SemanticQuery {
  input: SemanticSearchRequest;
}

interface BooleanQuery {
  input: BooleanSearchRequest;
}
