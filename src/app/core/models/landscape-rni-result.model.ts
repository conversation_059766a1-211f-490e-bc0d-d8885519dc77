export enum LandscapeRniResultStatusEnum {
  COMPUTING = 'COMPUTING',
  FAILURE = 'FAILURE',
  SUCCESS = 'SUCCESS',
}

export interface LandscapeRniResultData {
  labels?: string[];
  source?: any[];
  threshold?: { simple_average: number[], weighted_average: number[] };
  count_retries?: number
}

export interface LandscapeRniResult {
  task_id: string;
  status: LandscapeRniResultStatusEnum;
  message?: string;
  data?: LandscapeRniResultData
}

export interface LandscapeRniResultResponse {
  data: LandscapeRniResult;
  status: number;
}
