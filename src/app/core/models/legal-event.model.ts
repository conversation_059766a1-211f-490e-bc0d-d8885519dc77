import { PaginationMetadata } from '@core/services';

export enum LegalEventImpactEnum {
  POSITIVE = 'POSITIVE',
  NEGATIVE = 'NEGATIVE',
  NEUTRAL = 'NEUTRAL'
}

export interface LegalEvent {
  event_attributes: Array<any>;
  event_class: string;
  event_class_description: string;
  event_code: string;
  event_code_description: string;
  event_date: string;
  effective_date: string;
  impact: LegalEventImpactEnum;
  publication_number: string;
}

export interface LegalEventsResponse {
  legal_events: Array<LegalEvent>;
  page: PaginationMetadata;
}
