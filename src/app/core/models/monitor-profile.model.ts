import { BooleanSearchRequest, PaginationMetadata } from '@core/services';
import { CollaboratorTypeEnum, MonitorRun, TeamUser, UserGroup } from '.';

export enum MonitorProfileDeliveryMethodEnum {
  TEXT = 'Text',
  PDF = 'PDF',
  EXCEL = 'Excel',
  CSV = 'CSV'
}

export enum MonitorProfileMachineLearningStatusEnum {
  PENDING = 'Pending',
  ERROR = 'Error',
  CREATING = 'Creating',
  TRAINING = 'Training',
  READY = 'Ready'
}

export enum LegalStatusPatentListScopeEnum {
  FAMILY = 'family',
  PUBLICATION = 'publication',
}

export interface MonitorProfile {
  id?: number;
  name: string;
  description: string;
  delivery_method: string;
  delivery_frequency: string;
  scope: string;
  ipc_codes?: any;
  company: string;
  boolean_query_active: boolean;
  boolean_query: BooleanSearchRequest;
  semantic_query_active: boolean;
  semantic_query: SemanticProfile;
  machine_learning_active: boolean;
  machine_learning_status: MonitorProfileMachineLearningStatusEnum;
  machine_learning_message: string;
  machine_learning_profile: MLProfile;
  last_run?: string;
  users?: Array<TeamUser>;
  groups?: Array<UserGroup>;
  updated_at?: string;
  active?: boolean;
  legal_status_active?: boolean;
  legal_status_profile?: LegalStatusProfile;
  legal_status_snapshot?: any;
  email_enabled?: boolean;
  collection_id?: number;  
}

interface LegalStatusProfile {
  patent_numbers: Array<string>;
  scope: LegalStatusPatentListScopeEnum;
}

interface SemanticProfile {
  search_input: string;
  patent_numbers: Array<string>;
  search_filters: SearchFilter;
}

export interface SearchFilter {
  text_weighting: number;
}

export interface MLProfile {
  search_input: string;
  patent_numbers: Array<string>;
  selected_text_publications: Array<string>;
  selected_number_publications: Array<string>;
}

export interface MonitorSharedProfile {
  id: number;
  name: string;
  description: string;
  delivery_method?: MonitorProfileDeliveryMethodEnum;
  legal_status_active?: boolean;
  shared_by?: TeamUser;
  scope?: string;
  collaborators?: Array<{
    id: number;
    type: CollaboratorTypeEnum;
  }>;
}

export interface MonitorSharedRuns {
  profile_info: MonitorSharedProfile;
  page: PaginationMetadata;
  shared_runs: Array<MonitorRun>;
}

