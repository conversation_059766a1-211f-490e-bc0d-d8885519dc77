export enum MonitorRunStatusEnum {
  PENDING = 'Pending',
  ERROR = 'Error',
  PROCESSING = 'Processing',
  FINISHED = 'Finished'
}

export enum MonitorRunSearchStatusEnum {
  PENDING = 'Pending',
  ERROR = 'Error',
  PROCESSING = 'Processing',
  FINISHED = 'Finished',
  SKIPPED = 'Skipped'
}

export enum MonitorRunMLStatusEnum {
  PENDING = 'Pending',
  ERROR = 'Error',
  GETTING_DATA = 'GettingData',
  LOADING_MODEL = 'LoadingModel',
  RUNNING_PREDICTION = 'RunningPrediction',
  CREATING_SNAPSHOT = 'CreatingSnapshot',
  FINISHED = 'Finished',
  SKIPPED = 'Skipped',
}

export enum MonitorRunTypeEnum {
  Initial = 'Initial',
  Manual = 'Manual',
  Scheduled = 'Scheduled',
  Feedback = 'Feedback'
}

export interface MonitorRun {
  boolean_status: MonitorRunSearchStatusEnum;
  citation_status: MonitorRunSearchStatusEnum;
  ml_status: MonitorRunMLStatusEnum;
  name: string;
  run_from: string;
  run_to: string;
  run_type: MonitorRunTypeEnum;
  semantic_status: MonitorRunSearchStatusEnum;
  status: MonitorRunStatusEnum;
  created_at: string;
  email_sent: string;
  id: number;
  number_of_documents: {
    BOOLEAN: number;
    CITATION: number;
    MACHINE_LEARNING: number;
    SEMANTIC: number;
    TOTAL: number;
  };
  shared_at?: string;
  profile_id: number;
  updated_at: string;
  user_id: number;
}
