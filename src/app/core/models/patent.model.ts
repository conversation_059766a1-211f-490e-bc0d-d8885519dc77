import { DocumentAnnotation, DocumentLabel, MlMatchTypeEnum, MlSourceTypeEnum } from '@core/services';
import { TeamUser } from '@core/models/collaboration.model';
import { UserGroup } from '@core/models/user.model';
import { TagModel } from './tag.model';
import { AssigneeTypeEnum, TaskModel } from '@core/models/task.model';

export interface Patent {
  bibliographic: Bibliographic;
  general: General;
  analytics: Analytic;
  machine_learning_feedback: MachineLearningFeedback;
  fulltext?: FullText;
  landscape?: LandscapeDraft;
  legal_status_changes?: Array<LegalStatusChange>;
  users?: Array<TeamUser>;
  groups?: Array<UserGroup>;
  annotations?: Array<{comments: Array<DocumentAnnotation>, labels: Array<DocumentLabel>}>;
  citations?: Array<any>;
  document_info?: Array<any>;
  legal_statuses?: Array<any>;
  custom_tags?: Array<TagModel>;
  tags?: {
    green: boolean;
    sep: boolean;
  };
  highlight?: DocumentHighlight;
  result: any;
  taskStats?: TaskStats;
}

interface Analytic {
  citation_backward_count: number;
  citation_forward_count: number;
  technology_broadness: number;
  consistency: number;
  impact: number;
  impact_weighted: number;
  impact_top10: number;
  risk: number;
  risk_weighted: number;
  risk_top10: number;
  recency: number;
  recency_top10: number;
  recency_year: number;
  market_coverage: number;
  market_coverage_gdp: number;
}

interface Bibliographic {
  title: string;
  title_publication_number?: string;
  abstract?: string;
  abstract_publication_number?: string;
  cpc?: Array<string>;
  publication_date?: string;
  priority_date?: string;
  anticipated_expiration_date?: string;
  inventors?: Array<string>;
  inventors_original?: Array<string>;
  applicants: Array<string>;
  applicants_original: Array<string>;
  tech_areas: Array<string>;
  tech_fields: Array<string>;
  assignees: Array<string>;
  assignees_original: Array<string>;
  ipc: Array<string>;
  ipc4: Array<string>;
  legal_status?: Array<string>;
  also_published_as: Array<string>;
  owners?: Array<Owner>;
  owner_ids?: Array<number>;
  ultimate_owners?: Array<Owner>;
}

interface General {
  docdb_family_id: string;
  similarity_index: number;
  publication_number: number;
  raw_publication_number?: string;
  obfuscated?: boolean;
  original_number?: string;
  original_number_normalized?: string;
  rank: number;
  green_codes?: GreenCode[];
}

interface FullText {
  claims?: string;
  claims_publication_number?: string;
  description?: string;
  description_publication_number?: string;
}

export interface DocumentHighlight {
  title?: string;
  abstract?: string;
  claims?: string;
  description?: string;
}

interface LandscapeDraft {
  id: number;
  document_id: number;
  source: string;
  status: string;
}

export interface MachineLearningFeedback {
  source: MlSourceTypeEnum;
  match: MlMatchTypeEnum;
  profile_id: number;
  document_id: number;
  created_at: Date;
  updated_at: Date;
}

interface LegalStatusChange {
  current_status: LegalStatus;
  previous_status: LegalStatus;
  family_id: number;
  publication_number: string;
}

interface LegalStatus {
  extended: string;
  general: string;
}

interface GreenCode {
  [code: string]: {root_title: string,parent_title: string,title: string}
}

interface Owner {
  id: number;
  name: string;
  root_id: number;
}

export interface LegalStatusIcon {
  icon: string;
  tooltip: string;
  description: string;
  name: string;
}

export interface TaskStats {
  todo: Array<TaskModel>;
  in_progress: Array<TaskModel>;
  five_point: Array<TaskModel>;
  four_point: Array<TaskModel>;
  three_point: Array<TaskModel>;
  two_point: Array<TaskModel>;
  one_point: Array<TaskModel>;
  tasks_by_assignee: Record<string, TasksByAssigneeStats>;
  all_tasks: Array<TaskModel>;
  all_ratings: Array<TaskModel>;
}

export interface TasksByAssigneeStats {
  assignee: { id: number, type: AssigneeTypeEnum };
  team_user: TeamUser;
  all_tasks: Array<TaskModel>;
  ratings: Array<TaskModel>,
  answerableRequests: Array<TaskModel>,
  createdRequests: Array<TaskModel>,
  otherRequests: Array<TaskModel>
}
