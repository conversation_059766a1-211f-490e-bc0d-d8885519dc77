export interface Publication {
    bibliographic: PublicationBibliographic;
    general: PublicationGeneral;
    fulltext?: PublicationFullText;
    custom_toaster?: string;
}

interface PublicationBibliographic {
    abstract?: string;
    abstract_lang?: string;
    applicants: Array<string>;
    applicants_original: Array<string>;
    application_number: string;
    assignees: Array<string>;
    assignees_original: Array<string>;
    cpc?: Array<string>;
    cpc4?: Array<string>;
    docdb_family_id: number;
    inventors?: Array<string>;
    inventors_original?: Array<string>;
    ipc: Array<string>;
    ipc4: Array<string>;
    legal_status?: Array<string>;
    publication_date?: string;
    publication_number?: string;
    tech_areas: Array<string>;
    tech_fields: Array<string>;
    title: string;
    title_lang: string;
    priority_claims?: Array<{linkage_type: string, priority_date: string, priority_number: string}>;
}

interface PublicationGeneral {
    docdb_family_id: string;
    obfuscated?: boolean;
    original_number?: string;
    original_numbers?: string;
    publication_number: number;
    rank: number;
}

interface PublicationFullText {

    claims: string;
    claims_lang: string;

    description: string;
    description_lang: string;
}
