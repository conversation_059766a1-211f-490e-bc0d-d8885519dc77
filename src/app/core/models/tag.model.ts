import { TeamUser } from "./collaboration.model";

export interface TagModel {
  id?: number;
  name: string;
  color: string;
  company_id?: number;
  user_id?: number;
  user?: TeamUser;
  created_at?: string;
  updated_at?: string;
  assigned_at?: string;
  tag_document_id?: number;
  documents_count?: number;
  collection_id?: number;
  assigner_id?: number;
  assigned_by?: TeamUser;
  private?: boolean;
}
