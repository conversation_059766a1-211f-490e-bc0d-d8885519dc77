import { TeamUser } from '@core/models/collaboration.model';
import { Patent } from '@core/models/patent.model';
import { TagModel } from '@core/models/tag.model';
import { TopicModel } from '@core/models/topic.model';

export enum TaskTypeEnum {
  STAR_RATING = 'STAR_RATING',
  YES_NO_ANSWER = 'YES_NO_ANSWER',
  TEXT_REPLY = 'TEXT_REPLY',
  LABELS = 'LABELS'
}

export enum TaskStatusEnum {
  NEW = 'NEW',
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  DONE = 'DONE'
}

export enum TaskAssignmentStatusEnum {
  NEW = 'NEW',
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  DONE = 'DONE',
  OVERDUE = 'OVERDUE'
}

export enum TaskResourceTypeEnum {
  DOCUMENT = 'DOCUMENT',
  COLLECTION = 'COLLECTION',
  MONITOR_RUN = 'MONITOR_RUN'
}

export enum TaskDisplayTypeEnum {
  INCOMING = 'INCOMING',
  OUTGOING = 'OUTGOING'
}

export enum TaskRedirectModeEnum {
  PATENT_VIEW = 'PATENT_VIEW',
  RESOURCE_VIEW = 'RESOURCE_VIEW',
}

export enum AssigneeTypeEnum {
  USER = 'USER',
  GROUP = 'GROUP'
}

export interface Assignee {
  id: number;
  type: AssigneeTypeEnum;
}

export interface TaskModel {
  id: number;
  author_id: number;
  resource_id: number;
  resource_type: TaskResourceTypeEnum;
  subject: string;
  description: string;
  task_type: TaskTypeEnum;
  status: TaskStatusEnum;
  deadline: string;
  created_at?: Date;
  updated_at?: Date;
  assignments?: TaskAssignmentModel[];
  assignee_ids?: number[];
  document_ids?: number[];
  task_types?: TaskTypeEnum[];
  assignees?: Assignee[];
  team_users?: TeamUser[];
  display_type?: TaskDisplayTypeEnum;
  author?: TeamUser;
  assignedTagsByAssignees?: TagModel[];
  topic_ids?: number[];
  topics?: TopicModel[];
  company_id?: number;
  document_id: number;
}

export interface TaskAssignmentModel {
  id: number;
  task_id: number;
  assignee_id: number;
  document_id: number;
  answer: string;
  answered_user?: TeamUser;
  answered_by?: number;
  status: TaskAssignmentStatusEnum;
  answered_at: Date;
  edited_at: Date;
  task?: TaskModel;
  assignee?: TeamUser;
  assignee_type?: AssigneeTypeEnum;
  message?: string;
  document?: Patent;
  assignedTagsByAssignee?: TagModel[];
  is_deletable?: boolean;
  is_answerable?: boolean;
}

export interface TaskFormError {
  messages: string[];
  details: { [key: string]: string[] };
}

export interface TaskTypeItem {
  value: TaskTypeEnum;
  label: string;
  icon: string;
  description: string;
}
