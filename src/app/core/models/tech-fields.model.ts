export const TECH_FIELDS = [
  {
    area: 'Chemistry',
    name: 'Basic Materials Chemistry',
    value: 'Basic Materials Chemistry'
  },
  {
    area: 'Chemistry',
    name: 'Biotechnology',
    value: 'Biotechnology'
  },
  {
    area: 'Chemistry',
    name: 'Chemical Engineering',
    value: 'Chemical Engineering'
  },
  {
    area: 'Chemistry',
    name: 'Environmental Technology',
    value: 'Environmental Technology'
  },
  {
    area: 'Chemistry',
    name: 'Food Chemistry',
    value: 'Food Chemistry'
  },
  {
    area: 'Chemistry',
    name: 'Macromolecular Chemistry, Polymers',
    value: 'Macromolecular Chemistry, Polymers'
  },
  {area: 'Chemistry', name: 'Materials, Metallurgy', value: 'Materials, Metallurgy'},
  {
    area: 'Chemistry',
    name: 'Micro-Structural and Nano-Technology',
    value: 'Micro-Structural and Nano-Technology'
  },
  {
    area: 'Chemistry',
    name: 'Organic Fine Chemistry',
    value: 'Organic Fine Chemistry'
  },
  {
    area: 'Chemistry',
    name: 'Pharmaceuticals',
    value: 'Pharmaceuticals'
  },
  {
    area: 'Chemistry',
    name: 'Surface Technology, Coating',
    value: 'Surface Technology, Coating'
  },
  {
    area: 'Electrical Engineering',
    name: 'Audio-Visual Technology',
    value: 'Audio-Visual Technology'
  },
  {
    area: 'Electrical Engineering',
    name: 'Basic Communication Processes',
    value: 'Basic Communication Processes'
  },
  {
    area: 'Electrical Engineering',
    name: 'Computer Technology',
    value: 'Computer Technology'
  },
  {
    area: 'Electrical Engineering',
    name: 'Digital Communication',
    value: 'Digital Communication'
  },
  {
    area: 'Electrical Engineering',
    name: 'Electrical Machinery, Apparatus, Energy',
    value: 'Electrical Machinery, Apparatus, Energy'
  },
  {
    area: 'Electrical Engineering',
    name: 'IT Methods for Management',
    value: 'IT Methods for Management'
  },
  {
    area: 'Electrical Engineering',
    name: 'Semiconductors',
    value: 'Semiconductors'
  },
  {
    area: 'Electrical Engineering',
    name: 'Telecommunications',
    value: 'Telecommunications'
  },
  {
    area: 'Instruments',
    name: 'Analysis of Biological Materials',
    value: 'Analysis of Biological Materials'
  },
  {area: 'Instruments', name: 'Control', value: 'Control'},
  {area: 'Instruments', name: 'Measurement', value: 'Measurement'},
  {
    area: 'Instruments',
    name: 'Medical Technology',
    value: 'Medical Technology'
  },
  {area: 'Instruments', name: 'Optics', value: 'Optics'},
  {
    area: 'Mechanical Engineering',
    name: 'Engines, Pumps, Turbines',
    value: 'Engines, Pumps, Turbines'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Handling',
    value: 'Handling'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Machine Tools',
    value: 'Machine Tools'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Mechanical Elements',
    value: 'Mechanical Elements'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Other Special Machines',
    value: 'Other Special Machines'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Textile and Paper Machines',
    value: 'Textile and Paper Machines'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Thermal Processes and Apparatus',
    value: 'Thermal Processes and Apparatus'
  },
  {
    area: 'Mechanical Engineering',
    name: 'Transport',
    value: 'Transport'
  },
  {
    area: 'Other Fields',
    name: 'Civil Engineering',
    value: 'Civil Engineering'
  },
  {
    area: 'Other Fields',
    name: 'Other Consumer Goods',
    value: 'Other Consumer Goods'
  },
  {area: 'Other Fields', name: 'Furniture, Games', value: 'Furniture, Games'}
];

export function techFieldsToNodes() {
  const techFieldNodes = {};

  TECH_FIELDS.forEach(item => {
    if (!techFieldNodes[item.area]) {
      techFieldNodes[item.area] = {
        name: item.area, id: item.area, children: []
      };
    }

    techFieldNodes[item.area].children.push({
      name: item.name, id: item.name
    });
  });

  return Object.values(techFieldNodes);
}

export const MAIN_TECHNOLOGY_AREAS = [
  {value: 'ma1', label: 'Electrical Engineering'},
  {value: 'ma2', label: 'Instruments'},
  {value: 'ma3', label: 'Chemistry'},
  {value: 'ma4', label: 'Mechanical Engineering'},
  {value: 'ma5', label: 'Other Fields'},
];

export const MAIN_TECHNOLOGY_AREA_KEYS = MAIN_TECHNOLOGY_AREAS.map(item => item.value);
