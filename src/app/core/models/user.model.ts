import { Collaboration, UserStatusEnum } from '@core/models/collaboration.model';

export enum SubscriptionType {
  Free = 'FREE',
  Basic = 'BASIC',
  Professional = 'PROFESSIONAL',
  Enterprise = 'ENTERPRISE',
  Trial = 'TRIAL',
  External = 'EXTERNAL', // For now used only to identify the shared search user
  Collaborator = 'COLLABORATOR'
}

export enum DelegationSourceEnum {
  IP_LOUNGE = 'IP_LOUNGE'
}

export interface UserProfile {
  id: number;
  company_id: number;
  company_name: string;
  department_name: string;
  created_at: Date;
  email: string;
  first_name: string;
  last_name: string;
  is_admin: boolean;
  is_manager: boolean;
  is_sales: boolean;
  sales_rep_id: number;
  valid_to: Date;
  country: string;
  save_history: boolean;
  gdpr: boolean;
  terms_and_conditions: boolean;
  phone1: string;
  phone2: string;
  mobile: string;
  website: string;
  newsletter: boolean;
  ui_settings: object;
  must_change_password: boolean;
  features: Array<Feature>;
  status: UserStatusEnum;
  delegation_source: DelegationSourceEnum;
  two_factor_authentication_enabled: boolean;
  locale: string;
  groups: UserGroup[];
  company?: {id: number, name:string};
}

export class UserSubscription {
  type: SubscriptionType;
  valid_until: Date;
  api_package: number;
  api_usage: number;
  api_access_throttle: number;
  api_max_result: number;
  self_managed: boolean;
}

export class User {
  profile: UserProfile;
  subscription: UserSubscription;
}

export class UserStatistics {
  total_searches: number;
  monthly_quota: number;
  remaining_quota: number;
}

export class Feature {
  id: number;
  name: string;
  description: string;
  short_name: string;
}

export class UserGroup {
  id: number;
  name: string;
  description: string;
  created_at?: string;
  updated_at?: string;
  is_system?: boolean;
  collaboration?: Collaboration;
}
