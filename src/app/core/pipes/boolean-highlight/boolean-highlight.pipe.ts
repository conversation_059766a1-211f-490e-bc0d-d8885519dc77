import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'booleanHighlight'
})
export class BooleanHighlightPipe implements PipeTransform {

  transform(value: string, highlightClass: string): string {
    if(value?.length>0){
      const regex = new RegExp(/(?:OR|AND)?\s*(CPC|IPC|CPC4|IPC4|APPLICANTS|INVENTORS|APPLICANTS_ORIGINAL|INVENTORS_ORIGINAL|TITLE|ABSTRACT|TECH_FIELDS|AUTHORITIES|RAW_PUBLICATION_NUMBER|PUBLICATION_DATE|PRIORITY_DATE|PUBLICATION_AUTHORITY|ALSO_PUBLISHED_AS|CLAIMS|DESCRIPTION|TEXT|PUBLICATION_KIND|APPLICATION_NUMBERS|RISK|IMPACT|MARKET_COVERAGE|TECHNOLOGY_BROADNESS|RECENCY|RECENCY_YEARS|CONSISTENCY|CITATION_BACKWARD_COUNT|CITATION_FORWARD_COUNT|NUMBER_OF_AUTHORITIES|LEGAL_STATUS|TAG)/g );
      return value.replace(regex, (match: string) => {
        return `<span class="${highlightClass}">${match}</span>`;
      });
    }
    return value;
  }

}
