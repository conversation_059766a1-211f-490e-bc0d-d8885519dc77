import { BypassSecurityPipe } from './bypass-security.pipe';
import {
  <PERSON><PERSON>aniti<PERSON>,
  SafeHtml,
  SafeResourceUrl,
  SafeScript,
  SafeStyle,
  SafeUrl,
  SafeValue
} from '@angular/platform-browser';
import { SecurityContext } from '@angular/core';

describe('SanitizerPipe', () => {
  it('create an instance', () => {
    class TestDomSanitizerImpl implements DomSanitizer {
      bypassSecurityTrustHtml(value: string): SafeHtml {
        return undefined;
      }

      bypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {
        return undefined;
      }

      bypassSecurityTrustScript(value: string): SafeScript {
        return undefined;
      }

      bypassSecurityTrustStyle(value: string): SafeStyle {
        return undefined;
      }

      bypassSecurityTrustUrl(value: string): SafeUrl {
        return undefined;
      }

      sanitize(context: SecurityContext, value: SafeValue | string | null | {}): string | null {
        return undefined;
      }
    }

    const pipe = new BypassSecurityPipe(new TestDomSanitizerImpl());
    expect(pipe).toBeTruthy();
  });
});
