import { CountSerialPipe } from '@core/pipes';
import { PaginationMetadata } from '@core/services';

describe('CountSerialPipe', () => {

  let pipe: CountSerialPipe;
  let pagination: PaginationMetadata;

  beforeEach(() => {
    pipe = new CountSerialPipe();
  });
  it('providing undefined returns 0', () => {
    pagination = undefined;
    expect(pipe.transform(pagination, NaN)).toBe(0);
  });
  it('providing data returns 18', () => {
    pagination = {
      current_page: 1,
      last_page: 1,
      page_size: 25,
      total_hits: 21,
      origin_total_hits: 0
    };
    expect(pipe.transform(pagination, 17)).toBe(18);
  });
  it('providing data returns 32', () => {
    pagination = {
      current_page: 2,
      last_page: 2,
      page_size: 25,
      total_hits: 41,
      origin_total_hits: 0
    };
    expect(pipe.transform(pagination, 6)).toBe(32);
  });
});
