import { Pipe, PipeTransform } from '@angular/core';
import { PaginationMetadata } from '@core/services';

/**
 * countSerial
 *
 * Pi<PERSON> for making patent table serial number for rows
 * <AUTHOR> <<EMAIL>>
 * @example
 * {{pagination | countSerial:index}}
 */
@Pipe({
  name: 'countSerial'
})
export class CountSerialPipe implements PipeTransform {
  /**
   * Pipe transform
   *
   * make patent table serial number for each row
   * @param value pagination Meta object
   * @param index Index of single patent object in patent array
   * @returns serial number of patent object
   */
  transform(value: PaginationMetadata, index: number): number {
    if (value) {
      return ((value.page_size * (value.current_page - 1)) + index + 1);
    }
    return 0;
  }
}
