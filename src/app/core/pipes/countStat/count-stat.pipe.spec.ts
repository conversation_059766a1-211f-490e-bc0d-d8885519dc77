import { CountStatPipe } from '@core/pipes';
import { PaginationMetadata } from '@core/services';

describe('CountStatPipe', () => {
  let pipe: CountStatPipe;
  let pagination: PaginationMetadata;

  beforeEach(() => {
    pipe = new CountStatPipe();
  });

  it('providing undefined returns empty', () => {
    pagination = undefined;
    expect(pipe.transform(pagination)).toBe('');
  });

  it('providing data returns (Showing 1 - 9 of 9)', () => {
    pagination = {
      current_page: 1,
      last_page: 1,
      page_size: 25,
      total_hits: 9,
      origin_total_hits: 0
    };
    expect(pipe.transform(pagination)).toBe('Showing 1 - 9 of 9');
  });

  it('providing data returns (Showing 26 - 27 of 27)', () => {
    pagination = {
      current_page: 2,
      last_page: 2,
      page_size: 25,
      total_hits: 27,
      origin_total_hits: 0
    };
    expect(pipe.transform(pagination)).toBe('Showing 26 - 27 of 27');
  });

  it('providing data and format returns (displaying 1 to 9 of total 9)', () => {
    pagination = {
      current_page: 1,
      last_page: 1,
      page_size: 25,
      total_hits: 9,
      origin_total_hits: 0
    };
    expect(pipe.transform(pagination, 'displaying {START} to {END} of total {TOTAL}')).toBe('displaying 1 to 9 of total 9');
  });

});
