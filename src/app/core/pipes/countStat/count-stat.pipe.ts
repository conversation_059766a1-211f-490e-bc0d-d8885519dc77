import { Pipe, PipeTransform } from '@angular/core';
import { PaginationMetadata } from '@core/services';

@Pipe({
  name: 'countStat'
})
export class CountStatPipe implements PipeTransform {

  transform(value: PaginationMetadata, format: string = 'Showing {START} - {END} of {TOTAL}'): string {
    if (value) {
      const start = ((value.page_size.valueOf() * (value.current_page.valueOf() - 1)) + 1).toString();
      const end = ((value.total_hits.valueOf() - (value.page_size.valueOf() * value.current_page)) > 0 ?
        (value.page_size.valueOf() * value.current_page).toString() : value.total_hits.toString());
      const total = value.total_hits.toString();
      format = format.replace('{START}', start);
      format = format.replace('{END}', end);
      format = format.replace('{TOTAL}', total);

      return format;
    }
    return '';
  }

}
