import { DateFormatPipe } from './date-format.pipe';

describe('dateFormatPipe', () => {

  let pipe: DateFormatPipe;

  beforeEach(() => {
    pipe = new DateFormatPipe(null);
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('parse date range', () => {
    const input = '2022-03-23 - 2022-03-29';
    const expectedResult = 'March 23rd - 29th, 2022';
    expect(pipe.transform(input)).toBe(expectedResult);
  });

  it('parse date range in same month and year', () => {
    const input = '2022-03-23 - 2022-03-29';
    const expectedResult = 'March 23rd - 29th, 2022';
    expect(pipe.transform(input, 'Range')).toBe(expectedResult);
  });

  it('parse date range in same month and different year', () => {
    const input = '2021-11-23 - 2021-12-02';
    const expectedResult = 'November 23rd - December 2nd, 2021';
    expect(pipe.transform(input, 'Range')).toBe(expectedResult);
  });

  it('parse date range in different month and year', () => {
    const input = '2021-12-23 - 2022-01-03';
    const expectedResult = 'December 23rd, 2021 - January 3rd, 2022';
    expect(pipe.transform(input, 'Range')).toBe(expectedResult);
  });

  it('parse date', () => {
    const input = '2022-03-23';
    const expectedResult = 'March 23rd, 2022';
    expect(pipe.transform(input, 'Date')).toBe(expectedResult);
  });
});
