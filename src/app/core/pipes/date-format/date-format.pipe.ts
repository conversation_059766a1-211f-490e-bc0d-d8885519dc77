import moment from 'moment-timezone';
import { UserService } from './../../services/user/user.service';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateFormat'
})
export class DateFormatPipe implements PipeTransform {

  constructor(private userService: UserService) {}

  transform(value: string, format: 'Range' | 'Date' | 'ShortDate' | 'ShortDateTime' = 'Range', localizeTime: boolean = false): string {
    if (format === 'Range' && value.length === 23) {
      const dates = value.split(' - ');
      const start = new Date(dates[0]);
      const end = new Date(dates[1]);
      if (start.getMonth() === end.getMonth() && start.getFullYear() === end.getFullYear() ) {
        return `${this.getMonth(end)} ${this.getDay(start)} - ${this.getDay(end)}, ${end.getFullYear()}`;
      } else if (start.getMonth() !== end.getMonth() && start.getFullYear() === end.getFullYear()) {
        return `${this.getMonth(start)} ${this.getDay(start)} - ${this.getMonth(end)} ${this.getDay(end)}, ${end.getFullYear()}`;
      } else if (start.getMonth() !== end.getMonth() && start.getFullYear() !== end.getFullYear()) {
        return `${this.getMonth(start)} ${this.getDay(start)}, ${start.getFullYear()} - ${this.getMonth(end)} ${this.getDay(end)}, ${end.getFullYear()}`;
      }
    } else if (format === 'Date') {
      const date = new Date(value);
      return `${this.getMonth(date)} ${this.getDay(date)}, ${date.getFullYear()}`;
    } else if (format === 'ShortDate') {
      const date = new Date(value);
      return this.getShortDate(date);
    } else if (format === 'ShortDateTime') {
      const date = new Date(value+'Z');
      return this.getShortDateTime(date, localizeTime);
    }
    return value;
  }

  private getMonth(d: Date): string {
    return d.toLocaleString('en-GB', { month: 'long' });
  }

  private getDay(d: Date): string {
    const i = d.getDate();
    const j = i % 10,
        k = i % 100;
    if (j === 1 && k !== 11) {
        return i + 'st';
    }
    if (j === 2 && k !== 12) {
        return i + 'nd';
    }
    if (j === 3 && k !== 13) {
        return i + 'rd';
    }
    return i + 'th';
  }

  private getShortDate(d: Date): string {
    const options: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    };
    return d.toLocaleDateString(this.userService.getLocale().language, options);
  }

  private getShortDateTime(d: Date, localizeTime: boolean): string {
    const options: Intl.DateTimeFormatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        hourCycle: 'h24',
        minute: '2-digit'
    };
    if(localizeTime){
      options.timeZone = moment.tz.guess();
    }
    return d.toLocaleDateString(this.userService.getLocale().language, options).replace(',', '');
  }
}
