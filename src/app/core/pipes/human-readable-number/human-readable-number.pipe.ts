import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'humanReadableNumber'
})
export class HumanReadableNumberPipe implements PipeTransform {

  private PREFIXES = {
    '24': 'Y',
    '21': 'Z',
    '18': 'E',
    '15': 'P',
    '12': 'T',
    '9': 'G',
    '6': 'M',
    '3': 'k',
    '0': '',
    '-3': 'm',
    '-6': 'µ',
    '-9': 'n',
    '-12': 'p',
    '-15': 'f',
    '-18': 'a',
    '-21': 'z',
    '-24': 'y'
  };

  transform(value: any, precision: number = 3): string {
    return this.toHumanString(value.toString(), precision);
  }

  private getExponent(val: number): number {
    if (val === 0) {
      return 0;
    }
    return Math.floor(Math.log10(Math.abs(val)));
  }

  private precise(val: number, precision: number): number {
    return Number.parseFloat(val.toPrecision(precision));
  }

  private toHumanString(val: string, precision: number = 3): string {
    const n = this.precise(Number.parseFloat(val), precision);
    const e = Math.max(Math.min(3 * Math.floor(this.getExponent(n) / 3), 24), -24);
    return this.precise(n / Math.pow(10, e), precision).toString() + this.PREFIXES[e];
  }
}
