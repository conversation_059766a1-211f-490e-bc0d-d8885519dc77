import { InterpolateColorsPipe } from './interpolate-colors.pipe';

describe('InterpolateColorsPipe', () => {
  let pipe: InterpolateColorsPipe;

  beforeEach(() => {
    pipe = new InterpolateColorsPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('validate colorStart RGB formate', () => {
    const colorStart = 'rgb(1512, 44, 87)';
    const colorEnd = 'rgb(211, 219, 231)';
    const palateCount = 6;
    const expectedResult = [];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('validate colorEnd RGB formate', () => {
    const colorStart = 'rgb(15, 44, 87)';
    const colorEnd = 'rgba(211, 219, 21)';
    const palateCount = 6;
    const expectedResult = [];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('create color palate of 0', () => {
    const colorStart = 'rgb(15, 44, 87)';
    const colorEnd = 'rgb(211, 219, 231)';
    const palateCount = 0;
    const expectedResult = [];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('create color palate of 1', () => {
    const colorStart = 'rgb(15, 44, 87)';
    const colorEnd = 'rgb(211, 219, 231)';
    const palateCount = 1;
    const expectedResult = ['rgb(15,44,87)'];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('create color palate of 2', () => {
    const colorStart = 'rgb(15, 44, 87)';
    const colorEnd = 'rgb(211, 219, 231)';
    const palateCount = 2;
    const expectedResult = ['rgb(15,44,87)', 'rgb(211,219,231)'];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('create color palate of 3', () => {
    const colorStart = 'rgb(15, 44, 87)';
    const colorEnd = 'rgb(211, 219, 231)';
    const palateCount = 3;
    const expectedResult = ['rgb(15,44,87)', 'rgb(113,132,159)', 'rgb(211,219,231)'];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });

  it('create color palate of 5', () => {
    const colorStart = 'rgb(224, 220, 233)';
    const colorEnd = 'rgb(102, 81, 145)';
    const palateCount = 5;
    const expectedResult = ['rgb(224,220,233)', 'rgb(194,185,211)', 'rgb(163,151,189)', 'rgb(133,116,167)', 'rgb(102,81,145)'];
    expect(pipe.transform(colorStart, colorEnd, palateCount)).toEqual(expectedResult);
  });
});
