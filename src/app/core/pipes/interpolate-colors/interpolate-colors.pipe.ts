import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'interpolateColors'
})
export class InterpolateColorsPipe implements PipeTransform {

  /**
  * This function will take two RGB string color and a number to give back a array of color palate.
  * The color palate array length will be same as give number
  * @param colorStart RGB color string from which the colors palate starts
  * @param colorEnd RGB color string to  which the colors palate ends
  * @param palateCount Number of color palate in output
  * @returns Array of color palate strings in RGB format
  */
  transform(colorStart, colorEnd, palateCount: number): string[] {
    const matchColors = /rgb\((\d{1,3}), (\d{1,3}), (\d{1,3})\)/;
    if (matchColors.exec(colorStart) === null || matchColors.exec(colorEnd) === null || palateCount < 1) {
      return [];
    }
    const stepFactor = palateCount === 1 ? .5 : 1 / (palateCount - 1),
      interpolatedColorArray = [];

    colorStart = colorStart.match(/\d+/g).map(Number);
    colorEnd = colorEnd.match(/\d+/g).map(Number);

    for(let i = 0; i < palateCount; i++) {
        interpolatedColorArray.push(this.interpolateColor(colorStart, colorEnd, stepFactor * i));
    }

    return interpolatedColorArray;
  }

  /**
   * This function will take two array of RGB color Numbers and a factor number to generated color shade string of given factor in RGB format
   * @param rgbArrStart RGB color number array
   * @param rgbArrEnd RGB color number array
   * @param factor factor by which new RGB color will be generated
   * @returns Generated color shade string of given factor in RGB format
   */
  private interpolateColor(rgbArrStart: number[], rgbArrEnd: number[], factor = 0.5): string {
    const result = rgbArrStart.slice();
    for (let i = 0; i < 3; i++) {
        result[i] = Math.round(result[i] + factor * (rgbArrEnd[i] - rgbArrStart[i]));
    }
    return `rgb(${result[0]},${result[1]},${result[2]})`;
  }
}
