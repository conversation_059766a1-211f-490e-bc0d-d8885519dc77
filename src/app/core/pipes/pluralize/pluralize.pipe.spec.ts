import { PluralizePipe } from '@core/pipes';

describe('Pipe: Pluralize', () => {
  let pipe: PluralizePipe;

  beforeEach(() => {
    pipe = new PluralizePipe();
  });

  it('providing null returns null', () => {
    expect(pipe.transform(null, 10)).toBe(null);
  });

  it('empty string', () => {
    expect(pipe.transform('', 0)).toBe('');
  });

  it('empty string', () => {
    expect(pipe.transform('', 0)).toBe('');
  });

  it('pluralize zero', () => {
    expect(pipe.transform('bird', 0)).toBe('birds');
  });

  it('pluralize one', () => {
    expect(pipe.transform('Bird', 1)).toBe('Bird');
  });

  it('pluralize multiple', () => {
    expect(pipe.transform('Bird', 2)).toBe('Birds');
  });

  it('pluralize plural', () => {
    expect(pipe.transform('birds', 2)).toBe('birds');
  });

  it('pluralize difficult', () => {
    expect(pipe.transform('matrix', 2)).toBe('matrices');
  });
});
