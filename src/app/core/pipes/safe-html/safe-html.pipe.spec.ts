import { SafeHtmlPipe } from '@core/pipes';

describe('Pipe: SafeHtml', () => {
  it('create an instance', () => {
    const p = new SafeHtmlPipe();
    expect(p).toBeTruthy();
  });

  let pipe: SafeHtmlPipe;

  beforeEach(() => {
    pipe = new SafeHtmlPipe();
  });

  const naVal = null;

  it('providing an NA string returns N/A', () => {
    expect(pipe.transform(naVal, '<br/>', 'N/A')).toBe('N/A');
  });

  it('providing an NA string returns N/AN/A', () => {
    expect(pipe.transform(naVal, '<br/', 'N/AN/A')).toBe('N/AN/A');
  });

  const newlinesVal = 'a\r\n\r\n\nb\n\n\r\n \r\n   \nc\r\n\n\n';

  it('providing multi break lines string returns a string which is replaced by br tags', () => {
    expect(pipe.transform(newlinesVal)).toBe('a<br/>b<br/>c');
  });

  const scriptVal = '<b>Test <script>This is a script text <a>This is a link inside script</a></script> test</b>';
  it('providing a string has script tag returns a string without script tag', () => {
    expect(pipe.transform(scriptVal)).toBe('<b>Test  test</b>');
  });

  const styleVal = '<b>Test <style>This is a style text <a>This is a link inside style</a></style> test</b>';
  it('providing a string has style tag returns a string without style tag', () => {
    expect(pipe.transform(styleVal)).toBe('<b>Test  test</b>');
  });

  const imgVal = '<b>Test <img>This is a img text <a>This is a link inside img</a></img> test</b>';
  it('providing a string has img tag returns a string without img tag', () => {
    expect(pipe.transform(imgVal)).toBe('<b>Test This is a img text <a>This is a link inside img</a> test</b>');
  });

  const htmlVal = '<html>Test <img>This is a html text <a>This is a link inside html</a></img> test</html>';
  it('providing a string has html tag returns a string without html tag', () => {
    expect(pipe.transform(htmlVal)).toBe('Test This is a html text <a>This is a link inside html</a> test');
  });

  const multilineVal = '<script type="text/javascript"  >\n' +
    '  alert("This is from JavaScript");\n' +
    '</script  >\n' +
    '\n' +
    '<script type="text/javascript" src="jsfile.js">abc</script>\n' +
    '\n' +
    '<   script type="text/javascript">\n' +
    '  alert("This is from JavaScript");\n' +
    '<   /  script>\n' +
    '\n' +
    '<  script type="text/javascript" src="jsfile.js" /  >';
  it('providing a multiline string has tag returns a string without tag', () => {
    expect(pipe.transform(multilineVal)).toBe('&lt;   script type="text/javascript"&gt;<br/>alert("This is ' +
      'from JavaScript");<br/>&lt;   /  script&gt;<br/>&lt;  script type="text/javascript" src="jsfile.js" /  &gt;');
  });

  const newlinesBetweenTagsVal = '<div></div>\r\n\r\n\n\t  \t\n\n\r\n \r\n   \n\r\n\n\n<div></div>\r\n\r\n\n\t  \t\n\n\r\n \r\n   \n\r\n\n\n<br/>';

  it('providing multi break lines between tags returns a string which break lines were removed', () => {
    expect(pipe.transform(newlinesBetweenTagsVal)).toBe('<div></div><div></div><br />');
  });

  const newlinesBeforeTagsVal = '<div></div>abc123\r\n\r\n\n\t  \t\n\n\r\n \r\n   \n\r\n\n\n<div></div>xxx\r\n\r\n\n\t  \n\n\r\n \r\n   \n\r\n\n\n<br/>';

  xit('providing multi break lines before tags returns a string which break lines were removed', () => {
    expect(pipe.transform(newlinesBeforeTagsVal)).toBe('<div></div>abc123 <div></div>xxx <br />');
  });

  const newlinesAfterTagsVal = '<div></div>\r\n\r\n\n\t  \t\n\n\r\n \r\n   \n\r\n\n\nabc123<div></div>\r\n\r\n\n\t  \n\n\r\n \r\n   \n\r\n\n\nxxx<br/>';

  it('providing multi break lines after tags returns a string which break lines were removed', () => {
    expect(pipe.transform(newlinesAfterTagsVal)).toBe('<div></div> abc123<div></div> xxx<br />');
  });
});
