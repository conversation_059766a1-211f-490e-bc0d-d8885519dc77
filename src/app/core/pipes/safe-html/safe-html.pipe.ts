import { Pipe, PipeTransform } from '@angular/core';
import * as sanitizeHtml from 'sanitize-html';

@Pipe({
  name: 'safeHtml'
})
export class SafeHtmlPipe implements PipeTransform {
  /**
   * Remove/keep some html tags, attributes. Refer https://github.com/apostrophecms/sanitize-html for more information.
   * @param options:
   *  - allowedTags: [
   *     "address", "article", "aside", "footer", "header", "h1", "h2", "h3", "h4",
   *     "h5", "h6", "hgroup", "main", "nav", "section", "blockquote", "dd", "div",
   *     "dl", "dt", "figcaption", "figure", "hr", "li", "main", "ol", "p", "pre",
   *     "ul", "a", "abbr", "b", "bdi", "bdo", "br", "cite", "code", "data", "dfn",
   *     "em", "i", "kbd", "mark", "q", "rb", "rp", "rt", "rtc", "ruby", "s", "samp",
   *     "small", "span", "strong", "sub", "sup", "time", "u", "var", "wbr", "caption",
   *     "col", "colgroup", "table", "tbody", "td", "tfoot", "th", "thead", "tr"
   *     ]
   *  - disallowedTagsMode: 'discard',
   *  - allowedAttributes: {
   *    a: [ 'href', 'name', 'target' ],
   *    img: [ 'src' ]
   *  }
   *  - selfClosing: [ 'img', 'br', 'hr', 'area', 'base', 'basefont', 'input', 'link', 'meta' ],
   *  - allowedSchemes: [ 'http', 'https', 'ftp', 'mailto', 'tel' ],
   *  - allowedSchemesByTag: {},
   *  - allowedSchemesAppliedToAttributes: [ 'href', 'src', 'cite' ],
   *  - allowProtocolRelative: true,
   *  - enforceHtmlBoundary: false
   */
  transform(value: string, newLineTag = '<br/>', naSymbol = null, options = {}) {
    if (!value) {
      return naSymbol ? naSymbol : value;
    }

    options['allowedAttributes'] = {
      '*': ['id', 'overflow', 'class', 'height', 'width', 'data-*']
    };

    options['allowedTags'] = sanitizeHtml.defaults.allowedTags.concat([
      'maction', 'math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mi', 'mmultiscripts', 'mn', 'mo', 'mover',
      'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msubsup', 'msup', 'mtable',
      'mtd', 'mtext', 'mtr', 'munder', 'munderover', 'semantics', 'span', 'div', 'p'
    ]);

    value = sanitizeHtml(value, options);
    value = value.trim();
    value = value.replace(/>(?:\r\n[\s\t]*|\r[\s\t]*|\n[\s\t]*)+</g, '><'); // replace newline between tags
    value = value.replace(/(<\/*\w+[\s\/]*>)(?:\r\n[\s\t]*|\r[\s\t]*|\n[\s\t]*)+/g, '$1 '); // replace newline after tags
    //value = value.replace(/(?:\r\n[\s\t]*|\r[\s\t]*|\n[\s\t]*)+(<\/*\w+[\s\/]*>)/g, ' $1'); // replace newline before tags

    if (newLineTag) {
      value = value.replace(/(?:\r\n[\s\t]*|\r[\s\t]*|\n[\s\t]*)+/g, newLineTag);
    }

    return value;
  }
}
