import { TagParserPipe } from './tag-parser.pipe';

describe('TagParserPipe', () => {
  it('create an instance', () => {
    expect(new TagParserPipe()).toBeTruthy();
  });

  let pipe: TagParserPipe;

  beforeEach(() => {
    pipe = new TagParserPipe();
  });

  it('tagged users have been parsed correctly', () => {
    const input = 'Hi [@1631=Audrius Stonkus]]abc[]&nbsp;[@30156=Toan Da Cuoi]&nbsp;]abc[][@1700=<EMAIL>]' +
      '[@1631=Audrius Stonkus]';

    const fontSize = '17px/25px';

    const expectedResult = 'Hi <input readonly class="lcd-tagged-user" data-user-id="1631" value="Audrius Stonkus"/>' +
      ']abc[]&nbsp;<input readonly class="lcd-tagged-user" data-user-id="30156" value="Toan <PERSON>"/>&nbsp;]abc[]' +
      '<input readonly class="lcd-tagged-user" data-user-id="1700" value="<EMAIL>"/><input readonly ' +
      'class="lcd-tagged-user" data-user-id="1631" value="Audrius Stonkus"/>';
    const realResult = pipe.transform(input, fontSize).replace(/ style=".+?"/gi, '');
    expect(realResult).toBe(expectedResult);
  });
});
