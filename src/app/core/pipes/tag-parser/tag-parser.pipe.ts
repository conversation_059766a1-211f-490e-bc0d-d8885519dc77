import { Pipe, PipeTransform } from '@angular/core';
import { TextUtil } from '@core/utils';

@Pipe({
  name: 'tagParser'
})
export class TagParserPipe implements PipeTransform {

  transform(value: string, fontSize: string, isHtmlContent = true, prefix: string = ''): string {
    if (!value) {
      return value;
    }

    const fontName = 'Open Sans Regular, sans-serif';
    const fontWeight = 400;

    return value.replace(/\[(group)?@(\d+=.+?)]/gi, (val) => {
      const regex = new RegExp(/\[(group)?@(\d+)=(.+)?]/, 'gi');
      const match = regex.exec(val);
      const name = match[3];
      if (isHtmlContent) {
        const id = match[2];
        const type = match[1] ? match[1] : "user";
        const style = `width: ${TextUtil.calculateTextWidth(prefix+name, fontName, fontSize, fontWeight)}px;`;
        return `<input readonly class="lcd-tagged-${type}" data-${type}-id="${id}" value="${prefix+name}" style="${style}"/>`;
      } else {
        return prefix+name;
      }
    });
  }

}
