import { Pipe, PipeTransform } from '@angular/core';
import moment from 'moment';
import { DateFormatPipe } from '@core/pipes';

@Pipe({
  name: 'timeReadable'
})
export class TimeReadablePipe implements PipeTransform {
  constructor(
    private dateFormatPipe: DateFormatPipe
  ) {
  }

  transform(value: string): string {
    if (value) {
      const momentDate = moment.utc(value);
      const hoursDiff = moment().diff(momentDate, 'hours');
      if (hoursDiff > 24) {
        return this.dateFormatPipe.transform(momentDate.toDate().toISOString(), 'ShortDate');
      }
      return moment.utc(value).fromNow();
    }

    return value;
  }
}
