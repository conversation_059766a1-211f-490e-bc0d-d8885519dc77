import { TruncatePipe } from '@core/pipes';

describe('Pipe: Truncate', () => {
  let pipe: TruncatePipe;

  beforeEach(() => {
    pipe = new TruncatePipe();
  });

  it('providing null returns null', () => {
    expect(pipe.transform(null, 10, true)).toBe(null);
  });

  it('providing empty returns empty', () => {
    expect(pipe.transform('', 10, true)).toBe('');
  });

  it('providing white-characters returns a space', () => {
    expect(pipe.transform('   ', 10, true)).toBe('');
  });

  it('providing limit is less than length of string returns string has length equals limit', () => {
    expect(pipe.transform('I am mr Bean', 5, true)).toBe('I am...');
  });

  it('providing limit is less than length of string returns string has length equals limit', () => {
    expect(pipe.transform('I am mr Bean', 7, true)).toBe('I am mr...');
  });

  it('providing limit is less than length of string returns string has length equals limit', () => {
    expect(pipe.transform('I am mr <PERSON>', 8, true)).toBe('I am mr...');
  });

  it('providing limit is less than length of string returns string has length equals limit', () => {
    expect(pipe.transform('I am mr Bean', 10, true)).toBe('I am mr...');
  });

  it('providing limit is equal length of string returns the origin value', () => {
    expect(pipe.transform('I am mr Bean', 12, true)).toBe('I am mr Bean');
  });

  it('providing limit is greater than length of string returns the origin value', () => {
    expect(pipe.transform('I am mr Bean', 20, true)).toBe('I am mr Bean');
  });
});
