import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'truncate'
})
export class TruncatePipe implements PipeTransform {
  transform(value: string, limit = 25, completeWords = false, ellipsis = '...') {
    if (!value) {
      return value;
    }

    value = value.trim();

    if (limit >= value.length) {
      return value;
    }

    let subValue = value.substr(0, limit);

    if (completeWords && value[subValue.length] !== ' ') {
      limit = subValue.lastIndexOf(' ');

      if (limit > 0) {
        subValue = subValue.substr(0, limit);
      }
    }

    return `${subValue.trim()}${ellipsis}`;
  }
}
