import { UserTitlePipe } from '@core/pipes';

describe('UserTitlePipe', () => {
  let pipe: UserTitlePipe;

  beforeEach(() => {
    pipe = new UserTitlePipe();
  });

  it('create an instance', () => {
    pipe = new UserTitlePipe();
    expect(pipe).toBeTruthy();
  });

  it('return both first name and last name', () => {
    expect(pipe.transform({first_name: 'Abc', last_name: 'Xyz', email: 'xxx'})).toBe('Abc Xyz');
  });

  it('return first name', () => {
    expect(pipe.transform({first_name: 'Abc', last_name: null, email: 'xxx'})).toBe('Abc');
  });

  it('return last name', () => {
    expect(pipe.transform({first_name: null, last_name: 'Xyz', email: 'xxx'})).toBe('Xyz');
  });

  it('return email', () => {
    expect(pipe.transform({first_name: null, last_name: null, email: 'xxx'})).toBe('xxx');
  });
});
