import { Pipe, PipeTransform } from '@angular/core';
import { TeamUser, UserProfile } from '@core/models';

@Pipe({
  name: 'userTitle'
})
export class UserTitlePipe implements PipeTransform {

  transform(user: UserProfile | TeamUser | { first_name: string, last_name: string, email: string, name?: string }): string {
    if (!user) {
      return null;
    }

    if (user.first_name || user.last_name) {
      return `${user.first_name || ''} ${user.last_name || ''}`.trim();
    }

    if (user['name']) {
      return user['name'];
    }

    return user.email;
  }

}
