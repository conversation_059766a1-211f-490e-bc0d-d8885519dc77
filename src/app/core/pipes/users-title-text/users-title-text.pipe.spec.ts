import { UsersTitleTextPipe, UserTitlePipe } from '@core/pipes';
import { TeamUser } from '@core/models';

describe('UsersTitleTextPipe', () => {
  let pipe: UsersTitleTextPipe;
  const users: TeamUser[] = [
    { id: 1, first_name: 'Abc', last_name: '<PERSON>yz', email: 'xxx' },
    { id: 2, first_name: 'What', last_name: 'If', email: 'marvel' },
    { id: 3, first_name: 'Games', last_name: 'Of', email: 'Thrones' },
    { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: 'Rises' },
  ];

  beforeEach(() => {
    pipe = new UsersTitleTextPipe(new UserTitlePipe());
  });

  it('create an instance', () => {
    pipe = new UsersTitleTextPipe(new UserTitlePipe());
    expect(pipe).toBeTruthy();
  });

  it('return one user title text', () => {
    expect(pipe.transform([users[0]])).toBe('Abc Xyz');
  });

  it('return two users title text', () => {
    expect(pipe.transform([users[1], users[2]])).toBe('What If and Games Of');
  });

  it('return more then two users title text', () => {
    expect(pipe.transform(users)).toBe('Abc Xyz, What If, Games Of and Dark Knight');
  });
});
