import { Pipe, PipeTransform } from '@angular/core';
import { TeamUser, UserGroup } from '@core/models';
import { UserTitlePipe } from '../user-title/user-title.pipe';

@Pipe({
  name: 'usersTitleText'
})
export class UsersTitleTextPipe implements PipeTransform {
  constructor(public userTitlePipe: UserTitlePipe) {
  }

  transform(users: TeamUser[] | UserGroup[]): string {
    if (!users?.length) {
      return '';
    }

    let names = users.map((u, i, a) => this.userTitlePipe.transform(u) + (((i + 2) < a.length) ? ',' : ''));
    if (names.length > 1) {
      const last = names.pop();
      names = [...names, 'and', last];
    }
    return names.join(' ');
  }

}
