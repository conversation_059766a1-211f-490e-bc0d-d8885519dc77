import { TestBed } from '@angular/core/testing';

import { AdvancedFilterService } from './advanced-filter.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('AdvancedFilterService', () => {
  let service: AdvancedFilterService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([]), ],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    });
    service = TestBed.inject(AdvancedFilterService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
