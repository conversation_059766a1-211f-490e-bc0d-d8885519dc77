import { Injectable, Injector } from '@angular/core';
import { BooleanSearchService } from '../boolean-search/boolean-search.service';

@Injectable({
  providedIn: 'root'
})
export class AdvancedFilterService extends BooleanSearchService {
  constructor(
    public inject: Injector,
  ) {
    super(inject);
  }

  isFilterValid() {
    return super.areClausesValid(false) && !this.clauses.every(c => c.errorMsg);
  }
}
