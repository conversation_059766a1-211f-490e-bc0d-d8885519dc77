import { Injectable } from '@angular/core';
import { ApiService } from '@core';
import { Observable } from 'rxjs';
import { SmartHighlightRequest, SmartHighlights } from './type';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AnalysisService {

  constructor(
    private apiService: ApiService,
  ) {
  }

  calculateSmartHighlights(payload: SmartHighlightRequest): Observable<SmartHighlights> {
    return this.apiService.post('web/patent/smart_highlights', payload)
      .pipe(
        map(({data}) => {
          return data as SmartHighlights;
        })
      );
  }
}
