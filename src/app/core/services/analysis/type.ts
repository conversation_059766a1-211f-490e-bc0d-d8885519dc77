export interface SmartHighlightRequest {
  document_id: number;
  publication_number?: string;
  monitor_run_id?: number;
  search_hash?: string;
  top_highest?: number;
}

export interface SmartHighlightParagraphEntry {
  id: string;
  text: string;
}

export interface SmartHighlightItem {
  score: number;
  paragraph: SmartHighlightParagraphEntry;
  label: string;
  doc_field?: string;
}

export interface SmartHighlights {
  claims: SmartHighlightItem[];
  description: SmartHighlightItem[];
}
