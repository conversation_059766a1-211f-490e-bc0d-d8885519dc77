import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { finalize, map, tap } from 'rxjs/operators';
import { ApiService } from '../api/api.service';
import {
  AnnotationPosition,
  DocumentAnnotation,
  DocumentAnnotationResponse,
  DocumentCommentSourceEnum,
  DocumentLabelResponse,
  DocumentsResponse,
  Label,
  LabelsAndCommentsResponse,
  LabelsResponse
} from './types';
import { AnnotationStoreService } from '@core/store';
import { TextHighlightService } from '../text-highlight/text-highlight.service';
import { UserService } from '../user/user.service';
import { PatentViewService } from '../patent-view/patent-view.service';
import { PatentCommentsFilterEnum, PatentCommentsSortEnum } from '@patent/types';

interface HighlightNotePosition {
  id: string,
  top: number,
  height: number
}


@Injectable({
  providedIn: 'root'
})
export class AnnotationService {
  static HIGHLIGHT_COMMENT_CSS_CLASS = 'pa-comment-highlight';
  static HIGHLIGHT_EXPLAINING_TEXT_ID = 0;
  commentsSortByOption: any[] = [
    PatentCommentsSortEnum.RECENTLY_ADDED,
    PatentCommentsSortEnum.OLDEST_ADDED
  ];
  commentsFilterByOption: any[] = [
    PatentCommentsFilterEnum.SEE_ALL,
    PatentCommentsFilterEnum.THIS_PATENT_ONLY,
    PatentCommentsFilterEnum.MY_COMMENTS_ONLY
  ];
  commentsSortBy = PatentCommentsSortEnum.RECENTLY_ADDED;
  commentsFilterBy = PatentCommentsFilterEnum.SEE_ALL;
  focusCommentId: number;

  private commentsNoteTimeout;

  constructor(
    private apiService: ApiService,
    private textHighlightService: TextHighlightService,
    private patentViewService: PatentViewService,
    public userService: UserService,
    public annotationStore: AnnotationStoreService
  ) {
  }

  reset() {
    this.resetSortAndFilterOptions();
    this.focusCommentId = undefined;
    this.annotationStore.resetAnnotation();
  }

  resetSortAndFilterOptions() {
    this.commentsSortBy = PatentCommentsSortEnum.RECENTLY_ADDED;
    this.commentsFilterBy = PatentCommentsFilterEnum.SEE_ALL;
  }

  getLabels(params = {}): Observable<LabelsResponse> {
    return this.apiService.get('web/annotations/labels', params)
      .pipe(
        map(({data}) => {
          return {labels: data.labels, page: data.page} as LabelsResponse;
        }),
        tap(({labels, page}) => this.annotationStore.annotationLabels = [...labels])
      );
  }

  getAnnotatedDocuments(payload): Observable<DocumentsResponse> {
    return this.apiService.get(`web/annotations/documents`, payload)
      .pipe(
        map(({data}) => {
          return {documents: data.documents, page: data.page} as DocumentsResponse;
        })
      );

  }

  getLabeledDocuments(label_id: number, payload: {}): Observable<DocumentsResponse> {
    return this.apiService.get(`web/annotations/labels/${label_id}/documents`, payload).pipe(
      map(({data}) => {
        return {documents: data.documents, page: data.page} as DocumentsResponse;
      })
    );
  }

  getLabelsAndComments(documentId: number, params: {} = {}): Observable<LabelsAndCommentsResponse> {
    return this.apiService.get(`web/annotations/documents/${documentId}`, params);
  }

  saveDocumentLabel(payload: {
    entries: DocumentAnnotation[]
  }, documentId: number): Observable<DocumentLabelResponse> {
    return this.apiService.post(`web/annotations/documents/${documentId}/labels`, payload)
      .pipe(
        finalize(() => {
          this.annotationStore.setSavedAnnotationId(null);
        })
      );
  }

  saveDocumentComment(payload: DocumentAnnotation, documentId: number): Observable<DocumentAnnotationResponse> {
    return this.apiService.post(`web/annotations/documents/${documentId}/comments`, payload)
      .pipe(
        finalize(() => {
          this.annotationStore.setSavedAnnotationId(null);
          if (payload.parent_comment_id) {
            this.annotationStore.setRepliedToAnnotationId(payload.parent_comment_id);
          }
        })
      );
  }

  updateDocumentLabel(payload: DocumentAnnotation, documentId: number, annotationId: number):
    Observable<DocumentAnnotationResponse> {
    return this.apiService.patch(`web/annotations/documents/${documentId}/labels/${annotationId}`, payload)
      .pipe(
        finalize(() => {
          this.annotationStore.setSavedAnnotationId(annotationId);
        })
      );
  }

  updateDocumentComment(payload: DocumentAnnotation, documentId: number, annotationId: number):
    Observable<DocumentAnnotationResponse> {
    return this.apiService.patch(`web/annotations/documents/${documentId}/comments/${annotationId}`, payload)
      .pipe(
        finalize(() => {
          this.annotationStore.setSavedAnnotationId(annotationId);
          if (payload.parent_comment_id) {
            this.annotationStore.setRepliedToAnnotationId(payload.parent_comment_id);
          }
        })
      );
  }

  deleteDocumentLabel(documentId: number, annotationId: number): Observable<DocumentAnnotation> {
    return this.apiService.delete(`web/annotations/documents/${documentId}/labels/${annotationId}`);
  }

  deleteDocumentComment(documentId: number, annotationId: number): Observable<DocumentAnnotation> {
    return this.apiService.delete(`web/annotations/documents/${documentId}/comments/${annotationId}`);
  }

  deleteLabelsAndComments(documentId: number): Observable<DocumentAnnotation> {
    return this.apiService.delete(`web/annotations/documents/${documentId}`);
  }

  /**
   * newLabel
   *
   * create new label for specific user
   * @param payload new label object as payload
   */
  newLabel(payload: Object): Observable<Label> {
    return this.apiService.post(`web/annotations/labels`, payload).pipe(map(resp => resp.data));
  }

  /**
   * deleteLabel
   *
   * delete a label object for specific user
   * @param id Label id to be deleted
   */
  deleteLabel(id: number): Observable<any> {
    return this.apiService.delete(`web/annotations/labels/${id}`);
  }

  /**
   * updateLabel
   *
   * update a label object for specific user
   * @param id Label id to be updated
   * @param payload existing label object as payload
   * @returns Promise Object represents API response
   */
  updateLabel(id: number, payload: Object): Observable<Label> {
    return this.apiService.patch(`web/annotations/labels/${id}`, payload).pipe(map(resp => resp.data));
  }

  getLabel(labelId: number): Observable<Label> {
    return this.apiService.get(`web/annotations/labels/${labelId}`).pipe(map(resp => resp.data));
  }

  highlightComments(renderNote: boolean = true, scrollToComment?: DocumentAnnotation) {
    this.sortAndFilterComments();
    if (this.annotationStore.listComments?.length > 0) {
      const replies = this.annotationStore.listComments.filter(c => c.parent_comment_id);

      this.annotationStore.generalComments.forEach(c => {
        c.replies = replies.filter(r => r.parent_comment_id === c.id);
      });
      this.annotationStore.sectionComments.forEach(c => {
        c.replies = replies.filter(r => r.parent_comment_id === c.id);
        if (c.publication_number === (this.patentViewService.activePublicationName || null)) {
          this.highlightDocumentAnnotation(c);
        } else {
          this.annotationStore.sectionComments = this.annotationStore.sectionComments.filter(cm => cm.id !== c.id);
        }
      });
      if (renderNote) {
        this.displayCommentsNote();
      }
      if (scrollToComment) {
        setTimeout(() => {
          this.scrollToComment(scrollToComment);
        }, 200);
      }
    }
  }

  sortAndFilterComments() {
    this.annotationStore.generalComments = this.annotationStore.listComments.filter(c => c.source === DocumentCommentSourceEnum.GENERAL && !c.parent_comment_id);
    this.annotationStore.sectionComments = this.annotationStore.listComments.filter(c => c.source !== DocumentCommentSourceEnum.GENERAL && !c.parent_comment_id);
    if (this.annotationStore.listComments?.length < 1) {
      return
    }
    if (this.commentsFilterBy) {
      this.annotationStore.generalComments = this.filterComments(this.annotationStore.generalComments);
      this.annotationStore.sectionComments = this.filterComments(this.annotationStore.sectionComments);
    }
    if (this.commentsSortBy) {
      this.annotationStore.generalComments = this.sortComments(this.annotationStore.generalComments);
      this.annotationStore.sectionComments = this.sortComments(this.annotationStore.sectionComments);
    }
  }

  removeCommentHighlight(commentId: any) {
    const commentsEle = this.getHighlightedElements(commentId);
    for (let ele of commentsEle) {
      const textNode = document.createTextNode(ele.textContent || '');
      ele.parentNode?.replaceChild(textNode, ele);
    }
  }

  getAnnotationElementId(id: any): string {
    return `cm-${id}`;
  }

  getAnnotationElement(comment: DocumentAnnotation): HTMLElement {
    const eleId = this.getAnnotationElementId(comment.id);
    return <HTMLElement>document.getElementById(eleId);
  }

  getHighlightedElement(id: any): HTMLElement {
    if (!id) {
      return null;
    }
    const highlightEleId = this.getAnnotationElementId(id);
    return <HTMLElement>document.querySelector(`[data-id="${highlightEleId}"]`);
  }

  getHighlightedElements(id: any): Element[] {
    const eleId = this.getAnnotationElementId(id);
    return Array.from(document.querySelectorAll(`[data-id="${eleId}"]`));
  }

  displayCommentsNote() {
    clearTimeout(this.commentsNoteTimeout);
    this.commentsNoteTimeout = setTimeout(() => {
      const initialPositions: HighlightNotePosition[] = [];
      const highlightedNoteElements = Array.from(document.getElementsByClassName("comments-highlighted-note"));
      const highlightedNoteContainer = document.querySelector<HTMLElement>('.comments-highlighted-container');
      const highlightedNoteContainerRect = highlightedNoteContainer?.getBoundingClientRect();
      const commentsContainerTop = highlightedNoteContainerRect?.top || 0;

      if (highlightedNoteContainer) {
        highlightedNoteContainer.style.height = '100%';
      }

      highlightedNoteElements.forEach((noteEle: HTMLElement, index) => {
        const commentId = noteEle.id?.replace(/^\D+/g, '');
        const commentHighlightedEle = this.getHighlightedElement(commentId);
        if (!commentHighlightedEle) {
          noteEle.style.display = 'none';
          return;
        }
        const commentHighlightedRect = commentHighlightedEle.getBoundingClientRect();
        const noteEleTop = commentHighlightedRect.top - commentsContainerTop;
        initialPositions.push({id: commentId, top: noteEleTop, height: noteEle.offsetHeight} as HighlightNotePosition);
      });

      const fixedPositions = this.fixOverlappedHighlightNotes(initialPositions);

      highlightedNoteElements.forEach((noteEle: HTMLElement, index) => {
        const commentId = noteEle.id?.replace(/^\D+/g, '');
        const position = fixedPositions.find((p) => p.id === commentId);
        if (position) {
          noteEle.style.top = position.top + 'px';
        }
      });

      if (highlightedNoteContainer && fixedPositions.length) {
        const lastPosition = fixedPositions[fixedPositions.length - 1];
        highlightedNoteContainer.style.height = (lastPosition.top + lastPosition.height) + 'px';
      }
    }, 100);
  }

  focusoutComment(comment: DocumentAnnotation) {
    const commentComponent = this.getAnnotationElement(comment);
    if (commentComponent) {
      commentComponent.classList.remove('comment-hovered');
    }
    this.getHighlightedElements(comment.id).forEach(el => el.classList.remove('comment-hovered'));
  }

  focusinComment(comment: DocumentAnnotation) {
    const commentComponent = this.getAnnotationElement(comment);
    if (commentComponent) {
      commentComponent.classList.add('comment-hovered');
    }
    this.getHighlightedElements(comment.id).forEach(el => el.classList.add('comment-hovered'));
  }

  scrollToComment(comment: DocumentAnnotation) {
    const ele = this.getAnnotationElement(comment);
    if (ele) {
      ele.scrollIntoView({behavior: 'smooth', block: 'start'});
    }
  }

  scrollToHighlightedElement(comment: DocumentAnnotation) {
    const ele = this.getHighlightedElement(comment.id);
    if (ele) {
      ele.scrollIntoView({behavior: 'smooth', block: 'start'});
      this.focusReferenceText(comment);
    }
  }

  focusReferenceText(comment: DocumentAnnotation) {
    this.getHighlightedElements(comment.id).forEach(el => {
      el.classList.add('comment-focused');
    });
    setTimeout(() => {
      this.getHighlightedElements(comment.id).forEach(el => {
        el.classList.remove('comment-focused');
      });
    }, 2000);
  }

  clearHighlightedExplainingText() {
    this.removeCommentHighlight(AnnotationService.HIGHLIGHT_EXPLAINING_TEXT_ID);
  }

  highlightDocumentAnnotation(annotation: DocumentAnnotation): void {
    this.stylizeDocumentAnnotation(
      annotation,
      AnnotationService.HIGHLIGHT_COMMENT_CSS_CLASS,
      TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS
    );
  }

  highlightExplainingText(annotation: DocumentAnnotation): void {
    this.stylizeDocumentAnnotation(
      annotation,
      TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS,
      AnnotationService.HIGHLIGHT_COMMENT_CSS_CLASS
    );
  }

  private stylizeDocumentAnnotation(
    annotation: DocumentAnnotation,
    addCssClass: string,
    removeCssClass: string,
    isSavedAnnotation: boolean = true
  ): void {
    const highlightEleId = this.getAnnotationElementId(annotation.id);
    const highlightedEle = this.getHighlightedElement(annotation.id);

    if (highlightedEle) {
      return;
    }

    const field = annotation.field + '-text';
    const node: HTMLElement = document.getElementById(field);

    let isTextValid = true;

    if (isSavedAnnotation) {
      isTextValid = this.textHighlightService.validateText(node, annotation);

      if (!isTextValid) {
        let newPosition: AnnotationPosition = this.textHighlightService.getNewPosition(node, annotation);
        if (newPosition) {
          annotation.start_pos = newPosition.startPos;
          annotation.end_pos = newPosition.endPos;
          isTextValid = true;
        }
      }
    }

    if (isTextValid) {
      window.getSelection().removeAllRanges();
      const position = this.textHighlightService.getSavedAnnotationPosition(node, annotation);
      const range = document.createRange();
      range.setStart(position.startNode, position.startPos);
      range.setEnd(position.endNode, position.endPos);

      const highlightNode = document.createElement('span');
      highlightNode.dataset.id = highlightEleId;

      this.setAnnotationElementStyle(highlightNode, addCssClass, removeCssClass);
      if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
        range.surroundContents(highlightNode);
      } else {
        const selection = window.getSelection();
        selection.addRange(range);
        this.textHighlightService.setHighlightChildNodes(range, highlightNode);
      }
      const self = this;
      window.getSelection().removeAllRanges();
      this.getHighlightedElements(annotation.id).forEach(el => {
        el.addEventListener("mouseleave", function () {
          self.focusoutComment(annotation);
        });
        el.addEventListener("mouseover", function () {
          self.focusinComment(annotation);
        });
        el.addEventListener("click", function () {
          self.patentViewService.setShowCommentView(annotation);
        });
      });
    } else {
      if (isSavedAnnotation) {
        this.annotationStore.sectionComments = this.annotationStore.sectionComments.filter(c => c.id !== annotation.id);
      }
    }
  }

  private sortComments(comments: DocumentAnnotation[]) {
    return comments.sort((a, b) => {
      if (this.commentsSortBy === this.commentsSortByOption[0]) {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
      if (this.commentsSortBy === this.commentsSortByOption[1]) {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      }
    })
  }

  private filterComments(comments: DocumentAnnotation[]) {
    return comments.filter(c => {
      switch (this.commentsFilterBy) {
        case PatentCommentsFilterEnum.THIS_PATENT_ONLY:
          return c.publication_number === this.patentViewService.activePublicationName;
        case PatentCommentsFilterEnum.MY_COMMENTS_ONLY:
          return c.user_id === this.userService.getUser()?.profile?.id;
        default:
          return true;
      }
    });
  }

  private setAnnotationElementStyle(
    element: HTMLElement,
    addCssClass = AnnotationService.HIGHLIGHT_COMMENT_CSS_CLASS,
    removeCssClass = TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS
  ): void {
    element.style.borderBottomColor = '#fb6f09';
    element.classList.add(addCssClass);
    element.classList.remove(removeCssClass);
  }

  extractTaggedUserIds(commentText: string): number[] {
    return commentText ? [...new Set(Array.from(commentText.matchAll(/\[@(\d+)=/g)).map((o) => Number(o[1])))] : [];
  }

  extractTaggedGroupIds(commentText: string): number[] {
    return commentText ? [...new Set(Array.from(commentText.matchAll(/\[group@(\d+)=/g)).map((o) => Number(o[1])))] : [];
  }

  private fixOverlappedHighlightNotes(currentPositions: HighlightNotePosition[]) {
    if (currentPositions.length < 2) {
      return currentPositions;
    }

    const fixedPositions = currentPositions.sort((a, b) => a.top - b.top);
    const spaceBetweenNotes = 2;
    for (let i = 1; i < fixedPositions.length; i++) {
      const prevPos = fixedPositions[i - 1];
      const currentPos = fixedPositions[i];
      const closestDistance = prevPos.height + spaceBetweenNotes;

      if (currentPos.top - prevPos.top < closestDistance) {
        fixedPositions[i].top = prevPos.top + closestDistance;
      }
    }
    return fixedPositions;
  }
}
