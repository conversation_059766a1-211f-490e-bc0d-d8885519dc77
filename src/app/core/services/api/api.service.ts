import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { catchError, filter, map, switchMap, tap } from 'rxjs/operators';
import { interval, Observable, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { SettingsService } from '../settings/settings.service';
import { RoutingHistoryService } from '../routing-history/routing-history.service';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private API_URL: string;

  constructor(
    private http: HttpClient,
    private router: Router,
    private routingHistoryService: RoutingHistoryService,
    private settingsService: SettingsService
  ) {
    this.API_URL = this.settingsService.settings.apiUrl;
  }

  private addExtraParams(params?: {}, updates?: {}) {
    if (!updates) {
      return params;
    }

    if (!params) {
      params = {};
    }

    params['redirectTo403Page'] = updates['redirectTo403Page'] ? 1 : 0;
    return params;
  }

  public get(
    path: string,
    params?: {},
    redirectTo403Page = true
  ): Observable<any> {
    params = this.addExtraParams(params, {redirectTo403Page});
    return this.http.get(`${this.API_URL}${path}`, {params})
      .pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public put(
    path: string,
    body: Object = {},
    redirectTo403Page = true
  ): Observable<any> {
    const params = this.addExtraParams({}, {redirectTo403Page});
    return this.http.put(
      `${this.API_URL}${path}`,
      JSON.stringify(body),
      {params}
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public post(
    path: string,
    body: Object = {},
    params?: {},
    redirectTo403Page = true
  ): Observable<any> {
    params = this.addExtraParams(params, {redirectTo403Page});
    return this.http.post(
      `${this.API_URL}${path}`,
      JSON.stringify(body),
      {params}
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public postFormData(
    path: string,
    body: FormData,
    params?: {},
    redirectTo403Page = true
  ): Observable<any> {
    params = this.addExtraParams(params, {redirectTo403Page});
    return this.http.post(`${this.API_URL}${path}`, body, {params})
      .pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public getImage(
    path: string,
    redirectTo403Page = true
  ): Observable<any> {
    const params = this.addExtraParams({}, {redirectTo403Page});
    return this.http.get(`${this.API_URL}${path}`, {
      responseType: 'blob',
      headers: new HttpHeaders({
        'accept': 'image/*',
      }),
      params
    }).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public delete(
    path,
    options = {},
    redirectTo403Page = true
  ): Observable<any> {
    options['params'] = this.addExtraParams(options['params'], {redirectTo403Page});
    return this.http.delete(
      `${this.API_URL}${path}`, options
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  /**
   * @author: Shahab Shakoor
   */
  public patch(
    path: string,
    body: Object = {},
    params?: {},
    redirectTo403Page = true
  ): Observable<any> {
    params = this.addExtraParams(params, {redirectTo403Page});
    return this.http.patch(
      `${this.API_URL}${path}`,
      JSON.stringify(body),
      {params}
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public calculateCharts(path: string, body: Object = {}, params?): Observable<any> {
    return this.http.post(`${this.API_URL}${path}`,
      JSON.stringify(body),
      {params}
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public export(
    path: string,
    body: Object = {},
    params?
  ): any {
    return this.http.post(
      `${this.API_URL}${path}`,
      JSON.stringify(body),
      {params, responseType: 'blob'}
    ).pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  public asyncExport(
    path: string,
    body: Object = {},
    params?
  ): any {

    const self = this;
    let isLoading = false;

    function getStatus(url: string): Observable<any> {
      return interval(3000).pipe(
        filter(() => !isLoading),
        tap(() => isLoading = true),
        switchMap(() => self.http.get(url, {observe: 'response'})),
        tap(() => isLoading = false),
        tap(({body}) => {
          if (body['status'] === 'FAILURE') {
            throw new Error("Export failed, please try again");
          }
        })
      );
    }

    return this.http.post(
      `${this.API_URL}${path}`,
      JSON.stringify(body),
      {params, observe: 'response'}
    ).pipe(
      map(res => res.headers.get('Location')),
      switchMap(location => getStatus(location)),
      tap(res => {
        if (res.body['status'] === 'FAILURE') {
          throw new Error("Export failed, please try again")
        }
      }),
      filter(res => res.body['status'] === 'SUCCESS'),
      map(res => res.headers.get('Location')),
      switchMap(location => this.http.get(location, {responseType: 'blob'})),
      catchError(err => this.formatErrors(err, this.router))
    );
  }

  public getPdf(path: string, params): Observable<any> {
    const head = new HttpHeaders().append('accept', 'application/pdf');
    return this.http.get(`${this.API_URL}${path}`, {params, responseType: 'blob', headers: head})
      .pipe(catchError(err => this.formatErrors(err, this.router)));
  }

  private formatErrors(error: any, router: Router) {
    const isNotIpLoungeLogin = !window.location.pathname.startsWith('/auth/ip-lounge');
    const isNotChangePasswordUrl = !this.router.url.startsWith('/auth/change-password');
    const isNotProfileChangePasswordUrl = !this.router.url.startsWith('/profile');
    if (error.status === 401 && isNotChangePasswordUrl && isNotProfileChangePasswordUrl && isNotIpLoungeLogin) {
      if (!window.location.pathname.startsWith('/auth/')) {
        this.routingHistoryService.saveHistoryUrl(window.location.pathname + window.location.search);
      }
      router.navigate(['/auth/login']);
    }
    return throwError(error);
  }

  public postStream(path: string, body: Object = {}, params?: {}, redirectTo403Page = true): Observable<any> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream'
    });
    params = this.addExtraParams(params, {redirectTo403Page});
    return this.http.post(`${this.API_URL}${path}`, JSON.stringify(body), 
      {params, headers, responseType: 'text', observe: 'events', reportProgress: true})
      .pipe(
        filter( event => {
          return event.type === 4 || (event.type === 3 && event['partialText']);
        }),
        map(event => {
          if (event.type === 4) {
            return event.body;
          } else if (event.type === 3 && event['partialText']) {
            return event['partialText'];
          }
          return '';
        }),
        filter(text => !!text)
      );
  }
}
