import { TestBed } from '@angular/core/testing';

import { ApplicantsAliasesService } from './applicants-aliases.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ApplicantsAliasesService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
  }));

  it('should be created', () => {
    const service: ApplicantsAliasesService = TestBed.inject(ApplicantsAliasesService);
    expect(service).toBeTruthy();
  });
});
