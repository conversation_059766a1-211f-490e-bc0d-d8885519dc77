import { EventEmitter, Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { <PERSON>as, ApplicantAlias } from './types';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { debounceTime, map, switchMap, tap } from 'rxjs/operators';
import { PaginationMetadata } from '../semantic-search/types';

@Injectable({
  providedIn: 'root'
})
export class ApplicantsAliasesService {

  changeApplicantsEvent = new EventEmitter(true);

  private getAllApplicantAliasesSubject = new BehaviorSubject<boolean>(false);
  private getAllApplicantAliasesSubscription: Subscription = null;

  constructor(private apiService: ApiService) {
  }

  private _allApplicantAliases: ApplicantAlias[] = [];

  get allApplicantAliases(): ApplicantAlias[] {
    return this._allApplicantAliases;
  }

  public save(payload: any) {
    return this.apiService.post('web/alias/applicant', payload);
  }

  public delete(id: number) {
    return this.apiService.delete(`web/alias/applicant/${id}`);
  }

  public getApplicantAliases(payload = null): Observable<{
    applicant_aliases: ApplicantAlias[],
    page?: PaginationMetadata
  }> {
    return this.apiService.get('web/alias/applicant', payload)
      .pipe(map(resp => resp.data));
  }

  subscribeGetAllApplicantAliases(): Subscription {
    if (this.getAllApplicantAliasesSubscription) {
      return;
    }

    this.getAllApplicantAliasesSubscription = this.getAllApplicantAliasesSubject.asObservable()
      .pipe(
        debounceTime(200),
        switchMap((reload) => this.getAllApplicantAliases(reload))
      )
      .subscribe();
  }

  notifyGetAllApplicantAliases(reload: boolean = false): void {
    this.getAllApplicantAliasesSubject.next(reload);
  }

  getAllApplicantAliases(reload: boolean = false): Observable<ApplicantAlias[]> {
    if (this._allApplicantAliases.length && !reload) {
      return of(this._allApplicantAliases);
    }

    return this.getApplicantAliases({load_all: 1})
      .pipe(
        map(({applicant_aliases}) => applicant_aliases),
        tap((applicantAliases) => this._allApplicantAliases = applicantAliases)
      );
  }

  applicantsToAliases(applicantAliases: any[]): Alias[] {
    let currentAliasTitle = '';
    let currentAlias: Alias;
    const aliases: Alias[] = [];
    applicantAliases.forEach((item) => {
      if (item.alias !== currentAliasTitle) {
        currentAlias = {id: item.id, alias_name: item.alias, applicants: [item]} as Alias;
        aliases.push(currentAlias);
        currentAliasTitle = item.alias;
      } else {
        currentAlias.applicants.push(item);
      }
    });
    return aliases;
  }

  getOriginalApplicants(aliases: string[]): string[] {
    return aliases.flatMap(name => {
      const foundApplicantAliases = this.allApplicantAliases.filter((a) => a.alias === name);
      if (foundApplicantAliases?.length > 0) {
        return foundApplicantAliases.map(applicant => applicant.applicant);
      }
      return [name];
    });
  }

  clearStoredData(): void {
    this._allApplicantAliases = [];
  }
}
