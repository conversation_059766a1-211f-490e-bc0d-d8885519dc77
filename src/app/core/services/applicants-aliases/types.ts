import { PaginationMetadata } from '../semantic-search/types';

export interface ApplicantAlias {
  created_at: string;
  id: number;
  updated_at: string;
  alias: string;
  applicant: string;
}

export interface Alias {
  id: number;
  alias_name: string;
  applicants: Array<ApplicantAlias>;
}

export interface ApplicantAliasesResponse {
  applicant_aliases: Array<ApplicantAlias>;
  page: PaginationMetadata;
}
