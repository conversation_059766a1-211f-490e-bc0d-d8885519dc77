import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService } from '../api/api.service';
import { LoginMethodEnum } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(
    private apiService: ApiService
  ) { }

  getLoginMethod(payload: { email: string }): Observable<{ method: LoginMethodEnum, sso_url?: string }> {
    return this.apiService.post('auth/login/method', payload)
      .pipe(
        map(response => response.data)
      );
  }
}
