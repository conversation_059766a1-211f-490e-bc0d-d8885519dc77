import { Injectable } from '@angular/core';
import { AUTHORITY_CODE_NODES } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class AuthorityService {

  constructor() {
  }

  static filterAuthorities(term: string): Array<{ id: string, name: string }> {
    term = term ? term.trim().toUpperCase() : '';

    if (term.length === 0) {
      return AUTHORITY_CODE_NODES.map(o => o);
    } else {
      const authorityCodesById = AuthorityService.filterAuthoritiesByCodes(term.split(','));
      const authorityCodesByName = AUTHORITY_CODE_NODES.filter(v => {
        return v.name.toUpperCase().indexOf(term) > -1 && authorityCodesById.findIndex(o => v.id === o.id) === -1;
      }).map(o => o);

      const mergedAuthorityCodes = authorityCodesById.concat(authorityCodesByName);
      return mergedAuthorityCodes.length > 0 ? mergedAuthorityCodes : AUTHORITY_CODE_NODES.map(o => o);
    }
  }

  static filterAuthoritiesByCodes(codes: Array<string>): Array<{ id: string, name: string }> {
    if (!codes || codes.length === 0) {
      return [];
    }

    codes = codes.map(o => o.toUpperCase().substring(0, 2));
    return AUTHORITY_CODE_NODES.filter(v => codes.includes(v.id.toUpperCase())).map(o => o);
  }
}
