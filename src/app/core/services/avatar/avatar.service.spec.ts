import { TestBed } from '@angular/core/testing';

import { AvatarService } from './avatar.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { GroupService, TeamUser, UserGroup, UserTitlePipe } from '@core';

describe('AvatarService', () => {
  let service: AvatarService;

  // Mock dependencies
  const mockGroupService = {};
  const mockUserTitlePipe = {
    transform: (user: TeamUser) => `${user.first_name} ${user.last_name}`
  };

  // Test data setup
  const createTestUser = (id: number): TeamUser => ({
    id,
    email: `user${id}@test.com`,
    first_name: `First${id}`,
    last_name: `Last${id}`,
    more_users: false
  });

  const createTestGroup = (id: number): UserGroup => ({
    id,
    name: `Group${id}`,
    description: `Description${id}`
  });

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AvatarService,
        {provide: GroupService, useValue: mockGroupService},
        {provide: UserTitlePipe, useValue: mockUserTitlePipe}
      ]
    });
    service = TestBed.inject(AvatarService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('calculateMaxGroupsAndMaxUsers', () => {
    it('should handle empty users and groups', () => {
      const result = service['calculateMaxGroupsAndMaxUsers']([], [], 3, 2);
      expect(result).toEqual([0, 0]);
    });

    it('should reallocate unused user slots to groups', () => {
      // Given: 1 user and 3 groups, with 3 user slots and 1 group slot
      const users = [1].map(createTestUser);
      const groups = [1, 2, 3].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 1);
      // Should use 1 user slot and reallocate 2 unused user slots to groups
      expect(result).toEqual([3, 1]);
    });

    it('should reallocate unused group slots to users', () => {
      // Given: 4 users and 1 group, with 2 user slots and 3 group slots
      const users = [1, 2, 3, 4].map(createTestUser);
      const groups = [1].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 2, 3);
      // Should use 1 group slot and reallocate 1 unused group slot to users
      expect(result).toEqual([1, 4]);
    });

    it('should handle bidirectional reallocation', () => {
      // Given: 3 users and 3 groups (total 6 items)
      // Available slots: 4 user slots + 3 group slots = 7 total slots
      const users = [1, 2, 3].map(createTestUser);
      const groups = [1, 2, 3].map(createTestGroup);

      // When calculating with 4 user slots and 3 group slots
      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 4, 3);

      // Then: Should show all 3 users and 3 groups
      // Because:
      // 1. Initially we have 4 user slots and 3 group slots
      // 2. We don't need one slot for "more" indicator (total items < total slots)
      // 3. Since we have enough user slots for all users (3), use them
      // 4. Remaining slot is enough for all groups (3)
      expect(result).toEqual([3, 3]);
    });

    it('should handle bidirectional reallocation with more indicator when slots exceed users but not groups', () => {
      // Given: 2 users and 4 groups (total 6 items)
      // Available slots: 3 user slots + 2 group slots = 5 total slots
      const users = [1, 2].map(createTestUser);
      const groups = [1, 2, 3, 4].map(createTestGroup);

      // When calculating with 3 user slots and 2 group slots
      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 2);

      // Then: Should show 2 users and 2 groups
      // Because:
      // 1. Need 1 slot for "more" indicator (6 items > 5 slots)
      // 2. Can show all users (2) with 1 unused user slot
      // 3. Can show 2 groups using group slots
      expect(result).toEqual([2, 2]);
    });

    it('should handle bidirectional reallocation with more indicator when slots exceed groups but not users', () => {
      // Given: 4 users and 2 groups (total 6 items)
      // Available slots: 3 user slots + 2 group slots = 5 total slots
      const users = [1, 2, 3, 4].map(createTestUser);
      const groups = [1, 2].map(createTestGroup);

      // When calculating with 3 user slots and 2 group slots
      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 2);

      // Then: Should show 3 users and 1 group
      // Because:
      // 1. Need 1 slot for "more" indicator (6 items > 5 slots)
      // 2. Show max possible users with user slots (3)
      // 3. Show 1 group with remaining slot
      expect(result).toEqual([1, 3]);
    });

    it('should handle bidirectional reallocation when both users and groups need extra slots', () => {
      // Given: 5 users and 4 groups (total 9 items)
      // Available slots: 3 user slots + 3 group slots = 6 total slots
      const users = [1, 2, 3, 4, 5].map(createTestUser);
      const groups = [1, 2, 3, 4].map(createTestGroup);

      // When calculating with 3 user slots and 3 group slots
      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 3);

      // Then: Should show 3 users and 2 groups
      // Because:
      // 1. Need 1 slot for "more" indicator (9 items > 6 slots)
      // 2. Maximize visible items while keeping more indicator
      // 3. Balance between users and groups
      expect(result).toEqual([2, 3]);
    });

    it('should handle bidirectional reallocation when total slots exactly match item count', () => {
      // Given: 3 users and 2 groups (total 5 items)
      // Available slots: 3 user slots + 2 group slots = 5 total slots
      const users = [1, 2, 3].map(createTestUser);
      const groups = [1, 2].map(createTestGroup);

      // When calculating with 3 user slots and 2 group slots
      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 2);

      // Then: Should show all items since no more indicator needed
      // Because total items equals total slots
      expect(result).toEqual([2, 3]);
    });

    it('should handle exact fit without reallocation', () => {
      // Given: 2 users and 1 group, with 2 user slots and 1 group slot
      const users = [1, 2].map(createTestUser);
      const groups = [1].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 2, 1);
      // Should maintain original allocation since it fits perfectly
      expect(result).toEqual([1, 2]);
    });

    it('should handle case with fewer total items than slots', () => {
      // Given: 1 user and 1 group, with 3 user slots and 2 group slots
      const users = [1].map(createTestUser);
      const groups = [1].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 2);
      // Should only use needed slots without reallocation
      expect(result).toEqual([1, 1]);
    });

    it('should optimize allocation when both types have unused slots', () => {
      // Given: 2 users and 2 groups, with 4 user slots and 3 group slots
      const users = [1, 2].map(createTestUser);
      const groups = [1, 2].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 4, 3);
      // Should maintain efficient allocation without over-allocating
      expect(result).toEqual([2, 2]);
    });

    it('should handle uneven distribution with more items than slots', () => {
      // Given: 5 users and 4 groups, with 3 user slots and 2 group slots
      const users = [1, 2, 3, 4, 5].map(createTestUser);
      const groups = [1, 2, 3, 4].map(createTestGroup);

      const result = service['calculateMaxGroupsAndMaxUsers'](users, groups, 3, 2);
      // Should optimize allocation while keeping space for more indicator
      expect(result).toEqual([1, 3]);
    });
  });

  describe('calculateMaxDisplayedUsersAndGroups', () => {
    it('should return total sum when slots exceed available items', () => {
      const users = [1, 2].map(createTestUser);
      const groups = [1].map(createTestGroup);
      const result = service.calculateMaxDisplayedUsersAndGroups(users, groups, 5, 3);
      expect(result).toBe(3); // Total number of items (2 users + 1 group)
    });

    it('should respect maximum display limits and account for more indicator', () => {
      const users = [1, 2, 3, 4].map(createTestUser);
      const groups = [1, 2, 3].map(createTestGroup);
      const result = service.calculateMaxDisplayedUsersAndGroups(users, groups, 2, 1);
      expect(result).toBe(2); // 2 users + 0 group (reduced 1 for more indicator)
    });

    it('should handle empty arrays', () => {
      const result = service.calculateMaxDisplayedUsersAndGroups([], [], 3, 2);
      expect(result).toBe(0);
    });
  });

  describe('getDisplayedUsersAndGroups', () => {
    it('should return correct number of users and groups accounting for more indicator', () => {
      const users = [1, 2, 3, 4].map(createTestUser);
      const groups = [1, 2].map(createTestGroup);
      const result = service.getDisplayedUsersAndGroups(users, groups, 2, 1);

      expect(result.length).toBe(2); // 2 users + 0 group (reduced for more indicator)
      expect(result[0].id).toBe(1); // First user
      expect(result[1].id).toBe(2); // Second user
    });

    it('should handle empty arrays', () => {
      const result = service.getDisplayedUsersAndGroups([], [], 3, 2);
      expect(result).toEqual([]);
    });

    it('should handle null inputs', () => {
      const result = service.getDisplayedUsersAndGroups(null, null, 3, 2);
      expect(result).toEqual([]);
    });
  });

  describe('getRemainingUsersAndGroups', () => {
    it('should return items beyond the display limits accounting for more indicator', () => {
      const users = [1, 2, 3, 4].map(createTestUser);
      const groups = [1, 2, 3].map(createTestGroup);
      const result = service.getRemainingUsersAndGroups(users, groups, 2, 1);

      expect(result.length).toBe(5); // 2 remaining users + 3 remaining groups (because of more indicator)
      // Check if it contains the correct remaining items
      const remainingUserIds = result.slice(0, 2).map(u => u.id);
      const remainingGroupIds = result.slice(2).map(g => g.id);
      expect(remainingUserIds).toEqual([3, 4]);
      expect(remainingGroupIds).toEqual([1, 2, 3]);
    });

    it('should return empty array when all items fit within display limits', () => {
      const users = [1, 2].map(createTestUser);
      const groups = [1].map(createTestGroup);
      const result = service.getRemainingUsersAndGroups(users, groups, 2, 1);
      expect(result).toEqual([]);
    });

    it('should handle empty arrays', () => {
      const result = service.getRemainingUsersAndGroups([], [], 3, 2);
      expect(result).toEqual([]);
    });

    it('should handle null inputs', () => {
      const result = service.getRemainingUsersAndGroups(null, null, 3, 2);
      expect(result).toEqual([]);
    });
  });
});
