import { Injectable } from '@angular/core';
import { TeamUser, UserGroup } from '@core/models';
import { GroupService } from '@core/services';
import { UserTitlePipe } from '@core/pipes';

@Injectable({
  providedIn: 'root'
})
export class AvatarService {

  constructor(
    public groupService: GroupService,
    private userTitlePipe: UserTitlePipe
  ) {
  }

  hasMoreUsersAndGroups(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                        numberDisplayedGroups: number): boolean {
    return this.sumSharedUsersAndSharedGroups(users, groups) > numberDisplayedUsers + numberDisplayedGroups;
  }

  countSharedUsers(users: TeamUser[]) {
    return users?.length || 0;
  }

  countSharedGroups(groups: UserGroup[]) {
    return groups?.length || 0;
  }

  sumSharedUsersAndSharedGroups(users: TeamUser[], groups: UserGroup[]): number {
    return this.countSharedUsers(users) + this.countSharedGroups(groups);
  }

  hasUsersAndGroups(users: TeamUser[], groups: UserGroup[]): boolean {
    return this.sumSharedUsersAndSharedGroups(users, groups) > 0;
  }

  calculateMaxDisplayedUsersAndGroups(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                                      numberDisplayedGroups: number): number {
    const [maxGroups, maxUsers] = this.calculateMaxGroupsAndMaxUsers(users, groups, numberDisplayedUsers, numberDisplayedGroups);
    return Math.min(maxUsers + maxGroups, this.sumSharedUsersAndSharedGroups(users, groups));
  }

  getUsersAndGroupsSharedByMeTitle(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                                   numberDisplayedGroups: number, titlePrefix = 'Shared with'): string {
    let title = `${titlePrefix} ${this.getDisplayedUsersAndGroups(users, groups, numberDisplayedUsers, numberDisplayedGroups)
      .map(u => this.userTitlePipe.transform(u)).join(', ')}`;
    const [maxGroups, maxUsers] = this.calculateMaxGroupsAndMaxUsers(users, groups, numberDisplayedUsers, numberDisplayedGroups);
    const remainingUsers = this.countSharedUsers(users) - maxUsers;
    const remainingGroups = this.countSharedGroups(groups) - maxGroups;

    if (remainingUsers > 0) {
      title += ` ${remainingGroups > 0 ? ',' : 'and'} ${remainingUsers} more user${remainingUsers > 1 ? 's' : ''}`;
    }

    if (remainingGroups > 0) {
      title += ` and ${remainingGroups} more group${remainingGroups > 1 ? 's' : ''}`;
    }

    return `${title}.`;
  }

  getDisplayedUsersAndGroups(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                             numberDisplayedGroups: number): Array<TeamUser> {
    const results = [];
    const [maxGroups, maxUsers] = this.calculateMaxGroupsAndMaxUsers(users, groups, numberDisplayedUsers, numberDisplayedGroups);

    if (users) {
      for (let i = 0; i < maxUsers; i++) {
        results.push(users[i]);
      }
    }

    if (groups) {
      for (let i = 0; i < maxGroups; i++) {
        results.push(groups[i]);
      }
    }

    return results;
  }

  getRemainingUsersAndGroups(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                             numberDisplayedGroups: number): Array<TeamUser> {
    const results = [];
    const [maxGroups, maxUsers] = this.calculateMaxGroupsAndMaxUsers(users, groups, numberDisplayedUsers, numberDisplayedGroups);

    if (users) {
      for (let i = maxUsers; i < this.countSharedUsers(users); i++) {
        results.push(users[i]);
      }
    }

    if (groups) {
      for (let i = maxGroups; i < this.countSharedGroups(groups); i++) {
        results.push(groups[i]);
      }
    }

    return results;
  }

  getAvatarStyle(directionIndex: number, opacityIndex: number, direction: 'left' | 'right' = 'right', distanceBetweenUsers) {
    if (opacityIndex === null) {
      opacityIndex = directionIndex;
    }

    const opacityStep = 0;
    const rightDistance = (directionIndex * distanceBetweenUsers) + 'px';
    const opacity = 1 - opacityStep * opacityIndex;
    const style = {'opacity': opacity, 'z-index': directionIndex + 1, '--hoverZIndex': directionIndex + 2};
    style[direction] = rightDistance;
    return style;
  }

  genMoreUsersData(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                   numberDisplayedGroups: number): TeamUser {
    const max = this.calculateMaxDisplayedUsersAndGroups(users, groups, numberDisplayedUsers, numberDisplayedGroups);
    const sum = this.sumSharedUsersAndSharedGroups(users, groups);
    return {
      id: null,
      email: null,
      first_name: `+${sum - max}`,
      last_name: null,
      more_users: true
    } as TeamUser;
  }

  getAvatarIndex(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number, numberDisplayedGroups: number,
                 baseIndex: number, direction: 'left' | 'right' = 'right'): number {
    if (direction === 'left') {
      return baseIndex;
    }

    return this.calculateMaxDisplayedUsersAndGroups(users, groups, numberDisplayedUsers, numberDisplayedGroups) - baseIndex +
      (this.hasMoreUsersAndGroups(users, groups, numberDisplayedUsers, numberDisplayedGroups) ? 0 : -1)
  }

  private calculateMaxGroupsAndMaxUsers(users: TeamUser[], groups: UserGroup[], numberDisplayedUsers: number,
                                        numberDisplayedGroups: number): [number, number] {
    // Early return for empty case
    if (!users?.length && !groups?.length) {
      return [0, 0];
    }

    // First, adjust slots if we need space for "more" indicator
    let adjustedUserSlots = numberDisplayedUsers;
    let adjustedGroupSlots = numberDisplayedGroups;

    const hasMore = this.hasMoreUsersAndGroups(users, groups, adjustedUserSlots, adjustedGroupSlots);
    if (hasMore) {
      // Reduce group slots first if available, otherwise reduce user slots
      if (adjustedGroupSlots > 0) {
        adjustedGroupSlots--;
      } else {
        adjustedUserSlots--;
      }
    }

    const actualUserCount = this.countSharedUsers(users);
    const actualGroupCount = this.countSharedGroups(groups);

    // Calculate initial allocation
    let usedUserSlots = Math.min(adjustedUserSlots, actualUserCount);
    let usedGroupSlots = Math.min(adjustedGroupSlots, actualGroupCount);

    // Calculate unused slots
    const unusedUserSlots = adjustedUserSlots - usedUserSlots;
    const unusedGroupSlots = adjustedGroupSlots - usedGroupSlots;

    // Redistribute unused slots where needed
    if (unusedUserSlots > 0 && actualGroupCount > usedGroupSlots) {
      // Reallocate unused user slots to groups
      const additionalGroups = Math.min(unusedUserSlots, actualGroupCount - usedGroupSlots);
      usedGroupSlots += additionalGroups;
    }

    if (unusedGroupSlots > 0 && actualUserCount > usedUserSlots) {
      // Reallocate unused group slots to users
      const additionalUsers = Math.min(unusedGroupSlots, actualUserCount - usedUserSlots);
      usedUserSlots += additionalUsers;
    }

    return [usedGroupSlots, usedUserSlots];
  }
}
