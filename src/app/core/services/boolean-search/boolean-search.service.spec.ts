import { TestBed } from '@angular/core/testing';

import {
  BooleanFieldEnum,
  BooleanSearchService,
  Clause,
  ClauseConjunctionEnum,
  ClauseOperatorEnum,
  Field
} from '@core/services';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { cloneDeep } from 'lodash';
import { toUpperCaseClauseConjunction } from '@core/services/boolean-search/utils';
import { provideMatomo } from 'ngx-matomo-client';

describe('BooleanSearchService', () => {
  const TEST_CLAUSES = [
    new Clause(null, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.CONTAINS, 'a', 1, 0),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.PRIORITY_DATE), ClauseOperatorEnum.GREATER_THAN,
      new NgbDate(1999, 1, 14), 0, 1),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CLAIMS), ClauseOperatorEnum.NOT_CONTAIN, 'b', 1, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.APPLICANTS), ClauseOperatorEnum.EQUAL_TEXT, 'C', 0, 1),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.APPLICANTS), ClauseOperatorEnum.STARTS_WITH, 'C', 0, 1),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.APPLICANTS), ClauseOperatorEnum.CONTAINS, 'C', 0, 1),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.APPLICANTS), ClauseOperatorEnum.NOT_CONTAIN, 'C', 0, 1),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CPC), ClauseOperatorEnum.CONTAINS, 'D', 2, 0, ['D']),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CPC), ClauseOperatorEnum.NOT_CONTAIN, 'D', 0, 0, ['D']),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CPC), ClauseOperatorEnum.CONTAINS, 'D OR C', 0, 0, ['D', 'C']),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CPC), ClauseOperatorEnum.NOT_CONTAIN, 'D OR C', 0, 1, ['D', 'C']),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.IPC), ClauseOperatorEnum.NOT_CONTAIN, 'E', 1, 0, ['E']),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.IPC), ClauseOperatorEnum.CONTAINS, 'E', 0, 0, ['E']),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.IPC), ClauseOperatorEnum.NOT_CONTAIN, 'E OR F', 0, 0, ['E', 'F']),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.IPC), ClauseOperatorEnum.CONTAINS, 'E OR F', 0, 1, ['E', 'F']),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.INVENTORS), ClauseOperatorEnum.NOT_EQUAL_TEXT, 'F', 0, 1),
    new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.AUTHORITIES), ClauseOperatorEnum.CONTAINS, 'GERMANY', 1, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.RISK), ClauseOperatorEnum.LESS_THAN, '2', 0, 1),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.CONTAINS, 'air OR water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air OR water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.CONTAINS, 'air (water) & hot *water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air (water) & hot *water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.CONTAINS, 'air (water) and hot *water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air (water) and hot *water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air water^1 and hot *water~2', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, '"air water"^3 and hot *water~4', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air water^5 and "hot *water"~6', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, '"air water"^7 and "hot *water"~8', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, '(air water)^9 and (hot *water)~10', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.TITLE), ClauseOperatorEnum.NOT_CONTAIN, 'air "water -ice AND +hot *<>water', 0, 0),
    new Clause(ClauseConjunctionEnum.OR, Field.getFieldByName(BooleanFieldEnum.OWNER_IDS), ClauseOperatorEnum.CONTAINS, '1 OR 2 OR 3', 0, 0),
  ];
  const EXPECTED_FIELDS = [
    {field: BooleanFieldEnum.TITLE, operator: '=', value: 'a', open_brackets: 1, close_brackets: 0},
    {
      field: BooleanFieldEnum.PRIORITY_DATE,
      operator: '>',
      value: '1999-01-14',
      conjunction: 'AND',
      open_brackets: 0,
      close_brackets: 1
    },
    {field: BooleanFieldEnum.CLAIMS, operator: '<>', value: 'b', conjunction: 'AND', open_brackets: 1, close_brackets: 0},
    {field: BooleanFieldEnum.APPLICANTS, operator: '=', value: '(C)', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.APPLICANTS, operator: '=', value: '(C*)', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.APPLICANTS, operator: '=', value: '(*C*)', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.APPLICANTS, operator: '<>', value: '(*C*)', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.CPC, operator: '=', value: '(D*)', conjunction: 'AND', open_brackets: 2, close_brackets: 0},
    {field: BooleanFieldEnum.CPC, operator: '<>', value: '(D*)', conjunction: 'AND', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.CPC, operator: '=', value: '(D* OR C*)', conjunction: 'AND', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.CPC, operator: '<>', value: '(D* OR C*)', conjunction: 'AND', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.IPC, operator: '<>', value: '(E*)', conjunction: 'OR', open_brackets: 1, close_brackets: 0},
    {field: BooleanFieldEnum.IPC, operator: '=', value: '(E*)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.IPC, operator: '<>', value: '(E* OR F*)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.IPC, operator: '=', value: '(E* OR F*)', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.INVENTORS, operator: '<>', value: '(F)', conjunction: 'AND', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.AUTHORITIES, operator: '=', value: 'DE', conjunction: 'AND', open_brackets: 1, close_brackets: 0},
    {field: BooleanFieldEnum.RISK, operator: '<', value: '2', conjunction: 'OR', open_brackets: 0, close_brackets: 1},
    {field: BooleanFieldEnum.TITLE, operator: '=', value: '(air OR water)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '(air OR water)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '=', value: '("air (water) & hot *water")', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '("air (water) & hot *water")', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '=', value: '("air (water)" AND "hot *water")', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '("air (water)" AND "hot *water")', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '(air water^1 AND hot *water~2)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '("air water"^3 AND hot *water~4)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '(air water^5 AND "hot *water"~6)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '("air water"^7 AND "hot *water"~8)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '((air water)^9 AND (hot *water)~10)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.TITLE, operator: '<>', value: '(air "water -ice AND +hot *<>water)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
    {field: BooleanFieldEnum.OWNER_IDS, operator: '=', value: '(1 OR 2 OR 3)', conjunction: 'OR', open_brackets: 0, close_brackets: 0},
  ];
  const EXPECTED_QUERY = '(TITLE=a AND PRIORITY_DATE>1999-01-14) AND (CLAIMS<>b OR APPLICANTS=(C)) OR APPLICANTS=(C*) OR APPLICANTS=(*C*) OR APPLICANTS<>(*C*) ' +
    'AND ((CPC=(D*) AND CPC<>(D*) AND CPC=(D* OR C*) AND CPC<>(D* OR C*)) OR (IPC<>(E*) OR IPC=(E*) OR IPC<>(E* OR F*) OR IPC=(E* OR F*)) ' +
    'AND INVENTORS<>(F)) AND (AUTHORITIES=DE OR RISK<2) ' +
    'OR TITLE=(air OR water) OR TITLE<>(air OR water) OR TITLE=("air (water) & hot *water") OR TITLE<>("air (water) & hot *water") ' +
    'OR TITLE=("air (water)" AND "hot *water") OR TITLE<>("air (water)" AND "hot *water") ' +
    'OR TITLE<>(air water^1 AND hot *water~2) OR TITLE<>("air water"^3 AND hot *water~4) OR TITLE<>(air water^5 AND "hot *water"~6) ' +
    'OR TITLE<>("air water"^7 AND "hot *water"~8) OR TITLE<>((air water)^9 AND (hot *water)~10) ' +
    'OR TITLE<>(air "water -ice AND +hot *<>water) ' +
    'OR OWNER_IDS=(1 OR 2 OR 3)';

  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
  }));

  it('Should be able to format complex boolean query', () => {
    const service: BooleanSearchService = TestBed.inject(BooleanSearchService);
    service.reset();
    service.clauses.length = 0;
    service.clauses.push(...TEST_CLAUSES);
    expect(service.buildBooleanQuery()).toBe(EXPECTED_QUERY);
  });

  it('Should be able to validate boolean query', () => {
    const service: BooleanSearchService = TestBed.inject(BooleanSearchService);
    service.reset();
    expect(service.areClausesValid(false)).toBeFalsy();
    service.clauses.length = 0;
    service.clauses.push(...TEST_CLAUSES);
    expect(service.areClausesValid(false)).toBeTruthy();
  });

  it('Should be able to build server request', () => {
    const service: BooleanSearchService = TestBed.inject(BooleanSearchService);
    service.reset();
    service.clauses.length = 0;
    service.clauses.push(...TEST_CLAUSES);
    expect(service.buildPayload(false)).toEqual({additional_params: {}, search_fields: EXPECTED_FIELDS});
  });

  it('Should be able to load saved search', () => {
    const service: BooleanSearchService = TestBed.inject(BooleanSearchService);
    service.reset();
    service.clauses.length = 0;

    const expectedClauses = cloneDeep(TEST_CLAUSES);
    expectedClauses.filter(clause => clause.field.name === BooleanFieldEnum.TITLE)
      .forEach(clause => {
        clause.value = toUpperCaseClauseConjunction(clause.value.toString());
      });

    service.setClausesFromPayload(EXPECTED_FIELDS);
    expect(service.clauses).toEqual(expectedClauses);
  });
});
