import { catchError, debounceTime, distinctUntilChanged, switchMap, tap } from 'rxjs/operators';
import { EventEmitter, Injectable, Injector } from '@angular/core';
import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';

import { ApiService } from '../api/api.service';
import {
  BOOLEAN_DEFAULT_CLAUSES,
  BOOLEAN_FIELDS, BooleanFieldEnum,
  BooleanSearchLogicEnum,
  BooleanSearchRequest, TreeSelectionItem,
  Clause,
  ClauseConjunctionEnum,
  ClauseOperatorEnum,
  Field, OperationPrecedenceEnum
} from './types';
import { UserService } from '../user/user.service';
import { SearchQueryParams } from '../semantic-search/types';
import { PatentViewReferralType } from '@core/services/patent/types';
import { HttpErrorResponse } from '@angular/common/http';
import { tooltipTextAdvanced, tooltipTextBasic } from '@boolean/boolean-search/tooltip';
import { PatentListScopeEnum, PatentTableService, PublicationService, TagService } from '@core/services';
import { cloneDeep } from 'lodash';
import { TECH_FIELDS } from '@core/models';
import { splitOr } from '@core/services/boolean-search/utils';

@Injectable({
  providedIn: 'root'
})
export class BooleanSearchService {
  /**
   * additional data for singleton patent viewer
   */
  linkData = {title: 'Boolean Search', referral: PatentViewReferralType.REFERRAL_BOOLEAN, backPatentSearch: true};
  resetEvent = new EventEmitter(true);
  private documentsSubject = new BehaviorSubject<Array<Object>>([]);
  documents = this.documentsSubject.asObservable();
  private recoveredClausesFromStorageSubject = new BehaviorSubject<Array<Clause>>([]);
  recoveredClausesFromStorage$ = this.recoveredClausesFromStorageSubject.asObservable();
  private recoveredClausesFromHistorySubject = new BehaviorSubject<[Array<Clause>, any]>([null, null]);
  recoveredClausesFromHistory$ = this.recoveredClausesFromHistorySubject.asObservable();
  private _errorClauses: Array<Clause> = [];
  private _clauseValidators = {};
  private _clauseValidatorSubscriptions = {};

  protected userService: UserService;
  protected apiService: ApiService;
  protected patentTableService: PatentTableService;
  protected tagService: TagService;
  protected publicationService: PublicationService;

  constructor(
    protected inject: Injector
  ) {
    this.userService = inject.get(UserService);
    this.apiService = inject.get(ApiService);
    this.patentTableService = inject.get(PatentTableService);
    this.tagService = inject.get(TagService);
    this.publicationService = inject.get(PublicationService);
  }

  private _clauses: Array<Clause> = [];

  /**
   * get clauses array
   */
  get clauses(): Array<Clause> {
    return this._clauses;
  }

  private _fields: Array<Field> = [];

  get fields(): Array<Field> {
    return this._fields;
  }

  set fields(value: Array<Field>) {
    this._fields = value;
  }

  private _messageAlert: string | Array<string> = null;

  get messageAlert(): string | Array<string> {
    return this._messageAlert;
  }

  set messageAlert(value: string | Array<string>) {
    this._messageAlert = value;
  }

  private _validateMessage: string | Array<string> = null;

  get validateMessage(): string | Array<string> {
    return this._validateMessage;
  }

  set validateMessage(value: string | Array<string>) {
    this._validateMessage = value;
  }

  get clausesContainErrors(): boolean {
    return this._errorClauses.length > 0;
  }

  private _filterClauses: Clause[] = [];

  /**
   * get filtered clauses array
   */
  get filterClauses(): Array<Clause> {
    return this._filterClauses;
  }

  set filterClauses(val: Clause[]) {
    this._filterClauses = val;
  }

  private _search_input: string = null;

  get search_input(): string {
    return this._search_input;
  }

  set search_input(value: string) {
    this._search_input = value;
  }

  private _disableAdvancedModeSearch: boolean;
  get disableAdvancedModeSearch(): boolean {
    return this._disableAdvancedModeSearch;
  }

  set disableAdvancedModeSearch(value: boolean) {
    this._disableAdvancedModeSearch = value;
  }

  set recoveredClausesFromStorage(value: Array<Clause>) {
    this.recoveredClausesFromStorageSubject.next(value);
  }

  set recoveredClausesFromHistory(value: [Array<Clause>, any]) {
    this.recoveredClausesFromHistorySubject.next(value);
  }

  get ownerIdsOfClauses(): number[] {
    const ownerClauses = this.clauses.filter(clause => clause.field?.name === BooleanFieldEnum.OWNER_IDS);
    return ownerClauses
      .map(clause => {
        return (clause.value?.toString() || '')
          .split(' OR ')
          .map(o => parseInt(o.trim()))
      })
      .flatMap(o => o);
  }

  /**
   * makeClausesFromSearchFields
   *
   * convert boolean request payload into boolean clauses array
   * @param searchFields boolean request payload array
   * @returns clause array
   */
  static makeClausesFromSearchFields(searchFields: Array<any>): Array<Clause> {
    const clausesTmp = [];
    for (const searchField of searchFields) {
      if (searchField.field && searchField.value) {
        const clause = new Clause();
        clause.setField(Field.getFieldByName(searchField.field));
        switch (searchField.field) {
          case BooleanFieldEnum.CPC:
          case BooleanFieldEnum.IPC:
            clause.valueArr = this.buildTagsCpcIpc(searchField.value.toString());
            break;
          case BooleanFieldEnum.TECH_FIELDS:
            clause.valueArr = this.buildTagsTechFields(searchField.value.toString());
            break;
          case BooleanFieldEnum.LEGAL_STATUS:
            clause.valueArr = this.buildTagsLegalStatus(searchField.value.toString());
            break;
        }
        clause.setValueByText(searchField.value.trim());
        clause.setConjunctionByText(searchField.conjunction);
        clause.setOperatorByText(searchField.operator);
        clause.cleanValue();
        if ('open_brackets' in searchField) {
          clause.openBrackets = searchField.open_brackets;
        }
        if ('close_brackets' in searchField) {
          clause.closeBrackets = searchField.close_brackets;
        }
        clausesTmp.push(clause);
      }
    }
    return clausesTmp;
  }

  private static buildTagsCpcIpc(value: string): Array<string> {
    return value.replace(/[\(\)\*('OR')]/g, '').split(' ').filter(item => item.length);
  }

  private static buildTagsTechFields(value: string): Array<string> {
    const techFields = value.replace(/[\(\)\*()]/g, '').split(' OR ').filter(item => item.length);
    return this.groupTechFieldsByArea(techFields);
  }

  private static buildTagsLegalStatus(value: string): Array<string> {
    return splitOr(value.replace(/[\(\)\*()]/g, '')).filter(v => v.length).map(v => v.toLowerCase());
  }

  private static groupTechFieldsByArea(techFields: string[]): string[] {
    const techAreas = {};
    TECH_FIELDS.forEach(field => {
      const {area, name} = field;
      if (!techAreas[area]) {
        techAreas[area] = [];
      }
      techAreas[area].push(name);
    });
    Object.keys(techAreas).forEach(area => {
      const fields = techAreas[area];
      if (fields.every(field => techFields.includes(field))) {
        techFields.splice(techFields.indexOf(fields[0]), fields.length, area);
      }
    });
    return techFields;
  }

  private static orEmpty(o: any): string {
    return o || '';
  }

  /**
   * Convert json query to raw string query for a clause
   *
   * @return string
   */
  private static jsonToStringQuery(jsonQuery: {}): string {
    return `${jsonQuery['field']}${jsonQuery['operator']}${BooleanSearchService.orEmpty(jsonQuery['value'])}`;
  }

  getValidClauses(isPublicationSearch: boolean): Array<Clause> {
    return this.clauses.filter((c) => c.isValid(isPublicationSearch));
  }

  addErrorClause(clause: Clause) {
    this._errorClauses.push(clause);
  }

  removeErrorClause(clause: Clause) {
    this._errorClauses = this._errorClauses.filter(o => !o.equals(clause));
  }

  addValidatorForClause(clause: Clause, isPublicationSearch: boolean) {
    if (clause && clause.isBaseValid() && clause.isFieldValid(isPublicationSearch)) {
      const value = clause.formatValue();
      if (clause.id in this._clauseValidators) {
        this._clauseValidators[clause.id].next(value);
      } else {
        const behavior = new BehaviorSubject<string>(value);
        const subscription = behavior.pipe(
          debounceTime(500),
          distinctUntilChanged(),
          tap((val) => {
            this.removeErrorClause(clause);
            clause.errorMsg = null;
          }),
          switchMap((val) => {
            return this.callValidatorService(clause, isPublicationSearch);
          })
        ).subscribe();
        this._clauseValidators[clause.id] = behavior;
        this._clauseValidatorSubscriptions[clause.id] = subscription;
      }
    }
  }

  removeValidatorOfClause(clause: Clause) {
    if (clause && clause.id) {
      const subscription = this._clauseValidatorSubscriptions[clause.id] as Subscription;
      if (subscription) {
        subscription.unsubscribe();
        delete this._clauseValidatorSubscriptions[clause.id];
      }
      if (this._clauseValidators[clause.id]) {
        delete this._clauseValidators[clause.id];
      }
    }
  }
  private precessPayload(payload: BooleanSearchRequest){
    if(this.userService.getUISetting('booleanSearchLogic', BooleanSearchLogicEnum.MATHEMATICAL) === BooleanSearchLogicEnum.MATHEMATICAL){
      payload.operation_precedence = OperationPrecedenceEnum.MATHEMATICAL;
    } else {
      payload.operation_precedence = OperationPrecedenceEnum.DEFAULT;
    }
  }

  search(
    payload: BooleanSearchRequest,
    queryParams?: SearchQueryParams,
    updateDocuments = true
  ): Observable<any> {
    const endpoint = 'web/search/boolean';
    this.precessPayload(payload)
    return this.apiService.post(endpoint, payload, queryParams)
      .pipe(
        tap(({data}) => {
          if (updateDocuments) {
            this.documentsSubject.next(data.publications ? this.publicationService.publicationsToDocuments(data.publications) : data.documents);
          }
        })
      );
  }

  public getCachedSearch(hash: string, payload?: BooleanSearchRequest, queryParams?: SearchQueryParams): Observable<any> {
    return this.apiService.post(`search/hash/${hash}`, payload, queryParams);
  }

  getDocuments(): Object[] {
    return this.documentsSubject.value;
  }

  resetDocuments(): void {
    this.documentsSubject.next([]);
  }

  setDocuments(value): void {
    this.documentsSubject.next(value);
  }

  export(hash: string, ids: any, queryParams: any): Observable<any> {
    return this.apiService.asyncExport(`web/export/${hash}`, ids, queryParams);
  }

  /**
   * setClausesFromPayload
   *
   * set clauses array from boolean request payload
   * @param value new value for boolean clauses array
   */
  setClausesFromPayload(value: any) {
    this._clauses = BooleanSearchService.makeClausesFromSearchFields(value);
  }

  /**
   * resetErrorOfClauses
   *
   * reset error message for each clause
   */
  resetErrorOfClauses() {
    for (const clause of this._clauses) {
      clause.errorMsg = null;
    }
  }

  /**
   * resetClauses
   */
  resetClauses() {
    this._clauses = [new Clause()];
  }

  /**
   * booleanInit
   *
   * initiate the boolean input query reference
   * @param clause clause to initiate the query reference
   */
  booleanInit(isPublicationSearch: boolean, clause = new Clause()) {
    this.fields = BOOLEAN_FIELDS;
    if (this.clauses.length === 0) {
      this.generateClauseId(clause);
      this.clauses.push(clause);
      this.addValidatorForClause(clause, isPublicationSearch);
    }
  }

  setDefaultClause(clauses: Clause[]){
    this.clauses.length = 0;
    this.clauses.push(...cloneDeep(clauses));
    this.fields = BOOLEAN_FIELDS;
  }

  /**
   * check whether boolean clause array is valid or not
   * @returns boolean state of clause array validity
   */
  areClausesValid(isPublicationSearch: boolean) {
    if (this.clauses.length === 0) {
      return false;
    }

    if (this.isInitialClauses()) {
      return this.clauses.some(c => c.isValid(isPublicationSearch));
    }

    return this.clauses.every((c, i) => this.isDefaultClause(c, i) || c.isValid(isPublicationSearch));
  }

  validateClauses(isPublicationSearch: boolean) {
    for (let clause of this.clauses) {
      clause.isFieldValid(isPublicationSearch);
    }
  }

  /**
   * check whether boolean clause array have empty values or not
   * @returns boolean state of clause array value emptiness
   */
  areClausesEmpty() {
    if (this.clauses.length === 0) {
      return true;
    }
    return this.clauses.every(c => c.isEmpty());
  }

  cleanBooleanClauses(){
    let brackets = 0;
    this._clauses = this._clauses.filter(c => {
      if(c.openBrackets > 0){
        brackets++;
      }
      if(c.isEmpty()){
        if(brackets === 0){
          return false;
        }
      }
      if(c.closeBrackets > 0){
        brackets--;
      }
      return true;
    });
  }

  /**
   * isInitialClauses
   *
   * check if the boolean form contains initial clause only
   */
  isInitialClauses(): boolean {
    const defaultFields = BOOLEAN_DEFAULT_CLAUSES.map(c => c.field);
    if (this.clauses.length === BOOLEAN_DEFAULT_CLAUSES.length) {
      return this.clauses.every((c, i) => c.field?.equals(defaultFields[i]));
    }
  }

  private isDefaultClause(clause: Clause, i): boolean {
    const defaultFields = BOOLEAN_DEFAULT_CLAUSES.map(c => c.field);
    return clause.field?.equals(defaultFields[i]);
  }

  isInputEmpty() {
    const firstClause = this.clauses[0];
    return this.clauses.length === 1 && (!firstClause.value || !firstClause.field);
  }

  /**
   * resetBoolean
   *
   * reset all boolean reference on active component
   */
  reset(resetSearchInput = false) {
    this.resetClauses();
    this.resetEvent.emit(true);
    if (this.messageAlert) {
      this.messageAlert = null;
    }
    if (this.validateMessage) {
      this.validateMessage = null;
    }
    if (resetSearchInput) {
      this.search_input = '';
    }
  }

  /**
   * setBoolean
   *
   * set boolean reference on active component
   */
  setClauses(clauses: Array<Clause>) {
    this._clauses = clauses;
  }

  /**
   * build Boolean search request payload
   * @returns Boolean search request payload object
   */
  buildPayload(isPublicationSearch: boolean, filters = [], owners: TreeSelectionItem[] = []): BooleanSearchRequest {
    const payload = {
      additional_params: {}
    };

    const items = this.getValidClauses(isPublicationSearch);
    payload['search_fields'] = BooleanSearchService.clausesToJsonQuery(items);


    if (filters.length > 0) {
      payload['search_filters'] = {free_text_query: this.filtersToQueryString(filters)};
      payload.additional_params['filter_input'] = JSON.stringify(filters);
    }

    if (this.patentTableService.selectedLegalStatus?.length > 0) {
      payload.additional_params['results_table_filter'] = {
        selected_legal_statuses: this.patentTableService.selectedLegalStatus
      }
    }

    if (owners?.length > 0) {
      const ownerIds = this.ownerIdsOfClauses;
      payload.additional_params['owners'] = owners.filter(o => ownerIds.includes(o.id));
    }

    return payload;
  }

  /**
   * Format boolean query as string
   */
  buildBooleanQuery(prepareTagId = true): string {
    return BooleanSearchService.buildBooleanQueryFromClauses(this.clauses, prepareTagId);
  }

  static clausesToJsonQuery(clauses: Clause[]): any[] {
    return clauses.length > 0 ? [clauses.shift().toJsonQuery(false), ...clauses.map(c => c.toJsonQuery(true))] : [];
  }

  /**
   * Format boolean query as string from clauses
   */
  static buildBooleanQueryFromClauses(clauses: Clause[], prepareTagId = true): string {
    let out = '';
    let brackets = 0;
    for (let i = 0; i < clauses.length; i++) {
      const clause = clauses[i];
      if (!clause.field || !clause.value) {
        continue;
      }
      const c = clause.toJsonQuery(i > 0, prepareTagId);

      if (out.length > 0) {
        out += ' ' + BooleanSearchService.orEmpty(c['conjunction']) + ' ';
      }

      for (let j = 0; j < clause.openBrackets; j++) {
        out += '(';
        brackets++;
      }
      out += BooleanSearchService.jsonToStringQuery(c);
      for (let j = 0; j < clause.closeBrackets; j++) {
        if (brackets === 0) {
          break;
        }
        out += ')';
        brackets--;
      }
    }
    for (let j = 0; j < brackets; j++) {
      out += ')';
    }
    out = out.replace(/ASSIGNEE/g, 'OWNER');
    return out.trim();
  }

  private replaceTagNameToTagId(search_input: string) {
    const tags = this.tagService.tags;
    if (tags.length === 0) {
      return search_input;
    }
    const tagMap = new Map(tags.map(tag => [tag.name, tag.id.toString()]));
    const regex = new RegExp('(?<=TAG(=|<>))\\s*\\b(' + [...tagMap.keys()].join('|') + ')\\b', 'gi');
    return search_input.replace(regex, (matched) => tagMap.get(matched.trim()));
  }

  public buildPayloadAdvancedMode(filters = []) {
    const payload = {
      search_input: this.replaceTagNameToTagId(this.search_input)
    };
    if(filters.length > 0){
      payload['search_filters'] = {free_text_query: this.filtersToQueryString(filters)};
      payload['additional_params'] = {
        filter_input: JSON.stringify(filters)
      };
    }
    return payload;
  }

  closeMismatchedBrackets() {
    const len = this.clauses.length;
    if (len > 0) {
      let brackets = 0;

      for (let i = 0; i < len; i++) {
        const clause = this.clauses[i];
        if (!clause.field || !clause.value) {
          continue;
        }
        brackets += clause.openBrackets;
        brackets -= Math.min(brackets, clause.closeBrackets);
      }
      this.clauses[len - 1].closeBrackets += brackets;
    }
  }

  numberOfOpenBracketsAtPosition(position: number): number {
    let level = 0;
    for (let i = 0; i < position; i++) {
      level -= this.clauses[i].closeBrackets;
      level += this.clauses[i].openBrackets;
    }
    level += this.clauses[position].openBrackets;
    return level;
  }

  /**
   * Remove closing brackets which do not have an opening match
   */
  ensureBracketMatch() {
    let openBrackets = 0;
    for (let i = 0; i < this.clauses.length; i++) {
      const clause = this.clauses[i];
      openBrackets += clause.openBrackets;
      if (clause.closeBrackets > openBrackets) {
        clause.closeBrackets = openBrackets;
      }
      openBrackets -= clause.closeBrackets;
    }
  }

  /**
   * Increase number of opening or closing brackets
   */
  incLevel(n: number, direction: number) {
    const clause = this.clauses[n];
    if (direction > 0) {
      if (clause.openBrackets === 3) {
        clause.openBrackets = 0;
        this.ensureBracketMatch();
      } else {
        if (this.numberOfOpenBracketsAtPosition(n) < 3) {
          clause.openBrackets++;
        } else if (clause.openBrackets > 0) {
          clause.openBrackets--;
          this.ensureBracketMatch();
        }
      }
    } else if (direction < 0 && this.numberOfOpenBracketsAtPosition(n) - clause.closeBrackets > 0
      && this.numberOfOpenBracketsAtPosition(this.clauses.length - 1) > 0) {
      clause.closeBrackets++;
      if (clause.closeBrackets > 3) {
        clause.closeBrackets = 0;
      }
      this.ensureBracketMatch();
    }
  }

  parseErrorValue(error): Array<{ value: string, reason: string }> {
    const errorValue = [];
    if (error && error.semantic_errors) {
      for (const semanticError of error.semantic_errors) {
        errorValue.push({value: semanticError.value, reason: semanticError.reason});
      }
    }
    return errorValue;
  }

  parseErrorMessage(error, defaultMessage: string, attachErrorToClauses: boolean): string {
    let errorMessage = defaultMessage;

    if (error) {
      if (error.message) {
        errorMessage = `<p>${error.message}. Please review it and try again.</p>`;
      }

      errorMessage += '<dl>';

      if (error.semantic_errors) {
        if (attachErrorToClauses) {
          this.resetErrorOfClauses();
        }

        for (const semanticError of error.semantic_errors) {
          if (attachErrorToClauses) {
            for (const clause of this.clauses) {
              if (clause.field.name === semanticError.term && clause.value === semanticError.value) {
                clause.errorMsg = semanticError.reason;
                break;
              }
            }
          }

          errorMessage += `<li>${semanticError.reason}</li>`;
        }
      }

      if (error.parser_error) {
        errorMessage += `<li>${error.parser_error.message}</li>`;
      }

      if (error.lexer_error) {
        errorMessage += `<li>${error.lexer_error.message}</li>`;
      }

      errorMessage += '</dl>';
    }

    return errorMessage;
  }

  addNewClause(isPublicationSearch: boolean) {
    const clause = new Clause();
    this.generateClauseId(clause);
    this.clauses.push(clause);
    this.addValidatorForClause(clause, isPublicationSearch);
  }

  removeClause(index: number) {
    if (this.clauses.length > 1) {
      this.removeValidatorOfClause(this.clauses[index]);
      this.clauses.splice(index, 1);
    }
    this.ensureBracketMatch();
  }

  parse(query: string, clause: Clause, isPublicationSearch: boolean): Observable<any> {
    const searchType = isPublicationSearch ? PatentListScopeEnum.PUBLICATION : PatentListScopeEnum.FAMILY;
    const payload = {search_input: query, search_type: searchType};
    this.precessPayload(payload)
    return this.apiService.post('search/boolean/parse', payload)
      .pipe(
        tap((resp) => {
          clause.errorMsg = null;
          this.removeErrorClause(clause);
        }),
        catchError((err) => {
          if (err instanceof HttpErrorResponse && err.status) {
            this.addErrorClause(clause);
            clause.errorMsg = this.parseErrorMessage(err.error, '', false);
            clause.errorValue = this.parseErrorValue(err.error);
          }
          return of(null);
        }));
  }

  isClauseLimited(): boolean {
    return this.userService.isFreeUser() && this.clauses.length > 2;
  }

  parseAdvanced(query: string, searchType: PatentListScopeEnum) {
    const payload = {search_input: query, search_type: searchType};

    this.precessPayload(payload)
    return this.apiService.post('search/boolean/parse', payload);
  }

  private generateClauseId(clause: Clause) {
    if (clause && !clause.id) {
      clause.id = this._clauses.length + 1;
    }
  }

  private callValidatorService(clause: Clause, isPublicationSearch: boolean): Observable<any> {
    if (!clause.isValid(isPublicationSearch)) {
      return of(null);
    }

    const query = BooleanSearchService.jsonToStringQuery(clause.toJsonQuery(false));
    return this.parse(query, clause, isPublicationSearch);
  }

  public getTooltipSearchInput(advancedMode: boolean) {
    return advancedMode ? tooltipTextAdvanced : tooltipTextBasic;
  }

  public legalStatusFilterToClauses(filter, field): Array<Clause> {
    const clauses: Array<Clause> = [];

    const values = filter.value.split('AND');
    values.forEach(value => {
      const val = value.split('=');
      const clause = new Clause();
      clause.setField(field);
      clause.value = val[1];
      clause.operator = ClauseOperatorEnum.EQUAL;
      clause.conjunction = ClauseConjunctionEnum.AND;
      clause.filterItem = filter;
      clauses.push(clause);
    });

    return clauses;
  }

  private filtersToQueryString(filters: any[]): string {
    return (filters || []).map(filter => `(${filter.query})`).join(' AND ');
  }

  clearStoredData(): void {
    this._clauses = [];
    this._fields = [];
    this._messageAlert = null;
    this._validateMessage = null;
    this._errorClauses = [];
    this._clauseValidators = {};
    this._clauseValidatorSubscriptions = {};
    this._filterClauses = [];
    this._search_input = null;
    this._disableAdvancedModeSearch = false;
    this.documentsSubject.next([]);
  }
}
