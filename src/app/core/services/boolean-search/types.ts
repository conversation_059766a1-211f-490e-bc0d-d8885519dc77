import { NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { AUTHORITY_CODE_NODES, TECH_AREAS, TECH_FIELDS } from '@core/models';
import { PatentListScopeEnum } from '../collection/types';
import {
  appendWildcard, detachWildcard, discloseByParenthesis, discloseByWildcard,
  encloseByParenthesis, encloseByWildcard,
  enquoteClauseValue, isEndWidthWildcardText, isFullWildcardText,
  joinOr, splitThenJoinClauseValue, splitThenJoinOr,
  unquoteClauseValue, toUpperCaseClauseConjunction, splitOr, enquoteClauseTextValue
} from '@core/services/boolean-search/utils';

export interface BooleanSearchRequest {
  search_input?: string;
  search_fields?: {};
  operation_precedence?: OperationPrecedenceEnum;
  search_filters?: {
    free_text_query: string;
  };
  search_document_type?: PatentListScopeEnum;
  additional_params?: {
    filter_input?: string;
    results_table_filter?: {
      selected_legal_statuses: string[];
    };
  }
}

export interface TreeSelectionItem {
  value: string;
  id: number;
  name?: string;
  children_count?: number;
}

export enum FieldTypeEnum {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  NUMBER_LIST = 'number_list',
  GROUP = 'group'
}

export enum BooleanSearchLogicEnum {
  DEFAULT = 'default',
  MATHEMATICAL = 'mathematical'
}

export enum OperationPrecedenceEnum {
  DEFAULT = 'DEFAULT',
  MATHEMATICAL = 'MATHEMATICAL'
}

export enum BooleanFieldEnum {
  TEXT_FIELDS_GROUP = 'TEXT_FIELDS_GROUP',
  TITLE = 'TITLE',
  ABSTRACT = 'ABSTRACT',
  CLAIMS = 'CLAIMS',
  DESCRIPTION = 'DESCRIPTION',
  TEXT = 'TEXT',
  TAC = 'TAC',

  DATES_GROUP = 'DATES_GROUP',
  PRIORITY_DATE = 'PRIORITY_DATE',
  PUBLICATION_DATE = 'PUBLICATION_DATE',
  APPLICATION_DATE = 'APPLICATION_DATE',

  NUMBERS_GROUP = 'NUMBERS_GROUP',
  APPLICATION_NUMBERS = 'APPLICATION_NUMBERS',
  PUBLICATION_KIND = 'PUBLICATION_KIND',
  ALSO_PUBLISHED_AS = 'ALSO_PUBLISHED_AS',

  ENTITIES_GROUP = 'ENTITIES_GROUP',
  APPLICANTS = 'APPLICANTS',
  INVENTORS = 'INVENTORS',
  OWNERS = 'OWNERS',
  OWNER_IDS = 'OWNER_IDS',

  CLASSIFICATION_GROUP = 'CLASSIFICATION_GROUP',
  CPC = 'CPC',
  IPC = 'IPC',
  TECH_FIELDS = 'TECH_FIELDS',

  AUTHORITY_GROUP = 'AUTHORITY_GROUP',
  AUTHORITIES = 'AUTHORITIES',
  PUBLICATION_AUTHORITY = 'PUBLICATION_AUTHORITY',
  NUMBER_OF_AUTHORITIES = 'NUMBER_OF_AUTHORITIES',

  OTHERS_GROUP = 'OTHERS_GROUP',
  LEGAL_STATUS = 'LEGAL_STATUS',
  IMPACT = 'IMPACT',
  RISK = 'RISK',
  CITATION_BACKWARD_COUNT = 'CITATION_BACKWARD_COUNT',
  CITATION_FORWARD_COUNT = 'CITATION_FORWARD_COUNT',
  TAG = 'TAG'
}

const providers = [
  BooleanFieldEnum.APPLICANTS, BooleanFieldEnum.INVENTORS, BooleanFieldEnum.OWNERS
];

export class Field {
  name: BooleanFieldEnum;
  title: string;
  type: FieldTypeEnum;
  tooltip: string;
  group_name: BooleanFieldEnum;

  comparableFieldTypes = [FieldTypeEnum.NUMBER, FieldTypeEnum.DATE];

  constructor(name: BooleanFieldEnum, title: string, type: FieldTypeEnum, tooltip: string, group_name: BooleanFieldEnum) {
    this.name = name;
    this.title = title;
    this.type = type;
    this.tooltip = tooltip;
    this.group_name = group_name;
  }

  public static getFieldByName(name: BooleanFieldEnum): Field {
    return BOOLEAN_FIELDS.find(f => f.name === name);
  }

  isComparable(): boolean {
    return this.comparableFieldTypes.indexOf(this.type) > -1;
  }

  equals(field: Field): boolean {
    return field && (this.type === field.type) && (this.name === field.name);
  }

  isTypeGroup(): boolean {
    return this.type === FieldTypeEnum.GROUP;
  }

  isGroupOf(field: Field): boolean {
    return this.isTypeGroup() && this.name === field?.group_name;
  }

  isExcludedFieldForPublicationSearch(): boolean {
    return EXCLUDED_BOOLEAN_FIELDS_FOR_PUBLICATION_SEARCH.includes(this.name);
  }
  isExcludedFieldForFamilySearch(): boolean {
    return EXCLUDED_BOOLEAN_FIELDS_FOR_FAMILY_SEARCH.includes(this.name)
  }
}

export enum ClauseOperatorEnum {
  EQUAL = '=',
  LESS_THAN = '<',
  GREATER_THAN = '>',
  NOT_EQUAL = '<>',

  STARTS_WITH = 'STARTS WITH',
  CONTAINS = 'CONTAINS',
  NOT_CONTAIN = 'DOES NOT CONTAIN',

  EQUAL_TEXT = 'EQUALS',
  NOT_EQUAL_TEXT = 'DOES NOT EQUAL'
}

export enum ClauseConjunctionEnum {
  AND = 'AND',
  OR = 'OR'
}

export class Clause {
  id: number;
  conjunction: ClauseConjunctionEnum;
  field: Field;
  operator: ClauseOperatorEnum;
  value: string | NgbDate;
  valueArr?: Array<string | TreeSelectionItem>;
  comparableOperators = [
    ClauseOperatorEnum.EQUAL, ClauseOperatorEnum.LESS_THAN,
    ClauseOperatorEnum.GREATER_THAN, ClauseOperatorEnum.NOT_EQUAL
  ];
  textOperators = [
    ClauseOperatorEnum.CONTAINS, ClauseOperatorEnum.NOT_CONTAIN
  ];
  numberListOperators = [
    ClauseOperatorEnum.CONTAINS, ClauseOperatorEnum.NOT_CONTAIN
  ];
  equalsTextOperators = [
    ClauseOperatorEnum.EQUAL_TEXT, ClauseOperatorEnum.NOT_EQUAL_TEXT
  ];
  errorMsg: string = null;
  warningMsg: string = null;
  errorValue: Array<{ value: string, reason: string }> = null;
  filterItem = null;
  openBrackets = 0;
  closeBrackets = 0;

  constructor(conjunction?: ClauseConjunctionEnum, field?: Field, operator?: ClauseOperatorEnum, value?: string | NgbDate,
              openBrackets?: number, closeBrackets?: number, valueArr?: Array<string | TreeSelectionItem>) {
    this.conjunction = conjunction ? conjunction : ClauseConjunctionEnum.AND;
    this.field = field;
    this.operator = operator ? operator : this.getDefaultOperator(field);
    this.value = value;
    this.closeBrackets = closeBrackets ? closeBrackets : 0;
    this.openBrackets = openBrackets ? openBrackets : 0;
    if (valueArr) {
      this.valueArr = valueArr;
    }
  }

  /**
   * Decide if we keep or throw away value and comparison operator when changing field in condition.
   * If switching between fields of the same type, we keep it.
   */
  private static shouldKeepInputValue(prevField: Field, newField: Field) {
    if (!prevField || !newField || prevField.type !== newField.type) {
      return false;
    }

    const keepValueFields = [
      {
        type: FieldTypeEnum.TEXT,
        fields: [
          BooleanFieldEnum.TITLE, BooleanFieldEnum.ABSTRACT, BooleanFieldEnum.DESCRIPTION, BooleanFieldEnum.CLAIMS,
          BooleanFieldEnum.TEXT
        ]
      },
      {
        type: FieldTypeEnum.TEXT,
        fields: [BooleanFieldEnum.IPC, BooleanFieldEnum.CPC]
      },
      {
        type: FieldTypeEnum.TEXT,
        fields: [BooleanFieldEnum.ALSO_PUBLISHED_AS, BooleanFieldEnum.APPLICATION_NUMBERS]
      },
      {
        type: FieldTypeEnum.TEXT,
        fields: [BooleanFieldEnum.AUTHORITIES, BooleanFieldEnum.PUBLICATION_AUTHORITY]
      },
      {
        type: FieldTypeEnum.NUMBER,
        fields: null
      },
      {
        type: FieldTypeEnum.DATE,
        fields: null
      }
    ];

    return keepValueFields.some(({type, fields}) => {
      if (type != prevField.type) {
        return false;
      }
      return !fields?.length || (fields.includes(prevField.name) && fields.includes(newField.name));
    });
  }

  clone(): Clause {
    return new Clause(this.conjunction, this.field, this.operator, this.value, this.openBrackets, this.closeBrackets,
      this.valueArr);
  }

  setField(field: Field) {
    if (!Clause.shouldKeepInputValue(this.field, field)) {
      this.value = null;
      this.filterItem = null;
      this.operator = this.getDefaultOperator(field);
      this.errorMsg = null;
      if (this.valueArr) {
        this.valueArr = [];
      }
    }

    this.field = field;
  }

  isComparable(field: Field): boolean {
    return field && field.isComparable();
  }

  isAuthorityField(): boolean {
    return this.field && [BooleanFieldEnum.PUBLICATION_AUTHORITY, BooleanFieldEnum.AUTHORITIES].includes(this.field.name);
  }

  isPlainTextField(): boolean {
    return this.field && [
      BooleanFieldEnum.TITLE, BooleanFieldEnum.ABSTRACT, BooleanFieldEnum.CLAIMS,
      BooleanFieldEnum.DESCRIPTION, BooleanFieldEnum.TEXT
    ].includes(this.field.name);
  }

  isNumberListField(field: Field): boolean {
    return field && [BooleanFieldEnum.OWNER_IDS].includes(field.name);
  }

  isValuablePercentageField(field: Field): boolean {
    return field && [BooleanFieldEnum.IMPACT, BooleanFieldEnum.RISK].includes(field.name);
  }

  isFullWildcardOperator(): boolean {
    return [ClauseOperatorEnum.CONTAINS, ClauseOperatorEnum.NOT_CONTAIN].includes(this.operator);
  }

  isEndWidthWildcardOperator(): boolean {
    return [ClauseOperatorEnum.STARTS_WITH].includes(this.operator);
  }

  toJsonQuery(hasConjunction: boolean, prepareTagId = true): {} {
    const result = {
      field: this.field.name,
      operator: this.operatorForJsonQuery(),
      value: this.formatValue(prepareTagId),
      open_brackets: this.openBrackets,
      close_brackets: this.closeBrackets,
    };

    if (hasConjunction) {
      result['conjunction'] = this.conjunction;
    }

    return result;
  }

  isValid(isPublicationSearch: boolean): boolean {
    return this.isBaseValid() && this.isValueValid() && this.isFieldValid(isPublicationSearch);
  }

  isEmpty(): boolean{
    return this.isValueEmpty();
  }

  isInitial(): boolean {
    return !this.field && !this.value;
  }

  haveParenthesis(): boolean{
    return (this.openBrackets + this.closeBrackets) > 0;
  }

  isBaseValid(): boolean {
    return !!(this.conjunction && this.operator && this.field);
  }

  setValueByTechArea(area: { name: string; value: string }) {
    this.value = area.value;
  }

  setConjunctionByText(conjunctionText: string) {
    if (conjunctionText === 'OR') {
      this.conjunction = ClauseConjunctionEnum.OR;
    } else {
      this.conjunction = ClauseConjunctionEnum.AND;
    }
  }

  setValueByText(valueText: string) {
    if (this.field && this.field.type === FieldTypeEnum.DATE && valueText && typeof (valueText) === 'string') {
      const [year, month, day] = valueText.split(/-/).map(o => parseInt(o, 10));
      if (year && month && day) {
        this.value = new NgbDate(year, month, day);
      } else {
        this.value = new NgbDate(year, 1, 1);
      }
    } else if (this.isAuthorityField()) {
      const authorityById = AUTHORITY_CODE_NODES.find(o => o.id.toUpperCase() === valueText);
      const authorityByName = AUTHORITY_CODE_NODES.find(o => o.name === valueText.toUpperCase());
      this.value = authorityByName ? authorityByName.name : authorityById?.name;
    } else {
      this.value = valueText;
    }
  }

  setValueByLegalStatus(status: { value: string; text: string}, event?: MouseEvent) {
    event?.stopPropagation();
    this.valueArr = this.valueArr || [];
    this.valueArr.push(status.value);
    this.value = this.valueArr.join(' OR ');
  }

  setUnSelectLegalStatus(status: { value: string; text: string }, event?: MouseEvent) {
    event?.stopPropagation();
    const index = this.valueArr.indexOf(status.value);
    this.valueArr.splice(index, 1);
    this.value = this.valueArr.join(' OR ');
  }

  /**
   * When loading saved fields, strip automatically appended * and Parentheses () around combine values
   */
  cleanValue() {
    if (this.value && typeof this.value === 'string') {
      this.value = unquoteClauseValue(discloseByParenthesis(this.value.toString()));

      if (providers.includes(this.field.name)) {
        if (this.isFullWildcardOperator()) {
          this.value = splitThenJoinClauseValue(this.value, discloseByWildcard);
        } else if (this.isEndWidthWildcardOperator()) {
          this.value = splitThenJoinClauseValue(this.value, detachWildcard);
        }
      }

      if ([BooleanFieldEnum.IPC, BooleanFieldEnum.CPC].includes(this.field.name)) {
        if (this.isFullWildcardOperator()) {
          this.value = splitThenJoinClauseValue(this.value, detachWildcard);
        }
        this.valueArr = splitOr(this.value);
      }
    }
  }

  setOperatorByText(operatorText: string) {
    switch (operatorText) {
      case '>':
        this.operator = ClauseOperatorEnum.GREATER_THAN;
        break;
      case '<':
        this.operator = ClauseOperatorEnum.LESS_THAN;
        break;
      case '=':
        this.setEqualTextOperator();
        break;
      case '<>':
        this.setNotEqualTextOperator();
        break;
    }
  }

  equals(clause: Clause): boolean {
    return this.conjunction === clause.conjunction &&
      ((!this.field && !clause.field) || this.field.equals(clause.field)) &&
      this.operator === clause.operator && this.value === clause.value;
  }

  setValueByAuthority(authority: { id: string, name: string }) {
    this.value = authority.id;
  }

  getOperators(): Array<ClauseOperatorEnum> {
    if (this.field) {
      if (this.isNumberListField(this.field)) {
        return this.numberListOperators;
      }

      if (this.isEqualTextOperator(this.field.name)) {
        return this.equalsTextOperators;
      }

      if (providers.includes(this.field.name)) {
        return [ClauseOperatorEnum.EQUAL_TEXT, ClauseOperatorEnum.STARTS_WITH, ...this.textOperators];
      }

      if (this.isValuablePercentageField(this.field)) {
        return this.comparableOperators;
      }
    }

    return this.isComparable(this.field) ? this.comparableOperators : this.textOperators;
  }

  formatValue(prepareTagId = true): string {
    if (!this.value) {
      return;
    }

    const dateValue: any = this.value;

    if (this.field.type === FieldTypeEnum.DATE && dateValue && dateValue.year && dateValue.month && dateValue.day) {
      const month = dateValue.month.toString().padStart(2, '0');
      const day = dateValue.day.toString().padStart(2, '0');
      return `${dateValue.year}-${month}-${day}`;
    }

    if (providers.includes(this.field.name)) {
      const value = this.value.toString();
      if (this.isFullWildcardOperator()) {
        return encloseByParenthesis(enquoteClauseValue(splitThenJoinOr(value, encloseByWildcard), value));
      } else if (this.isEndWidthWildcardOperator()) {
        return encloseByParenthesis(enquoteClauseValue(splitThenJoinOr(value, appendWildcard), value));
      } else {
        return encloseByParenthesis(enquoteClauseValue(value, value), true);
      }
    }

    if ([BooleanFieldEnum.CPC, BooleanFieldEnum.IPC].includes(this.field.name) && this.isFullWildcardOperator()) {
      return encloseByParenthesis(this.getClassificationValue());
    }

    if (this.field.name === BooleanFieldEnum.TAG && prepareTagId) {
      const value = this.valueArr?.find(item => typeof item === 'object' && item.value.toLowerCase() === this.value.toString().toLowerCase());
      if (value && typeof value === 'object') {
        return value.id.toString();
      }
    }

    if (this.field.name === BooleanFieldEnum.LEGAL_STATUS) {
      const value = this.value.toString();
      return encloseByParenthesis(enquoteClauseValue(value, value), true);
    }

    if (this.field.name === BooleanFieldEnum.TECH_FIELDS) {
      const values = splitOr(this.value.toString());
      let techAreaValues = [];
      values.forEach(vl => {
        techAreaValues.push(...TECH_FIELDS.filter(tf => tf.area === vl).map(tf => tf.name));
      });

      if (techAreaValues.length) {
        const results = values.filter(value => !TECH_AREAS.find(o => o.name === value));
        results.push(joinOr(techAreaValues));
        return encloseByParenthesis(joinOr(results));
      }

      return encloseByParenthesis(this.value.toString());
    }

    if (this.isAuthorityField()) {
      const value = this.value.toString().toUpperCase();
      const authorityById = AUTHORITY_CODE_NODES.find(o => o.id.toUpperCase() === value);
      if (authorityById) {
        return authorityById.id;
      } else {
        const authorityByName = AUTHORITY_CODE_NODES.find(o => o.name.toUpperCase() === value);
        if (authorityByName) {
          return authorityByName.id;
        }
      }
    }

    if (this.isNumberListField(this.field)) {
      return encloseByParenthesis(this.value.toString());
    }

    if (this.isPlainTextField()) {
      // In case some inline operators like AND/OR have been used in query, make sure the
      // expression is in brackets and operators are uppercase - parser cannot handle them otherwise.
      const upperValue = toUpperCaseClauseConjunction(this.value.toString());
      return encloseByParenthesis(
        splitThenJoinClauseValue(upperValue, (val) => {
          return enquoteClauseTextValue(val, val);
        })
      );
    }

    return this.value.toString();
  }

  operatorForJsonQuery(): ClauseOperatorEnum {
    if ([ClauseOperatorEnum.STARTS_WITH, ClauseOperatorEnum.CONTAINS, ClauseOperatorEnum.EQUAL_TEXT].includes(this.operator)) {
      return ClauseOperatorEnum.EQUAL;
    } else if ([ClauseOperatorEnum.NOT_CONTAIN, ClauseOperatorEnum.NOT_EQUAL_TEXT].includes(this.operator)) {
      return ClauseOperatorEnum.NOT_EQUAL;
    }

    return this.operator;
  }

  getClassificationValue() {
    return joinOr(
      this.valueArr.map(x => typeof(x) === 'string' ? x : x.value)
        .filter(x => x?.trim()?.length > 0)
        .map(x => appendWildcard(x))
    ).toUpperCase();
  }

  private setEqualTextOperator() {
    switch (true) {
      case this.isEqualTextOperator(this.field.name):
        this.operator = ClauseOperatorEnum.EQUAL_TEXT;
        break;
      case this.isNumberListField(this.field):
        this.operator = ClauseOperatorEnum.CONTAINS;
        break;
      case providers.includes(this.field.name):
        const value = this.value?.toString();
        switch (true) {
          case value && isFullWildcardText(value):
            this.operator = ClauseOperatorEnum.CONTAINS;
            break;
          case value && isEndWidthWildcardText(value):
            this.operator = ClauseOperatorEnum.STARTS_WITH;
            break;
          default:
            this.operator = ClauseOperatorEnum.EQUAL_TEXT;
        }
        break;
      case !this.isComparable(this.field) && !this.isValuablePercentageField(this.field):
        this.operator = ClauseOperatorEnum.CONTAINS;
        break;
      default:
        this.operator = ClauseOperatorEnum.EQUAL;
        break;
    }
  }

  private setNotEqualTextOperator() {
    switch (true) {
      case this.isEqualTextOperator(this.field.name):
        this.operator = ClauseOperatorEnum.NOT_EQUAL_TEXT;
        break;
      case this.isNumberListField(this.field):
        this.operator = ClauseOperatorEnum.NOT_CONTAIN;
        break;
      case providers.includes(this.field.name):
        const value = this.value?.toString();
        if (value && isFullWildcardText(value)) {
          this.operator = ClauseOperatorEnum.NOT_CONTAIN;
        } else {
          this.operator = ClauseOperatorEnum.NOT_EQUAL_TEXT;
        }
        break;
      case !this.isComparable(this.field) && !this.isValuablePercentageField(this.field):
        this.operator = ClauseOperatorEnum.NOT_CONTAIN;
        break;
      default:
        this.operator = ClauseOperatorEnum.NOT_EQUAL_TEXT;
    }
  }

  private isEqualTextOperator(name: BooleanFieldEnum): boolean {
    return [
      BooleanFieldEnum.TECH_FIELDS, BooleanFieldEnum.ALSO_PUBLISHED_AS,
      BooleanFieldEnum.APPLICATION_NUMBERS, BooleanFieldEnum.LEGAL_STATUS, BooleanFieldEnum.TAG
    ].includes(name);
  }

  private isValueValid(): boolean {
    return !!(this.value);
  }

  isFieldValid(isPublicationSearch: boolean): boolean {
    let isValid = this.field && (!isPublicationSearch || !this.field.isExcludedFieldForPublicationSearch());
    this.errorMsg = isValid ? null : 'This field is only available for patent family search';
    if (isValid && this.field.name === BooleanFieldEnum.LEGAL_STATUS) {
      if (!isPublicationSearch && this.value?.toString().indexOf('pending') > -1) {
        this.errorMsg = 'The Pending status is not available for family search';
        isValid = false;
      }
    }
    return isValid;
  }

  private isValueEmpty(): boolean {
    return !(this.value);
  }

  private getDefaultOperator(field: Field): ClauseOperatorEnum {
    if (field) {
      if (this.isEqualTextOperator(field.name) || providers.includes(field.name)) {
        return ClauseOperatorEnum.EQUAL_TEXT;
      }
      if (this.isValuablePercentageField(field)) {
        return ClauseOperatorEnum.EQUAL;
      }
      if (this.isNumberListField(field)) {
        return ClauseOperatorEnum.CONTAINS;
      }
    }
    return this.isComparable(field) ? ClauseOperatorEnum.EQUAL : ClauseOperatorEnum.CONTAINS;
  }
}

const SYNTAX_HINT = `<br /><br />You can enter multiple terms in different ways: <ul>
            <li>Separated by spaces: your input is treated as separate words, and patents that present at least once every word will be retrieved as relevant. In order to consider the words together as a string please include them between quotation marks "".</li>
            <li>Separated by 'AND': the results must contain all the terms  but not in any specific order</li>
            <li>Separated by 'OR': the results must contain at least one of the terms, no specific order of the terms</li>
            <li>You can also combine 'AND' and 'OR' operators with brackets.</li>
            <li>It is also possible to search for words in defined proximity to one another.
              E.g. "car ventilation"~4 will return texts that contain the words "car" and "ventilation" in a proximity of 4 words,
              e.g. "... with the temperature inside the car regulated through the ventilation...."
            </li>
            <li>To prioritize documents using specific keywords in the ranking, you can use the boost operator,
              e.g. "ventilation"^10 will move up documents using the term "ventilation", as though the term occurred 10 times the actual number.
              This can therefore be understood as ranking the results by words with different weightings applied.
            </li>
            </ul>
            <br />
            Examples: <ul>
            <li>car seat ventilation</li>
            <li>car AND seat</li>
            <li>car OR automobile</li>
            <li>(car OR automobile) AND seat ventilation</li>
            <li>"car ventilation"~4 to search in proximity</li>
            <li>"ventilation"^10 to boost for certain keyword(s)</li></ul>`;


export const BOOLEAN_FIELDS = [
  new Field(BooleanFieldEnum.TEXT_FIELDS_GROUP, 'Text:', FieldTypeEnum.GROUP, '', null),

  new Field(BooleanFieldEnum.TITLE, 'Title', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in the patent title.' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),
  new Field(BooleanFieldEnum.ABSTRACT, 'Abstract', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in the patent abstract.' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),
  new Field(BooleanFieldEnum.CLAIMS, 'Claims', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in the patent claims. ' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),
  new Field(BooleanFieldEnum.DESCRIPTION, 'Description', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in the patent description. ' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),
  new Field(BooleanFieldEnum.TAC, 'Title, abstract, claims', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in the title, abstract or claims of the patent.' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),
  new Field(BooleanFieldEnum.TEXT, 'Full document text', FieldTypeEnum.TEXT,
    'Search for keyword(s) that is/are contained / not contained in any part of the patent text. ' + SYNTAX_HINT, BooleanFieldEnum.TEXT_FIELDS_GROUP),

  new Field(BooleanFieldEnum.DATES_GROUP, 'Dates:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.PRIORITY_DATE, 'Priority date', FieldTypeEnum.DATE,
    'Search for patent families that have the priority date before (">"), on ("="), after ("<") or ' +
    'not on ("<>") the date you specify in the input box. Please note: Format is YYYY-MM-DD', BooleanFieldEnum.DATES_GROUP),
  new Field(BooleanFieldEnum.PUBLICATION_DATE, 'Publication date', FieldTypeEnum.DATE,
    'Search for patent families that have a publication date before (">"), on ("="), after ("<") or ' +
    'not on ("<>") the date you specify in the input box. Please note: Format is YYYY-MM-DD', BooleanFieldEnum.DATES_GROUP),
  new Field(BooleanFieldEnum.APPLICATION_DATE, 'Application date', FieldTypeEnum.DATE,
    'Search for patent families that have a application date before (">"), on ("="), after ("<") or ' +
    'not on ("<>") the date you specify in the input box. Please note: Format is YYYY-MM-DD', BooleanFieldEnum.DATES_GROUP),
  new Field(BooleanFieldEnum.NUMBERS_GROUP, 'Numbers:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.APPLICATION_NUMBERS, 'Application number', FieldTypeEnum.TEXT,
    'Search for a patent number that equals or does not equal your input. Please note: Preferred ' +
    'format of input is without dashes or other separators e.g. EP07802230A', BooleanFieldEnum.NUMBERS_GROUP),
  new Field(BooleanFieldEnum.ALSO_PUBLISHED_AS, 'Publication number', FieldTypeEnum.TEXT,
    'Search for a patent number that equals or does not equal your input. Please note: Preferred ' +
    'format of input is without dashes or other separators e.g. EP2049363', BooleanFieldEnum.NUMBERS_GROUP),
  new Field(BooleanFieldEnum.PUBLICATION_KIND, 'Publication kind code', FieldTypeEnum.TEXT, `Limit search to certain document kinds only.
    Publication kind is usually one or two characters of document number, for example the publication kind of document EP2049363A1 is A1.`,
    BooleanFieldEnum.NUMBERS_GROUP),

  new Field(BooleanFieldEnum.ENTITIES_GROUP, 'Entities:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.APPLICANTS, 'Applicant', FieldTypeEnum.TEXT,
    'Search by filtering the applicant to equal a certain name or just contain that name (this is useful ' +
    'when looking at subsidies that have different endings to their names like \'SIEMENS\' and' +
    ' \'SIEMENS HEALTHCARE\'). You can also exclude Applicant(s) by selecting \'does not contain\'.', BooleanFieldEnum.ENTITIES_GROUP),
  new Field(BooleanFieldEnum.INVENTORS, 'Inventor', FieldTypeEnum.TEXT,
    'Search by filtering the inventor to equal a certain name or just contain that name . ' +
    'You can also exclude an inventor by selecting \'does not contain\'.', BooleanFieldEnum.ENTITIES_GROUP),

  new Field(BooleanFieldEnum.OWNERS, 'Owner', FieldTypeEnum.TEXT,
    'Search by filtering the assignee to equal a certain name or just contain that name . ' +
    'You can also exclude an owner by selecting \'does not contain\'.', BooleanFieldEnum.ENTITIES_GROUP),
  new Field(BooleanFieldEnum.OWNER_IDS, 'Owner subsidiaries', FieldTypeEnum.NUMBER_LIST,
      'Search by filtering the owner to equal a certain name.', BooleanFieldEnum.ENTITIES_GROUP),

  new Field(BooleanFieldEnum.CLASSIFICATION_GROUP, 'Classification:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.CPC, 'CPC code', FieldTypeEnum.TEXT,
    'Click the three dots on the right side of the input field to open up the hierarchy of CPC ' +
    '(Cooperative Patent Classification) codes. In the pop-up menu, you can expand the different classes with the ' +
    'blue \'+\' symbol. By clicking on the name of a class you can select it to be your query input. ' +
    'You can include or exclude classes with the \'contains\'/\'does not contain\' operators.', BooleanFieldEnum.CLASSIFICATION_GROUP),
  new Field(BooleanFieldEnum.IPC, 'IPC code', FieldTypeEnum.TEXT,
    'Click the three dots on the right side of the input field to open up the hierarchy of IPC (International ' +
    'Patent Classification) codes. In the pop-up menu, you can expand the different classes with the blue \'+\' ' +
    'symbol. By clicking on the name of a class you can select it to be your query input. You can include or ' +
    'exclude classes with the \'contains\'/\'does not contain\' operators.', BooleanFieldEnum.CLASSIFICATION_GROUP),
  new Field(BooleanFieldEnum.TECH_FIELDS, 'Technology area', FieldTypeEnum.TEXT,
    'Click the three dots on the right side of the input field to open up the hierarchy of the ' +
    'five main areas and by clicking on the plus in the pop-up menu, their 35 sub technical fields. Click ' +
    'on the name of a field to select it for your search. These categories are based on ' +
    'the categorization introduced by Schmoch (2008).', BooleanFieldEnum.CLASSIFICATION_GROUP),

  new Field(BooleanFieldEnum.AUTHORITY_GROUP, 'Authority:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.AUTHORITIES, 'Authority in patent family', FieldTypeEnum.TEXT,
    'Search for an authority amongst all documents that belong to a patent family. ' +
    'Either type in the country you are looking for or select it from the dropdown menu.', BooleanFieldEnum.AUTHORITY_GROUP),
  new Field(BooleanFieldEnum.PUBLICATION_AUTHORITY, 'Publication authority', FieldTypeEnum.TEXT,
    'Search for an authority amongst the publications of a patent family. ' +
    'Either type in the country you are looking for or select it from the dropdown menu.', BooleanFieldEnum.AUTHORITY_GROUP),
  new Field(BooleanFieldEnum.NUMBER_OF_AUTHORITIES, 'Number of authorities', FieldTypeEnum.NUMBER,
    'Search for patents that have been published in less than ("<"), equal to ("="), more than (">") or unlike to ("<>") a number of authorities ' +
    'that you specify.', BooleanFieldEnum.AUTHORITY_GROUP),

  new Field(BooleanFieldEnum.OTHERS_GROUP, 'Others:', FieldTypeEnum.GROUP, '', null),
  new Field(BooleanFieldEnum.LEGAL_STATUS, 'Legal status', FieldTypeEnum.TEXT,
    'Select one of the legal status from the dropdown menu to search for patent families.', BooleanFieldEnum.OTHERS_GROUP),
  new Field(BooleanFieldEnum.IMPACT, 'Patent value', FieldTypeEnum.TEXT,
    'Search for patents that have a patent value indicator lower ("<"), equal ("="), greater (">") ' +
    'or unlike ("<>") the selection you make from the dropdown  menu. The Patent value indicator estimates, ' +
    'where a patent lies in the distribution of patent values when compared with other patents form it\'s ' +
    'technology field. This takes into account many different aspects, e.g. amongst others in how many countries ' +
    'a patent was filed or how many citations it has. Please note that patent indicators just offer ' +
    'an indication of the respective aspect of a patent family.', BooleanFieldEnum.OTHERS_GROUP),
  new Field(BooleanFieldEnum.RISK, 'Legal risk', FieldTypeEnum.TEXT, 'Search for patents that have a ' +
    'legal risk value indicator lower ("<"), equal ("="), greater (">") or unlike ("<>") the selection you make from ' +
    'the dropdown  menu. The Legal risk indicator estimates, where a patent lies in the distribution of legal ' +
    'risks when compared with other patents form it\'s technology field. This takes into account many different ' +
    'aspects, e.g. amongst others in how many countries a patent was filed or how densely populated the IPC codes ' +
    'are that it has been filed in. Please note that patent indicators just offer an indication of the respective ' +
    'aspect of a patent family.', BooleanFieldEnum.OTHERS_GROUP),
  new Field(BooleanFieldEnum.CITATION_BACKWARD_COUNT, 'Number of references', FieldTypeEnum.NUMBER,
    'Search for patents that have  less ("<"), equal ("="), more (">") or unlike ("<>") ' +
    'the number of references (forward citations) that you specify.', BooleanFieldEnum.OTHERS_GROUP),
  new Field(BooleanFieldEnum.CITATION_FORWARD_COUNT, 'Number of citations', FieldTypeEnum.NUMBER,
    'Search for patents that have  less ("<"), equal ("="), more (">") or unlike ("<>") the number of ' +
    'citations (backward citations) that you specify.', BooleanFieldEnum.OTHERS_GROUP),
  new Field(BooleanFieldEnum.TAG, 'Custom tag', FieldTypeEnum.TEXT,
    'Select one of the custom tags from the dropdown menu to search for patent families.', BooleanFieldEnum.OTHERS_GROUP),
];


export const BOOLEAN_DEFAULT_CLAUSES = [
  new Clause(null, Field.getFieldByName(BooleanFieldEnum.TEXT), ClauseOperatorEnum.CONTAINS, '', 0, 0),
  new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.APPLICANTS), ClauseOperatorEnum.EQUAL_TEXT, '', 0, 0),
  new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.CPC), ClauseOperatorEnum.CONTAINS, '', 0, 0, []),
  new Clause(ClauseConjunctionEnum.AND, Field.getFieldByName(BooleanFieldEnum.IPC), ClauseOperatorEnum.CONTAINS, '', 0, 0, [])
];

export const EXCLUDED_BOOLEAN_FIELDS_FOR_PUBLICATION_SEARCH = [
  BooleanFieldEnum.PRIORITY_DATE, BooleanFieldEnum.NUMBER_OF_AUTHORITIES, BooleanFieldEnum.IMPACT,
  BooleanFieldEnum.RISK, BooleanFieldEnum.CITATION_BACKWARD_COUNT, BooleanFieldEnum.CITATION_FORWARD_COUNT
];
export const EXCLUDED_BOOLEAN_FIELDS_FOR_FAMILY_SEARCH = [
  BooleanFieldEnum.APPLICATION_DATE
];
export const EXCLUDED_FIELDS_MESSAGE: string = 'This field is only available for patent family search';
