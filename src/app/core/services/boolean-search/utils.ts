const AND_CONJUNCTION = ' AND ';
const OR_CONJUNCTION = ' OR ';
const CLAUSE_CONJUNCTIONS = [AND_CONJUNCTION, OR_CONJUNCTION];

// https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-query-string-query.html#_reserved_characters
const ES_RESERVED_KEYWORDS = ['+', '-', '=', '&&', '||', '>', '<', '!', '(', ')', '{', '}', '[', ']', '^', '"', '~', '*', '?', ':', '\\', '/'];
// Text contains some boosting, fuzziness, operators that should not be escaped for ES
const SKIP_TEXT_NORMALIZING_KEYWORDS = ['~', '^', '+', '-'];
const REMOVING_KEYWORDS = ['<', '>', '"'];

export const splitOr = (val: string): string[] => {
  return val.split(new RegExp(OR_CONJUNCTION, 'gi'));
}

export const splitAnd = (val: string): string[] => {
  return val.split(new RegExp(AND_CONJUNCTION, 'gi'));
}

export const joinOr = (parts: string[]): string => {
  return parts.join(OR_CONJUNCTION);
}

export const joinAnd = (parts: string[]): string => {
  return parts.join(AND_CONJUNCTION);
}

export const splitThenJoinOr = (val: string, callbackFunc: (val: string) => string): string => {
  return joinOr(splitOr(val).map((v) => callbackFunc(v)));
}

export const splitThenJoinAnd = (val: string, callbackFunc: (val: string) => string): string => {
  return joinAnd(splitAnd(val).map((v) => callbackFunc(v)));
}

export const splitThenJoinClauseValue = (val: string, callbackFunc: (val: string) => string): string => {
  return splitThenJoinAnd(val, (val) => {
    return splitThenJoinOr(val, callbackFunc);
  });
}

export const containClauseConjunction = (val: string): boolean => {
  return CLAUSE_CONJUNCTIONS.some((v) => val.includes(v));
}

export const toUpperCaseClauseConjunction = (val: string): string => {
  return CLAUSE_CONJUNCTIONS.reduce((acc, v) => acc.replace(v.toLowerCase(), v), val);
}

export const isEnclosedBy = (val: string, open: string, close: string): boolean => {
  return val.startsWith(open) && val.endsWith(close);
}

export const appendWildcard = (val: string): string => {
  if (!val || !val.length) {
    return val;
  }

  if (val.endsWith('*') || val.endsWith('*"') || val.endsWith('*)')) {
    return val;
  }
  return val + '*';
}

export const detachWildcard = (val: string): string => {
  if (val.endsWith('*')) {
    return val.slice(0, -1);
  }
  return val;
}

export const encloseBy = (val: string, open: string, close: string, force: boolean = false): string => {
  if (!val || !val.length) {
    return val;
  }

  if (force || (!val.startsWith(open) && !val.startsWith('"' + open) && !val.startsWith('(' + open))) {
    val = open + val;
  }
  if (force || (!val.endsWith(close) && !val.endsWith(close + '"') && !val.endsWith(close + ')'))) {
    val = val + close;
  }
  return val;
}

export const encloseByParenthesis = (val: string, force: boolean = false): string => {
  return force || containClauseConjunction(val) || containSpecialCharacter(val) ? encloseBy(val, '(', ')', true) : val;
}

export const encloseByWildcard = (val: string): string => {
  return encloseBy(val, '*', '*');
}

export const encloseByQuote = (val: string): string => {
  return encloseBy(val, '"', '"');
}

export const discloseBy = (val: string, open: string, close: string): string => {
  if (isEnclosedBy(val, open, close)) {
    return val.slice(1, -1);
  }
  return val;
}

export const discloseByParenthesis = (val: string): string => {
  return discloseBy(val, '(', ')');
}

export const discloseByWildcard = (val: string): string => {
  return discloseBy(val, '*', '*');
}

export const containSpecialCharacter = (val: string): boolean => {
  return ES_RESERVED_KEYWORDS.some((c) => val.includes(c));
}

export const removeSpecialCharacters = (val: string): string => {
  let cleanedVal = val;
  for (const kw of REMOVING_KEYWORDS) {
    cleanedVal = cleanedVal.replace(new RegExp('\\' + kw, 'g'), '');
  }
  return cleanedVal;
}

export const normalizeNormalValue = (val: string): string => {
  return encloseByQuote(removeSpecialCharacters(val));
}

export const normalizeText = (val: string): string => {
  if (SKIP_TEXT_NORMALIZING_KEYWORDS.some((kw) => val.includes(kw))) {
    return val;
  }

  return normalizeNormalValue(val);
}

export const enquoteClauseValue = (val: string, originalVal: string = null): string => {
  const result = !originalVal || containSpecialCharacter(originalVal) ? splitThenJoinOr(val, normalizeNormalValue) : val;
  return result.toUpperCase();
}

export const enquoteClauseTextValue = (val: string, originalVal: string = null): string => {
  return !originalVal || containSpecialCharacter(originalVal) ? splitThenJoinOr(val, normalizeText) : val;
}

export const unquoteClauseValue = (val: string): string => {
  return splitThenJoinClauseValue(val, (v) => {
    if (isEnclosedBy(v, '("', '")')) {
      return encloseByParenthesis(v.slice(2, -2));
    }
    if (isEnclosedBy(v, '"', '"')) {
      return v.slice(1, -1);
    }
    return v;
  });
}

export const isFullWildcardText = (val: string): boolean => {
  return isEnclosedBy(val, '("*', '*")') || isEnclosedBy(val, '(*', '*)') ||
    isEnclosedBy(val, '*', '*') || isEnclosedBy(val, '"*', '*"');
}

export const isEndWidthWildcardText = (val: string): boolean => {
  return val.endsWith('*') || val.endsWith('*)') || val.endsWith('*")');
}
