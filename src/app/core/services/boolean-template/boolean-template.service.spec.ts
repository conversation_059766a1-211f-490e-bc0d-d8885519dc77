import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';

import { BooleanTemplateService } from './boolean-template.service';

describe('BooleanTemplateService', () => {
  let service: BooleanTemplateService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(BooleanTemplateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
