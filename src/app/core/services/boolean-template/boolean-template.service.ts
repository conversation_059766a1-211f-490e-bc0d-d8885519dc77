import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { BooleanTemplate } from './type';

@Injectable({
  providedIn: 'root'
})
export class BooleanTemplateService {

  private _savedTemplate: BooleanTemplate = {title: '', content: '', type: ''};

  get savedTemplate(): BooleanTemplate {
    return this._savedTemplate;
  }
  set savedTemplate(value: BooleanTemplate) {
    this._savedTemplate = value;
  }

  constructor(private apiService: ApiService) { }

  public save(payload: BooleanTemplate, id = null) {
    if (!id) {
      return this.apiService.post('web/boolean_templates', payload);
    }
    return this.apiService.patch(`web/boolean_templates/${id}`, payload);
  }

  public listTemplate() {
    return this.apiService.get('web/boolean_templates');
  }

  public removeTemplate(id: number) {
    return this.apiService.delete(`web/boolean_templates/${id}`);
  }

  resetTemplate(): void {
    this._savedTemplate = {title: '', content: '', type: ''};
  }

  clearStoredData(): void {
    this.resetTemplate();
  }
}
