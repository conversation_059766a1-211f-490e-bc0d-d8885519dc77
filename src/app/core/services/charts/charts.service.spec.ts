import { TestBed } from '@angular/core/testing';

import { ChartsService } from './charts.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('ChartsService', () => {

  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers: [ChartsService]
  }));

  it('should be created', () => {
    const service: ChartsService = TestBed.inject(ChartsService);
    expect(service).toBeTruthy();
  });
});
