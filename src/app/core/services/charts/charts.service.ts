import { Injectable } from '@angular/core';

import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { ApiService } from '../api/api.service';
import * as Highcharts from 'highcharts';
import exporting from 'highcharts/modules/exporting';
import add3d from 'highcharts/highcharts-3d';
import { chartsConfig } from '@shared/charts/config';


@Injectable({
  providedIn: 'root'
})
export class ChartsService {
  private chartsSubject = new BehaviorSubject<Record<string, any>>({});
  public charts$ = this.chartsSubject.asObservable();

  constructor(
    private apiService: ApiService
  ) {
    // Set up chart export
    exporting(Highcharts);
    add3d(Highcharts);
    Highcharts.getOptions().exporting.buttons.contextButton.menuItems = [
      'downloadPNG', 'downloadJPEG', 'downloadPDF', 'downloadSVG', 'separator', 'printChart'
    ];
    Highcharts.getOptions().plotOptions.series = {animation: false};
  }

  get charts() {
    return this.chartsSubject.getValue();
  }

  setCharts(charts: Record<string, any>): void {
    this.chartsSubject.next(charts);
  }

  calculate(payload: Object, search_hash: string): Observable<any> {
    return this.apiService.calculateCharts(`search/charts/${search_hash}`, payload)
      .pipe(
        tap(({charts}) => this.chartsSubject.next(charts)),
      );
  }

  // This method doesn't update charts observable.
  // this is useful in case of presenting the same chart more than once with different data on the same screen.
  calculateToSingleChart(payload: Object, search_hash: string): Observable<any> {
    return this.apiService.calculateCharts(`search/charts/${search_hash}`, payload);
  }

  resetCharts(): void {
    this.chartsSubject.next({});
  }

  classificationCodesColor(code: string): string {
    switch (code.charAt(0).toUpperCase()) {
      case 'A':
        return chartsConfig.colorPalette[0];
      case 'B':
        return chartsConfig.colorPalette[1];
      case 'C':
        return chartsConfig.colorPalette[2];
      case 'D':
        return chartsConfig.colorPalette[3];
      case 'E':
        return chartsConfig.colorPalette[4];
      case 'F':
        return chartsConfig.colorPalette[5];
      case 'G':
        return chartsConfig.colorPalette[6];
      case 'H':
        return chartsConfig.colorPalette[7];
      case 'Y':
      default:
        return chartsConfig.colorPalette[8];
    }
  }

  greenCategoriesColor(category: string, index: number = -1): string {
    if (index !== -1) {
      return chartsConfig.colorGreenPalette[index];
    }

    const categories = [
      'environmental management',
      'water-related adaptation technologies',
      'climate change mitigation technologies related to energy generation, transmission of distribution',
      'capture, storage, sequestration or disposal of greenhouse gases',
      'climate change mitigation technologies related to transportation',
      'climate change mitigation technologies related to buildings',
      'climate change mitigation technologies related to wastewater treatment or waste management',
      'climate change mitigation technologies in the production or processing of goods'
    ];

    const categoryIndex = categories.indexOf(category.toLowerCase());

    if (categoryIndex !== -1) {
      return chartsConfig.colorGreenPalette[categoryIndex];
    }

    return chartsConfig.colorGreenPalette[categories.length + 1];
  }
}
