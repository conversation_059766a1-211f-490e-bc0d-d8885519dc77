import { tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

import { ApiService } from '../api/api.service';
import { CitationSearchRequest } from './types';
import { Npl } from '@core/models';
import { PatentViewReferralType } from '@core/services/patent/types';
import { SearchQueryParams } from '../semantic-search';
import { PatentListScopeEnum, PublicationService } from '@core/services';

@Injectable({
  providedIn: 'root'
})
export class CitationSearchService {
  public linkData = {title: 'Citation Search', referral: PatentViewReferralType.REFERRAL_CITATION, backPatentSearch: true};

  private documentsSubject = new BehaviorSubject<Array<Object>>([]);
  public documents = this.documentsSubject.asObservable();

  private nplDocumentsSubject = new BehaviorSubject<Array<Npl>>([]);
  public nplDocuments = this.nplDocumentsSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private publicationService: PublicationService,
  ) {
  }

  public search(payload: CitationSearchRequest, queryParams: SearchQueryParams): Observable<any> {
    return this.apiService.post('search/citation_flat', payload, queryParams)
      .pipe(
        tap(({data}) => {
          if (payload.search_type === PatentListScopeEnum.PUBLICATION) {
            data['documents'] = this.publicationService.publicationsToDocuments(data['publications']);
          }
          this.documentsSubject.next(data.documents);
        })
      );
  }

  public getCachedSearch(hash: string, payload?: CitationSearchRequest, queryParams?: SearchQueryParams): Observable<any> {
    return this.apiService.post(`search/hash/${hash}`, payload, queryParams);
  }

  public searchNpls(searchHash: string, queryParams): Observable<any> {
    return this.apiService.get('search/node/' + searchHash, queryParams)
      .pipe(
        tap(({data}) => {
          this.nplDocumentsSubject.next(data.nodes);
        })
      );
  }

  public getDocuments(): Array<Object> {
    return this.documentsSubject.value;
  }

  public resetDocuments(): void {
    this.documentsSubject.next([]);
  }

  public setDocuments(value): void {
    this.documentsSubject.next(value);
  }

  public getNplDocuments(): Array<Object> {
    return this.nplDocumentsSubject.value;
  }

  public resetNplDocuments(): void {
    this.nplDocumentsSubject.next([]);
  }

  public export(hash: string, body: any, queryParams: any): Observable<any> {
    const citation_params = ['citation_category', 'citation_direction', 'citation_phase', 'citation_pl_npl']
    if(body['search_filters'] && Object.keys(body['search_filters']).some(r=> citation_params.includes(r))){
      Object.keys(body['search_filters'] ).forEach(key => {
        if (citation_params.includes(key)) {
          delete body['search_filters'][key];
        }
      });
    }
    return this.apiService.asyncExport(`web/export/${hash}`, body, queryParams);
  }

  clearStoredData(): void {
    this.resetDocuments();
    this.resetNplDocuments();
  }
}
