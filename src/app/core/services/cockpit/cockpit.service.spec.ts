import { TestBed } from '@angular/core/testing';

import { CockpitService } from './cockpit.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('CockpitService', () => {
  let service: CockpitService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(CockpitService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  it('should load correct data', () => {
    service.getSingleton('test').subscribe(res => {
      expect(res).toEqual('<p>This is a my data<\/p>');
    });
    httpMock.expectOne(service.BASE_URL + '/content/item/test')
      .flush({
        'Text': '<p>This is a my data<\/p>',
        '_mby': '5fa5223ff8c75660f34d98b2',
        '_by': '5fa5223ff8c75660f34d98b2'
      });
  });

  it('should fail on network error', () => {
    service.getSingleton('test').subscribe(res => {
      expect(res).toEqual(service.ERROR_MESSAGE);
    });
    httpMock.expectOne(service.BASE_URL + '/content/item/test')
      .error(new ErrorEvent('Network error'));
  });

  it('should fail on HTTP error', () => {
    service.getSingleton('test').subscribe(res => {
      expect(res).toEqual(service.ERROR_MESSAGE);
    });
    httpMock.expectOne(service.BASE_URL + '/content/item/test')
      .flush('Invalid token', {status: 401, statusText: 'Unauthorized'});
  });
});
