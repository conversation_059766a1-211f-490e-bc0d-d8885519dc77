import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SettingsService } from '../settings/settings.service';
import { FAQ, News } from './types';

/**
 * Service to load pages from Cockpit headless CMS.
 * See https://getcockpit.com/documentation/getting-started for documentation.
 */
@Injectable({
  providedIn: 'root'
})
export class CockpitService {
  readonly ERROR_MESSAGE: string = 'Could not display this page. Please try again later.';
  BASE_URL: string;
  TOKEN: string;

  constructor(
    private http: HttpClient,
    private settingsService: SettingsService
  ) {
    this.BASE_URL = this.settingsService.settings.cockpitUrl;
    this.TOKEN = this.settingsService.settings.cockpitToken;
  }

  public getSingleton(name: string): Observable<string> {
    const headers = new HttpHeaders().set('api-key', this.TOKEN);
    return this.http
      .get(`${this.BASE_URL}/content/item/${name}`, {'headers': headers})
      .pipe(catchError(err => this.handleError(err)), map(data => data['Text']));
  }

  public getObject(name: string): Observable<any> {
    const headers = new HttpHeaders().set('api-key', this.TOKEN);
    return this.http
      .get(`${this.BASE_URL}/content/item/${name}`, {'headers': headers})
      .pipe(catchError(err => this.handleError(err)), map(data => data['Data']));
  }

  public getFAQS(params?: {}): Observable<Array<FAQ>> {
    const headers = new HttpHeaders().set('api-key', this.TOKEN);
    return this.http
      .get(`${this.BASE_URL}/content/items/FAQ`, {'headers': headers, 'params': params})
      .pipe(catchError(err => this.handleError(err)), map(data => data));
  }

  public getNews(params?: {}): Observable<Array<News>> {
    const headers = new HttpHeaders().set('api-key', this.TOKEN);
    return this.http
      .get(`${this.BASE_URL}/content/items/NEWS`, {'headers': headers, 'params': params})
      .pipe(catchError(err => this.handleError(err)), map(data => data["data"]));
  }

  private handleError(error: any): Observable<any> {
    console.error('Failed to load data from Cockpit', error);
    return of({Text: this.ERROR_MESSAGE});
  }
}
