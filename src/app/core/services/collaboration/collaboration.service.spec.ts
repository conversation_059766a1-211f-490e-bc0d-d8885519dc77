import { TestBed } from '@angular/core/testing';

import { CollaborationService } from './collaboration.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';

describe('CollaborationService', () => {
  let service: CollaborationService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    });
    service = TestBed.inject(CollaborationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
