import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { ApiService } from '../api/api.service';
import { catchError, map } from 'rxjs/operators';
import {
  Collaboration,
  CollaborationResourceTypeEnum,
  CollaborationsResponse,
  CollaborationStatusEnum,
  CollaboratorTypeEnum,
  PatentResource,
  TeamUser,
  UserGroup
} from '@core/models';
import { PaginationMetadata } from '../semantic-search';
import { Collection, UserService } from '@core/services';
import { SafeHtmlPipe, TagParserPipe, TruncatePipe, UserTitlePipe } from '@core/pipes';

@Injectable({
  providedIn: 'root'
})
export class CollaborationService {
  public collaborationsPage: PaginationMetadata;
  private notificationsSubject = new BehaviorSubject<Array<Collaboration>>(null);
  public notifications$ = this.notificationsSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private userService: UserService,
    private truncatePipe: TruncatePipe,
    private userTitlePipe: UserTitlePipe,
    private tagParserPipe: TagParserPipe,
    private safeHtmlPipe: SafeHtmlPipe,
  ) {
  }

  get notifications(): Array<Collaboration> {
    return this.notificationsSubject.getValue();
  }

  set notifications(collaborations: Array<Collaboration>) {
    this.notificationsSubject.next(collaborations);
  }

  purgeCollaborations() {
    this.notifications = [];
  }

  create(payload): Observable<any> {
    return this.apiService.post('web/collaborations', payload);
  }

  getCollaboration(resourceId: number, resourceType: CollaborationResourceTypeEnum): Observable<any> {
    return this.apiService.get(`web/collaborations/collaborators/${resourceId}`, {resource_type: resourceType})
      .pipe(map(({data}) => data.collaborators));
  }

  getUsers(resourceIds, resourceType: CollaborationResourceTypeEnum): Observable<Array<TeamUser>> {
    const payload = {
      resource_id: resourceIds.join(','),
      resource_type: resourceType,
      collaborator_type: CollaboratorTypeEnum.USER
    };
    return this.apiService.get('web/collaborations/collaborators', payload)
      .pipe(map(({data}) => data.collaborators));
  }

  getGroups(resourceIds, resourceType: CollaborationResourceTypeEnum): Observable<Array<UserGroup>> {
    const payload = {
      resource_id: resourceIds.join(','),
      resource_type: resourceType,
      collaborator_type: CollaboratorTypeEnum.GROUP
    };
    return this.apiService.get('web/collaborations/collaborators', payload)
      .pipe(map(({data}) => data.collaborators));
  }

  getSharedWithMe(payload): Observable<CollaborationsResponse> {
    return this.apiService.get(`web/collaborations`, payload).pipe(map(resp => resp.data));
  }

  getTeamActivities(payload, redirectTo403Page = true): Observable<CollaborationsResponse> {
    return this.apiService.get(`web/collaborations/team_activities`, payload, redirectTo403Page)
      .pipe(map(resp => resp.data));
  }

  markAsRead(resourceId: number, resourceType: CollaborationResourceTypeEnum): Observable<any> {
    return this.apiService.patch('web/collaborations', {
      resource_id: resourceId,
      resource_type: resourceType,
    }).pipe(
      catchError(() => of(null))
    );
  }

  isSharedCollection(collectionId, collaboration: Collaboration): boolean {
    return collaboration.status !== CollaborationStatusEnum.UNSHARED &&
      collaboration.resource_id === collectionId && this.isCollectionType(collaboration);
  }

  getCollaborationTitle(c: Collaboration, isShorten = false) {
    const userTitle = c.user ? `<span class="highlight">${this.userTitlePipe.transform(c.user)}</span>` : 'you';
    const ownerTitle = c.owner?.id !== this.userService.getUser()?.profile?.id ? `<span class="highlight">${this.userTitlePipe.transform(c.owner)}</span>` : 'you';
    switch (true) {
      case this.isPatentType(c):
        const patent = c.resource as PatentResource;
        const title = isShorten ? this.truncatePipe.transform(patent.title, 30, true) : patent.title;
        const highlightDocument = isShorten ? `<span class="highlight">${patent.publication_number}-${title}</span>` : `${patent.publication_number}-${title}`;
        return `<span class="text-capitalize">${ownerTitle}</span> shared the document "${highlightDocument}" with ${userTitle}`;
      case this.isCollectionType(c):
        const col = c.resource as Collection;

        if (col.monitor_profile?.name) {
          const profileName = isShorten ? `<span class="highlight">${this.truncatePipe.transform(col.monitor_profile?.name.toString(), 30, true)}</span>`
            : col.monitor_profile?.name;
          const countPatents = `${col.results_count} patents`;
          return `<span class="text-capitalize">${ownerTitle}</span> shared ${countPatents} in monitoring profile "${profileName}"`;
        } else {
          const collectionName = isShorten ? this.truncatePipe.transform(col.name, 30, true) : col.name;
          const highlightList = isShorten ? `<span class="highlight">${collectionName}</span>` : collectionName;
          return `<span class="text-capitalize">${ownerTitle}</span> shared the list "${highlightList}" with ${userTitle}`;
        }
      case this.isCopiedMonitorType(c):
        let name = (c.resource as Collection).monitor_profile?.name?.toString();
        name = isShorten ? `<span class="highlight">${this.truncatePipe.transform(name, 30, true)}</span>` : name;
        return `<span class="text-capitalize">${ownerTitle}</span> sent a monitoring profile "${name}" to ${userTitle}`;
      case this.isLandscapeType(c):
        const profileName = c.resource['landscape_profile']?.name;
        const nameLandscape = isShorten ? `<span class="highlight">${this.truncatePipe.transform(profileName, 30, true)}</span>` : profileName;
        return `<span class="text-capitalize">${ownerTitle}</span> shared the Landscape "${nameLandscape}" with ${userTitle}`;
      case this.isAnnotationCommentType(c):
      case this.isAnnotationTagType(c):
      case this.isReplyOnTaggedCommentType(c):
        let parsedComment = this.tagParserPipe.transform(c.resource['annotation_comment']?.comment, null, true);
        parsedComment = this.safeHtmlPipe.transform(parsedComment, null, null, {allowedTags: []});
        const comment = isShorten ? `<span class="highlight" title="${parsedComment}">${this.truncatePipe.transform(parsedComment, 30, true)}</span>` : parsedComment;
        let commentTitle = '';
        switch (true) {
          case this.isAnnotationCommentType(c):
            if (c.resource['annotation_comment']?.parent_comment_id) {
              commentTitle = `<span class="text-capitalize">${userTitle}</span> replied "${comment}" on ` + (c.user ? `${ownerTitle}'s comment` : 'your Patent comment');
            } else {
              commentTitle = `<span class="text-capitalize">${ownerTitle}</span> commented "${comment}" on Patent shared with ${userTitle}`;
            }
            break;
          case this.isReplyOnTaggedCommentType(c):
            commentTitle = `<span class="text-capitalize">${ownerTitle}</span> replied on comment "${comment}" that ${userTitle} was tagged`;
            break;
          case this.isAnnotationTagType(c):
            commentTitle = `<span class="text-capitalize">${ownerTitle}</span> tagged ${userTitle} in a comment "${comment}"`;
            break;
        }
        return commentTitle;
    }
  }

  isCollaborationUnread(c: Collaboration): boolean {
    return c.status === CollaborationStatusEnum.NEW;
  }

  isPatentType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.PATENT;
  }

  isCollectionType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.COLLECTION;
  }

  isCopiedMonitorType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.COPIED_MONITOR;
  }

  isLandscapeType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.LANDSCAPE;
  }

  isAnnotationCommentType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.PATENT_COMMENT;
  }

  isReplyOnTaggedCommentType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.REPLY_ON_TAGGED_COMMENT;
  }

  isAnnotationTagType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.PATENT_TAG;
  }

  clearStoredData(): void {
    this.notifications = [];
  }
}
