import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CollectionStoreService } from '@core/store';
import { PatentViewReferralType } from '@core/services/patent/types';
import { AggregationRequest, Collection, CollectionsResponse, Folder, FoldersResponse } from './types';
import { ApiService } from '../api/api.service';
import { ConfirmationDialogService } from '../confirmation-dialog/confirmation-dialog.service';
import { PatentResultsResponse } from '@core/services';

@Injectable({
  providedIn: 'root'
})
export class CollectionService {

  /**
   * additional data for singleton patent viewer
   */
  linkData = {
    title: 'List',
    referral: PatentViewReferralType.REFERRAL_COLLECTIONS,
    previousUrl: 'collections',
    backPatentSearch: true
  };

  private collectionByShareCodeSubject = new BehaviorSubject<Collection>(null);
  readonly collectionByShareCode = this.collectionByShareCodeSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private confirmationDialogService: ConfirmationDialogService,
    private collectionsStore: CollectionStoreService
  ) {
  }

  getCollections(payload, redirectTo403Page = true): Observable<CollectionsResponse> {
    return this.apiService.get('web/result_collections', payload, redirectTo403Page)
      .pipe(map(resp => resp.data));
  }

  getSharedCollections(payload, redirectTo403Page = true): Observable<CollectionsResponse> {
    return this.apiService.get('web/result_collections/shared', payload, redirectTo403Page).pipe(map(resp => resp.data));
  }

  getMonitorResultCollections(payload, profileId: number, redirectTo403Page = true): Observable<any> {
    return this.apiService.get(`web/monitor/profile${profileId ? '/' + profileId : ''}/collections`, payload, redirectTo403Page)
      .pipe(map(resp => resp.data));
  }

  createCollection(payload: Collection): Observable<Collection> {
    return this.apiService.post('web/result_collections', payload).pipe(map(resp => resp.data));
  }

  getCollection(id: number, params?: {}): Observable<Collection> {
    return this.apiService.get(`web/result_collections/${id}`, params).pipe(map(resp => resp.data));
  }

  getCollectionByShareCode(shareCode: string): Observable<any> {
    return this.apiService.get(`web/result_collections/shared/${shareCode}`);
  }

  updateCollection(id: number, payload: Collection): Observable<Collection> {
    return this.apiService.patch(`web/result_collections/${id}`, payload).pipe(map(resp => resp.data));
  }

  cloneCollection(id: number, payload: Collection): Observable<Collection> {
    return this.apiService.post(`web/result_collections/${id}/clone`, payload).pipe(map(resp => resp.data));
  }

  deleteCollection(id: number) {
    return this.apiService.delete(`web/result_collections/${id}`);
  }

  getCollectionDocuments(id: number, params?: {}): Observable<PatentResultsResponse> {
    return this.apiService.get(`web/result_collections/${id}/documents`, params)
      .pipe(map(resp => resp.data));
  }

  updateDocuments(id: number, payload) {
    return this.apiService.patch(`web/result_collections/${id}/documents`, payload);
  }

  getFolders(payload): Observable<FoldersResponse> {
    return this.apiService.get('web/result_collections/folders', payload)
      .pipe(map(resp => resp.data));
  }

  getShareFolders(payload, redirectTo403Page = true): Observable<FoldersResponse> {
    return this.apiService.get('web/result_collections/folders/shared', payload, redirectTo403Page).pipe(map(resp => resp.data));
  }

  getFolder(id: number): Observable<Folder> {
    return this.apiService.get(`web/result_collections/folders/${id}`).pipe(map(resp => resp.data));
  }

  createFolder(payload: Folder): Observable<Folder> {
    return this.apiService.post('web/result_collections/folders', payload).pipe(map(resp => resp.data));
  }

  updateFolder(id: number, payload: {}): Observable<Folder> {
    return this.apiService.patch(`web/result_collections/folders/${id}`, payload).pipe(map(resp => resp.data));
  }

  deleteFolder(id: number) {
    return this.apiService.delete(`web/result_collections/folders/${id}`);
  }

  aggregateCollections(payload: AggregationRequest): Observable<Collection> {
    return this.apiService.post('web/result_collections/aggregate', payload).pipe(map(resp => resp.data));
  }

  /**
   * helper function for export function in patent table component
   */
  getDocuments(): Object[] {
    return this.collectionsStore.collectionDocuments;
  }

  /**
   * helper function for export function in patent table component
   */
  export(hash: string, ids: any, queryParams: any): Observable<any> {
    return this.apiService.asyncExport(`web/export/${hash}`, ids, queryParams);
  }

  alertAfterCopyShareLink() {
    this.confirmationDialogService.alert('Sharing Link',
      'The share link is copied to the clipboard.');
  }

  setCollectionByShareCode(data: Collection) {
    this.collectionByShareCodeSubject.next(data);
  }

  collectionConversion(id: number, operation: string, profile?: {
    profile_name: string,
    profile_category?: string
  }): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.post(`web/result_collections/convert/${id}`, {operation, ...profile}).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  clearStoredData(): void {
    this.collectionByShareCodeSubject.next(null);
  }

  sharedFolderMessage(folder: Folder): string{
    return `This list is shared via the folder <b>${folder.name}</b> . To make changes, open the folder’s sharing settings.`;
  }

  isCollectionSharedViaFolder(collection: Collection){
    if(collection?.folder_collaboration && (collection.folder_collaboration.users.length>0 || collection.folder_collaboration.groups.length>0)){
        return true;
    }
    return false
  }
}
