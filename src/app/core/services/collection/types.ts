import {
  Collaboration,
  MonitorProfile,
  MonitorRunStatusEnum,
  MonitorRunTypeEnum,
  TeamUser,
  UserGroup
} from '@core/models';
import { PaginationMetadata } from '../semantic-search';
import { TagModel } from '@core/models/tag.model';

export enum CollectionSourceTypeEnum {
  BOOLEAN_SEARCH_HISTORY = 'BOOLEAN_SEARCH_HISTORY',
  SEMANTIC_SEARCH_HISTORY = 'SEMANTIC_SEARCH_HISTORY',
  CITATION_SEARCH_HISTORY = 'CITATION_SEARCH_HISTORY',
  MONITOR_RUN = 'MONITOR_RUN',
  TAG = 'TAG'
}

export enum PatentListScopeEnum {
  FAMILY = 'FAMILY',
  PUBLICATION = 'PUBLICATION',
}

export interface CollectionSource {
  id: number;
  name: string;
  collection_id: number;
  created_at?: string;
  updated_at?: string;
  monitor_run_id?: number;
  search_history_id?: number;
  documents_count?: number;
  is_remove_event?: boolean;
  resource?: any;
  user_id?: number;
  user?: TeamUser;
  monitor_run?: {
    id: number;
    can_read: boolean;
    legal_status_active: boolean;
    name: string;
    profile_id: number;
    profile_name: string;
    run_type: MonitorRunTypeEnum;
    status: MonitorRunStatusEnum;
    user_id: string;
  }
}

export interface Collection {
  id?: number;
  name: string;
  description?: string;
  expires_at?: string;
  search_hash?: string;
  monitor_run_id?: number;
  search_history_id?: number;
  folder_id?: number;
  created_at?: string;
  updated_at?: string;
  results_count?: number;
  user_id?: number;
  share_code?: string;
  type?: string;
  collection_type: PatentListScopeEnum;
  document_ids?: Array<string>;
  publication_numbers?: Array<string>;
  users?: Array<TeamUser>;
  groups?: Array<UserGroup>;
  monitor_profile?: MonitorProfile;
  tag?: TagModel;
  collection_sources?: CollectionSource[];
  collaboration?: Collaboration;
  folder_collaboration?: {users: TeamUser[] , groups: UserGroup[]};
  permissions?: string[];
}

export interface Folder {
  id?: number;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  collections_count?: number;
  collaborators: {users: TeamUser[], groups: UserGroup[]};
  user_id: number;
  user: TeamUser;
  collaboration?: Collaboration;
  parent_id?: number;
  children?: Array<Folder>;
  permissions?: string[];
}

export interface CollectionsResponse {
  result_collections: Array<Collection>;
  page: PaginationMetadata;
}

export interface FoldersResponse {
  folders: Array<Folder>;
  page: PaginationMetadata;
}

export interface AggregationRequest {
  folder_id: number;
  collection_ids: Array<number>;
  description: string;
  name: string;
  operation: string;
}

export interface SortBySimilarityParam {
  text_weighting: number;
  publications: string;
  term: string;
  language: { code: string, name: string };
}

export enum CollectionViewTypeEnum {
  DOCUMENTS_VIEW = 'documents',
  COLLECTIONS_VIEW = 'collections',
  OPENED_DOCUMENTS_VIEW = 'opened',
  ANNOTATED_VIEW = 'annotated',
  TAGGED_VIEW = 'tagged',
}
export interface SourceFilterOption {
  name: string;
  value: string;
  selected?: boolean;
}

