import { TestBed } from '@angular/core/testing';

import { CorporateEntitiesService } from './corporate-entities.service';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('CorporateEntitiesService', () => {
  let service: CorporateEntitiesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(CorporateEntitiesService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
