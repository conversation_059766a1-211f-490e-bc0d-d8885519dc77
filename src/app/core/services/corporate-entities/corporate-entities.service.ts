import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { map, Observable } from 'rxjs';
import { CorporateEntity } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class CorporateEntitiesService {

  constructor(
    private apiService: ApiService
  ) {
  }

  getCorporateByName(payload: { name: string, root_only: boolean }): Observable<Array<CorporateEntity>> {
    return this.apiService.post(`search/corporate_entities`, payload)
      .pipe(
        map((resp) => resp.data.corporate_entities as Array<CorporateEntity>)
      );
  }

  getChildren(id): Observable<Array<CorporateEntity>> {
    return this.apiService.get(`search/corporate_entities/${id}/children`)
      .pipe(
        map((resp) => resp.data.children as Array<CorporateEntity>)
      );
  }
}
