import { Injectable } from '@angular/core';
import { ApiService, BooleanSearchRequest, PaginationMetadata } from '@core/services';
import { DocumentsBodyRequest, DocumentsQueryParams } from '@core/services/document/types';
import { Observable } from 'rxjs';
import { DocumentHighlight, Patent } from '@core/models';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DocumentService {

  constructor(
    private apiService: ApiService
  ) {
  }

  getDocument(id: number): Observable<Patent> {
    return this.apiService.get(`search/document/${id}`).pipe(map(({data}) => data));
  }

  getDocuments(payload: DocumentsBodyRequest, query?: DocumentsQueryParams):
    Observable<{ documents: Patent[], publications: Patent[], page: PaginationMetadata, search_info: {new_search: boolean, search_hash: string} }> {
    return this.apiService.post('web/search/document', payload, query).pipe(map(({data}) => data));
  }

  getHighlight(payload): Observable<DocumentHighlight> {
    return this.apiService.post(`search/boolean/highlights`, payload)
      .pipe(map(({data}) => data.highlight as DocumentHighlight));
  }
}
