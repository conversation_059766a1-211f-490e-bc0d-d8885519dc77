export interface DocumentsQueryParams {
  show_analytics?: 1 | 0;
  show_general?: 1 | 0;
  show_bibliographic?: 1 | 0;
  show_fulltext?: 1 | 0;
  show_preprocessed?: 1 | 0;
  page?: number;
  page_size?: number;
}

export interface DocumentsBodyRequest {
  documents_ids: Array<number>;
  publication_numbers: Array<string>;
  additional_info: object;
  search_filters: {
    free_text_query: string;
  };
  applicant_aliases: Array<{alias: string, applicant: string}>;
}
