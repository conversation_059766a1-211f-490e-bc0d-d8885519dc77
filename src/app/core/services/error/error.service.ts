import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ErrorService {
  private _error$ = new BehaviorSubject<string | Error>('');
  error$ = this._error$.asObservable();

  constructor() {
  }

  public addError(error: string | Error): void {
    this._error$.next(error);
  }

  clearStoredData(): void {
    this._error$.next('');
  }
}
