import { TestBed } from '@angular/core/testing';

import { EspacenetService } from './espacenet.service';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideMatomo } from 'ngx-matomo-client';

describe('EspacenetService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
  }));

  it('should be created', () => {
    const service: EspacenetService = TestBed.inject(EspacenetService);
    expect(service).toBeTruthy();
  });
});
