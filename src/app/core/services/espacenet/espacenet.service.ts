import { Injectable } from '@angular/core';
import { Patent } from '@core/models';
import { UserService } from '../user/user.service';

@Injectable({
  providedIn: 'root'
})
export class EspacenetService {

  constructor(private userService: UserService) {
  }

  getWorldwideLinkByPatent(patent: Patent, byQueriedPatent?: boolean): string {
    if (!patent.general || !patent.general.raw_publication_number) {
      return '';
    }
    let patentNumber = patent.general.raw_publication_number;
    if (byQueriedPatent && patent.general.original_number_normalized) {
      patentNumber = patent.general.original_number_normalized;
    }

    return this.getWorldwideLinkByPublicationNumber(patentNumber, patent.general.docdb_family_id);
  }

  public getWorldwideLinkByPublicationNumber(publication: string, family_id: string): string {
    if (!publication || !family_id || !this.userService.getUser().subscription ||
      this.userService.isExternalUser()) {
      return '';
    }

    const publicationRegexp = publication.replace(/-|-/gi, '');

    return `https://worldwide.espacenet.com/patent/search/family/${family_id}/publication/${publicationRegexp}?q=${publicationRegexp}`;
  }

  public getWipoLinkByPublicationNumber(publication: string): string {
    if (!publication || !this.userService.getUser().subscription ||
      this.userService.isExternalUser()) {
      return '';
    }

    const cleanedPublication = publication
      .replace(/-|-/gi, '')
      .replace(/[a-zA-Z]+\d{0,3}$/, '');

    return `https://patentscope.wipo.int/search/en/detail.jsf?docId=${cleanedPublication}`;
  }

  public getGlobalDossierByPublicationNumber(publication: string): string {
    if (!publication || !this.userService.getUser().subscription || this.userService.isExternalUser()) {
      return '';
    }

    const cleanedPublication = publication.replace(/-|-/gi, '');
    const authority = cleanedPublication.match(/^[a-zA-Z]{2}/)[0];
    let publicationNumber: string;
    let externalAuthority: string;

    if (['US', 'CN', 'EP', 'KR', 'JP'].includes(authority)) {
      publicationNumber = cleanedPublication.match(/[a-zA-Z]{2}([a-zA-Z]*\d+)/)[1];
      externalAuthority = authority;
    } else {
      publicationNumber = cleanedPublication.replace(/[a-zA-Z]+\d{0,3}$/, '');
      externalAuthority = authority === 'WO' ? 'WIPO' : 'CASE';
    }

    const searchTypes = {
      'US': 'patent',
      'CN': 'publication',
      'EP': 'publication',
      'KR': 'publication',
      'JP': 'publication',
      'WIPO': 'publication',
      'CASE': 'publication'
    };
    const searchType = searchTypes[externalAuthority];

    return `https://globaldossier.uspto.gov/result/${searchType}/${externalAuthority}/${publicationNumber}/1`;
  }
}
