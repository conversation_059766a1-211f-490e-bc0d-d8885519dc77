import { Injectable } from '@angular/core';
import { ImageOptions, jsPDF, jsPDFOptions } from 'jspdf';
import JSZ<PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import * as htmlToImage from 'html-to-image';
import { chartsConfig } from '@shared/charts/config';
import { BehaviorSubject, concatMap, of } from 'rxjs';

interface ExportingParams {
  cssSelector: string;
  actionType: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT';
  scale: number;
  downloadName: string;
  startCallback?: () => void,
  successCallback?: () => void,
  failureCallback?: (error: string) => void,
}

interface ExportingElement {
  element: HTMLElement;
  exportingTitle: string,
  fileName: string;
  scaledContentWidth: number;
  scaledContentHeight: number;
  exportedResult?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ExportChartService {
  private exportingChartsCompleted = new BehaviorSubject<{
    exportingParams: ExportingParams,
    exportingElements: ExportingElement[],
    taskId: string
  }>(null);
  private readonly exportingChartsCompleted$ = this.exportingChartsCompleted.asObservable();

  private exportingTasks = new Set<string>();

  constructor() {
    this.exportingChartsCompleted$
      .pipe(
        concatMap((data) => of(data))
      )
      .subscribe({
        next: async (data) => {
          if (data) {
            const {exportingParams, exportingElements, taskId} = data;
            await this.completeExportingCharts(exportingParams, exportingElements, taskId);
          }
        }
      });
  }

  async exportCharts(
    chartsCssSelector: string,
    actionType: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT',
    scale: number,
    downloadName: string,
    startCallback?: () => void,
    successCallback?: () => void,
    failureCallback?: (error: string) => void
  ) {
    const exportingParams = {
      cssSelector: chartsCssSelector,
      actionType,
      scale,
      downloadName,
      startCallback,
      successCallback,
      failureCallback
    } as ExportingParams;
    const taskId = this.generateTaskId(downloadName, actionType, true);
    await this.startExportingCharts(exportingParams, taskId);
  }

  async exportChart(
    exportingEle: HTMLElement,
    actionType: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT',
    scale: number,
    downloadName: string,
    saveFile: boolean,
    startCallback: () => void = null,
    successCallback: (dataUrl: string) => void = null,
    failureCallback: (error: any) => void = null
  ) {
    if (!exportingEle) {
      if (typeof (failureCallback) === 'function') {
        failureCallback('No chart to export');
      }
      return;
    }

    const taskId = this.generateTaskId(downloadName, actionType, saveFile);

    if (this.exportingTasks.has(taskId)) {
      if (typeof (failureCallback) === 'function') {
        failureCallback('The chart is being downloaded');
      }
      return;
    }

    this.exportingTasks.add(taskId);

    if (typeof (startCallback) === 'function') {
      startCallback();
    }

    const isPdfOrPrintType = this.isPdfOrPrintType(actionType);
    const border = isPdfOrPrintType ? 50 : 0;

    const {pageWidth, pageHeight, contentWidth, contentHeight} = this.getExportDimension(exportingEle, scale, border);
    await this.getExportFactory(exportingEle, actionType, contentWidth, contentHeight, `${scale}`).then(async (dataUri) => {
      try {
        if (saveFile) {
          await (async () => {
            const pdfFileName = `${downloadName}.pdf`;
            if (isPdfOrPrintType) {
              if (actionType === 'PRINT') {
                this.printChart(contentWidth, contentHeight, pageHeight, pageWidth, downloadName, dataUri);
              } else {
                const pdf = new jsPDF({
                  orientation: pageWidth > pageHeight ? 'landscape' : 'portrait',
                  unit: "px",
                  format: [pageWidth, pageHeight],
                  compress: true
                } as jsPDFOptions);

                pdf.addImage({
                  imageData: dataUri,
                  x: border,
                  y: border,
                  width: contentWidth,
                  height: contentHeight,
                  format: 'PNG'
                } as ImageOptions);

                await pdf.save(pdfFileName, {returnPromise: true}).then(() => {
                  pdf.close();
                });
              }
            } else {
              await (async () => {
                await saveAs(dataUri, `${downloadName}.${actionType.toLowerCase()}`);
              })();
            }
          })();
        }

        this.exportingTasks.delete(taskId);
        if (typeof (successCallback) === 'function') {
          successCallback(dataUri);
        }
      } catch (e) {
        this.exportingTasks.delete(taskId);
        console.error('Error in downloading chart', e);
        if (typeof (failureCallback) === 'function') {
          failureCallback('Error in downloading/exporting chart');
        }
      }
    }).catch((error) => {
      this.exportingTasks.delete(taskId);
      console.error('Error in downloading chart', error);
      if (typeof (failureCallback) === 'function') {
        failureCallback('Error in downloading/exporting chart');
      }
    });
  }

  isExporting(downloadName: string, actionType: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT', saveFile: boolean) {
    return this.exportingTasks.has(this.generateTaskId(downloadName, actionType, saveFile));
  }

  getBase64OfImageUri(dataUri): string {
    const base64Str = 'base64,';
    const base64Index = dataUri.indexOf(base64Str);
    if (base64Index > 0) {
      return dataUri.substring(base64Index + base64Str.length);
    }

    const isSvg = dataUri.indexOf('data:image/svg') >= 0;

    if (isSvg) {
      return window.btoa(unescape(dataUri.substring(dataUri.indexOf(',') + 1)));
    }
  }

  noExportingTasks() {
    return this.exportingTasks.size === 0;
  }

  private printChart(contentWidth: number, contentHeight: number, pageHeight: number, pageWidth: number, downloadName: string, dataUri: string) {
    const printMargin = 22;
    const [printWidth, printHeight] = [210, 297];
    const isLandscape = contentWidth > contentHeight;
    const [rotatedPageWidth, rotatedPageHeight] = isLandscape ? [printHeight, printWidth] : [printWidth, printHeight];
    const [windowWidth, windowHeight] = isLandscape ? [pageHeight, pageWidth] : [pageWidth, pageHeight];
    const orientation = isLandscape ? 'landscape' : 'portrait';

    const printWindow = window.open(
      "",
      "OctiminePrintWindow",
      `height=${windowHeight},width=${windowWidth}},resizable=1,status=1`
    );
    printWindow.document.write(`<html><head><title>${downloadName}</title>`);
    printWindow.document.write(`<style>@page { size: A4 ${orientation}; margin: ${printMargin / 2}mm; }</style></head>`);
    printWindow.document.write(`<body style="width: ${rotatedPageWidth - printMargin}mm; height: ${rotatedPageHeight - printMargin}mm; display: flex; margin: auto; padding: 0; position: relative; page-break-after: auto;">`);
    printWindow.document.write(`<img src="${dataUri}" style="margin: auto; padding: 0; display: block; max-width: 100%; max-height: 100%;"/>`);
    printWindow.document.write("</body></html>");
    printWindow.document.close();
    printWindow.onafterprint = function(event) {
      printWindow.close();
    };
    printWindow.onload = () => {
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    };
  }

  private generateTaskId(downloadName: string, actionType: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT', saveFile: boolean): string {
    return `${downloadName}_${actionType}_${saveFile.toString()}`;
  }

  private async startExportingCharts(params: ExportingParams, taskId: string) {
    if (!params) {
      return;
    }

    if (this.exportingTasks.has(taskId)) {
      return;
    }

    if (typeof (params.startCallback) === 'function') {
      params.startCallback();
    }

    this.exportingTasks.add(taskId);

    await this.selectExportingChartElements(params).then(async (exportingElements) => {
      if (exportingElements.length > 0) {
        const [maxWidth, maxHeight] = this.getMaxDimension(exportingElements);

        await (async () => {
          for (const ele of exportingElements) {
            const {scaledWidth, scaledHeight, newScale} = this.getScaledDimension(ele, maxWidth, maxHeight);

            await this.getExportFactory(ele.element, params.actionType, scaledWidth, scaledHeight, `${newScale}`)
              .then(async (dataUri) => {
                ele.exportedResult = dataUri;
                this.exportingChartsCompleted.next({
                  exportingParams: params, exportingElements, taskId
                });
              });
          }
        })();
      } else {
        this.exportingTasks.delete(taskId);

        if (typeof (params.failureCallback) === 'function') {
          params.failureCallback('There is no chart to download/export');
        }
      }
    });
  }

  private async selectExportingChartElements(params: ExportingParams) {
    const exportingElements: ExportingElement[] = [];

    document.querySelectorAll(params.cssSelector).forEach((el) => {
      const chartItemEle = el.querySelector('.chart-item.exportable') as HTMLElement;
      if (chartItemEle) {
        const exportingCssSelector = chartItemEle.dataset.exportCssSelector || '.chart-content';
        const exportingEle = chartItemEle.querySelector(exportingCssSelector) as HTMLElement;

        if (exportingEle) {
          exportingElements.push({
            element: exportingEle,
            exportingTitle: chartItemEle.dataset.exportTitle,
            fileName: `${chartItemEle.dataset.exportTitle}.${params.actionType.toLowerCase()}`,
            scaledContentWidth: exportingEle.offsetWidth * params.scale,
            scaledContentHeight: exportingEle.offsetHeight * params.scale,
          } as ExportingElement);
        }
      }
    });

    return Promise.resolve(exportingElements);
  }

  private async completeExportingCharts(exportingParams: ExportingParams, exportingElements: ExportingElement[], taskId: string) {
    if (!taskId) {
      return;
    }

    if (!exportingParams || exportingElements.length === 0) {
      this.exportingTasks.delete(taskId);
      return;
    }

    if (this.wereChartsExported(exportingElements)) {
      await this.generateExportResultFile(exportingParams, exportingElements).then(() => {
        this.exportingTasks.delete(taskId);

        if (typeof (exportingParams.successCallback) === 'function') {
          exportingParams.successCallback();
        }
      });
    }
  }

  private async generateExportResultFile(exportingParams: ExportingParams, exportingElements: ExportingElement[]) {
    if (this.isPdfOrPrintType(exportingParams.actionType)) {
      await this.generatePdfFile(exportingParams, exportingElements).then(async (pdf) => {
        await this.saveOrPrintPdfFile(exportingParams, pdf);
      });
    } else {
      await this.generateZipFile(exportingParams, exportingElements).then(async (zip) => {
        await this.saveZipFile(exportingParams, zip);
      });
    }
  }

  private async generateZipFile(exportingParams: ExportingParams, exportingElements: ExportingElement[]): Promise<JSZip> {
    return new Promise((resolve, reject) => {
      const zip = new JSZip();

      exportingElements.forEach((ele, index) => {
        zip.file(ele.fileName, this.getBase64OfImageUri(ele.exportedResult), {base64: true});
      });

      resolve(zip);
    });
  }

  private async generatePdfFile(exportingParams: ExportingParams, exportingElements: ExportingElement[]): Promise<jsPDF> {
    const isPdfOrPrintType = this.isPdfOrPrintType(exportingParams.actionType);

    const border = isPdfOrPrintType ? 50 : 0;

    const [maxWidth, maxHeight] = this.getMaxDimension(exportingElements);

    const pageWidth = maxWidth + border * 2;
    const pageHeight = maxHeight + border * 2;
    const orientation = pageWidth > pageHeight ? 'landscape' : 'portrait';

    const pdf = new jsPDF({
      unit: "px",
      compress: true
    } as jsPDFOptions);

    exportingElements.forEach((ele, index) => {
      const {scaledWidth, scaledHeight, newScale} = this.getScaledDimension(ele, maxWidth, maxHeight);
      pdf.addPage([pageWidth, pageHeight], orientation);
      pdf.addImage({
        imageData: ele.exportedResult,
        x: (pageWidth - scaledWidth) / 2,
        y: (pageHeight - scaledHeight) / 2,
        width: scaledWidth,
        height: scaledHeight,
        format: 'PNG',
      } as ImageOptions);

      const topHeight = (pageHeight - scaledHeight) / 2;
      const fontSize = 100;

      pdf.setTextColor(chartsConfig.labelColor);
      pdf.setFontSize(fontSize);
      pdf.text(ele.exportingTitle, pageWidth / 2, topHeight / 2 + 20, {align: 'center'});

      if (index === 0) {
        pdf.deletePage(1);
      }
    });

    return pdf;
  }

  private async saveOrPrintPdfFile(exportingParams: ExportingParams, pdf: jsPDF) {
    if (exportingParams.actionType === 'PRINT') {
      pdf.autoPrint();
      pdf.output('dataurlnewwindow', {filename: exportingParams.downloadName});
    } else {
      await pdf.save(exportingParams.downloadName, {returnPromise: true});
    }
    pdf.close();
  }

  private async saveZipFile(exportingParams: ExportingParams, zip: JSZip) {
    await zip.generateAsync({type: "blob"}).then(async (content) => {
      await (async () => {
        saveAs(content, `${exportingParams.downloadName}.zip`);
      })();
    });
  }

  private isPdfOrPrintType(actionType: string): boolean {
    return actionType === 'PDF' || actionType === 'PRINT';
  }

  private getMaxDimension(exportingElements: ExportingElement[]): number[] {
    // const maxWidth = Math.max(...exportingElements.map((item) => item.scaledContentWidth));
    // const maxHeight = Math.max(...exportingElements.map((item) => item.scaledContentHeight));
    // return [maxWidth, maxHeight];
    return [2480 - 100, 3508 - 100];
  }

  private getExportDimension(chartHtmlEle: HTMLElement, scale: number, border: number): {
    pageWidth: number, pageHeight: number, contentWidth: number, contentHeight: number
  } {
    const contentWidth = chartHtmlEle.offsetWidth * scale;
    const contentHeight = chartHtmlEle.offsetHeight * scale;
    const pageWidth = contentWidth + 2 * border;
    const pageHeight = contentHeight + 2 * border;
    return {pageWidth, pageHeight, contentWidth, contentHeight};
  }

  private getScaledDimension(ele: ExportingElement, maxWidth: number, maxHeight: number): {
    scaledWidth: number, scaledHeight: number, newScale: number
  } {
    const newScale = Math.min(maxWidth / ele.element.offsetWidth, maxHeight / ele.element.offsetHeight);
    const scaledWidth = ele.element.offsetWidth * newScale;
    const scaledHeight = ele.element.offsetHeight * newScale;
    return {scaledWidth, scaledHeight, newScale};
  }

  private async getExportFactory(chartHtmlEle: HTMLElement, actionType: string, exportWidth: number, exportHeight: number, scale: string): Promise<any> {
    const filterFunc = (node: HTMLElement) => {
      const exclusionClasses = ['highcharts-tooltip', 'highcharts-exporting-group'];
      return !exclusionClasses.some((classname) => node.classList?.contains(classname));
    }

    const exportOptions = {
      height: exportHeight,
      width: exportWidth,
      canvasHeight: exportHeight,
      canvasWidth: exportWidth,
      style: {
        transform: `scale(${scale})`,
        transformOrigin: 'top left'
      },
      cacheBust: false,
      skipFonts: true,
      backgroundColor: chartsConfig.bgColor,
      filter: filterFunc
    };

    switch (actionType) {
      case 'JPEG':
        return await htmlToImage.toJpeg(chartHtmlEle, exportOptions);
      case 'PNG':
        return await htmlToImage.toPng(chartHtmlEle, exportOptions);
      case 'SVG':
        return await htmlToImage.toPng(chartHtmlEle, exportOptions).then((data) => {
          return this.imageToSvg(data, exportWidth.toString(10), exportHeight.toString(10));
        });
      case 'PDF':
      case 'PRINT':
        return await htmlToImage.toPng(chartHtmlEle, exportOptions);
    }
  }

  private wereChartsExported(exportingElements: ExportingElement[]): boolean {
    return exportingElements.length > 0 && exportingElements.every((e) => !!e.exportedResult);
  }

  private async imageToSvg(dataUri: string, width: string, height: string): Promise<string> {
    const svgNamespace = 'http://www.w3.org/2000/svg';
    const xlinkNamespace = 'http://www.w3.org/1999/xlink';
    const svg = document.createElementNS(svgNamespace, 'svg');
    svg.setAttribute('xmlns:xlink', xlinkNamespace);
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    const svgImage = document.createElementNS(svgNamespace, 'image');
    svgImage.setAttribute('width', width);
    svgImage.setAttribute('height', height);
    svgImage.setAttribute('xlink:href', dataUri);
    svgImage.setAttribute('x', '0');
    svgImage.setAttribute('y', '0');
    svg.appendChild(svgImage);
    return await this.svgToDataURL(svg);
  }

  private async svgToDataURL(svg: SVGElement): Promise<string> {
    return Promise.resolve()
      .then(() => new XMLSerializer().serializeToString(svg))
      .then(encodeURIComponent)
      .then((html) => `data:image/svg+xml;charset=utf-8,${html}`)
  }

  clearStoredData() {
    this.exportingChartsCompleted.next(null);
    this.exportingTasks.clear();
  }
}
