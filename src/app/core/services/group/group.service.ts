import { Injectable } from '@angular/core';
import { ApiService, PaginationMetadata, UserGroup } from '@core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class GroupService {

  constructor(
    private apiService: ApiService
  ) {
  }

  getGroups(payload): Observable<{ page: PaginationMetadata, groups: Array<UserGroup> }> {
    return this.apiService.get('auth/groups', payload)
      .pipe(map(response => response.data));
  }

  getGroupMember(groupId: number): Observable<{ page: PaginationMetadata, users: Array<any> }> {
    return this.apiService.get(`auth/groups/${groupId}/users`)
      .pipe(map(response => response.data));
  }

  updateGroup(groupId: number, payload): Observable<UserGroup> {
    return this.apiService.patch(`auth/groups/${groupId}`, payload)
      .pipe(map(({data}) => data));
  }

  createGroup(payload): Observable<UserGroup> {
    return this.apiService.post('auth/groups', payload)
      .pipe(map(({data}) => data));
  }

  removeGroupFromCompany(groupId: number): Observable<any> {
    return this.apiService.delete(`auth/groups/${groupId}`);
  }

  removeFromGroup(groupId: number, userId: number): Observable<any> {
    return this.apiService.delete(`auth/groups/${groupId}/users/${userId}`);
  }

  addToGroup(groupId: number, userId: number): Observable<any> {
    return this.apiService.put(`auth/groups/${groupId}/users/${userId}`);
  }
}
