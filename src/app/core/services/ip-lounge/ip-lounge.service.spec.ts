import { TestBed } from '@angular/core/testing';

import { IpLoungeService } from './ip-lounge.service';

describe('IpLoungeService', () => {
  let service: IpLoungeService;

  const VALID_TOKEN = `eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6InNmSEtxRDJBc3lBUmpfaGwxM3pwRDRBNUlWQSIsImtpZCI6InNmSEtxRDJBc3l
    BUmpfaGwxM3pwRDRBNUlWQSJ9.eyJpc3MiOiJodHRwczovL2lkZW50aXR5LmRlbm5lbWV5ZXIuY29tIiwiYXVkIjoiaHR0cHM6Ly9pZGVudGl0eS5kZW5uZW1leWVy
    LmNvbS9yZXNvdXJjZXMiLCJleHAiOjI2MzE3MTMyNjgsIm5iZiI6MTYzMTcwOTY2OCwiY2xpZW50X2lkIjoiaXAtbG91bmdlLXFhLXVzciIsInNjb3BlIjpbImRlbm
    5lbWV5ZXItYXV0aG9yaXphdGlvbi1hcGkiLCJkZW5uZW1leWVyLXByb2ZpbGUtYXBpIiwiZXAtdmFsLXN0YWdpbmciLCJpcC1sb3VuZ2UtcWEiLCJvY3RpbWluZS10
    ZXN0IiwicHN4LXBvcnRhbC1iZXRhIiwicHN4LXBvcnRhbC1wcmVyZWxlYXNlIiwicHN4LXBvcnRhbC1xYSIsInBzeC1wb3J0YWwtc3RhZ2luZyJdLCJzdWIiOiIxNj
    czNDQiLCJhdXRoX3RpbWUiOjE2MzE3MDk2NjgsImlkcCI6Imlkc3J2IiwicHJlZmVycmVkX3VzZXJuYW1lIjoiYXN0b25rdXNAZGVubmVtZXllci5jb20iLCJhbXIi
    OlsicGFzc3dvcmQiXX0.QO3sOuHhgnkquADOQSdDPp4WnGKHEs7Jgx8_Q-akibA3-IovBkmqtjDQbQb-UH4UtOPXXDqE5uKCcR7ZfXQEINeZDplUAWeJ2uLY8yeAbH
    Bpw2yJsmX4oxgRm_id8mqkp2u837gLukVYeNaXu1IbYdj7rOC4rhKaYbdHVWEnUYWdZ0YdT-oVoxWxNsoAor2D2bJ-yNw2HZHPPkXw3j8u6KdvuYeWA0gJQQ910dqd
    ksiGrmg2E9PdZAIYWuH-JMGqQTdpie0HOiblbS6xq6SP0V3HwCdUvzTMawKig6HNSHy93MRap9U9JZOoV7Co7RahtncbX-Bk1EDBr1b-ObhSzA`;

  const EXPIRED_TOKEN = `eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6InNmSEtxRDJBc3lBUmpfaGwxM3pwRDRBNUlWQSIsImtpZCI6InNmSEtxRDJBc
    3lBUmpfaGwxM3pwRDRBNUlWQSJ9.eyJpc3MiOiJodHRwczovL2lkZW50aXR5LmRlbm5lbWV5ZXIuY29tIiwiYXVkIjoiaHR0cHM6Ly9pZGVudGl0eS5kZW5uZW1leW
    VyLmNvbS9yZXNvdXJjZXMiLCJleHAiOjE2MzE3MTMyNjgsIm5iZiI6MTYzMTcwOTY2OCwiY2xpZW50X2lkIjoiaXAtbG91bmdlLXFhLXVzciIsInNjb3BlIjpbImRl
    bm5lbWV5ZXItYXV0aG9yaXphdGlvbi1hcGkiLCJkZW5uZW1leWVyLXByb2ZpbGUtYXBpIiwiZXAtdmFsLXN0YWdpbmciLCJpcC1sb3VuZ2UtcWEiLCJvY3RpbWluZS
    10ZXN0IiwicHN4LXBvcnRhbC1iZXRhIiwicHN4LXBvcnRhbC1wcmVyZWxlYXNlIiwicHN4LXBvcnRhbC1xYSIsInBzeC1wb3J0YWwtc3RhZ2luZyJdLCJzdWIiOiIx
    ******************************************************************************************************************************
    IiOlsicGFzc3dvcmQiXX0.QO3sOuHhgnkquADOQSdDPp4WnGKHEs7Jgx8_Q-akibA3-IovBkmqtjDQbQb-UH4UtOPXXDqE5uKCcR7ZfXQEINeZDplUAWeJ2uLY8yeA
    bHBpw2yJsmX4oxgRm_id8mqkp2u837gLukVYeNaXu1IbYdj7rOC4rhKaYbdHVWEnUYWdZ0YdT-oVoxWxNsoAor2D2bJ-yNw2HZHPPkXw3j8u6KdvuYeWA0gJQQ910d
    qdksiGrmg2E9PdZAIYWuH-JMGqQTdpie0HOiblbS6xq6SP0V3HwCdUvzTMawKig6HNSHy93MRap9U9JZOoV7Co7RahtncbX-Bk1EDBr1b-ObhSzA`;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(IpLoungeService);
  });

  it('Can parse expired IP Lounge token', () => {
    service.saveIPLoungeToken(EXPIRED_TOKEN, []);
    expect(service.hasIPLoungeAccess()).toBeFalsy();
    expect(service.getIPLoungeServices().length).toEqual(0);
  });

  it('Can parse valid IP Lounge token', () => {
    service.saveIPLoungeToken(VALID_TOKEN, []);
    expect(service.hasIPLoungeAccess()).toBeTruthy();
  });

  it('Can parse invalid IP Lounge token', () => {
    service.saveIPLoungeToken('eyJ0e.adsfsadf.äüasdf', []);
    expect(service.hasIPLoungeAccess()).toBeFalsy();
  });

  it('Can parse broken IP Lounge token', () => {
    service.saveIPLoungeToken('1234', []);
    expect(service.hasIPLoungeAccess()).toBeFalsy();
  });

  it('Can extract services from valid token', () => {
    service.saveIPLoungeToken(VALID_TOKEN, [{'ServiceId': null, 'ServiceName': 'IP Lounge', 'ServiceCode': 'ip-lounge',
    'ServiceUrlTemplate': 'https://dev.dennemeyer.com/ipl/#/', 'ServiceIcon': null, 'IsBeta': null},
    {'ServiceId': 49, 'ServiceName': 'Octimine', 'ServiceCode': 'octimine',
    'ServiceUrlTemplate': 'https://staging.octimine.com/auth/ip-lounge?token=<access_token>', 'ServiceIcon': null, 'IsBeta': false}]);
    expect(service.hasIPLoungeAccess()).toBeTruthy();
    expect(service.getIPLoungeServices().length).toEqual(2);
  });

  it('Can handle broken services', () => {
    service.saveIPLoungeToken(VALID_TOKEN, [{'ServiceId': null, 'ServiceName': 'IP Lounge', 'ServiceCode': 'ip-lounge',
      'ServiceUrlTemplate': 1234, 'ServiceIcon': null, 'IsBeta': null}, {}, 456]);
    expect(service.hasIPLoungeAccess()).toBeTruthy();
    expect(service.getIPLoungeServices().length).toEqual(3);
  });
});
