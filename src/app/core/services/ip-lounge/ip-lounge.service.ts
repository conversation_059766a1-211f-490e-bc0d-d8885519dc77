import { Injectable } from '@angular/core';
import { IpLounge } from './types';

@Injectable({
  providedIn: 'root'
})
export class IpLoungeService {

  constructor() {
  }

  public saveIPLoungeToken(token: string, services: Array<Object>): void {
    try {
      const payload = token.split('.')[1].replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(payload).split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)).join('')
      );
      const tokenClaims = JSON.parse(jsonPayload);
      const expiryDate = new Date(tokenClaims['exp'] * 1000);
      window.localStorage['ip_lounge_token'] = JSON.stringify({
        token: token,
        expires: expiryDate,
        services: services
      });
    } catch (e) {
      console.error('Failed to parse IP Lounge token', e);
      window.localStorage.removeItem('ip_lounge_token');
    }
  }

  private getIPLoungeTokenData(): Object {
    const tokenString = window.localStorage['ip_lounge_token'];
    if (!tokenString) {
      return null;
    }
    try {
      const tokenObject = JSON.parse(tokenString);
      if (tokenObject['expires']) {
        const expiryDate = Date.parse(tokenObject['expires']);
        if (expiryDate && expiryDate >= Date.now()) {
          return tokenObject;
        }
      }
    } catch (e) {
      console.error('Failed to parse IP Lounge token', e);
    }
    return null;
  }

  public hasIPLoungeAccess(): boolean {
    return this.getIPLoungeTokenData() != null;
  }

  public getIPLoungeServices(): Array<IpLounge> {
    const tokenData = this.getIPLoungeTokenData();
    if (!tokenData) {
      return [];
    }

    return tokenData['services'].map(
      s => {
        const serviceCode = s['ServiceCode'];
        const url = typeof s['ServiceUrlTemplate'] === 'string' ? s['ServiceUrlTemplate'].replace('<access_token>', tokenData['token']) : null;
        const targetUrl = this.isOctimineService(serviceCode) && url.includes('url=') ? url.split('url=')[1].split('&')[0] : '/launchpad';
        return {
          url,
          targetUrl,
          serviceCode,
          serviceName: s['ServiceName'],
          serviceIcon: s['ServiceIcon']
        };
      });
  }

  public removeIpLoungeToken() {
    window.localStorage.removeItem('ip_lounge_token');
  }

  public isOctimineService(serviceCode: string): boolean {
    return ['octimine', 'patent-landscape', 'patent-monitoring'].includes(serviceCode);
  }

  clearStoredData(): void {
    this.removeIpLoungeToken();
  }
}
