import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class JwtService {

  public getAccessToken(): string {
    return window.localStorage['access_token'];
  }

  public getRefreshToken(): string {
    return window.localStorage['refresh_token'];
  }

  public saveAccessToken(token: string): void {
    window.localStorage['access_token'] = token;
  }

  public saveRefreshToken(token: string): void {
    window.localStorage['refresh_token'] = token;
  }

  public destroyTokens(): void {
    window.localStorage.removeItem('access_token');
    window.localStorage.removeItem('refresh_token');
  }
}
