import { TestBed } from '@angular/core/testing';

import { LandscapeService } from './landscape.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('LandscapeService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
    providers: [ provideMatomo({
      siteId: '7',
      trackerUrl: 'https://stats.dennemeyer.digital/',
      disabled: true
    }) ]
  }));

  it('should be created', () => {
    const service: LandscapeService = TestBed.inject(LandscapeService);
    expect(service).toBeTruthy();
  });
});
