import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { LandscapeStoreService } from '@core/store';
import { Observable } from 'rxjs';
import { PatentViewReferralType } from '@core/services/patent/types';
import { map } from 'rxjs/operators';
import { LandscapeProfile } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class LandscapeService {

  /**
   * additional data for singleton patent viewer
   */
  public linkData = {title: 'Landscape', referral: PatentViewReferralType.REFERRAL_LANDSCAPE, backPatentSearch: true};

  constructor(
    private apiService: ApiService,
    private landscapeStoreService: LandscapeStoreService
  ) {
  }

  /**
   * get list of landscape profiles
   * @returns - Promise Object represents API response
   * @param params Additional optional parameter
   */
  public loadProfiles(params?: {}): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiService.get('web/landscape/profile', params).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * get list of shared landscape profiles
   * @returns - Observable Object represents API response
   * @param params Additional optional parameter
   */
  public loadSharedProfiles(params?: {}): Observable<any> {
    return this.apiService.get(`web/landscape/shared-profile`, params)
      .pipe(map(response => response));
  }

  /**
   * loadProfileDraft
   *
   * Load landscape profile draft on draft page
   * @param ID ID for landscape profile to be fetched
   * @param params Additional optional parameter
   * @returns - Promise Object represents API response
   */
  loadProfileDraft(ID: number, params?: {}): Promise<any> {
    return new Promise((resolve, reject) => {
      this.apiService.get(`web/landscape/async/profile/${ID}/draft/documents`, params).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * Create new landscape profile
   * @param payload - payload for landscape profile to be create
   * @returns - Promise Object represents API response
   */
  public newProfile(payload: Object): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.post(`web/landscape/async/profile`, payload).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * Delete specific landscape profile
   * @param ID - ID of landscape profile to be delete
   * @returns - Promise Object represents API response
   */
  public deleteProfile(ID: number): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.delete(`web/landscape/profile/${ID}`).subscribe({
        next: res => {
          this.loadProfiles();
          return resolve(res);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * update the specific landscape profile
   * @param ID - ID of landscape profile to be update
   * @param payload - payload for landscape profile to be update
   * @returns - Promise Object represents API response
   */
  public updateProfile(ID: number, payload: Object): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.patch(`web/landscape/async/profile/${ID}`, payload).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * get landscape profile with specific ID
   * @param ID ID for landscape profile to be fetched
   * @param params Additional optional parameter
   */
  public loadProfile(ID: number, params?: {}): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.get(`web/landscape/profile/${ID}`, params).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * get landscape profile documents with specific ID
   * @param ID ID for landscape profile to be fetched
   * @param params Additional optional parameter
   */
  public loadProfileDocument(ID: number, params?: {}): Observable<any> {
    return this.apiService.get(`web/landscape/async/profile/${ID}/documents`, params)
      .pipe(map(response => response));
  }

  /**
   * computeLandscapeProfile
   *
   * Compute landscape profile from a draft
   * @param ID ID for landscape profile to be computed
   */
  public computeLandscapeProfile(ID: number): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.put(`web/landscape/async/profile/${ID}/draft/complete`).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * updateDocumentStatus
   *
   * update document status in landscape profile draft list
   * @param ID ID for landscape profile to be computed
   * @param payload payload with documents status update
   */
  public updateDocumentStatus(ID: number, payload: Array<any>): Promise<Object> {
    return new Promise((resolve, reject) => {
      this.apiService.put(`web/landscape/profile/${ID}/draft/documents`, payload).subscribe({
        next: (res) => {
          return resolve(res);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }


  public getDocuments(): Object[] {
    return this.landscapeStoreService.landscapeDocuments;
  }

  public getProfile(): LandscapeProfile {
    return this.landscapeStoreService.landscapeProfile;
  }

  public export(hash: string, ids: any, queryParams: any): Observable<any> {
    return this.apiService.asyncExport(`web/export/${hash}`, ids, queryParams);
  }

  public sendFile(file, params): Observable<Object> {
    return this.apiService.postFormData(`web/landscape/profile/parse/patent_list`, file, params);
  }

  public getCompetitiveInsight(profileId: number, payload: {}): Observable<any> {
    return this.apiService.post(`web/landscape/profile/${profileId}/competitive/insights`, payload);
  }

  public computeCompetitiveProfile(profileId: number): Observable<any> {
    return this.apiService.post(`web/landscape/profile/${profileId}/competitive`, {});
  }
}
