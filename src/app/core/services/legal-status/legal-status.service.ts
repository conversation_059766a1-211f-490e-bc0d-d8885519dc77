import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { LegalEventsResponse } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class LegalStatusService {

  constructor(
    private apiService: ApiService
  ) {
  }

  getLegalEvents(familyId: any, payload = {}): Observable<LegalEventsResponse> {
    return this.apiService.get(`search/legal_status/${familyId}/events`, payload)
      .pipe(map((resp) => resp.data));
  }

  getLegalStatusQuery(status: string | string[], inverse = false): string {
    if (!status) {
      return '';
    }

    if (typeof status === 'string') {
      return this.getSingleLegalStatusQuery(status, inverse);
    }

    return status.map((val) => {
      return this.getSingleLegalStatusQuery(val, inverse);
    }).join(inverse ? ' AND ' : ' OR ');
  }

  private getSingleLegalStatusQuery(status: string, inverse = false): string {
    if (inverse) {
      return this.getSingleLegalStatusInverseQuery(status);
    }
    if (status === 'dead/unknown') {
      return 'LEGAL_STATUS=(invalid AND unknown)';
    }
    if (status === 'invalid') {
      return '(LEGAL_STATUS=invalid AND LEGAL_STATUS<>unknown)';
    }
    return `LEGAL_STATUS=${status}`;
  }

  private getSingleLegalStatusInverseQuery(status: string): string {
    return `LEGAL_STATUS<>${status}`;
  }

  /**
   * convert search history saved legal status to new view 
   * @param status legal status string
   * @returns compatible legal status string
   */
  preProcessLegalStatus(status: string): string {
    if(status === 'dead/unknown' || status === 'invalid' ){
      return 'dead';
    }
    return status;
  }
}
