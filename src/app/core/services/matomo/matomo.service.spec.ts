import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

import { MatomoService } from './matomo.service';
import { provideMatomo } from 'ngx-matomo-client';

describe('MatomoService', () => {
  let service: MatomoService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers:[ provideMatomo({siteId: '7', trackerUrl: 'https://stats.dennemeyer.digital/', disabled: true })]
    });
    service = TestBed.inject(MatomoService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
