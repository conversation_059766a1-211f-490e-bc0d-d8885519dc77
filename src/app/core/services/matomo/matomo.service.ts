import { Injectable } from '@angular/core';
import { MatomoTracker } from 'ngx-matomo-client';

interface EventOptions {
  eventCategory: string,
  eventAction: string,
  eventName?: string | '',
  eventValue?: number | undefined
}

@Injectable({
  providedIn: 'root'
})
export class MatomoService {

  constructor(private matomoTracker: MatomoTracker) { 
    if (window["_paq"]) {
      window["_paq"].push(['HeatmapSessionRecording::disable']);
    }
  }

  semanticSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Search', eventAction: 'Semantic search', eventName: 'Semantic search button'});
  }

  booleanSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Search', eventAction: 'Boolean search', eventName: 'Boolean search button'});
  }

  citationSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Search', eventAction: 'Citation search', eventName: 'Citation search button'});
  }

  nplSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Search', eventAction: 'NPL search', eventName: 'NPL search button'});
  }

  reloadSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Search', eventAction: 'Reload search history', eventName: 'Reload search button'});
  }

  monitorTechnologyPlusCard() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Plus card'});
  }

  monitorTechnologyDlSetupButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Deep learning Set up monitor button'});
  }

  monitorTechnologySemanticSetupButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Semantic Set up monitor button'});
  }

  monitorTechnologyBooleanSetupButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Boolean Set up monitor button'});
  }

  monitorTechnologyTimeIntervalButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Report view - time interval button'});
  }

  monitorTechnologyFeedbackThumbs() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Feedback - thumbs'});
  }

  monitorTechnologyUpdateModelButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Technology Monitor', eventName: 'Update the model button'});
  }

  monitorLegalStatusPlusCard() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Legal Status Monitor', eventName: 'Plus card'});
  }

  monitorLegalStatusFinishButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Legal Status Monitor', eventName: 'Finish set up button'});
  }

  monitorLegalStatusTimeIntervalButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Legal Status Monitor', eventName: 'Report view - time interval'});
  }

  monitorLegalStatusTrackButton() {
    this.trackUserAndEvent({eventCategory: 'Monitor', eventAction: 'Legal Status Monitor', eventName: 'Track Legal Status button'});
  }

  visualAnalysisChartCategories() {
    this.trackUserAndEvent({eventCategory: 'Visual Analysis', eventAction: 'Switch between charts', eventName: 'Click on chart categories to switch'});
  }

  visualAnalysisChartFilter() {
    this.trackUserAndEvent({eventCategory: 'Visual Analysis', eventAction: 'Chart filtering click', eventName: 'Click on charts to filter'});
  }

  resultListSaveButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'Save search result', eventName: 'Save results button'});
  }

  resultListExportButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'Exports', eventName: 'Export the list button'});
  }

  resultListShareButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'Share search results', eventName: 'Share results button'});
  }

  resultListAddToSearchButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'Add to search', eventName: 'Add to search button'});
  }

  resultListPatentViewerButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'Patent viewer', eventName: 'Patent viewer green button'});
  }

  resultListViewPdfButton() {
    this.trackUserAndEvent({eventCategory: 'Result List', eventAction: 'View PDF', eventName: 'View PDF button'});
  }

  taskManagementAddTaskButton() {
    this.trackUserAndEvent({eventCategory: 'Task Management', eventAction: 'Tasks created', eventName: 'Add task button'});
  }

  taskManagementTaskAccomplished() {
    this.trackUserAndEvent({eventCategory: 'Task Management', eventAction: 'Tasks accomplished', eventName: 'Accomplish task'});
  }

  private trackUserAndEvent(eventOptions: EventOptions) {
    const {eventCategory, eventAction, eventName, eventValue} = eventOptions;
    this.matomoTracker.trackEvent(eventCategory, eventAction, eventName, eventValue);
  }

  public login(userId: string) {
      this.matomoTracker.setUserId(userId);
  }

  public logout() {
    this.matomoTracker.resetUserId();
  }
}
