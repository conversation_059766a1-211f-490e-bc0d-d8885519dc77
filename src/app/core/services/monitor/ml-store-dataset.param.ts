import { MlMatchTypeEnum } from './ml-match-type.enum';
import { MlSourceTypeEnum } from './ml-source-type.enum';


export class MlStoreDatasetParam {
  source: MlSourceTypeEnum;
  match: MlMatchTypeEnum;
  document_id: string;

  constructor(source: MlSourceTypeEnum, match: MlMatchTypeEnum, document_id: string) {
    this.source = source;
    this.match = match;
    this.document_id = document_id;
  }
}
