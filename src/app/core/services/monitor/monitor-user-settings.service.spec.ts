import { TestBed } from '@angular/core/testing';

import { MonitorUserSettingsService } from './monitor-user-settings.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('MonitorUserSettingsService', () => {
  let service: MonitorUserSettingsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(MonitorUserSettingsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
