import { Injectable } from '@angular/core';
import { ApiService, MonitorProfileUserSettings } from '@core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class MonitorUserSettingsService {
  constructor(
    private apiService: ApiService
  ) {
  }

  updateUserSettings(profileId: number, settings: MonitorProfileUserSettings): Observable<MonitorProfileUserSettings> {
    return this.apiService.post(`web/monitor/profile/${profileId}/user/settings`, settings)
      .pipe(map(response => response.data));
  }

  getUserSettings(profileId: number): Observable<MonitorProfileUserSettings> {
    return this.apiService.get(`web/monitor/profile/${profileId}/user/settings`, {})
      .pipe(map(response => response.data));
  }

}
