import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MonitorService } from './monitor.service';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [
      HttpClientTestingModule,
      RouterModule.forRoot([])
    ], providers: [ provideMatomo({
      siteId: '7',
      trackerUrl: 'https://stats.dennemeyer.digital/',
      disabled: true
    }) ]
  }));

  it('should be created', () => {
    const service: MonitorService = TestBed.inject(MonitorService);
    expect(service).toBeTruthy();
  });
});
