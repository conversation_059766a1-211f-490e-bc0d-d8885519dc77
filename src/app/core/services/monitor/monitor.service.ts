import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import {
  LegalStatusPatentListScopeEnum,
  MonitorProfile,
  MonitorRun,
  MonitorSharedProfile,
  MonitorSharedRuns,
  Patent
} from '@core/models';
import { HttpErrorResponse } from '@angular/common/http';
import { catchError, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { BaseMonitorStoreService, MonitorStoreService } from '@core/store';
import { PatentViewReferralType } from '@core/services/patent/types';
import { Location } from '@angular/common';
import { ApiService } from '../api/api.service';
import { MatomoService } from '../matomo/matomo.service';
import { MlStoreDatasetParam } from './ml-store-dataset.param';
import { PaginationMetadata } from '@core/services';
import { Router } from '@angular/router';
import { UrlUtil } from '@core/utils';

@Injectable({
  providedIn: 'root'
})
export class MonitorService {
  /**
   * additional data for singleton patent viewer
   */
  public linkData = {title: 'Monitor', referral: PatentViewReferralType.REFERRAL_MONITOR, backPatentSearch: true};

  /**
   * reference to monitoring profiles pagination data
   */
  private errorsSubject = new BehaviorSubject<Array<string>>([]);
  public errors = this.errorsSubject.asObservable();

  /**
   * reference to monitoring profile section param
   */
  private sectionSubject = new BehaviorSubject<any>(undefined);
  public section = this.sectionSubject.asObservable();

  readonly MONITOR_RUNS_PAGE_SIZE = 10;

  constructor(
    private location: Location,
    private apiService: ApiService,
    private monitorStoreService: MonitorStoreService,
    private matomoService: MatomoService,
    private router: Router,
  ) {
  }

  public loadProfiles(storeService: BaseMonitorStoreService, clearProfiles = true, params = {}):
    Observable<{profiles: MonitorProfile[], page: PaginationMetadata}> {
    this.purgeErrors();
    if (clearProfiles) {
      this.purgeProfiles(storeService);
    }
    return this.apiService.get('web/monitor/profile', params).pipe(
      tap(({data}) => storeService.monitorProfiles = data.monitor_profiles),
      map(({data}) => ({profiles: data.monitor_profiles, page: data.page as PaginationMetadata}))
    );
  }

  public purgeProfiles(storeService: BaseMonitorStoreService): void {
    storeService.monitorProfiles = null;
  }

  public getProfile(id: number): Observable<MonitorProfile> {
    this.purgeErrors();
    return this.apiService.get(`web/monitor/profile/${id}`, {})
      .pipe(map(response => response.data));
  }

  public loadSharedProfiles(query = {}): Observable<{profiles: MonitorSharedProfile[], page: PaginationMetadata}> {
    return this.apiService.get(`web/monitor/shared-profile`, query).pipe(
      map(({data}) => ({profiles: data.shared_profiles, page: data.page as PaginationMetadata})));
  }

  public getSharedProfile(id: number, query = {}): Observable<MonitorSharedRuns> {
    return this.apiService.get(`web/monitor/shared-profile/${id}`, query)
      .pipe(map(response => response.data));
  }

  public deleteSharedProfile(ID: number) {
    this.purgeErrors();
    return this.apiService.delete(`web/monitor/shared-profile/${ID}`);
  }

  public loadSharedProfileDocuments(id: number, query = {}): Observable<Object> {
    return this.apiService.get(`web/monitor/shared-profile/results/${id}`, query)
      .pipe(map(response => response.data));
  }

  /**
   * Create new monitoring profile
   * @param payload - payload for monitoring profile to be create
   * @returns - MonitorProfile Object represents API response
   */
  public newProfile(payload: Object): Observable<MonitorProfile> {
    this.purgeErrors();
    return this.apiService.post(`web/monitor/profile`, payload).pipe(map(response => response.data));
  }

  public cloneProfile(ID: number): Observable<MonitorProfile> {
    this.purgeErrors();
    return this.apiService.post(`web/monitor/profile/${ID}/clone`).pipe(take(1)).pipe(map(response => response.data));
  }

  /**
   * Delete specific monitoring profile
   * @param ID - ID of monitoring profile to be delete
   * @returns - subscription Object represents API response
   */
  public deleteProfile(ID: number) {
    this.purgeErrors();
    return this.apiService.delete(`web/monitor/profile/${ID}`);
  }

  /**
   * update the specific monitoring profile
   * @param ID - ID of monitoring profile to be update
   * @param payload - payload for monitoring profile to be update
   * @returns - Promise Object represents API response
   */
  public updateProfile(ID: number, payload: Object): Promise<Object> {
    this.purgeErrors();
    return new Promise((resolve, reject) => {
      this.apiService.patch(`web/monitor/profile/${ID}`, payload).subscribe({
        next: ({data}) => {
          return resolve(data);
        },
        error: err => {
          return reject(err);
        }
      });
    });
  }

  /**
   * creation of monitor run manually
   *
   * @param payload - payload for creating monitor run
   * @param queryParams - additional query parameter
   * @returns - Observable Object represents API response
   */
  public createMonitorRun(payload: Object, queryParams: Object): Observable<Object> {
    this.purgeErrors();
    return this.apiService.post(`web/monitor/prediction`, payload, queryParams);
  }

  public clearLegalStatusSnapshot(id: number): Observable<any> {
    this.purgeErrors();
    return this.apiService.delete(`web/monitor/profile/${id}/legal_status_snapshot`)
  }

  /**
   * initMonitorRun
   *
   * trigger initial monitor run request for default set of result sets
   *
   * @param ID - monitor profile ID
   * @returns - Observable Object represents API response
   */
  public initMonitorRun(ID: number): Observable<any> {
    this.purgeErrors();
    return this.apiService.post(`web/monitor/profile/${ID}/init`)
  }

  /**
   * deleteMonitorRun
   *
   * delete a monitor run of specific monitor profile
   * @param ID - monitor profile ID
   * @returns - Observable Object represents API response
   */
  public deleteMonitorRun(ID: number): Observable<any> {
    this.purgeErrors();
    return this.apiService.delete(`web/monitor/run/${ID}`);
  }

  /**
   * Get monitoring runs list of specific monitoring profile
   * @param ID - Monitoring profile ID
   * @param page - Optional - page number for monitor run
   * @returns Observable of API response
   */
  public getMonitorRuns(queryParam = {}): Observable<any> {
    this.purgeErrors();
    return this.apiService.get(`web/monitor/run`, queryParam)
      .pipe(map(response => response));
  }

  /**
   * Get monitoring runs list of specific monitoring profile
   * @param ID - Monitoring profile ID
   * @param page - Optional - page number for monitor run
   * @returns Observable of API response
   */
  public getWidgetProfiles(queryParams = {}, redirectTo403Page = true): Observable<any> {
    this.purgeErrors();
    return this.apiService.get(`web/monitor/widget`, queryParams, redirectTo403Page)
      .pipe(map(response => response.data));
  }

  /**
   * Get specific monitor run by ID
   * @param ID Id for MOnitor run
   */
  public getMonitorRun(ID: number): Observable<MonitorRun> {
    // this.purgeErrors();
    return this.apiService.get(`web/monitor/run/` + ID)
      .pipe(map(response => response.data));
  }


  public getMonitorRunResults(runID: number, query = {}): Observable<{data: {documents: Patent[], publications?: Patent[], page: PaginationMetadata}}> {
    this.purgeErrors();
    let param = `show_general=1&show_tags=1`;
    if ("type" in query) {
      param += '&type=' + (query['type'] as Array<string>).join('&type=');
    }
    return this.apiService.get(`web/monitor/run/${runID}/results?${param}`, query)
      .pipe(map(response => response));
  }

  /**
   * Get legal status snapshot Results of specific monitoring run of specific monitoring profile
   * @param query - query parameter object for API call
   * @returns Observable of API response
   */
  public getLegalStatusMonitorResult(runId: number, query = {}): Observable<any> {
    this.purgeErrors();
    return this.apiService.get(`web/monitor/run/${runId}/legal_status/results`, query)
      .pipe(map(response => {
        this.monitorStoreService.setResultSetDocuments(response.data.documents);
        return response;
      }));
  }

  /**
   * Send request for Machine learning Training
   * @param ID - Id of Monitoring profile
   * @param payload - payload for creating monitor run
   * @param is_feedback - whether training request comes from feedback training or normal ML setup
   * @returns - Observable Object represents API response
   */
  public requestTraining(ID: number, payload: Object, is_feedback: boolean): Observable<Object> {
    this.purgeErrors();
    const params = {monitor_profile_id: ID, feedback: is_feedback ? 1 : 0};
    return this.apiService.post(`web/monitor/ml_training`, payload, params);
  }

  /**
   * setError
   *
   * process ERROR from API Call
   * @param errorObj Error object
   * @param message default Message string
   */
  public setError(errorObj: HttpErrorResponse, message: string) {
    const arr = [];
    arr.push(message);
    if (errorObj.error.message.indexOf('Length must be between 0 and 4000.') > -1) {
      arr.push(' > Patent number must be between 0 and 4000.');
    }
    this.setErrors(arr);
  }

  /**
   * Set array of error strings
   * @param errors - array of error strings
   */
  public setErrors(errors): void {
    this.errorsSubject.next(errors);
  }

  /**
   * Reset error array
   */
  public purgeErrors(): void {
    this.errorsSubject.next([]);
  }

  /**
   * Get array of error strings
   * @returns array of error strings
   */
  public getErrors(): Array<string> {
    return this.errorsSubject.getValue();
  }

  /**
   * Set profile section param
   * @param section - name of section
   */
  public setSection(section): void {
    this.sectionSubject.next(section);
  }

  /**
   * Reset section
   */
  public purgeSection(): void {
    this.sectionSubject.next(undefined);
  }

  /**
   * Get section string
   * @returns name of section param
   */
  public getSection(): string {
    return this.sectionSubject.getValue();
  }

  /**
   * Store search results in ML profile
   *
   * add patent number to ML selection list
   * @param id - MOnitor profile ID
   * @param queryParams query parameter
   * @see {@link https://staging.octimine.com/api/v2.0/docs/?selectedApi=2#tag/Monitor/paths/~1web~1monitor~1profile~1{id}~1hash/put
   * @returns Observable of API response
   */
  public mlSelectPatents(ID: number, queryParams: {}): Observable<any> {
    return this.apiService.put(`web/monitor/profile/${ID}/ml/hash`, queryParams);
  }

  /**
   * Store training feedback  in ML profile
   *
   * @see {@link https://api.octimine.de/api/v2.0/docs/?selectedApi=2#tag/Monitor/paths/~1web~1monitor~1profile~1{id}~1ml~1dataset/get}
   */
  public mlStoreDatasetEntry(id: string, payload: MlStoreDatasetParam[]): Observable<any> {
    return this.apiService.post(`web/monitor/profile/${id}/ml/dataset`, payload);
  }

  /**
   * mlDeleteDatasetEntry
   *
   * Delete a dataset entry of a monitor profile (machine learning)
   * @param id monitor profile ID
   * @param documentId document ID
   */
  public mlDeleteDatasetEntry(id: string, documentId: string): Observable<any> {
    return this.apiService.delete(`/web/monitor/profile/${id}/ml/dataset/${documentId}`);
  }

  /**
   * loadMonitorRuns
   *
   * Load the monitor result snapshot list of active monitor profile
   * @param storeService
   * @param loadFirst whether to load document of first snapshot on result page
   * @param pageNumber Page number
   */
  public loadMonitorRuns(storeService: BaseMonitorStoreService, loadFirst: boolean = true, pageNumber: number = 1): Promise<Object> {
    this.purgeErrors();
    return new Promise((resolve, reject) => {
      if (storeService.monitorProfile === null) {
        return reject(false);
      }
      const queryParam = {
        monitor_profile_id: storeService.monitorProfile.id,
        page: pageNumber,
        page_size: this.MONITOR_RUNS_PAGE_SIZE
      }
      this.getMonitorRuns(queryParam)
        .subscribe({
          next: ({data}) => {
            storeService.setMonitorRuns(data);
            if (data['monitor_runs'].some(snapshot => snapshot['status'] !== 'Finished')) {
              const self = this;
              setTimeout(function () {
                self.loadMonitorRuns(storeService, loadFirst, pageNumber).then(() => {
                }).catch(() => {
                });
              }, 3000);
            } else if (storeService.selectedMonitorRun === null || (loadFirst && data.page.total_hits > 0)) {
              storeService.setSelectedMonitorRun(data.monitor_runs[0].id);
              storeService.setMonitorRunID(data.monitor_runs[0].id);
            }
            storeService.loading = false;
            return resolve(data);
          },
          error: (err) => {
            this.setErrors(['Error while getting result set list.']);
            storeService.loading = false;
            return reject(err);
          }
        });
    });
  }

  /**
   * helper function for export option in patent control bar
   */
  public export(hash: string, ids: any, queryParams: any): Observable<any> {
    return this.apiService.asyncExport(`web/export/${hash}`, ids, queryParams);
  }

  /**
   * updateURLRunID
   */
  public updateURLRunID(runID: number) {
    this.updateURL({runID: runID});
  }

  public updateURL(queryParam: { [x: string]: any; } = {}) {
    const pathUrl = this.location.path().split('?')[0] + UrlUtil.queryParamsToUrlString(queryParam);
    this.router.navigateByUrl(pathUrl);
  }

  markRunAsRead(profileId: number): Observable<any> {
    return this.apiService.patch(`web/monitor/shared-run/${profileId}`);
  }

  isLegalStatusProfile(profile: MonitorProfile, isLegalStatusPage): boolean {
    return profile?.legal_status_active || (!profile && isLegalStatusPage);
  }

  isLegalStatusTrackable(publications: Array<string>) {
    return this.apiService.get('web/monitor/legal_status/trackable', {'publication_numbers': publications.join(',')});
  }

  getLegalStatusTracking(publications: Array<string>) {
    return this.apiService.get('web/monitor/legal_status/tracking', {'publication_numbers': publications.join(',')});
  }

  trackLegalStatus(publications: Array<string>) {
    return this.apiService.post('web/monitor/legal_status/tracking', {
      'publication_numbers': publications,
      'scope': LegalStatusPatentListScopeEnum.PUBLICATION
    });
  }

  public getDocuments(): Object[] {
    return this.monitorStoreService.resultSetDocuments;
  }

  getBaseUrl(): string {
    const regExp = /\/monitor(\/legalStatus)?\/profile\/\d+/
    return this.router.url.match(regExp)[0];
  }

  clearStoredData() {
    this.errorsSubject.next([]);
    this.sectionSubject.next(undefined);
  }

  loadLegalStatusTracking(publications: Array<string>) {
    this.monitorStoreService.isLegalStatusReady = false;

    this.isLegalStatusTrackable(publications)
      .pipe(
        take(1),
        switchMap(response => {
          const { not_trackable } = response.data;
          this.monitorStoreService.untrackablePublications = not_trackable;

          return this.getLegalStatusTracking(publications).pipe(take(1));
        }),
        catchError(error => {
          console.error(error);
          return of(null);
        }),
        finalize(() => this.monitorStoreService.isLegalStatusReady = true)
      )
      .subscribe({
        next: response => {
          this.monitorStoreService.alreadyTrackedPublications = response.data;
          this.monitorStoreService.trackablePublications = publications.filter(p => !(p in this.monitorStoreService.alreadyTrackedPublications) && !(p in this.monitorStoreService.untrackablePublications));
          this.monitorStoreService.checkedPublications = [];
        }
      });
  }

  getMonitorRunUrl(profileId: number, isLegalStatusProfile: boolean, runId: number, isOwner: boolean): string {
    const profilePath = isOwner ? 'profile' : 'shared-profile';
    const legalStatusPath = isLegalStatusProfile? '/legalStatus': '';
    const queryParams = {};
    if (runId) {
      queryParams['runID'] = runId;
    }
    const queryParamsStr = UrlUtil.queryParamsToUrlString(queryParams);
    return `monitor${legalStatusPath}/${profilePath}/${profileId}${queryParamsStr}`;
  }

  openNewProfilePage(profile, isLegalStatusPage){
    if (this.isLegalStatusProfile(profile, isLegalStatusPage)) {
      this.router.navigate(['/monitor/legalStatus/profile/new']);
      this.matomoService.monitorLegalStatusPlusCard();
    } else {
      this.router.navigate(['/monitor/profile/new']);
      this.matomoService.monitorTechnologyPlusCard();
    }
  }

  findMonitorRunsPage(profileId: number, runId: number, page: number): Observable<number> {
    const maxPageSize = this.MONITOR_RUNS_PAGE_SIZE * 5;
    return this.getMonitorRuns({monitor_profile_id: profileId, page_size: maxPageSize, page})
      .pipe(
        map(({data}) => {
          if (!data.monitor_runs?.length) {
            return 1;
          }
          const index = data.monitor_runs.findIndex(r => r.id === runId);
          if (index > -1) {
            return Math.ceil((maxPageSize * (page - 1) + index + 1) / this.MONITOR_RUNS_PAGE_SIZE);
          }
          return -1;
        }),
        switchMap((val) => {
          if (val > 0) {
            return of(val);
          }
          return this.findMonitorRunsPage(profileId, runId, page + 1);
        })
      );
  }

  findSharedMonitorRunsPage(profileId: number, runId: number, page: number): Observable<number> {
    const maxPageSize = this.MONITOR_RUNS_PAGE_SIZE * 5;
    return this.getSharedProfile(profileId, {page_size: maxPageSize, page})
      .pipe(
        map(({shared_runs}) => {
          if (!shared_runs?.length) {
            return 1;
          }
          const index = shared_runs.findIndex(r => r.id === runId);
          if (index > -1) {
            return Math.ceil((maxPageSize * (page - 1) + index + 1) / this.MONITOR_RUNS_PAGE_SIZE);
          }
          return -1;
        }),
        switchMap((val) => {
          if (val > 0) {
            return of(val);
          }
          return this.findSharedMonitorRunsPage(profileId, runId, page + 1);
        })
      );
  }
}

