import { Injectable } from '@angular/core';
import { CollaborationStatusEnum } from '@core/models/collaboration.model';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { Observable } from 'rxjs/internal/Observable';
import { map, tap } from 'rxjs/operators';
import { ApiService } from '../api/api.service';
import { PaginationMetadata } from '../semantic-search/types';
import { Notifications } from './types'

@Injectable({
  providedIn: 'root'
})
export class NotificationsService {
  private notificationsSubject = new BehaviorSubject<Array<Notifications>>(null);
  public pagination: PaginationMetadata;

  constructor(private apiService: ApiService) {
  }

  set notifications(value: Array<Notifications>){
    this.notificationsSubject.next(value);
  }

  get notifications(): Array<Notifications>{
    return this.notificationsSubject.getValue();
  }

  loadNotifications(payload): Observable<any> {
    return this.apiService.get(`web/notifications`, payload).pipe(map(resp => resp.data));
  }

  markAllAsRead(): Observable<any> {
    return this.apiService.post('web/notifications/read').pipe(tap(res => {
      if (!this.notifications) {
        return;
      }
      this.notifications.forEach(n => n.status = CollaborationStatusEnum.READ);
    }));
  }

  markAsReadForResource(resourceId: number, resourceType: string): Observable<any> {
    const payload = {
      'resource_id': resourceId,
      'resource_type': resourceType
    }
    return this.apiService.post('web/notifications/read', payload).pipe(tap(res => {
      if (!this.notifications) {
        return;
      }
      this.notifications.filter(n => n.resource_id == resourceId && n.resource_type == resourceType).forEach(n => n.status = CollaborationStatusEnum.READ);
    }));
  }

  markAsRead(notificationId: number): Observable<any> {
    return this.apiService.post(`web/notifications/${notificationId}`);
  }

  isCollectionType(n: Notifications): boolean {
    return n.url.startsWith('/collections/');
  }

  isPatentType(n: Notifications): boolean {
    return n.url.startsWith('/patent/view/');
  }

  isCopiedMonitorType(n: Notifications): boolean {
    return n.url.startsWith('/monitor/');
  }

  isLandscapeType(n: Notifications): boolean {
    return n.url.startsWith('/landscape/');
  }

  isNotificationUnread(n: Notifications): boolean {
    return n.status === CollaborationStatusEnum.NEW;
  }
  resetService() {
    this.notifications = null;
    this.pagination = null;
  }

  clearStoredData() {
    this.notifications = null;
    this.pagination = null;
  }
}
