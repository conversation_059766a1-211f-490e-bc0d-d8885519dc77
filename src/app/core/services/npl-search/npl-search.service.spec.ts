import { TestBed } from '@angular/core/testing';

import { NplSearchService } from './npl-search.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('NplSearchService', () => {
  let service: NplSearchService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(NplSearchService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
