import { EventEmitter, Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { NPL } from './types';
import { PaginationMetadata } from '../semantic-search/types';

@Injectable({
  providedIn: 'root'
})
export class NplSearchService {
  private papersSubject = new BehaviorSubject<Array<NPL>>([]);
  public papers = this.papersSubject.asObservable();
  private chartsSubject = new BehaviorSubject<Record<string, any>>({});
  public chartsEvent = new EventEmitter(true);
  private expandedRowsSubject = new BehaviorSubject<Array<number>>([]);
  private paginationSubject = new BehaviorSubject<PaginationMetadata>(null);
  pageSize = 50;

  constructor(private apiService: ApiService) { }

  search(payload: {}, param: {}, updateDocuments = true): Observable<any> {
    return this.apiService.post('search/npl_v2', payload, param).pipe(
      tap(({data}) => {
        if (updateDocuments) {
          this.papersSubject.next(data.papers);
          this.paginationSubject.next(data.page);
        }
      })
    );
  }

  public getDocuments(): NPL[] {
    return this.papersSubject.value;
  }

  public resetDocuments(): void {
    this.papersSubject.next([]);
  }

  public setDocuments(value): void {
    this.papersSubject.next(value);
  }

  public getPagination(): PaginationMetadata {
    return this.paginationSubject.value;
  }

  public resetPagination(): void {
    this.paginationSubject.next(null);
  }

  public setPagination(value: PaginationMetadata): void {
    this.paginationSubject.next(value);
  }

  public calculateCharts(payload: Object) {
    return this.apiService.post('search/npl_v2/charts', payload)
    .pipe(
        tap(({charts}) => this.chartsSubject.next(charts))
    );
  }

  /**
   * getter for expanded NPL rows reference
   */
  get expandedRows(){
    return this.expandedRowsSubject.getValue();
  }
  /**
   * setter for expanded NPL rows reference
   */
  set expandedRows(value: number[]){
    this.expandedRowsSubject.next(value);
  }
  /**
   * reset expanded NPL rows reference
   */
  resetExpandedRows(){
    this.expandedRowsSubject.next([]);
  }

  /**
   * expand all displayed NPL rows
   */
  expandAllRows(){
    this.expandedRows = this.papersSubject.getValue().map(d => d?.corpus_id);
  }

  resetService(){
    this.resetDocuments();
    this.resetPagination();
    this.resetExpandedRows();
  }

  clearStoredData(): void {
    this.resetService();
    this.chartsSubject.next({});
  }
}
