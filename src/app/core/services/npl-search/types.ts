export interface NPL {
  abstract: string,
  authors: Array<{name:string, author_id:string}>,
  citation_count?: number,
  citations?: Array<any>,
  corpus_id?: number,
  embedding_model?: string,
  embedding_vector?: Array<any>,
  external_ids?: Array<{source?: string, source_id?:string}>,
  fields_of_study?: Array<{category?: string, source?:string}>,
  influential_citation_count?: number,
  is_open_access?: Boolean,
  journal?: {name?:string, pages?: string, volume?: string},
  publication_date?: string,
  publication_types?: Array<string>,
  publication_venue_id?: number,
  rank: number,
  reference_count?: number,
  references?: Array<any>,
  title: string,
  url?: string,
  venue: string,
  year: number  
}

export interface NPLRequestPayload {
  search_input: string,
  search_hash?: string,
}
