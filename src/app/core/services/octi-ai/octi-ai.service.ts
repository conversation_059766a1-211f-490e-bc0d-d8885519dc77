import {Injectable} from '@angular/core';
import {ApiService} from "@core/services";
import {BehaviorSubject, Observable} from "rxjs";
import {map} from "rxjs/operators";
import { PromptType } from './types';

@Injectable({
  providedIn: 'root'
})
export class OctiAiService {

  octiAIHistory: {[key: string]: any[]} = {};
  
  private octiAINewChatSubject = new BehaviorSubject<boolean>(null);
  readonly octiAINewChat$ = this.octiAINewChatSubject.asObservable();
  
  private octiAIHistoryRefreshSubject = new BehaviorSubject<boolean>(null);
  readonly octiAIHistoryRefresh$ = this.octiAIHistoryRefreshSubject.asObservable();

  constructor(
    private apiService: ApiService
  ) {
  }

  askOctiAboutPatent(chatId: string, familyId: number, text: string, publicationNumber: string, timestamp: string, prompt_id: PromptType): Observable<any> {
    const payload = {
      "chat_id": chatId,
      "family_id": familyId,
      "publication_number": publicationNumber,
      "text": text,
      "prompt_id": prompt_id,
      "timestamp": timestamp	
    };
    // TODO: this endpoint will stream response and it need to catch the post request stream
    return this.apiService.post('web/patent/ask_octi', payload);
  }


  askOctiAboutPatentStream(chatId: string, familyId: number, text: string, publicationNumber: string, timestamp: string, prompt_id: PromptType): Observable<any> {
    const payload = {
      "chat_id": chatId,
      "family_id": familyId,
      "publication_number": publicationNumber,
      "text": text,
      "prompt_id": prompt_id,
      "timestamp": timestamp	
    };
    return this.apiService.postStream('web/patent/ask_octi/stream', payload);
  }

  askOctiAboutList(chatId: string, familyIds: number[], text: string, publicationNumbers: string[], timestamp: string, prompt_id: PromptType): Observable<any> {
    const payload = {
      "chat_id": chatId,
      "family_ids": familyIds,
      "publication_numbers": publicationNumbers,
      "text": text,
      "prompt_id": prompt_id,
      "timestamp": timestamp	
    };
    return this.apiService.post('web/patent/list/ask_octi', payload);
  }
  
  askOctiAboutListStream(chatId: string, familyIds: number[], text: string, publicationNumbers: string[], timestamp: string, prompt_id: PromptType): Observable<any> {
    const payload = {
      "chat_id": chatId,
      "family_ids": familyIds,
      "publication_numbers": publicationNumbers,
      "text": text,
      "prompt_id": prompt_id,
      "timestamp": timestamp	
    };
    return this.apiService.postStream('web/patent/list/ask_octi/stream', payload);
  }

  saveChatEvent(chatId: string, event_data: {}, event_type: string, timestamp: string): Observable<any> {
    const payload = {
      "event_data": event_data,
      "event_type": event_type,
      "timestamp": timestamp	
    };
    return this.apiService.post(`web/octi_ai/chat_history/${chatId}/events`, payload);
  }

  getOctiHistory(chatId: string): Observable<any[]> {
    return this.apiService.get(`web/patent/chat_history/${chatId}`).pipe(map(({data}) => data));
  }

  deleteOctiHistory(chatId: string): Observable<any> {
    return this.apiService.delete(`web/patent/chat_history/${chatId}`);
  }

  setNewChat(value: boolean = true){
    this.octiAINewChatSubject.next(value);
  }

  removeHistoryByKey(key: string){
    if(key in this.octiAIHistory) {
      this.octiAIHistory[key] = [];
    }
  }
}
