export interface OctiQuestion {
  promptType: PromptType;
  text?: string;
}

export enum PromptType {
  SUMMARY = "summary",
  EXPLAIN = "explain",
  IMPORTANT= "importance",
  USAGE = "usage",
  CLAIM = "first_claim",
  FEATURES = 'features',
  COMPANIES = 'companies',
  PROBLEMS = 'problems',
  DEFAULT = "default",
  COMPARE = "compare"
};

export interface SingleChat {
  timestamp?: string;
  prompt_id?: PromptType;
  text: string;
  is_reply?: boolean;
}

export interface ChatEvent {
  timestamp?: string;
  data: {event_type: string, event_data: any};
  internal: boolean;
  type: string;
}

export interface PatentData {
  doc_family_id: number;
  publication_number: string;
}