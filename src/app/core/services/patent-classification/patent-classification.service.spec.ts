import { TestBed } from '@angular/core/testing';

import { PatentClassificationService } from './patent-classification.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('PatentClassificationService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])]
  }));

  it('should be created', () => {
    const service: PatentClassificationService = TestBed.inject(PatentClassificationService);
    expect(service).toBeTruthy();
  });
});
