import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PatentClassificationService {

  constructor(private apiService: ApiService) {
  }

  getIpc(params: object) {
    return this.apiService.get('search/classification/ipc', params);
  }

  getCpc(params: object) {
    return this.apiService.get('search/classification/cpc', params);
  }

  getCpcIpc(classification: string, params?: object) {
    return this.apiService.get(`search/classification/${classification}`, params);
  }

  getCpcIpcChildren(classification: string, params?: object) {
    return this.apiService.get(`search/classification/${classification}/children`, params);
  }

  getCpcIpcAncestors(classification: string, params?: object) {
    return this.apiService.get(`search/classification/${classification}/ancestors`, params)
      .pipe(map(resp => resp.data));
  }
}
