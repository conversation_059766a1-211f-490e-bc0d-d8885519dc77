import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { PatentNumberService } from './patent-number.service';
import { RouterModule } from '@angular/router';

describe('PatentNumberService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])]
  }));

  it('should be created', () => {
    const service: PatentNumberService = TestBed.inject(PatentNumberService);
    expect(service).toBeTruthy();
  });
});
