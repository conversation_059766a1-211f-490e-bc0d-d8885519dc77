import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import {
  PatentListRequest,
  PatentListResponse,
  PatentQueryParams,
  PatentNumberSearchRequest,
  PatentNumberSearchTypeEnum
} from './types';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { Patent, Publication } from '@core/models';
import { PatentListScopeEnum, PublicationService } from '@core/services';

@Injectable({
  providedIn: 'root'
})
export class PatentNumberService {
  private documentsSubject = new BehaviorSubject<Array<Patent>>([]);
  public documents = this.documentsSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private publicationService: PublicationService,
  ) {
  }

  public search(
    payload: PatentNumberSearchRequest,
    queryParams?: PatentQueryParams
  ): Observable<any> {
    return this.apiService.post('search/patent_number', payload, queryParams)
      .pipe(
        tap(({data}) => this.documentsSubject.next(payload.search_type === PatentNumberSearchTypeEnum.PUBLICATION ? data.publications : data.documents))
      );
  }

  public getDocuments(): Patent[] {
    return this.documentsSubject.value;
  }

  public setDocuments(patents: Patent[]) {
    return this.documentsSubject.next(patents);
  }

  public resetDocuments(): void {
    this.documentsSubject.next([]);
  }

  public getDocument(document_id: number): Observable<Patent> {
    return this.apiService.get(`search/document/${document_id}`).pipe(map(({data}) => data.document));
  }

  public getPublication(publication_number: string): Observable<Publication> {
    return this.apiService.get(`search/publications/${publication_number}`).pipe(map(({data}) => data.publication));
  }

  public getPatents(payload: PatentNumberSearchRequest, queryParams?: PatentQueryParams): Observable<any> {
    return this.apiService.post('search/patent_number', payload, queryParams).pipe(
      tap(({data}) => {
        if (payload.search_type === PatentNumberSearchTypeEnum.PUBLICATION) {
          data['documents'] = this.publicationService.publicationsToDocuments(data['publications']);
        }
      })
    );
  }

  public getDocumentInfo(familyId: string): Observable<any> {
    return this.apiService.get(`search/document_info/${familyId}`);
  }

  public extractPatentList(payload: PatentListRequest): Observable<PatentListResponse> {
    return this.apiService.post('web/result_collections/extract/patent_list', payload).pipe(map((resp) => resp.data));
  }

  public sendFile(file, params): Observable<Object> {
    return this.apiService.postFormData(`search/parse/patent_list_file`, file, params);
  }

  public getCachedSearch(hash: string, payload?: PatentNumberSearchRequest, queryParams?: PatentQueryParams): Observable<any> {
    return this.apiService.post(`search/hash/${hash}`, payload, queryParams);
  }

  clearStoredData(): void {
    this.resetDocuments();
  }
}
