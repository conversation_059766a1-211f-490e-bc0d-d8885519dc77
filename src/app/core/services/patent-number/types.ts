export interface PatentQueryParams {
  show_analytics?: 1 | 0;
  show_general?: 1 | 0;
  show_bibliographic?: 1 | 0;
  show_fulltext?: 1 | 0;
  show_preprocessed?: 1 | 0;
  show_tags?: 1 | 0,
  page?: number;
  page_size?: number;
}

export enum PatentNumberTypeEnum {
  MIXED = 'mixed',
  APPLICATION_NUMBER = 'application_number',
  PUBLICATION_NUMBER = 'publication_number',
}


export enum PatentNumberSearchTypeEnum {
  FAMILY = 'FAMILY',
  PUBLICATION = 'PUBLICATION',
}

export interface PatentNumberSearchRequest {
  patent_numbers?: Array<string>;
  patent_numbers_type?: PatentNumberTypeEnum;
  search_type?: PatentNumberSearchTypeEnum;
  skip_invalid?: number;
}

export interface PatentListRequest {
  search_hash?: string;
  include_patent_id?: boolean;
  collection_id?: number;
  search_filters?: Object;
}

export interface PatentListResponse {
  patent_id_list?: number[];
  patent_list: string[];
}
