import { Injectable, OnDestroy } from '@angular/core';
import { PatentClassificationService } from '../patent-classification/patent-classification.service';
import { Patent, TeamUser, UserProfile } from '@core/models';
import { BaseStoreService } from '@core/store';
import { take } from 'rxjs/operators';
import { PatentNumberService } from '../patent-number';
import { generateCpcIpcDescription } from '@shared/charts/utils';
import { LegalStatusService } from '@core/services';
import { UserTitlePipe } from '@core/pipes';
import { BehaviorSubject } from 'rxjs';
import { TagModel } from '@core/models/tag.model';

@Injectable({
  providedIn: 'root'
})
export class PatentTableService implements OnDestroy {
  legalStatusFilter: {name: string, value ?: string, unchecked?:boolean}[] = [{name: 'Alive', value: 'valid'}, {name: 'Dead'}, {name: 'Unknown'}];
  selectedLegalStatus: string[] = [];
  private selectedPublications = [];
  private openedPatent = [];
  private ipcClassifications = {};
  private cpcClassifications = {};

  constructor(
    private patentNumberService: PatentNumberService,
    private patentClassificationService: PatentClassificationService,
    private legalStatusService: LegalStatusService
  ) {
  }

  getPublicationNumber(patent: Patent, formatPublicationNumber: boolean = true): string {
    const publicationNumber = patent && patent.general && patent.general.publication_number ?
      patent.general.publication_number?.toString() : 'N/A';
    const rawPublicationNumber = patent && patent.general && patent.general.raw_publication_number ?
      patent.general.raw_publication_number?.toString() : 'N/A';
    const notFormattedPublicationNumber = !formatPublicationNumber && publicationNumber.includes('-') ? publicationNumber : rawPublicationNumber;
    return formatPublicationNumber ? notFormattedPublicationNumber.replace(/-|-/gi, '') : notFormattedPublicationNumber;
  }

  getQueriedPublicationNumber(patent: Patent): string {
    return patent && patent.general && patent.general.original_number_normalized ?
      patent.general.original_number_normalized.replace(/-|-/gi, '') : this.getPublicationNumber(patent);
  }

  getIPC(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.ipc ? patent.bibliographic.ipc.join(', ') : 'N/A';
  }

  getIPC4(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.ipc4 ? patent.bibliographic.ipc4.join(', ') : 'N/A';
  }

  getCPC(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.cpc ? patent.bibliographic.cpc.join(', ') : 'N/A';
  }

  getApplicants(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.applicants ? patent.bibliographic.applicants.join(', ') : 'N/A';
  }

  getApplicantsOriginal(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.applicants_original ? patent.bibliographic.applicants_original.join(', ') : 'N/A';
  }

  getInventors(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.inventors ? patent.bibliographic.inventors.join(', ') : 'N/A';
  }

  getInventorsOriginal(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.inventors_original ? patent.bibliographic.inventors_original.join(', ') : 'N/A';
  }

  getTechAreas(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.tech_areas ? patent.bibliographic.tech_areas.join(', ') : 'N/A';
  }

  getTechFields(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.tech_fields ? patent.bibliographic.tech_fields.join(', ') : 'N/A';
  }

  getAssignees(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.assignees ? patent.bibliographic.assignees.join(', ') : 'N/A';
  }

  getAssigneesOriginal(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.assignees_original ? patent.bibliographic.assignees_original.join(', ') : 'N/A';
  }

  getOwners(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.owners ? patent.bibliographic.owners.map(ow => ow.name).join(', ').toUpperCase() : 'N/A';
  }

  getOwnersIds(patent: Patent): string {
    if (patent && patent.bibliographic) {
      const ids = (patent.bibliographic.owners || []).map(ow => ow.id).filter(id => !!id);
      return ids.length > 0 ? ids.join(', ') : (patent.bibliographic.owner_ids || []).join(', ');
    }
    return 'N/A';
  }

  getUltimateOwners(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.ultimate_owners ? patent.bibliographic.ultimate_owners.map(uw => uw.name).join(', ') : 'N/A';
  }

  getUltimateOwnersIds(patent: Patent): string {
    return patent && patent.bibliographic && patent.bibliographic.ultimate_owners ? patent.bibliographic.ultimate_owners.map(uw => uw.id).join(', ') : 'N/A';
  }

  getAnalyticsValue(patent: Patent): string {
    const analyticsValues = [
      'Bottom 75%', 'Top 25%', 'Top 10%', 'Top 1%'
    ];
    return patent && patent.analytics && patent.analytics.impact && analyticsValues[patent.analytics.impact]
      ? analyticsValues[patent.analytics.impact] : 'N/A';
  }

  selectAllPatent(isSelectAll: boolean, allPatents: any, storeService: BaseStoreService) {
    let updatedPatentIds: number[] = [], updatedPublications: string[] = [];

    if (isSelectAll && allPatents) {
      // remove duplicates
      updatedPatentIds = storeService.selectedPatentIds.filter(id => {
        return allPatents['patent_id_list'].findIndex(o => Number(id) === Number(o)) === -1;
      });
      // concat
      updatedPatentIds = updatedPatentIds.concat(allPatents['patent_id_list']);
      updatedPublications = allPatents['patent_list'];
    }

    this.selectedPublications = updatedPublications;
    storeService.selectedPatentIds = updatedPatentIds;
    storeService.selectedPublications = this.selectedPublications;
    return this.selectedPublications;
  }

  selectVisiblePatent(event, patents: Array<Patent>, storeService: BaseStoreService) {
    this.updateSelectedPatents(event.target.checked, patents, storeService);
    return this.selectedPublications;
  }

  selectPatent(event, patent, storeService: BaseStoreService) {
    this.updateSelectedPatents(event.target.checked, [patent], storeService);
    return this.selectedPublications;
  }

  resetSelectedPatent() {
    this.selectedPublications = [];
  }

  openDetail(docdb_family_id: number, obfuscated: boolean) {
    if (obfuscated) {
      return;
    }

    const elIndex = this.openedPatent.indexOf(docdb_family_id);
    if (elIndex === -1) {
      this.openedPatent.push(docdb_family_id);
    } else {
      this.openedPatent.splice(elIndex, 1);
    }

    return this.openedPatent;
  }

  openAll(patents: Array<Patent>) {
    if (this.openedPatent.length === patents.length) {
      this.openedPatent = [];
      return this.openedPatent;
    }

    this.openedPatent = patents.map(patent => patent.general.docdb_family_id);

    return this.openedPatent;
  }

  closeAll() {
    this.openedPatent = [];
  }

  ngOnDestroy(): void {
    this.openedPatent = [];
    this.selectedPublications = [];
  }

  showBooleanSearchLinkByIPC(patent: Patent, profile: UserProfile, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic.ipc && patent.bibliographic.ipc.length > 0;
  }

  showBooleanSearchLinkByCPC(patent: Patent, profile: UserProfile, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic.cpc && patent.bibliographic.cpc.length > 0;
  }

  showBooleanSearchLinkByInvestors(patent: Patent, profile: UserProfile, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic.inventors && patent.bibliographic.inventors.length > 0;
  }

  showBooleanSearchLinkByApplicants(patent: Patent, profile: UserProfile, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic.applicants && patent.bibliographic.applicants.length > 0;
  }

  showBooleanSearchLinkByAssignees(patent: Patent, profile: UserProfile, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic.assignees && patent.bibliographic.assignees.length > 0;
  }

  showBooleanSearchLinks(patent: Patent, field: string, hasLinksToBooleanSearch: boolean): boolean {
    return hasLinksToBooleanSearch && patent && patent.bibliographic &&
      patent.bibliographic[field] && patent.bibliographic[field].length > 0;
  }
  setLegalStatusFilter(storeService: BaseStoreService){
    if(storeService?.isPublications){
      this.legalStatusFilter = [{name: 'Granted',value: 'valid'}, {name: 'Pending'}, {name: 'Dead'}, {name: 'Unknown'}];
    } else {
      this.legalStatusFilter = [{name: 'Alive', value: 'valid'}, {name: 'Dead'}, {name: 'Unknown'}];
    }
    const o = storeService.filters.find((item) => item.resultTable === 'legalStatus');
    if(o && o.labelValue){ this.legalStatusFilter.forEach(f => { f.unchecked = o.labelValue.indexOf(f.name) === -1; }); }
  }



  getFocal(patent) {
    return patent.citations[0].document_number;
  }

  getLevel(patent) {
    const familyId = patent.citations[0].docdb_family_id;
    const focal = this.patentNumberService.getDocuments().find(fc => fc['general'].docdb_family_id === familyId);
    return focal ? 1 : 2;
  }

  getIpcDescription(ipc: string): string {
    return this.getCpcIpcDescription(this.ipcClassifications[ipc], ipc);
  }

  getCpcDescription(cpc: string): string {
    return this.getCpcIpcDescription(this.cpcClassifications[cpc], cpc);
  }

  getPatentTitle(patent: Patent, showHighlight = false): string {
    if (patent?.highlight?.title && showHighlight) {
      return patent.highlight.title;
    }

    return patent?.bibliographic?.title ? patent.bibliographic.title : 'N/A';
  }

  getPatentAbstract(patent: Patent, showHighlight = false): string {
    if (patent?.highlight?.abstract && showHighlight) {
      return patent.highlight.abstract;
    }

    return patent?.bibliographic?.abstract;
  }

  getPatentClaims(patent: Patent, showHighlight = false): string {
    if (patent?.highlight?.claims && showHighlight) {
      return patent.highlight.claims;
    }

    return patent?.fulltext?.claims;
  }

  getPatentDescription(patent: Patent, showHighlight = false): string {
    if (patent?.highlight?.description && showHighlight) {
      return patent.highlight.description;
    }

    return patent?.fulltext?.description;
  }

  loadIpcClassifications(patent: Patent) {
    let codes: string[] = [];
    if (patent.bibliographic?.ipc) {
      codes = [...codes, ...patent.bibliographic.ipc.filter((ipc) => {
        return !this.ipcClassifications[ipc.toString()] && codes.indexOf(ipc) === -1;
      })];
    }
    if (!codes.length) {
      return;
    }
    const classifications = codes.join(',');
    const params = {classification_symbol: 'in:' + classifications, page_size: 100};

    return this.patentClassificationService.getIpc(params).pipe(take(1)).subscribe({
      next: (resp) => {
        this.ipcClassifications = {...this.ipcClassifications, ...resp.data.results};
      }
    });
  }

  loadCpcClassifications(patent: Patent) {
    let codes: string[] = [];
    if (patent.bibliographic?.cpc) {
      codes = [...codes, ...patent.bibliographic.cpc.filter((cpc) => {
        return !this.cpcClassifications[cpc.toString()] && codes.indexOf(cpc) === -1;
      })];
    }
    if (!codes.length) {
      return;
    }
    const classifications = codes.join(',');
    const params = {classification_symbol: 'in:' + classifications, page_size: 100};

    return this.patentClassificationService.getCpc(params).pipe(take(1)).subscribe({
      next: (resp) => {
        this.cpcClassifications = {...this.cpcClassifications, ...resp.data.results};
      }
    });
  }

  loadIpc4Description(patents: Array<Patent>) {
    if (!patents) {
      return;
    }
    let ipcs: string[] = [];
    patents.forEach(patent => {
      if (patent.bibliographic?.ipc4) {
        ipcs = [...ipcs, ...patent.bibliographic.ipc4.filter((ipc) => {
          return !this.ipcClassifications[ipc.toString()] && ipcs.indexOf(ipc) === -1;
        })];
      }
    });
    if (!ipcs.length) {
      return;
    }
    const classificationsIpc4 = ipcs.join(',');
    const params = {classification_symbol: 'in:' + classificationsIpc4, page_size: 100};

    return this.patentClassificationService.getIpc(params).pipe(take(1)).subscribe({
      next: (classifications) => {
        this.ipcClassifications = {...this.ipcClassifications, ...classifications.data.results};
      }
    });
  }

  setLegalStatusFromHistory(selectedLegalStatuses: string[], storeService: BaseStoreService){
    if (selectedLegalStatuses.length > 0) {
      this.setLegalStatusFilter(storeService);
      selectedLegalStatuses = selectedLegalStatuses.map(s => this.legalStatusService.preProcessLegalStatus(s))
      this.legalStatusFilter.forEach(o => {
        if (!selectedLegalStatuses.includes((o.value?.toLowerCase() || o.name.toLowerCase()))) {
          o.unchecked = true;
        }
      });
      this.selectLegalStatus(storeService);
    }
  }

  selectLegalStatus(storeService: BaseStoreService): void {
    const filters = [...storeService.filters];
    const index = filters.findIndex((item) => item.resultTable === 'legalStatus');

    this.selectedLegalStatus = this.legalStatusFilter.filter(o => !o.unchecked)
      .map(o => o.value?.toLowerCase() || o.name.toLowerCase());

    if (this.legalStatusFilter.every(o => !o.unchecked)) {
      if(index > -1){
        filters.splice(index, 1);
        storeService.setFilters(filters);
      }
      return;
    }

    let filter: any;
    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        resultTable: 'legalStatus',
        title: 'Legal status',
        type: 'result-table',
      };
    }
    if(this.legalStatusFilter.every(o => o.unchecked)){
      filter.query = this.legalStatusService.getLegalStatusQuery(this.legalStatusFilter.map(o => o.value || o.name), true);
      filter.labelValue = this.legalStatusService.getLegalStatusQuery(this.legalStatusFilter.map(o => o.name), true);
    } else {
      filter.query = this.legalStatusService.getLegalStatusQuery(this.legalStatusFilter.filter(o=> !o.unchecked).map(o => o.value || o.name));
      filter.labelValue = this.legalStatusService.getLegalStatusQuery(this.legalStatusFilter.filter(o=> !o.unchecked).map(o => o.name));
    }

    filter.value = filter.query;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    storeService.setFilters(filters);
  }

  collectionSourceTypeFilter(sources: string[], storeService: BaseStoreService) {
    if (sources === null) {
      storeService.setFilters(storeService.filters.filter(f => f.collectionSource !== 'sourceType'));
      return;
    }

    const filter = storeService.filters.find(f => f.collectionSource === 'sourceType');
    if(filter){
      filter.labelValue = sources.join(' OR ');
      filter.value = sources.join(',');
    } else {
      storeService.filters.push({
        collectionSource: 'sourceType',
        title: 'Source collection',
        type: 'collectionSource',
        labelValue: sources.join(' OR '),
        value: sources.join(','),
      });
    }
    storeService.setFilters(storeService.filters);
  }

  collectionSourceUserFilter(sourceUsers: TeamUser[], storeService: BaseStoreService) {
    if (sourceUsers === null) {
      storeService.setFilters(storeService.filters.filter(f => f.collectionSource !== 'sourceUser'));
      return;
    }
    const filter = storeService.filters.find(f => f.collectionSource === 'sourceUser');

    if(filter){
      filter.labelValue = sourceUsers.map(u=> new UserTitlePipe().transform(u)).join(' OR ');
      filter.value = sourceUsers.map(u =>u.id).join(',');
    } else {
      storeService.filters.push({
        collectionSource: 'sourceUser',
        title: 'Source User',
        type: 'collectionSource',
        labelValue: sourceUsers.map(u=> new UserTitlePipe().transform(u)).join(' OR '),
        value: sourceUsers.map(u =>u.id).join(','),
      });
    }

    storeService.setFilters(storeService.filters);
  }

  removeResultTableFilter(filter: any, storeService: BaseStoreService) {
    storeService.setFilters(storeService.filters.filter((item) => item.resultTable !== filter.resultTable));
    this.selectedLegalStatus = [];
  }

  private updateSelectedPatents(checked: boolean, patents: Array<Patent>, storeService: BaseStoreService) {
    // remove any repeated patents form selected patent list
    let updatedPatentIds: number[] = storeService.selectedPatentIds.filter(id => {
      return patents.findIndex(o =>
        Number(id) === Number(o.general.docdb_family_id)) === -1;
    });
    let updatedPublications: string[] = storeService.selectedPublications.filter(p => {
      return patents.findIndex(o =>
        p.replace(/-|-/gi, '') === this.getPublicationNumber(o)) === -1;
    });

    if (checked) {
      // concat
      updatedPatentIds = updatedPatentIds.concat(patents.map(p => Number(p.general.docdb_family_id)))
      updatedPublications = updatedPublications.concat(patents.map(p => this.getPublicationNumber(p, false).toString()))
    }

    this.selectedPublications = updatedPublications;
    storeService.selectedPatentIds = updatedPatentIds;
    storeService.selectedPublications = this.selectedPublications;
  }

  private getCpcIpcDescription(data: any, defaultValue: string): string {
    return generateCpcIpcDescription(data?.descriptions, data?.title || defaultValue);
  }

  clearStoredData(): void {
    this.selectedLegalStatus = [];
    this.selectedPublications = [];
    this.openedPatent = [];
    this.ipcClassifications = {};
    this.cpcClassifications = {};
  }
}
