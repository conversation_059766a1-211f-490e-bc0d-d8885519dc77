import { EventEmitter, Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';

@Injectable({
  providedIn: 'root'
})
export class PatentViewModeService {
  currentMode: EventEmitter<PatentSideBarViewModeEnum> = new EventEmitter(true);
  currentPatentUrl: string;

  hasResultList: boolean = false;
  notFound: boolean = false;

  constructor() {
  }

  clearStoredData(): void {
    this.currentPatentUrl = null;
    this.hasResultList = false;
  }
}
