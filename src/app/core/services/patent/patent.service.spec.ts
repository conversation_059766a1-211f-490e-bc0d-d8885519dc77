import { TestBed } from '@angular/core/testing';

import { PatentService } from './patent.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('PatentService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])]
  }));

  it('should be created', () => {
    const service: PatentService = TestBed.inject(PatentService);
    expect(service).toBeTruthy();
  });

  it('should get family legal status icon', () => {
    const service: PatentService = TestBed.inject(PatentService);
    expect(service.getFamilyLegalStatusIcon({legal_status: ['valid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['pending']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['valid', 'valid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['valid', 'unknown']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['valid', 'invalid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['pending', 'invalid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['valid', 'invalid', 'unknown']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid']}).icon).toBe('dot-danger');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid', 'invalid', 'invalid']}).icon).toBe('dot-danger');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid', 'invalid', 'unknown']}).icon).toBe('dot-mix');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid', 'invalid', 'valid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid', 'invalid', 'pending']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['invalid', 'unknown', 'valid']}).icon).toBe('dot-primary');
    expect(service.getFamilyLegalStatusIcon({legal_status: ['unknown', 'unknown']}).icon).toBe('dot-light');
    expect(service.getFamilyLegalStatusIcon({legal_status: []}).icon).toBe('dot-light');
    expect(service.getFamilyLegalStatusIcon({}).icon).toBe('dot-light');
    expect(service.getFamilyLegalStatusIcon(null).icon).toBe('dot-light');
  });

  it('should get patent legal status icon', () => {
    const service: PatentService = TestBed.inject(PatentService);
    expect(service.getPatentLegalStatusIcon({legal_status: 'valid', legal_status_extended: 'granted'}).icon).toBe('dot-primary');
    expect(service.getPatentLegalStatusIcon({legal_status: 'pending', legal_status_extended: 'pending'}).icon).toBe('dot-pending');
    expect(service.getPatentLegalStatusIcon({legal_status: 'pending', legal_status_extended: 'granted', grant_legal_status: 'valid', grant_legal_status_extended: 'granted'}).icon).toBe('dot-primary');
    expect(service.getPatentLegalStatusIcon({legal_status: 'expired'}).icon).toBe('dot-danger');
    expect(service.getPatentLegalStatusIcon({legal_status: null}).icon).toBe('dot-light');
    expect(service.getPatentLegalStatusIcon({}).icon).toBe('dot-light');
    expect(service.getPatentLegalStatusIcon(null).icon).toBe('dot-light');
  });
});
