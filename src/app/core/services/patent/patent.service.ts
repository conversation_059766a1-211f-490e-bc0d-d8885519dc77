import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Patent, LegalStatusIcon } from '@core/models';
import { PdfViewerDialogComponent } from '../../../shared/components/pdf-viewer-dialog/pdf-viewer-dialog.component';
import { COUNTRY_FLAG, FlagSizeEnum } from './utils/countryCode';
import { PatentLegalStatusEnum } from '@patent/types';

@Injectable({
  providedIn: 'root'
})
export class PatentService {


  readonly VALID_LEGAL_STATUSES = [
    'valid',
    'active',
    'active_reinstated',
    'granted',
    'in_force'
  ];

  readonly INVALID_LEGAL_STATUSES = [
    'invalid',
    'expired',
    'expired_fee_related',
    'expired_lifetime',
    'withdrawn',
    'withdrawn_after_issue',
    'abandoned',
    'ceased',
    'revoked',
    'not_in_force'
  ];

  readonly UNKNOWN_LEGAL_STATUSES = [
    'unknown'
  ];

  readonly PENDING_LEGAL_STATUSES = [
    'pending'
  ];

  constructor(private apiService: ApiService, private modalService: NgbModal) {
  }

  public getPatentImage(query = {}) {
    return this.apiService.get('search/attachment/image', query);
  }

  public getListImage(payload: any) {
    payload['include_blobs'] = 1;
    return this.apiService.get('search/attachment/image/list', payload, false);
  }

  public getPdf(query = {}) {
    return this.apiService.getPdf('search/attachment/pdf', query);
  }

  public getListPdf(query = {}) {
    return this.apiService.get('search/attachment/pdf/list', query, false);
  }

  public downloadFile(blob: Blob, name: string, patent: Patent) {
    let type = '';
    switch (blob.type) {
      case 'application/pdf':
        type = 'pdf';
        break;
      case 'text/csv':
        type = 'csv';
        break;

      default:
        type = 'xlsx';
        break;
    }
    if (!name) {
      name = 'export';
    }

    if (blob.type === 'application/pdf') {
      const modalRef = this.modalService.open(PdfViewerDialogComponent, {
        windowClass: 'modal-pdf-viewer',
        size: 'xl',
        container: 'body'
      });
      modalRef.componentInstance.name = name;
      modalRef.componentInstance.patent = patent;
      modalRef.componentInstance.src = blob;

      return;
    }

    const ua = navigator.userAgent;

    const msie = ua.indexOf('MSIE');
    const trident = ua.indexOf('Trident/');
    const edge = ua.indexOf('Edge/');

    if (msie > 0 || trident > 0 || edge > 0) {
      (navigator as any).msSaveOrOpenBlob(blob, name + '.' + type);
      return;
    } else {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = name + '.' + type;
      document.body.appendChild(link);
      link.click();
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      }, 1000);
      return;
    }
  }

  /**
   * getFamilyLegalStatus
   *
   * load legal status of patent family members
   * @param familyID family id for a specific patent
   */
  public getFamilyLegalStatus(familyID: string) {
    return this.apiService.get(`search/legal_status/${familyID}`);
  }

  /**
   * getFamilyLegalStatus
   *
   * load document info (legal status, legal events, etc) of patent family members
   * @param familyID family id for a specific patent
   */
  public getDocumentInfo(familyID: string) {
    return this.apiService.get(`search/document_info/${familyID}`);
  }

  public getFamilyLegalStatusIcon(family: any, mixDeadUnknown = false): LegalStatusIcon {
    let icon: LegalStatusIcon = {
      icon: 'dot-light',
      name: 'Unknown',
      tooltip: 'Legal status is unknown',
      description: null
    };
    let legalStatus = family ? family['legal_status'] : null;
    if (legalStatus) {
      if (typeof (legalStatus) === 'string') {
        legalStatus = [legalStatus];
      }

      const legalStatusSet = new Set<string>(legalStatus);

      if (legalStatusSet.size > 0) {
        const legalStatusArray = [...legalStatusSet];
        const hasGrantedOrPending = legalStatusArray.some((stt) => this.VALID_LEGAL_STATUSES.includes(stt) || this.PENDING_LEGAL_STATUSES.includes(stt));
        const hasUnknown = legalStatusArray.some(stt => this.UNKNOWN_LEGAL_STATUSES.includes(stt));
        const allUnknown = legalStatusArray.every(stt => this.UNKNOWN_LEGAL_STATUSES.includes(stt));

        if (hasGrantedOrPending) {
          icon.icon = 'dot-primary';
          icon.name = 'Alive';
          icon.tooltip = 'The family has at least one granted or pending member';
        } else if (allUnknown) {
          icon.icon = 'dot-light';
          icon.name = 'Unknown';
          icon.tooltip = !mixDeadUnknown ? 'The family has only unknown members' : 'The family has members with unknown legal status';
        } else if (hasUnknown) {
          icon.icon = 'dot-mix';
          icon.name = 'Dead / Unknown';
          icon.tooltip = 'The family has no granted or pending members, but includes unknown ones';
        } else {
          icon.icon = 'dot-danger';
          icon.name = 'Dead';
          icon.tooltip = 'The family has no granted or pending members';
        }
      }
    }
    return icon;
  }

  public getPatentLegalStatus(patent: any): PatentLegalStatusEnum {
    if (!patent) {
      return PatentLegalStatusEnum.UNKNOWN;
    }

    const legalStatus = patent['grant_legal_status'] ? patent['grant_legal_status'] : patent['legal_status'];
    if (!legalStatus) {
      return PatentLegalStatusEnum.UNKNOWN;
    }

    if (this.INVALID_LEGAL_STATUSES.includes(legalStatus)) {
      return PatentLegalStatusEnum.DEAD;
    }

    if (this.VALID_LEGAL_STATUSES.includes(legalStatus)) {
      return PatentLegalStatusEnum.GRANTED;
    }

    if (this.PENDING_LEGAL_STATUSES.includes(legalStatus)) {
      return PatentLegalStatusEnum.PENDING;
    }

    return PatentLegalStatusEnum.UNKNOWN;
  }

  public getPatentLegalStatusIcon(patent: any): LegalStatusIcon {
    const legalStatus = this.getPatentLegalStatus(patent);
    const icon: LegalStatusIcon = {icon: 'dot-light', name: legalStatus, tooltip: 'Legal status information not available', description: null};
    if (legalStatus === PatentLegalStatusEnum.UNKNOWN) {
      return icon;
    }
    const legal_status_extended = patent['legal_status_extended'] ? patent['legal_status_extended'] : patent['legal_status'];
    let grant_legal_status_extended = patent['grant_legal_status_extended'] ? patent['grant_legal_status_extended'] : patent['grant_legal_status'];
    grant_legal_status_extended = grant_legal_status_extended?.split('_').join(' ');
    const publication_type = patent['publication_type'];
    if (legal_status_extended) {
      icon.description = legal_status_extended.split('_').join(' ');
    }

    if (legalStatus === PatentLegalStatusEnum.DEAD) {
      icon.icon = 'dot-danger';
      icon.name = 'Dead';
    }
    else if (legalStatus === PatentLegalStatusEnum.GRANTED) {
      icon.icon = 'dot-primary';
      icon.name = 'Granted';
    }
    else if (legalStatus === PatentLegalStatusEnum.PENDING) {
      icon.icon = 'dot-pending';
      icon.name = 'Pending';
    }

    if (publication_type === 'application') {
      switch (legal_status_extended) {
        case 'granted':
          icon.tooltip = `The application is granted`;
          break;
        case 'pending':
          icon.tooltip = `The application is under examination`;
          break;
        case 'withdrawn':
          icon.tooltip = `The applicant has withdrawn the application`;
          break;
        case 'abandoned':
          icon.tooltip = `The applicant has abandoned the application`;
          break;
        case 'ceased':
          icon.tooltip = `The patent office has refused, terminated before grant, or rejected the application`;
          break;
        case 'withdrawn_after_issue':
          icon.tooltip = `The applicant has withdrawn the granted application`;
          break;
        case 'expired_lifetime':
          icon.tooltip = `The application has exceeded its lifetime limit`;
          break;
        case 'revoked':
          icon.tooltip = `The patent office has revoked the application`;
          break;
        default:
          icon.tooltip = `Information not provided by the patent office`;
          break;
      }
    }
    else {
      switch (legal_status_extended) {
        case 'active':
          icon.tooltip = `The patent/utility model is active`;
          break;
        case 'active_reinstated':
          icon.tooltip = `The patent office has reinstated the patent/utility model`;
          break;
        case 'expired_fee_related':
          icon.tooltip = `The patent/utility model annuity or maintenance fee payment has been missed`;
          break;
        case 'expired':
          icon.tooltip = `The patent/utility model is expired`;
          break;
        case 'expired_lifetime':
          icon.tooltip = `The patent/utility has exceeded its lifetime limit`;
          break;
        case 'ceased':
          icon.tooltip = `The patent office has ceased the patent/utility model`;
          break;
        case 'withdrawn_after_issue':
          icon.tooltip = `The applicant has withdrawn the granted patent/utility model`;
          break;
        case 'in_force':
          icon.tooltip = `The patent/utility model is active in at least one designated country`;
          break;
        case 'not_in_force':
          icon.tooltip = `The patent/utility model is not active in any of the designated countries`;
          break;
        case 'revoked':
          icon.tooltip = `The patent office has revoked the patent/utility model`;
          break;
        default:
          icon.tooltip = `Information not provided by the patent office`;
          break;
      }
    }
    return icon;
  }


  getFlagCssByPublication(publication: string, size: FlagSizeEnum | '' = ''): string{
    if(publication){
      const arr = publication.match(/^[A-Za-z]{2}/i)
      if(arr && arr[0]){
        if (arr[0].toLowerCase() === 'ep' && publication.toLowerCase().endsWith('c0')) {
          return `flag-icon-border fi fi-upc ${size}`;
        }

        return COUNTRY_FLAG(arr[0], size);
      }
    }
    return '';
  }

  showSubsidiary(patent: Patent): boolean {
    if (!patent?.bibliographic?.ultimate_owners?.length){
      return false;
    }
    if (patent.bibliographic.owners?.length === 1 && patent.bibliographic.ultimate_owners.length === 1 &&
      patent.bibliographic.ultimate_owners[0].id === patent.bibliographic.owners[0].id) {
      return false;
    }
    return true;
  }

  getLegalStatuses(document) {
    let legal_status = document.bibliographic.legal_status;
    const grant_legal_status = document.bibliographic.grant_legal_status;
    const legal_status_extended = document.bibliographic.legal_status_extended;
    const grant_legal_status_extended = document.bibliographic.grant_legal_status_extended;
    const publication_type = document.bibliographic.publication_type;
    if (typeof legal_status === 'string') {
      legal_status = [{
        legal_status,
        publication_type,
        grant_legal_status,
        legal_status_extended,
        grant_legal_status_extended
      }];
    } else {
      legal_status = legal_status?.map(ls => {
        return {
          legal_status: ls,
          publication_type,
          grant_legal_status,
          legal_status_extended,
          grant_legal_status_extended
        };
      });
    }
    if (legal_status === null || legal_status === undefined || legal_status.length === 0) {
      legal_status = [{
        legal_status: 'unknown',
        publication_type,
        grant_legal_status,
        legal_status_extended,
        grant_legal_status_extended
      }];
    }
    return legal_status;
  }

  getAmountOfPublicationByAuthority(publications: string[], authority: string): number {
    return publications?.filter(pub => pub.startsWith(authority)).length;
  }
}
