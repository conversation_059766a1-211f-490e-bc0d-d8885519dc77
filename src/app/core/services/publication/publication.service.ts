import { Injectable } from '@angular/core';
import { Patent } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class PublicationService {

  constructor() { }


  publicationsToDocuments(publications: Object[]): Patent[] {
    if (!publications) {
      return [];
    }

    return publications.map((publication) => this.publicationToDocument(publication));
  }

  publicationToDocument(publication: Object): Patent {
    if (!publication) {
      return null;
    }

    const originalNumber = publication['general']['original_number_normalized'] ?? publication['general']['original_number'];
    let publicationNumber = publication['general']['publication_number'] ?? originalNumber;
    publicationNumber = publicationNumber ? publicationNumber.replace(/-|-/gi, '') : null


    return {
      ...publication,
      general: {
        ...publication['general'],
        raw_publication_number: publicationNumber,
        original_number_normalized: originalNumber,
        similarity_index: 0,
        term_weights: {},
        green_codes: [],
      },
    } as Patent;
  }
}
