import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { PaginationMetadata } from '../semantic-search/types';
import { Patent } from '@core/models/patent.model';

@Injectable({
  providedIn: 'root'
})
export class RatingService {

  linkData = {
    title: 'Rating documents',
    previousUrl: 'ratings',
    backPatentSearch: true
  };

  private reloadRatingColumnSubject = new BehaviorSubject<boolean>(false);
  readonly reloadRatingColumn$ = this.reloadRatingColumnSubject.asObservable();


  constructor(
    private apiService: ApiService
  ) {
  }

  get reloadRatingColumn(): boolean {
    return this.reloadRatingColumnSubject.getValue();
  }

  set reloadRatingColumn(value: boolean) {
    this.reloadRatingColumnSubject.next(value);
  }

  getRatingDocuments(params?: {}): Observable<{ documents: Array<Patent>, page: PaginationMetadata }> {
    return this.apiService.get('web/tasks/documents', params).pipe(map(response => response.data));
  }

  clearStoredData(): void {
  }
}
