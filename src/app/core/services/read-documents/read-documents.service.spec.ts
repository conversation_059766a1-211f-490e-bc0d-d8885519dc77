import { TestBed } from '@angular/core/testing';

import { ReadDocumentsService } from './read-documents.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ReadDocumentsService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers: [provideMatomo({
      siteId: '7',
      trackerUrl: 'https://stats.dennemeyer.digital/',
      disabled: true
    })]
  }));

  it('should be created', () => {
    const service: ReadDocumentsService = TestBed.inject(ReadDocumentsService);
    expect(service).toBeTruthy();
  });
});
