import { Injectable } from '@angular/core';
import { ApiService } from '../api/api.service';
import { concatMap, map, tap } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { ReadDocument, TeamReadDocumentsResponse, TeamUser, TeamUserTypeEnum } from '@core/models';
import { UserService } from '@core/services';

@Injectable({
  providedIn: 'root'
})
export class ReadDocumentsService {

  constructor(
    private apiService: ApiService,
    private userService: UserService
  ) {
  }

  markAsRead(docdb_family_id: number) {
    if (!docdb_family_id) {
      return;
    }
    return this.apiService.put(`web/read_document/${docdb_family_id}`);
  }

  getDocuments(payload) {
    return this.apiService.get(`web/read_document`, payload);
  }

  markAsUnread(docdb_family_id: number) {
    if (!docdb_family_id) {
      return;
    }
    return this.apiService.delete(`web/read_document/${docdb_family_id}`);
  }

  markAllAsUnread() {
    return this.apiService.delete(`web/read_document`);
  }

  markSelectedAsUnread(document_ids: number[]) {
    return this.apiService.post(`web/read_document/delete`, {document_ids});
  }

  getTeamReadDocuments(payload): Observable<TeamReadDocumentsResponse> {
    return this.apiService.get(`web/read_document/team`, payload)
      .pipe(
        map(({data}) => data as TeamReadDocumentsResponse),
        concatMap((resp) => {
          return this.getUsersForReadDocuments(resp.read_documents)
            .pipe(
              map(() => resp)
            );
        })
      );
  }

  getUsersForReadDocuments(readDocuments: ReadDocument[]): Observable<ReadDocument[]> {
    if (!readDocuments?.length) {
      return of([]);
    }
    const usersPayload = {
      load_all: 1,
      include_me: 1,
      include_blocked: 1,
      id: 'in:' + readDocuments.map((rd) => rd.user_id).join(',')
    };
    return this.userService.getTeamUsers(usersPayload)
      .pipe(
        map(({users}) => users.map((u) => {
          return {...u, type: TeamUserTypeEnum.USER} as TeamUser;
        })),
        tap((users) => {
          const usersMap = new Map(users.map((u) => [u.id, u]));

          readDocuments.forEach((rd) => {
            rd.user = usersMap.get(rd.user_id);
          });
        }),
        map((users) => readDocuments)
      );
  }
}
