import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { SettingsService } from '../settings/settings.service';

@Injectable({
  providedIn: 'root'
})
export class RecaptchaService {
  private tokenSubject = new BehaviorSubject<string>(null);
  readonly token = this.tokenSubject.asObservable();

  private RECAPTCHA_ELE_ID = 'recaptcha';

  constructor(
    private settingsService: SettingsService
  ) {
  }

  getToken() {
    window['grecaptcha'].ready(() => {
      window['grecaptcha'].execute(this.settingsService.settings.recaptchaSiteKey, {
        action: 'submit'
      }).then((token) => {
        this.tokenSubject.next(token);
      });
    });
  }

  addJs() {
    this.clear();

    const ref = document.getElementsByTagName('script')[0];

    const js = document.createElement('script');
    js.id = this.RECAPTCHA_ELE_ID;
    js.async = true;
    js.src = `https://www.google.com/recaptcha/api.js?render=${this.settingsService.settings.recaptchaSiteKey}`;

    ref.parentNode.insertBefore(js, ref);
  }

  clear() {
    const grecaptchaEle = document.querySelector('.grecaptcha-badge');

    if (grecaptchaEle) {
      grecaptchaEle.remove();
    }

    const scripts = document.getElementsByTagName('script');
    for (let i = 0; i < scripts.length; i++) {
      const tag = scripts[i];
      const src = tag.getAttribute('src');
      if (src && src.indexOf('recaptcha') > -1) {
        tag.remove();
      }
    }

    const recaptchaEle = document.getElementById(this.RECAPTCHA_ELE_ID);
    if (recaptchaEle) {
      recaptchaEle.remove();
    }
  }

  clearStoredData(): void {
    this.tokenSubject.next(null);
  }
}
