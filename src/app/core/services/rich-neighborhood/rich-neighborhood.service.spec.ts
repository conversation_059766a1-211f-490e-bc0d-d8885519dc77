import { TestBed } from '@angular/core/testing';

import { RichNeighborhoodService } from './rich-neighborhood.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('RichNeighborhoodService', () => {
  let service: RichNeighborhoodService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])]
    });
    service = TestBed.inject(RichNeighborhoodService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
