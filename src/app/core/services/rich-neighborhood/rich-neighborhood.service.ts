import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../api/api.service';
import { LandscapeRniResultResponse } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class RichNeighborhoodService {

  constructor(
    private apiService: ApiService
  ) {
  }

  calculateRichNeighborhood(landscapeProfileId: number):
    Observable<LandscapeRniResultResponse> {
    return this.apiService.post(`web/landscape/profile/${landscapeProfileId}/rni_results`);
  }

  getRichNeighborhoodResults(landscapeProfileId: number): Observable<LandscapeRniResultResponse> {
    return this.apiService.get(`web/landscape/profile/${landscapeProfileId}/rni_results`);
  }
}
