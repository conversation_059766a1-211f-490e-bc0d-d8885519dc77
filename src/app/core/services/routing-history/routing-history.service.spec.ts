import { TestBed } from '@angular/core/testing';
import { ReplaySubject } from 'rxjs';
import { GuardsCheckStart, Router, RouterEvent } from '@angular/router';
import { RoutingHistoryService } from '@core/services';


describe('RoutingHistoryService', () => {
  let service: RoutingHistoryService;
  const eventSubject = new ReplaySubject<RouterEvent>(1);

  const routerMock = {
    navigate: jasmine.createSpy('navigate'),
    events: eventSubject.asObservable(),
    url: 'test/url'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [],
      providers: [
        {provide: Router, useValue: routerMock}
      ],
      imports: []
    });

    service = TestBed.inject(RoutingHistoryService);
    service.subscribeHistories();
  });

  beforeEach(function () {
    let store = {};

    spyOn(localStorage, 'getItem').and.callFake((key) => {
      return store[key];
    });

    spyOn(localStorage, 'setItem').and.callFake((key, value) => {
      return store[key] = value + '';
    });

    spyOn(localStorage, 'clear').and.callFake(() => {
      store = {};
    });

    localStorage.clear();
  });

  it('should be created', () => {
    const sv: RoutingHistoryService = TestBed.inject(RoutingHistoryService);
    expect(sv).toBeTruthy();
  });

  it('provide login url returns search patent url', () => {
    eventSubject.next(new GuardsCheckStart(1, 'regular', '/auth/login', null));

    expect(service.getReferralUrl()).toBe('/launchpad');
  });

  it('provide one url returns search patent url', () => {
    eventSubject.next(new GuardsCheckStart(1, 'regular', '/citation', null));

    expect(service.getReferralUrl()).toBe('/launchpad');
  });

  it('provide citation url returns search patent url', () => {
    eventSubject.next(new GuardsCheckStart(1, 'regular', '/auth/login', null));
    eventSubject.next(new GuardsCheckStart(2, 'regular', '/citation', null));

    expect(service.getReferralUrl()).toBe('/launchpad');
  });

  it('provide login url returns citation url', () => {
    eventSubject.next(new GuardsCheckStart(1, 'regular', '/citation', null));
    eventSubject.next(new GuardsCheckStart(2, 'regular', '/auth/login', null));

    expect(service.getReferralUrl()).toBe('/citation');
  });

  it('provide login url returns monitor url', () => {
    eventSubject.next(new GuardsCheckStart(1, 'regular', '/search/patent', null));
    eventSubject.next(new GuardsCheckStart(2, 'regular', '/citation', null));
    eventSubject.next(new GuardsCheckStart(3, 'regular', '/monitor', null));
    eventSubject.next(new GuardsCheckStart(4, 'regular', '/auth/login', null));

    expect(service.getReferralUrl()).toBe('/monitor');
  });

  it('provide a string without a question mark returns itself', () => {
    const url = '/search/patent?search=12345';

    eventSubject.next(new GuardsCheckStart(1, 'regular', url, null));
    eventSubject.next(new GuardsCheckStart(2, 'regular', '/auth/login', null));

    expect(service.getReferralUrl()).toBe(url);
  });

  it('doesn\'t redirect to auth pages', () => {
    const url = '/auth/forgot-password';

    eventSubject.next(new GuardsCheckStart(1, 'regular', url, null));
    eventSubject.next(new GuardsCheckStart(2, 'regular', '/auth/login', null));

    expect(service.getReferralUrl()).toBe('/launchpad');
  });

  it('provide one urls returns 1', () => {
    const urls = ['/citation'];
    for (const url of urls) {
      service.saveHistoryUrl(url);
    }

    expect(service.getHistoryUrls()).toEqual(urls);
  });

  it('provide two urls returns 2', () => {
    const urls = ['/citation', '/profile'];
    for (const url of urls) {
      service.saveHistoryUrl(url);
    }

    expect(service.getHistoryUrls()).toEqual(['/profile', '/citation']);
  });

  it('provide three urls with a duplicated one returns 2', () => {
    const urls = ['/citation', '/profile', '/profile'];
    for (const url of urls) {
      service.saveHistoryUrl(url);
    }

    expect(service.getHistoryUrls()).toEqual(['/profile', '/citation']);
  });

  it('provide three urls with a duplicated one returns 2', () => {
    const urls = ['/citation', '/profile', '/citation'];
    for (const url of urls) {
      service.saveHistoryUrl(url);
    }

    expect(service.getHistoryUrls()).toEqual(['/citation', '/profile']);
  });
});
