import { Injectable } from '@angular/core';
import { GuardsCheckStart, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class RoutingHistoryService {
  private previousUrlsKey = 'previousUrlsKey';

  private LOGIN_URL = '/auth/login';
  private START_URL = '/launchpad';
  private subscriptions = new Subscription();

  constructor(
    private router: Router
  ) {
  }

  public subscribeHistories(): void {
    localStorage.removeItem(this.previousUrlsKey);
    const router$ = this.router.events
      .pipe(filter(event => event instanceof GuardsCheckStart))
      .subscribe({
        next: ({urlAfterRedirects}: GuardsCheckStart) => {
          this.saveHistoryUrl(urlAfterRedirects);
        }
      });
    this.subscriptions.add(router$);
  }

  public unsubscribeHistories(): void {
    this.subscriptions.unsubscribe();
  }

  public getReferralUrl(): string {
    const historyUrls = this.getHistoryUrls();
    const previousUrl = this.getPreviousUrl();

    if (this.isLoginUrl(previousUrl) && historyUrls.length >= 2) {
      const url = historyUrls[1];
      if (url.indexOf('/auth') === -1) {  // Never redirect to auth pages
        return decodeURI(url);
      }
    }
    return this.START_URL;
  }

  public saveHistoryUrl(url: string): void {
    const historyUrls = this.getHistoryUrls();
    const previousUrl = this.getPreviousUrl();

    if (previousUrl !== url) {
      historyUrls.unshift(url);
    }

    const newUrls = historyUrls.slice(0, 2).join(',');

    localStorage.setItem(this.previousUrlsKey, newUrls);
  }

  public getHistoryUrls(): Array<string> {
    const previousUrlsStr = localStorage.getItem(this.previousUrlsKey);
    return previousUrlsStr ? previousUrlsStr.split(',') : [];
  }

  private getPreviousUrl(): string {
    const historyUrls = this.getHistoryUrls();

    if (historyUrls.length > 0) {
      return decodeURI(historyUrls[0]);
    }

    return null;
  }

  public purgePreviousUrls() {
    localStorage.removeItem(this.previousUrlsKey);
  }

  private isLoginUrl(url: string): boolean {
    return this.LOGIN_URL === url;
  }
}
