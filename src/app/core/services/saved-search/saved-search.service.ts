import { Injectable } from '@angular/core';
import { TruncatePipe } from '@core/pipes';

@Injectable({
  providedIn: 'root'
})
export class SavedSearchService {

  constructor(
    private truncatePipe: TruncatePipe
  ) {
  }

  toHumanReadable(data: {}, type: string, isTruncated = false, limit = 255): string {
    switch (type) {
      case 'semantic':
        return this.semanticToHumanReadable(data, isTruncated, limit);
      case 'boolean':
        return this.booleanToHumanReadable(data, isTruncated, limit);
      case 'citation':
        return this.citationToHumanReadable(data, isTruncated, limit);
    }

    return null;
  }

  private semanticToHumanReadable(data, isTruncated, limit): string {
    if (!data) {
      return null;
    }

    const results = [];

    if (data.search_input) {
      const value = isTruncated ? this.truncatePipe.transform(data.search_input, limit, true) : data.search_input;
      results.push(`SEARCH_INPUT=${value}`);
    }

    if (data.patent_numbers) {
      const patentNumbers = data.patent_numbers.join(',');
      const value = isTruncated ? this.truncatePipe.transform(patentNumbers, limit, true) : patentNumbers;
      results.push(`PATENT_NUMBERS=${value}`);
    }

    return results.join(' AND ');
  }

  private booleanToHumanReadable(data, isTruncated, limit): string {
    if (data && data.search_input) {
      return isTruncated ? this.truncatePipe.transform(data.search_input, limit, true) : data.search_input;
    }

    return null;
  }

  private citationToHumanReadable(data, isTruncated, limit): string {
    if (data && data.patent_numbers) {
      const patentNumbers = data.patent_numbers.join(',');
      const value = isTruncated ? this.truncatePipe.transform(patentNumbers, limit, true) : patentNumbers;
      return `PATENT_NUMBERS=${value}`;
    }

    return null;
  }
}
