import { TestBed } from '@angular/core/testing';

import { SearchHistoryService } from './search-history.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TruncatePipe } from '@core/pipes';
import { provideMatomo } from 'ngx-matomo-client';

describe('SearchHistoryService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [
      HttpClientTestingModule,
      RouterModule.forRoot([])
    ],
    providers: [
      TruncatePipe, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      })
    ]
  }));

  it('should be created', () => {
    const service: SearchHistoryService = TestBed.inject(SearchHistoryService);
    expect(service).toBeTruthy();
  });
});
