import {Injectable} from '@angular/core';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import {ApiService} from '../api/api.service';
import {TruncatePipe} from '@core/pipes';
import {map} from 'rxjs/operators';
import {Router} from '@angular/router';
import {
  BaseStoreService,
  BooleanSearchStoreService,
  CitationSearchStoreService,
  SemanticSearchStoreService,
  NplSearchStoreService
} from '@core/store';
import {
  AdvancedFilterService,
  PaginationMetadata, PatentListScopeEnum,
  PatentTableService,
  SearchHistory,
  UserService
} from '@core/services';
import {translatableLanguages} from "@core/data";

@Injectable({
  providedIn: 'root'
})
export class SearchHistoryService {
  private searchFilterFields = {
    quantity_cut_off: 'Quantity Cut-Off',
    similarity_cut_off: 'Similarity Cut-Off',
    inclusion_and: 'Inclusion (AND)',
    inclusion_or: 'Inclusion (OR)',
    exclusion_and: 'Exclusion (AND)',
    exclusion_or: 'Exclusion (OR)',
    applicants_plus: 'Applicant (+)',
    applicants_minus: 'Applicant (-)',
    cpc_plus: 'CPC Code (+)',
    cpc_minus: 'CPC Code (-)',
    ipc_plus: 'IPC Code (+)',
    ipc_minus: 'IPC Code (-)',
    earliest_priority_date: 'Earliest Priority Date',
    latest_priority_date: 'Latest Priority Date',
    earliest_publication_date: 'Earliest Publication Date',
    latest_publication_date: 'Latest Publication Date',
    authorities_plus: 'Authority (+)',
    authorities_minus: 'Authority (-)',
    legal_status: 'Legal Status',
    ma1: 'Electrical Engineering',
    ma2: 'Instruments',
    ma3: 'Chemistry',
    ma4: 'Mechanical Engineering',
    ma5: 'Other Fields',
    text_weighting: 'Importance',
    free_text_query: 'Advanced query',
    // Next ones are NPL specific
    author: 'Author',
    venue: 'Venue',
    field_of_study: 'Field of study',
    min_year: 'Min Year',
    max_year: 'Max Year',
    open_access: 'Open Access',
    influential: 'Influential',
  };

  constructor(
    private apiService: ApiService,
    private router: Router,
    private truncatePipe: TruncatePipe,
    private semanticSearchStoreService: SemanticSearchStoreService,
    private booleanSearchStoreService: BooleanSearchStoreService,
    private citationSearchStoreService: CitationSearchStoreService,
    private nplSearchStoreService: NplSearchStoreService,
    private patentTableService: PatentTableService,
    private userService: UserService
  ) {
  }

  public getHistories(payload: any, redirectTo403Page = true): Observable<{
    searches: SearchHistory[],
    page: PaginationMetadata
  }> {
    return this.apiService.get('web/history', payload, redirectTo403Page)
      .pipe(
        map(({data}) => data),
        tap(({searches, page}) => {
          searches.forEach((hs: SearchHistory) => hs.formatted_search_filters = this.formatSearchFilters(hs));
        })
      );
  }

  public delete(id: number): Observable<any> {
    return this.apiService.delete(`web/history/${id}`);
  }

  public clear(): Observable<any> {
    return this.apiService.delete(`web/history`);
  }

  public get(id: number): Observable<SearchHistory> {
    const savedSearch = this.getSavedSearch(id);
    if (savedSearch) {
      const result = JSON.parse(savedSearch);
      this.removeSavedSearch(id);
      return new BehaviorSubject(result).asObservable();
    }
    return this.apiService.get(`web/history/${id}`)
      .pipe(
        map(o => o.data)
      );
  }

  /**
   * get search type as string from given search item
   * @param search search item
   */
  public getType(search): string {
    switch(search.search_type) {
      case 'CITATION':
        return 'Citation search';
      case 'BOOLEAN':
        if (search.search_document_type === PatentListScopeEnum.PUBLICATION) {
          return 'Boolean search - Publication';
        }

        return 'Boolean search';
      case 'SEMANTIC':
        if (search.search_input && !search.patent_numbers) {
          return 'Free text search';
        }

        if (!search.search_input && search.patent_numbers?.length === 1) {
          return 'Publication search - Single';
        }

        if (!search.search_input && search.patent_numbers?.length > 1) {
          return 'Publication search - Multiple';
        }

        if (search.search_input && search.patent_numbers) {
          return 'Combined search';
        }
        break;
      case 'NPL':
        return 'Non-patent literature search'
      default:
        return '';
    }
  }

  textAndNumberSearch(hs: SearchHistory): string {
    return [hs.patent_numbers?.join(', '), hs.search_input].filter((v) => v?.trim()?.length > 0).join(' ');
  }

  /**
   * load given search record
   * @param item search record
   */
  loadSavedSearch(item, newTab?: boolean): void {
    item['type'] = 'history';
    const storeService = this.getSearchStoreService(item.search_type);
    if (storeService) {
      storeService.savedSearch = item;
    }
    this.storeSavedSearch(item);
    if(newTab){
      window.open(this.buildSavedSearchUrl(item.id, item.search_type), '_blank');
    } else {
      this.router.navigateByUrl(this.buildSavedSearchUrl(item.id, item.search_type));
    }
  }

  buildSavedSearchUrl(savedSearchId: number, searchType: string): string {
    if (!savedSearchId || !searchType) {
      return null;
    }

    let url;
    switch (searchType) {
      case 'SEMANTIC':
        url = '/search/patent';
        break;
      case 'BOOLEAN':
        url = '/boolean';
        break;
      case 'CITATION':
        url = '/citation';
        break;
      case 'NPL':
        url = '/npl-search'
        break;
      default:
        return null;
    }
    return `${url}?search=${savedSearchId}`;
  }

  getSearchStoreService(searchType: string): BaseStoreService {
    switch (searchType) {
      case 'SEMANTIC':
        return this.semanticSearchStoreService;
      case 'BOOLEAN':
        return this.booleanSearchStoreService;
      case 'CITATION':
        return this.citationSearchStoreService;
      case 'NPL':
        return this.nplSearchStoreService;
      default:
        return null;
    }
  }

  restoreSavedValues(item) {
    if (!item) {
      return;
    }

    const storeService = this.getSearchStoreService(item.search_type);

    if (storeService) {
      switch (item.search_type) {
        case 'SEMANTIC':
          if (item.additional_params?.chart_filters?.length) {
            storeService.filters.push(...item.additional_params?.chart_filters);
          }

          if (item.additional_params?.advanced_filter_applied_query?.length) {
            storeService.advancedFilterAppliedQuery = item.additional_params.advanced_filter_applied_query;

            if (item.additional_params.advanced_filter_boolean_clauses?.length) {
              const filterClauses = AdvancedFilterService.makeClausesFromSearchFields(item.additional_params.advanced_filter_boolean_clauses);
              storeService.advancedFilterAppliedAdvancedQuery = '';
              storeService.advancedFilterAdvancedQuery = '';
              storeService.advancedFilterBooleanClauses = [...filterClauses];
              storeService.advancedFilterAppliedBooleanClauses = [...filterClauses];
              storeService.isAdvancedFilterAdvancedMode = false;
            } else {
              storeService.advancedFilterAdvancedQuery = item.additional_params.advanced_filter_applied_query;
              storeService.advancedFilterAppliedAdvancedQuery = item.additional_params.advanced_filter_applied_query;
              storeService.advancedFilterBooleanClauses = [];
              storeService.advancedFilterAppliedBooleanClauses = [];
              storeService.isAdvancedFilterAdvancedMode = true;
            }
          }
          break;
        case 'BOOLEAN':
          const filterInput = item.additional_params?.filter_input ? JSON.parse(item.additional_params.filter_input) : [];
          storeService.setFilters(filterInput);
          if(item.search_document_type === PatentListScopeEnum.PUBLICATION){
            storeService.isPublications = true;
          }
          break;
        case 'CITATION':
          break;
        default:
          break;
      }
      this.patentTableService.setLegalStatusFromHistory(item.additional_params?.results_table_filter?.selected_legal_statuses || [], storeService)
    }
  }

  getSourceLanguageTitle(languageCode: string): string {
    const lang = translatableLanguages.find((l) => l.code === languageCode);
    return lang ? lang.name : 'English';
  }

  private formatSearchFilters(search: SearchHistory): {title: string, value: any}[] {
    const filters: {title: string, value: any}[] = [];
    if (search.search_filters) {
      Object.keys(search.search_filters).forEach(key => {
        const filterTitle = this.searchFilterFields[key];
        if (filterTitle && key.substring(0, 2) !== 'ma' || (key.substring(0, 2) === 'ma' && search.search_input)) {
          const filterValue = this.formatData(search, key);

          if (filterValue) {
            filters.push({title: filterTitle, value: filterValue});
          }
        }
      });
    }
    if(search.search_type === 'BOOLEAN' && search?.additional_params?.filter_input){
      const filterInput = JSON.parse(search.additional_params.filter_input);
      const filterValue = filterInput.map(f => f.query).join(' AND ');

      if (filterValue) {
        filters.push({title: this.searchFilterFields.free_text_query, value: filterValue});
      }
    }

    return filters;
  }

  private formatData(search: SearchHistory, key) {
    if (key.indexOf('date') > -1) {
      const arr = search.search_filters[key].split('-');
      const date = new Date(arr[0] + '/' + arr[1] + '/' + arr[2]);
      return date.toLocaleDateString(this.userService.getLocale().language, {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }

    const val = search.search_filters[key];

    switch (true) {
      case Array.isArray(val):
        return val.join(', ');
      case typeof(val) === 'object':
        return Object.keys(val).map(k => `${k}: ${val[k]}`).join(', ');
      default:
        return val;
    }
  }

  private storeSavedSearch(item) {
    window.localStorage[`saved_search_${item.id}`] = JSON.stringify(item);
  }

  private getSavedSearch(id) {
    return window.localStorage[`saved_search_${id}`];
  }

  private removeSavedSearch(id) {
    window.localStorage.removeItem(`saved_search_${id}`);
  }
}
