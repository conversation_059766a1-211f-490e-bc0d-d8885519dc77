import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';

import { SemanticSearchService } from './semantic-search.service';
import { RouterModule } from '@angular/router';

describe('SemanticSearchService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])]
  }));

  it('should be created', () => {
    const service: SemanticSearchService = TestBed.inject(SemanticSearchService);
    expect(service).toBeTruthy();
  });

  const requests = [
    {},
    {'patent_numbers': ['FRPS54654'], 'search_filters': {'earliest_priority_date': 'asda-as-as'}},
    {'patent_numbers': ['FRPS54654', '']},
    {'search_input': 'Test'},
  ];
  const error_details = [
    {},
    {
      'patent_numbers': {'0': ['Invalid patent number - missing/invalid publication number']},
      'search_filters': {'earliest_priority_date': ['Not a valid date.']}
    },
    {'patent_numbers': {'0': ['Invalid patent number - missing/invalid publication number'], '1': ['Empty number']}},
    {'search_filters': {'earliest_priority_date': ['Not a valid date.']}}
  ];
  const expectations = [
    '',
    '<ul><li>patent numbers -  <ul><li> "<u>FRPS54654</u>": Invalid patent number - missing/invalid publication number</li>' +
    '</ul></li><li>search filters -  <ul><li>earliest priority date -  "<u>asda-as-as</u>": Not a valid date.</li></ul></li></ul>',
    '<ul><li>patent numbers -  <ul><li> "<u>FRPS54654</u>": Invalid patent number - missing/invalid publication number</li>' +
    '<li> "<u></u>": Empty number</li></ul></li></ul>',
    '<ul><li>search filters -  <ul><li>earliest priority date -  "<u>*missing value*</u>": Not a valid date.</li></ul></li></ul>'
  ];
  for (let i = 0; i < requests.length; i++) {
    it('Can extract error messages from responses', () => {
      const service: SemanticSearchService = TestBed.inject(SemanticSearchService);
      expect(service.extractErrorMessage(error_details[i], requests[i])).toBe(expectations[i]);
    });
  }

  const savedFilters = {authorities_plus: ['GR', 'IT'], authorities_minus: ['US', 'AU']};
  const defaultFilters = {authorities_plus: 'DE,EP', authorities_minus: 'JP,CN'};
  const noSavedFilters = {authorities_plus: '', authorities_minus: ''};
  it('Should be merged default filters with saved filters', () => {
    const service: SemanticSearchService = TestBed.inject(SemanticSearchService);
    expect(service.mergeDefaultFilterWithSavedFilters(savedFilters, defaultFilters)).toEqual(
      {authorities_plus: ['GR', 'IT', 'DE', 'EP'], authorities_minus: ['US', 'AU', 'JP', 'CN']});
    expect(service.mergeDefaultFilterWithSavedFilters(noSavedFilters, defaultFilters)).toEqual(
      {authorities_plus: ['DE', 'EP'], authorities_minus: ['JP', 'CN']});
    expect(service.mergeDefaultFilterWithSavedFilters(null, defaultFilters)).toEqual(
      {authorities_plus: ['DE', 'EP'], authorities_minus: ['JP', 'CN']});
    expect(service.mergeDefaultFilterWithSavedFilters(savedFilters, null)).toEqual(savedFilters);
    expect(service.mergeDefaultFilterWithSavedFilters(null, null)).toEqual({});
  });

  const text = 'The invention relates to a method in which a system of layers with a contact layer of gallium.';
  const booleanQuery = ['title=mobile and tech_fields=Computer Technology',
                        'title<mobile and tech_fields<Computer Technology',
                        'title>mobile and tech_fields>Computer Technology',
                        'title<>mobile and tech_fields<>Computer Technology',
                        'title!=mobile and tech_fields!=Computer Technology'];
  const noBooleanQuery = ['title=mobile', 'title<mobile', 'title>mobile', 'title<>mobile', 'title!=mobile'];
  it('Should be detect boolean query', () => {
    const service: SemanticSearchService = TestBed.inject(SemanticSearchService);
    for (const query of booleanQuery) {
      expect(service.detectBooleanQuery(text + query)).toBeTruthy();
    }

    for (const query of noBooleanQuery) {
      expect(service.detectBooleanQuery(text + query)).toBeFalsy();
    }
  });
});
