import { tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

import { ApiService } from '../api/api.service';
import { SearchQueryParams, SemanticSearchRequest } from './types';
import { PatentViewReferralType } from '@core/services/patent/types';

@Injectable({
  providedIn: 'root'
})
export class SemanticSearchService {

  private documentsSubject = new BehaviorSubject<Array<Object>>([]);
  public documents = this.documentsSubject.asObservable();

  /**
   * additional data for singleton patent viewer
   */
  public linkData = {title: 'Semantic Search', referral: PatentViewReferralType.REFERRAL_PATENT, backPatentSearch: true};

  constructor(private apiService: ApiService) {
  }

  public search(
    payload: SemanticSearchRequest,
    queryParams?: SearchQueryParams,
    updateDocuments = true
  ): Observable<any> {
    const endpoint = 'search/semantic';
    return this.apiService.post(endpoint, payload, queryParams)
      .pipe(
        tap(({data}) => {
          if (updateDocuments) {
            this.documentsSubject.next(data.documents);
          }
        })
      );
  }

  public getCachedSearch(hash: string, payload?: SemanticSearchRequest, queryParams?: SearchQueryParams): Observable<any> {
    return this.apiService.post(`search/hash/${hash}`, payload, queryParams);
  }

  public getDocuments(): Object[] {
    return this.documentsSubject.value;
  }

  public resetDocuments(): void {
    this.documentsSubject.next([]);
  }

  public setDocuments(value): void {
    this.documentsSubject.next(value);
  }

  public export(hash: string, ids: any, queryParams: any): Observable<any> {
    return this.apiService.asyncExport(`web/export/${hash}`, ids, queryParams);
  }

  /**
   * Extract human-readable error message from API response
   * @param err_dict - error dictionary as returned by the API
   * @param request - payload which has been sent to the API (keys in the error dictionary always match keys in payload)
   */
  public extractErrorMessage(err_dict: any, request: any): string {
    if (typeof (err_dict) === 'string') {
      return err_dict;
    }
    let message = '';
    for (const error in err_dict) {
      if (!err_dict.hasOwnProperty(error)) {
        continue;
      }
      let key = error;
      const value = err_dict[key];
      let payload = request[key];
      if (payload === undefined) {
        payload = '*missing value*';  // Should not happen
      }
      if (key.length <= 3) {
        key = '';  // Ignore short numeric keys like "0", looks stupid
      } else {
        key = key.replace(/_/g, ' ') + ' - ';  // strip underscores so message looks better
      }

      if (typeof (value) === 'string') {
        message += `<li>${key} <u>"${payload}"</u> ${value}</li>`;
      } else if (Array.isArray(value)) {
        message += `<li>${key} "<u>${payload}</u>": ${value[0]}</li>`;
      } else {
        message += `<li>${key} ${this.extractErrorMessage(value, payload)}</li>`;
      }
    }
    if (message) {
      message = `<ul>${message}</ul>`;
    }
    return message;
  }

  public mergeDefaultFilterWithSavedFilters(savedFilters, defaultFilters): Object {
    if (!savedFilters) {
      savedFilters = {};
    }
    if (!defaultFilters) {
      return savedFilters;
    }
    const newFilter = {};

    Object.keys(defaultFilters).forEach(fieldFilter => {
      const filtersSaved = savedFilters[fieldFilter];
      if (!filtersSaved) {
        newFilter[fieldFilter] = defaultFilters[fieldFilter].split(',');
      } else {
        const arrDefaultFilters = defaultFilters[fieldFilter].split(',');
        const filterToMerge = [];
        arrDefaultFilters.forEach(filter => {
          if (filter && filtersSaved.indexOf(filter) === -1) {
            filterToMerge.push(filter);
          }
        });
        if (filterToMerge.length) {
          newFilter[fieldFilter] = filtersSaved.concat(filterToMerge);
        }

      }
    });
    return Object.assign(savedFilters, newFilter);
  }

  public detectBooleanQuery(text: string): boolean {
    const operators = ['=', '!=', '<>', '<', '>'];
    for (const operator of operators) {
      const regExp = new RegExp(operator, 'gi');
      if ((text.match(regExp) || []).length / text.length > 0.01) {
        return true;
      }
    }
    return false;
  }

  clearStoredData(): void {
    this.resetDocuments();
  }
}
