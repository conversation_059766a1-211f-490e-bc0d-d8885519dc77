import { Patent } from '@core/models';

export interface SemanticSearchRequest {
  search_input?: string;
  search_filters?: SemanticSearchFilters;
  patent_numbers?: Array<string>;
  top_term_weights?: number;
  additional_params?: {
    advanced_filter_applied_query?: string;
    advanced_filter_boolean_clauses?: Array<{}>;
    chart_filters?: Array<string>;
    results_table_filter?: {
      selected_legal_statuses: string[];
    };
  };
  boost_factors?: {
    keywords?: Array<string>;
    ipc_codes: Array<string>;
    cpc_codes: Array<string>;
  };
  translation?: {
    source_language: string;
  }
}

export interface SemanticSearchFilters {
  quantity_cut_off?: number;
  similarity_cut_off?: number;
  inclusion_and?: Array<string>;
  inclusion_or?: Array<string>;
  exclusion_and?: Array<string>;
  exclusion_or?: Array<string>;
  applicants_plus?: Array<string>;
  applicants_minus?: Array<string>;
  authorities_plus?: Array<string>;
  authorities_minus?: Array<string>;
  cpc_plus?: Array<string>;
  cpc_minus?: Array<string>;
  ipc_plus?: Array<string>;
  ipc_minus?: Array<string>;
  earliest_priority_date?: string;
  latest_priority_date?: string;
  earliest_publication_date?: string;
  latest_publication_date?: string;
  ma1?: boolean;
  ma2?: boolean;
  ma3?: boolean;
  ma4?: boolean;
  ma5?: boolean;
  legal_status?: Array<string>;
  free_text_query?: string;
  text_weighting?: number;
}

export interface SearchQueryParams {
  page?: number;
  page_size?: number;
  show_analytics?: 1 | 0;
  show_general?: 1 | 0;
  show_bibliographic?: 1 | 0;
  show_fulltext?: 1 | 0;
  show_tags?: 1 | 0;
  sort_order?: 'asc' | 'desc';
  sort_by?: string;
  skip_log_search?: 1 | 0;
  save_history?: 1 | 0;
  prefetch_images?: 1 | 0;
}

export interface PaginationMetadata {
  current_page: number;
  last_page: number;
  page_size: number;
  total_hits: number;
  origin_total_hits?: number;
  complete?: boolean;
}

export interface PatentResultsResponse {
  documents: Patent[],
  publications: Patent[],
  page: PaginationMetadata,
  search_info: { new_search: boolean, search_hash: string }
}
