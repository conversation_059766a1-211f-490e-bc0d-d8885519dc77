import { extractPublications, FIXTURE_INVALID_PATENT_NUMBERS, FIXTURE_VALID_PATENT_NUMBERS } from '@core/services';

describe('Freetext Util', () => {
  for (const validPatentNumber of FIXTURE_VALID_PATENT_NUMBERS) {
    const inputs = [
      `this is a patent number ${validPatentNumber}`,
      `${validPatentNumber},${validPatentNumber},`,
      `${validPatentNumber} is a patent number`,
      `${validPatentNumber} is a patent number and ${validPatentNumber} is too`,
      `${validPatentNumber} is a patent number and ${validPatentNumber}`
    ];

    const expectedResults = [
      [
        [validPatentNumber], 'this is a patent number'
      ],
      [
        [validPatentNumber, validPatentNumber], ''
      ],
      [
        [validPatentNumber], 'is a patent number'
      ],
      [
        [validPatentNumber, validPatentNumber], 'is a patent number and is too'
      ],
      [
        [validPatentNumber, validPatentNumber], 'is a patent number and'
      ]
    ];

    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const expectedResult = expectedResults[i];

      const result = extractPublications(input);

      it(`Provide "${input}" returns "${validPatentNumber}"`, () => {
        expect(result.publications).toEqual(jasmine.arrayWithExactContents(expectedResult[0]));
        expect(result.remainingText).toBe(expectedResult[1]);
      });
    }
  }

  const allValidPatentNumbersStr = FIXTURE_VALID_PATENT_NUMBERS.join(', ');
  const result1 = extractPublications(allValidPatentNumbersStr);

  it(`Provide ${allValidPatentNumbersStr} returns all valid patent numbers`, () => {
    expect(result1.publications).toEqual(jasmine.arrayWithExactContents(FIXTURE_VALID_PATENT_NUMBERS));
    expect(result1.remainingText).toEqual('');
  });

  for (const invalidPatentNumber of FIXTURE_INVALID_PATENT_NUMBERS) {
    const inputs = [
      `this is a patent number ${invalidPatentNumber}`,
      `this is a patent number ${invalidPatentNumber}, this is // a -- ,,,,, .....   ${invalidPatentNumber}    valid number`,
      `this is a patent number ${invalidPatentNumber}. from European Patent Office`,
      `${invalidPatentNumber} is a patent number`,
      `${invalidPatentNumber} is a patent number and ${invalidPatentNumber} is too`,
      `${invalidPatentNumber} is a patent number and ${invalidPatentNumber}`
    ];

    for (let i = 0; i < inputs.length; i++) {
      const input = inputs[i];
      const result = extractPublications(input);

      it(`Provide "${input}" returns no valid patent number`, () => {
        expect(result.publications).toEqual(jasmine.arrayWithExactContents([]));
        expect(result.remainingText).toBe(input);
      });
    }
  }

  const allInvalidPatentNumbersStr = FIXTURE_INVALID_PATENT_NUMBERS.join(', ');
  const result2 = extractPublications(allInvalidPatentNumbersStr);

  it(`Provide ${allInvalidPatentNumbersStr} returns no valid patent number`, () => {
    expect(result2.publications).toEqual([]);
    expect(result2.remainingText).toEqual(allInvalidPatentNumbersStr);
  });
});
