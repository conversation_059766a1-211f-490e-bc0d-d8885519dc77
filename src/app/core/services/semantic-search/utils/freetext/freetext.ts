import { AUTHORITY_CODE_NODES } from '@core/models';
import { trim } from 'lodash';

const authorityCodes = AUTHORITY_CODE_NODES.map(o => o.id).join('|');

export function regExpPublications(): RegExp {
  return new RegExp(`\\b(${authorityCodes})-?[a-z0-9]{0,5}?[0-9]{4,12}[a-z0-9]{0,5}?-?[a-z0-9]{0,2}\\b`, 'gi');
}

export function extractPublications(text): {
  publications: Array<string>,
  remainingText: string
} {
  if (text === '' || text == null) {
    return {publications: [], remainingText: ''};
  }

  const publications = [];
  let remainingText = text;
  const results = text.match(regExpPublications());

  if (results) {
    for (const rs of results) {
      const twoFirstCharacters = rs.substring(0, 2).toUpperCase();

      if (authorityCodes.indexOf(twoFirstCharacters) > -1) {
        publications.push(rs);
      }
    }
    if (publications.length > 0) {
      remainingText = removeOccurrences(publications, remainingText);
    }
  }

  return {
    publications,
    remainingText
  };
}

function removeOccurrences(occurrences: Array<string>, text: string): string {
  const pattern = '(' + occurrences.map(o => '\\b' + o + '\\b,?\\s?').join('|') + ')';
  return trim(text.replace(new RegExp(pattern, 'gi'), '').trim(), ',');
}
