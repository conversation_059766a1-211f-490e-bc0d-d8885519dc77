import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { SettingsService } from '../settings/settings.service';
import { Settings } from '@core/models';

@Injectable({
  providedIn: 'root'
})
export class SettingsHttpService {

  constructor(
    private http: HttpClient,
    private settingsService: SettingsService
  ) {
  }

  initializeSettings(): Promise<any> {
    return new Promise<void>(
      (resolve) => {
        this.http.get('/assets/settings.json')
          .toPromise()
          .then((response) => {
              this.settingsService.settings = <Settings>response;
              resolve();
            }
          );
      }
    );
  }
}
