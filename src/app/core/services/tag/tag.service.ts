import { Injectable, EventEmitter } from '@angular/core';
import { ApiService } from '../api/api.service';
import { TagModel } from '@core/models/tag.model';
import { PaginationMetadata } from '../semantic-search';
import { BehaviorSubject, Observable, of, from } from 'rxjs';
import { map, tap, switchMap } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Router } from '@angular/router';
import { ToastService } from '../toast';
import { ToastTypeEnum } from '../toast/types';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class TagService {
  tags: TagModel[] = [];
  refreshLabelsEvent: EventEmitter<number> = new EventEmitter();

  private tagChangedSubject = new BehaviorSubject<TagModel>(null);
  public tagChanged$ = this.tagChangedSubject.asObservable();

  private assignedTagsSubject = new BehaviorSubject<{ tag: TagModel, documentIds: number[] }>(null);
  readonly assignedTags$ = this.assignedTagsSubject.asObservable();

  private deletedTagsSubject = new BehaviorSubject<TagModel>(null);
  readonly deletedTags$ = this.deletedTagsSubject.asObservable();

  constructor(
    private apiService: ApiService,
    private ngbModal: NgbModal,
    private toastService: ToastService,
    private router: Router
  ) { }

  getTag(tagId: number): Observable<TagModel> {
    return this.apiService.get(`web/tags/${tagId}`).pipe(map(resp => resp.data));
  };

  getTags(payload?): Observable<{ tags: Array<TagModel>, page: PaginationMetadata }> {
    return this.apiService.get(`web/tags`, payload)
    .pipe(tap(({data}) => { this.tags = data.tags }))
      .pipe(
        map(response => response.data),
        tap(({tags}) => {
          tags.forEach((tag: TagModel) => {
            this.tagChangedSubject.next(tag);
          });
        })
      );
  };

  assign(tag: TagModel, payload) {
    return this.apiService.post(`web/tags/${tag.id}/documents`, payload)
      .pipe(
        tap(() => {
          if (payload['document_ids']?.length) {
            this.assignedTagsSubject.next({tag: tag, documentIds: payload['document_ids']});
          }
        })
      );
  }

  unassign(tagId: number, documentId: number) {
    return this.apiService.delete(`web/tags/${tagId}/documents/${documentId}`)
      .pipe(
        tap(() => {
          this.assignedTagsSubject.next(null);
        })
      );
  }

  create(payload: TagModel): Observable<TagModel> {
    return this.apiService.post('web/tags', payload).pipe(map(resp => resp.data));
  }

  update(payload: TagModel, id: number): Observable<TagModel> {
    return this.apiService.patch(`web/tags/${id}`, payload).pipe(
      map(resp => resp.data),
      tap((tag: TagModel) => {
        this.tags = this.tags.map(t => (t.id === tag.id ? tag : t));
        this.tagChangedSubject.next(tag);
      })
    );
  }

  delete(id: number) {
    return this.apiService.delete(`web/tags/${id}`);
  }

  multiDelete(tag_ids: number[]) {
    return this.apiService.post(`web/tags/delete`, {tag_ids});
  }

  getTextColor(color: string): string {
    const rgb = this.hexToRgb(color);
    const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
    return luminance > 0.6 ? '#282828' : '#fffefd';
  }

  hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? { r: parseInt(result[1], 16), g: parseInt(result[2], 16), b: parseInt(result[3], 16) }
      : null;
  }

  rgbToHex(r: number, g: number, b: number) {
    const hex = '#' + ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');
    return hex;
  }

  getDarkColor(color: string) {
    const rgb = this.hexToRgb('#' + color);
    const r = Math.round(rgb.r * (100 - 20) / 100);
    const g = Math.round(rgb.g * (100 - 20) / 100);
    const b = Math.round(rgb.b * (100 - 20) / 100);
    return this.rgbToHex(r, g, b);
  }

  clearStoredData(): void {
    this.tags = [];
  }

  deleteTagsWithConfirmation(tags: TagModel[]): Observable<TagModel[]> {
    if (!tags || tags.length === 0) {
      return of([]);
    }
    
    const isSingleTag = tags.length === 1;
    const { modalTitle, modalDescription, modalQuestion } = this.getModalConfig(tags, isSingleTag);
    
    return from(this.showConfirmationModal(modalTitle, modalDescription, modalQuestion)).pipe(
      switchMap(result => {
        if (!result) {
          return of([]);
        }
        
        return this.processTagsDeletion(tags);
      })
    );
  }

  private getModalConfig(tags: TagModel[], isSingleTag: boolean): { modalTitle: string, modalDescription: string, modalQuestion: string } {
    if (isSingleTag) {
      const tag = tags[0];
      return {
        modalTitle: 'Delete tag',
        modalDescription: `By deleting this tag, ${tag.documents_count} Documents will get untagged`,
        modalQuestion: `Are you sure you want to delete "${tag.name}" tag permanently?`
      };
    } else {
      const tagsCount = tags.length;
      const totalDocuments = tags.reduce((sum, tag) => sum + (tag.documents_count || 0), 0);
      const tagNamesText = this.formatTagNames(tags);
      
      return {
        modalTitle: 'Delete tags',
        modalDescription: `By deleting these tags, ${totalDocuments} Documents will get untagged`,
        modalQuestion: `Are you sure you want to delete ${tagsCount} tags (${tagNamesText}) permanently?`
      };
    }
  }
  
  private formatTagNames(tags: TagModel[]): string {
    const tagsCount = tags.length;
    if (tagsCount <= 3) {
      return tags.map(tag => `"${tag.name}"`).join(', ');
    } else {
      const firstTags = tags.slice(0, 2).map(tag => `"${tag.name}"`).join(', ');
      return `${firstTags} and ${tagsCount - 2} more`;
    }
  }
  
  private processTagsDeletion(tags: TagModel[]): Observable<TagModel[]> {
    const tagIds = tags.map(tag => tag.id);
    
    return this.multiDelete(tagIds).pipe(
      tap(() => this.handleTagDeletionUIEffects(tags)),
      map(() => tags)
    );
  }
  
  private handleTagDeletionUIEffects(tags: TagModel[]): void {
    const isAnyTagInCollectionRoute = tags.some(tag => this.isCollectionRoute(tag));
    if (isAnyTagInCollectionRoute) {
      this.router.navigate(['/collections/tagged']);
    }
    
    const isSingleTag = tags.length === 1;
    
    if (isSingleTag) {
      const tag = tags[0];
      const isCollectionRoute = this.isCollectionRoute(tag);
      const header = !isCollectionRoute ? 'Tag deleted' : `Tag "${tag.name}" was deleted`;
      const body = !isCollectionRoute ? 
        `The tag "${tag.name}" has been deleted successfully` : 
        `You have deleted the tag "${tag.name}" collection successfully.`;
      
      this.toastService.show({
        type: ToastTypeEnum.SUCCESS,
        header: header,
        body: body,
        delay: 3000
      });
      this.deletedTagsSubject.next(tag);
    } else {
      this.toastService.show({
        type: ToastTypeEnum.SUCCESS,
        header: 'Tags deleted',
        body: `${tags.length} tags have been deleted successfully`,
        delay: 3000
      });
    }
  }

  private showConfirmationModal(title: string, description: string, question: string): Promise<boolean> {
    return new Promise<boolean>(resolve => {
      const modalRef = this.ngbModal.open(ModalDialogComponent, {
        size: 'lg', 
        backdrop: 'static', 
        centered: true
      });
      
      modalRef.componentInstance.options = {
        title,
        description,
        question,
        confirmButton: 'Remove',
        cancelButton: 'Cancel'
      };
      
      modalRef.result.then(
        result => resolve(!!result),
        () => resolve(false)
      );
    });
  }

  private isCollectionRoute(tag: TagModel): boolean {
    const currentUrl = this.router.url;
    const isInCollectionRoute = currentUrl.match(/^\/collections\/[^\/]+\/collection\/(\d+)$/);
    const collectionId = isInCollectionRoute ? isInCollectionRoute[1] : null;
    return isInCollectionRoute && collectionId === tag?.collection_id?.toString();
  }
}
