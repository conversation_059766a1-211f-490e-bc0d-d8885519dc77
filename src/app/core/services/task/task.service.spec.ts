import { TestBed } from '@angular/core/testing';

import { TaskService } from './task.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import {
  AssigneeTypeEnum,
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskDisplayTypeEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TaskStatusEnum,
  TaskTypeEnum,
  TeamUser,
  TeamUserTypeEnum
} from '@core/models';
import { PluralizePipe, UsersTitleTextPipe, UserTitlePipe } from '@core/pipes';
import { UserService } from '../user/user.service';


describe('TaskService', () => {
  let service: TaskService;
  let mockUser: any;

  const createMockTeamUser = (id: number): TeamUser => ({
    id,
    first_name: `User${id}`,
    last_name: null,
    email: null,
    type: TeamUserTypeEnum.USER
  });

  const createMockAssignment = (params: Partial<TaskAssignmentModel>): TaskAssignmentModel => ({
    id: params.id || 1,
    task_id: params.task_id || 1,
    assignee_id: params.assignee_id || 1,
    status: params.status || TaskAssignmentStatusEnum.NEW,
    assignee_type: params.assignee_type || AssigneeTypeEnum.USER,
    assignee: createMockTeamUser(params.assignee_id || 1),
    document_id: params.document_id || 1,
    answer: params.answer || '',
    answered_at: params.answered_at || null,
    message: params.message || ''
  } as TaskAssignmentModel);

  const createMockTask = (params: Partial<TaskModel>): TaskModel => ({
    id: params.id || 1,
    author_id: params.author_id || 1,
    resource_id: params.resource_id || 1,
    resource_type: params.resource_type || TaskResourceTypeEnum.DOCUMENT,
    subject: params.subject || 'Test Task',
    description: params.description || '',
    task_type: params.task_type || TaskTypeEnum.STAR_RATING,
    status: params.status || TaskStatusEnum.NEW,
    assignments: params.assignments || [],
    assignee_ids: params.assignee_ids || [],
    assignees: params.assignees || [],
    team_users: params.team_users || [],
    display_type: params.display_type || TaskDisplayTypeEnum.INCOMING
  } as TaskModel);

  beforeEach(() => {
    mockUser = {
      profile: {
        id: 1,
        groups: [{id: 100}]
      }
    };

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        provideMatomo({siteId: '', trackerUrl: '', disabled: true}),
        UsersTitleTextPipe,
        UserTitlePipe,
        TaskService,
        PluralizePipe,
        {
          provide: UserService,
          useValue: {
            getUser: () => mockUser
          }
        }
      ]
    });

    service = TestBed.inject(TaskService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('splitTaskIntoRequestsAndRatings', () => {
    it('should correctly split done assignments into ratings', () => {
      const task = createMockTask({
        assignments: [
          createMockAssignment({
            id: 1,
            status: TaskAssignmentStatusEnum.DONE,
            assignee_id: 2
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(1);
      expect(result.answerableRequests.length).toBe(0);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
    });

    it('should correctly identify answerable requests for current user', () => {
      const task = createMockTask({
        author_id: 2, // Different author
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 1, // Current user
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(0);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
    });

    it('should correctly identify group assignments as answerable requests', () => {
      const task = createMockTask({
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 100, // Group ID
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.answerableRequests.length).toBe(0);
      expect(result.createdRequests.length).toBe(1);
      expect(result.otherRequests.length).toBe(0);
    });

    it('should identify created requests when user is author', () => {
      const task = createMockTask({
        author_id: 1, // Current user
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 2,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.answerableRequests.length).toBe(0);
      expect(result.createdRequests.length).toBe(1);
      expect(result.otherRequests.length).toBe(0);
    });

    it('should handle multiple assignments of different types', () => {
      const task = createMockTask({
        author_id: 2,
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 2,
            status: TaskAssignmentStatusEnum.DONE
          }),
          createMockAssignment({
            id: 2,
            assignee_id: 1,
            status: TaskAssignmentStatusEnum.NEW
          }),
          createMockAssignment({
            id: 3,
            assignee_id: 3,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(1);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);

      expect(result.ratings[0].assignments.length).toBe(1);
      expect(result.answerableRequests[0].assignments.length).toBe(2);
    });

    it('should handle mixed user and group assignments for single task', () => {
      const task = createMockTask({
        id: 1,
        author_id: 2,
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 1,
            status: TaskAssignmentStatusEnum.NEW
          }),
          createMockAssignment({
            id: 2,
            assignee_id: 100,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(0);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
      expect(result.answerableRequests[0].assignments.length).toBe(2);
    });

    it('should handle complex assignments with multiple statuses in single task', () => {
      const task = createMockTask({
        id: 1,
        author_id: 2,
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 1,
            status: TaskAssignmentStatusEnum.DONE
          }),
          createMockAssignment({
            id: 2,
            assignee_id: 100,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.NEW
          }),
          createMockAssignment({
            id: 3,
            assignee_id: 1,
            status: TaskAssignmentStatusEnum.OVERDUE
          }),
          createMockAssignment({
            id: 4,
            assignee_id: 2,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(1);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
      expect(result.answerableRequests[0].assignments.length).toBe(3);
    });

    it('should handle task where author is current user and has group assignments', () => {
      const task = createMockTask({
        id: 1,
        author_id: 2,
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 100,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.NEW
          }),
          createMockAssignment({
            id: 2,
            assignee_id: 101,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.OPEN
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(0);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
      expect(result.answerableRequests[0].assignments.length).toBe(2);
    });

    it('should handle mixed status group assignments for author', () => {
      const task = createMockTask({
        id: 1,
        author_id: 1,
        assignments: [
          createMockAssignment({
            id: 1,
            assignee_id: 100,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.DONE
          }),
          createMockAssignment({
            id: 2,
            assignee_id: 100,
            assignee_type: AssigneeTypeEnum.GROUP,
            status: TaskAssignmentStatusEnum.NEW
          })
        ]
      });

      const result = service.splitTaskIntoRequestsAndRatings(task);

      expect(result.ratings.length).toBe(1);
      expect(result.answerableRequests.length).toBe(0);
      expect(result.createdRequests.length).toBe(1);
      expect(result.otherRequests.length).toBe(0);
      expect(result.createdRequests[0].assignments.length).toBe(1);
    });
  });

  describe('splitTasksIntoRequestsAndRatings', () => {
    it('should correctly split multiple tasks with deep property verification', () => {
      const tasks = [
        createMockTask({
          id: 1,
          assignments: [
            createMockAssignment({
              id: 1,
              status: TaskAssignmentStatusEnum.DONE,
              answer: 'Done answer'
            })
          ]
        }),
        createMockTask({
          id: 2,
          assignments: [
            createMockAssignment({
              id: 2,
              assignee_id: 1,
              status: TaskAssignmentStatusEnum.NEW
            })
          ]
        })
      ];

      const result = service.splitTasksIntoRequestsAndRatings(tasks);

      expect(result.ratings[0].assignments[0].answer).toBe('Done answer');
      expect(result.ratings[0].assignee_ids).toEqual([1]);
      expect(result.createdRequests[0].assignments[0].assignee_id).toBe(1);
    });

    it('should handle tasks with overdue and mixed status assignments', () => {
      const tasks = [
        createMockTask({
          id: 1,
          author_id: 2, // Different author
          assignments: [
            createMockAssignment({
              id: 1,
              assignee_id: 1, // Current user
              status: TaskAssignmentStatusEnum.OVERDUE
            }),
            createMockAssignment({
              id: 2,
              assignee_id: 1, // Current user
              status: TaskAssignmentStatusEnum.OPEN
            })
          ]
        })
      ];

      const result = service.splitTasksIntoRequestsAndRatings(tasks);

      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
      expect(result.answerableRequests[0].assignments.length).toBe(2);
      expect(result.answerableRequests[0].assignments[0].status).toBe(TaskAssignmentStatusEnum.OVERDUE);
    });

    it('should handle tasks with no assignments', () => {
      const tasks = [
        createMockTask({
          id: 1,
          assignments: []
        })
      ];

      const result = service.splitTasksIntoRequestsAndRatings(tasks);

      expect(result.ratings.length).toBe(0);
      expect(result.answerableRequests.length).toBe(0);
      expect(result.createdRequests.length).toBe(0);
      expect(result.otherRequests.length).toBe(0);
    });

    it('should correctly copy team_users and assignees data', () => {
      const teamUser = createMockTeamUser(2);
      const tasks = [
        createMockTask({
          id: 1,
          author_id: 1,
          team_users: [teamUser],
          assignments: [
            createMockAssignment({
              id: 1,
              assignee_id: 2,
              status: TaskAssignmentStatusEnum.NEW,
              assignee: teamUser
            })
          ]
        })
      ];

      const result = service.splitTasksIntoRequestsAndRatings(tasks);

      expect(result.createdRequests[0].team_users).toEqual([teamUser]);
      expect(result.createdRequests[0].assignments[0].assignee).toEqual(teamUser);
    });

    it('should handle complex mixed assignments with multiple statuses', () => {
      const tasks = [
        createMockTask({
          id: 1,
          author_id: 2,
          assignments: [
            createMockAssignment({
              id: 1,
              assignee_id: 1,
              status: TaskAssignmentStatusEnum.DONE
            }),
            createMockAssignment({
              id: 2,
              assignee_id: 100,
              assignee_type: AssigneeTypeEnum.GROUP,
              status: TaskAssignmentStatusEnum.NEW
            }),
            createMockAssignment({
              id: 3,
              assignee_id: 1,
              status: TaskAssignmentStatusEnum.OVERDUE
            })
          ]
        }),
        createMockTask({
          id: 2,
          author_id: 1,
          assignments: [
            createMockAssignment({
              id: 4,
              assignee_id: 2,
              status: TaskAssignmentStatusEnum.NEW
            })
          ]
        }),
        createMockTask({
          id: 2,
          author_id: 2,
          assignments: [
            createMockAssignment({
              id: 4,
              assignee_id: 3,
              status: TaskAssignmentStatusEnum.NEW
            })
          ]
        })
      ];

      const result = service.splitTasksIntoRequestsAndRatings(tasks);

      expect(result.ratings.length).toBe(1);
      expect(result.answerableRequests.length).toBe(1);
      expect(result.createdRequests.length).toBe(1);
      expect(result.otherRequests.length).toBe(1);
    });

    describe('self-rating and group assignment handling', () => {
      it('should handle self-rating tasks correctly', () => {
        const currentUserId = 1; // matches mockUser.profile.id
        const task = createMockTask({
          id: 1,
          author_id: currentUserId,
          assignments: [
            createMockAssignment({
              id: 1,
              assignee_id: currentUserId,
              assignee_type: AssigneeTypeEnum.USER,
              status: TaskAssignmentStatusEnum.NEW
            })
          ]
        });

        const result = service.splitTaskIntoRequestsAndRatings(task);

        expect(result.answerableRequests.length).toBe(0);
        expect(result.createdRequests.length).toBe(1);
        expect(result.otherRequests.length).toBe(0);
      });

      it('should handle completed group assignments correctly', () => {
        const task = createMockTask({
          id: 1,
          author_id: 2,
          assignments: [
            createMockAssignment({
              id: 1,
              assignee_id: 100,
              assignee_type: AssigneeTypeEnum.GROUP,
              status: TaskAssignmentStatusEnum.DONE,
              answer: '5'
            }),
            createMockAssignment({
              id: 2,
              assignee_id: 100,
              assignee_type: AssigneeTypeEnum.GROUP,
              status: TaskAssignmentStatusEnum.NEW
            })
          ]
        });

        const result = service.splitTaskIntoRequestsAndRatings(task);

        expect(result.ratings.length).toBe(1);
        expect(result.ratings[0].assignments[0].status).toBe(TaskAssignmentStatusEnum.DONE);
        expect(result.answerableRequests.length).toBe(1);
        expect(result.answerableRequests[0].assignments[0].status).toBe(TaskAssignmentStatusEnum.NEW);
        expect(result.createdRequests.length).toBe(0);
        expect(result.otherRequests.length).toBe(0);
      });
    });
  });
});
