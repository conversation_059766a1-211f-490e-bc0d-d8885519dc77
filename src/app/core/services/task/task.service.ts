import { Injectable } from '@angular/core';
import {
  ApiService,
  Assignee,
  AssigneeTypeEnum,
  GroupService,
  PaginationMetadata,
  PluralizePipe,
  TASK_TYPES,
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskDisplayTypeEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TaskStats,
  TaskStatusEnum,
  TaskTypeEnum,
  TaskTypeItem,
  TeamUser,
  TeamUserTypeEnum,
  UserService,
  UsersTitleTextPipe,
  UserTitlePipe
} from '@core';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { TagModel } from '@core/models/tag.model';
import { cloneDeep } from 'lodash';
import { RatingSorter } from '@patent/patent-ratings/shared/rating-sorter';
import { SortRatingsOptionEnum } from '@patent/patent-ratings/shared/types';

@Injectable({
  providedIn: 'root'
})
export class TaskService {
  tasks: TaskModel[] = [];

  readonly NO_PASS_VALUE = 1;
  readonly PASS_VALUE = 5;

  constructor(
    private apiService: ApiService,
    private userService: UserService,
    private groupService: GroupService,
    private userTitleTextPipe: UserTitlePipe,
    private usersTitleTextPipe: UsersTitleTextPipe,
    private pluralizePipe: PluralizePipe,
  ) {
  }

  getTasks(payload, redirectTo403Page = true): Observable<{ tasks: Array<TaskModel>, page: PaginationMetadata }> {
    const userId = this.userService.getUser()?.profile?.id;
    return this.apiService.get('web/tasks', payload, redirectTo403Page)
      .pipe(
        map(response => response.data),
        map(({tasks, page}) => {
          const validTasks = tasks.filter((t: TaskModel) => {
            if (t.assignments.length === 0) {
              return false;
            }
            if (t.author_id === userId) {
              return true;
            }
            return t.assignments.some((ta) => this.isTaskAssignmentAssignedToMe(ta));
          });
          return {tasks: validTasks, page: page};
        }),
        tap(({tasks, page}) => {
          tasks.forEach((t) => {
            t.display_type = t.author_id === userId ? TaskDisplayTypeEnum.OUTGOING : TaskDisplayTypeEnum.INCOMING;
          });
        }),
      );
  }

  getRatings(payload, excludeSelfPendingRatings: boolean = false, redirectTo403Page = true): Observable<{
    tasks: Array<TaskModel>,
    page: PaginationMetadata
  }> {
    payload['task_type'] = `in:${TaskTypeEnum.STAR_RATING},${TaskTypeEnum.YES_NO_ANSWER}`;
    return this.apiService.get('web/tasks', payload, redirectTo403Page)
      .pipe(
        map(response => response.data as { tasks: Array<TaskModel>, page: PaginationMetadata }),
        map(({tasks, page}) => {
          if (excludeSelfPendingRatings) {
            tasks = tasks.filter((t) => !(this.isSelfRatingTask(t) && this.isTaskOpen(t)));
          }
          return {tasks, page};
        }),
      );
  }

  getPendingSelfRating(payload, redirectTo403Page = true): Observable<TaskModel> {
    const userId = this.userService.getUser()?.profile?.id;
    payload['task_type'] = `in:${TaskTypeEnum.STAR_RATING},${TaskTypeEnum.YES_NO_ANSWER}`;
    payload['status'] = `ne:${TaskStatusEnum.DONE}`;
    payload['author_id'] = userId;
    payload['assigned_user_ids'] = [userId];
    payload['page_size'] = 1;

    return this.apiService.get('web/tasks', payload, redirectTo403Page)
      .pipe(
        map(response => response.data),
        map(({tasks}) => tasks.length > 0 ? tasks[0] : null),
      );
  }

  findPendingSelfRatingOrNew(documentId: number): Observable<TaskModel> {
    return this.getPendingSelfRating({document_id: documentId})
      .pipe(
        map((task) => {
          if (task) {
            return task;
          }
          return this.buildSelfRatingTask(documentId, TaskResourceTypeEnum.DOCUMENT, [documentId]);
        }),
        catchError(() => {
          return of(this.buildSelfRatingTask(documentId, TaskResourceTypeEnum.DOCUMENT, [documentId]));
        })
      );
  }

  updateTask(taskId: number, payload): Observable<TaskModel> {
    const userId = this.userService.getUser()?.profile?.id;
    return this.apiService.patch(`web/tasks/${taskId}`, payload)
      .pipe(
        map(response => response.data),
        tap((task) => {
          task.display_type = task.author_id === userId ? TaskDisplayTypeEnum.OUTGOING : TaskDisplayTypeEnum.INCOMING;
        })
      );
  }

  createTask(payload): Observable<TaskModel> {
    const userId = this.userService.getUser()?.profile?.id;
    return this.apiService.post('web/tasks', payload)
      .pipe(
        map(response => response.data),
        tap((task) => {
          task.display_type = task.author_id === userId ? TaskDisplayTypeEnum.OUTGOING : TaskDisplayTypeEnum.INCOMING;
        })
      );
  }

  getTask(id: number): Observable<TaskModel> {
    const userId = this.userService.getUser()?.profile?.id;
    return this.apiService.get(`web/tasks/${id}`)
      .pipe(
        map(response => response.data),
        tap((task) => {
          task.display_type = task.author_id === userId ? TaskDisplayTypeEnum.OUTGOING : TaskDisplayTypeEnum.INCOMING;
        })
      );
  }

  deleteTask(id: number): Observable<any> {
    return this.apiService.delete(`web/tasks/${id}`);
  }

  answerTaskAssignment(taskAssignmentId: number, payload): Observable<TaskAssignmentModel> {
    return this.apiService.post(`web/tasks/assignments/${taskAssignmentId}`, payload)
      .pipe(map(response => response.data));
  }

  updateTaskAssignment(taskAssignmentId: number, payload): Observable<TaskAssignmentModel> {
    return this.apiService.patch(`web/tasks/assignments/${taskAssignmentId}`, payload)
  }

  markTaskAssignmentAsOpen(taskAssignmentIds: number[]): Observable<TaskAssignmentModel> {
    return this.apiService.post(`web/tasks/assignments/open`, {task_assignment_ids: taskAssignmentIds})
      .pipe(map(response => response.data));
  }

  markMyTaskAssignmentsAsOpen(tasks: TaskModel[]): Observable<TaskModel[]> {
    const newTaskAssignments = [];
    for (const task of tasks) {
      newTaskAssignments.push(...this.myAssignments(task).filter(ta => this.isTaskAssignmentNew(ta)));
    }

    if (!newTaskAssignments.length) {
      return of(tasks);
    }

    const taskAssignmentIds = newTaskAssignments.map(ta => ta.id);
    return this.markTaskAssignmentAsOpen(taskAssignmentIds)
      .pipe(
        tap(() => {
          newTaskAssignments.forEach((ta) => {
            ta.status = TaskAssignmentStatusEnum.OPEN;
          });
          for (const task of tasks) {
            if (this.isTaskNew(task)) {
              task.status = TaskStatusEnum.OPEN;
            }
          }
        }),
        map(() => tasks),
        catchError((error) => {
          console.error('Error marking task as open', error);
          return of(null);
        })
      );
  }


  getTaskTypeItem(taskTypeValue: TaskTypeEnum): TaskTypeItem {
    return TASK_TYPES.find((taskType) => taskType.value === taskTypeValue);
  }

  getAssigneeUsers(task: TaskModel): Assignee[] {
    return (task.assignees || []).filter((a) => a.type === AssigneeTypeEnum.USER);
  }

  getAssigneeGroups(task: TaskModel): Assignee[] {
    return (task.assignees || []).filter((a) => a.type === AssigneeTypeEnum.GROUP);
  }

  getAssignedTeamUsers(task: TaskModel): TeamUser[] {
    return (task.team_users || []).filter((a) => a.type === TeamUserTypeEnum.USER);
  }

  getAssignedTeamGroups(task: TaskModel): TeamUser[] {
    return (task.team_users || []).filter((a) => a.type === TeamUserTypeEnum.GROUP);
  }

  getAuthorAndAssigneesForTasks(tasks: TaskModel[]): Observable<TaskModel[]> {
    if (!tasks?.length) {
      return of([]);
    }

    const usersPayload = {
      load_all: 1,
      include_me: 1,
      include_blocked: 1
    };
    const getTeamUsersObs = this.userService.getTeamUsers(usersPayload)
      .pipe(
        map(({users}) => users.map((u) => {
          return {...u, type: TeamUserTypeEnum.USER} as TeamUser;
        }))
      );

    const groupsPayload = {load_all: 1};
    const getTeamGroupsObs = this.groupService.getGroups(groupsPayload)
      .pipe(
        map(({groups}) => groups.map((g) => {
          return {id: g.id, first_name: g.name, type: TeamUserTypeEnum.GROUP} as TeamUser;
        }))
      );

    const observables = [getTeamUsersObs, getTeamGroupsObs];

    return forkJoin(observables)
      .pipe(
        tap(([users, groups]) => {
          tasks.forEach((task) => {
            const userIds = this.getAssigneeUsers(task).map((a) => a.id);
            const assigneeUsers = users.filter((u) => userIds.includes(u.id));
            const groupIds = this.getAssigneeGroups(task).map((a) => a.id);
            const assigneeGroups = groups.filter((u) => groupIds.includes(u.id));
            task.team_users = assigneeUsers.concat(assigneeGroups);
            task.assignments.forEach((assignment) => {
              const teams = this.isUserTaskAssignment(assignment) ? assigneeUsers : assigneeGroups;
              assignment.assignee = teams.find((u) => u.id === assignment.assignee_id);
              if (assignment.answered_by) {
                assignment.answered_user = users.find((u) => u.id === assignment.answered_by);
              }
            });
            task.assignments = task.assignments.filter((ta) => ta.assignee);
            task.author = users.find((u) => u.id === task.author_id);
          });
        }),
        map((users) => tasks)
      );
  }

  isIncomingTask(task: TaskModel): boolean {
    return task.display_type === TaskDisplayTypeEnum.INCOMING;
  }

  isOutgoingTask(task: TaskModel): boolean {
    return task.display_type === TaskDisplayTypeEnum.OUTGOING;
  }

  canEditTask(task: TaskModel): boolean {
    return this.isOutgoingTask(task) && [TaskStatusEnum.NEW, TaskStatusEnum.OPEN].includes(task.status);
  }

  canCloseTask(task: TaskModel): boolean {
    return this.isOutgoingTask(task) && task.status !== TaskStatusEnum.CLOSED;
  }

  canEditRatingTask(task: TaskModel): boolean {
    return [TaskStatusEnum.NEW, TaskStatusEnum.OPEN].includes(task.status);
  }

  canReopenTask(task: any) {
    return this.isOutgoingTask(task) && task.status === TaskStatusEnum.CLOSED;
  }

  canAssigneeViewTask(task: TaskModel): boolean {
    return this.isIncomingTask(task);
  }

  canOwnerViewTask(task: TaskModel): boolean {
    return this.isOutgoingTask(task);
  }

  canAnswerTaskAssignment(task: TaskModel, ta: TaskAssignmentModel): boolean {
    return [TaskStatusEnum.NEW, TaskStatusEnum.OPEN].includes(task.status) &&
      [TaskAssignmentStatusEnum.NEW, TaskAssignmentStatusEnum.OPEN, TaskAssignmentStatusEnum.OVERDUE].includes(ta?.status);
  }

  representativeTaskAssignmentStatus(task: TaskModel): TaskAssignmentStatusEnum {
    if (!task?.assignments?.length) {
      return null;
    }

    if (this.isTaskClosed(task)) {
      return TaskAssignmentStatusEnum.CLOSED;
    }

    if (this.isTaskDone(task)) {
      return TaskAssignmentStatusEnum.DONE;
    }

    const allStatuses = [
      TaskAssignmentStatusEnum.CLOSED,
      TaskAssignmentStatusEnum.DONE,
      TaskAssignmentStatusEnum.OVERDUE,
      TaskAssignmentStatusEnum.OPEN,
      TaskAssignmentStatusEnum.NEW,
    ];

    for (const status of allStatuses) {
      const isAllStatuses = task.assignments.every(ta => ta.status === status);
      if (isAllStatuses) {
        return status;
      }
    }

    const someStatuses = [
      TaskAssignmentStatusEnum.OVERDUE,
      TaskAssignmentStatusEnum.OPEN
    ];

    for (const status of someStatuses) {
      const isSomeStatuses = task.assignments.some(ta => ta.status === status);
      if (isSomeStatuses) {
        return status;
      }
    }

    return TaskAssignmentStatusEnum.NEW;
  }

  myAssignment(task: TaskModel): TaskAssignmentModel {
    if (!task?.assignments?.length) {
      return null;
    }

    const statuses = [
      TaskAssignmentStatusEnum.OVERDUE,
      TaskAssignmentStatusEnum.NEW,
      TaskAssignmentStatusEnum.OPEN,
      TaskAssignmentStatusEnum.DONE,
      TaskAssignmentStatusEnum.CLOSED
    ];
    for (const status of statuses) {
      const assignment = task.assignments.find(ta => {
        if (ta.status !== status) {
          return false;
        }

        return this.isTaskAssignmentAssignedToMe(ta);
      });
      if (assignment) {
        return assignment;
      }
    }

    return null;
  }

  myAssignments(task: TaskModel): TaskAssignmentModel[] {
    if (!task?.assignments?.length) {
      return [];
    }
    return task.assignments.filter(ta => this.isTaskAssignmentAssignedToMe(ta));
  }

  notMyAssignments(task: TaskModel): TaskAssignmentModel[] {
    if (!task?.assignments?.length) {
      return [];
    }
    return task.assignments.filter(ta => !this.isTaskAssignmentAssignedToMe(ta));
  }

  isTaskCreatedByMe(task: TaskModel): boolean {
    return task?.author_id && task.author_id === this.userService.getUser()?.profile?.id;
  }

  isTaskAssignedToMe(task: TaskModel): boolean {
    return this.myAssignments(task)?.length > 0;
  }

  isTaskAssignmentAssignedToMe(ta: TaskAssignmentModel): boolean {
    const myProfile = this.userService.getUser()?.profile;

    if (this.isUserTaskAssignment(ta)) {
      return ta.assignee_id === myProfile?.id;
    }

    const groupIds = (myProfile?.groups || []).map((g) => g.id);
    return groupIds.includes(ta.assignee_id);
  }

  isTaskAssignmentOverdue(ta: TaskAssignmentModel): boolean {
    return ta.status === TaskAssignmentStatusEnum.OVERDUE;
  }

  isTaskAssignmentAnsweredByMe(ta: TaskAssignmentModel): boolean {
    return ta.answered_by === this.userService.getUser()?.profile?.id;
  }

  isReplyTaskType(task: TaskModel): boolean {
    return task.task_type === TaskTypeEnum.TEXT_REPLY;
  }

  isStarRatingTaskType(task: TaskModel): boolean {
    return task.task_type === TaskTypeEnum.STAR_RATING;
  }

  isYesNoAnswerTaskType(task: TaskModel): boolean {
    return task.task_type === TaskTypeEnum.YES_NO_ANSWER;
  }

  isLabelsAnswerTaskType(task: TaskModel): boolean {
    return task.task_type === TaskTypeEnum.LABELS;
  }

  isTaskNew(task: TaskModel): boolean {
    return task?.status === TaskStatusEnum.NEW;
  }

  isTaskDone(task: TaskModel): boolean {
    return task?.status === TaskStatusEnum.DONE;
  }

  isTaskOpen(task: TaskModel): boolean {
    return [TaskStatusEnum.NEW, TaskStatusEnum.OPEN].includes(task?.status);
  }

  isTaskClosed(task: TaskModel): boolean {
    return task?.status === TaskStatusEnum.CLOSED;
  }

  isDoneRatingTask(task: TaskModel): boolean {
    return this.isTaskDone(task) && this.isStarRatingTaskType(task) && task.company_id === this.userService.getUser()?.profile?.company_id;
  }

  isTaskAssignmentTodo(ta: TaskAssignmentModel): boolean {
    return [TaskAssignmentStatusEnum.NEW].includes(ta?.status);
  }

  isTaskAssignmentInProgress(ta: TaskAssignmentModel): boolean {
    return [TaskAssignmentStatusEnum.OPEN, TaskAssignmentStatusEnum.OVERDUE].includes(ta?.status);
  }

  isTaskAssignmentDone(ta: TaskAssignmentModel): boolean {
    return [TaskAssignmentStatusEnum.DONE].includes(ta?.status);
  }

  isTaskAssignmentNew(ta: TaskAssignmentModel): boolean {
    return ta?.status == TaskAssignmentStatusEnum.NEW;
  }

  isUserTaskAssignment(ta: TaskAssignmentModel): boolean {
    return ta?.assignee_type === AssigneeTypeEnum.USER;
  }

  isGroupTaskAssignment(ta: TaskAssignmentModel): boolean {
    return ta?.assignee_type === AssigneeTypeEnum.GROUP;
  }

  isPassValue(val: number | string): boolean {
    return Number(val) === this.PASS_VALUE;
  }

  isNoPassValue(val: number | string): boolean {
    return Number(val) === this.NO_PASS_VALUE;
  }

  getAnswers(task: TaskModel, documentId: number): TaskAssignmentModel[] {
    const results = (task?.assignments || [])
      .filter(ta => [TaskAssignmentStatusEnum.DONE, TaskAssignmentStatusEnum.OVERDUE, TaskAssignmentStatusEnum.CLOSED].includes(ta.status));
    return documentId ? results.filter(ta => ta.document_id === documentId) : results;
  }

  getDoneAnswers(task: TaskModel, documentId: number): TaskAssignmentModel[] {
    const results = (task?.assignments || []).filter(ta => TaskAssignmentStatusEnum.DONE === ta.status);
    return documentId ? results.filter(ta => ta.document_id === documentId) : results;
  }

  calculateAvgStarRating(task: TaskModel, documentId: number): any {
    const ratings = this.getAnswers(task, documentId).map(ta => this.answerToRating(ta.answer));
    return ratings.length > 0 ? (ratings.reduce((a, b) => a + b) / ratings.length).toFixed(1) : 0;
  }

  answerToRating(answer: string): number {
    return answer ? Number(answer) : 0;
  }

  getTaskCreationSuccessMessage(data: { message: string; payload: TaskModel; savedTasks: TaskModel[] },
                                source: 'search_results' | 'monitor_run' | 'collection'): string[] {
    if (data?.savedTasks?.length > 0) {
      let sourceTitle = '';
      switch (source) {
        case 'search_results':
          sourceTitle = 'this search results';
          break;
        case 'monitor_run':
          sourceTitle = 'this monitor run';
          break;
        case 'collection':
          sourceTitle = 'this collection';
          break;
      }
      const manyTasks = data.savedTasks.length > 1;
      const pluralSuffix = manyTasks ? 's' : '';
      const baseMsg = `The task${pluralSuffix} for the selected patent${data.payload.document_ids?.length > 1 ? 's' : ''}
in ${sourceTitle} ${manyTasks ? 'have' : 'has'} been created successfully.
You can view ${manyTasks ? 'them' : 'it'} in the <a href="/tasks" target="_blank">Tasks section</a>`;

      if (manyTasks) {
        return [
          `${baseMsg} or see the results at the following page${pluralSuffix}:`,
          ...data.savedTasks.map((t) => {
            const title = TASK_TYPES.find((tt) => tt.value === t.task_type)?.label;
            return `<a href="/tasks/${t.id}" target="_blank">${title} task</a>`;
          })
        ];
      } else {
        const task = data.savedTasks[0];
        const title = TASK_TYPES.find((tt) => tt.value === task.task_type)?.label?.toLowerCase();
        const taskUrl = `<a href="/tasks/${task.id}" target="_blank">${title} task "${task.subject}"</a>`;
        return [`${baseMsg} or see the results at ${taskUrl} page.`];
      }
    }

    return null;
  }

  updateAssignedTags(tasks: TaskModel[], tags: TagModel[]) {
    tasks.forEach((task) => {
      task.assignments.forEach((assignment) => {
        assignment.assignedTagsByAssignee = tags.filter((tag) => tag.assigner_id === assignment.assignee_id);
      });
      task.assignedTagsByAssignees = tags.filter((tag) => task.assignee_ids.includes(tag.assigner_id));
    });
  }

  clearStoredData(): void {
    this.tasks = [];
  }

  splitTeamUsers(task: TaskModel): { title: string, items: TeamUser[] }[] {
    return [
      {
        title: 'person',
        items: this.getAssignedTeamUsers(task)
      },
      {
        title: 'team',
        items: this.getAssignedTeamGroups(task)
      }];
  }

  getRequestTitle(task: TaskModel, isAnswerable: boolean): string {
    if (isAnswerable) {
      return this.getAnswerableRequestTitle(task);
    }

    if (this.isTaskCreatedByMe(task)) {
      return this.getCreatedRequestTitle(task);
    }

    return this.getOtherRequestTitle(task);
  }

  getAuthorName(task: TaskModel): string {
    const authorName = this.usersTitleTextPipe.transform([task.author]);
    return `<span class="content-heading-h6">${authorName}</span>`
  }

  getAssigneeNames(task: TaskModel): string {
    const users = task.assignments.filter((ta) => this.isUserTaskAssignment(ta)).sort((a, b) => this.isTaskAssignmentAssignedToMe(a) ? -1 : 1);
    const groups = task.assignments.filter((ta) => this.isGroupTaskAssignment(ta)).sort((a, b) => this.isTaskAssignmentAssignedToMe(a) ? -1 : 1);
    const subjects = [...users, ...groups].map((ta) => {
      if (this.isUserTaskAssignment(ta)) {
        if (this.isTaskAssignmentAssignedToMe(ta)) {
          return this.youSubject();
        }
        return `<span class="content-heading-h6">${this.userTitleTextPipe.transform(ta.assignee)}</span>`;
      }
      return `<span class="content-heading-h6">${this.userTitleTextPipe.transform(ta.assignee)}</span>'s team`;
    });

    if (subjects.length <= 2) {
      return subjects.join(' and ');
    }

    if (users.length > 0 && groups.length > 0) {
      return users.length === 1
        ? `${subjects[0]} and ${groups.length} more ${this.pluralizePipe.transform('team', groups.length)}`
        : `${subjects[0]} and ${subjects.length - 1} more people and teams`;
    }

    if (users.length > 0) {
      return `${subjects[0]} and ${subjects.length - 1} more people`;
    }

    return `${subjects[0]} and ${subjects.length - 1} more teams`;
  }

  buildSelfRatingTaskAssignment(): TaskAssignmentModel {
    const profile = this.userService.getUser()?.profile;
    return {
      id: 0,
      task_id: 0,
      assignee_id: profile?.id,
      assignee: profile as TeamUser,
      assignee_type: AssigneeTypeEnum.USER,
      status: TaskAssignmentStatusEnum.OPEN
    } as TaskAssignmentModel;
  }

  ratingTypeForNewTask(): TaskTypeEnum {
    return this.userService.hasBinaryRating ? TaskTypeEnum.YES_NO_ANSWER : TaskTypeEnum.STAR_RATING;
  }

  buildSelfRatingTask(resourceId: number, resourceType: TaskResourceTypeEnum, documentIds: number[]): TaskModel {
    const profile = this.userService.getUser()?.profile;
    return {
      id: 0,
      subject: 'Self rating task',
      author_id: profile?.id,
      author: profile as TeamUser,
      resource_id: resourceId,
      resource_type: resourceType,
      document_ids: documentIds,
      task_type: this.ratingTypeForNewTask(),
      assignees: [{id: profile?.id, type: AssigneeTypeEnum.USER}],
      status: TaskStatusEnum.OPEN,
      assignments: [this.buildSelfRatingTaskAssignment()]
    } as TaskModel;
  }

  isSelfRatingTask(task: TaskModel): boolean {
    const isAnyOneSelfRating = task.assignments.length === 1 && task.assignments[0].assignee_id === task.author_id && task.assignments[0].assignee_type === AssigneeTypeEnum.USER;
    const isMySelfRating = task.id === 0 && task.author_id === this.userService.getUser()?.profile?.id;
    return isAnyOneSelfRating || isMySelfRating;
  }

  isSelfRatingTaskAssignment(task: TaskModel, taskAssignment: TaskAssignmentModel): boolean {
    if (this.isTaskAssignmentAssignedToMe(taskAssignment)) {
      return this.isTaskCreatedByMe(task) || taskAssignment?.id === 0;
    }
    return false;
  }

  /**
   * @description Processes an array of tasks and categorizes them into ratings, answerable requests,
   * and created requests based on their assignments and status.
   *
   * @algorithm
   * 1. Initialize empty arrays for ratings, answerable requests, and created requests
   * 2. Iterate through each task in the input array
   * 3. For each task, call splitTaskIntoRequestsAndRatings to get categorized sub-tasks
   * 4. Aggregate results into respective category arrays
   * 5. Return combined results
   *
   * @param {TaskModel[]} tasks - Array of tasks to be categorized
   * @returns {Object} Object containing three arrays: ratings, answerableRequests, createdRequests and otherRequests
   *
   * @example
   * const tasks = [
   *   {
   *     id: 1,
   *     assignments: [
   *       {
   *         status: 'DONE',
   *         assignee_id: 1,
   *         assignee_type: 'USER'
   *        },
   *       {
   *         status: 'NEW',
   *         assignee_id: 2,
   *         assignee_type: 'GROUP'
   *       }
   *     ]
   *   },
   *   // ... more tasks
   * ];
   *
   * const result = taskSplitter.splitTasksIntoRequestsAndRatings(tasks);
   * // Result structure:
   * // {
   * //   ratings: [TaskModel],           // Completed assignments
   * //   answerableRequests: [TaskModel], // Tasks assigned to current user
   * //   createdRequests: [TaskModel]     // Tasks created by current user with pending assignments
   * //   otherRequests: [TaskModel]     // Tasks created by other users with pending assignments
   * // }
   */
  splitTasksIntoRequestsAndRatings(tasks: TaskModel[]): {
    ratings: TaskModel[],
    answerableRequests: TaskModel[],
    createdRequests: TaskModel[],
    otherRequests: TaskModel[],
  } {
    const _ratings = [];
    const _answerableRequests = [];
    const _createdRequests = [];
    const _otherRequests = [];
    for (let task of tasks) {
      const {ratings, answerableRequests, createdRequests, otherRequests} = this.splitTaskIntoRequestsAndRatings(task);
      _ratings.push(...ratings);
      _answerableRequests.push(...answerableRequests);
      _createdRequests.push(...createdRequests);
      _otherRequests.push(...otherRequests);
    }
    return {
      ratings: _ratings,
      answerableRequests: _answerableRequests,
      createdRequests: _createdRequests,
      otherRequests: _otherRequests
    };
  }

  /**
   * @description Processes a single task and categorizes its assignments into ratings,
   * answerable requests, created requests, other requests based on specific conditions.
   *
   * @algorithm
   * 1. Initialize empty arrays for ratings and pending assignments
   * 2. Iterate through each assignment in the task
   * 3.   If assignment is done, clone task with that assignment and add to ratings
   * 4.   If assignment is not done, add to pending assignments
   * 5.   Check if any assignment is assigned to the current user/group, task is not created by current user
   * 6.     If yes, set hasMyAssignments to true
   * 7. If there are pending assignments, clone task with those assignments
   * 8. If hasMyAssignments is true, return ratings and answerable requests from cloned task
   * 9. If task is created by current user, return ratings and created requests from cloned task
   * 10. Otherwise, return ratings and other requests from cloned task
   *
   * @param {TaskModel} task - Task object containing assignments to be processed
   * @returns {Object} Object containing three arrays:
   *    - ratings: Array of tasks with done assignments
   *    - answerableRequests: Array of tasks assigned to current user/group
   *    - createdRequests: Array of tasks created by current user with in-progress assignments
   *    - otherRequests: Array of tasks created by other users without assigning to current user/group
   *
   * @example
   * const task = {
   *   id: 1,
   *   creatorId: 'currentUser',
   *   assignments: [
   *     {
   *       status: 'DONE',
   *       assignee_id: 1,
   *       assignee_type: 'USER',
   *     },
   *     {
   *       status: 'NEW',
   *       assignee_id: 2,
   *       assignee_type: 'GROUP',
   *     }
   *   ]
   * };
   *
   * const result = splitTaskIntoRequestsAndRatings(task);
   * // result = {
   * //   ratings: [{ // clone of task with done assignment }],
   * //   answerableRequests: [{ // clone of task that is assigned to current user or group }],
   * //   createdRequests: []
   * //   otherRequests: []
   * // }
   */
  splitTaskIntoRequestsAndRatings(task: TaskModel): {
    ratings: TaskModel[],
    answerableRequests: TaskModel[],
    createdRequests: TaskModel[],
    otherRequests: TaskModel[],
  } {
    const ratings = [];
    const isCreatedByMe = this.isTaskCreatedByMe(task);

    let hasMyAssignments = false;
    const pendingAssignments = [];

    for (const ta of task.assignments) {
      if (this.isTaskAssignmentDone(ta)) {
        ratings.push(this.cloneTaskWithAssignments(task, [ta]));
      } else {
        pendingAssignments.push(ta);
        if (!isCreatedByMe && this.isTaskAssignmentAssignedToMe(ta)) {
          hasMyAssignments = true;
        }
      }
    }

    const otherRequests = pendingAssignments.length ? [this.cloneTaskWithAssignments(task, pendingAssignments)] : [];

    if (hasMyAssignments) {
      return {ratings, answerableRequests: otherRequests, createdRequests: [], otherRequests: []};
    }

    if (isCreatedByMe) {
      return {ratings, answerableRequests: [], createdRequests: otherRequests, otherRequests: []};
    }

    return {ratings, answerableRequests: [], createdRequests: [], otherRequests};
  }

  canEditRatingTaskAssignment(ta: TaskAssignmentModel) {
    return ta.is_answerable;
  }

  canDeleteRatingTaskAssignment(ta: TaskAssignmentModel) {
    return ta.is_deletable;
  }

  deleteTaskAssignment(taskAssignmentId: number): Observable<any> {
    return this.apiService.delete(`web/tasks/assignments/${taskAssignmentId}`);
  }

  classifyTasks(tasks: TaskModel[]): TaskStats {
    const ratingTitles = ['one_point', 'two_point', 'three_point', 'four_point', 'five_point'];
    const classifiedTasks = {
      todo: [],
      in_progress: [],
      five_point: [],
      four_point: [],
      three_point: [],
      two_point: [],
      one_point: [],
      tasks_by_assignee: {},
      all_tasks: tasks,
      all_ratings: []
    } as TaskStats;

    tasks.forEach(task => {
      task.assignments.forEach(ta => {
        const clonedTask = this.cloneTaskWithAssignments(task, [ta]);
        if (this.isTaskAssignmentTodo(ta)) {
          classifiedTasks.todo.push(clonedTask);
        } else if (this.isTaskAssignmentInProgress(ta)) {
          classifiedTasks.in_progress.push(clonedTask);
        } else if (this.isTaskAssignmentDone(ta)) {
          const rating = this.answerToRating(ta.answer);
          if (rating) {
            classifiedTasks[ratingTitles[rating - 1]].push(clonedTask);
          }
        }

        const assigneeKey = `${ta.assignee_id}_${ta.assignee_type}`;
        const existing = classifiedTasks.tasks_by_assignee[assigneeKey];
        const {
          ratings,
          answerableRequests,
          createdRequests,
          otherRequests
        } = this.splitTaskIntoRequestsAndRatings(clonedTask);

        classifiedTasks.all_ratings.push(...ratings);

        if (existing) {
          existing.all_tasks.push(clonedTask);
          existing.ratings.push(...ratings);
          existing.answerableRequests.push(...answerableRequests);
          existing.createdRequests.push(...createdRequests);
          existing.otherRequests.push(...otherRequests);
        } else {
          classifiedTasks.tasks_by_assignee[assigneeKey] = {
            assignee: {
              id: ta.assignee_id,
              type: ta.assignee_type
            },
            team_user: ta.assignee,
            all_tasks: [clonedTask],
            ratings,
            answerableRequests,
            createdRequests,
            otherRequests
          };
        }
      });
    });

    classifiedTasks.todo = classifiedTasks.todo.sort(RatingSorter.sortByDeadline);
    classifiedTasks.in_progress = classifiedTasks.in_progress.sort(RatingSorter.sortByDeadline);

    for (const assigneeKey in classifiedTasks.tasks_by_assignee) {
      const tasksByAssignee = classifiedTasks.tasks_by_assignee[assigneeKey];
      tasksByAssignee.ratings = tasksByAssignee.ratings.sort((a, b) => {
        return RatingSorter.sortByOption(a, b, SortRatingsOptionEnum.RECENTLY_ADDED, this.answerToRating);
      });
      tasksByAssignee.answerableRequests = tasksByAssignee.answerableRequests.sort(RatingSorter.sortByDeadline);
      tasksByAssignee.createdRequests = tasksByAssignee.createdRequests.sort(RatingSorter.sortByDeadline);
    }

    return classifiedTasks;
  }

  cloneTaskWithAssignments(task: TaskModel, assignments: TaskAssignmentModel[]): TaskModel {
    const cloneTask = cloneDeep(task) as TaskModel;
    cloneTask.assignments = assignments;
    cloneTask.assignee_ids = assignments.map((ta) => ta.assignee_id);
    cloneTask.assignees = assignments.map((ta) => ({
      id: ta.assignee_id,
      type: ta.assignee_type
    } as Assignee));
    cloneTask.team_users = assignments.map((ta) => ta.assignee);
    return cloneTask;
  }

  private getAnswerableRequestTitle(task: TaskModel): string {
    if (!task) {
      return '';
    }

    const assigneeNames = this.getAssigneeNames(task);

    if (this.isTaskCreatedByMe(task)) {
      if (this.isUserTaskAssignment(this.myAssignment(task))) {
        return 'Rate this patent yourself.';
      }
      return `${assigneeNames} has been requested to rate this patent.`;
    }

    return `${this.getAuthorName(task)} has requested ${assigneeNames} to rate this patent.`;
  }

  private getCreatedRequestTitle(task: TaskModel): string {
    if (!task) {
      return '';
    }
    return `You have requested ${this.getAssigneeNames(task)} to rate this patent.`;
  }

  private getOtherRequestTitle(task: TaskModel): string {
    if (!task) {
      return '';
    }
    return `${this.getAuthorName(task)} has requested ${this.getAssigneeNames(task)} to rate this patent.`;
  }

  private youSubject(subject = 'you'): string {
    return `<span class="content-heading-h6">${subject}</span>`
  }
}
