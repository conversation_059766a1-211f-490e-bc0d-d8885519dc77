export const abstract = `<div>Disclosed is a MP3 car player which prevents a compact disk (CD)\nplayer equipped in a car from discontinuing playing of a CD as a result of\nbumpy road conditions, etc., is able to use both conventional CDs and MP3\nCD-ROMs, and compresses and decompresses audio data at a high\ncompression ratio to enable the recording of 150 ∼ 200 songs in a single CD-ROM\nwithout the loss of sound quality, thereby making use of the expensive\nCD changer unnecessary. The present invention comprises a file type detector,\nan MP3 file input unit, a peripheral interface unit, a controller unit, an MP3\ndecoder, and a digital/analog converter.</div>`;

export const claims = `<div _ngcontent-ubq-c290="" id="claims-text" ng-reflect-text-html="&lt;ul class=&quot;claims&quot;&gt;&lt;li class=&quot;" ng-reflect-smart-highlight-items="" ng-reflect-figma-class="true" ng-reflect-ng-class="[object Object]" class="claims-tree figma-claims-tree"><div><ul class="claims"><li class="claim-root claim-node claim-no-children"><div class="claim" id="oct-claim-1"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>An MP3 car player, comprising:<br></p><p>a display unit displaying information of replaying audio data;</p><p>a keypad controlling drives of compact disks (CDs) and compact disk<br>read only memories (CD-ROMs);</p><p>a speaker unit outputting the audio data in a format audible to the<br>human ear;</p><p>a file type detector reading source data of the CDs and CD-ROMs and<br>determining whether the file of the data is an ordinary audio CD file or an MP3<br>audio file;</p><p>an MP3 file input unit receiving the MP audio file detected from the file<br>type detector, and detecting errors and temporarily storing the data in a first<br>memory;</p><p>an MP3 decoder receiving the MP3 data from the MP3 file input unit<br>and restoring compressed data by use of a Huffman decoding method, and<br>converting the data into pulse code modulation data;</p><p>a controller unit reading the data stored in the MP3 file input unit,<br>controlling the display unit and the keypad, and writing data to the MP3<br>decoder;</p><p>a peripheral interface unit controlling the control signals between the<br>display unit, the keypad, and the controller unit; and</p><p>a digital/analog converter receiving ordinary audio CD data from the file<br>type detector or receiving pulse code modulation digital data from the MP3 <br>decoder and converting the data into analog data and outputting the data to the<br>speaker unit.</p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="oct-claim-2"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>The MP3 car player in claim 1, wherein the MP3 file input unit<br>comprises:<br></p><p>an integrated development environment (IDE) interface unit receiving<br>MP3 audio files determined by the file type detector and performing IDE<br>communications far use in a memory card;</p><p>an error detector receiving the MP3 file data through the IDE interface<br>unit and checking whether or not the data are normally received, and when<br>errors are detected in the data, returning to an initialization step; and</p><p>a memory block receiving addresses of a second memory, chip<br>selection signals, and read and write signals from the controller, and controlling<br>the second memory when temporarily storing the errorless MP3 data in the first<br>memory in order to decode the errorless data before transmitting the data to the<br>MP3 decoder.</p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="oct-claim-3"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>The MP3 car player in claim 1, wherein the peripheral interface unit<br>comprises:<br></p><p>a display interface unit performing interface operations an displaying<br>items such as titles of audio data or track numbers on the display unit; and</p><p>a keypad interface unit controlling the operations between the keypad<br>and the controller, the operations including controlling keys which operate the<br>CDs and CD-ROMs and selecting rows and columns of the keypad.</p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="oct-claim-4"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>The MP3 car player in claim 1, wherein the controller unit comprises: <br></p><p>a controller reading the data stored in the MP3 file input unit, and<br>displaying information of the audio data on the display device, and outputting<br>the data to the MP3 decoder according to input from the keypad; and</p><p>a third memory storing instructions to drive the controller, address map<br>information, and bootstrap signals to initialize the operation when power is<br>supplied.</p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="oct-claim-5"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>The MP3 car player in claim 1, wherein the MP3 decoder comprises:<br></p><p>an MP3 interface unit handling the data received from the MP3 file input<br>unit for each serial bit;</p><p>a control and status register controlling a response signal which<br>conveys information of whether or not the data provided to the MP3 interface<br>unit have errors to the controller, and displaying an empty or full status of the<br>provided data;</p><p>a fourth memory receiving serial data from the MP3 interface unit and<br>storing the data;</p><p>a parser receiving data from a fifth memory and encoding the data<br>using the Huffman decoding method;</p><p>an inverse modified discrete cosine transform unit performing inverse<br>modified discrete cosine transform on the data encoded from the parser, and<br>transforming the data into pulse code modulation data;</p><p>a sixth memory temporarily storing the pulse code modulation data; and</p><p>a pulse code modulation interface unit receiving the pulse code<br>modulation data from the sixth memory and outputting the data to the <br>digital/analog converter.</p></div></div></li></ul></div></div>`;


export const description = `<div _ngcontent-ubq-c291="" id="description-text" sectioncollapseclass="button-main-tertiary-grey button-square button-small" class="figma-innerHTML ng-tns-c291-0 ng-star-inserted" ng-reflect-section-collapse-class="button-main-tertiary-grey butt" ng-reflect-text-html="&lt;article&gt;&lt;h2&gt;&lt;u&gt;&lt;b&gt;BACKGROUND " ng-reflect-figma-class="true" ng-reflect-display-heading-sections="true" ng-reflect-display-section-anchors="true"><div><article><h2 id="section-heading-backgroundoftheinvention" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-t5dmsofo9k"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-mfyhr8pt5j"><div>BACKGROUND OF THE INVENTION</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-backgroundoftheinvention"></i></h2><h2 id="section-heading-afieldoftheinvention" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-ae71bypq74"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-aearu6su2w"><div>(a) Field of the Invention</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-afieldoftheinvention"></i></h2><div id="section-content-tmp-d1z9gveszp" class="figma-section-content"><div class="desc-paragraph" id="oct-p-1"><div id="paragraph-number-oct-p-1" class="displayed-paragraph-number noselect content-color-primary content-body-medium content-style-semi-bold pa-text-non-select">[1]</div>The present invention relates to a Moving Picture Experts Group<br>(MPEG) audio layer-3 (MP3) player. More specifically, the present invention<br>relates to a MP3 car player equipped with a compact disk (CD) player in which<br>interruptions in the playing of CDs caused by bumpy road conditions, etc. are<br>prevented, and which is able to compress and decompress digital audio data at<br>high compression rates without loss of sound quality.</div></div><h2 id="section-heading-bdescriptionoftherelatedart" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-sc6p3l7xqh"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-369knh2wl2"><div>(b) Description of the Related Art<span data-id="cm-2456" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(251, 111, 9);"></span></div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-bdescriptionoftherelatedart"></i></h2><div id="section-content-tmp-us3jkg755z" class="figma-section-content"><div class="desc-paragraph" id="oct-p-2"><div id="paragraph-number-oct-p-1" class="displayed-paragraph-number noselect content-color-primary content-body-medium content-style-semi-bold pa-text-non-select">[2]</div><span data-id="cm-2456" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">Conventional car CD players use a memory in order to prevent</span><br><span data-id="cm-2456" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">discontinuous sound playing. However, the small capacity of the memory is</span><br><span data-id="cm-2456" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">such that these interruptions can not be completely prevented. Also, since the</span><br><span data-id="cm-2456" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(251, 111, 9);">CD player</span> only plays audio CDs, the CD player cannot process data on the CD<br>Read Only Memory (CD-ROM) which holds far greater compression rates.</div></div><div id="section-content-tmp-plz293ulkg" class="figma-section-content"><div class="desc-paragraph" id="oct-p-3">In most cases, the MP3 player is used together with a personal<br>computer (PC) which performs downloading and decoding of audio data before<br>uploading into the player. Since the PC utilizes software to perform these<br>functions, the MP3 player is ultimately dependent on the performance of the PC,<br>thereby making real-time implementation difficult unless the user owns a top-of-the-line<br>PC. Such use together with a PC also limits portability. Further, even if<br>the MP3 player is equipped allowing full portability (i.e., to internally perform<br>downloading and decoding), high power consumption is a serious drawback <br>that results in a reduced amount of time the user can use the MP3 player on<br>battery power.</div></div><h2 id="section-heading-summaryoftheinvention" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-nbcauijvv7"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-m6ajcrdnoi"><div>SUMMARY OF THE INVENTION</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-summaryoftheinvention"></i></h2><div id="section-content-tmp-itutnti0o4" class="figma-section-content"><div class="desc-paragraph" id="oct-p-4">It is an object of the present invention to provide an MP3 car player<br>having a large memory allowing audio data to be temporarily stored and output,<br>and which can use both the conventional CD and CD-ROM, thereby removing<br>the need for the expensive CD changer often used together with CD players<br>installed in automobiles.</div></div><div id="section-content-tmp-ibhvqp6mmd" class="figma-section-content"><div class="desc-paragraph" id="oct-p-5">It is another object of the present invention to provide an MP3 car<br>player which implements the use of an MPEG audio layer-3 chip to drive an<br>MPEG decoder, and realizes the integration of peripheral circuits and an<br>interface unit in a separate, single chip.</div></div><div id="section-content-tmp-iul8x1se8l" class="figma-section-content"><div class="desc-paragraph" id="oct-p-6">In one aspect of the present invention, the MP3 car player comprises a<br>display unit displaying information of replaying audio data; a keypad controlling<br>drives of compact disks (CDs) and compact disk read only memories (CD-ROMs);<br>a speaker unit outputting the audio data in a format audible to the<br>human ear a file type detector reading source data of the CDs and CD-ROMs<br>and determining whether the file of the data is an ordinary audio CD file or an<br>MP3 audio file: an MP3 file input unit receiving the MP audio file detected from<br>the file type detector, and detecting errors and temporarily storing the data in a<br>first memory; an MP3 decoder receiving the MP3 data from the MP3 file input<br>unit and restoring compressed data by use of a Huffman decoding method, and <br>converting the data into pulse code modulation data; a controller unit reading<br>the data stored in the MP3 file input unit, controlling the display unit and the<br>keypad, and writing data to the MP3 decoder; a peripheral interface unit<br>controlling the control signals between the display unit, the keypad, and the<br>controller unit; and a digital/analog converter receiving ordinary audio CD data<br>from the file type detector or receiving pulse code modulation digital data from<br>the MP3 decoder and converting the data into analog data and outputting the<br>data to the speaker unit.</div></div><div id="section-content-tmp-0ns24kuzpz" class="figma-section-content"><div class="desc-paragraph" id="oct-p-7">The MP3 file input unit comprises an integrated development<br>environment (IDE) interface unit receiving MP3 audio files determined by the<br>file type detector and performing IDE communications for use in a memory<br>card; an error detector receiving the MP3 file data through the IDE interface unit<br>and checking whether or not the data are normally received, and when errors<br>are detected in the data, returning to an initialization step; and a memory block<br>receiving addresses of a second memory, chip selection signals, and read and<br>write signals from the controller, and controlling the second memory when<br>temporarily storing the errorless MP3 data in the first memory in order to<br>decode the errorless data before transmitting the data to the MP3 decoder.</div></div><div id="section-content-tmp-zm2l79b42o" class="figma-section-content"><div class="desc-paragraph" id="oct-p-8">The peripheral interface unit comprises a display interface unit<br>performing interface operations on displaying items such as titles of audio data<br>or track numbers on the display unit; and a keypad interface unit controlling the<br>operations between the keypad and the controller, the operations including<br>controlling keys which operate the CDs and CD-ROMs and selecting rows and <br>columns of the keypad.</div></div><div id="section-content-tmp-9v3z7qa2uh" class="figma-section-content"><div class="desc-paragraph" id="oct-p-9">The controller unit comprises a controller reading the data stored in the<br>MP3 file input unit, and displaying information of the audio data on the display<br>device, and outputting the data to the MP3 decoder according to input from the<br>keypad; and a third memory storing instructions to drive the controller, address<br>map information, and bootstrap signals to initialize the operation when power is<br>supplied.</div></div><div id="section-content-tmp-ezj1dyzhu2" class="figma-section-content"><div class="desc-paragraph" id="oct-p-10">The MP3 decoder comprises an MP3 interface unit handling the data<br>received from the MP3 file input unit for each serial bit; a control and status<br>register controlling a response signal which conveys information of whether or<br>not the data provided to the MP3 interface unit have errors to the controller, and<br>displaying an empty or full status of the provided data; a fourth memory<br>receiving serial data from the MP3 interface unit and storing the data; a parser<br>receiving data from a fifth memory and encoding the data using the Huffman<br>decoding method; an inverse modified discrete cosine transform unit performing<br>inverse modified discrete cosine transform on the data encoded from the parser,<br>and transforming the data into pulse code modulation data; a sixth memory<br>temporarily storing the pulse code modulation data; and a pulse code<br>modulation interface unit receiving the pulse code modulation data from the<br>sixth memory and outputting the data to the digital/analog converter.</div></div><h2 id="section-heading-briefdescriptionofthedrawings" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-o38xf9hr35"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-wz6xgtq4mu"><div>BRIEF DESCRIPTION OF THE DRAWINGS</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-briefdescriptionofthedrawings"></i></h2><div id="section-content-tmp-4juzp4g59u" class="figma-section-content"><div class="desc-paragraph" id="oct-p-11">The accompanying drawings, which are incorporated in and constitute <br>a part of the specification, illustrate an embodiment of the invention, and,<br>together with the description, serve to explain the principles of the invention:<br><li>FIG. 1 is a schematic diagram of an MP3 car player configuration<br>according to a preferred embodiment of the present invention;</li><li>FIG. 2 is a block diagram of an MP3 decoder shown in FIG. 1; and</li><li>FIG. 3 is a flow chart of an operation of the MP3 player shown in FIG. 1.</li></div></div><h2 id="section-heading-detaileddescriptionofthepreferredembodiments" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-v0x4zrew3c"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-en2dnw3pkf"><div>DETAILED DESCRIPTION OF THE PREFERRED EMBODIMENTS</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-detaileddescriptionofthepreferredembodiments"></i></h2><div id="section-content-tmp-7vbulsz6cf" class="figma-section-content"><div class="desc-paragraph" id="oct-p-12">In the following detailed description, only the preferred embodiment of<br>the invention has been shown and described, simply by way of illustration of the<br>best mode contemplated by the inventor(s) of carrying out the invention. As will<br>be rearized, the invention is capable of modification in various obvious respects,<br>all without departing from the invention. Accordingly, the drawings and<br>description are to be regarded as illustrative in nature, and not restrictive.</div></div><div id="section-content-tmp-h0uq8edq7k" class="figma-section-content"><div class="desc-paragraph" id="oct-p-13">FIG. 1 shows a schematic diagram of an MP3 car player configuration<br>according to a preferred embodiment of the present invention.</div></div><div id="section-content-tmp-m6do5t0mtz" class="figma-section-content"><div class="desc-paragraph" id="oct-p-14">A file type detector 100 receives source data of CDs and CD-ROMs.<br>Output terminals of the file type detector 100 are coupled to an integrated<br>development environment (IDE) interface unit 201 (or digital/analog converter<br>depending on file type). The IDE interface unit 201 receives MP3 audio files<br>from the file type detector 100.</div></div><div id="section-content-tmp-j2j01dtjv8" class="figma-section-content"><div class="desc-paragraph" id="oct-p-15"><div id="paragraph-number-oct-p-1" class="displayed-paragraph-number noselect content-color-primary content-body-medium content-style-semi-bold pa-text-non-select">[15]</div>An error detector 202 receives the MP3 audio files from the IDE<br>interface unit 201, and either is reloaded and returned to an initialization step or <br>the error detector 202 outputs data to a memory block 203, depending on<br>whether or not errors are present in the audio data. The IDE interface unit 201,<br>the error detector 202, and the memory block 203 comprise an MP3 file input<br>unit 200.</div></div><div id="section-content-tmp-gu502nky26" class="figma-section-content"><div class="desc-paragraph" id="oct-p-16"><div id="paragraph-number-oct-p-1" class="displayed-paragraph-number noselect content-color-primary content-body-medium content-style-semi-bold pa-text-non-select">[16]</div>The memory block 203 receives the MP3 audio files from the error<br>detector 202 and temporarily stores the files.</div></div><div id="section-content-tmp-7gijnzasct" class="figma-section-content"><div class="desc-paragraph" id="oct-p-17">A controller 401 receives and transmits memory address control signals<br>from/to the memory block 203, and a ROM 402 receives and transmits<br>bootstrap signals from/to the controller 401.</div></div><div id="section-content-tmp-71gmxcrh6v" class="figma-section-content"><div class="desc-paragraph" id="oct-p-18">A liquid crystal display (LCD) interface unit 301 and a keypad interface<br>unit 302 receive and transmit control signals from/to the controller 401. The<br>LCD interface unit 301 and the keypad interface unit 302 comprise a peripheral<br>interface unit 300.</div></div><div id="section-content-tmp-6chqrmyu9b" class="figma-section-content"><div class="desc-paragraph" id="oct-p-19"><div id="paragraph-number-oct-p-1" class="displayed-paragraph-number noselect content-color-primary content-body-medium content-style-semi-bold pa-text-non-select">[19]</div>An MP3 decoder 500 receives the data stored in the memory block 203,<br>and receives and transmits data status control signals from/to the controller 401.</div></div><div id="section-content-tmp-yf1m51erq9" class="figma-section-content"><div class="desc-paragraph" id="oct-p-20">A digital/analog converter 600 receives audio CD data from the file type<br>detector 100, or receives pulse code modulation digital data from the MP3<br>decoder 500, converts the data into analog data, amplifies the data in an<br>operational amplifier (not shown), and outputs the converted and amplified data<br>to a speaker.</div></div><div id="section-content-tmp-3z5ih16rst" class="figma-section-content"><div class="desc-paragraph" id="oct-p-21">An operation of the MP3 car player will now be described with<br>reference to FIG. 1 and the flow chart of FIG. 3. First, the file type detector 100<br>receives audio data and detects whether the file of the audio data is an ordinary <br>CD file or an MP3 file in step S100.</div></div><div id="section-content-tmp-pms538okdh" class="figma-section-content"><div class="desc-paragraph" id="oct-p-22">If the input audio data are ordinary audio CD files, the pulse code<br>modulation audio digital data are transmitted to the digital/analog converter 600<br>in step S105, after which the digital data are converted into analog data in step<br>S160. Next, the analog data are amplified and output to the speaker in step<br>S170.</div></div><div id="section-content-tmp-dbpm8iam7u" class="figma-section-content"><div class="desc-paragraph" id="oct-p-23">If it is determined in step S100 that the input audio data are MP3 files,<br>the data are provided to the MP3 file input unit 200 where the data pass<br>through the IDE interface unit 201 and are transmitted to the error detector 202.</div></div><div id="section-content-tmp-cls4e96gty" class="figma-section-content"><div class="desc-paragraph" id="oct-p-24">The MP3 file input unit 200, performing IDE communications in the IDE<br>interface unit 201 for use in a memory card, is a type of disk drive interface<br>which uses direct memory access (DMA) channels. The error detector 202,<br>after receiving the data from the IDE interface unit 201, checks whether there<br>exist any errors in the data of the input MP3 files in step S110. If it is<br>determined by the error detector 202 that there are errors in the MP3 files, the<br>error detector 202 is reloaded and the operation of the MP3 player is returned<br>to the first step in step S120. However, if it is determined in step S110 that<br>there are no errors in the input MP3 files, the memory block 203, in step S130,<br>stores the errorless MP3 data for a subsequent decoding operation, transmits<br>the data to the MP3 decoder 500, and receives addresses of a flash memory,<br>chip selecting signal, read/write signal from the controller 401 in order to control<br>the data.</div></div><div id="section-content-tmp-pv6cicicwv" class="figma-section-content"><div class="desc-paragraph" id="oct-p-25">The peripheral interface unit 300 receives the errorless MP3 data, and, <br>in step S140, performs an interface function through (a) the LCD interface unit<br>301 for the display of the titles of the audio data, track numbers, etc., and (b)<br>the keypad interface unit 302 for enabling the user to control various operations<br>of the MP3 player. With regard to the interface with the keypad interface unit<br>302, keys such as play, pause, next, and back are provided so the user is able<br>to control operations between a keypad of the keypad interface unit 302 and<br>the controller 401.</div></div><div id="section-content-tmp-iwmg9zmlly" class="figma-section-content"><div class="desc-paragraph" id="oct-p-26">Following the above, the MP3 decoder 500 receives the MP3 data from<br>the memory block 203 after the data has passed through the controller 401,<br>and decompresses the compressed data using the Huffman decoding<br>methodafter which the MP3 decoder 500 converts the data into pulse code<br>modulation data then transmits the data to the digital/analog converter 600 in<br>step S150. The digital/analog converter 600 converts the data received from<br>the digital/analog converter 600 into analog data in step S160, then amplifies<br>the data in the operational amplifier and outputs the data to the speaker in step<br>S170.</div></div><div id="section-content-tmp-iiyksz9pq7" class="figma-section-content"><div class="desc-paragraph" id="oct-p-27">During the operation of the MP3 <span data-id="cm-2638" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(251, 111, 9);">player described above, the controller</span><br><span data-id="cm-2638" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">unit 400 performs control such that the controller </span><span data-id="cm-2639" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(251, 111, 9);"><span data-id="cm-2638" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">401 selects the address of the</span></span><br><span data-id="cm-2639" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);"><span data-id="cm-2638" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);">flash memory of the memory block 203, the error detector 202 receives data</span></span><br><span data-id="cm-2639" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(251, 111, 9);"><span data-id="cm-2638" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(251, 111, 9);">according to a read signal, the titles of the audio</span> data or track numbers are</span><br><span data-id="cm-2639" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(251, 111, 9);">displayed by the LCD interface unit 301</span>, input of the keypad is received from<br>the keypad interface unit 302, and the data stored in the memory block 203 are<br>output to the MP3 decoder 500 according to a write signal. </div></div><div id="section-content-tmp-cheapnh5q5" class="figma-section-content"><div class="desc-paragraph" id="oct-p-28">The ROM 402 stores instructions to drive the controller 401, address<br>map information, and bootstrap signals to initialize the operation of the MP3<br>player when power is supplied.</div></div><div id="section-content-tmp-36hko7c4kb" class="figma-section-content"><div class="desc-paragraph" id="oct-p-29">The MP3 decoder 500 will now be described.</div></div><div id="section-content-tmp-4477cly2ou" class="figma-section-content"><div class="desc-paragraph" id="oct-p-30">FIG. 2 is a block diagram of the MP3 decoder 500 shown in FIG. 1.</div></div><div id="section-content-tmp-rzcxh7hhs8" class="figma-section-content"><div class="desc-paragraph" id="oct-p-31">The MP3 decoder 500 comprises an MP3 bit stream interface unit 501;<br>a control/status register 502; a parser 503; an inverse modified discrete cosine<br>transform (MDCT) unit 504; a fifth memory 506; a sixth memory 507; and a<br>pulse code modulation interface unit 505.</div></div><div id="section-content-tmp-zpb4irq2r6" class="figma-section-content"><div class="desc-paragraph" id="oct-p-32">The MP3 bit stream interface unit 501 receives MP3 bit stream data<br>from the MP3 file input unit 200, and sequentially processes the MP3 data into<br>units of bits. The control/status register 502 controls a response signal which<br>conveys the information on the errors of the received data to the controller unit<br>400 in order to perform decoding, and indicates a full or empty status of the<br>received data.</div></div><div id="section-content-tmp-b0mat611r7" class="figma-section-content"><div class="desc-paragraph" id="oct-p-33">The fifth memory 506 receives and stores the serial data from the MP3<br>bit stream interface unit 501. The fifth memory 506 is implemented in a dual<br>port random access memory (RAM).</div></div><div id="section-content-tmp-lrggq3axi4" class="figma-section-content"><div class="desc-paragraph" id="oct-p-34">The parser 503 receives data from the fifth memory 506, and encodes<br>the data using the Huffman decoding method. The Huffman decoding method is<br>a statistical encoding method in which frequently generated data codes are<br>represented with a small number of bits, while data codes that are not<br>frequently generated are represented with a relatively large number of bits, <br>thereby reducing the overall-size of the data. In the method, generation counts<br>of the data to be compressed must be checked, and the generation counts are<br>stored in a counting table, after which optimized codes are assigned to each<br>data.</div></div><div id="section-content-tmp-g2s006ejd6" class="figma-section-content"><div class="desc-paragraph" id="oct-p-35">The inverse MDCT unit 504 converts the data encoded in the parser<br>503 into pulse code modulation data through the inverse MDCT. In discrete<br>cosine transform (DCT), natural sound is broken down into frequency<br>components from low to high frequencies, and high frequency components are<br>removed using the characteristic in which sound concentrates in low frequency<br>components, thereby compressing the data. However, since distortions occur in<br>the compression and expansion processes, the data are not completely<br>restored into original form. In order to prevent this problem, a modified DCT<br>(MDCT) has been developed. The MDCT precisely divides the frequency band<br>so as to efficiently use perceptual audio coding and psychoacoustic<br>compression to remove all superfluous information (i.e., the redundant and<br>irrelevant parts of a sound signal that the human ear does not hear). An<br>encoder divides the input data into detailed frequency bands. A cosine<br>transform operation is performed on the frequency bands. Coefficients obtained<br>from the cosine transform operation are divided into direct current components<br>and alternating current components, and are independently quantified, thereby<br>achieving a very large rate of data compression. The MDCT performed in<br>reverse results in inverse MDCT.</div></div><div id="section-content-tmp-yvgehq6oda" class="figma-section-content"><div class="desc-paragraph" id="oct-p-36">The sixth memory 507, which is comprised of first-in first-out memories, <br>temporarily stores the pulse code modulation data converted in the inverse<br>MDCT unit 504, and outputs the data to the digital/analog converter 600<br>through the pulse code modulation interface unit 505.</div></div><div id="section-content-tmp-v5d3ijc6yi" class="figma-section-content"><div class="desc-paragraph" id="oct-p-37">In the MP3 car player of the present invention as described above, a<br>large capacity of audio data is temporarily stored in memory and output by<br>control of the user such that interruptions in the playing of audio data (caused<br>by bumpy road conditions, etc.) by a compact disk (CD) player equipped in a<br>car are prevented. Further, the MP3 car player of the present invention is able<br>to use both the conventional CD and MP3 CD-ROMs, and is capable of<br>compressing and decompressing audio data at a high compression ratio to<br>allow the recording of 150 ∼ 200 songs on a single CD-ROM without losing any<br>sound quality. Such a feature makes the use of the expensive CD changer<br>typically used with automobile CD players unnecessary. In addition, since an<br>exclusive chip for performing MP3 functions can be implemented in hardware,<br>the MP3 audio data can be played in real-time and without the need for a PC.<br>Also, the peripheral circuit and interface circuit can be implemented in a single<br>chip so that chip area and power consumption are reduced.</div></div><div id="section-content-tmp-dpdmoh52cf" class="figma-section-content"><div class="desc-paragraph" id="oct-p-38">While this invention has been described in connection with what is<br>presently considered to be the most practical and preferred embodiment, it is to<br>be understood that the invention is not limited to the disclosed embodiments,<br>but, on the contrary, is intended to cover various modifications and equivalent<br>arrangements included within the spirit and scope of the appended claims.</div></div></article></div></div>`;
