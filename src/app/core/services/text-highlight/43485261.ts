export const abstract = `<div _ngcontent-ipo-c291="" id="abstract-text" class="ng-tns-c291-0 ng-star-inserted" ng-reflect-text-html="The invention relates to a met"><div><span data-id="cm-1801" class="pa-comment-highlight" style="border-bottom-color: rgb(255, 0, 0);">The invention relates to a<span data-id="hl-4535" class="highlight" style="background-color: rgb(248, 255, 168); box-shadow: none;"> method for configuring</span> infotainment applications in a motor vehicle, comprising the following steps: providing a motor vehicle having at least one infotainment application and having a control unit, which is involved in the at least one infotainment application, providing a configuration interface of the control unit, by means of which configuration interface the c<span data-id="hl-4601" class="highlight" style="background-color: rgb(248, 255, 168); box-shadow: none;">ontrol unit can be configured in such a way that the functional scope and/or a basic setting of the infotainment application is changed, providing a first configuration data server, which is arranged in the motor vehicle and is separate from the control unit in terms of devices and has an associated first configuration database for storing configuration data in a vehicle-related manner, providing a service center, which is arranged in a stationary manner at a distance from the motor vehicle and has a second configuration data server arranged there and a second configuration database for centrally storing configuration data, providing a wireless communication connection between the first configuration data server and the second configuration data server, providing a first modification interface, by means of which configuration data of the second configuration database having </span>first access rights can be modified, providing a second Internet-based modification interface, by means of which configuration data of the second configuration database having second access rights that differ from the first access rights can be modified, recognizing modifications of the configuration data of the second configuration database, changing configuration data of the first configuration database by means of the wireless communication connection based on configuration data from the second configuration database, changing the functional scope and/or a basic setting of the infotainment application by means of the configuration interface of the control unit based on configuration data from the first configuration database.</span></div></div>`;

export const claims = `<div _ngcontent-ipo-c290="" id="claims-text" ng-reflect-text-html="<ul class=&quot;claims&quot;><li class=&quot;" ng-reflect-smart-highlight-items="" ng-reflect-figma-class="true" ng-reflect-ng-class="[object Object]" class="claims-tree figma-claims-tree"><div><ul class="claims"><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0001"><div class="claim-no-toggler"></div><div class="claim-wrap"><p>A method for configuring infotai<span data-id="hl-4575" class="highlight" style="background-color: rgb(248, 255, 168); box-shadow: none;">nment applications in a motor vehic</span>le (10), comprising the steps:<br></p><p>• providing a motor vehicle (10) with at l<span data-id="cm-2001" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);">east one infotainment application and with a control u</span>nit, which is involved in the at least one infotainment application,</p><p>• providing a configuration interface (11, 12, 1<span data-id="cm-2101" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);">3) of the control unit, by mean</span>s of which the control unit can be configured in suc<span data-id="cm-1811" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);">h a way that the functional scope and/or a basic se</span>tting of the infotainment application is varied,</p><p>• providing a first configuration data server (14), which is arranged in the motor vehicle and separated in terms of equipment from the control unit, with an associated first configuration <span data-id="hl-4516" class="highlight" style="background-color: rgb(255, 210, 201); box-shadow: none;">database for the storage of configuration data relating to the vehicle,</span></p><p>• providing a service centre (<span data-id="cm-2072" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);">20) arranged stationarily remote from the motor vehicle, with a second configuration data server (22) arranged there and a second configuration database for central storage of config</span>uration data,</p><p>• providing a wireless communication connection (15, 21) between the first and the second configuration data server,</p><p>• providing a first modification interface (23, 32), by<span data-id="cm-1925" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(119, 122, 160);"> means of which configuration data of the second configuration database can be modified using first access rights,</span></p><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">• recognising modifications of the configuration data of the second configuration database,</span></p><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">• changing configuration data of the first configuration database by means of the wireless communication connection with the aid of configuration data from the second configuration database,</span></p><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">• changing the functional scope and/or a basic setting of the infotainment application by a configuration of the control unit by means of the configuration interface of the control unit with the aid of configuration data from the first configuration database,</span></p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);"> characterised in that the service centre is operated by a vehicle producer or OEM, in that the first modification interface is used for modifications of the vehicle producer or OEM and is not internet-based, in that a second internet-based modification interface (24, 25, 41, 42, 51, 52, 61, 62) is provided, by means of which configuration data of the second configuration database can be modified using second access rights, which differ from the first access rights.</span></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0002"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to claim 1, characterised in that a basic setting of the infotainment application relating to the management of personal information management data is changed.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0003"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to either of claims 1 or 2, characterised in that the changing of the functional scope and/or the basic setting of the infotainment application cannot be carried out by operating means available in the motor vehicle.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0004"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 3, characterised by the further step:</span><br></p><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">• providing a third internet-based modification interface, by means of which configuration data of the second configuration database can be modified using third access rights, which differ from the first and second access rights.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0005"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 4, characterised in that the changing of configuration data of the first configuration database takes place in response to the recognition of modifications of the configuration data of the second configuration database.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0006"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 5, characterised in that the changing of the functional scope and/or a basic setting of the infotainment application takes place by means of the configuration interface of the control unit in response to a change of configuration data of the first configuration database.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0007"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 6, characterised in that during the change of configuration data of the first configuration database, which follows the recognition of modifications to the configuration data of the second configuration database, only selected configuration data of the first configuration database are changed.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0008"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 7, characterised in that the infotainment application is an email client and in that the changing of the functional scope and/or the basic setting of the infotainment application comprises the configuration of the email client.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0009"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to any one of claims 1 to 8, characterised in that by means of the wireless communication connection, certificate signing requests or other data relevant to configuration and authentication are<span data-id="cm-1841" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);"> stored by the fir</span>st configuration data server at the second configuration data server.</span></p></div></div></li><li class="claim-root claim-node claim-no-children"><div class="claim" id="c-en-01-0010"><div class="claim-no-toggler"></div><div class="claim-wrap"><p><span data-id="cm-1925" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(119, 122, 160);">A method according to claim 9, characterised in that a certificate signing request is stored by the control unit at the configuration interface assigned to it and/or at the configuration data server.</span></p></div></div></li></ul></div></div>`;

export const description = `<div _ngcontent-ipo-c291="" id="description-text" sectioncollapseclass="button-main-tertiary-grey button-square button-small" class="figma-innerHTML ng-tns-c291-0 ng-star-inserted" ng-reflect-section-collapse-class="button-main-tertiary-grey butt" ng-reflect-text-html="<article><section id=&quot;cross-re" ng-reflect-figma-class="true" ng-reflect-display-heading-sections="true" ng-reflect-display-section-anchors="true"><div><article><section id="section-74i9mrugz0" data-origin-id="cross-reference-to-related-applications"><h1 id="section-heading-crossreferencetorelatedapplications" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-cgu76u02zh"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-g5ez564tyk"><div>Cross Reference to Related Applications</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-crossreferencetorelatedapplications"></i></h1><div class="figma-section-content"><h2 id="section-heading-crossreferencetorelatedapplications" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-51jpst9009"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-zrs231dfiq"><div>CROSS-REFERENCE TO RELATED APPLICATIONS</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-crossreferencetorelatedapplications"></i></h2><div id="section-content-tmp-29mwqeelzp" class="figma-section-content"><div class="desc-paragraph" id="oct-p-1">This application is<span data-id="cm-2076" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);"> a continuation of PCT International Application No. PCT/EP2010/004638, filed Jul. 29, 2010, which claims priority under 35 U.S.C. §119 from Ger</span>man Patent Application No. DE 10 2009 038 035.3, filed Aug. 19, 2009, the entire disclosures of which are herein expressly incorporated by reference.</div></div></div></section><section id="section-9qmm99487j" data-origin-id="summary-of-invention"><h1 id="section-heading-summary" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-nw7h2rjb7c"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-x35g5mlg3q"><div>Summary</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-summary"></i></h1><div class="figma-section-content"><h2 id="section-heading-backgroundandsummaryoftheinvention" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-6sivl3fduv"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-e62jque7a2"><div>BACKGROUND AND SUMMARY OF THE INVENTION<span data-id="cm-1844" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(119, 122, 160);"></span></div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-backgroundandsummaryoftheinvention"></i></h2><div id="section-content-tmp-zoctnj4iwi" class="figma-section-content"><div class="desc-paragraph" id="oct-p-2"><span data-id="cm-1844" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">The invention relates to a method of configuring infotainment applications in a motor vehicle.</span></div></div><div id="section-content-tmp-v2os4eko33" class="figma-section-content"><div class="desc-paragraph" id="oct-p-3"><span data-id="cm-1844" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">From German Patent document DE 100 44 917 A1, a method is known for utilizing functions and performance characteristics in a motor vehicle, wherein the vehicle is constructed with respect to the hardware for utilizing all functions with maximal performance characteristics, wherein, with respect to the software, functions can be blocked and/or performance characteristics can be limited, whereby a user of a motor vehicle can temporarily or durably request functions or performance characteristics which will then be temporarily or durably cleared with respect to the software.</span></div></div><div id="section-content-tmp-it9fslyyan" class="figma-section-content"><div class="desc-paragraph" id="oct-p-4"><span data-id="cm-1844" class="pa-comment-highlight pa-text-middle-highlight" style="border-bottom-color: rgb(119, 122, 160);">From WO 02/17184 A1, a “Total Fleet Logistics” system is known. This system permits the implementation of a vehicle diagnosis, a vehicle monitoring, a vehicle configuration and a vehicle programming, for example, by a fleet operator via the Internet.</span></div></div><div id="section-content-tmp-upsiora4y0" class="figma-section-content"><div class="desc-paragraph" id="oct-p-5"><span data-id="cm-1844" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(119, 122, 160);">However, no satisfactory solutions for the configuration of infotainment applications in motor vehicles of a multi-vehicle fleet arrangement are known from the state of the art. As a rule, the methods known for this purpose are limited to written instructions of the fleet operator to the users of the individual fleet vehicles</span>.</div></div><div id="section-content-tmp-457dpn9zry" class="figma-section-content"><div class="desc-paragraph" id="oct-p-6">It is an object of the invention to provide an improved method for the configuration of infotainment applications in the motor vehicles of a vehicle fleet.</div></div><div id="section-content-tmp-9jjgqnxzpn" class="figma-section-content"><div class="desc-paragraph" id="oct-p-7">This and other objects are achieved by a method of co<span data-id="hl-4515" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;">nfiguring infotainment ap</span>plications in a motor vehicle, comprising the steps of (a) providing a motor vehicle having at least one infotainment application and having a control unit which participates in the at least one infotainment application, (b) providing a configuration interface of the control unit, by way of which the control unit can be configured such that the functional scope and/or a basic setting of the infotainment application is changed, (c) providing a first configuration data server, which is arranged at the motor vehicle, is separate from the control unit with respect to equipment and has a pertaining first configuration data bank for the vehicle-related storage of configuration data, (d) providing a service center<span data-id="cm-2231" class="pa-comment-highlight" style="border-bottom-color: rgb(251, 111, 9);"> stationarily arranged at a distance from</span> the motor vehicle and having a second configuration data server arranged there and a second configuration data bank for the central storage of configuration data, (e) providing a wireless communication connection between the first and the second configuration data server, (f) providing a first modification interface by way of which configuration data of the second configuration data bank can be modified by means of first access rights, (g) providing a second Internet-based modification interface by way of which configuration data of the second configuration data bank can be modified by means of second access rights which differ from the first access rights, (h) recognizing modifications of the configuration data of the second configuration<span data-id="hl-4624" class="highlight" style="background-color: rgb(248, 255, 168); box-shadow: none;"> data bank, (i) changing of conf</span>iguration data of the first configuration data bank by way of the wireless communication connection by use of configuration data from the second configuration data bank, and (j) changing the functional scope and/or a basic setting of<span data-id="hl-4870" class="highlight pa-text-start-highlight" style="background-color: rgb(255, 215, 234); box-shadow: none;"> the infotainment application by way of the configuration interface of the control unit by use of configuration data from the first configuration data bank.</span></div></div><div id="section-content-tmp-g7oz5lb620" class="figma-section-content"><div class="desc-paragraph" id="oct-p-8"><span data-id="hl-4870" class="highlight pa-text-end-highlight" style="background-color: rgb(255, 215, 234); box-shadow: none;">On the part of the motor vehicle, the basis is a motor vehicle having at least one infotainment application and having a control unit which participates in the at least one infotainment application. In connection with a motor vehicle, the term “infotainment” should be understood as covering the areas of information, communication and entertainment. An infotainment application of a motor vehicle therefore represents applications in connection</span> with navigation, telephone, telematics, radio, audio, video, email, Internet, as well as future further developments, supplements to and/or substitutions for such applications.</div></div><div id="section-content-tmp-s6rf2w9gxe" class="figma-section-content"><div class="desc-paragraph" id="oct-p-9">The control unit participating in the at least one infotainment application has a configuration interface by way of which it can be configured such that the functional scope and/or a basic setting of the infotainment application is changed. A change of the functional scope of the infotainment application may consist, for example, of the clearing of a navigation function, of the clearing of an Intranet service or of the clearing of a TV function also when the motor vehicle is being driven. A change of a basic setting of the infotainment application may consist, for example, of defining a home address for a navigation system, of parameterizing an email account or of configuring an intranet access.</div></div><div id="section-content-tmp-8ofy8vvded" class="figma-section-content"><div class="desc-paragraph" id="oct-p-10">Furthermore, on the part of the motor vehicle, a first configuration data server which, relative to the equipment, is separate from the control unit, is provided which has a pertaining first configuration data bank for the vehicle-related storage of configuration data.</div></div><div id="section-content-tmp-zkwtm55xlb" class="figma-section-content"><div class="desc-paragraph" id="oct-p-11">At a distance from the motor vehicle, a second configuration data server is provided in a stationary manner at a service center and has a pertaining second configuration data bank for the central storage of configuration data. A wireless communication connection exists between the first and the second configuration data server.</div></div><div id="section-content-tmp-xt5chndlot" class="figma-section-content"><div class="desc-paragraph" id="oct-p-12">The service center can be operated, for example, by a vehicle manufacturer or OEM (original equipment manufacturer) and/or may be spatially arranged at this vehicle manufacturer or OEM.</div></div><div id="section-content-tmp-5gwjdommch" class="figma-section-content"><div class="desc-paragraph" id="oct-p-13">The configuration data of the second configuration databank can be configured by way of at least two different modification interfaces. <span data-id="hl-4625" class="highlight" style="background-color: rgb(255, 215, 234); box-shadow: none;">A signific</span>ant difference be<span data-id="hl-4631" class="highlight" style="background-color: rgb(255, 215, 234); box-shadow: none;">tween the</span> at least two modification interfaces consists of the access rights which are granted to a party accessing the second configuration data bank by way of the respective modification interface during the access.<span data-id="hl-4626" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;"> By way of a f</span><span data-id="hl-4627" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;">irst</span><span data-id="hl-4630" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;"> mo</span>dification i<span data-id="hl-4632" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;">nterf<span data-id="hl-4629" class="highlight pa-text-start-highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;"></span></span><span data-id="hl-4629" class="highlight pa-text-end-highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;">ace, the con</span>figuration data of the second configuration data bank can be modified by using first access rig<span data-id="hl-4628" class="highlight" style="background-color: rgb(213, 246, 198); box-shadow: none;">hts; b</span>y way of a second modification interface, the configuration data of the second configuration data bank can be modified by using second access rights which differ from the first access rights.<span data-id="cm-2324" class="pa-comment-highlight pa-text-start-highlight" style="border-bottom-color: rgb(255, 0, 0);"></span></div></div><div id="section-content-tmp-hvl2zzbie7" class="figma-section-content"><div class="desc-paragraph" id="oct-p-14"><span data-id="cm-2324" class="pa-comment-highlight pa-text-end-highlight" style="border-bottom-color: rgb(255, 0, 0);">The first modification interface is preferably implemented in a manner not based on the Internet. It is preferably implemented in a hard-wired manner, and preferably access can only take place to the second configuration data bank by a very specific arithmetic and logic unit and/or by a limited number of arithmetic and logic units, for example, by all arithmetic and logic units in a local hard-wired network, by way of the first modification interface. As an alternative or in addition, the first modification interface is preferably only accessible if corresponding first access data are available and utilized. The first modification interface is therefore particularly suitable for modifications of the second configuration data bank by a vehicle manufacturer or OEM.</span></div></div><div id="section-content-tmp-9fh2xc8v4t" class="figma-section-content"><div class="desc-paragraph" id="oct-p-15">For reasons of completeness, it should be noted that the first modification interface, in principle, may also be implemented on an Internet-based manner—even if it is mainly provided for modifications of the second configuration data bank by a vehicle manufacturer or OEM and if the service center is spatially arranged at the vehicle manufacturer's or OEM.</div></div><div id="section-content-tmp-yskeo3uyev" class="figma-section-content"><div class="desc-paragraph" id="oct-p-16">The second modification interface is implemented based on the Internet. It is preferably implemented on the basis of web technologies and/or client/server technologies known per se. According to a preferred embodiment of the invention, when corresponding second access data are available, a conventional PC connected with the Internet can access the second modification interface. The second modification interface is particularly suitable for modifications of the second configuration data bank by a fleet operator.</div></div><div id="section-content-tmp-u4vy2e1e22" class="figma-section-content"><div class="desc-paragraph" id="oct-p-17">The first access rights and the second access rights can be defined in a disjunct fashion, i.e. such that a party accessing by way of the first modification interface can have access that differs completely from that of a party accessing by way of the second modification interface. However, there may also be a certain amount of intersecting or a certain overlapping of the access rights.</div></div><div id="section-content-tmp-92za6g3xff" class="figma-section-content"><div class="desc-paragraph" id="oct-p-18">According to a preferred embodiment of the invention, in addition, a third (also Internet-based) modification interface of the second configuration data bank is provided by way of which configuration data of the second configuration data bank can be modified by means of access rights which again differ from the first and second access rights. The third modification interface is therefore suitable for modifications of the second configuration data bank by the user of fleet vehicles and/or service providers and/or application providers. However, such a modification interface may also be reserved for one of the above-mentioned user groups, while the other user groups are provided with their own Internet-based modification interfaces with their own access rights. The third modification interface and possibly additional modification interfaces are also preferably secured by corresponding access data.</div></div><div id="section-content-tmp-vjc6ggaj7h" class="figma-section-content"><div class="desc-paragraph" id="oct-p-19">Possible modifications of the configuration data of the second configuration data bank are recognized either on the part of the service center and/or on the part of the motor vehicle communicating with the service center. It is, for example, possible to implement a recognition of modifications of the configuration data of the second configuration data bank such that, after an access by way of one of the modification interfaces, the configuration data are considered to be changed. However, it may, for example, also be checked whether actually changing accesses have been carried out and/or the data status can be compared with an earlier data status with respect to changes.</div></div><div id="section-content-tmp-7999fybsjz" class="figma-section-content"><div class="desc-paragraph" id="oct-p-20">Regularly (for example, cyclically), at defined points-in-time and/or upon the recognition of a change of the data status of the configuration data of the second configuration data bank (i.e. to an extent “in response” to the recognition), configuration data of the first configuration bank are changed by way of the wireless communication connection between the first and the second configuration data server. This takes place by use of configuration data from the second configuration data bank, preferably only by use of previously changed configuration data from the second configuration bank. Correspondingly, preferably not all, but only selected, configuration data of the first configuration data bank are changed, particularly those configuration data of the first configuration data bank which have a content-related connection with previously changed configuration data of the second configuration data bank.</div></div><div id="section-content-tmp-vttcbsvnle" class="figma-section-content"><div class="desc-paragraph" id="oct-p-21">By means of the thus changed configuration data from the first configuration data bank, by way of the configuration interface of the control unit, finally the functional scope and/or a basic setting of the infotainment application is changed. This can also take place either regularly (for example, cyclically) at defined points-in-time and/or upon the recognition of a change of the data status of the configuration data of the first configuration data bank (i.e. to an extent, “in response” to the recognition).</div></div><div id="section-content-tmp-l486y3bm9k" class="figma-section-content"><div class="desc-paragraph" id="oct-p-22">Preferably, a basic setting of the infotainment application is changed, and the basic setting is a basic setting relating to the administration of personal information management data. Correspondingly, the infotainment application preferably is an application which,—at least among other things—administers personal information management data, such as an email client.</div></div><div id="section-content-tmp-pcxdb0q251" class="figma-section-content"><div class="desc-paragraph" id="oct-p-23">The term “personal information management data”, which, in the meantime, has become established in technical circles, in this case, relates to electronically existing personal data, such as contacts, schedules, notes, documents, such as letters, faxes, SMS's, emails, RSS feeds as well as current correspondence, future further developments, supplements to and/or substitutions for such data.</div></div><div id="section-content-tmp-rjnylg6hga" class="figma-section-content"><div class="desc-paragraph" id="oct-p-24">In the described manner, such changes of the functional scope and/or basic setting of the infotainment application are preferably carried out which cannot be made by operating devices available in the motor vehicle, particularly not the “conventional operating devices” (push buttons, touchpad, keyboard, pressure/rotary controls, etc.) of the motor vehicle.</div></div><div id="section-content-tmp-0kpcw2r033" class="figma-section-content"><div class="desc-paragraph" id="oct-p-25">If required, implemented definitions of the functional scope and/or of the basic setting can, however, be indicated by means of operating devices available in the motor vehicle. If, for example, an email client is configured in the above-described manner, the settings (email server, account name, . . . ) for a vehicle occupant can be represented on a display unit but cannot be changed.</div></div><div id="section-content-tmp-70z8cjrrw1" class="figma-section-content"><div class="desc-paragraph" id="oct-p-26">According to a preferred embodiment of the invention, by way of the wireless communication connection between the motor vehicle and the service center, data, particularly so-called certificate signing requests or other configuration-relevant and authentication-relevant data can be stored or are stored at the second configuration data service by the first configuration data server.</div></div><div id="section-content-tmp-efwr6fcjta" class="figma-section-content"><div class="desc-paragraph" id="oct-p-27">Such certificate signing requests with respect to a locally generated secret code are preferably previously stored by a control unit at “its” configuration interface, i.e. at the configuration interface assigned to it, and or at the configuration data server.</div></div><div id="section-content-tmp-45kbjoix87" class="figma-section-content"><div class="desc-paragraph" id="oct-p-28">In certain cases, the secret access code is generated in the vehicle and never leaves the vehicle. The vehicle generates a “certificate signing request” which contains the pertaining public code. This certificate signing request has to be signed by the “certificate authority” relevant with respect to a certain access authorization and has to be transmitted as a certificate back into the vehicle. By means of this certificate and the secret code, the vehicle application then obtains access to, for example, the business data.</div></div><div id="section-content-tmp-8xomdzbvjd" class="figma-section-content"><div class="desc-paragraph" id="oct-p-29">Other objects, advantages and novel features of the present invention will become apparent from the following detailed description of one or more preferred embodiments when considered in conjunction with the accompanying drawing.</div></div></div></section><section id="section-cfv4sq1sjz" data-origin-id="brief-description-of-drawings"><section id="section-a2n17rqjra" data-origin-id="description-of-drawings"><h1 id="section-heading-descriptionofdrawings" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-cgnvkcl3t1"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-e84r0xboo6"><div>Description of Drawings</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-descriptionofdrawings"></i></h1><div class="figma-section-content"><h2 id="section-heading-briefdescriptionofthedrawing" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-9p83vc8dwh"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-ja66rbdu67"><div>BRIEF DESCRIPTION OF THE DRAWING</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-briefdescriptionofthedrawing"></i></h2><div id="section-content-tmp-miw8pdnwdk" class="figma-section-content"><div class="desc-paragraph" id="oct-p-30"><br>FIG. 1 is a view of an example of the architecture of a system for implementing the method according to the invention.</div></div></div></section></section><section id="section-w0vel1yoyd" data-origin-id="detailed-description"><h1 id="section-heading-detaileddescription" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-l0b5plhdsp"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-3ded2jxwr1"><div>Detailed Description</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-detaileddescription"></i></h1><div class="figma-section-content"><h2 id="section-heading-detaileddescriptionofthedrawing" class="d-flex justify-content-between align-items-center"><i class="fa-solid fa-caret-down cursor-pointer section-collapse   button-main-tertiary-grey button-square button-small" id="section-collapse-pc3cke9tw4"></i><div class=" heading-title d-flex justify-content-start align-items-center" id="section-title-9r0agko6vn"><div>DETAILED DESCRIPTION OF THE DRAWING</div></div><i class="fa fa-link cursor-pointer me-3 d-none heading-actions" title="Click to copy to clipboard" id="section-anchor-detaileddescriptionofthedrawing"></i></h2><div id="section-content-tmp-g3xx9eizc2" class="figma-section-content"><div class="desc-paragraph" id="oct-p-31">According to the state of the art, functions in the field of information, communication and entertainment (overall term: infotainment) are generally unlimited and can be configured exclusively by the respective end user. One of a few exceptions is a blocking of certain functions by the vehicle manufacturer, for example, in that the end user first has to enter a clearing code before he can use the respective function.</div></div><div id="section-content-tmp-s4ly9m6ese" class="figma-section-content"><div class="desc-paragraph" id="oct-p-32">A configuration of infotainment applications is known, for example, in that the end user is provided with the possibility of configuring an email client or an access to an email server by way of operating devices in the vehicle or from a distance by way of an online portal.</div></div><div id="section-content-tmp-c6rsuu0i7d" class="figma-section-content"><div class="desc-paragraph" id="oct-p-33">A satisfactory central management for the configuration of infotainment systems, for example, by a party responsible for a motor vehicle fleet, is not possible by way of the known devices and methods. Also, so far, functions of the infotainment systems in the vehicle can, as a rule, not be limited by third parties. Currently, relevant configuration parameters, as a rule, can be changed only either by way of a coding, clearing or provisioning of the vehicle. Currently, such processes can, as a rule, be used only by the vehicle manufacturer and, in addition, with respect to the vehicle configuration, are extremely dependent on the respective market or country.</div></div><div id="section-content-tmp-bnit29v2zd" class="figma-section-content"><div class="desc-paragraph" id="oct-p-34">The state of the art or the above-mentioned methods and devices lead to the following disadvantageous aspects.</div></div><div id="section-content-tmp-xcpxelqzp8" class="figma-section-content"><div class="desc-paragraph" id="oct-p-35">A first aspect <b>1</b> concerns the configuration by the customer or end user. The end user is confronted by the task of correctly configuring the infotainment systems. This results in the fact that a “correct” or uniform configuration with respect to all vehicles of a vehicle fleet can hardly, or not at all, be ensured. It is an additional fact that, if necessary, access to company resources, for example, in the case of company cars and motor vehicle fleets of the company, also is to take place by way of the vehicle. In this case, the vehicle or a backend, as required, operated by the vehicle manufacturer basically acts as an independent device and is therefore not part of the IT infrastructure of the customer-side enterprise (using the vehicle fleet). In order to ensure a functioning configuration in all vehicles or in as many vehicles of the vehicle fleet as possible, the IT management of the customer-side enterprise therefore has to direct all end users (for example, employees of the customer-side enterprise) into the correct configuration. In this case, if necessary, also secrets (such as user identifications, passwords, secret codes and certificates) have to be exchanged, which could also be misused.</div></div><div id="section-content-tmp-oasssezzjc" class="figma-section-content"><div class="desc-paragraph" id="oct-p-36">According to the state of the art, the customer-side enterprise therefore loses control over such secrets. Furthermore, the customer-side enterprise has no control over the configuration of the used IT systems in the vehicle and in the backend. With the increasing spread and expansion of so-called personal information management (PIM) functionalities in the infotainment systems of modern motor vehicles, person-related data are increasingly processed which, as a rule, are confidential. If these PIM data relate to the enterprise, for example, business email or schedules, a processing in devices outside the enterprise is, as a rule, not permitted. As a result, the paradox situation may arise that, although the vehicle infotainment systems provide technically perfect PIM functions, the end user is finally not allowed to use them because of the lack of control by the customer-side enterprise.</div></div><div id="section-content-tmp-4no7ry8ld8" class="figma-section-content"><div class="desc-paragraph" id="oct-p-37">A second aspect <b>2</b> concerns the possibility of limiting functions (which is often absent according to the state of the art). With respect to information-related systems in enterprises (and outside of motor vehicles), it is currently customary for the IT management of the enterprise to set preconditions with respect to the use and configuration of these devices, and to also technically enforce these preconditions in the form of security guidelines and policies.</div></div><div id="section-content-tmp-xadz69a036" class="figma-section-content"><div class="desc-paragraph" id="oct-p-38">So far, there have not been any satisfactory possibilities of finding a corresponding solution in the case of vehicle infotainment systems. Thus, a customer-side enterprise or vehicle lessor can set no limitations and preconditions with respect to the configuration and the use of information, communication and entertainment functions of the vehicle.</div></div><div id="section-content-tmp-zgskxacrdp" class="figma-section-content"><div class="desc-paragraph" id="oct-p-39">It is true that in the meantime customer-side enterprises frequently have set preconditions for fleet vehicles and demanded restrictions in the functions, for example, as to which functions may be operated during the drive. However, currently, the vehicle manufacturer can react in this respect only at relatively high expenditures, specifically in that the latter either establishes a restriction individually for individual or all vehicles which are supplied to the respective customer-side enterprise, or a corresponding limitation is even incurred for all customers. In contrast, it would be significantly more advantageous if, instead, a party responsible for the vehicle fleet or the IT management of the customer-side enterprise itself could configure limitations in the vehicle.</div></div><div id="section-content-tmp-sxf7246c54" class="figma-section-content"><div class="desc-paragraph" id="oct-p-40">Even business models could be linked to a corresponding configurability. Currently, car rental firms cannot separately account for additional functions, such as the use of a navigation system. The customer is therefore either offered a vehicle with or without a navigation system. On the basis of the introduced method, a navigation application could be cleared by the car rental firm on demand.</div></div><div id="section-content-tmp-2919ydp2jk" class="figma-section-content"><div class="desc-paragraph" id="oct-p-41">A third aspect <b>3</b> concerns a policy-based software sale of or a software leasing of vehicle functions. Because of the very limited possibilities according to the state of the art of activating or deactivating software-based vehicle functions, it is currently, as a rule, not possible to make a certain function or application available to an end user for only a limited time. On the basis of the introduced method, it would be possible to rent (from the customer's viewpoint) or clear (from the viewpoint of the customer-side enterprise and/or from the vehicle manufacturer's viewpoint) a navigation application, for example, for the period of a vacation trip or business trip.</div></div><div id="section-content-tmp-zazjbt7ti4" class="figma-section-content"><div class="desc-paragraph" id="oct-p-42">The introduced method permits a central configuration of the information, communication and entertainment systems of the motor vehicle that can be carried out from a distance, for example, by the IT management of an enterprise, by a service provider, by an operator of a vehicle fleet, by a car rental agency or by a vehicle manufacturer or their dealer network. The end user of the vehicle will not be burdened with this task.</div></div><div id="section-content-tmp-qizro43abl" class="figma-section-content"><div class="desc-paragraph" id="oct-p-43">The introduced method further permits a limitation of the functionality or configuration settings in the information, communication and entertainment systems in the vehicle by the use of guidelines and policies by an authorized entity, for example, the IT management of an enterprise, by a service provider, by a fleet operator, by a rental car agency or by a vehicle manufacturer. A car rental agency and/or a vehicle manufacturer can therefore provide certain functions for a surcharge or otherwise limit them.</div></div><div id="section-content-tmp-tqxcmujcra" class="figma-section-content"><div class="desc-paragraph" id="oct-p-44">On the basis of the introduced method, customer-side enterprises are permitted to treat information, communication and entertainment systems of the motor vehicle as part of the IT infrastructure of the customer-side enterprise. The access to confidential firm resources, such as the email server, the intranet, the appointment schedule, the VPN, etc. can thereby be configured and limited by the enterprise itself.</div></div><div id="section-content-tmp-bql32aibzr" class="figma-section-content"><div class="desc-paragraph" id="oct-p-45">A preferred technical system or architecture for implementing the invention is schematically constructed as follows in FIG. 1.</div></div><div id="section-content-tmp-kc2ukhhz9b" class="figma-section-content"><div class="desc-paragraph" id="oct-p-46">In the on-board power supply system of the motor vehicle <b>10</b>, at least one electronic control unit, if necessary, also several electronic control units (in FIG. 1, precisely three control units), is equipped with a “configuration and policy client” <b>11</b>, <b>12</b>, <b>13</b>, by way of which the functions provided by the respective control unit are configured or limited. Thus, for example, the navigation can be set such that a destination input is possible only when the vehicle is standing still or the email application can be configured correctly.</div></div><div id="section-content-tmp-fx2gbx0v5o" class="figma-section-content"><div class="desc-paragraph" id="oct-p-47">The “configuration and policy client” receives the configuration data and guideline policies from an “in-vehicle configuration and policy server and database” <b>14</b>, which is central in the vehicle, is implemented in an electronic control unit in the vehicle, and in which all configurations and guidelines/policies are centrally provided.</div></div><div id="section-content-tmp-1t7q6lhydp" class="figma-section-content"><div class="desc-paragraph" id="oct-p-48">According to an optional but preferred definition of the introduced method, one or more control units in the on-board supply system of the motor vehicle <b>10</b> can store a “certificate signing request” for a locally generated secret code in the “configuration and policy client” <b>11</b>, <b>12</b>, <b>13</b>. The following use case can therefore be implemented on the basis of the infrastructure illustrated in FIG. 1. The secret code is generated in the vehicle. For this purpose, a certificate signing request is generated which has to be transmitted to a certificate authority (CA) situated outside the vehicle—this may especially be the vehicle manufacturer's CA—, has to be signed there and subsequently has to be transmitted as a signed certificate back into the vehicle.</div></div><div id="section-content-tmp-03g05muu00" class="figma-section-content"><div class="desc-paragraph" id="oct-p-49">The “configuration and policy client” <b>11</b>, <b>12</b>, <b>13</b> regularly updates the relevant configuration and policy data from this “in-vehicle configuration and policy server and database” <b>14</b>. Conversely, when updating the data in the “in-vehicle configuration and policy server and database” <b>14</b>, a notification of the clients <b>11</b>, <b>12</b>, <b>13</b> concerning the change can also take place. This can be called a “configuration and policy push”.</div></div><div id="section-content-tmp-ty0cac6fiz" class="figma-section-content"><div class="desc-paragraph" id="oct-p-50">According to a further optional but preferred definition of the introduced method, the “configuration and policy client” <b>11</b>, <b>12</b>, <b>13</b> can store information in the “in-vehicle configuration and policy server and database” <b>14</b>, for example, “certificate signing requests” or other configuration, and authentication, relevant data.</div></div><div id="section-content-tmp-vtn3jbjjsc" class="figma-section-content"><div class="desc-paragraph" id="oct-p-51">By way of current cryptographic methods, it is optionally ensured that the configuration and guideline data are confidential, authentic and/or free of manipulation.</div></div><div id="section-content-tmp-qrx3145iw0" class="figma-section-content"><div class="desc-paragraph" id="oct-p-52">By way of a communications device (for example, a GSM-, GPRS-, UMTS-, Wimax-, WLAN- or LTE-module) <b>15</b> built into the vehicle or by way of a consumer electronics (CE) device (or portable communications device) <b>17</b> introduced by the end user (and connected by way of a corresponding interface “CE device interface” <b>16</b>), the “in-vehicle configuration and policy server and database” <b>14</b> communicates with the backend—the “configuration and policy backend” <b>20</b>. The core component of the backend <b>20</b> is the “backend configuration and policy server and database” <b>22</b>, in which all relevant configuration and guideline data are stored. The communication takes place by way of a wireless communication connection between the communications device <b>15</b> or the CE device <b>17</b> and a communications device <b>21</b> connected with the “backend configuration and policy server and database” <b>22</b>.</div></div><div id="section-content-tmp-euwzhavaun" class="figma-section-content"><div class="desc-paragraph" id="oct-p-53">In contrast to the vehicle system <b>10</b>, the backend <b>20</b> is always available and can service several vehicles. The configuration and policy data are centrally situated in the “backend configuration and policy server and database” <b>22</b> and are retrieved in regular cycles by the in-vehicle configuration and policy server and database” <b>14</b> in that changes are transmitted into the vehicle <b>10</b>.</div></div><div id="section-content-tmp-ysawjnan1l" class="figma-section-content"><div class="desc-paragraph" id="oct-p-54">According to a further optional but preferred definition of the introduced method, the in-vehicle configuration and policy server and database” <b>14</b> can update or add data possibly (see above) stored there, such as “certificate signing requests” or other configuration, and authentication, relevant data, in the “backend configuration and policy server and database” <b>22</b>.</div></div><div id="section-content-tmp-0ysbrpg43d" class="figma-section-content"><div class="desc-paragraph" id="oct-p-55">By way of current cryptographic methods, it is optionally ensured that the configuration and guideline data are confidential, authentic and/or free of manipulation.</div></div><div id="section-content-tmp-rovctezf1a" class="figma-section-content"><div class="desc-paragraph" id="oct-p-56">The data stored in the “backend configuration and policy server and database” <b>22</b> may include, among others:</div></div><div id="section-content-tmp-pghoh841jd" class="figma-section-content"><div class="desc-paragraph" id="oct-p-57">(a) Settings, for example, configuration of email accounts, user information, such as email addresses, favorites, bookmarks, navigation destinations, video screen backgrounds, personalizations, sender lists, news channels, etc.</div></div><div id="section-content-tmp-9fpd2rspfo" class="figma-section-content"><div class="desc-paragraph" id="oct-p-58">(b) Authentication data, such as user names and passwords, secret codes, certificates (for example, for certificate-based authentication, for example, when retrieving company email), etc.</div></div><div id="section-content-tmp-p16bys40hz" class="figma-section-content"><div class="desc-paragraph" id="oct-p-59">(c) “Certificate signing requests”, which were generated by users of the on-board power supply system of the motor vehicle and have to be transmitted to a certificate authority (CA) of the vehicle manufacturer and/or of an authorized third party.</div></div><div id="section-content-tmp-vkxn0hkrar" class="figma-section-content"><div class="desc-paragraph" id="oct-p-60">(d) Guidelines and policies, such a limitations of certain functions in certain situations, limitations with respect to parameters, for example, speed or location or time or frequency of use, limitations with respect to communication partners, limitations of import/export interfaces, preconditions with respect to password quality, frequency of change of the password for access to information (for example, company emails), preconditions with respect to the auto-erasing of data memories in the vehicle after an incorrect password input, etc., preconditions with respect to a linkage to a certain vehicle code, etc.</div></div><div id="section-content-tmp-tub0im74tm" class="figma-section-content"><div class="desc-paragraph" id="oct-p-61">(e) Authorizations, such as clearing of vehicle codes for a vehicle, or replacing the function of the vehicle code by other access codes, such as user name and password.</div></div><div id="section-content-tmp-8morcki695" class="figma-section-content"><div class="desc-paragraph" id="oct-p-62">(f) Commands to be executed by the vehicle for the protection of the IT infrastructure of the enterprise when misuse is to be assumed, for example, after a vehicle was stolen, such as the erasing of the data stored in the vehicle, for example, emails, telephone books, etc. and access authorizations/secrets.</div></div><div id="section-content-tmp-tpkofe2xq1" class="figma-section-content"><div class="desc-paragraph" id="oct-p-63">The data stored in the “backend configuration and policy server and database” <b>22</b> can be processed by the vehicle manufacturer <b>30</b> and/or by an authorized third party <b>40</b>, <b>50</b>, <b>60</b> by way of a “policy configuration user interface” <b>23</b>, <b>24</b>.</div></div><div id="section-content-tmp-p4r2pjrjwg" class="figma-section-content"><div class="desc-paragraph" id="oct-p-64">For access by the vehicle manufacturer <b>30</b>, a separate first “policy configuration user interface” <b>23</b> can be provided for policies which can be configured especially by the vehicle manufacturer (“vendor configurable policies”). In the present case, the interface <b>23</b> is not implemented based on the Internet. In particular, access can be based on web or client/server technologies. For this purpose, a “policy configuration user interface client” <b>32</b> is implement on the part of the vehicle manufacturer.</div></div><div id="section-content-tmp-0oqwtx546i" class="figma-section-content"><div class="desc-paragraph" id="oct-p-65">For the, preferably Internet-based, access of authorized third parties, a separate second “policy configuration user interface” <b>24</b> can be provided for policies which can be configured especially by authorized third parties <b>40</b>, <b>50</b>, <b>60</b>, particularly end customers (user configurable policies”). In this case, web or client/server technologies can be used. In the case of every authorized third party <b>40</b>, <b>50</b>, <b>60</b>, a “policy configuration user interface client” <b>42</b>, <b>52</b>, <b>62</b> is implemented for this purpose. For the data connection between the backend <b>20</b> and the authorized third party <b>40</b>, <b>50</b>, <b>60</b>, a communication device <b>25</b> is provided at the backend <b>20</b>, on the one hand, and a communication device <b>41</b>, <b>51</b>, <b>61</b> is provided at each authorized third party <b>40</b>, <b>50</b>, <b>60</b>, on the other hand. Current authentication and coding techniques can be used for the access.</div></div><div id="section-content-tmp-c4z3nz1ky7" class="figma-section-content"><div class="desc-paragraph" id="oct-p-66">In particular, an authorized third party may be a customer-side enterprise <b>40</b>, especially its fleet management or IT management, and/or a car rental agency.</div></div><div id="section-content-tmp-dr3fi7chkv" class="figma-section-content"><div class="desc-paragraph" id="oct-p-67">Authorized third parties may also be end users <b>50</b>. End users may, in turn, either themselves be vehicle owners (and are therefore not subject to any instructions or policies of a customer-side enterprise), or they are renters of a rental car or personnel of a customer-side enterprise (who are subject to instructions or policies of a customer-side enterprise).</div></div><div id="section-content-tmp-y97vg7yser" class="figma-section-content"><div class="desc-paragraph" id="oct-p-68">Furthermore, authorized third parties may be other so-called “3rd parties” <b>60</b>, for example, service providers or application providers.</div></div><div id="section-content-tmp-kj2qmo1s4u" class="figma-section-content"><div class="desc-paragraph" id="oct-p-69">Although, in the present example, the access of the different groups <b>40</b>, <b>50</b>, <b>60</b> of authorized third parties is based on the same technology and comparable hardware, preferably different access rights are granted to the different groups <b>40</b>, <b>50</b>, <b>60</b> of authorized third parties.</div></div><div id="section-content-tmp-j7dum8pea6" class="figma-section-content"><div class="desc-paragraph" id="oct-p-70">Finally, the significant advantages of the introduced method are summarized and explained using examples of application scenarios.</div></div><div id="section-content-tmp-wa5fwy8e0e" class="figma-section-content"><div class="desc-paragraph" id="oct-p-71">The method according to the invention makes it possible for an enterprise to maintain control of the information, communication and entertainment functions of the vehicle. In the case of the method according to the state of the art, the control is, as a rule, completely subject to the vehicle user. This control is a prerequisite for the fact that the vehicle can be integrated in the infrastructure of the enterprise, for example, by access to emails, schedules or the intranet. Furthermore, the inventive method makes it possible to allow a technical department to centrally carry out the “correct” configuration for a plurality of vehicles so that, in the course of a growing complexity, each end user will no longer have to deal completely with all functions and their correct configuration.</div></div><div id="section-content-tmp-ytqxsxuaa9" class="figma-section-content"><div class="desc-paragraph" id="oct-p-72">The inventive method supports, among others, the following selected application cases or application scenarios chosen as examples.</div></div><div id="section-content-tmp-c1k8ulq91k" class="figma-section-content"><div class="desc-paragraph" id="oct-p-73">A first application scenario relates to the configuration of functions by the fleet manager or the IT management of a customer-side enterprise. The introduced method makes it possible to centrally, i.e. company-wide, configure the infotainment functions in the vehicle by a fleet management or the IT management. As a result, a uniform configuration is ensured for all vehicles of the vehicle fleet. If required, the manager can also block the configuration with respect to changes.</div></div><div id="section-content-tmp-31u9rlnhu0" class="figma-section-content"><div class="desc-paragraph" id="oct-p-74">A second application scenario relates to the configuration by a service provider. The introduced method makes it possible that a third party service provider can establish a configuration which the end user can then activate in the vehicle. Thus, an email provider (for example, GOOGLE MAIL) could offer a function by way of which the vehicle is automatically configured such that the email can also be retrieved in the vehicle. The vehicle user can then simply import the correct configuration into his vehicle.</div></div><div id="section-content-tmp-ie5bq1oixs" class="figma-section-content"><div class="desc-paragraph" id="oct-p-75">A third application scenario relates to the blockage of individual functions. A customer-side enterprise or a car rental firm can block functions in the infotainment domain for its own vehicles. Thus, for example, the maximal volume of the music can be limited; the destination input during the drive can be prevented, or a navigation into a foreign country can be prevented. By way of guidelines and policies, the functions can be limited individually. The car rental agency can therefore individually clear, for example, the navigation or the television, depending on which rate the car rental customer has booked. Analogously, this possibility also exists for the vehicle manufacturer (for example, clearing of the navigation for a certain time period).</div></div><div id="section-content-tmp-udl7uc97as" class="figma-section-content"><div class="desc-paragraph" id="oct-p-76">A fourth application scenario relates to the use of security guidelines. Since the infotainment system of the vehicle can be utilized for business information, for example, email or the address book, and this information is normally subject to security guidelines of the enterprise, it may be useful to technically implement these security guidelines also in the vehicle. The introduced method makes this possible, for example, in that certain functions, such as data exports, are blocked, or in that preconditions are set for the use, for example, in the form of password protection of sufficient quality or the linkage to a certain vehicle code. As required, for example, all data stored in the vehicle can be automatically erased when a security guideline is violated. The security guidelines may, for example, also include the limitation of websites which the end user is permitted to retrieve.</div></div><div id="section-content-tmp-kq8738ua0i" class="figma-section-content"><div class="desc-paragraph" id="oct-p-77">A fifth application scenario relates to a so-called “configuration roaming”. Furthermore, the introduced method makes it possible not to link the configuration to a certain vehicle but to a certain vehicle code or a certain person who has identified himself by a user name/password, by way of a CE device or by other authentication devices. Thus, an individual configuration could, for example, always follow the end user into the vehicle he is presently using.</div></div><div id="section-content-tmp-vbr78ez3xf" class="figma-section-content"><div class="desc-paragraph" id="oct-p-78">The foregoing disclosure has been set forth merely to illustrate the invention and is not intended to be limiting. Since modifications of the disclosed embodiments incorporating the spirit and substance of the invention may occur to persons skilled in the art, the invention should be construed to include everything within the scope of the appended claims and equivalents thereof.</div></div></div></section></article></div></div>`;
