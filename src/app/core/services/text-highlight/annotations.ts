export const annotations = [{
    "comment": "test comment",
    "document_id": 22577589,
    "end_pos": 232,
    "field": "claims",
    "start_pos": 83,
    "text": "method for depositing an epitaxial layer from monocrystalline linemSemiconductor material on a substrate, d a characterized in that after manufacture",
}, {
    "comment": "l",
    "document_id": 22577589,
    "end_pos": 13085,
    "field": "description",
    "start_pos": 12671,
    "text": "into contact, so that it is ensured that the deposition of the epitaxial layerhappens on the substrate from a precisely saturated solution. The method according to the invention thus achievesthat each deposited epitaxial layer has excellent crystal quality and best planar properties.In addition, this method eliminates the need for critical control of the proportion of semiconductor materialin the original batch"
}, {
    "document_id": 22577589,
    "end_pos": 28,
    "field": "abstract",
    "start_pos": 0,
    "text": "ONE OR MORE EPITAXIAL LAYERS"
}, {
    "document_id": 22577589,
    "end_pos": 810,
    "field": "claims",
    "start_pos": 676,
    "text": "Process according to Claim 1, characterized in that the solution originally produced is unsaturated in terms of semiconductor material"
}, {
    "document_id": 22577589,
    "end_pos": 12521,
    "field": "description",
    "start_pos": 12214,
    "text": "context of the present invention, the semiconductor piece is used28 as a source for the semiconductor material, which for each solution for the exact saturation of semiconductor materialis needed, and further to keep the solution at the exact degree of saturation »until the substrate with this inComes into"
}, {
    "comment": "desc test",
    "document_id": 19556893,
    "end_pos": 729,
    "field": "description",
    "start_pos": 509,
    "text": "Conventional car CD players use a memory in order to preventdiscontinuous sound playing. However, the small capacity of the memory issuch that these interruptions can not be completely prevented. Also, since theCD player"
}, {
    "comment": "l",
    "document_id": 19556893,
    "end_pos": 12003,
    "field": "description",
    "start_pos": 11759,
    "text": "player described above, the controllerunit 400 performs control such that the controller 401 selects the address of theflash memory of the memory block 203, the error detector 202 receives dataaccording to a read signal, the titles of the audio"
}, {
    "comment": "401-301",
    "document_id": 19556893,
    "end_pos": 12068,
    "field": "description",
    "start_pos": 11848,
    "text": "401 selects the address of theflash memory of the memory block 203, the error detector 202 receives dataaccording to a read signal, the titles of the audio data or track numbers aredisplayed by the LCD interface unit 301",
}, {
    "comment": "staging pre-ped",
    "document_id": 19556893,
    "end_pos": 79,
    "field": "abstract",
    "start_pos": 36,
    "text": "prevents a compact disk (CD)player equipped"
}, {
    "document_id": 19556893,
    "end_pos": 187,
    "field": "claims",
    "start_pos": 71,
    "text": "replaying audio data;a keypad controlling drives of compact disks (CDs) and compact diskread only memories (CD-ROMs)"
}, {
    "document_id": 19556893,
    "end_pos": 135,
    "field": "description",
    "start_pos": 53,
    "text": "The present invention relates to a Moving Picture Experts Group(MPEG) audio layer-"
}, {
    "document_id": 19556893,
    "end_pos": 11914,
    "field": "description",
    "start_pos": 11905,
    "text": "block 203"
}, {
    "document_id": 19556893,
    "end_pos": 70,
    "field": "abstract",
    "start_pos": 23,
    "text": "player which prevents a compact disk (CD)player"
}, {
    "comment": "This text is about a way to change how the entertainment features work in a car. It talks about setting up the car so that you can adjust things like what apps are available and how they work. There's a special system in the car that helps with this, along with another system located somewhere else called a service center. These systems communicate wirelessly to make sure everything is set up correctly.\n\nThere are different ways to make changes - one for people who have certain permissions and another one on the internet for those with different permissions. When any changes are made, they get noticed, and then data in the first system gets updated based on these changes from the second system. Finally, these updates affect how your infotainment apps behave when you use them",
    "document_id": ********,
    "end_pos": 2019,
    "field": "abstract",
    "start_pos": 0,
    "text": "The invention relates to a method for configuring infotainment applications in a motor vehicle, comprising the following steps: providing a motor vehicle having at least one infotainment application and having a control unit, which is involved in the at least one infotainment application, providing a configuration interface of the control unit, by means of which configuration interface the control unit can be configured in such a way that the functional scope and/or a basic setting of the infotainment application is changed, providing a first configuration data server, which is arranged in the motor vehicle and is separate from the control unit in terms of devices and has an associated first configuration database for storing configuration data in a vehicle-related manner, providing a service center, which is arranged in a stationary manner at a distance from the motor vehicle and has a second configuration data server arranged there and a second configuration database for centrally storing configuration data, providing a wireless communication connection between the first configuration data server and the second configuration data server, providing a first modification interface, by means of which configuration data of the second configuration database having first access rights can be modified, providing a second Internet-based modification interface, by means of which configuration data of the second configuration database having second access rights that differ from the first access rights can be modified, recognizing modifications of the configuration data of the second configuration database, changing configuration data of the first configuration database by means of the wireless communication connection based on configuration data from the second configuration database, changing the functional scope and/or a basic setting of the infotainment application by means of the configuration interface of the control unit based on configuration data from the first configuration database.",

}, {
    "comment": "section comment [group@6=@shahab]",
    "document_id": ********,
    "end_pos": 443,
    "field": "claims",
    "start_pos": 392,
    "text": "h a way that the functional scope and/or a basic se",
}, {
    "comment": "test section&nbsp;",
    "document_id": ********,
    "end_pos": 2676,
    "field": "description",
    "start_pos": 2635,
    "text": " stationarily arranged at a distance from",
}, {
    "comment": "Imagine you have a special computer in your car that controls all the fun stuff like music, navigation, and movies. Now, this computer can be updated or changed by another big computer far away at the car company's office.\n\nThe part we're talking about is how these updates happen. There are two main ways to make changes: one way uses the Internet (like when you download an app on your phone), and the other way does not use the Internet.\n\nThis text explains that for some very important changes, they prefer not to use the Internet. Instead, they connect directly using wires—kind of like plugging in a cable between two computers. This makes it more secure because only certain special computers at the car company can access it.\n\nSo basically, if someone from the car company needs to update or fix something really important in your car's system, they'll do it through this direct connection with cables rather than over Wi-Fi or online methods.",
    "document_id": ********,
    "end_pos": 7451,
    "field": "description",
    "start_pos": 6678,
    "text": "The first modification interface is preferably implemented in a manner not based on the Internet. It is preferably implemented in a hard-wired manner, and preferably access can only take place to the second configuration data bank by a very specific arithmetic and logic unit and/or by a limited number of arithmetic and logic units, for example, by all arithmetic and logic units in a local hard-wired network, by way of the first modification interface. As an alternative or in addition, the first modification interface is preferably only accessible if corresponding first access data are available and utilized. The first modification interface is therefore particularly suitable for modifications of the second configuration data bank by a vehicle manufacturer or OEM.",
}, {
    "document_id": ********,
    "end_pos": 6386,
    "field": "description",
    "start_pos": 6374,
    "text": "ace, the con"
}, {
    "document_id": ********,
    "end_pos": 6357,
    "field": "description",
    "start_pos": 6354,
    "text": " mo"
}, {
    "document_id": ********,
    "end_pos": 6128,
    "field": "description",
    "start_pos": 6119,
    "text": "tween the"
}, {
    "document_id": ********,
    "end_pos": 6374,
    "field": "description",
    "start_pos": 6369,
    "text": "nterf"
}, {
    "document_id": ********,
    "end_pos": 4292,
    "field": "description",
    "start_pos": 3691,
    "text": " the infotainment application by way of the configuration interface of the control unit by use of configuration data from the first configuration data bank.On the part of the motor vehicle, the basis is a motor vehicle having at least one infotainment application and having a control unit which participates in the at least one infotainment application. In connection with a motor vehicle, the term “infotainment” should be understood as covering the areas of information, communication and entertainment. An infotainment application of a motor vehicle therefore represents applications in connection"
}, {
    "document_id": ********,
    "end_pos": 16336,
    "field": "description",
    "start_pos": 16226,
    "text": "The state of the art or the above-mentioned methods and devices lead to the following disadvantageous aspects."
}];

