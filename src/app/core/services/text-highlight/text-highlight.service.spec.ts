import { TestBed } from '@angular/core/testing';

import { TextHighlightService } from './text-highlight.service';
import * as doc_19556893 from './19556893';
import * as doc_22577589 from './22577589';
import * as doc_43485261 from './43485261';
import {annotations} from './annotations'

describe('TextHighlightService', () => {
  let service: TextHighlightService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(TextHighlightService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should validate abstract section annotation',()=>{
    const nodeEle = document.createElement('div');
    annotations.forEach((c, i)=> {
      if(c.document_id === 19556893){
        nodeEle.innerHTML = doc_19556893.abstract;
      } else if(c.document_id === 43485261){
        nodeEle.innerHTML = doc_43485261.abstract;
      } else {  
        nodeEle.innerHTML = doc_22577589.abstract;      
      }
      const state = service.validateText(nodeEle, c);
      const position = service.getSavedAnnotationPosition(nodeEle, c);
      if(c.field === 'abstract'){
        expect(state).toBeTruthy();
        expect(position.startPos).toBeGreaterThanOrEqual(0);
        expect(position.endPos).toBeGreaterThan(0);
        expect(position.startNode).toBeTruthy();
        expect(position.endNode).toBeTruthy();
      } else {
        expect(state).toBeFalsy();
      }
      expect(position.foundStart).toBeFalsy();
      expect(position.foundEnd).toBeFalsy();
      nodeEle.innerHTML = '';
      expect(nodeEle.innerHTML).toBe('');
    })
    expect(nodeEle.innerHTML).toBe('');
  });

  it('should validate claims section annotation',()=>{
    const nodeEle = document.createElement('div');
    annotations.forEach(c=> {
      if(c.document_id === 19556893){
        nodeEle.innerHTML = doc_19556893.claims;
      } else if(c.document_id === 43485261){
        nodeEle.innerHTML = doc_43485261.claims;
      } else {  
        nodeEle.innerHTML = doc_22577589.claims;      
      }
      const state = service.validateText(nodeEle, c);
      const position = service.getSavedAnnotationPosition(nodeEle, c);
      if(c.field === 'claims'){
        expect(state).toBeTruthy();
        expect(position.startPos).toBeGreaterThanOrEqual(0);
        expect(position.endPos).toBeGreaterThan(0);
        expect(position.startNode).toBeTruthy();
        expect(position.endNode).toBeTruthy();
      } else {
        expect(state).toBeFalsy();
      }
      expect(position.foundStart).toBeFalsy();
      expect(position.foundEnd).toBeFalsy();
      nodeEle.innerHTML = '';
      expect(nodeEle.innerHTML).toBe('');
    })
    expect(nodeEle.innerHTML).toBe('');
  });

  it('should validate description section annotation',()=>{
    const nodeEle = document.createElement('div');
    annotations.forEach(c=> {
      if(c.document_id === 19556893){
        nodeEle.innerHTML = doc_19556893.description;
      } else if(c.document_id === 43485261){
        nodeEle.innerHTML = doc_43485261.description;
      } else {  
        nodeEle.innerHTML = doc_22577589.description;      
      }
      const state = service.validateText(nodeEle, c);
      const position = service.getSavedAnnotationPosition(nodeEle, c);
      if(c.field === 'description'){
        expect(state).toBeTruthy();
        expect(position.startPos).toBeGreaterThanOrEqual(0);
        expect(position.endPos).toBeGreaterThan(0);
        expect(position.startNode).toBeTruthy();
        expect(position.endNode).toBeTruthy();
      } else {
        expect(state).toBeFalsy();
      }
      expect(position.foundStart).toBeFalsy();
      expect(position.foundEnd).toBeFalsy();
      nodeEle.innerHTML = '';
      expect(nodeEle.innerHTML).toBe('');
    })
    expect(nodeEle.innerHTML).toBe('');
  });
});
