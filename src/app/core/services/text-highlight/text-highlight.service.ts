import { InnerHtmlDirective } from './../../directives/inner-html.directive';
import { Injectable } from '@angular/core';
import { TextUtil } from '@core/utils';
import { AnnotationPosition, DocumentAnnotation } from '../annotation/types';

@Injectable({
  providedIn: 'root'
})
export class TextHighlightService {

  static HIGHLIGHT_TEXT_CSS_CLASS = 'pa-text-highlight';
  static HIGHLIGHT_TEXT_TMP_CSS_CLASS = 'pa-text-highlight-tmp';
  static HIGHLIGHT_TEXT_LABEL_CSS_CLASS = 'pa-label-highlight';
  static HIGHLIGHT_TEXT_COMMENT_CSS_CLASS = 'pa-comment-highlight';
  static HIGHLIGHT_TEXT_START_CSS_CLASS = 'pa-text-start-highlight';
  static HIGHLIGHT_TEXT_MIDDLE_CSS_CLASS = 'pa-text-middle-highlight';
  static HIGHLIGHT_TEXT_END_CSS_CLASS = 'pa-text-end-highlight';
  static HIGHLIGHT_TEXT_NON_SELECT_CSS_CLASS = 'pa-text-non-select';

  constructor() {
  }

  getSelectedTextPosition(tag: HTMLElement, highlightTextCssClass: string, foundStart = false, foundEnd = false): AnnotationPosition {
    let startPos = 0;
    let endPos = 0;

    for (let i = 0; i < tag.childNodes.length; i++) {
      const currentNode = tag.childNodes[i] as HTMLElement;
      const textContent = currentNode.textContent;

      if (foundEnd) {
        break;
      }

      if (this.isSkipHighlightNode(currentNode)) {
        continue;
      }

      if (currentNode.nodeType === Node.TEXT_NODE) {
        if (!foundStart) {
          startPos += textContent.length;
        }

        if (!foundEnd) {
          endPos += textContent.length;
        }
      }

      if (currentNode.className?.includes(highlightTextCssClass)) {
        if (currentNode.className?.includes(TextHighlightService.HIGHLIGHT_TEXT_START_CSS_CLASS)) {
          foundStart = true;
        }

        if (currentNode.className?.includes(TextHighlightService.HIGHLIGHT_TEXT_END_CSS_CLASS)) {
          endPos += textContent.length;
          foundEnd = true;
        }
      }

      if (!foundEnd) {
        const pos = this.getSelectedTextPosition(currentNode, highlightTextCssClass, foundStart, foundEnd);
        if (!foundStart) {
          startPos += pos.startPos;
          foundStart = pos.foundStart;
        }
        if (!foundEnd) {
          endPos += pos.endPos;
          foundEnd = pos.foundEnd;
        }
      }
    }

    return {startPos: startPos, endPos: endPos, foundStart: foundStart, foundEnd: foundEnd};
  }

  isSkipHighlightNode(node: Node | Element): boolean {
    if (node instanceof Element) {
      return node.classList.contains(TextHighlightService.HIGHLIGHT_TEXT_NON_SELECT_CSS_CLASS);
    }
    return node.parentElement?.classList?.contains(TextHighlightService.HIGHLIGHT_TEXT_NON_SELECT_CSS_CLASS) || false;
  }

  /**
   * Ref https://javascript.info/selection-range
   */
  setHighlightChildNodes(range: Range, highlightNode: HTMLElement) {
    let isHighlightNodeUsed = false;
    // Recursively wrap text nodes in a span

    enum NodeType {
      START,
      MIDDLE,
      END
    }

    const cssClasses = {
      [NodeType.START]: TextHighlightService.HIGHLIGHT_TEXT_START_CSS_CLASS,
      [NodeType.MIDDLE]: TextHighlightService.HIGHLIGHT_TEXT_MIDDLE_CSS_CLASS,
      [NodeType.END]: TextHighlightService.HIGHLIGHT_TEXT_END_CSS_CLASS
    }

    const wrapNode = (node: Node, type: NodeType) => {
      if (node.nodeType === Node.TEXT_NODE) {
        // Wrap text node
        let newSpan: HTMLElement;
        if (isHighlightNodeUsed) {
          const tmpNode = document.createElement('div');
          tmpNode.appendChild(highlightNode.cloneNode());
          newSpan = tmpNode.firstChild as HTMLElement;
        } else {
          newSpan = highlightNode;
          isHighlightNodeUsed = true;
        }
        newSpan.textContent = node.textContent;
        newSpan.classList.remove(...Object.values(cssClasses));
        newSpan.classList.add(cssClasses[type]);
        node.parentNode.replaceChild(newSpan, node);
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Recursively wrap child nodes
        Array.from(node.childNodes).forEach(wrapNode);
      }
    }

    // Handle partial selection of nodes
    const handlePartialNode = (node: Text, type: NodeType) => {
      const isStart = type === NodeType.START;
      const newNode = node.splitText(isStart ? range.startOffset : range.endOffset);
      if (isStart) {
        range.setStartBefore(newNode);
        wrapNode(newNode, type);
      } else {
        range.setEndBefore(newNode);
        wrapNode(node, type);
      }
      return newNode;
    }

    // Collect all text nodes within the selection
    const nodes: Node[] = [];
    const walker = document.createTreeWalker(range.commonAncestorContainer, NodeFilter.SHOW_TEXT, {
      acceptNode(node) {
        return NodeFilter.FILTER_ACCEPT;
      }
    });

    while (walker.nextNode()) {
      const currentNode = walker.currentNode;
      if (range.intersectsNode(currentNode)) {
        nodes.push(currentNode);
      }
    }

    // Wrap nodes
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i] as Node;

      if (this.isSkipHighlightNode(node)) {
        continue;
      }

      if (i === 0 && node === range.startContainer && node.nodeType === Node.TEXT_NODE) {
        // Handle partially selected start node
        handlePartialNode(node as Text, NodeType.START);
      } else if (i === nodes.length - 1 && node === range.endContainer && node.nodeType === Node.TEXT_NODE) {
        // Handle partially selected end node
        handlePartialNode(node as Text, NodeType.END);
      } else {
        // Fully selected node
        wrapNode(node, NodeType.MIDDLE);
      }
    }
  }

  /**
   * Highlight the selected text by replacing the content with a new element with pa-text-highlight class.
   * Save the container element of selected text which is an element of textSelectionTagIds.
   * This purposes for restoring after clearing highlight.
   */
  highlightSelectedText(selection: Selection, containerElement: HTMLElement, cssClass: string): {
    range: Range,
    content: Node,
    highlightNode: HTMLElement
  } {
    let range: Range;
    try {
      range = selection.getRangeAt(0);
    } catch (e) {
      console.error(e);
    }

    if (range) {
      try {
        const wholeNodeRange = new Range();
        wholeNodeRange.setStartBefore(containerElement.firstChild);
        wholeNodeRange.setEndAfter(containerElement.firstChild);

        const highlightNode = document.createElement('span');
        highlightNode.id = 'pa-text-highlight-' + TextUtil.genRandomString();
        highlightNode.className = `${TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS} ${cssClass}`;

        const selectedContainer = {
          range: wholeNodeRange,
          content: containerElement.cloneNode(true),
          highlightNode: highlightNode
        };

        if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
          highlightNode.classList.add(...[TextHighlightService.HIGHLIGHT_TEXT_START_CSS_CLASS, TextHighlightService.HIGHLIGHT_TEXT_END_CSS_CLASS]);
          range.surroundContents(highlightNode);
        } else {
          this.setHighlightChildNodes(range, highlightNode);
        }
        return selectedContainer;
      } catch (e) {
        console.error(e);
      }
    }

    return {range: null, content: null, highlightNode: null};
  }

  clearCurrentHighlight(selectedContainer: { range: Range, content: Node, highlightNode: HTMLElement }) {
    if (!selectedContainer || !selectedContainer.range) {
      return;
    }

    selectedContainer.range.deleteContents();
    selectedContainer.content.childNodes.forEach((child) => {
      selectedContainer.range.insertNode(child);
    });

    selectedContainer.range = null;
    selectedContainer.content = null;
  }

  getSelection(event: MouseEvent) {
    if (event && event.view?.getSelection()) {
      return event.view.getSelection();
    }

    if (window.getSelection) {
      return window.getSelection();
    }

    if (document.getSelection) {
      return document.getSelection();
    }
  }

  getSelectedText(fieldId: string, separator: string): string {
    const results = [];
    document.querySelectorAll(`#${fieldId} .${TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS}`)
      .forEach((ele) => {
        if (!this.isSkipHighlightNode(ele)) {
          results.push(ele.textContent);
        }
      });
    return results.join(separator);
  }

  getHighlightNodeText(node: HTMLElement): string {
    if (!node) {
      return '';
    }

    let text = '';
    for (let i = 0; i < node.childNodes.length; i++) {
      const currentNode = node.childNodes[i];
      if (this.isSkipHighlightNode(currentNode)) {
        continue;
      }

      if (currentNode.nodeType === Node.TEXT_NODE) {
        text += currentNode.textContent;
      } else {
        text += this.getHighlightNodeText(currentNode as HTMLElement);
      }
    }

    return text;
  }

  validateText(node: HTMLElement, highlight): boolean {
    if (!node) { return false; }

    const nodeText = this.getHighlightNodeText(node);
    const stringHighlight = nodeText.replace(/\s/g, ' ').substring(highlight.start_pos, highlight.end_pos);
    const textAnnotation = highlight.text.replace(/\s/g, ' ');
    const isValidText = stringHighlight === textAnnotation;
    if (!isValidText) {
      const isLegacyValidText = nodeText.replace(/\n/g, '').replace(/\s/g, ' ').substring(highlight.start_pos, highlight.end_pos) === textAnnotation;
      if (isLegacyValidText) {
        // adjust the position hare for annotation saved with double line break
        let adjustStart = 0;
        if (highlight.start_pos > 0) {
          adjustStart = (nodeText.substring(0, (highlight.start_pos - 1)).match(/\n/g) || []).length;
        }
        const adjustBetween = (nodeText.substring(highlight.start_pos, highlight.end_pos).match(/\n/g) || []).length;
        highlight.start_pos += adjustStart;
        highlight.end_pos += (adjustStart + adjustBetween);
      }
      return isLegacyValidText;
    }
    return isValidText;
  }

  getSavedAnnotationPosition(node: Node, annotation: DocumentAnnotation, startPos = null, endPos = null): AnnotationPosition {
    startPos = startPos === null ? annotation.start_pos : startPos;
    endPos = endPos === null ? annotation.end_pos : endPos;

    let startNode: Node = null;
    let endNode: Node = null;

    for (let i = 0; i < node.childNodes.length; i++) {
      const currentNode: Node = node.childNodes[i];
      if (this.isSkipHighlightNode(currentNode)) {
        continue;
      }

      const textContent = currentNode.textContent;

      if (currentNode.nodeType === Node.TEXT_NODE) {
        if (startNode === null) {
          if (startPos <= textContent.length) {
            startNode = currentNode;
          } else {
            startPos -= textContent.length;
          }
        }

        if (endNode === null) {
          if (endPos <= textContent.length) {
            endNode = currentNode;
            break;
          } else {
            endPos -= textContent.length;
          }
        }
      }

      if (endNode === null) {
        const pos = this.getSavedAnnotationPosition(currentNode, annotation, startPos, endPos);
        if (startNode === null) {
          startPos = pos.startPos;
          startNode = pos.startNode;
        }

        if (endNode === null) {
          endPos = pos.endPos;
          endNode = pos.endNode;
        }
      }
    }

    return {
      startPos: startPos,
      endPos: endPos,
      startNode: startNode,
      endNode: endNode
    };
  }

  getNewPosition(node: HTMLElement, annotationNewPosition: DocumentAnnotation): AnnotationPosition {
    if (!node) {
      return null;
    }

    const textAnnotation = annotationNewPosition.text.replace(/\s/g, ' ');
    if (textAnnotation.split(' ').length < 3) {
      return null;
    }
    const textNode = node.textContent.replace(/\s/g, ' ');
    const positionLeft = textNode.toLowerCase().lastIndexOf(textAnnotation.toLowerCase(), annotationNewPosition.start_pos);
    const positionRight = textNode.toLowerCase().indexOf(textAnnotation.toLowerCase(), annotationNewPosition.start_pos);

    if (positionLeft === -1 && positionRight === -1) {
      return null;
    }

    let startPos = -1;
    let endPos = -1;
    if (positionLeft > -1 && positionRight === -1) {
      endPos = positionLeft + (annotationNewPosition.end_pos - annotationNewPosition.start_pos);
      startPos = positionLeft;

      return { startPos: startPos, endPos: endPos };
    }

    if (positionLeft === -1 && positionRight > -1) {
      endPos = positionRight + (annotationNewPosition.end_pos - annotationNewPosition.start_pos);
      startPos = positionRight;

      return { startPos: startPos, endPos: endPos };
    }

    if (positionLeft > -1 && positionRight > -1) {
      if (annotationNewPosition.start_pos - positionLeft <= positionRight - annotationNewPosition.start_pos) {
        endPos = positionLeft + (annotationNewPosition.end_pos - annotationNewPosition.start_pos);
        startPos = positionLeft;
      } else {
        endPos = positionRight + (annotationNewPosition.end_pos - annotationNewPosition.start_pos);
        startPos = positionRight;
      }
      return { startPos: startPos, endPos: endPos };
    }

    return null;
  }
}
