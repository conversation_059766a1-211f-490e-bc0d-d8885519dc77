import { NavigationExtras } from '@angular/router';

export enum ToastTypeEnum {
  DEFAULT = 'default',
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}

export interface ToastLink {
  text: string;
  new_tab_url?: string;
  current_tab_params?: {
    commands: string[];
    extras?: NavigationExtras;
  };
}

export interface ToastInfo {
  body: string;
  type: ToastTypeEnum;
  header?: string;
  classname?: string;
  delay?: number;
  link?: ToastLink;
  closable?: boolean;
  autohide?: boolean;
}

export const TOAST_ERROR_403: ToastInfo = {
  type: ToastTypeEnum.ERROR,
  header: `Sorry, you don't have permission to access this page.`,
  body: 'If your account permissions changed recently, try logging in again. Otherwise, reach out to us at ' +
        '<a href="mailto:<EMAIL>"><EMAIL></a> or by using our live chat support.',
  delay: 5000
}