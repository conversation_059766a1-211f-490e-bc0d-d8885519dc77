import { EventEmitter, Injectable } from '@angular/core';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

@Injectable({
  providedIn: 'root'
})
export class TooltipService {
  openingTooltips: NgbPopover[] = [];
  parentPopover: <PERSON><PERSON><PERSON><PERSON><PERSON>;

  readonly closeParentPopover$ = new EventEmitter<NgbPopover>(null);

  constructor() {
  }

  openTooltip(tooltip: NgbPopover, parentPopover: NgbPopover = null) {
    this.parentPopover = parentPopover;
    this.closeAllTooltips(tooltip);

    if (tooltip) {
      if (!tooltip.isOpen()) {
        tooltip.open();
      }
      this.openingTooltips.push(tooltip);
    }
  }

  closeTooltip(tooltip: NgbPopover) {
    if (tooltip) {
      try {
        tooltip.close();
        this.closeParentPopover();
      } catch (e) {
        console.warn('Error closing tooltip', e);
      }
      this.openingTooltips = this.openingTooltips.filter((t) => t !== tooltip);
    }
  }
  closeParentPopover() {
    if (!this.parentPopover || !this.parentPopover.isOpen()) {
      return;
    }
    this.closeParentPopover$.emit(this.parentPopover);
    this.parentPopover = null;
  }

  closeAllTooltips(tooltip: NgbPopover = null) {
    this.openingTooltips
      .filter((t) => t !== tooltip && t !== this.parentPopover)
      .forEach(tooltip => {
        try {
          tooltip.close();
        } catch (e) {
          console.warn('Error closing tooltip', e);
        }
      });
  }
}
