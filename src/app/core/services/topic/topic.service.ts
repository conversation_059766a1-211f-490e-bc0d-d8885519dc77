import { Injectable } from '@angular/core';
import { ApiService, PaginationMetadata, TopicModel } from '@core';
import { Observable, of, switchMap } from 'rxjs';
import { catchError, map, take } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TopicService {
  static readonly GENERAL_TOPIC_NAME = 'General';

  constructor(
    private apiService: ApiService
  ) {
  }

  getTopics(payload, redirectTo403Page = true): Observable<{ topics: Array<TopicModel>, page: PaginationMetadata }> {
    return this.apiService.get('web/topics', payload, redirectTo403Page)
      .pipe(
        map(response => response.data)
      );
  }

  updateTopic(topicId: number, payload): Observable<TopicModel> {
    return this.apiService.patch(`web/topics/${topicId}`, payload)
      .pipe(
        map(response => response.data)
      );
  }

  createTopic(payload): Observable<TopicModel> {
    return this.apiService.post('web/topics', payload)
      .pipe(
        map(response => response.data)
      );
  }

  getTopic(id: number): Observable<TopicModel> {
    return this.apiService.get(`web/topics/${id}`)
      .pipe(
        map(response => response.data),
      );
  }

  deleteTopic(id: number): Observable<any> {
    return this.apiService.delete(`web/topics/${id}`);
  }

  createGeneralTopic(): Observable<TopicModel> {
    return this.getTopics({is_predefined: 1, page_size: 1})
      .pipe(
        take(1),
        switchMap(({topics}) => {
          if (topics.length) {
            return of(topics[0]);
          }
          return this.createTopic({name: TopicService.GENERAL_TOPIC_NAME, is_predefined: 1});
        }),
        catchError((error) => {
          console.warn(error);
          return of(null);
        })
      );
  }
}
