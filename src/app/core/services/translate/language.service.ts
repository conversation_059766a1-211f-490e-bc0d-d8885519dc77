import { Injectable } from '@angular/core';
import { ApiService } from '@core/services';
import { DetectLanguageRequest, DetectLanguageResponse } from './types';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {

  constructor(private apiService: ApiService) {
  }

  detectLanguage(payload: DetectLanguageRequest): Observable<DetectLanguageResponse> {
    return this.apiService.post('web/language/detect', payload).pipe(map(resp => resp.data));
  }
}
