import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideMatomo } from 'ngx-matomo-client';
import { UserService } from './user.service';
import { RouterModule } from '@angular/router';

describe('UserService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
    providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
  }));

  it('should be created', () => {
    const service: UserService = TestBed.inject(UserService);
    expect(service).toBeTruthy();
  });
});
