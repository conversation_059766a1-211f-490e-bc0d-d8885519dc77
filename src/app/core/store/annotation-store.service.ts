import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { DocumentAnnotation, Label } from '@core/services';

@Injectable({
  providedIn: 'root'
})

/**
 * Store for annotation feature shared data
 * <AUTHOR> <<EMAIL>>
 */
export class AnnotationStoreService {
  /**
   * reference for annotation labels list
   */
  private annotationLabelsSubject = new BehaviorSubject<Array<Label>>([]);

  /**
   * reference for annotation custom label form slide
   */
  private annotationLabelSlideSubject = new BehaviorSubject <boolean>(true);

  /**
   * reference for list of user labeled annotation for a specific patent
   */
  private listLabeledSubject = new BehaviorSubject<Array<DocumentAnnotation>>([]);

  /**
   * reference for list of user comments annotation for a specific patent
   */
  private listCommentsSubject = new BehaviorSubject<Array<DocumentAnnotation>>([]);
  readonly listComments$ = this.listCommentsSubject.asObservable();


  public generalComments: DocumentAnnotation[] = [];
  public sectionComments: DocumentAnnotation[] = [];

  constructor() {
  }

  private _savedAnnotationId = new BehaviorSubject<number>(null);
  readonly savedAnnotationId$ = this._savedAnnotationId.asObservable();

  private _repliedToAnnotationId = new BehaviorSubject<number>(null);
  readonly repliedToAnnotationId$ = this._repliedToAnnotationId.asObservable();

  private _removeAnnotation$ = new BehaviorSubject<'comments' | 'labels'>(null);
  readonly removeAnnotation$ = this._removeAnnotation$.asObservable();

  get annotationLabels() {
    return this.annotationLabelsSubject.getValue();
  }

  set annotationLabels(labels: Array<Label>) {
    this.annotationLabelsSubject.next(labels);
  }

  get labelSlide() {
    return this.annotationLabelSlideSubject.getValue();
  }

  set labelSlide(SlideLeft: boolean) {
    this.annotationLabelSlideSubject.next(SlideLeft);
  }

  get listLabeled(): Array<DocumentAnnotation> {
    return this.listLabeledSubject.getValue();
  }

  set listLabeled(listLabeled: Array<DocumentAnnotation>) {
    this.listLabeledSubject.next(listLabeled);
  }

  get listComments(): Array<DocumentAnnotation> {
    return this.listCommentsSubject.getValue();
  }

  set listComments(listLabeled: Array<DocumentAnnotation>) {
    this.listCommentsSubject.next(listLabeled);
  }

  get removeAnnotation(): 'comments' | 'labels' {
    return this._removeAnnotation$.getValue();
  }

  set removeAnnotation(nextValue: 'comments' | 'labels') {
    this._removeAnnotation$.next(nextValue);
  }

  addComment(comment: DocumentAnnotation) {
    this.listComments = [...this.listComments, comment];
  }

  public resetAnnotation() {
    this.purgeAnnotationLabels();
    this.labelSlide = true;
    this.listLabeled = [];
    this.listComments = [];
    this.sectionComments = [];
    this.generalComments = [];
    this.removeAnnotation = null;
  }

  public purgeAnnotationLabels() {
    this.annotationLabels = [];
  }

  /**
   * removeLabeledHighlightsByLabelID
   */
  public removeLabeledHighlightsByLabelID(labelID: number): void {
    this.listLabeled = this.listLabeled.filter(label => label.label_id !== labelID);
  }

  setSavedAnnotationId(id: number) {
    this._savedAnnotationId.next(id);
  }

  setRepliedToAnnotationId(id: number) {
    this._repliedToAnnotationId.next(id);
  }

  clearStoredData() {
    this.annotationLabels = [];
    this.labelSlide = true;
    this.listLabeled = [];
    this.listComments = [];
    this._repliedToAnnotationId.next(null);
    this._savedAnnotationId.next(null);
  }
}
