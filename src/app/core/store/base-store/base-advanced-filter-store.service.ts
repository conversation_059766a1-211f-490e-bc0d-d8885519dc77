import { BehaviorSubject } from 'rxjs';
import { Clause } from '@core/services';

export abstract class BaseAdvancedFilterStoreService {

  private _advancedFilterBooleanClauses = new BehaviorSubject<Clause[]>([]);

  readonly advancedFilterBooleanClauses$ = this._advancedFilterBooleanClauses.asObservable();

  get advancedFilterBooleanClauses(): Clause[] {
    return this._advancedFilterBooleanClauses.value;
  }

  set advancedFilterBooleanClauses(val: Clause[]) {
    this._advancedFilterBooleanClauses.next(val);
  }

  private _advancedFilterAdvancedQuery = new BehaviorSubject<string>('');

  readonly advancedFilterAdvancedQuery$ = this._advancedFilterAdvancedQuery.asObservable();
  readonly advancedFilterAppliedAdvancedQuery$ = this._advancedFilterAdvancedQuery.asObservable();

  get advancedFilterAdvancedQuery(): string {
    return this._advancedFilterAdvancedQuery.value;
  }

  set advancedFilterAdvancedQuery(val: string) {
    this._advancedFilterAdvancedQuery.next(val);
  }

  private _advancedFilterAppliedBooleanClauses = new BehaviorSubject<Clause[]>([]);

  readonly advancedFilterAppliedBooleanClauses$ = this._advancedFilterAppliedBooleanClauses.asObservable();

  get advancedFilterAppliedBooleanClauses(): Clause[] {
    return this._advancedFilterAppliedBooleanClauses.value;
  }

  set advancedFilterAppliedBooleanClauses(val: Clause[]) {
    this._advancedFilterAppliedBooleanClauses.next(val);
  }

  private _advancedFilterAppliedAdvancedQuery = new BehaviorSubject<string>('');

  get advancedFilterAppliedAdvancedQuery(): string {
    return this._advancedFilterAppliedAdvancedQuery.value;
  }

  set advancedFilterAppliedAdvancedQuery(val: string) {
    this._advancedFilterAppliedAdvancedQuery.next(val);
  }

  private _advancedFilterAppliedQuery = new BehaviorSubject<string>('');

  readonly advancedFilterAppliedQuery$ = this._advancedFilterAppliedQuery.asObservable();

  get advancedFilterAppliedQuery(): string {
    return this._advancedFilterAppliedQuery.value;
  }

  set advancedFilterAppliedQuery(val: string) {
    this._advancedFilterAppliedQuery.next(val);
  }

  private _isAdvancedFilterAdvancedMode = new BehaviorSubject<boolean>(false);

  readonly _isAdvancedFilterAdvancedMode$ = this._isAdvancedFilterAdvancedMode.asObservable();

  get isAdvancedFilterAdvancedMode(): boolean {
    return this._isAdvancedFilterAdvancedMode.value;
  }

  set isAdvancedFilterAdvancedMode(val: boolean) {
    this._isAdvancedFilterAdvancedMode.next(val);
  }

  get isAdvancedFilterActive(): boolean {
    return this.advancedFilterAdvancedQuery?.length > 0 || this.advancedFilterAppliedAdvancedQuery?.length > 0;
  }

  resetAdvancedFilter() {
    this.advancedFilterBooleanClauses = [];
    this.advancedFilterAdvancedQuery = '';
    this.advancedFilterAppliedBooleanClauses = [];
    this.advancedFilterAppliedAdvancedQuery = '';
    this.advancedFilterAppliedQuery = '';
    this.isAdvancedFilterAdvancedMode = false;
  }

  clearAdvancedFilterStoredData() {
    this.resetAdvancedFilter();
  }
}
