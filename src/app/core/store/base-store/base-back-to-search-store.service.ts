import { BehaviorSubject } from 'rxjs';

export abstract class BaseBackToSearchStoreService {
  private _backPatentSearch$ = new BehaviorSubject<boolean>(false);

  get backPatentSearch(): boolean {
    return this._backPatentSearch$.getValue();
  }

  set backPatentSearch(nextValue: boolean) {
    this._backPatentSearch$.next(nextValue);
  }

  clearBackToSearchStoredData(): void {
    this.backPatentSearch = false;
  }
}
