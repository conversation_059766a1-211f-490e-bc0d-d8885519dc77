import { BehaviorSubject } from 'rxjs';

export abstract class BaseChartCalculationService {
  private _chartQuantity$ = new BehaviorSubject<number>(0);
  private _isCalculatingCharts$ = new BehaviorSubject <boolean>(false);
  readonly isCalculatingCharts$ = this._isCalculatingCharts$.asObservable();

  private _chartPayload$ = new BehaviorSubject<Object>(null);
  readonly chartPayload$ = this._chartPayload$.asObservable();
  private _shouldCalculateCharts$ = new BehaviorSubject<boolean>(true);

  public chartParameter: {[chartName: string]: {xAxis?:string, yAxis?:string, quantity?:number}} = {};

  get chartQuantity() {
    return this._chartQuantity$.getValue();
  }

  set chartQuantity(nextValue: number) {
    this._chartQuantity$.next(nextValue);
  }

  get isCalculatingCharts() {
    return this._isCalculatingCharts$.getValue();
  }

  set isCalculatingCharts(nextValue: any) {
    this._isCalculatingCharts$.next(nextValue);
  }

  get chartPayload(): Object {
    return this._chartPayload$.getValue();
  }

  set chartPayload(nextValue: Object) {
    this._chartPayload$.next(nextValue);
  }

  get shouldCalculateCharts(): boolean {
    return this._shouldCalculateCharts$.getValue();
  }

  set shouldCalculateCharts(nextValue: boolean) {
    this._shouldCalculateCharts$.next(nextValue);
  }

  clearChartCalculationStoredData(): void {
    this.chartQuantity = 0;
    this.isCalculatingCharts = false;
    this.chartPayload = null;
    this.shouldCalculateCharts = true;
    this.chartParameter = {};
  }
}
