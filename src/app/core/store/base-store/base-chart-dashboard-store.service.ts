import { DashboardActionMessageType } from '@shared/charts/chart-dashboard/types';
import { BehaviorSubject } from 'rxjs';
import { categories, manualCalculationCharts } from '@shared/charts/utils';
import { Chart } from '@shared/charts';
import { featureType } from '@core/services/user/types';

export abstract class BaseChartDashboardStoreService {
  defaultChartCategories = categories;
  customChartCategories: { name: string, charts: Array<string>, index: number }[] = [];

  private _chartDashboardType$ = new BehaviorSubject<featureType>('semantic');

  private _removeChart$ = new BehaviorSubject<Chart>(null);
  readonly removeChart$ = this._removeChart$.asObservable();

  private _chartDashboardAction$ = new BehaviorSubject<DashboardActionMessageType>({});
  readonly chartDashboardAction$ = this._chartDashboardAction$.asObservable();

  private _activeChartCategory$ = new BehaviorSubject<string>(null);

  private _arrangeDashboard = false;

  get arrangeDashboard() {
    return this._arrangeDashboard;
  }

  set arrangeDashboard(value) {
    this._arrangeDashboard = value;
  }

  private _addChartInDashboard = false;

  get addChartInDashboard() {
    return this._addChartInDashboard;
  }

  set addChartInDashboard(value) {
    this._addChartInDashboard = value;
  }

  get removeChart() {
    return this._removeChart$.getValue();
  }

  set removeChart(nextValue: Chart) {
    this._removeChart$.next(nextValue);
  }

  get activeChartCategory(): string {
    return this._activeChartCategory$.getValue();
  }

  set activeChartCategory(nextValue: string) {
    this._activeChartCategory$.next(nextValue);
  }

  get chartDashboardType(): featureType {
    return this._chartDashboardType$.getValue();
  }

  set chartDashboardType(nextValue: featureType ) {
    this._chartDashboardType$.next(nextValue);
  }

  get chartDashboardAction() {
    return this._chartDashboardAction$.getValue();
  }

  set chartDashboardAction(nextValue: DashboardActionMessageType) {
    this._chartDashboardAction$.next(nextValue);
  }

  isCustomChartCategory(): boolean {
    const foundCategory = this.getActiveCustomChartCategory();
    return !!(foundCategory);
  }

  getActiveCustomChartCategory(): {name: string, charts: Array<string>, index: number} {
    return this.customChartCategories.find((d) => this.generateCustomChartCategoryId(d) === this.activeChartCategory);
  }

  resetChartDashboardType(): void {
    this.chartDashboardType = 'semantic';
  }

  getActiveChartCategoryName(): string {
    const foundCategory = this.getActiveCustomChartCategory();
    if (foundCategory) {
      return foundCategory.name;
    }
    return this.activeChartCategory;
  }

  generateCustomChartCategoryId(category: {name: string, charts: Array<string>, index: number}): string {
    if (!category) {
      return null;
    }
    return `${category.index}***${category.name}`;
  }

  getActiveDefaultCategoryCharts() {
    if (this.isCustomChartCategory()) {
      return [];
    }
    return this.defaultChartCategories.find(ct => ct.category === this.activeChartCategory)?.charts?.filter(c => c.features.indexOf(this.chartDashboardType) > -1);
  }

  getChartActiveNames(): Array<string> {
    let chartNames = [ ];
    if (this.isCustomChartCategory()) {
      this.customChartCategories.forEach(tab => {
        chartNames.push(...tab.charts.filter(c => !manualCalculationCharts.includes(c)).map(val => val));
      });
    } else {
      this.getActiveDefaultCategoryCharts()?.filter(c => !manualCalculationCharts.includes(c.name))?.forEach(ch => {
        chartNames.push(ch.name);
      });
    }
    return [...new Set(chartNames)];
  }

  hasFeature(category): boolean {
    return category.charts.filter((chart) => chart.features.includes(this.chartDashboardType)).length > 0;
  }

  /**
   * Find a chart object from defaultChartCategories based on given chart name
   * @param chartName name of the chart
   * @returns Chart object from defaultChartCategories
   */
  getChartByName(chartName: string): Chart{
    if(this.defaultChartCategories){
      let chart;
      for(const c of this.defaultChartCategories){
        chart = c.charts.find(ch => ch.name === chartName);
        if(chart) break;
      }
      return chart;
    }
    return null;
  }

  clearChartDashboardStoredData() {
    this.defaultChartCategories = categories;
    this.customChartCategories = [];
    this.resetChartDashboardType();
    this.removeChart = null;
    this.chartDashboardAction = {};
    this.activeChartCategory = null;
    this.arrangeDashboard = false;
    this.addChartInDashboard = false;
  }
}
