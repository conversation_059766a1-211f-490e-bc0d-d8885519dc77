import { BehaviorSubject } from 'rxjs';

export abstract class BaseChartFilterStoreService {
  private _chartSelectedValues$ = new BehaviorSubject<Object>({});
  private _chartFilterDisabled$ = new BehaviorSubject<boolean>(false);
  readonly chartFilterDisabled$ = this._chartFilterDisabled$.asObservable();

  get chartSelectedValues() {
    return this._chartSelectedValues$.getValue();
  }

  set chartSelectedValues(nextValue: any) {
    this._chartSelectedValues$.next(nextValue);
  }

  get chartFilterDisabled(): boolean {
    return this._chartFilterDisabled$.getValue();
  }

  set chartFilterDisabled(nextValue: boolean) {
    this._chartFilterDisabled$.next(nextValue);
  }

  clearChartFilterStoredData(): void {
    this.chartSelectedValues = {};
    this.chartFilterDisabled = false;
  }
}
