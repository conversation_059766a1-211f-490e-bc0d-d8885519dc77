import { BehaviorSubject } from 'rxjs';

export abstract class BaseChartLayoutStoreService {
  private _expandingChartComponent$ = new BehaviorSubject<Object>({});
  readonly expandingChartComponent$ = this._expandingChartComponent$.asObservable();

  private _singleChartColumn$ = new BehaviorSubject<boolean>(false);
  readonly singleChartColumn$ = this._singleChartColumn$.asObservable();

  get expandingChartComponent() {
    return this._expandingChartComponent$.getValue();
  }

  set expandingChartComponent(nextValue: any) {
    this._expandingChartComponent$.next(nextValue);
  }

  get singleChartColumn() {
    return this._singleChartColumn$.getValue();
  }

  set singleChartColumn(nextValue: any) {
    this._singleChartColumn$.next(nextValue);
  }

  clearChartLayoutStoredData(): void {
    this.expandingChartComponent = {};
    this.singleChartColumn = false;
  }
}
