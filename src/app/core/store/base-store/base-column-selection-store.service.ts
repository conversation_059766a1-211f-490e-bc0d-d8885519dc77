import { BehaviorSubject } from 'rxjs';

export abstract class BaseColumnSelectionStoreService {

  private _selectedColumnsToShow$ = new BehaviorSubject<Array<any>>([]);
  readonly selectedColumnsToShow$ = this._selectedColumnsToShow$.asObservable();

  private _selectAllDocuments$ = new BehaviorSubject<boolean>(null);
  readonly selectAllDocuments$ = this._selectAllDocuments$.asObservable();

  get selectedColumnsToShow() {
    return this._selectedColumnsToShow$.getValue();
  }

  set selectedColumnsToShow(nextValue: Array<any>) {
    this._selectedColumnsToShow$.next(nextValue);
  }

  set selectAllDocuments(nextValue: boolean) {
    this._selectAllDocuments$.next(nextValue);
  }

  showColumn(column) {
    return this.selectedColumnsToShow.findIndex(val => val.property === column) > -1;
  }

  showedColumnTitle(column: string): string {
    const el = this.selectedColumnsToShow.find(val => val.property === column);
    return el ? el?.label : '';
  }

  clearColumnSelectionStoredData(): void {
    this.selectedColumnsToShow = [];
  }
}
