import { Clause, featureType, PaginationMetadata, SortBySimilarityParam, SortParams } from '@core';
import { ViewModeTypeEnum } from '@search/patent/types';

export interface BaseCurrentStateModel {
  search: any;
  searchHash: string;
  patentNumbers: string;
  isPublications: boolean;
  addNumberToSearch: string[];
  shouldCalculateCharts: boolean;
  selectedPublications: string[];
  selectedPatentIds: number[];
  pagination: PaginationMetadata;
  pageSize: number;
  patentListViewMode: ViewModeTypeEnum;
  patentTableSort: SortParams;
  docdbFamilyIdFromPatentViewer: string;
  filtersOperator: 'AND' | 'OR';
  filters: any;
  extraFilters: any;
  advancedFilterBooleanClauses: Clause[];
  advancedFilterAdvancedQuery: string;
  advancedFilterAppliedBooleanClauses: Clause[];
  advancedFilterAppliedAdvancedQuery: string;
  advancedFilterAppliedQuery: string;
  isAdvancedFilterAdvancedMode: boolean;
  sortBySimilarity: SortBySimilarityParam;
  originalSearchHash: string;
  chartDashboardType: featureType;
  activeChartCategory: string;
  chartSelectedValues: Object;
  chartFilterDisabled: boolean;
  expandSearchInput: string;
  originalSearchInput: string;
  executeExpandSearchInput: boolean;
  typedPublications: string[];
  singleChartColumn: boolean;
}

export abstract class BaseCurrentStateStoreService {

  private readonly CURRENT_STATE_STORAGE_KEY = 'CURRENT_STATE_DATA';

  storeCurrentState<T extends BaseCurrentStateModel>(id: string, data: T = {} as T) {
    const mergedData = {...this.getBaseCurrentState(), ...data};
    localStorage.setItem(this.storeCurrentStateById(id), JSON.stringify(mergedData));
  }

  getCurrentState<T extends BaseCurrentStateModel>(id: string): T | null {
    const data = localStorage.getItem(this.storeCurrentStateById(id));
    return data ? JSON.parse(data) as T : null;
  }

  removeCurrentState(id: string) {
    try {
      localStorage.removeItem(this.storeCurrentStateById(id));
    } catch (e) {
      console.warn('Error removing current state from local storage', e);
    }
  }

  removeAllCurrentStates() {
    try {
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith(this.CURRENT_STATE_STORAGE_KEY)) {
          localStorage.removeItem(key);
        }
      });
    } catch (e) {
      console.warn('Error removing all current states from local storage', e);
    }
  }

  restoreCurrentState(id: string, callback: ((data: BaseCurrentStateModel) => void) | null = null): boolean {
    const data = this.getCurrentState(id);
    if (data) {
      this.restoreBaseCurrentState(data);
      this._restoreExtendedCurrentState(data);
      if (callback) {
        callback(data);
      }
      this.removeCurrentState(id);
      return true;
    }
    return false;
  }

  storeCurrentStateById(id: string): string {
    return `${this.CURRENT_STATE_STORAGE_KEY}_${id}`;
  }

  getBaseCurrentState(): BaseCurrentStateModel {
    return {} as BaseCurrentStateModel;
  }

  restoreBaseCurrentState<T extends BaseCurrentStateModel>(data: T) {
    // Implementation at derived classes
  }

  protected _restoreExtendedCurrentState<T extends BaseCurrentStateModel>(data: T) {
    // Implementation depends on specific store requirements
  }
}
