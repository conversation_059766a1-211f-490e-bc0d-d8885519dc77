import { ExtraFilters } from '@core/models/extraFilters';
import { BehaviorSubject } from 'rxjs';

export abstract class BaseFilterStoreService {

  filtersOperator: 'AND' | 'OR' = 'AND';
  private _filterValues = [];
  private _filters$ = new BehaviorSubject<Array<any>>([]);
  readonly filters$ = this._filters$.asObservable();
  private _filterRemoved$ = new BehaviorSubject<Object>({});
  readonly filterRemoved$ = this._filterRemoved$.asObservable();
  private _extraFilters$ = new BehaviorSubject<ExtraFilters>(new ExtraFilters());

  get filters() {
    return this._filterValues;
  }

  set filters(nextValue: any) {
    this._filterValues = nextValue;
  }

  protected emitFilters() {
    this._filters$.next(this.filters);
  }

  get filterRemoved() {
    return this._filterRemoved$.getValue();
  }

  set filterRemoved(nextValue: Object) {
    this._filterRemoved$.next(nextValue);
  }

  get extraFilters() {
    return this._extraFilters$.getValue();
  }

  set extraFilters(nextValue: any) {
    this._extraFilters$.next(nextValue);
  }

  getChartFilterQuery() {
    if (!this.filters) {
      return '';
    }
    const chartFilters = this.filters.filter(f => f.type === 'chart' && f.query);
    if (chartFilters.length === 0) {
      return '';
    }
    return chartFilters.map(f => f.query).join(' AND ');
  }

  getResultTableFilterQuery() {
    if (!this.filters) {
      return '';
    }
    const chartFilters = this.filters.filter(f => f.type === 'result-table' && f.query);
    if (chartFilters.length === 0) {
      return '';
    }
    return chartFilters.map(f => f.query).join(' AND ');
  }

  clearFilterStoredData(): void {
    this.filtersOperator = 'AND';
    this.filters = [];
    this.emitFilters();
    this.filterRemoved = {};
    this.extraFilters = new ExtraFilters();
  }
}
