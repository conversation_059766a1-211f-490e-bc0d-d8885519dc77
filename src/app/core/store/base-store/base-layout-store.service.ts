import { BehaviorSubject } from 'rxjs';
import { ViewModeTypeEnum } from '@search/patent/types';

export abstract class BaseLayoutStoreService {
  private _patentListViewMode = new BehaviorSubject<ViewModeTypeEnum>(ViewModeTypeEnum.COMBINED);
  readonly patentListViewMode$ = this._patentListViewMode.asObservable();

  get patentListViewMode(): ViewModeTypeEnum {
    return this._patentListViewMode.getValue();
  }

  set patentListViewMode(val: ViewModeTypeEnum) {
    this._patentListViewMode.next(val);
  }

  get isCombinedMode(): boolean{
    return !this.patentListViewMode || this.patentListViewMode === ViewModeTypeEnum.COMBINED;
  }

  get isListMode(): boolean{
    return this.patentListViewMode === ViewModeTypeEnum.LIST;
  }

  get isAnalysisMode(): boolean{
    return this.patentListViewMode === ViewModeTypeEnum.ANALYSIS;
  }

  clearLayoutStoredData(): void {
    this.patentListViewMode = ViewModeTypeEnum.COMBINED;
  }
}
