import { EventEmitter, Injector } from '@angular/core';
import { MonitorProfile, MonitorSharedProfile, Patent } from '@core/models';
import { PaginationMetadata } from '@core/services/semantic-search/types';
import { BehaviorSubject } from 'rxjs';
import { BaseStoreService } from './base-store.service';
import { BaseCurrentStateModel } from '../base-store/base-current-state-store.service';

export interface MonitorCurrentStateModel extends BaseCurrentStateModel {
  scrollTopPage: any;
  additionalExportParams: Object;
  elementIdToScrollPage: string;
  selectedMonitorRun: number;
  monitorRunRequestArr: number[];
  monitorRuns: any[];
  resultSetPagination: PaginationMetadata;
  firstMonitorRun: any;
}

export abstract class BaseMonitorStoreService extends BaseStoreService {

  private cloneProfile: MonitorProfile;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
    this.chartParameter['competitors_portfolio_analytics_profile'] = {xAxis: 'tech_fields', yAxis: 'citation_backward_count'};
  }

  /**
   * scroll top position of the page
   */
  scrollTopPage = null;
  /**
   * reference for active monitor tab on monitor product page
   */
  additionalExportParams: Object = {};
  setupMonitoringEvent = new EventEmitter(true);
  /**
   * reference for monitor loading state
   */
  private monitorLoadingSubject = new BehaviorSubject <boolean>(false);
  /**
   * reference for monitor profile list
   */
  private monitorProfilesSubject = new BehaviorSubject<Array<MonitorProfile>>(null);
  /**
   * reference for monitor shared profile list
   */
  private sharedProfilesSubject = new BehaviorSubject<Array<MonitorSharedProfile>>(null);
  /**
   * reference for monitor shared profile
   */
  private sharedProfileSubject = new BehaviorSubject<MonitorSharedProfile>(null);
  /**
   * reference for monitor profile
   */
  private monitorProfileSubject = new BehaviorSubject<MonitorProfile>(null);
  monitorProfileOb = this.monitorProfileSubject.asObservable();
  /**
   * selected monitor run for single profile
   */
  private _selectedMonitorRun$ = new BehaviorSubject<number>(null);
  selectedMonitorRunOb = this._selectedMonitorRun$.asObservable();
  /**
   * reference for ML in training
   */
  private _freezeML$ = new BehaviorSubject <boolean>(false);
  /**
   * monitor runs for single profile
   */
  private _monitorRuns$ = new BehaviorSubject<any>(null);
  monitorRunsOb = this._monitorRuns$.asObservable();
  /**
   * first monitor run for single profile
   */
  private _firstMonitorRunSubject$ = new BehaviorSubject<any>(null);
  firstMonitorRun = this._firstMonitorRunSubject$.asObservable();
  /**
   * reference for monitor active result set documents on result tab
   */
  private monitorActiveResultSetDocumentsSubject = new BehaviorSubject<Array<Patent>>(null);
  private monitorActiveResultSetPaginationSubject = new BehaviorSubject<PaginationMetadata>(null);

  /**
   * list of in process monitor run ID
   */
  private _monitorRunRequestArr: Array <number> = [];

  /**
   * get list of in process monitor result set
   */
  get monitorRunRequestArr() {
    return this._monitorRunRequestArr;
  }

  /**
   * set list of in process monitor result set
   */
  set monitorRunRequestArr(value: Array <number>) {
    this._monitorRunRequestArr = value;
  }

  /**
   * Element ID to scroll page
   */
  private _elementIdToScrollPage = '';

  /**
   * get element ID to scroll page
   */
  get elementIdToScrollPage() {
    return this._elementIdToScrollPage;
  }

  /**
   * set element ID to scroll page
   */
  set elementIdToScrollPage(value: string) {
    this._elementIdToScrollPage = value;
  }

  get monitorProfiles() {
    return this.monitorProfilesSubject.getValue();
  }

  set monitorProfiles(profiles: Array<MonitorProfile>) {
    this.monitorProfilesSubject.next(profiles);
  }

  get loading(): boolean {
    return this.monitorLoadingSubject.getValue();
  }

  set loading(state: boolean) {
    this.monitorLoadingSubject.next(state);
  }

  get monitorProfile(): MonitorProfile {
    return this.monitorProfileSubject.getValue();
  }

  set monitorProfile(profile: MonitorProfile) {
    this.monitorProfileSubject.next(profile);
  }

  /**
   * get Selected monitor run for single profile
   */
  get selectedMonitorRun() {
    return this._selectedMonitorRun$.getValue();
  }

  /**
   * get all monitor runs for single profile
   */
  get monitorRuns() {
    return this._monitorRuns$.getValue();
  }

  /**
   * get count of monitor runs for single profile
   */
  get totalMonitorRuns() {
    if (this._monitorRuns$.getValue()) {
      return this._monitorRuns$.getValue().page.total_hits;
    }
    return 0;
  }

  /**
   * get State of ML training progress
   */
  get freezeML() {
    return this._freezeML$.getValue();
  }

  /**
   * get monitor active result set documents on result tab
   */
  get resultSetDocuments(): Patent[] {
    return this.monitorActiveResultSetDocumentsSubject.getValue();
  }

  /**
   * set monitor active result set documents on result tab
   */
  setResultSetDocuments(documents: Array<Patent>) {
    this.monitorActiveResultSetDocumentsSubject.next(documents);
  }

  /**
   * get monitor active result set pagination on result tab
   */
  get resultSetPagination() {
    return this.monitorActiveResultSetPaginationSubject.getValue();
  }

  /**
   * set monitor active result set pagination on result tab
   */
  set resultSetPagination(page: PaginationMetadata) {
    this.monitorActiveResultSetPaginationSubject.next(page);
  }

  get sharedProfiles(): Array<MonitorSharedProfile> {
    return this.sharedProfilesSubject.getValue();
  }

  get sharedProfile(): MonitorSharedProfile {
    return this.sharedProfileSubject.getValue();
  }

  purgeLoading() {
    this.loading = false;
  }

  purgeMonitorProfile() {
    this.monitorProfile = null;
  }

  /**
   * Set monitor run value
   * @param nextValue Selected monitor run value to be stored
   */
  setSelectedMonitorRun(nextValue: number): void {
    this._selectedMonitorRun$.next(Number(nextValue));
  }

  /**
   * reset the selected monitor run value
   */
  resetSelectedMonitorRun(): void {
    this._selectedMonitorRun$.next(null);
  }

  /**
   * set monitor run list in store
   * @param nextValue store list og monitor run in monitor store
   */
  setMonitorRuns(nextValue: any): void {
    this._monitorRuns$.next(nextValue);
  }

  /**
   * reset the monitor runs list
   */
  resetMonitorRuns(): void {
    this._monitorRuns$.next(null);
  }

  /**
   * set State of ML training progress
   * @param nextValue State of ML training progress
   */
  setFreezeML(nextValue: boolean): void {
    this._freezeML$.next(nextValue);
  }

  /**
   * reset the State of ML training progress
   */
  resetFreezeML(): void {
    this._freezeML$.next(false);
  }

  /**
   * Set first monitor run for user
   * @param runID - ID of monitor run
   */
  setMonitorRunID(runID): void {
    this._firstMonitorRunSubject$.next(runID);
  }

  /**
   * Reset first monitor run
   */
  purgeMonitorRunID(): void {
    this._firstMonitorRunSubject$.next(null);
  }

  /**
   * Get first monitor run ID
   * @returns ID of monitor run
   */
  getMonitorRunID(): string {
    return this._firstMonitorRunSubject$.getValue();
  }

  /**
   * reset monitor active result set documents on result tab
   */
  purgeResultSetDocuments() {
    this.setResultSetDocuments([]);
  }

  /**
   * reset monitor active result set pagination on result tab
   */
  purgeResultSetPagination() {
    this.resultSetPagination = null;
  }

  setSharedProfiles(profiles: Array<MonitorSharedProfile>) {
    return this.sharedProfilesSubject.next(profiles);
  }

  setSharedProfile(profile: MonitorSharedProfile) {
    return this.sharedProfileSubject.next(profile);
  }

  clearStoredData() {
    super.clearStoredData();
    this.scrollTopPage = null;
    this.additionalExportParams = {};
    this.monitorLoadingSubject.next(false);
    this.monitorProfilesSubject.next(null);
    this.sharedProfilesSubject.next(null);
    this.sharedProfileSubject.next(null);
    this.monitorProfileSubject.next(null);
    this._selectedMonitorRun$.next(null);
    this._freezeML$.next(false);
    this._monitorRuns$.next(null);
    this._firstMonitorRunSubject$.next(null);
    this.monitorActiveResultSetDocumentsSubject.next(null);
    this.monitorActiveResultSetPaginationSubject.next(null);
    this.monitorRunRequestArr = [];
    this.elementIdToScrollPage = '';
  }

  isPublicationProfile(profile: MonitorProfile){
    return profile?.scope !== 'Families';
  }

  purgeCloneProfile(){
    this.cloneProfile = undefined;
  }

  setCloneProfile(profile: MonitorProfile){
    this.cloneProfile = profile;
  }

  getCloneProfile(): MonitorProfile{
    return this.cloneProfile;
  }

  override storeCurrentState<T extends BaseCurrentStateModel = MonitorCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData: MonitorCurrentStateModel = {
      scrollTopPage: this.scrollTopPage,
      additionalExportParams: this.additionalExportParams,
      elementIdToScrollPage: this.elementIdToScrollPage,
      selectedMonitorRun: this.selectedMonitorRun,
      monitorRunRequestArr: this.monitorRunRequestArr,
      monitorRuns: this.monitorRuns,
      resultSetPagination: this.resultSetPagination,
      firstMonitorRun: this.getMonitorRunID(),
      ...data as any
    };
    super.storeCurrentState<MonitorCurrentStateModel>(id, stateData);
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = MonitorCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<MonitorCurrentStateModel>;
      this.scrollTopPage = stateData.scrollTopPage;
      this.additionalExportParams = stateData.additionalExportParams;
      this.elementIdToScrollPage = stateData.elementIdToScrollPage;
      this.setSelectedMonitorRun(stateData.selectedMonitorRun);
      this.monitorRunRequestArr = stateData.monitorRunRequestArr;
      this.setMonitorRuns(stateData.monitorRuns);
      this.resultSetPagination = stateData.resultSetPagination;
      this.setMonitorRunID(stateData.firstMonitorRun);
    }
  }
}
