import { PaginationMetadata } from '@core/services/semantic-search/types';
import { BehaviorSubject } from 'rxjs';

export abstract class BasePaginationStoreService {

  private _pagination$ = new BehaviorSubject<PaginationMetadata>({} as PaginationMetadata);
  readonly pagination$ = this._pagination$.asObservable();
  private _nplsPagination$ = new BehaviorSubject<PaginationMetadata>({} as PaginationMetadata);

  get pagination(): PaginationMetadata {
    return this._pagination$.getValue();
  }

  set pagination(nextValue: PaginationMetadata) {
    this._pagination$.next(nextValue);
  }

  get nplsPagination(): PaginationMetadata {
    return this._nplsPagination$.getValue();
  }

  set nplsPagination(nextValue: PaginationMetadata) {
    this._nplsPagination$.next(nextValue);
  }

  pageSize: number = 25;
  startYear: number = null;
  endYear: number = null;

  clearPaginationStoredData(): void {
    this.pagination = {} as PaginationMetadata;
    this.nplsPagination = {} as PaginationMetadata;
  }
}
