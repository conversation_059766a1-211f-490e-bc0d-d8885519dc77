import { BehaviorSubject } from 'rxjs';

export abstract class BasePatentRemovalStoreService {

  private _publicationsToRemove$ = new BehaviorSubject<Array<string>>([]);
  readonly publicationsToRemove$ = this._publicationsToRemove$.asObservable();

  get publicationsToRemove() {
    return this._publicationsToRemove$.getValue();
  }

  set publicationsToRemove(nextValue: Array<string>) {
    this._publicationsToRemove$.next(nextValue);
  }

  clearPatentRemovalStoredData(): void {
    this.publicationsToRemove = [];
  }
}
