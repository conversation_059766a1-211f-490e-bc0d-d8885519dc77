import { BehaviorSubject } from 'rxjs';

export abstract class BasePatentSelectionStoreService {
  private _selectedPublications$ = new BehaviorSubject<Array<string>>([]);
  readonly selectedPublications$ = this._selectedPublications$.asObservable();
  private _selectedPatentIds$ = new BehaviorSubject<number[]>([]);
  readonly selectedPatentIds$ = this._selectedPatentIds$.asObservable();

  get selectedPublications() {
    return this._selectedPublications$.getValue();
  }

  set selectedPublications(nextValue: Array<string>) {
    this._selectedPublications$.next(nextValue);
    if (nextValue.length === 0) {
      this.selectedPatentIds = [];
    }
  }

  get selectedPatentIds(): number[] {
    return [...new Set(this._selectedPatentIds$.getValue())];
  }

  set selectedPatentIds(nextValue: number[]) {
    this._selectedPatentIds$.next(nextValue);
  }


  clearPatentSelectionStoredData(): void {
    this.selectedPublications = [];
    this.selectedPatentIds = [];
  }
}
