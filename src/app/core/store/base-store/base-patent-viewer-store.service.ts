import {BehaviorSubject} from 'rxjs';

export abstract class BasePatentViewerStoreService {
  private _patentViewerDocuments$ = new BehaviorSubject<Array<Object>>([]);
  private _patentViewerPatent$ = new BehaviorSubject<Object>(null);

  get patentViewerDocuments(): Array<Object> {
    return this._patentViewerDocuments$.getValue();
  }

  set patentViewerDocuments(nextValue: Array<Object>) {
    this._patentViewerDocuments$.next(nextValue);
  }

  get patentViewerPatent(): Object {
    return this._patentViewerPatent$.getValue();
  }

  set patentViewerPatent(nextValue: Object) {
    this._patentViewerPatent$.next(nextValue);
  }


  clearPatentViewerStoredData(): void {
    this.patentViewerDocuments = [];
    this.patentViewerPatent = null;
  }

}
