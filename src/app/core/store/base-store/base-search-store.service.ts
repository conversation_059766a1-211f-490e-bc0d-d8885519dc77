import { EventEmitter } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { intitialState, State } from '../state';

export abstract class BaseSearchStoreService {

  searchingEvent = new EventEmitter(true);
  searchingNplsEvent = new EventEmitter(true);
  newSearchEvent = new EventEmitter(true);
  private _state$ = new BehaviorSubject<State>(intitialState);
  readonly state$ = this._state$.asObservable();
  private _searchHash$ = new BehaviorSubject<string>('');
  readonly searchHash$ = this._searchHash$.asObservable();
  private _searchInput$ = new BehaviorSubject<string>('');
  private _patentNumbers$ = new BehaviorSubject<string>('');
  private _searching$ = new BehaviorSubject<boolean>(false);
  readonly searching$ = this._searching$.asObservable();
  private _search$ = new BehaviorSubject<Object>({});
  private _addNumberToSearch$ = new BehaviorSubject<Array<string>>([]);
  readonly addNumberToSearch$ = this._addNumberToSearch$.asObservable();
  private _isPublications$ = new BehaviorSubject<boolean>(false);
  readonly isPublications$ = this._isPublications$.asObservable();

  get state() {
    return this._state$.getValue();
  }

  set state(val: State) {
    this._state$.next(val);
  }

  get searchHash() {
    return this._searchHash$.getValue();
  }

  set searchHash(val: string) {
    this._searchHash$.next(val);
  }

  get searchInput(): string {
    return this._searchInput$.getValue();
  }

  set searchInput(val: string) {
    this._searchInput$.next(val);
  }

  get patentNumbers(): string {
    return this._patentNumbers$.getValue();
  }

  set patentNumbers(val: string) {
    this._patentNumbers$.next(val);
  }

  get searching(): boolean {
    return this._searching$.getValue();
  }

  set searching(nextValue: boolean) {
    this._searching$.next(nextValue);
  }

  get search(): Object {
    return this._search$.getValue();
  }

  set search(nextValue: Object) {
    this._search$.next(nextValue);
  }

  get addNumberToSearch() {
    return this._addNumberToSearch$.getValue();
  }

  set addNumberToSearch(value: Array<string>) {
    this._addNumberToSearch$.next(value);
  }

  get isPublications() {
    return this._isPublications$.getValue();
  }

  set isPublications(value: boolean) {
    this._isPublications$.next(value);
  }

  hasCitationSearchHash() {
    return this.searchHash != null && this.searchHash[0] === 'c';
  }

  hasBooleanSearchHash() {
    return this.searchHash != null && this.searchHash[0] === 'b';
  }

  hasSemanticSearchHash() {
    return this.searchHash != null && this.searchHash[0] === 's';
  }

  clearSearchStoredData(): void {
    this.state = intitialState;
    this.searchHash = '';
    this.searchInput = '';
    this.patentNumbers = '';
    this.searching = false;
    this.search = {};
    this.addNumberToSearch = [];
    this.isPublications = false;
  }
}
