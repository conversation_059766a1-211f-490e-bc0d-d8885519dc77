import { SortBySimilarityParam } from '@core/services/collection/types';
import { extractPublications } from '@core/services/semantic-search/utils';
import { BehaviorSubject } from 'rxjs';
import { SortParams } from '../state';

export abstract class BaseSortPatentTableStoreService {
  protected _originalSearchHash: string;
  private _patentTableSortParams = new BehaviorSubject<SortParams>(null);
  readonly patentTableSortParams$ = this._patentTableSortParams.asObservable();

  private _sortBySimilarity = new BehaviorSubject<SortBySimilarityParam>(null);
  readonly sortBySimilarity$ = this._sortBySimilarity.asObservable();

  get sortBySimilarity(): SortBySimilarityParam {
    return this._sortBySimilarity.value;
  }

  set sortBySimilarity(val: SortBySimilarityParam) {
    this._sortBySimilarity.next(val);
  }

  set originalSearchHash(val: string){
    this._originalSearchHash = val
  }

  get originalSearchHash(): string{
    return this._originalSearchHash;
  }

  get patentTableSort(): SortParams {
    return this._patentTableSortParams.getValue();
  }

  set patentTableSort(value: SortParams) {
    this._patentTableSortParams.next(value);
  }

  getSortBySimilarityPayload(filters: string) {
    const {
      remainingText,
      publications
    } = extractPublications(this.sortBySimilarity.publications + ' ' + this.sortBySimilarity.term);
    const payload = {
      "search_input": remainingText,
      "patent_numbers": publications,
      "search_filters": {
        'text_weighting': this.sortBySimilarity.text_weighting,
        'relevant_search_hash': this._originalSearchHash
      },
      "top_term_weights": 100
    };

    if (filters) {
      payload['search_filters']['free_text_query'] = filters;
    }

    if (this.sortBySimilarity.language?.code) {
      payload['translation'] = {
        'source_language': this.sortBySimilarity.language.code
      };
    }

    return payload;
  }

  clearSortPatentTableStoredData() {
    this._originalSearchHash = null;
    this._patentTableSortParams.next(null);
    this._sortBySimilarity.next(null);
  }
}
