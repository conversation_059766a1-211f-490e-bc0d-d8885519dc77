import { SortBySimilarityParam } from '@core/services/collection/types';
import { MatomoService } from '@core/services/matomo/matomo.service';
import { UserService } from '@core/services/user/user.service';
import { Mixin } from 'ts-mixer';
import { Injector } from '@angular/core';
import {
  BaseAdvancedFilterStoreService,
  BaseApplicantStoreService,
  BaseBackToSearchStoreService,
  BaseChartCalculationService,
  BaseChartDashboardStoreService,
  BaseChartExportStoreService,
  BaseChartFilterStoreService,
  BaseChartLayoutStoreService,
  BaseColumnSelectionStoreService,
  BaseFilterStoreService,
  BaseLayoutStoreService,
  BasePaginationStoreService,
  BasePatentRemovalStoreService,
  BasePatentSelectionStoreService,
  BasePatentViewerStoreService,
  BaseSavedSearchStoreService,
  BaseSearchStoreService,
  BaseSortPatentTableStoreService
} from '.';
import { BooleanSearchService, PaginationMetadata } from '@core/services';
import { BaseCurrentStateModel, BaseCurrentStateStoreService } from '../base-store/base-current-state-store.service';

export interface SearchDataModel {
  documents: any[];
  pagination: PaginationMetadata;
  search: any;
  selectedColumnsToShow: any[];
  searchHash: string;
  isPublications: boolean;
}

class BaseGroupPatentSearchStoreService extends Mixin(
  BaseBackToSearchStoreService, BaseColumnSelectionStoreService, BaseApplicantStoreService,
  BasePatentRemovalStoreService, BasePatentSelectionStoreService, BasePatentViewerStoreService,
  BaseSortPatentTableStoreService, BasePaginationStoreService, BaseSavedSearchStoreService, BaseSearchStoreService
) {
  private readonly SEARCH_DATA_STORAGE_KEY = 'STORED_SEARCH_DATA_TMP';

  clearGroupPatentSearchStoredData() {
    this.clearBackToSearchStoredData();
    this.clearColumnSelectionStoredData();
    this.clearApplicantStoredData();
    this.clearPatentRemovalStoredData();
    this.clearPatentSelectionStoredData();
    this.clearPatentViewerStoredData();
    this.clearSortPatentTableStoredData();
    this.clearPaginationStoredData();
    this.clearSavedSearchStoredData();
    this.clearSearchStoredData();
    this.removeSearchData();
  }

  storeSearchData(documents: any[], pagination: PaginationMetadata) {
    const searchData = {
      documents: documents,
      pagination: pagination,
      search: this.search,
      selectedColumnsToShow: this.selectedColumnsToShow,
      searchHash: this.searchHash,
      isPublications: this.isPublications
    } as SearchDataModel;
    localStorage.setItem(this.SEARCH_DATA_STORAGE_KEY, JSON.stringify(searchData));
  }

  getSearchData(): SearchDataModel {
    const data = localStorage.getItem(this.SEARCH_DATA_STORAGE_KEY);
    return data ? JSON.parse(data) as SearchDataModel : null;
  }

  removeSearchData() {
    try {
      localStorage.removeItem(this.SEARCH_DATA_STORAGE_KEY);
    } catch (e) {
      console.warn('Error removing search data from local storage', e);
    }
  }

  restoreSearchData(callback = null): boolean {
    const data = this.getSearchData();
    if (data) {
      this.pagination = data.pagination;
      this.search = data.search;
      this.searchHash = data.searchHash;
      this.isPublications = data.isPublications;
      this.selectedColumnsToShow = data.selectedColumnsToShow;
      if (callback) {
        callback(data);
      }
      this.removeSearchData();
      return true;
    }
    return false;
  }
}

class BaseGroupPatentFilterStoreService extends Mixin(
  BaseFilterStoreService, BaseAdvancedFilterStoreService
) {
  clearGroupPatentFilterStoredData() {
    this.clearFilterStoredData();
    this.clearAdvancedFilterStoredData();
  }
}


class BaseGroupChartStoreService extends Mixin(
  BaseChartExportStoreService, BaseChartCalculationService, BaseChartDashboardStoreService, BaseChartLayoutStoreService,
  BaseChartFilterStoreService
) {
  clearGroupChartStoredData() {
    this.clearChartCalculationStoredData();
    this.clearChartDashboardStoredData();
    this.clearChartLayoutStoredData();
    this.clearChartFilterStoredData();
  }
}


class BaseGroupOthersStoreService extends Mixin(
  BaseLayoutStoreService
) {
  clearGroupOthersStoredData() {
    this.clearLayoutStoredData();
  }
}


/**
 * We CAN NOT OVERRIDE the methods from base classes when using Mixin
 * Unfortunately, Mixin only supports upto 10 classes. So we have split them into groups of classes.
 */
export class BaseStoreService extends Mixin(
  BaseGroupPatentSearchStoreService, BaseGroupPatentFilterStoreService, BaseGroupChartStoreService,
  BaseGroupOthersStoreService, BaseCurrentStateStoreService
) {
  readonly storeName: string;

  protected userService: UserService;
  private matomoService: MatomoService;
  private _typedPublications: Array<string> = [];
  private _docdbFamilyIdFromPatentViewer: string;
  private scrollToPatentIntervalId: any;
  private scrollToPatentRetries: number = 0;

  private _expandSearchInput = '';
  private _originalSearchInput = '';
  private _executeExpandSearchInput = false;
  constructor(
    protected injector: Injector
  ) {
    super();
    this.userService = injector.get(UserService);
    this.matomoService = injector.get(MatomoService);
  }

  get typedPublications(): Array<string> {
    return this._typedPublications;
  }

  set typedPublications(value: Array<string>) {
    this._typedPublications = value;
  }

  get docdbFamilyIdFromPatentViewer(): string {
    return this._docdbFamilyIdFromPatentViewer;
  }
  set docdbFamilyIdFromPatentViewer(value: string) {
    this._docdbFamilyIdFromPatentViewer = value;
  }

  get expandSearchInput(): string {
    return this._expandSearchInput;
  }
  set expandSearchInput(val: string) {
    this._expandSearchInput = val;
  }

  get originalSearchInput(): string {
    return this._originalSearchInput;
  }
  set originalSearchInput(val: string) {
    this._originalSearchInput = val;
  }

  get executeExpandSearchInput(): boolean {
    return this._executeExpandSearchInput;
  }
  set executeExpandSearchInput(val: boolean) {
    this._executeExpandSearchInput = val;
  }

  isBooleanSearchStore(): boolean {
    return this.storeName === 'booleanSearch';
  }

  isCitationSearchStore(): boolean {
    return this.storeName === 'citationSearch';
  }

  isLandscapeStore(): boolean {
    return this.storeName === 'landscape';
  }

  isMonitorLegalStore(): boolean {
    return this.storeName === 'monitor_legal';
  }

  isMonitorSearch(): boolean {
    return this.storeName === 'monitor';
  }

  isNplSearchStore(): boolean {
    return this.storeName === 'nplSearch';
  }

  isOpenedDocumentSearch(): boolean {
    return this.storeName === 'opened_document';
  }

  isSemanticSearchStore(): boolean {
    return this.storeName === 'semanticSearch';
  }

  isCollectionStore(): boolean {
    return this.storeName === 'collection';
  }

  isSearchStore(): boolean {
    return this.isSemanticSearchStore() || this.isBooleanSearchStore() || this.isCitationSearchStore() || this.isNplSearchStore();
  }

  setFilters(val: any, emitEvent = true, skipMatomoEvent = false) {
    if (this.userService.isFreeUser()) return;
    if (!skipMatomoEvent) {
      this.matomoEvent(val);
    }
    super.filters = val;

    if (emitEvent) {
      super.emitFilters();
    }
  }

  setSortBySimilarity(v: SortBySimilarityParam) {
    if (v) {
      if (!super.originalSearchHash) {
        super.originalSearchHash = this.searchHash;
      }
    } else if (super.sortBySimilarity) {
      super.originalSearchHash = undefined;
    }
    super.sortBySimilarity = v;
  }

  getUnderscoredStoreName(): string {
    return this.storeName.replace(/[A-Z]/g, c => `_${c.toLowerCase()}`);
  }

  getLowerCasedStoreName(): string {
    return this.storeName.replace(/[A-Z]/g, c => ` ${c.toLowerCase()}`);
  }

  getCapitalizedStoreName(): string {
    const storeName = this.getLowerCasedStoreName();
    return storeName.charAt(0).toUpperCase() + storeName.slice(1);
  }

  getDownloadChartsName(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT', defaultName: string = null, namePrefix: string = null): string {
    if (defaultName) {
      return defaultName;
    }

    let downloadName = namePrefix ? namePrefix : this.getCapitalizedStoreName();

    if (this.activeChartCategory) {
      downloadName += ` - ${this.getActiveChartCategoryName()}`;
    }

    if (!['PDF', 'PRINT'].includes(type)) {
      downloadName += ` (${type.toLowerCase()})`;
    }

    return downloadName;
  }


  getAppliedFiltersQuery(): string {
    const filters = [
      this.publicationsToRemove.length ? `NOT (RAW_PUBLICATION_NUMBER=(${this.publicationsToRemove.join(' OR ')}))` : null,
      this.getChartFilterQuery(),
      this.advancedFilterAppliedQuery,
      this.advancedFilterAppliedAdvancedQuery,
      this.getResultTableFilterQuery()
    ].filter((q) => q && q.trim().length > 0);

    if (filters.length > 0) {
      return filters.join(' AND ');
    }
  }

  protected matomoEvent(newFilters: any[]) {
    if (!newFilters.length || newFilters.length < super.filters.length) {
      return;
    }
    const newFilter = newFilters[newFilters.length - 1];

    if (newFilter['type'] === 'chart' &&
      (!super.filters.length || newFilter['value'] !== super.filters[super.filters.length - 1]['value'])) {
      this.matomoService.visualAnalysisChartFilter();
    }
  }

  scrollToPatentFromPatentViewer() {
    this.clearScrollToPatentValues();

    this.scrollToPatentIntervalId = setInterval(() => {
      const patentRow = document.getElementById(this.docdbFamilyIdFromPatentViewer);
      if (patentRow) {
        clearInterval(this.scrollToPatentIntervalId);

        const top = patentRow.getBoundingClientRect().top - window.innerHeight / 4;
        window.scrollTo(0, top);
        this.docdbFamilyIdFromPatentViewer = null;

        const openedPatentClass = 'opened-patent';
        patentRow.classList.add(openedPatentClass);
        setTimeout(() => {
          patentRow.classList.remove(openedPatentClass);
        }, 2000);
      }

      this.scrollToPatentRetries++;

      if (this.scrollToPatentRetries > 100) {
        clearInterval(this.scrollToPatentIntervalId);
      }
    }, 100);
  }

  private clearScrollToPatentValues() {
    if (this.scrollToPatentIntervalId) {
      clearInterval(this.scrollToPatentIntervalId);
    }
    this.scrollToPatentIntervalId = null;
    this.scrollToPatentRetries = 0;
  }

  getBaseCurrentState(): BaseCurrentStateModel {
    return {
      search: this.search,
      searchHash: this.searchHash,
      patentNumbers: this.patentNumbers,
      isPublications: this.isPublications,
      addNumberToSearch: this.addNumberToSearch,
      shouldCalculateCharts: this.shouldCalculateCharts,
      selectedPublications: this.selectedPublications,
      selectedPatentIds: this.selectedPatentIds,
      pagination: this.pagination,
      pageSize: this.pageSize,
      patentListViewMode: this.patentListViewMode,
      patentTableSort: this.patentTableSort,
      docdbFamilyIdFromPatentViewer: this.docdbFamilyIdFromPatentViewer,
      filtersOperator: this.filtersOperator,
      filters: this.filters,
      extraFilters: this.extraFilters,
      advancedFilterBooleanClauses: BooleanSearchService.clausesToJsonQuery(this.advancedFilterBooleanClauses),
      advancedFilterAdvancedQuery: this.advancedFilterAdvancedQuery,
      advancedFilterAppliedBooleanClauses: BooleanSearchService.clausesToJsonQuery(this.advancedFilterAppliedBooleanClauses),
      advancedFilterAppliedAdvancedQuery: this.advancedFilterAppliedAdvancedQuery,
      advancedFilterAppliedQuery: this.advancedFilterAppliedQuery,
      isAdvancedFilterAdvancedMode: this.isAdvancedFilterAdvancedMode,
      sortBySimilarity: this.sortBySimilarity,
      originalSearchHash: this.originalSearchHash,
      chartDashboardType: this.chartDashboardType,
      activeChartCategory: this.activeChartCategory,
      chartSelectedValues: this.chartSelectedValues,
      chartFilterDisabled: this.chartFilterDisabled,
      expandSearchInput: this.expandSearchInput,
      originalSearchInput: this.originalSearchInput,
      executeExpandSearchInput: this.executeExpandSearchInput,
      typedPublications: this.typedPublications,
      singleChartColumn: this.singleChartColumn
    } as BaseCurrentStateModel;
  }

  restoreBaseCurrentState(data: BaseCurrentStateModel) {
    this.search = data.search;
    this.searchHash = data.searchHash;
    this.patentNumbers = data.patentNumbers;
    this.isPublications = data.isPublications;
    this.addNumberToSearch = data.addNumberToSearch;
    this.shouldCalculateCharts = data.shouldCalculateCharts;
    this.selectedPublications = data.selectedPublications;
    this.selectedPatentIds = data.selectedPatentIds;
    this.pagination = data.pagination;
    this.pageSize = data.pageSize;
    this.patentListViewMode = data.patentListViewMode;
    this.patentTableSort = data.patentTableSort;
    this.docdbFamilyIdFromPatentViewer = data.docdbFamilyIdFromPatentViewer;
    this.filtersOperator = data.filtersOperator;
    this.setFilters(data.filters, true, true)
    this.extraFilters = data.extraFilters;
    this.advancedFilterBooleanClauses = BooleanSearchService.makeClausesFromSearchFields(data.advancedFilterBooleanClauses);
    this.advancedFilterAdvancedQuery = data.advancedFilterAdvancedQuery;
    this.advancedFilterAppliedBooleanClauses = BooleanSearchService.makeClausesFromSearchFields(data.advancedFilterAppliedBooleanClauses);
    this.advancedFilterAppliedAdvancedQuery = data.advancedFilterAppliedAdvancedQuery;
    this.advancedFilterAppliedQuery = data.advancedFilterAppliedQuery;
    this.isAdvancedFilterAdvancedMode = data.isAdvancedFilterAdvancedMode;
    this.sortBySimilarity = data.sortBySimilarity;
    this.originalSearchHash = data.originalSearchHash;
    this.chartDashboardType = data.chartDashboardType;
    this.activeChartCategory = data.activeChartCategory;
    this.chartSelectedValues = data.chartSelectedValues;
    this.chartFilterDisabled = data.chartFilterDisabled;
    this.expandSearchInput = data.expandSearchInput;
    this.executeExpandSearchInput = data.executeExpandSearchInput;
    this.originalSearchInput = data.originalSearchInput;
    this.typedPublications = data.typedPublications;
    this.singleChartColumn = data.singleChartColumn;
  }

  clearStoredData(): void {
    this.typedPublications = [];
    this.docdbFamilyIdFromPatentViewer = null;
    this.clearScrollToPatentValues();

    this.clearGroupOthersStoredData();
    this.clearGroupChartStoredData();
    this.clearGroupPatentFilterStoredData();
    this.clearGroupPatentSearchStoredData();
    this.removeAllCurrentStates();
  }
}
