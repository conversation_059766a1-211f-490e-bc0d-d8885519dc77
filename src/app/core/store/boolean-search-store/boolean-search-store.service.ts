import { Injectable, Injector } from '@angular/core';
import { TreeSelectionItem, Clause } from '@core/services/boolean-search/types';
import { BehaviorSubject } from 'rxjs';
import { BaseStoreService } from '../base-store';
import { State } from '@core/store';
import { BooleanSearchService } from '@core/services';
import { BaseCurrentStateModel } from '../base-store/base-current-state-store.service';

export interface BooleanSearchCurrentStateModel extends BaseCurrentStateModel {
  state: State;
  booleanSearchClauses: any;
  searchInput: string;
  selectedOwnersOfClauses: TreeSelectionItem[];
  sortBySimilaritySearchHash: string;
  originalBooleanSearchHash: string;
}

@Injectable({
  providedIn: 'root'
})
export class BooleanSearchStoreService extends BaseStoreService {
  readonly storeName = 'booleanSearch';
  private _booleanSearchClauses$ = new BehaviorSubject<Array<Clause>>([]);

  sortBySimilaritySearchHash: string = null;
  originalBooleanSearchHash: string = null;

  selectedOwnersOfClauses: TreeSelectionItem[] = [];

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get booleanSearchClauses(): Array<Clause> {
    return this._booleanSearchClauses$.getValue();
  }

  set booleanSearchClauses(nextValue: Array<Clause>) {
    this._booleanSearchClauses$.next(nextValue);
  }

  override storeCurrentState<T extends BaseCurrentStateModel = BooleanSearchCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData = {
      state: this.state,
      booleanSearchClauses: BooleanSearchService.clausesToJsonQuery(this.booleanSearchClauses),
      searchInput: this.searchInput,
      selectedOwnersOfClauses: this.selectedOwnersOfClauses,
      sortBySimilaritySearchHash: this.sortBySimilaritySearchHash,
      originalBooleanSearchHash: this.originalBooleanSearchHash,
      ...data as any
    };
    super.storeCurrentState<BooleanSearchCurrentStateModel>(id, stateData);
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = BooleanSearchCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<BooleanSearchCurrentStateModel>;
      this.state = stateData.state;
      this.booleanSearchClauses = BooleanSearchService.makeClausesFromSearchFields(stateData.booleanSearchClauses);
      this.searchInput = stateData.searchInput;
      this.selectedOwnersOfClauses = stateData.selectedOwnersOfClauses;
      this.sortBySimilaritySearchHash = stateData.sortBySimilaritySearchHash;
      this.originalBooleanSearchHash = stateData.originalBooleanSearchHash;
    }
  }

  clearStoredData() {
    super.clearStoredData();
    this.booleanSearchClauses = [];
    this.originalBooleanSearchHash = null;
    this.sortBySimilaritySearchHash = null;
  }
}
