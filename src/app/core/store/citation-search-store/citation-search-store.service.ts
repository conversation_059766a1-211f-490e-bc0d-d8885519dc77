import { Injectable, Injector } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { BaseStoreService } from '../base-store';
import { BaseCurrentStateModel } from '@core/store/base-store/base-current-state-store.service';
import { BooleanSearchCurrentStateModel, State } from '@core/store';
import { BooleanSearchService, PaginationMetadata, TreeSelectionItem } from '@core/services';

export interface CitationSearchCurrentStateModel extends BaseCurrentStateModel {
  citationLevel: string;
  citationDirection: string;
  nplsPagination: PaginationMetadata;
}

@Injectable({
  providedIn: 'root'
})
export class CitationSearchStoreService extends BaseStoreService {
  readonly storeName = 'citationSearch';
  private _citationLevel$ = new BehaviorSubject<string>(null);
  private _citationDirection$ = new BehaviorSubject<string>(null);

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }


  get citationLevel(): string {
    return this._citationLevel$.getValue();
  }

  set citationLevel(nextValue: string) {
    this._citationLevel$.next(nextValue);
  }

  get citationDirection(): string {
    return this._citationDirection$.getValue();
  }

  set citationDirection(nextValue: string) {
    this._citationDirection$.next(nextValue);
  }

  clearStoredData() {
    super.clearStoredData();
    this.citationLevel = null;
    this.citationDirection = null;
  }

  override storeCurrentState<T extends BaseCurrentStateModel = CitationSearchCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData = {
      citationLevel: this.citationLevel,
      citationDirection: this.citationDirection,
      nplsPagination: this.nplsPagination,
      ...data as any
    };
    super.storeCurrentState<CitationSearchCurrentStateModel>(id, stateData);
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = CitationSearchCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<CitationSearchCurrentStateModel>;
      this.citationLevel = stateData.citationLevel;
      this.citationDirection = stateData.citationDirection;
      this.nplsPagination = stateData.nplsPagination;
    }
  }
}
