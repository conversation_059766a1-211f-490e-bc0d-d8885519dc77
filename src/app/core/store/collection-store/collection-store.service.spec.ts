import { TestBed } from '@angular/core/testing';

import { CollectionStoreService } from './collection-store.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { BooleanHighlightPipe } from '@core/pipes/boolean-highlight/boolean-highlight.pipe';
import { DateFormatPipe } from '@core/pipes/date-format/date-format.pipe';

describe('CollectionStoreService', () => {
  let service: CollectionStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [ BooleanHighlightPipe, DateFormatPipe, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    });
    service = TestBed.inject(CollectionStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
