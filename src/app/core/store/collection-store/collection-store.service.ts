import { Injectable, Injector } from '@angular/core';
import { BaseStoreService } from '../base-store';
import { BehaviorSubject } from 'rxjs';
import { Collection, Folder } from '@core/services';
import { DateFormatPipe } from '@core/pipes/date-format/date-format.pipe';
import { BooleanHighlightPipe } from '@core/pipes/boolean-highlight/boolean-highlight.pipe';
import { BaseCurrentStateModel } from '../base-store/base-current-state-store.service';

export interface CollectionCurrentStateModel extends BaseCurrentStateModel {
  collectionActivePage: number;
  filteredSources: any[];
}

@Injectable({
  providedIn: 'root'
})
export class CollectionStoreService extends BaseStoreService {
  readonly storeName = 'collection';
  /**
   * reference for collections active tab
   */
  collectionsTab: string;
  currentCollectionsViewUrl: string;
  /**
   * reference for selected patents for a collection
   */
  private currentCollectionIdSubject = new BehaviorSubject<number>(null);
  readonly currentCollectionId = this.currentCollectionIdSubject.asObservable();
  private refreshCollectionsSubject = new BehaviorSubject<boolean>(false);
  readonly refreshCollections = this.refreshCollectionsSubject.asObservable();
  private refreshFoldersSubject = new BehaviorSubject<boolean>(false);
  readonly refreshFolders = this.refreshFoldersSubject.asObservable();
  /**
   * reference for collections list
   */
  private collectionsSubject = new BehaviorSubject<Array<Collection>>([]);
  private monitorResultCollectionsSubject = new BehaviorSubject<Array<Collection>>([]);
  private foldersSubject = new BehaviorSubject<Array<Folder>>([]);
  /**
   * reference for collections active profile
   */
  private collectionsLoadingSubject = new BehaviorSubject <boolean>(false);
  /**
   * reference for active single collection
   */
  private collectionSubject = new BehaviorSubject<Collection>(null);
  /**
   * reference for collections active profile documents
   */
  private collectionDocumentsSubject = new BehaviorSubject<Array<Object>>(null);
  /**
   * reference for collections active page number
   */
  private collectionActivePageSubject = new BehaviorSubject <number>(1);
  private saveToCollectionSuccessSubject = new BehaviorSubject<string>(null);
  readonly saveToCollectionSuccess = this.saveToCollectionSuccessSubject.asObservable();


  private filteredSourcesSubject = new BehaviorSubject<any[]>([]);
  readonly filteredSources$ = this.filteredSourcesSubject.asObservable();

  private dateFormatPipe: DateFormatPipe;
  private booleanHighlightPipe: BooleanHighlightPipe;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
    this.dateFormatPipe = injector.get(DateFormatPipe);
    this.booleanHighlightPipe = injector.get(BooleanHighlightPipe);
  }

  get collections(): Array<Collection> {
    return this.collectionsSubject.getValue();
  }

  set collections(val: Array<Collection>) {
    this.collectionsSubject.next(val);
  }

  get folders(): Array<Folder> {
    return this.foldersSubject.getValue();
  }

  set folders(val: Array<Folder>) {
    this.foldersSubject.next(val);
  }

  get monitorResultCollections(): Array<Collection> {
    return this.monitorResultCollectionsSubject.getValue();
  }

  set monitorResultCollections(val: Array<Collection>) {
    this.monitorResultCollectionsSubject.next(val);
  }

  get collectionsLoading(): boolean {
    return this.collectionsLoadingSubject.getValue();
  }

  set collectionsLoading(state: boolean) {
    this.collectionsLoadingSubject.next(state);
  }

  get collection() {
    return this.collectionSubject.getValue();
  }

  set collection(profile: Collection) {
    this.collectionSubject.next(profile);
  }

  /**
   * get information of active collection page documents
   */
  get collectionDocuments() {
    return this.collectionDocumentsSubject.getValue();
  }

  /**
   * store information of active collection page documents
   */
  set collectionDocuments(documents: Array<Object>) {
    this.collectionDocumentsSubject.next(documents);
  }

  /**
   * get information regarding the pagination of collection documents table
   */
  get collectionActivePage() {
    return this.collectionActivePageSubject.getValue();
  }

  /**
   * store information regarding the pagination of collection documents table
   */
  set collectionActivePage(page: number) {
    this.collectionActivePageSubject.next(page);
  }

  get filteredSources() {
    return this.filteredSourcesSubject.getValue();
  }

  set filteredSources(val: any[]) {
    this.filteredSourcesSubject.next(val);
  }

  getAppliedCollectionSourceTypeFilter() {
    return this.filters.find(f => f.collectionSource === 'sourceType');
  }

  getAppliedCollectionSourceUserFilter() {
    return this.filters.find(f => f.collectionSource === 'sourceUser');
  }

  purgeCollections() {
    this.collections = [];
  }

  purgeCollectionsLoading() {
    this.collectionsLoading = false;
  }

  purgeCollection() {
    this.collection = null;
  }

  /**
   * reset information of active collection page documents
   */
  purgeCollectionDocuments() {
    this.collectionDocuments = null;
  }

  /**
   * reset information regarding the pagination of collection documents table
   */
  purgeCollectionActivePage() {
    this.collectionActivePage = 1;
  }

  /**
   * resetCollection
   */
  resetCollection() {
    this.purgeCollectionActivePage();
    this.purgeCollectionDocuments();
    this.purgeCollection();
  }

  setCurrentCollectionId(val: number) {
    this.currentCollectionIdSubject.next(val);
  }

  setRefreshCollections(val: boolean) {
    this.refreshCollectionsSubject.next(val);
  }

  setRefreshFolders(val: boolean) {
    this.refreshFoldersSubject.next(val);
  }

  setSaveToCollectionSuccess(val: string) {
    this.saveToCollectionSuccessSubject.next(val);
  }

  getSaveToCollectionSuccess(): string {
    return this.saveToCollectionSuccessSubject.value;
  }

  clearStoredData() {
    super.clearStoredData();
    this.collectionsTab = null;
    this.currentCollectionsViewUrl = null;
    this.collectionsSubject.next([]);
    this.foldersSubject.next([]);
    this.collectionsLoadingSubject.next(false);
    this.collectionSubject.next(null);
    this.collectionDocumentsSubject.next(null);
    this.collectionActivePageSubject.next(1);
    this.saveToCollectionSuccessSubject.next(null);
    this.currentCollectionIdSubject.next(null);
    this.refreshCollectionsSubject.next(false);
    this.refreshFoldersSubject.next(false);
    this.monitorResultCollectionsSubject.next([]);
  }

  getSourceDescription(source): string{
    if(source?.run_type){
      return `<div class="statement p-b-spacing-sm">${source?.profile_name}</div><div>Reported period: ${this.dateFormatPipe.transform(source.name)}</div>`;
    } else if(source?.search_input || source?.patent_numbers) {
      if(source?.search_type === 'SEMANTIC'){
        let output = ``;
        if(source?.search_input?.length>0){
          output += `<div class="ellipsis-text-6">${source?.search_input}</div>`;
        }
        if(source?.patent_numbers?.length >0){
          output += `<div class="source-query-patents">`;
          if(source?.search_input?.length>0){
            output += `<span class="statement">AND</span>`;
          }
          source?.patent_numbers.forEach(p => {
            output += `<span class="tag-label-secondary tag-label-secondary-outline tag-label-small">${p}</span>`;
          });
          output += `</div>`;
        }
        return output;
      } else if(source?.search_type === 'BOOLEAN'){
        return `<div class="ellipsis-text-6">${this.booleanHighlightPipe.transform(source?.search_input, 'statement')}</div>`;
      } else if(source?.search_type === 'CITATION'){
          let output = '';
          if(source?.patent_numbers?.length >0){
            output += `<div class="source-query-patents">`;
            source?.patent_numbers.forEach(p => {
              output += `<span class="tag-label-secondary tag-label-secondary-outline tag-label-small">${p}</span>`;
            });
            output += `</div>`;
          }
        return output;
      }
      return `unknown search type ${source?.search_type}`;
    }
    return ''
  }

  override storeCurrentState<T extends BaseCurrentStateModel = CollectionCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData: CollectionCurrentStateModel = {
      collectionActivePage: this.collectionActivePage,
      filteredSources: this.filteredSources,
      ...data as any
    };
    super.storeCurrentState<CollectionCurrentStateModel>(id, stateData);
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = CollectionCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<CollectionCurrentStateModel>;
      this.collectionActivePage = stateData.collectionActivePage;
      this.filteredSources = stateData.filteredSources;
    }
  }
}
