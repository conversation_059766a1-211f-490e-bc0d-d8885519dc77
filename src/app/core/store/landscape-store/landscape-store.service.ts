import { Injectable, Injector } from '@angular/core';
import { LandscapeProfile, Patent } from '@core/models';
import { BehaviorSubject } from 'rxjs';
import { BaseStoreService } from '../base-store';
import { BaseCurrentStateModel } from '@core/store/base-store/base-current-state-store.service';

export interface LandscapeCurrentStateModel extends BaseCurrentStateModel {
  activePage: number;
  startYear: number;
  endYear: number;
}

@Injectable({
  providedIn: 'root'
})
export class LandscapeStoreService extends BaseStoreService {
  readonly storeName = 'landscape';
  isGreenReport = false;
  /**
   * reference for landscape profile list
   */
  private landscapeProfilesSubject = new BehaviorSubject<Array<LandscapeProfile>>([]);
  /**
   * reference for shared landscape profile list
   */
  private sharedLandscapeProfilesSubject = new BehaviorSubject<Array<LandscapeProfile>>([]);
  /**
   * reference for landscape active profile
   */
  private landscapeLoadingSubject = new BehaviorSubject <boolean>(false);
  /**
   * reference for landscape active profile
   */
  private landscapeProfileSubject = new BehaviorSubject<LandscapeProfile>(new LandscapeProfile);
  /**
   * reference for landscape active profile documents
   */
  private landscapeProfileDocumentsSubject = new BehaviorSubject<Array<Patent>>(null);
  /**
   * reference for landscape active page number
   */
  private landscapeActivePageSubject = new BehaviorSubject <number>(1);

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get landscapeProfiles(): Array<LandscapeProfile> {
    return this.landscapeProfilesSubject.getValue();
  }

  set landscapeProfiles(profiles: Array<LandscapeProfile>) {
    this.landscapeProfilesSubject.next(profiles);
  }

  get sharedLandscapeProfiles() {
    return this.sharedLandscapeProfilesSubject.getValue();
  }

  set sharedLandscapeProfiles(profiles: Array<LandscapeProfile>) {
    this.sharedLandscapeProfilesSubject.next(profiles);
  }

  get landscapeLoading(): boolean {
    return this.landscapeLoadingSubject.getValue();
  }

  set landscapeLoading(state: boolean) {
    this.landscapeLoadingSubject.next(state);
  }

  get landscapeProfile() {
    return this.landscapeProfileSubject.getValue();
  }

  set landscapeProfile(profile: LandscapeProfile) {
    this.landscapeProfileSubject.next(profile);
  }

  get landscapeDocuments() {
    return this.landscapeProfileDocumentsSubject.getValue();
  }

  set landscapeDocuments(documents: Array<Patent>) {
    this.landscapeProfileDocumentsSubject.next(documents);
  }

  get activePage() {
    return this.landscapeActivePageSubject.getValue();
  }

  set activePage(page: number) {
    this.landscapeActivePageSubject.next(page);
  }

  purgeLandscapeProfiles() {
    this.landscapeProfiles = [];
  }

  purgeSharedLandscapeProfiles() {
    this.sharedLandscapeProfiles = [];
  }

  purgeLandscapeLoading() {
    this.landscapeLoading = false;
  }

  purgeLandscapeProfile() {
    this.landscapeProfile = new LandscapeProfile();
  }

  purgeLandscapeDocuments() {
    this.landscapeDocuments = null;
  }

  purgeLandscapeActivePage() {
    this.activePage = 1;
  }

  clearStoredData() {
    super.clearStoredData();
    this.isGreenReport = false;
    /**
     * Reset all BehaviorSubjects
     */
    this.landscapeProfiles = [];
    this.sharedLandscapeProfiles = [];
    this.landscapeLoading = false;
    this.landscapeProfile = new LandscapeProfile();
    this.landscapeDocuments = null;
    this.activePage = 1;
  }

  override storeCurrentState<T extends BaseCurrentStateModel = LandscapeCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData: LandscapeCurrentStateModel = {
      activePage: this.activePage,
      startYear: this.startYear,
      endYear: this.endYear,
      ...data as any
    };
    super.storeCurrentState<LandscapeCurrentStateModel>(id, stateData);
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = LandscapeCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<LandscapeCurrentStateModel>;
      this.activePage = stateData.activePage;
      this.startYear = stateData.startYear;
      this.endYear = stateData.endYear;
    }
  }
}
