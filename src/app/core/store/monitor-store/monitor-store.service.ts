import { Injectable, Injector } from '@angular/core';
import { BaseMonitorStoreService } from '../base-store';

@Injectable({
  providedIn: 'root'
})
export class MonitorStoreService extends BaseMonitorStoreService {

  readonly storeName = 'monitor';

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  trackablePublications: Array<string> = [];
  checkedPublications: Array<string> = [];
  untrackablePublications: Object = {};
  alreadyTrackedPublications: Object = {};
  isLegalStatusReady: boolean;

  resetPatentLegalStatusTracking(){
    this.trackablePublications = [];
    this.checkedPublications = [];
    this.untrackablePublications = {};
    this.alreadyTrackedPublications = {};
    this.isLegalStatusReady = false;
  }

}
