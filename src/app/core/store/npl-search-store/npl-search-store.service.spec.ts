import { TestBed } from '@angular/core/testing';

import { NplSearchStoreService } from './npl-search-store.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('NplSearchStoreService', () => {
  let service: NplSearchStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ], providers: [ provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    });
    service = TestBed.inject(NplSearchStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
