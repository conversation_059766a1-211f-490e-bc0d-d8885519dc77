import { TestBed } from '@angular/core/testing';

import { OpenedDocumentStoreService } from './opened-document-store.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('OpenedDocumentStoreService', () => {
  let service: OpenedDocumentStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ], providers: [ provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    });
    service = TestBed.inject(OpenedDocumentStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
