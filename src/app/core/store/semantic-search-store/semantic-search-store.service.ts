import { Injectable, Injector } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { BaseStoreService } from '../base-store';
import { State } from '@core/store';
import { BaseCurrentStateModel } from '../base-store/base-current-state-store.service';

export interface SemanticSearchCurrentStateModel extends BaseCurrentStateModel {
  state: State;
  searchInput: string;
  weighting: number;
  textWeighting: number;
  altSearchDocumentIds: number[];
}

@Injectable({
  providedIn: 'root'
})
export class SemanticSearchStoreService extends BaseStoreService {
  readonly storeName = 'semanticSearch';
  private _weighting$ = new BehaviorSubject<number>(4);
  private _textWeighting$ = new BehaviorSubject<number>(1.0);


  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  private _altSearchDocumentIds: Array<number> = [];

  get altSearchDocumentIds(): Array<number> {
    return this._altSearchDocumentIds;
  }

  set altSearchDocumentIds(value: Array<number>) {
    this._altSearchDocumentIds = value;
  }

  get weighting(): number {
    return this._weighting$.getValue();
  }

  set weighting(val: number) {
    this._weighting$.next(val);
  }

  get textWeighting(): number {
    return this._textWeighting$.getValue();
  }

  set textWeighting(val: number) {
    this._textWeighting$.next(val);
  }

  override storeCurrentState<T extends BaseCurrentStateModel = SemanticSearchCurrentStateModel>(id: string, data: Partial<T> = {} as Partial<T>) {
    const stateData: SemanticSearchCurrentStateModel = {
      state: this.state,
      searchInput: this.searchInput,
      weighting: this.weighting,
      textWeighting: this.textWeighting,
      altSearchDocumentIds: this.altSearchDocumentIds,
      ...data as any
    };
    super.storeCurrentState<SemanticSearchCurrentStateModel>(id, stateData);
  }

  clearStoredData() {
    super.clearStoredData();
    this.weighting = 4;
    this.textWeighting = 1.0;
    this.altSearchDocumentIds = [];
    this.originalSearchInput = '';
  }

  protected override _restoreExtendedCurrentState<T extends BaseCurrentStateModel = SemanticSearchCurrentStateModel>(data: Partial<T>) {
    if (data) {
      const stateData = data as Partial<SemanticSearchCurrentStateModel>;
      this.state = stateData.state;
      this.searchInput = stateData.searchInput;
      this.weighting = stateData.weighting;
      this.textWeighting = stateData.textWeighting;
      this.altSearchDocumentIds = stateData.altSearchDocumentIds;
    }
  }
}
