import { TestBed } from '@angular/core/testing';

import { StoreDataManagerService } from './store-data-manager.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import {
  BooleanHighlightPipe,
  BypassSecurityPipe,
  ChartsService,
  CountStatPipe, DateFormatPipe, HumanReadableNumberPipe, InterpolateColorsPipe, PluralizePipe,
  SafeHtmlPipe,
  TagParserPipe, TimeReadablePipe,
  TruncatePipe, UsersTitleTextPipe,
  UserTitlePipe
} from '@core';
import { Router, RouterEvent } from '@angular/router';
import { ReplaySubject } from 'rxjs';

describe('StoreDataManagerService', () => {
  let service: StoreDataManagerService;
  const eventSubject = new ReplaySubject<RouterEvent>(1);

  const routerMock = {
    navigate: jasmine.createSpy('navigate'),
    events: eventSubject.asObservable(),
    url: 'test/url'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
      providers: [
        ChartsService,
        BypassSecurityPipe,
        CountStatPipe,
        DateFormatPipe,
        HumanReadableNumberPipe,
        InterpolateColorsPipe,
        SafeHtmlPipe,
        TagParserPipe,
        TimeReadablePipe,
        TruncatePipe,
        BooleanHighlightPipe,
        UserTitlePipe,
        UsersTitleTextPipe,
        PluralizePipe,
        {provide: Router, useValue: routerMock},
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    });
    service = TestBed.inject(StoreDataManagerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
