import { Injectable } from '@angular/core';
import {
  AnnotationStoreService,
  ApplicantsAliasesService,
  BooleanSearchService,
  BooleanSearchStoreService,
  BooleanTemplateService,
  CitationSearchService,
  CitationSearchStoreService,
  CollaborationService,
  CollectionService,
  CollectionStoreService,
  ErrorService,
  ExportChartService,
  IpLoungeService,
  JwtService,
  LandscapeStoreService,
  MonitorLegalStoreService,
  MonitorService,
  MonitorStoreService,
  NotificationsService,
  NplSearchService,
  NplSearchStoreService,
  OpenedDocumentStoreService,
  PatentNumberService,
  PatentTableService,
  PatentViewModeService,
  RatingService,
  RecaptchaService,
  SemanticSearchService,
  SemanticSearchStoreService,
  TagService,
  TaskService,
  UserService
} from '@core';

@Injectable({
  providedIn: 'root'
})
export class StoreDataManagerService {
  constructor(
    private userService: UserService,
    private recaptchaService: RecaptchaService,
    private booleanSearchService: BooleanSearchService,
    private booleanTemplateService: BooleanTemplateService,
    private citationSearchService: CitationSearchService,
    private collaborationService: CollaborationService,
    private collectionService: CollectionService,
    private errorService: ErrorService,
    private exportChartService: ExportChartService,
    private jwtService: JwtService,
    private monitorService: MonitorService,
    private notificationsService: NotificationsService,
    private nplSearchService: NplSearchService,
    private patentNumberService: PatentNumberService,
    private patentTableService: PatentTableService,
    private patentViewModeService: PatentViewModeService,
    private semanticSearchService: SemanticSearchService,
    private tagService: TagService,
    private taskService: TaskService,
    private ratingService: RatingService,
    private ipLoungeService: IpLoungeService,
    private booleanSearchStoreService: BooleanSearchStoreService,
    private citationSearchStoreService: CitationSearchStoreService,
    private collectionStoreService: CollectionStoreService,
    private landscapeStoreService: LandscapeStoreService,
    private monitorLegalStoreService: MonitorLegalStoreService,
    private monitorStoreService: MonitorStoreService,
    private nplSearchStoreService: NplSearchStoreService,
    private openedDocumentStoreService: OpenedDocumentStoreService,
    private semanticSearchStoreService: SemanticSearchStoreService,
    private annotationStoreService: AnnotationStoreService,
    private applicantsAliasesService: ApplicantsAliasesService,
  ) {
  }

  clearAllStoredData(): void {
    this.booleanSearchService.clearStoredData();
    this.booleanTemplateService.clearStoredData();
    this.citationSearchService.clearStoredData();
    this.collaborationService.clearStoredData();
    this.collectionService.clearStoredData();
    this.errorService.clearStoredData();
    this.exportChartService.clearStoredData();
    this.monitorService.clearStoredData();
    this.notificationsService.clearStoredData();
    this.nplSearchService.clearStoredData();
    this.patentNumberService.clearStoredData();
    this.patentTableService.clearStoredData();
    this.patentViewModeService.clearStoredData();
    this.recaptchaService.clearStoredData();
    this.semanticSearchService.clearStoredData();
    this.tagService.clearStoredData();
    this.taskService.clearStoredData();
    this.ratingService.clearStoredData();
    this.ipLoungeService.removeIpLoungeToken();
    this.userService.clearStoredData();
    this.booleanSearchStoreService.clearStoredData();
    this.citationSearchStoreService.clearStoredData();
    this.collectionStoreService.clearStoredData();
    this.landscapeStoreService.clearStoredData();
    this.monitorLegalStoreService.clearStoredData();
    this.monitorStoreService.clearStoredData();
    this.nplSearchStoreService.clearStoredData();
    this.openedDocumentStoreService.clearStoredData();
    this.semanticSearchStoreService.clearStoredData();
    this.annotationStoreService.clearStoredData();
    this.applicantsAliasesService.clearStoredData();
    this.jwtService.destroyTokens();
  }
}
