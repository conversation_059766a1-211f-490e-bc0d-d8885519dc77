export class ColorUtil {

  static TAG_COLORS: { name: string, code: string }[] = [
    {name: '<PERSON>', code: '#E0E4E5'}, {name: 'Cream', code: '#F1CDBE'}, {name: 'Orange', code: '#FBC7A2'},
    {name: '<PERSON>', code: '#D0B493'}, {name: 'Yellow', code: '#FDE5A8'}, {name: 'Bright yellow', code: '#F1FC7B'},
    {name: 'Lemon', code: '#E6EBAC'}, {name: 'Olive', code: '#A6DCB2'}, {name: 'Fluorescent', code: '#93FF97'},
    {name: 'Arctic', code: '#BDFEF2'}, {name: 'Light blue', code: '#A6E9F8'}, {name: 'Sapphire', code: '#B0D5DD'},
    {name: 'Blue', code: '#79BEE7'}, {name: 'Iris', code: '#B9C8FF'}, {name: '<PERSON>', code: '#D2AFFF'},
    {name: 'Lavender', code: '#F4D6FF'}, {name: '<PERSON><PERSON><PERSON>', code: '#E7B8DC'}, {name: '<PERSON>', code: '#FFD7EA'},
    {name: 'Red ruby', code: '#FDA3A3'}, {name: 'Scarlet', code: '#DA9C9C'}
  ];

  static genRandomColor(hasHexPrefix: boolean): string {
    let result = null, count = 0;
    while (true) {
      result = Math.floor(Math.random() * 16777215).toString(16);
      count++;
      if (!isNaN(Number(result)) || count > 10) {
        break;
      }
    }
    return result ? (hasHexPrefix ? '#' : '') + result : null;
  }

  static getRandomTagColor(): { name: string, code: string } {
    while (true) {
      const index = Math.floor(Math.random() * (ColorUtil.TAG_COLORS.length + 1));
      const color = ColorUtil.TAG_COLORS[index];
      if (color) {
        return color;
      }
    }
  }
}
