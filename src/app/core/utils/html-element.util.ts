export class HtmlElementUtil {
  static scrollBy(parentElement: HTMLElement, childElement: HTMLElement, topBuffer: number = 0) {
    const patentBodyEleRect = parentElement?.getBoundingClientRect();
    const eleRect = childElement?.getBoundingClientRect();
    if (!patentBodyEleRect || !eleRect) {
      return;
    }
    parentElement.scrollBy({
      top: eleRect.top - patentBodyEleRect.y - topBuffer,
      behavior: 'smooth'
    });
  }
}
