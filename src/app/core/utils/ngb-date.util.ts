import { NgbDate } from '@ng-bootstrap/ng-bootstrap';

export function dateStr2NgbDate(value: string): NgbDate {
  if (value) {
    const date = value.split('-');
    const day = parseInt(date[2], 10);
    const month = parseInt(date[1], 10);
    const year = parseInt(date[0], 10);
    return new NgbDate(year, month, day);
  }

  return null;
}

export function ngbDate2DateStr(date: NgbDate, includeTime = 'T00:00:00'): string {
  if (date && date.year && date.month && date.day) {
    const month = date.month.toString().padStart(2, '0');
    const day = date.day.toString().padStart(2, '0');
    const result = `${date.year}-${month}-${day}`;
    return includeTime ? `${result}${includeTime}` : result;
  }

  return null;
}
