export class TextUtil {
  static calculateTextWidth(txt: string, fontName: string, fontSize: string, fontWeight: number) {
    const id = 'get-width-of-text-func';
    let ele = document.getElementById(id) as HTMLCanvasElement;
    if (!ele) {
      ele = document.createElement('canvas');
      ele.id = id;
    }
    const ctx = ele.getContext('2d');
    const fontSpec = `${fontWeight} ${fontSize} ${fontName}`;
    if (ctx.font !== fontSpec) {
      ctx.font = fontSpec;
    }
    return ctx.measureText(txt.trim()).width;
  }

  static genRandomString(): string {
    return [...Array(10)].map(() => Math.random().toString(36)[2]).join('');
  }
}
