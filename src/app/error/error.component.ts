import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss']
})
export class ErrorComponent implements OnInit {
  errorCode: string;
  srcHref: string;
  srcApi: string;

  constructor(private activatedRoute: ActivatedRoute) {
  }

  ngOnInit(): void {
    this.activatedRoute.params.subscribe({
      next: val => {
        this.errorCode = val.code?.toString();
      }
    });

    this.activatedRoute.queryParams.subscribe({
      next: val => {
        this.srcHref = val.src_href;
        this.srcApi = val.src_api;
      }
    });
  }

  get403Message(): string {
    return 'If your account permissions changed recently, try logging in again. Otherwise, reach out to us at ' +
      '<a href="mailto:<EMAIL>"><EMAIL></a> or by using our live chat support.';
  }

  get404Message(): string {
    return 'We could not find any resources that appropriate for your request.';
  }
}
