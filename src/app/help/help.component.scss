:host::ng-deep {
  .sec-title {
    font-size: 45px;
    color: #fff;
    text-transform: uppercase;
  }

  .user-guide-title {
    width: 1080px;
    margin: 30px auto;
  }

  .user-guide-video-container {
    background-color: #283339;
    padding: 10px 0 20px;
  }

  .user-guide-video iframe {
    width: 1080px;
    height: 500px;
    margin: 30px auto;
    display: block;
  }

  .collapse-content {
    padding-left: 60px;
    padding-top: 20px;
    padding-bottom: 20px;
  }

  h2 {
    font-family: 'Open Sans Regular';
    padding-top: 48px;
    padding-bottom: 16px;
    border-bottom: 1px solid #BCCACE;
    color: #485D66;
    font-size: 2em;
  }

  h3 {
    font-family: 'Open Sans Bold';
    font-size: 1.5rem;
    line-height: 36px;
    color: #0F2C35;
    margin-bottom: 16px;
  }

  table {
    min-width: 1024px;
    border-collapse: collapse;
    width: 100%;
  }

  table, td, th {
    border: 1px solid #ddd;
    font-size: 1em !important;
    padding: 0 4px 0 4px;
  }

  th {
    background-color: #eee;
    font-weight: bold;
    height: 30px !important;
  }

  td {
    height: 36px !important;
  }

  .title-content {
    font-weight: bold !important;
    color: #0F2C35 !important;
  }
}

a {
  color: #00A083;
  &:hover {
    color: #2F8A76;
  }
}

.tab-title {
  border-bottom: 1px solid #BCCACE;
  color: #485D66;
  font-size: 36px;
}

.tab-text {
  color: #485D66;
  font-size: 16px;
}

