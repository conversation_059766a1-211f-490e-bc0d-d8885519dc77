import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-help',
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss']
})
export class HelpComponent implements OnInit {
  private _selectedTab = 'using-app';

  constructor(private route: ActivatedRoute) {
  }

  ngOnInit() {
    this.route.fragment.subscribe({
      next: fragment => {
        if (fragment) {
          this._selectedTab = fragment;
        }
      }
    });
  }

  get selectedTab(): string {
    return this._selectedTab;
  }
}
