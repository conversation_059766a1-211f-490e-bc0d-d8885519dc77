import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HistorySavesComponent } from './history-saves/history-saves.component';
import { AuthGuard } from '@core/guards';

const routes: Routes = [
  {path: '', component: HistorySavesComponent, canActivate: [AuthGuard]},
  {path: 'history', component: HistorySavesComponent, canActivate: [AuthGuard]},
  {path: 'documents', component: HistorySavesComponent, canActivate: [AuthGuard]},
  {path: 'opened-documents', component: HistorySavesComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HistorySavesRoutingModule {
}
