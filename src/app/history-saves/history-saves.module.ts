import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { SharedModule } from '@shared/shared.module';
import { HistorySavesRoutingModule } from './history-saves-routing.module';
import { HistorySavesComponent } from './history-saves/history-saves.component';
import { SearchModule } from 'app/search/search.module';
import { NgbAlertModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    HistorySavesRoutingModule,
    SearchModule,
    NgbAlertModule,
  ],
  declarations: [
    HistorySavesComponent
  ]
})
export class HistorySavesModule {
}
