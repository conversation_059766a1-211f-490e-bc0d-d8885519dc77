<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>
  <app-search-menu searchTab="history"></app-search-menu>

  <div class="flex-fill page-content container justify-content-start mb-4">
    <div class="d-flex flex-column">
      <app-alert type="warning" [message]="messageCollaboratorQuotas" [headline]="'Collaborator’s user rights'" version="figma" *ngIf="messageCollaboratorQuotas"></app-alert>

      <app-page-bar pageTitle="Search history">
        <app-filter-term-input leftSide placeHolder="Search for histories" (termChanged)="onFilterChanged($event)"></app-filter-term-input>
        <ng-container rightSide>
          <a href="javascript:void(0)" class="item-bar icon-delete" *ngIf="selectedHistories.length > 0"
             (click)="deleteSelectedHistories()" ngbTooltip="Delete selected histories" container="body">
            Delete {{selectedHistories.length}} selected
          </a>

          <a href="javascript:void(0)" class="item-bar icon-delete" [ngClass]="{'disabled': !histories.length || loading}"
             (click)="deleteAllHistories()" ngbTooltip="Delete all histories" container="body">
            Delete all history
          </a>
        </ng-container>
      </app-page-bar>

      <div class="overflow-auto flex-fill">
        <div *ngIf="!loading else loadingTemplate">
          <app-alert type="danger" [message]="message" *ngIf="message"></app-alert>
          <app-alert type="success" [message]="messageAlert" *ngIf="messageAlert"></app-alert>
          <app-alert type="warning" message="No search history found" *ngIf="!histories.length && filterTerm?.length && !messageAlert"></app-alert>

          <table id="histories-table" class="table table-condensed publication-table w-100 table-hover" *ngIf="histories.length">
            <thead class="w-100">
                <tr>
                    <th width="30px">
                      <label class="checkbox m-0 p-0">
                        <input type="checkbox" (change)="selectAllHistories($event)" [checked]="areHistoriesSelected">
                        <span class="no-text">&nbsp;</span>
                      </label>
                    </th>
                    <th width="70px">Load</th>
                    <th (click)="sort('search_input')" class="cursor-pointer"
                        appTableSortIcon sortColumn="search_input" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">
                      Input
                    </th>
                    <th width="170px" (click)="sort('created_at')" class="cursor-pointer"
                        appTableSortIcon sortColumn="created_at" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">
                      Date
                    </th>
                    <th width="240px" (click)="sort('search_type')" class="cursor-pointer"
                        appTableSortIcon sortColumn="search_type" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">
                      Type
                    </th>
                    <th width="70px" (click)="sort('search_filters')" class="cursor-pointer"
                        appTableSortIcon sortColumn="search_filters" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">Filters</th>
                    <th width="80px" (click)="sort('number_of_hits')" class="cursor-pointer"
                        appTableSortIcon sortColumn="number_of_hits" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">Results</th>
                    <th width="90px"></th>
                </tr>
            </thead>

            <tbody *ngFor="let hs of histories | paginate:
            {
            itemsPerPage: pagination?.page_size,
            id: 'histories-pagination',
            currentPage: pagination?.current_page,
            totalItems: pagination?.total_hits < 400 ? pagination?.total_hits : 400
            }; index as i;"
                   [ngClass]="{'has-detail-bg': openedRows.has(hs.id), 'preview-border': openedRows.has(hs.id) && (i === 0 || !openedRows.has(histories[i-1].id)),
                   'preview-border-vertical': openedRows.has(hs.id) && i > 0 && openedRows.has(histories[i-1].id)}">
                <tr [ngClass]="{'open-sans-semi-bold detailed' : openedRows.has(hs.id)}">
                  <td>
                    <label class="checkbox m-0 p-0">
                      <input type="checkbox" (change)="selectHistory($event, hs)" [checked]="isHistorySelected(hs)">
                      <span class="no-text">&nbsp;</span>
                    </label>
                  </td>
                  <td class="table-actions">
                    <div class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square load-search" ngbTooltip="Load this search" (click)="loadSearch(hs)">
                      <i class="fa-regular fa-redo"></i>
                    </div>
                  </td>
                  <td (click)="openDetail(hs.id)">
                    <div [ngClass]="{'text-ellipsis text-ellipsis-2': !openedRows.has(hs.id)}">
                      <span *ngIf="hs.expand_search_input" class="m-r-spacing-xx-s" ngbTooltip="Enhanced with Octi AI" tooltipClass="white-tooltip"><i class="fa-solid fa-sparkles"></i></span>
                      {{ textAndNumberSearch(hs) }}
                    </div>
                  </td>
                  <td (click)="openDetail(hs.id)">{{ hs.created_at | dateFormat: 'ShortDateTime': true }}</td>
                  <td (click)="openDetail(hs.id)">{{ getType(hs) }}</td>
                  <td (click)="openDetail(hs.id)">{{ hasFilters(hs) }}</td>
                  <td (click)="openDetail(hs.id)">
                    {{ hs.number_of_hits ?? hs.max_results }}
                  </td>
                  <td class="table-actions">
                    <div class="d-flex align-items-center justify-content-center gap-spacing-sm">
                      <div class="button-main-secondary-grey button-medium button-square" ngbTooltip="Delete this search history" (click)="deleteHistory(hs)">
                        <i class="fa-regular fa-fw fa-trash-alt"></i>
                      </div>

                      <div class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square" [ngClass]="{'preview-close': openedRows.has(hs.id)}"
                           (click)="openDetail(hs.id)">
                        <span *ngIf="!openedRows.has(hs.id)" class="caret-down"><i class="fa-regular fa-angle-down"></i></span>
                        <span *ngIf="openedRows.has(hs.id)" class="caret-up"><i class="fa-regular fa-times"></i></span>
                      </div>
                    </div>
                  </td>
                </tr>

                <div class="d-table-row patent-detail-container has-detail-bg" *ngIf="openedRows.has(hs.id)">
                  <td colspan="9" class="patent-detail px-1 border-0">

                    <ng-container *ngIf="hs.source_language" [ngTemplateOutlet]="searchDetailRowTemplate"
                                  [ngTemplateOutletContext]="{title: 'Input language', value: hs.source_language + ' - ' + getSourceLanguageTitle(hs.source_language)}">
                    </ng-container>

                    <div class="d-flex justify-content-between" *ngIf="hs.formatted_search_filters?.length > 0 else noFilterTemplate">
                      <div style="width: 30px" class="p-0 m-0"></div>
                      <div class="patent-detail-title px-3 py-2 flex-fill">Filters:</div>
                      <div style="width: 60px" class="p-0 m-0"></div>
                    </div>

                    <ng-container *ngFor="let f of techAreasFilters(hs); let last = last;" [ngTemplateOutlet]="searchDetailRowTemplate"
                                  [ngTemplateOutletContext]="f">
                    </ng-container>

                    <ng-container *ngFor="let f of hs.formatted_search_filters; let last = last;" [ngTemplateOutlet]="searchDetailRowTemplate"
                                  [ngTemplateOutletContext]="f">
                    </ng-container>
                  </td>

                </div>
            </tbody>
          </table>

          <div class="d-flex justify-content-between mt-4 align-items-center" *ngIf="pagination?.last_page > 1">
            <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" [pageOptions]="pageOptions"></app-page-size>

            <div class="flex-fill">
              <pagination-controls id="histories-pagination" class="d-flex justify-content-end"
                                   (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #loadingTemplate>
  <app-spinner></app-spinner>
</ng-template>

<ng-template #searchDetailRowTemplate let-title="title" let-value="value">
  <div class="patent-detail-content">
    <div class="d-flex justify-content-between">
      <div style="width: 30px" class="p-0 m-0"></div>
      <div style="width: 200px" class="ps-3 pe-2">
        {{ title }}:
      </div>
      <div class="ps-2 pe-3 flex-fill">
        {{ value }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noFilterTemplate>
    <div class="d-flex justify-content-between">
      <div style="width: 30px" class="p-0 m-0"></div>
      <div class="px-3 py-2 flex-fill">No filters used</div>
      <div style="width: 60px" class="p-0 m-0"></div>
    </div>
</ng-template>

