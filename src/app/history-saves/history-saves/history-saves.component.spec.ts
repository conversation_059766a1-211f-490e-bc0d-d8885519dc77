import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { HistorySavesComponent } from './history-saves.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TruncatePipe } from '@core/pipes';
import { SearchModule } from 'app/search/search.module';
import { NgbAlertModule } from '@ng-bootstrap/ng-bootstrap';
import { provideMatomo } from 'ngx-matomo-client';

describe('HistorySavesComponent', () => {
  let component: HistorySavesComponent;
  let fixture: ComponentFixture<HistorySavesComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        HistorySavesComponent
      ],
      imports: [
        SharedModule,
        SearchModule,
        NgbAlertModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        TruncatePipe, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HistorySavesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
