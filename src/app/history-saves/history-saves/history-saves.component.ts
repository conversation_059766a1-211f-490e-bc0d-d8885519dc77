import { Component, OnInit } from '@angular/core';
import {
  ConfirmationDialogService,
  MatomoService,
  PaginationMetadata,
  SearchHistory,
  SearchHistoryService,
  UserService
} from '@core/services';
import { catchError, concatMap, debounce, take, tap } from 'rxjs/operators';
import { Router } from "@angular/router";
import { BehaviorSubject, forkJoin, Observable, Subscription, timer } from "rxjs";
import { MAIN_TECHNOLOGY_AREA_KEYS, MAIN_TECHNOLOGY_AREAS } from '@core';


@Component({
  selector: 'app-history-saves',
  templateUrl: './history-saves.component.html',
  styleUrls: ['./history-saves.component.scss']
})
export class HistorySavesComponent implements OnInit {

  filterTerm = '';
  message: string;
  messageAlert: string;

  openedRows: Set<number> = new Set<number>();
  histories: Array<SearchHistory> = [];
  payload: any;
  pagination: PaginationMetadata;
  loading = false;
  pageSize = 25;
  pageOptions = [25, 50, 100];
  selectedHistories: Array<SearchHistory> = [];

  sortBy = 'created_at';
  sortOrder: 'asc' | 'desc' = 'desc';

  private searchHistoriesSubject = new BehaviorSubject<string>(null);
  private subscriptions = new Subscription();

  get areHistoriesSelected(): boolean {
    const selectedIds = this.selectedHistories.map((hs) => hs.id);
    return this.histories.every((hs) => selectedIds.includes(hs.id));
  }

  get messageCollaboratorQuotas(): string {
    return this.userService.messageCollaboratorQuotas;
  }

  constructor(
    public userService: UserService,
    private confirmationDialogService: ConfirmationDialogService,
    private searchHistoryService: SearchHistoryService,
    private router: Router,
    private matomoService: MatomoService,
  ) {
  }

  ngOnInit() {
    this.getUserStatistics();
    const searchHistories$ = this.searchHistoriesSubject.asObservable()
      .pipe(
        tap((val) => {
          this.loading = true;
        }),
        debounce((val: string) => timer(val?.length > 0 ? 1000 : 0)),
        concatMap((val) => this.searchHistory(val)),
        tap(() => {
          this.loading = false;
        })
      )
      .subscribe();
    this.subscriptions.add(searchHistories$);
  }

  deleteAllHistories() {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to<br/> <b>delete all of search history records</b> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const clear$ = this.searchHistoryService.clear().subscribe({
          next: () => {
            this.loading = false;
            this.searchHistoriesSubject.next(this.filterTerm);
          }, error: error => {
            this.loading = false;
            console.log(error);
          }
        });
        this.subscriptions.add(clear$);
      }
    });
  }

  openDetail(hsId: number): void {
    if (this.openedRows.has(hsId)) {
      this.openedRows.delete(hsId);
    } else {
      this.openedRows.add(hsId);
    }
  }

  textAndNumberSearch(hs: SearchHistory): string {
    return this.searchHistoryService.textAndNumberSearch(hs);
  }

  getType(search: SearchHistory): string {
    return this.searchHistoryService.getType(search);
  }

  hasFilters(search: SearchHistory): string {
    return search.formatted_search_filters?.length > 0 ? 'Yes' : 'No';
  }

  onPageChange($event: number) {
    this.onPageChangeHistory($event);
  }

  loadSearch(item: SearchHistory): void {
    this.matomoService.reloadSearchButton();
    this.searchHistoryService.loadSavedSearch(item);
  }

  sort(field): void {
    if (field === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }

    this.searchHistoriesSubject.next(this.filterTerm);
  }

  onChangePageSize(event: number): void {
    this.pageSize = event;
    this.resetCurrentPage();
    this.searchHistoriesSubject.next(this.filterTerm);
  }

  selectAllHistories(event: Event) {
    if (event.target['checked']) {
      const historyIds = this.selectedHistories.map((hs) => hs.id);
      this.selectedHistories = [...this.selectedHistories, ...this.histories.filter((a) => !historyIds.includes(a.id)) ];
    } else {
      const historyIds = this.histories.map((hs) => hs.id);
      this.selectedHistories = this.selectedHistories.filter((a) => !historyIds.includes(a.id));
    }
  }

  selectHistory(event: Event, hs: SearchHistory) {
    if (event.target['checked']) {
      if (!this.selectedHistories.some((a) => a.id === hs.id)) {
        this.selectedHistories.push(hs);
      }
    } else {
      this.selectedHistories = this.selectedHistories.filter((a) => a.id !== hs.id);
    }
  }

  isHistorySelected(hs: SearchHistory): boolean {
    return this.selectedHistories.some((a) => a.id === hs.id);
  }

  deleteHistory(hs: SearchHistory) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green">${this.textAndNumberSearch(hs)}</span> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const delete$ = this.searchHistoryService.delete(hs.id)
          .pipe(take(1))
          .subscribe({
            next: () => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.histories.length === 1);
              this.searchHistoriesSubject.next(this.filterTerm);
              this.openedRows.delete(hs.id);
            }, error: error => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(delete$);
      }
    });
  }

  deleteSelectedHistories() {
    const subject = this.selectedHistories.length > 1 ? 'search histories' : 'search history';
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to<br/> <b>delete ${this.selectedHistories.length} selected ${subject}</b> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const obs = this.selectedHistories.map((hs) => this.searchHistoryService.delete(hs.id));
        const obs$ = forkJoin(obs).subscribe({
          next: (val) => {
            this.loading = false;
            this.resetCurrentPageAfterDeleting(this.areHistoriesSelected);
            this.selectedHistories = [];
            this.searchHistoriesSubject.next(this.filterTerm);
          },
          error: (error) => {
            this.loading = false;
            console.log(error);
          }
        });
        this.subscriptions.add(obs$);
      }
    });
  }

  getSourceLanguageTitle(languageCode: string): string {
    return this.searchHistoryService.getSourceLanguageTitle(languageCode);
  }

  onFilterChanged(event: string) {
    this.filterTerm = event;
    this.resetCurrentPage();
    this.searchHistoriesSubject.next(event);
  }

  cleanSelectedHistories() {
    this.selectedHistories = [];
  }

  private searchHistory(filterTerm: string): Observable<{
    searches: SearchHistory[],
    page: PaginationMetadata
  }> {
    this.message = null;
    this.openedRows.clear();
    const payload = this.buildPayload(this.pagination ? this.pagination.current_page : 1, filterTerm);

    return this.searchHistoryService.getHistories(payload)
      .pipe(
        tap(({searches, page}) => {
          this.histories = searches;
          this.pagination = page;

          if (this.histories.length === 0 && !this.filterTerm) {
            this.message = 'No search history found';
          }
        }),
        catchError((error) => {
          console.log(error);
          this.message = 'An error occurred while loading search history';
          throw error;
        })
      );
  }

  private buildPayload(page: number, filterTerm: string): any {
    const payload = {
      page_size: this.pageSize,
      page: page,
      sort_by: this.sortBy,
      sort_order: this.sortOrder,
      search_type: 'in:SEMANTIC,BOOLEAN,NPL'
    };

    if (filterTerm) {
      payload['search_input'] = `like:%${filterTerm}%`;
    }
    return payload;
  }

  private onPageChangeHistory($event): void {
    const {current_page} = this.pagination;

    if (current_page === $event) {
      return;
    }

    this.pagination.current_page = $event;
    this.searchHistoriesSubject.next(this.filterTerm);
  }

  private resetCurrentPage() {
    if (this.pagination) {
      this.pagination.current_page = 1;
    }
  }

  private resetCurrentPageAfterDeleting(shouldDecreaseCurrentPage = false) {
    if (this.pagination) {
      if (this.pagination.current_page > 1 && this.pagination.current_page === this.pagination.last_page && shouldDecreaseCurrentPage) {
        this.pagination.current_page--;
      }
    }
  }

  private getUserStatistics() {
    if (!this.userService.isCollaboratorUser()) {
      return;
    }

    const userStatistics$ = this.userService.getStatistics().subscribe();
    this.subscriptions.add(userStatistics$);
  }

  techAreasFilters(hs: SearchHistory): {title: string, value: string}[] {
    const existingFilters = Object.keys(hs.search_filters || []).filter(key => key.startsWith('ma'));
    if (!existingFilters.length) {
      return [];
    }

    const filteredAreas = MAIN_TECHNOLOGY_AREAS
      .filter(area => !existingFilters.includes(area.value))
      .map(area => area.label);

    return [{
      title: 'Tech areas',
      value: filteredAreas.join(', ')
    }];
  }

}
