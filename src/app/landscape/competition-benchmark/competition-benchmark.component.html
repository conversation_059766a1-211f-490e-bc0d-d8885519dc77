<div class="competitive-insights-section">
    <div class="container-fluid ">

        <div class="section-title">
            <span>Insights</span>
            <app-tooltip tooltipText="Explore your insights here. Scroll down to dive deeper into the associated charts." tooltipIconSize="sm"></app-tooltip>
        </div>

        <app-alert *ngIf="competitiveProfile?.status == 'COMPUTING'" classes="mt-4" type="warning" [hideCloseBtn]="true" message="Competition benchmark is not ready yet. Please check again later.">
        </app-alert>

        <app-spinner *ngIf="loadingCompetitiveInsights"></app-spinner>
        <div class="row my-3 insight-section" *ngIf="!loadingCompetitiveInsights" [class.show-more-insight]="seeMoreInsights" [@fadeInOut]>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.top_competitors?.assessment">
                <div class="insight-box" [ngbPopover]="popoverTC" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span>Top competitors</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.top_competitors?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class=" insight-icon">
                            <i class="fa-light fa-circle-nodes"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.top_competitors?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.top_competitors?.description}}</div>
                    <ng-template #popoverTC>
                        <div>
                          <div class="popover-title">Top competitors</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We look at the most recurrent names in the patents similar to yours.
                            These are potential competitors, since they overlap with your technological fields,
                            and could be relevant from a strategic point of view.
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.patent_age?.assessment">
                <div class="insight-box cursor-pointer"  (click)="navigateToChart('app-technology-time-line')" [ngbPopover]="popoverPD" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span><i class="fa-light fa-chart-pie-simple"></i> Portfolio age difference</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.patent_age?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class=" insight-icon">
                            <i class="fa-light fa-calendar-days"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.patent_age?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.patent_age?.description}}</div>
                    <ng-template #popoverPD>
                        <div>
                          <div class="popover-title">Portfolio age difference</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the average age of your patents with the one of your competition.
                            A younger portfolio suggests active innovation and alignment with current trends,
                            while an older one may indicate an established presence,
                            but could require refreshment to maintain competitiveness.
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.citations_counts?.assessment">
                <div class="insight-box"  [ngbPopover]="popoverC" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span>Patent citations</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.citations_counts?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div  class="  insight-icon">
                            <i class="fa-light fa-book-open-cover"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.citations_counts?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.citations_counts?.description}}</div>
                    <ng-template #popoverC>
                        <div>
                          <div class="popover-title">Patents citations</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the number of citations of your patents with the ones of your competition.
                            This indicates the influence or novelty of your technologies, reflecting their recognition and potential impact
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.market_coverage?.assessment">
                <div class="insight-box"  [ngbPopover]="popoverMC" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span>Market coverage</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.market_coverage?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class="insight-icon">
                            <i class="fa-light fa-globe-pointer"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.market_coverage?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.market_coverage?.description}}</div>
                    <ng-template #popoverMC>
                        <div>
                          <div class="popover-title">Market coverage</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the average number of markets that your patents cover with the ones of your competition.
                            Wider coverage indicates broader protection and a competitive edge.
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.top_authorities?.assessment">
                <div class="insight-box cursor-pointer" (click)="navigateToChart('app-authorities-map')" [ngbPopover]="popoverWFP" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span><i class="fa-light fa-chart-pie-simple"></i> New markets</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.top_authorities?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class=" insight-icon">
                            <i class="fa-light fa-globe"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.top_authorities?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.top_authorities?.description}}</div>
                    <ng-template #popoverWFP>
                        <div>
                          <div class="popover-title">New markets</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We look at the countries where patents similar to yours are more present than you.
                            This can give information regarding competitors' potential areas for market expansion,
                            or innovation efforts to enhance positioning
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.patent_value?.assessment">
                <div class="insight-box cursor-pointer" (click)="navigateToChart('app-patent-value')"  [ngbPopover]="popoverMV" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span><i class="fa-light fa-chart-pie-simple"></i> Portfolio value</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.patent_value?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div  class=" insight-icon">
                            <i class="fa-light fa-piggy-bank"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.patent_value?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.patent_value?.description}}</div>
                    <ng-template #popoverMV>
                        <div>
                          <div class="popover-title">Portfolio value</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the estimated average value of your patents with the one of your competition.
                            This can indicate your portfolio's potential revenue generation or strategic value
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.technology_broadness?.assessment">
                <div class="insight-box"  [ngbPopover]="popoverB" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span>Technology broadness</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.technology_broadness?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class="insight-icon">
                            <i class="fa-light fa-chart-scatter-bubble"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.technology_broadness?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.technology_broadness?.description}}</div>
                    <ng-template #popoverB>
                        <div>
                          <div class="popover-title">Technology broadness</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the average number of technology fields covered by your patents with the one of your competition.
                            A broader coverage indicates diversity and a more robust positioning spread across multiple innovation sectors
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.top_technology_fields?.assessment">
                <div class="insight-box cursor-pointer"  (click)="navigateToChart('app-technological-fields')"  [ngbPopover]="popoverTF" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span><i class="fa-light fa-chart-pie-simple"></i> New technology fields</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.top_technology_fields?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div class=" insight-icon">
                            <i class="fa-light fa-lightbulb-gear"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.top_technology_fields?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.top_technology_fields?.description}}</div>
                    <ng-template #popoverTF>
                        <div>
                          <div class="popover-title">New technology fields</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We look at the technological fields where patents similar to yours are more present than you.
                            This can give information regarding trends or competitor's strategies.
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
            <div class="col-md-4 my-2" *ngIf="competitiveInsights?.green_patents?.assessment">
                <div class="insight-box cursor-pointer" (click)="navigateToChart('app-green-categories')"  [ngbPopover]="popoverS" [autoClose]="'outside'" popoverClass="white-popover" triggers="hover">
                    <div class="insight-caption">
                        <span><i class="fa-light fa-chart-pie-simple"></i> Portfolio sustainability</span>
                        <span class="insight-caption-icon-{{competitiveInsights?.green_patents?.assessment}}"></span>
                    </div>

                    <div class="insight-title-container">
                        <div  class=" insight-icon">
                            <i class="fa-light fa-hand-holding-seedling"></i>
                        </div>
                        <div class="d-flex align-items-center flex-grow-1">
                            <div class="insight-title">{{competitiveInsights?.green_patents?.summary}}</div>
                        </div>
                    </div>
                    <div class="insight-description">{{competitiveInsights?.green_patents?.description}}</div>
                    <ng-template #popoverS>
                        <div>
                          <div class="popover-title">Portfolio sustainability</div>
                          <div class="popover-descriptions m-y-spacing-md">
                            We compare the number of green patents in your portfolio with the ones of your competition, according to the OECD classification.
                            This indicates the degree of environmental impact and innovation in eco-friendly technologies
                          </div>
                        </div>
                    </ng-template>
                </div>
            </div>
        </div>
        <div class="more-insights-section" *ngIf="competitiveInsights">
            <a href="javascript:void(0)" class="more-insight-btn" 
                (click)="seeMoreInsights=!seeMoreInsights">See {{seeMoreInsights? 'less': 'more'}} insights 
                <i class="fa-light ms-2" [ngClass]="seeMoreInsights ? 'fa-chevron-up':'fa-chevron-down'"></i>
            </a>
        </div>
    </div>
</div>
<div class="competitive-charts-section">
    <div class="container-fluid ">
        <app-spinner *ngIf="loadingCompetitiveProfile"></app-spinner>
        <div class="row" *ngIf="!loadingCompetitiveProfile">
            <div class="col-sm-6">
                <div class="competitive-charts">
                    <div class="competitive-charts-title sticky-top">My portfolio 
                        <a class="charts-download-btn" href="javascript:void(0)" (click)="onDownloadCharts('main')" [class.disabled]="downloadingCharts">
                            <i class="fa-light fa-arrow-down-to-line"></i>
                        </a>
                    </div>
                    <div class="charts-container charts-container-main" *ngIf="competitiveChartsData.main">
                        <div>
                            <app-technology-time-line [title]="'Patent families published'"
                                [showFavoriteOption]="false" [disableFilter]="true" [hideSlider]="true" 
                                [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [yAxisPercentage]="true"
                                [secondarySource]="getMainChartSource('basic_technology_timeline')">
                            </app-technology-time-line>
                        </div>
                        <div>
                            <app-authorities-map [title]="'Protected authorities'" 
                                [showFavoriteOption]="false" [disableFilter]="true" [storeService]="landscapeStoreService" 
                                [showTooltipPatentFamilies]="false"
                                [secondarySource]="getMainChartSource('authorities_authorities_map')"
                                [showLegend]="false">
                            </app-authorities-map>
                        </div>
                        <div>
                            <app-technological-fields [title]="'Covered technology areas'"
                                [showFavoriteOption]="false" [disableFilter]="true" [storeService]="landscapeStoreService" 
                                [showTooltipPatentFamilies]="false"
                                [secondarySource]="getMainChartSource('basic_technological_fields')">
                            </app-technological-fields>
                        </div>
                        <div>
                            <app-green-categories [title]="'Share of green patent families'" [showFavoriteOption]="false"
                                [disableFilter]="true" [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [secondarySource]="getMainChartSource('green_categories')">
                            </app-green-categories>
                        </div>
                        <div>
                            <app-patent-value [title]="'Estimated value of patent families'" [showFavoriteOption]="false" 
                                [disableFilter]="true" [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [secondarySource]="getMainChartSource('analytics_patent_value')">
                            </app-patent-value>
                        </div>
                    </div>

                    <app-spinner *ngIf="!competitiveChartsData.main"></app-spinner>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="competitive-charts">
                    <div class="competitive-charts-title sticky-top">Main competition 
                        <a class="charts-download-btn" href="javascript:void(0)" 
                            (click)="onDownloadCharts('competitive')" [class.disabled]="downloadingCharts">
                            <i class="fa-light fa-arrow-down-to-line"></i>
                        </a>
                    </div>
                    <div class="charts-container charts-container-competitive" *ngIf="competitiveChartsData.competitive">
                        <div>
                            <app-technology-time-line [title]="'Patent families published by competition'" 
                                [chartOptions]="secondaryOptionTTL" [showFavoriteOption]="false" [disableFilter]="true" 
                                [hideSlider]="true" [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [yAxisPercentage]="true"
                                [secondarySource]="getCompetitiveChartSource('basic_technology_timeline')">
                            </app-technology-time-line>
                        </div>
                        <div>
                            <app-authorities-map [title]="'Authorities protected by competition'" 
                                [chartOptions]="secondaryOptionAM" [showFavoriteOption]="false" [disableFilter]="true" 
                                [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [secondarySource]="getCompetitiveChartSource('authorities_authorities_map')"
                                [showLegend]="false">
                            </app-authorities-map>
                        </div>
                        <div>
                            <app-technological-fields [title]="'Technology areas covered by competition'"
                                [chartOptions]="secondaryOptionTF" [showFavoriteOption]="false" [disableFilter]="true" 
                                [showTooltipPatentFamilies]="false"
                                [storeService]="landscapeStoreService" [secondarySource]="getCompetitiveChartSource('basic_technological_fields')">
                            </app-technological-fields>
                        </div>
                        <div>
                            <app-green-categories [title]="'Share of green patent families of competition'" 
                                [chartOptions]="secondaryOptionGC" [showFavoriteOption]="false" [disableFilter]="true"
                                [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [secondarySource]="getCompetitiveChartSource('green_categories')">
                            </app-green-categories>
                        </div>
                        <div>
                            <app-patent-value [title]="'Estimated value of competition\'s patent families'" 
                                [chartOptions]="secondaryOptionPV" [showFavoriteOption]="false" [disableFilter]="true" 
                                [storeService]="landscapeStoreService" [showTooltipPatentFamilies]="false"
                                [secondarySource]="getCompetitiveChartSource('analytics_patent_value')">
                            </app-patent-value>
                        </div>
                    </div>
                    <app-spinner *ngIf="!competitiveChartsData.competitive"></app-spinner>
                </div>
            </div>
        </div>
    </div>
</div>