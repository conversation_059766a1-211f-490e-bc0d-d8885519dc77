.competitive-insights-section {
  padding-top: 1rem;
  padding-bottom: 1rem;
  background-color: #fff;

  .section-title {
    font-family: "Open Sans Semi Bold";
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1.75rem;
    display: flex;
    justify-content: space-between;
  }

  .insight-box {
    background-color: #f5f7fa;
    border: 1px solid #CBDCE2;
    border-radius: .5rem;
    padding: .75rem;
    height: 100%;
    display: flex;
    flex-direction: column;

    &:hover {
      border: 1px solid #698A95;
    }
  }

  .insight-caption {
    display: flex;
    justify-content: space-between;
    font-family: "Open Sans Regular";
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 500;
    line-height: 1.25rem;
    color: #4B5D66;
    margin-bottom: .75rem;

    &-icon {

      &-good,
      &-neutral,
      &-bad {
        padding: 0 10px;
        border-radius: 1rem;

        &::before {
          font-family: "Font Awesome 6 Pro";
          font-weight: 300;
          box-sizing: border-box;
        }
      }

      &-good {
        background-color: #D5F6C6;
        color: #1B7626;

        &::before {
          content: "\f118";
        }
      }

      &-neutral {
        background-color: #FFEBB9;
        color: #725408;

        &::before {
          content: "\e39b";
        }
      }

      &-bad {
        background-color: #FFD2C9;
        color: #951522;

        &::before {
          content: "\e376";
        }
      }
    }
  }

  .insight-title {
    text-align: left;
    color: #0F2C35;
    /* Heading/H3 */
    font-family: "Open Sans Semi Bold";
    font-size: 1.25rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1.75rem;
    display: flex;
    align-items: center;
  }

  .insight-title-container {
    border-bottom: 1px solid #CBDCE2;
    padding-bottom: .5rem;
    margin-bottom: .5rem;
    display: flex;
    height: 4rem;
  }

  .insight-description {
    display: block;
    font-size: 0.875rem;
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem;
    color: #4B5D66;
  }

  .insight-title,
  .insight-description {
    white-space: normal !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    overflow-wrap: break-word;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .insight-icon {
    display: flex;
    align-items: center;

    i {
      border-radius: .5rem;
      background: #E8F0F3;
      padding: .375rem;
      font-size: 24px;
      line-height: 24px;
    }

    padding-right: 1rem;
  }

  .more-insights-section {
    width: 100%;
    text-align: center;
    border-bottom: 1px solid #CBDCE2;
    line-height: 0.1em;
    margin: 10px 0 25px;
    height: 12px;

    .more-insight-btn {
      padding: 0.375rem 0.9375rem;
      background-color: #f8fbfc;
      color: #0F2C35;
      border-radius: .25rem;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.25rem;
    }
  }
}

.insight-section {
  max-height: 190px;
  transition: max-height .5s ease-in-out;
  overflow: hidden;
  flex-wrap: nowrap;

  &.show-more-insight {
    flex-wrap: wrap;
    max-height: 1900px;
  }
}

.competitive-charts-section {
  padding-top: 1rem;
  padding-bottom: 1rem;
  background-color: #F8FBFC;

  .competitive-charts {
    background-color: #fff;
    padding: 0 1rem 1rem;
    border-radius: .5rem;
    border: 1px solid #CBDCE2;
  }

  .competitive-charts-title {
    font-family: "Open Sans Semi Bold";
    background-color: #fff;
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 600;
    line-height: 2rem;
    border-bottom: 1px solid #CBDCE2;
    padding-top: 1rem;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .charts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .charts-download-btn {
    float: right;
    font-size: 1rem;
    font-family: "Open Sans Semi Bold";
    color: #0F2C35;
    border-radius: 3px;
    line-height: 1.5rem;
    padding: 0 6px;

    &:hover {
      background-color: #D4E4E9;
    }
  }
}

:host::ng-deep {
  .charts-container-competitive {
    .chart-item .graph-wrap {
      background-color: #FFF3EB;
    }
  }
}