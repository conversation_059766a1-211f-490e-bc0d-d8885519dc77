import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CompetitionBenchmarkComponent } from './competition-benchmark.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('CompetitionBenchmarkComponent', () => {
  let component: CompetitionBenchmarkComponent;
  let fixture: ComponentFixture<CompetitionBenchmarkComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CompetitionBenchmarkComponent ],
      imports: [
        HighchartsChartModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CompetitionBenchmarkComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
