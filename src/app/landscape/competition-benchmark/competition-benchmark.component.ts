import { animate, state, style, transition, trigger } from '@angular/animations';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ChartsService, ExportChartService, LandscapeProfile, LandscapeService, LandscapeStoreService } from '@core';
import { Subscription, take } from 'rxjs';

import * as TTFsetting from '@shared/charts/basic-charts/technology-time-line/settings';
import * as AMsetting from '@shared/charts/authorities-charts/authorities-map/settings';
import * as TFsetting from '@shared/charts/basic-charts/technological-fields/settings';
import * as GCsetting from '@shared/charts/landscape-charts/green-categories/settings';
import * as PVsetting from '@shared/charts/analytics-charts/patent-value/settings';
import { ViewModeTypeEnum } from '@search/patent/types';
import { manualCalculationCharts } from '@shared/charts';

@Component({
  selector: 'app-competition-benchmark',
  templateUrl: './competition-benchmark.component.html',
  styleUrls: ['./competition-benchmark.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ])
  ]
})
export class CompetitionBenchmarkComponent implements OnInit, OnDestroy {

  @Input() profile: LandscapeProfile;

  loadingCompetitiveProfile = false;
  competitiveProfile: LandscapeProfile;
  competitiveInsights: any;
  loadingCompetitiveInsights = false;
  seeMoreInsights = false;
  downloadingCharts: boolean;

  private competitiveChartsData: {main: any, competitive: any} = {main: null, competitive: null};
  private subscriptions = new Subscription();
  private competitiveChartsName = [
    'basic_technology_timeline',
    'authorities_authorities_map',
    'basic_technological_fields',
    'green_categories',
    'analytics_patent_value'
  ];
  secondaryOptionTTL = TTFsetting.secondaryChartSetting;
  secondaryOptionAM = AMsetting.secondaryChartSetting;
  secondaryOptionTF = TFsetting.secondaryChartSetting;
  secondaryOptionGC = GCsetting.secondaryChartSetting;
  secondaryOptionPV = PVsetting.secondaryChartSetting;

  constructor(
    private landscapeService: LandscapeService,
    public landscapeStoreService: LandscapeStoreService,
    private exportChartService: ExportChartService,
    private chartService: ChartsService,
  ) { }

  get isCompetitiveMode(): boolean {
    return this.landscapeStoreService.patentListViewMode === ViewModeTypeEnum.COMPETITIVE_LANDSCAPE;
  }

  ngOnInit(): void {
    this.loadProfile();

    const searchHash$ = this.landscapeStoreService.searchHash$.subscribe(hash => {
      if (hash) {
        this.getCharts();
      }
    });
    this.subscriptions.add(searchHash$);

  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.chartService.resetCharts();
    this.landscapeStoreService.chartSelectedValues = {};
  }

  loadProfile(): void {
    if (!this.profile) {
      return;
    }
    this.loadingCompetitiveProfile = true;
    this.landscapeService.loadProfile(this.profile.competitive_profile_id).then(
      response => {
        this.competitiveProfile = response as LandscapeProfile;
        if (this.competitiveProfile.status == 'COMPUTING') {
          setTimeout(() => this.loadProfile(), 5000);
          return;
        }
        else if (this.competitiveProfile.status != 'COMPLETED') {
          return;
        }
        this.loadingCompetitiveProfile = false;
        this.loadCompetitiveInsights();
      }
    ).catch(
      error => {
        console.error(error);
        throw error;
      }
    );
  }

  public getCharts() {
    this.landscapeStoreService.isCalculatingCharts = true;
    const payload = this.buildChartPayload();

    const calculate$ = this.chartService.calculate(payload, this.landscapeStoreService.searchHash)
      .pipe(take(1))
      .subscribe({
        next: ({charts}) => {
          this.landscapeStoreService.isCalculatingCharts = false;
        },
        error: (err) => {
          console.log(err);
          this.landscapeStoreService.isCalculatingCharts = false;
          throw err;
        }
      });
    this.subscriptions.add(calculate$);
  }

  private buildChartPayload() {
    const payload = {
      charts: this.getChartNames(),
      'parameters': {
        'technology_timeline_trendline': true
      }
    };

    const freeTextQuery = this.landscapeStoreService.getAppliedFiltersQuery();

    if (freeTextQuery && !this.isCompetitiveMode) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    return payload;
  }

  private getChartNames(): Array<string> {
    let chartNames = [...this.competitiveChartsName];
    if (this.landscapeStoreService.isCustomChartCategory()) {
      this.landscapeStoreService.customChartCategories.forEach(tab => {
        chartNames.push(...tab.charts.filter(c => !manualCalculationCharts.includes(c)).map(val => val));
      });
    } else {
      chartNames.push(...this.landscapeStoreService.getActiveDefaultCategoryCharts().filter(c => !manualCalculationCharts.includes(c.name)).map(ch => ch.name));
    }
    return [...new Set(chartNames)];
  }

  private loadCompetitiveInsights() {
    if (this.loadingCompetitiveInsights) {
      return;
    }
    if (this.profile.competitive_profile_id && !this.competitiveInsights) {
      this.loadingCompetitiveInsights = true;
      const insights$ = this.landscapeService.getCompetitiveInsight(this.profile.id, {})
        .subscribe({
          next: ({ data }) => {
            this.competitiveInsights = data;
            this.loadingCompetitiveInsights = false;
            this.loadCompetitiveCharts();
          }, error: (err) => {
            console.error(err);
            this.loadingCompetitiveInsights = false;
            throw err;
          }
        });
      this.subscriptions.add(insights$);
    }
  }

  private loadCompetitiveCharts(){
    const mainChartsPayload = {
      charts: this.competitiveChartsName,
      search_filters: {},
      parameters: {
        'technology_timeline_trendline': true,
        'technology_timeline_fill_missing_years': true,
        'top_technological_fields_sort_tech_areas_by': 'index',
      }
    }
    if (this.competitiveChartsData.main === null && this.profile?.charts?.hash) {
      const mainCharts$ = this.chartService.calculateToSingleChart(mainChartsPayload, this.profile.charts.hash)
        .subscribe({next: ({charts}) => {
          setTimeout(() => {
            this.competitiveChartsData.main = charts;
          }, 1000);
        }, error: (err) => {
            console.error(err);
            throw err;
        }});
      this.subscriptions.add(mainCharts$)
    }
    const competitiveChartsPayload = {
      charts: this.competitiveChartsName,
      search_filters: {},
      parameters: {
        'technology_timeline_trendline': true,
        'technology_timeline_min_year': this.profile.oldest_priority_date ? new Date(this.profile.oldest_priority_date).getFullYear() : null,
        'technology_timeline_max_year': this.profile.newest_priority_date ? new Date(this.profile.newest_priority_date).getFullYear() : null,
        'technology_timeline_fill_missing_years': true,
        'top_technological_fields_sort_tech_areas_by': 'index',
      }
    }
    if(this.competitiveChartsData.competitive === null && this.competitiveProfile?.charts?.hash){
      const competitiveCharts$ = this.chartService.calculateToSingleChart(competitiveChartsPayload, this.competitiveProfile.charts.hash)
        .subscribe({next: ({charts}) => {
          setTimeout(() => {
            this.competitiveChartsData.competitive = charts;
          }, 1000);
        }, error: (err) => {
            console.error(err);
            this.loadingCompetitiveProfile = false;
            throw err;
        }});
      this.subscriptions.add(competitiveCharts$)
    }
  }

  async onDownloadCharts(section: 'main' | 'competitive') {
    if(this.downloadingCharts) { return }
    this.downloadingCharts = true;
    await this.exportChartService.exportCharts(`.charts-container-${section} app-base-card-chart`, `PNG`, 2, `Competitive charts (${section})`,
      () => {
        document.documentElement.style.cursor = 'wait';
      }, () => {
        this.downloadingCharts = false;
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      }, (error: string) => {
        console.warn(error);
        this.downloadingCharts = false;
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      });
  }

  getMainChartSource(chartName: string){
    return  this.competitiveChartsData?.main[chartName] || null;
  }

  getCompetitiveChartSource(chartName: string){
    return  this.competitiveChartsData?.competitive[chartName] || null;
  }

  navigateToChart(chart: string){
    setTimeout(() => {
      if (document.querySelectorAll(`.charts-container-main ${chart}`).length>0) {
        document.querySelectorAll(`.charts-container-main ${chart}`)[0]
          .scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
      }
    });
  }
}
