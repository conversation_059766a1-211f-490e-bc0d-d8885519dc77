<div class="d-flex flex-column justify-content-start min-vh-100">
    <app-header></app-header>

    <!-- Analysis-->
    <div class="landscape-profiles">
      <div class="pt-4 mt-3">
        <div class="page-content container-fluid mb-5 d-flex justify-content-center" [hidden]="hideBanner">
            <div class="head-banner">
                <div class="banner-icon">
                    <img src="assets/images/landscape/competitive_banner.svg" alt="">
                </div>
                <div class="banner-content">
                    <div class="d-flex head-banner-title">
                        <span class="overflow-ellipsis">Competition benchmark for {{profile?.name}}</span>
                    </div>
                    <div class="mt-3 head-banner-text">
                        <span>
                            When it comes to innovation, Octimine helps you understand where you stand compared to your competitors.
                            Using our technology, we compare your portfolio with a second portfolio of highly similar patents.
                            Here you can navigate through the key insights and visuals.
                        </span>
                    </div>

                    <span class="close-banner-btn cursor-pointer" (click)="hideBanner=!hideBanner"><i class="fa-light fa-xmark"></i></span>
                </div>
            </div>
        </div>
      <div class="d-flex justify-content-center pt-5" *ngIf="profileStatus == 'COMPUTING'">
        <h5 class="message-computing">Please wait while your profile is being computed</h5>
        <app-spinner *ngIf="loading"></app-spinner>
      </div>

        <app-patent-list-layout [storeService]="landscapeStoreService"
                                [documents]="documents"
                                [isLoading]="loading"
                                [showDashboardActionBar]="true"
                                [menuItems]="tabMenuItems"
                                [alwaysShowDocumentsControlBar]="true">
          <ng-container alertMessages [ngTemplateOutlet]="noDocumentsMessage"></ng-container>

          <ng-container additionalTabsContent [ngTemplateOutlet]="additionalTabsContent"></ng-container>

          <ng-container documentsControlBar [ngTemplateOutlet]="landscapeControlBar"></ng-container>

          <ng-container documentsTable [ngTemplateOutlet]="landscapeList"></ng-container>

          <ng-container documentsVisual>
            <app-charts-container [isColumnLayout]="isCombinedMode" [storeService]="landscapeStoreService"></app-charts-container>
          </ng-container>
        </app-patent-list-layout>

        <app-filters-bar [alwaysBeSticky]="true" (filterRemoved)="onFilterRemoved($event)" [storeService]="landscapeStoreService" [hidden]="isCompetitiveMode"></app-filters-bar>
      </div>

      <app-footer-banner></app-footer-banner>
    </div>
    <!-- ./Analysis-->

    <app-footer></app-footer>
  </div>

  <app-zoom-chart [showFavoriteOption]="false" [storeService]="landscapeStoreService"></app-zoom-chart>

  <ng-template #landscapeControlBar>
    <div class="lcp-control-bar sticky-top">
      <div class="container-fluid">
        <app-patent-control-bar [columnsToShow]="columnsToShow"
                                [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                                [searchService]="landscapeService"
                                [hasFilterListControl]="true" (filterListEvent)="onAdvancedFilter($event)"
                                [hasHarmonizeControl]="hasDocuments" [hasSaveToCollectionControl]="hasDocuments"
                                [hasTemporaryLinkControl]="hasDocuments" [saveSearchTextInput]="profile?.name"
                                [hasExportControl]="hasDocuments" [exportDisplayOptions]="['csv','xlsx','pdf']"
                                [exportAdditionalParams]="{layout: 'landscape', title: 'Landscape report', subtitle: profile?.name}"
                                [storeService]="landscapeStoreService">
        </app-patent-control-bar>
      </div>
    </div>
  </ng-template>
  <ng-template #landscapeFooter>
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <a routerLink="/landscape" class="btn btn-primary  btn-md"><i class="fas fa-arrow-left p-0"></i> Dashboard</a>
      </div>
      <div class="d-flex justify-content-end align-items-center">
        <ng-container *ngIf="pagination && profileStatus == 'READY'" [hidden]="!hasDocuments">
          <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                         [pageOptions]="[25,50,100]"></app-page-size>
          <app-pagination [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
        </ng-container>
      </div>
    </div>
  </ng-template>
  <ng-template #landscapeList>
    <div class="container-fluid" [ngClass]="{'pe-0': isCombinedMode}">
      <div class="dataTable-wrap  result-table2" *ngIf="profileStatus == 'READY'">
        <app-spinner [hidden]="documents !== undefined"></app-spinner>

        <div class="new-layout-headline" *ngIf="hasDocuments">
          <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{pagination?.total_hits}}
          {{ 'patent family' | pluralize: pagination?.total_hits }}
          ({{pagination?.total_publications}} {{ 'publication' | pluralize: pagination?.total_publications }})
          in this portfolio
        </div>

        <div *ngIf="documents?.length > 0" class="psr-patent-table">
          <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()"
                     [message]="collectionsStoreService.getSaveToCollectionSuccess()"></app-alert>
          <app-patent-table [patents]="documents" (sort)="onSort($event)"
                            [pagination]="pagination" [hasLinksToBooleanSearch]="true"
                            backButtonTitle="Back to landscape" pathUrl="/patent" [linkData]="linkData"
                            [storeService]="landscapeStoreService" [showHighlight]="true">
          </app-patent-table>
        </div>

        <ng-container [ngTemplateOutlet]="landscapeFooter"></ng-container>
      </div>
    </div>
  </ng-template>

  <ng-template #noDocumentsMessage>
    <div *ngIf="!hasDocuments && !loading" class="container-fluid my-4">
      <app-alert type="warning" [hideCloseBtn]="true" [message]="getAlertMessage()">
      </app-alert>
    </div>
  </ng-template>

  <ng-template #additionalTabsContent>
    <div *ngIf="isCompetitiveMode && !hasCompetitiveProfile" class="container-fluid my-4 d-flex align-items-center">
        <div class="flex-fill pt-4">
          <app-alert type="warning" [hideCloseBtn]="true" [message]="computingCompetitionBenchmark ? 'Competition benchmark is being computed. Please check again in ~15 minutes' : 'Competition benchmark not ready yet'">
          </app-alert>
        </div>
        <a class="btn btn-primary btn-md ms-4 mt-1" *ngIf="userService.isAdmin() && !computingCompetitionBenchmark" (click)="onComputeCompetitiveProfile()">
          Compute competition benchmark
        </a>
    </div>
    <div *ngIf="isCompetitiveMode && hasCompetitiveProfile">
        <app-competition-benchmark [profile]="profile"></app-competition-benchmark>
    </div>
  </ng-template>
