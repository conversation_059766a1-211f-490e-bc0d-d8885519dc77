import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { HighchartsChartModule } from 'highcharts-angular';

import { CompetitiveLandscapeComponent } from './competitive-landscape.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('CompetitiveLandscapeComponent', () => {
  let component: CompetitiveLandscapeComponent;
  let fixture: ComponentFixture<CompetitiveLandscapeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CompetitiveLandscapeComponent ],
      imports: [
        HighchartsChartModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CompetitiveLandscapeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
