import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { PaginationMetadata } from '@core/services/semantic-search/types';
import { SortParams } from '@core/store/state';
import { ViewModeTypeEnum } from '@search/patent/types';
import { columnsToShow, selectedColumnsCombinedMode } from './columns';
import { Subscription, take } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { CollectionStoreService, LandscapeStoreService } from '@core/store';
import { AdvancedFilterService, ApplicantsAliasesService, CollaborationService, LandscapeService, NotificationsService, ToastService, UserService } from '@core/services';
import { CollaborationResourceTypeEnum, ExtraFilters, LandscapeProfile, Patent } from '@core/models';
import { get } from 'lodash';

@Component({
  selector: 'app-competitive-landscape',
  templateUrl: './competitive-landscape.component.html',
  styleUrls: ['./competitive-landscape.component.scss'],
})
export class CompetitiveLandscapeComponent implements OnInit, OnDestroy {
  pagination: PaginationMetadata;
  openedPatent = [];
  columnsToShow = columnsToShow;
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;
  sorting: SortParams = {field: null, order: null} as SortParams;
  pageSize = 25;
  profileStatus = '';
  hideBanner: boolean;

  private profileID: number;
  private intervalToLoadProfile;
  private loadingProfile: boolean = false;
  private intervalToLoadDocuments;
  private loadingDocuments: boolean = false;
  private intervalTime = 5000;
  private subscriptions = new Subscription();
  computingCompetitionBenchmark: boolean = false;

  tabMenuItems = [{
    id: 'competitive_landscape_mode',
    icon: 'competitive-nav-icon no-icon',
    title: 'Competition benchmark',
    show: true,
  },{
    id: 'list_mode',
    icon: 'list-nav-icon',
    title: 'List',
    show: true,
  },{
    id: 'analysis_mode',
    icon: 'visual-analysis-nav-icon',
    title: 'Visual analysis',
    show: true,
  },{
    id: 'combined_mode',
    icon: 'combined-nav-icon',
    title: 'Combined',
    show: true,
  }];

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    public landscapeStoreService: LandscapeStoreService,
    private notificationsService: NotificationsService,
    private collaborationService: CollaborationService,
    public landscapeService: LandscapeService,
    private toastService: ToastService,
    public collectionsStoreService: CollectionStoreService,
    private advancedFilterService: AdvancedFilterService,
    private applicantsAliasesService: ApplicantsAliasesService,
    public userService: UserService
  ) {
  }

  get loading() {
    return this.loadingProfile
      || this.intervalToLoadProfile
      || this.loadingDocuments
      || this.intervalToLoadDocuments;
  }

  get activePage() {
    return this.landscapeStoreService.activePage;
  }

  set activePage(state) {
    this.landscapeStoreService.activePage = state;
  }

  get profile() {
    return this.landscapeStoreService.landscapeProfile;
  }

  set profile(profile) {
    this.landscapeStoreService.landscapeProfile = profile;
  }

  get documents() {
    return this.landscapeStoreService.landscapeDocuments;
  }

  set documents(documents) {
    this.landscapeStoreService.landscapeDocuments = documents;
  }

  get isCombinedMode(): boolean {
    return !this.landscapeStoreService.patentListViewMode ||
      this.landscapeStoreService.patentListViewMode === ViewModeTypeEnum.COMBINED;
  }

  get isCompetitiveMode(): boolean {
    return this.landscapeStoreService.patentListViewMode === ViewModeTypeEnum.COMPETITIVE_LANDSCAPE;
  }

  get linkData() {
    return this.landscapeService.linkData;
  }

  get totalSelectedPatents() {
    return this.landscapeStoreService.selectedPublications.length;
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }

  get hasCompetitiveProfile(): boolean {
    return !!this.profile?.competitive_profile_id;
  }

  get isProfileComputing(): boolean {
    return this.profile.status === 'COMPUTING';
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.landscapeStoreService.chartSelectedValues = {};
    this.landscapeStoreService.resetChartDashboardType();
    this.toastService.clear();
    clearInterval(this.intervalToLoadProfile);
    clearInterval(this.intervalToLoadDocuments);
  }

  ngOnInit() {
    if (this.landscapeStoreService.backPatentSearch) {
      this.setFieldsAfterBack();
    } else {
      this.clearStoredValues();
    }

    if (this.route.snapshot.params.id > 0) {
      this.clearStoredData();
      if(this.route.snapshot.queryParams['view-mode']){
        this.landscapeStoreService.patentListViewMode = this.route.snapshot.queryParams['view-mode'];
      }
      this.landscapeStoreService.isCalculatingCharts = true;
      this.profileID = this.route.snapshot.params.id;
      this.landscapeStoreService.landscapeLoading = true;
      this.intervalToLoadProfile = (() => {
        this.loadProfile();
        return setInterval(() => this.loadProfile(), this.intervalTime);
      })();
      const markAsReadForResource$ = this.notificationsService.markAsReadForResource(this.profileID, 'LANDSCAPE_PROFILE')
        .pipe(take(1))
        .subscribe();
      this.subscriptions.add(markAsReadForResource$);

      const markAsRead$ = this.collaborationService.markAsRead(this.profileID, CollaborationResourceTypeEnum.LANDSCAPE)
        .pipe(take(1))
        .subscribe();
      this.subscriptions.add(markAsRead$);
    } else {
      this.router.navigateByUrl('/landscape');
    }

    const filters$ = this.landscapeStoreService.filters$.subscribe({
      next: filters => {
        if (this.profile && (this.documents || this.documents === undefined)) {
          this.activePage = 1;
          this.getDocuments();
        }
      }
    });
    this.subscriptions.add(filters$);

    const changeApplicantsEvent$ = this.applicantsAliasesService.changeApplicantsEvent.subscribe({
      next: (res) => {
        if (this.profile) {
          this.activePage = 1;
          this.getDocuments();
        }
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);
  }


  clearFilter() {
    this.advancedFilterService.reset();
    this.landscapeStoreService.resetAdvancedFilter();
    this.landscapeStoreService.setFilters([]);
  }

  navigate(page: number) {
    this.activePage = page;
    this.getDocuments();
  }

  getPublicationNumber(patent: Patent): string {
    const patentNumber = 'general.raw_publication_number';
    return get(patent, patentNumber, '').replace(/-|-/gi, '');
  }

  openDetail(patentIndex: number) {
    const elIndex = this.openedPatent.indexOf(patentIndex);
    if (elIndex === -1) {
      this.openedPatent.push(patentIndex);
    } else {
      this.openedPatent.splice(elIndex, 1);
    }
  }

  onFilterRemoved(filter: Object) {
  }

  onSort(val: SortParams) {
    this.sorting = val;
    this.getDocuments();
  }

  onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.activePage = 1;
    this.getDocuments();
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.getDocuments();
      this.landscapeStoreService.searchHash = this.landscapeStoreService.searchHash;
    }
  }

  getAlertMessage(): string {
    const hasFilters = this.landscapeStoreService.getAppliedFiltersQuery()?.length > 0;
    if (hasFilters) {
      return 'No documents match your filters. Try to clear your filters.';
    }

    return 'No document matched in this landscape profile.';
  }

  private loadProfile() {
    if (this.loadingProfile) {
      return;
    }
    this.loadingProfile = true;
    this.landscapeService.loadProfile(this.profileID).then(
      response => {
        this.profile = response as LandscapeProfile;
        this.loadingProfile = false;
        if (this.isProfileComputing) {
          this.profileStatus = this.profile.status;
          return;
        }
        clearInterval(this.intervalToLoadProfile);
        this.intervalToLoadProfile = undefined;
        this.intervalToLoadDocuments = (() => {
          this.getDocuments();
          return setInterval(() => this.getDocuments(), this.intervalTime);
        })()
        this.landscapeStoreService.state.total_hits = this.profile.matched_documents_count;
      }
    ).catch(
      error => {
        switch (error.status) {
          case 404:
            this.router.navigateByUrl('/landscape');
            break;
          default:
            console.error(error);
            throw error;
        }
        this.landscapeStoreService.landscapeLoading = false;
      }
    );
  }

  /**
   * get documents associated with active profile
   */
  private getDocuments() {
    if (this.loadingDocuments) {
      return;
    }
    this.loadingDocuments = true;
    this.openedPatent = [];
    this.documents = this.pagination = undefined;
    const query = this.buildQuery();
    const loadProfileDocument$ = this.landscapeService.loadProfileDocument(this.profile.id, query)
      .pipe(take(1))
      .subscribe({next: ({data}) => {
      this.profileStatus = data['status'];
      this.loadingDocuments = false;
      if (data['status'] === 'READY') {
        clearInterval(this.intervalToLoadDocuments);
        this.intervalToLoadDocuments = undefined;
        this.pagination = data['page'] as PaginationMetadata;
        this.documents = data['documents'];
        if (data['search_info'].search_hash !== this.landscapeStoreService.searchHash) {
          this.landscapeStoreService.searchHash = data['search_info'].search_hash;
        }
        this.landscapeStoreService.pagination = this.pagination;
        this.landscapeStoreService.search = {params: Object.assign({}, query)};
      }
    }, error: (err) => {
      this.loadingDocuments = false;
      console.error(err);
      throw err;
    }});
    this.subscriptions.add(loadProfileDocument$);
  }

  /**
   * buildQuery
   *
   * query builder for result table and chart section
   * @returns query array
   */
  private buildQuery(): Array<string> {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;
    query['show_general'] = 1;
    query['show_analytics'] = 1;
    query['show_tags'] = 1;
    if (this.sorting.order) {
      query['sort_order'] = this.sorting.order;
    }
    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
    }

    const filterQuery = this.landscapeStoreService.getAppliedFiltersQuery();

    if (filterQuery) {
      query['filters'] = filterQuery;
    }

    return query;
  }

  private clearStoredData() {
    this.landscapeStoreService.searchHash = null;
    this.landscapeStoreService.extraFilters = new ExtraFilters();
    this.landscapeStoreService.setFilters([], false);
    this.landscapeStoreService.selectedPublications = [];
    this.landscapeStoreService.selectedColumnsToShow = [];
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.landscapeStoreService.activeChartCategory = 'basic';
    this.landscapeStoreService.resetAdvancedFilter();
    this.landscapeStoreService.patentTableSort = {field: null, order: null} as SortParams;
    this.documents = undefined;
    this.profile = undefined;
    this.landscapeStoreService.setFilters([], false);
    this.landscapeStoreService.patentListViewMode = ViewModeTypeEnum.COMPETITIVE_LANDSCAPE;
  }

  private setFieldsAfterBack() {
    if (this.landscapeStoreService.patentTableSort) {
      this.sorting = {...this.landscapeStoreService.patentTableSort};
    }
    this.landscapeStoreService.backPatentSearch = false;
  }

  onComputeCompetitiveProfile() {
    this.computingCompetitionBenchmark = true;
    const computeBenchmark$ = this.landscapeService.computeCompetitiveProfile(this.profileID)
    .pipe(take(1))
    .subscribe({next: ({data}) => {
      this.computingCompetitionBenchmark = false;
    }});
    this.subscriptions.add(computeBenchmark$);
  }
}
