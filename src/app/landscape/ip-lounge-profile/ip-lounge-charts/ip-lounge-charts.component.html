<div class="chart-section tabs-container" id="landscape-chart-section">
  <div class="container-fluid chart-container ">
    <div class="d-flex justify-content-between" [ngClass]="isColumnLayout?'column-layout':'border-b-1 pt-4 mb-3'">
        <div class="tabs-title section-title-text" [class.align-self-center]="isColumnLayout">Visual analysis</div>
        <div *ngIf="!isColumnLayout" class="d-flex justify-content-end position-relative charts-control-bar"  style="min-width: 250px;">
          <div class="charts-control-option list-btn ms-4" [class.disabled]="arrangeDashBoard" ngbDropdown placement="bottom-end" container="body">
            <div class="cursor-pointer text-end" ngbDropdownToggle>
              <span>Charts <i class="fa-solid fa-ellipsis-vertical charts-control-option-icon"></i></span>
            </div>
            <div class="" ngbDropdownMenu>
              <a class="dropdown-item px-3" href="javascript:void(0)" (click)="saveTabs()" ngbTooltip="Click here to save the VISUAL ANALYSIS display setting" container="body"><i class="fas chart-section-btn fa-bookmark"></i> Save tab selection</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item disabled px-3" href="javascript:void(0)">Download all charts as:</a>
              <div class="dropdown-item px-3 d-flex justify-content-between">
                <div class="d-flex justify-content-start align-items-center" *ngFor="let type of ['JPEG', 'PNG', 'PDF', 'SVG']; let last = last;" [ngClass]="{'me-1': !last}">
                  <ng-container [ngTemplateOutlet]="downloadingSpinnerTemplate" *ngIf="isDownloading(type)"></ng-container>
                  <a href="javascript:void(0)" (click)="onDownloadChart(type)" [ngClass]="{'ms-1': isDownloading(type)}">{{type}}</a>
                </div>
              </div>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item px-3 d-flex justify-content-start align-items-center" href="javascript:void(0)" [class.event-inprogress]="isDownloading('PRINT')" (click)="onDownloadChart('PRINT')">
                <i class="fa-solid fa-print" *ngIf="!isDownloading('PRINT')"></i>
                <ng-container [ngTemplateOutlet]="downloadingSpinnerTemplate" *ngIf="isDownloading('PRINT')"></ng-container>
                <div class="ms-2">Print chart</div>
              </a>
            </div>
          </div>
        </div>
    </div>
      <!--Charts wrap-->
      <div class="charts-wrap charts-container">
        <div class="row">
            <div [ngClass]="isColumnLayout ? 'col-md-12' : 'col-md-6'" class="mb-4">
                <app-green-categories [storeService]="landscapeStoreService"></app-green-categories>
            </div>
            <div [ngClass]="isColumnLayout ? 'col-md-12' : 'col-md-6'" class="mb-4">
                <app-green-families-timeline [storeService]="landscapeStoreService"></app-green-families-timeline>
            </div>
            <div [ngClass]="isColumnLayout ? 'col-md-12' : 'col-md-6'" class="mb-4">
              <app-green-technologies-landscape [storeService]="landscapeStoreService"></app-green-technologies-landscape>
            </div>
            <div [ngClass]="isColumnLayout ? 'col-md-12' : 'col-md-6'" class="mb-4">
              <app-green-ranking [storeService]="landscapeStoreService" [disableFilter]="true"></app-green-ranking>
            </div>
        </div>
      </div>
      <!--./Charts wrap-->
  </div>
</div>

<ng-template #downloadingSpinnerTemplate>
  <img [ngSrc]="'assets/images/octimine_blue_spinner.gif'" alt="Download charts" ngbTooltip="Downloading charts" container="body" width="20" height="20" />
</ng-template>
