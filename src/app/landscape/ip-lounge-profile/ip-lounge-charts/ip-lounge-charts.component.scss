@import 'scss/components/chart_tabs';

.filter-single {
  border-bottom: 1px solid #FFF;
  span {
    color: #FFF; font-size: 0.9rem;
  }
}

.noUi-connect {
  background: #CF7900;
}

.noUi-connects {
  border-radius: 10px;
}

.noUi-horizontal {
  height: 7px;
}

.noUi-horizontal .noUi-handle {
  width: 20px;
  height: 20px;
  top: -9px;
  border-radius: 50%;
  border-color: #fff;
  background: #fff;
}

.noUi-horizontal .noUi-handle:before,
.noUi-horizontal .noUi-handle:after {
  display: none;
}

.noUi-touch-area {
  cursor: pointer;
}

.noUi-touch-area:focus,
.noUi-horizontal .noUi-handle:focus {
  outline: 0px;
}

.noUi-target {
  background: #00526E;
  border-radius: 10px;
  border: 1px solid #006A8E;
  box-shadow: none;
}

.noUi-tooltip {
  display: none;
}

.noUi-active .noUi-tooltip {
  display: block;
}

.range-slider-wrap {
  padding: 30px 0px;
}

.full-width {
  width: 100%;
}

.chart-single {
  margin-bottom: 30px;
}

.chart-box {
  display: none;
}

.chart-box.active {
  display: block;
}

.btn-competitors {
  padding: 3px;
  background-color: #efefef;
  margin: -1px 3px 3px 3px;
  font-size: .8rem;
}
.competitor-chart-filter {
  background-color: #FFFFFF;

  .quality-input {
    width: 200px;
  }
}

.chart-section-header .btn-primary-outline {
  color: #FFF;
  border-color: #FFF;
  &:hover {
    border-color: #00a083;
  }
}

.chart-content {
  min-width: 504px;
}
