import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { LandscapeStoreService } from '@core/store';
import { ChartsService, ExportChartService } from '@core/services';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-ip-lounge-charts',
  templateUrl: './ip-lounge-charts.component.html',
  styleUrls: ['./ip-lounge-charts.component.scss']
})
export class IpLoungeChartsComponent implements OnInit, OnDestroy {
  @Input() isColumnLayout?: boolean;

  private subscriptions = new Subscription();

  constructor(
    public landscapeStoreService: LandscapeStoreService,
    private chartService: ChartsService,
    private modalService: NgbModal,
    private exportChartService: ExportChartService
  ) {
  }

  /**
   * loading reference
   */
  get loading() {
    return this.landscapeStoreService.landscapeLoading;
  }

  set loading(state) {
    this.landscapeStoreService.landscapeLoading = state;
  }

  get documents() {
    return this.landscapeStoreService.landscapeDocuments;
  }

  ngOnInit() {

    const filters$ = this.landscapeStoreService.filters$.subscribe({
      next: filters => {
        const self = this;
        if (this.landscapeStoreService.searchHash && !this.landscapeStoreService.isCalculatingCharts) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(filters$);

    const searchHash$ = this.landscapeStoreService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.chartService.resetCharts();
    this.landscapeStoreService.chartSelectedValues = {};
    this.chartService.resetCharts();
  }

  /**
   * get chart datasource form the Microservice
   */
  public getCharts() {
    this.landscapeStoreService.isCalculatingCharts = true;
    const payload = this.buildPayload();

    const calculate$ = this.chartService.calculate(payload, this.landscapeStoreService.searchHash)
      .pipe(take(1))
      .subscribe({
        next: ({charts}) => {
          this.landscapeStoreService.isCalculatingCharts = false;
        },
        error: (err) => {
          console.log(err);
          this.landscapeStoreService.isCalculatingCharts = false;
          throw err;
        }
      });
    this.subscriptions.add(calculate$);
  }

  async onDownloadChart(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT') {
    await this.exportChartService.exportCharts('.tab-pane.active app-base-card-chart', type, 2, this.getDownloadChartName(type),
      () => {
        document.documentElement.style.cursor = 'wait';
      }, () => {
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      }, (error: string) => {
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      });
  }

  private buildPayload() {
    const payload = {
      charts: [
        'green_categories',
        'green_families_timeline',
        'green_technologies_landscape',
        'green_ranking'
      ],
      'parameters': {}
    };

    const freeTextQuery = this.landscapeStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    return payload;
  }

  isDownloading(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT'): boolean {
    return this.exportChartService.isExporting(this.getDownloadChartName(type), type, true);
  }

  private getDownloadChartName(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT'): string {
    return this.landscapeStoreService.getDownloadChartsName(type, null, 'IP Lounge');
  }
}
