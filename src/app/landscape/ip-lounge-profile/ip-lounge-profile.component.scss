@import 'scss/layout2021/variables';
@import 'scss/layout2021/tooltip';

.state {
  color: $brand-gray;
  margin-right: 40px;
  position: relative;
  font-family: $font-open-sans-regular;
  font-size: 0.9rem;

  &.active {
    color: black; // $blue-light;
    font-family: $font-open-sans-bold;
  }

  img {
    width: 50px;
  }

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -30px;
    width: 30px;
    height: 1px;
    background: #d5d5d5;
    margin-left: -40px;
  }

  &:last-child {
    margin-right: 0;

    &::after {
      content: none;
      width: 0;
    }
  }

  span {
    display: block;
    text-transform: uppercase;
  }
}

#unmappedModal {
  a.badge {
    color: $color-text-01;

    &.bg-primary {
      background: $brand-green;
    }
  }

  .patent-list {
    border-top: 1px solid $brand-green;
    padding-top: 10px;
    margin-top: 10px;
  }
}
.highlights {
  padding: 10px 0 5px;
  font-size: 1.3em;
  margin: 0;
}
.bg-green {
  padding: 0 1.5rem;
  margin: 0 -1.5rem;
  background: $brand-green;
  .highlights{
    color: $color-text-01;
  }
}

.box-bibliographic {
  width: 25%;
  padding: 15px;
  .content {
    width: 65%;
  }
}

.info-title {
  font-size: 1rem;
  font-weight: bold;
}

.info-label {
  color: #688A95;
}

.info-value {
  font-weight: bold;
  color: #0F2C35;
}

.high-title {
  color: #698A95;
  font-weight: bold;
  display: block;
}

.chart-card {
  box-shadow: 0px 3px 9px #EAEAEA;
  border-radius: 3px;
  opacity: 1;
  width: 36%;
}


.chart-analytics {
  box-shadow: 0px 3px 9px #00000008;
  border: 1px solid #ECECEC;
  border-radius: 3px;
  opacity: 1;
  width: 48%;
}
::ng-deep {
  .analytic-radar-chart {
      background: #FFF !important;
      margin-bottom: 0px !important;
      height: 410px !important;

      .graph-wrap {
          height: 100% !important;
      }
  }
}

.landscape-profiles {
  .single-info-card {
    width: 50%;
    .content {
      height: 100%;
      position: relative;

      .information-tooltip {
        position: absolute;
        right: 1rem;
        top: 1rem;
      }
    }
  }
}

.profile-result {
  background-color: #f8fbfcb5;

}

.head-banner {
  width: 1030px;
  padding: 20px 30px;
  background-image: url('/assets/images/background-green-analysis.jpg');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  color: #0F2C35;
  &-title {
    font-family: Open Sans Bold;
    font-size: 1.2rem;
    .green {
      color: #15BC31;
    }
  }

  &-text {
    font-family: Open Sans Regular;
  }
}
