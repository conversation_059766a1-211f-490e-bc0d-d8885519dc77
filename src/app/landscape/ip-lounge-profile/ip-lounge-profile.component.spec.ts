import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { GreenTechnologiesLandscapeComponent } from '@shared/charts/landscape-charts/green-technologies-landscape/green-technologies-landscape.component';
import { SharedModule } from '@shared/shared.module';
import { HighchartsChartModule } from 'highcharts-angular';

import { IpLoungeProfileComponent } from './ip-lounge-profile.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('IpLoungeProfileComponent', () => {
  let component: IpLoungeProfileComponent;
  let fixture: ComponentFixture<IpLoungeProfileComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ GreenTechnologiesLandscapeComponent ],
      imports: [
        HighchartsChartModule,
        NgxSliderModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(IpLoungeProfileComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
