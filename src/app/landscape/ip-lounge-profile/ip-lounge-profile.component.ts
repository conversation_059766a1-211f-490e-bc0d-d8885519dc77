import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { get } from 'lodash';
import { Subscription, take } from 'rxjs';
import { Options } from '@angular-slider/ngx-slider';
import { columnsToShow, selectedColumnsCombinedMode } from './columns';
import {
  AdvancedFilterService,
  ApplicantsAliasesService,
  ChartsService,
  CollaborationService,
  LandscapeService,
  NotificationsService,
  PaginationMetadata,
  PatentClassificationService
} from '@core/services';
import { CollaborationResourceTypeEnum, ExtraFilters, LandscapeProfile, Patent } from '@core/models';
import { CollectionStoreService, LandscapeStoreService, SortParams } from '@core/store';
import { generateCpcIpcDescription } from '@shared/charts';
import { tooltipsText } from '@landscape/profile/tooltip';
import { ViewModeTypeEnum } from '@search/patent/types';

declare var $: any;

@Component({
  selector: 'app-ip-lounge-profile',
  templateUrl: './ip-lounge-profile.component.html',
  styleUrls: ['./ip-lounge-profile.component.scss']
})
export class IpLoungeProfileComponent implements OnInit, OnDestroy {

  pagination: PaginationMetadata;
  openedPatent = [];
  startYear: number = 1993;
  endYear: number = 2008;
  sliderOptions: Options = {
    floor: this.startYear as number,
    ceil: this.endYear as number,
    showSelectionBar: true,
    getSelectionBarColor: (value: number): string => {
      return '#389C87';
    }
  };
  columnsToShow = columnsToShow;
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;
  sorting: SortParams = {field: null, order: null} as SortParams;
  pageSize = 25;
  profileStatus = '';
  tooltip = tooltipsText;

  private top3CPCDescriptions = {};
  private profileID: number;
  private unMappedPatents: Array<string>;
  private isSearching = false;
  private intervalToLoadProfile;
  private intervalToLoadDocuments;
  private intervalTime = 5000;
  private subscriptions = new Subscription();

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    public landscapeStoreService: LandscapeStoreService,
    private notificationsService: NotificationsService,
    private collaborationService: CollaborationService,
    public landscapeService: LandscapeService,
    public collectionsStoreService: CollectionStoreService,
    private patentClassificationService: PatentClassificationService,
    private advancedFilterService: AdvancedFilterService,
    private applicantsAliasesService: ApplicantsAliasesService,
    private chartService: ChartsService,
  ) {
  }

  get loading() {
    return this.landscapeStoreService.landscapeLoading;
  }

  set loading(state) {
    this.landscapeStoreService.landscapeLoading = state;
  }

  get activePage() {
    return this.landscapeStoreService.activePage;
  }

  set activePage(state) {
    this.landscapeStoreService.activePage = state;
  }

  get profile() {
    return this.landscapeStoreService.landscapeProfile;
  }

  set profile(profile) {
    this.landscapeStoreService.landscapeProfile = profile;
  }

  get documents() {
    return this.landscapeStoreService.landscapeDocuments;
  }

  set documents(documents) {
    this.landscapeStoreService.landscapeDocuments = documents;
  }

  get isCombinedMode(): boolean {
    return !this.landscapeStoreService.patentListViewMode ||
      this.landscapeStoreService.patentListViewMode === ViewModeTypeEnum.COMBINED;
  }

  get linkData() {
    return this.landscapeService.linkData;
  }

  get totalSelectedPatents() {
    return this.landscapeStoreService.selectedPublications.length;
  }

  get hasDocuments(): boolean {
    return this.documents && this.documents.length > 0;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.landscapeStoreService.isGreenReport = false;
    this.landscapeStoreService.activeChartCategory = '';
  }

  ngOnInit() {
    this.landscapeStoreService.isGreenReport = true;
    this.landscapeStoreService.activeChartCategory = 'sustainability';

    if (this.landscapeStoreService.backPatentSearch) {
      this.setFieldsAfterBack();
    } else {
      this.clearStoredValues();
    }

    if (this.route.snapshot.params.id > 0) {
      this.clearStoredData();
      this.landscapeStoreService.isCalculatingCharts = true;
      this.profileID = this.route.snapshot.params.id;
      this.loading = true;
      this.intervalToLoadProfile = (() => {
        this.loadProfile();
        return setInterval(() => this.loadProfile(), this.intervalTime);
      })();
      const markAsReadForResource$ = this.notificationsService.markAsReadForResource(this.profileID, 'LANDSCAPE_PROFILE')
        .pipe(take(1))
        .subscribe();
      this.subscriptions.add(markAsReadForResource$);

      const markAsRead$ = this.collaborationService.markAsRead(this.profileID, CollaborationResourceTypeEnum.LANDSCAPE)
        .pipe(take(1))
        .subscribe();
      this.subscriptions.add(markAsRead$);
    } else {
      this.router.navigateByUrl('/landscape');
    }

    const filters$ = this.landscapeStoreService.filters$.subscribe({
      next: filters => {
        if (this.profile && (this.documents || this.documents === undefined)) {
          this.activePage = 1;
          this.getDocuments();
        }
      }
    });
    this.subscriptions.add(filters$);

    const changeApplicantsEvent$ = this.applicantsAliasesService.changeApplicantsEvent.subscribe({
      next: (res) => {
        if (this.profile) {
          this.activePage = 1;
          this.getDocuments();
        }
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);

    const searchHash$ = this.landscapeStoreService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);
  }

  public getCharts() {
    this.landscapeStoreService.isCalculatingCharts = true;
    const payload = this.buildPayload();

    const calculate$ = this.chartService.calculate(payload, this.landscapeStoreService.searchHash)
      .pipe(take(1))
      .subscribe({
        next: ({charts}) => {
          this.landscapeStoreService.isCalculatingCharts = false;
        },
        error: (err) => {
          console.log(err);
          this.landscapeStoreService.isCalculatingCharts = false;
          throw err;
        }
      });
    this.subscriptions.add(calculate$);
  }

  private buildPayload() {
    const payload = {
      charts: [
        'green_categories',
        'green_families_timeline',
        'green_technologies_landscape',
        'green_ranking'
      ],
      'parameters': {}
    };

    const freeTextQuery = this.landscapeStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    return payload;
  }

  clearFilter() {
    this.advancedFilterService.reset();
    this.landscapeStoreService.resetAdvancedFilter();
    this.landscapeStoreService.setFilters([]);
    this.setNewLimits();
  }

  navigate(page: number) {
    this.activePage = page;
    this.getDocuments();
  }

  getPublicationNumber(patent: Patent): string {
    const patentNumber = 'general.raw_publication_number';
    return get(patent, patentNumber, '')
      .replace(/-|-/gi, '');
  }

  openDetail(patentIndex: number) {
    const elIndex = this.openedPatent.indexOf(patentIndex);
    if (elIndex === -1) {
      this.openedPatent.push(patentIndex);
    } else {
      this.openedPatent.splice(elIndex, 1);
    }
  }

  setNewLimits(): void {
    // Due to change detection rules in Angular, we need to re-create the options object to apply the change
    this.startYear = (new Date(this.profile.oldest_priority_date?.toString())).getFullYear();
    this.endYear = (new Date(this.profile.newest_priority_date?.toString())).getFullYear();
    const newOptions: Options = Object.assign({}, this.sliderOptions);
    newOptions.floor = this.startYear as number;
    newOptions.ceil = this.endYear as number;
    this.sliderOptions = newOptions;
  }

  onFilterRemoved(filter: Object) {
    if (filter['title'] === 'Priority Year') {
      this.setNewLimits();
    }
  }

  onSort(val: SortParams) {
    this.sorting = val;
    this.getDocuments();
  }

  onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.activePage = 1;
    this.getDocuments();
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.getDocuments();
      this.landscapeStoreService.searchHash = this.landscapeStoreService.searchHash;
    }
  }

  getAlertMessage(): string {
    const hasFilters = this.landscapeStoreService.getAppliedFiltersQuery()?.length > 0;
    if (hasFilters) {
      return 'No documents match your filters. Try to clear your filters.';
    }

    return 'No document matched in this landscape profile.';
  }

  private loadProfile() {
    if (this.isSearching) return;
    this.isSearching = true;

    this.landscapeService.loadProfile(this.profileID).then(
      response => {
        this.profile = response as LandscapeProfile;
        this.isSearching = false;

        if (this.profile.status === 'COMPUTING') {
          this.profileStatus = this.profile.status;
          return;
        }
        clearInterval(this.intervalToLoadProfile);
        this.setNewLimits();

        this.intervalToLoadDocuments = (() => {
          this.getDocuments();
          return setInterval(() => this.getDocuments(), this.intervalTime);
        })()
        this.landscapeStoreService.state.total_hits = this.profile.matched_documents_count;
        this.unMappedPatents = null;
        if (this.profile.processed_input.not_found && this.profile.processed_input.invalid) {
          this.unMappedPatents = [...this.profile.processed_input.not_found, ...this.profile.processed_input.invalid];
        }
        this.loadTop3CPCDescriptions();
      }
    ).catch(
      error => {
        switch (error.status) {
          case 404:
            this.router.navigateByUrl('/landscape');
            break;
          default:
            console.error(error);
            throw error;
        }
        this.loading = false;
        this.isSearching = false;
      }
    );
  }

  /**
   * get documents associated with active profile
   */
  private getDocuments() {
    if (this.isSearching) return;
    this.isSearching = true;

    this.openedPatent = [];
    this.documents = this.pagination = undefined;
    const query = this.buildQuery();
    const loadProfileDocument$ = this.landscapeService.loadProfileDocument(this.profile.id, query)
      .pipe(take(1))
      .subscribe({next: ({data}) => {
      this.isSearching = false;
      this.profileStatus = data['status'];
      if (data['status'] === 'READY') {
        this.loading = false;
        clearInterval(this.intervalToLoadDocuments);
        this.pagination = data['page'] as PaginationMetadata;
        this.documents = data['documents'];
        if (data['search_info'].search_hash !== this.landscapeStoreService.searchHash) {
          this.landscapeStoreService.searchHash = data['search_info'].search_hash;
        }
        this.landscapeStoreService.pagination = this.pagination;
        this.landscapeStoreService.search = {params: Object.assign({}, query)};
      }
    }, error: (err) => {
      this.loading = false;
      this.isSearching = false;
      console.error(err);
      throw err;
    }});
    this.subscriptions.add(loadProfileDocument$);
  }

  /**
   * buildQuery
   *
   * query builder for result table and chart section
   * @returns query array
   */
  private buildQuery(): Array<string> {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;
    query['show_general'] = 1;
    query['show_analytics'] = 1;
    query['show_tags'] = 1;
    if (this.sorting.order) {
      query['sort_order'] = this.sorting.order;
    }
    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
    }

    const filterQuery = this.landscapeStoreService.getAppliedFiltersQuery();

    if (filterQuery) {
      query['filters'] = filterQuery;
    }

    return query;
  }

  private clearStoredData() {
    this.landscapeStoreService.searchHash = null;
    this.landscapeStoreService.extraFilters = new ExtraFilters();
    this.landscapeStoreService.setFilters([], false);
    this.landscapeStoreService.selectedPublications = [];
    this.landscapeStoreService.selectedColumnsToShow = [];
  }

  private loadTop3CPCDescriptions() {
    if (this.profile?.top3_cpc_codes) {
      const params = {classification_symbol: 'in:' + this.profile.top3_cpc_codes.join(','), page_size: 100};

      const getCpc$ = this.patentClassificationService.getCpc(params)
        .pipe(take(1))
        .subscribe({
          next: (classifications) => {
            this.top3CPCDescriptions = classifications.data.results;

            const self = this;
            $('.top3-cpc-description-tooltip-selector').tooltip('dispose').tooltip({
              html: true,
              title: function () {
                const cpc = $(this).attr('cpc');
                return self.getTop3CPCTooltip(cpc);
              },
              template: '<div class="tooltip ipc-cpc-tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',
              trigger: 'mouseenter click'
            });
          }
        });
      this.subscriptions.add(getCpc$);
    }
  }

  private getTop3CPCDescription(cpc: string): string {
    const item = this.top3CPCDescriptions[cpc];
    return item ? generateCpcIpcDescription(item['descriptions'], item['title'] || cpc, 500) : '';
  }

  private getTop3CPCTooltip(cpc: string): string {
    const desc = this.getTop3CPCDescription(cpc);
    return `<div class="fw-bold text-start">${cpc}</div><div class="text-start">${desc}</div>`;
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.landscapeStoreService.resetAdvancedFilter();
    this.landscapeStoreService.patentTableSort = {field: null, order: null} as SortParams;
    this.documents = undefined;
    this.profile = undefined;
    this.landscapeStoreService.setFilters([], false);
    this.landscapeStoreService.patentListViewMode = ViewModeTypeEnum.COMBINED;
  }

  private setFieldsAfterBack() {
    if (this.landscapeStoreService.patentTableSort) {
      this.sorting = {...this.landscapeStoreService.patentTableSort};
    }
    this.landscapeStoreService.backPatentSearch = false;
  }
}
