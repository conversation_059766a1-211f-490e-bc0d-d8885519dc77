import { IpLoungeProfileComponent } from './ip-lounge-profile/ip-lounge-profile.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard, FeatureGuard } from '@core/guards';
import { LandscapeComponent } from './landscape/landscape.component';
import { ProfileComponent } from './profile/profile.component';
import { ProfileEditComponent } from './profile-edit/profile-edit.component';
import { ProfileDraftComponent } from './profile-draft/profile-draft.component';
import { CompetitiveLandscapeComponent } from './competitive-landscape/competitive-landscape.component';

const routes: Routes = [
  {path: '', component: LandscapeComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'new', component: ProfileEditComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'edit/:id', component: ProfileEditComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'profile/:id', component: ProfileComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'draft/:id', component: ProfileDraftComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'green-report/:id', component: IpLoungeProfileComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'competitive-landscape/:id', component: CompetitiveLandscapeComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'competition-benchmark/:id', component: CompetitiveLandscapeComponent, canActivate: [AuthGuard, FeatureGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LandscapeRoutingModule {
}
