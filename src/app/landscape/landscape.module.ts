import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LandscapeComponent } from './landscape/landscape.component';
import { LandscapeRoutingModule } from './landscape-routing.module';
import { SharedModule } from '@shared/shared.module';
import { ProfileComponent } from './profile/profile.component';
import { ProfileEditComponent } from './profile-edit/profile-edit.component';
import { ProfileCardComponent } from './landscape/profile-card/profile-card.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { ProfileDraftComponent } from './profile-draft/profile-draft.component';
import { NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { UnmappedPatentDialogComponent } from './unmapped-patent-dialog/unmapped-patent-dialog.component';
import { ChartDashboardModule, LandscapeChartsModule } from '@shared/charts';
import { IpLoungeProfileComponent } from './ip-lounge-profile/ip-lounge-profile.component';
import { IpLoungeChartsComponent } from './ip-lounge-profile/ip-lounge-charts/ip-lounge-charts.component';
import { ChartsModule } from '@shared/charts/charts.module';
import { CompetitiveLandscapeComponent } from './competitive-landscape/competitive-landscape.component';
import { CompetitionBenchmarkComponent } from './competition-benchmark/competition-benchmark.component';


@NgModule({
  declarations: [
    LandscapeComponent,
    ProfileComponent,
    ProfileEditComponent,
    ProfileCardComponent,
    ProfileDraftComponent,
    UnmappedPatentDialogComponent,
    IpLoungeProfileComponent,
    IpLoungeChartsComponent,
    CompetitiveLandscapeComponent,
    CompetitionBenchmarkComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    NgxSliderModule,
    NgbDropdownModule,
    LandscapeRoutingModule,
    NgbTooltipModule,
    ChartsModule,
    LandscapeChartsModule,
    ChartDashboardModule,
  ],
  exports: [
    ChartsModule,
    LandscapeChartsModule,
    ChartDashboardModule
  ]
})
export class LandscapeModule {
}
