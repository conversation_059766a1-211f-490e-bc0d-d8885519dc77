<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="bg-gray">
    <!-- Nav tabs -->
    <ul ngbNav [activeId]="'Landscape'" class="pb-0 mb-0 nav-tabs main-tabs nav-1 container d-flex justify-content-center">
      <li [ngbNavItem]="'Landscape'">
        <a ngbNavLink>Landscape</a>
      </li>
    </ul>
    <!-- /Nav tabs -->
  </div>

  <div class="flex-fill clt-container">
    <app-spinner *ngIf="loading"></app-spinner>
    <div class="page-content pb-4 container" *ngIf="!loading">
        <app-page-bar pageTitle="My Profiles">
            <div leftSide>
              <div class="d-flex justify-content-start align-items-center">
                <div class="input-group justify-content-between align-items-end col-5 ps-0 me-3" >
                  <input type="text" class="form-control border-end-0 flex-fill filter-input" placeholder="Search for an existing profile" [(ngModel)]="filter" (ngModelChange)="onChangeFilter()">
                  <button ngbTooltip="Search for a term" class="btn border-start-0 filter-button" (click)="filterProfile()"><i class="fa fa-search icon"></i></button>
                </div>
    
                <span >SORT BY:</span>
                <div ngbDropdown class="position-relative mx-2">
                  <div ngbDropdownToggle tabindex="2" class=" caret-off">
                    <input class="form-control" [value]="getSortLabel()" readonly style="cursor: pointer;">
                    <div class="dropdown-icon"></div>
                  </div>
                  <div ngbDropdownMenu>
                    <a *ngFor="let item of listSort" class="dropdown-item" [ngClass]="item.value === sortBy ? 'active' : ''"
                       (click)="onSortBy(item.value)">{{ item.label }}</a>
                  </div>
                </div>
    
                &nbsp;
                <a (click)="onSortOrder(sortOrder!='desc' ? 'desc' : 'asc')"  class="sort-order " href="javascript:void(0)">
                  <i class="fas fa-sort-amount-down-alt" [hidden]="sortOrder!='desc'"></i>
                  <i class="fas fa-sort-amount-up-alt" [hidden]="sortOrder!='asc'"></i>
                </a>
              </div>
            </div>
  
            <ng-container rightSide>
              <span *ngIf="pagination && pagination.total_hits > 0" class="d-flex align-content-center flex-wrap count-stat color-2">
                {{ pagination | countStat }}
              </span>
            </ng-container>
        </app-page-bar>
        <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
        <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>

        <div class="row" *ngIf="userService.canCreateLandscapeProfile()">
            <div class="col-8" [hidden]="landscapeProfiles.length===0">
                <a [routerLink]="['new']" class="btn btn-primary btn-landscape btn-md"><i class="fas fa-plus p-0"></i> NEW LANDSCAPE</a>
            </div>
            <div class="col-4 text-end" [hidden]="landscapeProfiles.length===0">
                <app-tooltip id='landscape'
                     [tooltipText]='tooltip.text.landscape'
                      tooltipIconSize="sm">
                </app-tooltip>
            </div>
        </div>

        <div class="analysis-steps-wrap" [hidden]="landscapeProfiles.length > 0">
          <ng-container  *ngIf="userService.canCreateLandscapeProfile()">
            <div class="analysis-step-top text-center" [hidden]="!searchAll">
                <h4 class="text-gray">Please click on the new landscape button to create your first profile</h4>
                <a [routerLink]="['new']" class="btn btn-primary btn-lg"><i class="fas fa-plus p-0"></i> NEW LANDSCAPE</a>
            </div>
            <app-alert type="info" *ngIf="!searchAll"
                       [message]="'We couldn\'t find any landscape profile matching ' + filter">
            </app-alert>
          </ng-container>
            <ul class="spets-list d-flex" *ngIf="searchAll && sharedProfiles.length === 0 && landscapeProfiles.length === 0">
                <li>
                    <div class="thumb">
                        <img src="assets/images/info-icon.svg" alt="">
                    </div>
                    <p class="thumb-title">INFORMATION SETUP</p>
                    <p class="thumb-description">
                        Setup queries & patent lists to retrieve patents for analysis.
                    </p>
                </li>
                <li>
                    <div class="thumb">
                        <img src="assets/images/auto.svg" alt="">
                    </div>
                    <p class="thumb-title">CONFIRM SELECTION</p>
                    <p class="thumb-description">
                        Review the retrieved documents to include them into the final landscape.
                    </p>
                </li>
                <li>
                    <div class="thumb">
                        <img src="assets/images/analysis-icon.svg" alt="">
                    </div>
                    <p class="thumb-title">ANALYZE LANDSCAPE</p>
                    <p class="thumb-description">
                        Use analytics and explore the landscape to gain deeper insights.
                    </p>
                </li>
            </ul>
        </div>
        <div class="landscape-boxes">
            <div class="cards-wrap">
                <app-landscape-profile-card class="single-card landscape-profile-card " [profile]="p" *ngFor="let p of landscapeProfiles" (delete)="onDelete($event)" (shareProfile)="openShareWith($event)"></app-landscape-profile-card>
            </div>
        </div>
        

        <div class="row" *ngIf="pagination && landscapeProfiles.length > 0">
            <div class="col-lg-12">
                <app-pagination [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
            </div>
        </div>

      <div *ngIf="sharedProfiles && sharedProfiles.length > 0" class="mt-5">
        
        <app-page-bar pageTitle="Shared Profiles">
            <div leftSide>
              <div class="d-flex justify-content-start align-items-center" *ngIf="isFreeUser">
                <div class="input-group justify-content-between align-items-end col-5 ps-0 me-3" >
                  <input type="text" class="form-control border-end-0 flex-fill filter-input" placeholder="Search for an existing profile" [(ngModel)]="filter" (ngModelChange)="onChangeFilter()">
                  <button ngbTooltip="Search for a term" class="btn border-start-0 filter-button" (click)="filterProfile()"><i class="fa fa-search icon"></i></button>
                </div>
    
                <span >SORT BY:</span>
                <div ngbDropdown class="position-relative mx-2">
                  <div ngbDropdownToggle tabindex="2" class=" caret-off">
                    <input class="form-control" [value]="getSortLabel()" readonly style="cursor: pointer;">
                    <div class="dropdown-icon"></div>
                  </div>
                  <div ngbDropdownMenu>
                    <a *ngFor="let item of listSort" class="dropdown-item" [ngClass]="item.value === sortBy ? 'active' : ''"
                       (click)="onSortBy(item.value)">{{ item.label }}</a>
                  </div>
                </div>
                &nbsp;
                <a (click)="onSortOrder(sortOrder!='desc' ? 'desc' : 'asc')"  class="sort-order " href="javascript:void(0)">
                  <i class="fas fa-sort-amount-down-alt" [hidden]="sortOrder!='desc'"></i>
                  <i class="fas fa-sort-amount-up-alt" [hidden]="sortOrder!='asc'"></i>
                </a>
              </div>
            </div>
  
            <ng-container rightSide>
              <span *ngIf="sharedPagination && sharedPagination.total_hits > 0 " class="d-flex align-content-center flex-wrap count-stat color-2">
                {{ sharedPagination | countStat }}
              </span>
            </ng-container>
        </app-page-bar>
        <app-alert type="info" message="No Landscape profile have been shared with you yet"
                   *ngIf="sharedProfiles.length === 0"></app-alert>

        <div class="landscape-boxes">
            <div class="cards-wrap">
                <app-landscape-profile-card class="single-card landscape-profile-card "  [sharedProfile]="true" [profile]="p" *ngFor="let p of sharedProfiles"></app-landscape-profile-card>
            </div>
        </div>
        <div class="row" *ngIf="sharedPagination">
          <div class="col-lg-12">
            <app-pagination [pagination]="sharedPagination" (navigatePage)="navigateSharedProfile($event)"></app-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-footer></app-footer>

</div>

<app-contact-us-banner></app-contact-us-banner>
