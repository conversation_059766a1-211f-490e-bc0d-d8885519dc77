<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <!-- Draft-->
  <div class="flex-fill monitoring-profiles">
    <div class="d-flex justify-content-center pt-5" *ngIf="isFetching">
      <h5 class="message-creating">Please wait while your profile is being created</h5>
    </div>
    <app-spinner [hidden]="!loading"></app-spinner>

    <div [hidden]="loading" class="page-content container clt-patents-container mt-3 mb-3">
      <!--./Highlights-->
      <div class="border rounded p-4 mt-3 mb-3 section-highlights">
        <!--Title-->
        <div class="card-title" *ngIf="profile">
          <div class="row d-flex align-items-center flex-wrap">
            <div class="col-md-6">
              <h3 class="mb-4">{{profile?.name}}</h3>
              <p>
                <b>{{profile?.category}}</b> &nbsp;&nbsp;
                <i class="far fa-clock"></i>&nbsp;<span class="text-gray">{{profile?.updated_at | dateFormat: 'ShortDate'}}</span>
              </p>
            </div>
            <div class="col-md-6">
              <div class="d-flex justify-content-end">
                <div class="state">
                  <div class="d-flex">
                    <img src="assets/images/landscape/info-icon.svg" alt="" class="me-3">
                    <span>
                      INFORMATION<br>SETUP
                    </span>
                  </div>
                </div>
                <div class="state active">
                  <div class="d-flex">
                    <img src="assets/images/landscape/pencil-ruler-solid.png" alt="" class="me-3"/>
                    <span>
                      CONFIRM<br>SELECTION
                    </span>
                  </div>
                </div>
                <div class="state">
                  <div class="d-flex">
                    <img src="assets/images/landscape/analysis-icon.svg" alt="" class="me-3">
                    <span>
                      Analyze<br>Landscape
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--./Title-->
        <hr>

        <div>
          <app-alert type="info" *ngIf="profile?.processed_input?.boolean_total_results_count > profile?.submitted_boolean_results_count "
          [message]="'Top '+profile?.submitted_boolean_results_count+' patents have been included in this profile from boolean query out of '+profile?.processed_input?.boolean_total_results_count+' total results'"></app-alert>
          <!--Result boxes-->
          <div class="info-cards-wrap d-flex">
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">PATENTS</h5>
                <p class="info-description">
                  {{profile?.submitted_patent_numbers_count + profile?.submitted_semantic_results_count + profile?.submitted_boolean_results_count}}
                </p>
              </div>
            </div>
            <!--./Single item-->
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">PATENT FAMILIES</h5>
                <p class="info-description">
                  {{profile?.matched_documents_count}}
                </p>
              </div>
            </div>
            <!--./Single item-->
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">UN-MAPPED FAMILIES</h5>
                <a href="javascript:void(0)" class="info-description" (click)="openUnmappedPatents()">
                  {{profile?.unmapped_patent_numbers_count}}
                </a>
              </div>
            </div>
            <!--./Single item-->
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">BOOLEAN</h5>
                <a href="javascript:void(0)" class="info-description" (click)="onFilterRows({'source':'BOOLEAN'})">
                  {{ profile?.submitted_boolean_results_count}}
                </a>
              </div>
            </div>
            <!--./Single item-->
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">SEMANTIC</h5>
                <a href="javascript:void(0)" class="info-description" (click)="onFilterRows({'source':'SEMANTIC'})">
                  {{ profile?.submitted_semantic_results_count}}
                </a>
              </div>
            </div>
            <!--./Single item-->
            <!--Single item-->
            <div class="single-info-card">
              <div class="content">
                <h5 class="info-title">LIST</h5>
                <a href="javascript:void(0)" class="info-description" (click)="onFilterRows({'source':'PATENT_NUMBER'})">
                  {{ profile?.submitted_patent_numbers_count}}
                </a>
              </div>
            </div>
            <!--./Single item-->
          </div>
        </div>
        <!--/Result boxes-->
      </div>
      <!--./Highlights-->
    </div>
    <div [hidden]="loading" class="page-content container-fluid clt-patents-container mt-3 mb-3" >
      <!--Documents-->
      <div class=" p-4 mt-3 mb-3  section-documents">
        <!-- Table -->
        <div class="dataTable-wrap  result-table2" id="draft-documents-section">
          <app-spinner [hidden]="documents !== undefined"></app-spinner>
          <app-alert type="danger" *ngIf="documents && documents.length === 0"
                     message="No document matched in this landscape profile"></app-alert>
          <h4 [hidden]="documents === undefined || documents?.length === 0" class="mb-2">Patent number results [{{pagination?.total_hits}}]</h4>
          <p class="text-gray" [hidden]="documents === undefined || documents?.length === 0">{{pagination | countStat}}</p>

          <div class="type-2" *ngIf="documents && documents?.length > 0">
            <div class="row pb-3">
              <div class="col-7">
                <app-patent-control-bar [columnsToShow]="columnsToShow" [searchService]="landscapeService"
                                        [hasHarmonizeControl]="false"
                                        [hasTemporaryLinkControl]="false"
                                        [hasExportControl]="false"
                                        [storeService]="landscapeStoreService">
                </app-patent-control-bar>
              </div>
              <div class="col-5 d-flex justify-content-end">

                <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" [pageOptions]="[25,50,100]">
                </app-page-size>
                <div class="c-and-r ms-2">
                  <button type="button" (click)="onComputeLandscape()" class="btn btn-primary  btn-md btn--compute">COMPUTE LANDSCAPE</button>
                </div>
              </div>
            </div>
            <div class="text-end tools-bar expand-all">
              <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()" [hidden]="documents === undefined || documents?.length === 0">
                {{ patentTable.openedPatent.length === documents.length ? 'Collapse all': 'Expand all'}}
                <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < documents.length"></i>
                <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === documents.length"></i>
              </a>
            </div>
            <div class="draft-filter-bar" *ngIf="showFilter">
              <app-filters-bar (filterRemoved)="onFilterRemoved($event)" [storeService]="landscapeStoreService"></app-filters-bar>
            </div>
            <div class="loading-box" [ngClass]="fadedTable ? 'active': 'inactive'">
              <div class="faded-layer"></div>
              <app-patent-table #patentTable [patents]="documents" (sort)="onSort($event)"
                                preSelected="landscape.status" preSelectedOn="ACTIVE" (filterRows)="onFilterRows($event)"
                                [pagination]="pagination" (patentChecked)="onPatentChecked($event)"
                                (checkedAll)="onCheckedAll($event)"
                                [hasLinksToBooleanSearch]="true"
                                pathUrl="/patent" [linkData]="linkData"
                                backButtonTitle="Back to landscape"
                                [storeService]="landscapeStoreService">
              </app-patent-table>
            </div>
          </div>
        </div>
        <!-- ./Table -->

        <!--back to dashboard-->
        <div class="row align-items-center" *ngIf="documents?.length > 0">
          <div *ngIf="pagination" class="col-sm-6 d-flex justify-content-center">
            <app-pagination [pagination]="pagination" (navigatePage)="onNavigatePage($event)"></app-pagination>
          </div>
          <div class="col-sm-3 order-first c-and-r">
            <a routerLink="/landscape" class="btn btn-primary  btn-md"><i class="fas fa-arrow-left p-0"></i>  Dashboard</a>
          </div>
          <div class="col-sm-3 text-end c-and-r">
            <button type="button" (click)="onComputeLandscape()" class="btn btn-primary  btn-md btn--compute">COMPUTE LANDSCAPE</button>
          </div>
        </div>
      </div>
      <!--./Documents-->
    </div>
  </div>
  <!-- ./Draft-->

  <app-footer></app-footer>

</div>

<app-contact-us-banner></app-contact-us-banner>
