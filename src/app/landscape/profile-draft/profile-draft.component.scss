@import 'scss/layout2021/variables';

.state {
  color: $brand-gray;
  margin-right: 40px;
  position: relative;
  font-family: $font-open-sans-regular;
  font-size: 0.9rem;
  
  &.active {
    color: black;
    font-family: $font-open-sans-bold;
  }

  img {
    width: 50px;
  }

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -30px;
    width: 30px;
    height: 1px;
    background: #d5d5d5;
    margin-left: -40px;
  }

  &:last-child {
    margin-right: 0;

    &::after {
      content: none;
      width: 0;
    }
  }

  span {
    display: block;
    text-transform: uppercase;
  }
}

#unmappedModal {
  a.badge {
    color: #fff;

    &.bg-primary {
      background: $brand-green;
    }
  }

  .patent-list {
    border-top: 1px solid $brand-green;
    padding-top: 10px;
    margin-top: 10px;
  }
}

.single-info-card {
  max-height: 153px;
}
.info-title {
  min-height: 60px;
}