import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProfileDraftComponent } from './profile-draft.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ProfileDraftComponent', () => {
  let component: ProfileDraftComponent;
  let fixture: ComponentFixture<ProfileDraftComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ProfileDraftComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([{path: 'landscape', component: ProfileDraftComponent}])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProfileDraftComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
