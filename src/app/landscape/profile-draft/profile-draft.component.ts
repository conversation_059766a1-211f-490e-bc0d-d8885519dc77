import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { columnsToShow } from './columns';
import {
  AdvancedFilterService,
  BooleanSearchService,
  LandscapeService,
  PaginationMetadata,
  PatentNumberService,
  PatentNumberTypeEnum
} from '@core/services';
import { Subscription, take } from 'rxjs';
import { LandscapeProfile, Patent } from '@core/models';
import { LandscapeStoreService, SortParams } from '@core/store';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UnmappedPatentDialogComponent } from '@landscape/unmapped-patent-dialog/unmapped-patent-dialog.component';
import { PatentTableComponent } from '@shared/components';

@Component({
  selector: 'app-profile-draft',
  templateUrl: './profile-draft.component.html',
  styleUrls: ['./profile-draft.component.scss']
})
export class ProfileDraftComponent implements OnInit, On<PERSON><PERSON>roy {
  /**
   * Landscape profile draft reference
   */
  profile: LandscapeProfile;
  /**
   * profile associated document ids reference for the draft profile
   */
  documentIds: number[];
  /**
   * pagination reference
   */
  pagination: PaginationMetadata;
  /**
   * reference for opened subsection of patent row
   */
  openedPatent = [];
  columnsToShow = columnsToShow;
  /**
   * reference for sort order and sort by on patent table
   */
  sorting: SortParams = {field: null, order: null} as SortParams;
  /**
   * reference for patent table size for app-page-size component
   */
  pageSize = 100;
  /**
   * reference for patent table active page number for app-page-size component
   */
  activePage = 1;
  /**
   * reference to fade patent table component for processing status change request
   */
  fadedTable: boolean = false;
  isFetching = false;
  @ViewChild('patentTable') patentTable: PatentTableComponent;
  /**
   * profile ID of Active landscape profile
   */
  private profileID: number;
  /**
   * reference for un-mapped patent number
   */
  private unMappedPatents: Array<string>;
  private isSearching = false;
  private intervalToLoadProfile;
  private intervalToLoadDocuments;
  private intervalTime = 5000;

  private subscriptions = new Subscription();

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    public landscapeService: LandscapeService,
    public landscapeStoreService: LandscapeStoreService,
    private booleanSearchService: BooleanSearchService,
    private patentNumberService: PatentNumberService,
    private modalService: NgbModal,
    private advancedFilterService: AdvancedFilterService
  ) {
  }

  /**
   * loading reference
   */
  get loading() {
    return this.landscapeStoreService.landscapeLoading;
  }

  set loading(state) {
    this.landscapeStoreService.landscapeLoading = state;
  }

  /**
   * profile associated documents reference for the draft profile
   */

  get documents() {
    return this.landscapeStoreService.landscapeDocuments;
  }

  set documents(documents: Array<Patent>) {
    this.landscapeStoreService.landscapeDocuments = documents;
  }

  get linkData() {
    return this.landscapeService.linkData;
  }

  get showFilter(): boolean {
    return this.landscapeStoreService.filters.length > 0;
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    clearInterval(this.intervalToLoadProfile);
    clearInterval(this.intervalToLoadDocuments);
  }

  ngOnInit(): void {
    if (this.landscapeStoreService.backPatentSearch) {
      this.landscapeStoreService.backPatentSearch = false;
    } else {
      this.clearStoredValues();
    }

    if (this.route.snapshot.params.id > 0) {
      this.landscapeStoreService.setFilters([], false);
      this.booleanSearchService.filterClauses = [];
      this.landscapeStoreService.selectedColumnsToShow = [];
      this.profileID = this.route.snapshot.params.id;
      this.loading = true;
      this.intervalToLoadProfile = (() => {
        this.loadProfile();
        return setInterval(() => this.loadProfile(), this.intervalTime);
      })();

    } else {
      this.router.navigateByUrl('/landscape');
    }
  }

  loadProfile() {
    if (this.isSearching) return;

    this.isSearching = true;
    this.landscapeService.loadProfile(this.profileID).then(
      response => {
        this.profile = response as LandscapeProfile;
        this.isSearching = false;
        if (this.profile['status'] === 'FETCHING_PORTFOLIO') {
          this.isFetching = true;
          return;
        } else if (this.profile['status'] === 'DRAFT') {
          clearInterval(this.intervalToLoadProfile);
          this.unMappedPatents = null;
          if (this.profile.processed_input.not_found && this.profile.processed_input.invalid) {
            this.unMappedPatents = [...this.profile.processed_input.not_found, ...this.profile.processed_input.invalid];
          }
          this.intervalToLoadDocuments = (() => {
            this.loadDocuments();
            return setInterval(() => this.loadDocuments(), this.intervalTime);
          })();
        } else {
          this.router.navigateByUrl('/landscape');
        }
      }
    ).catch(
      error => {
        switch (error.status) {
          case 404:
            this.router.navigateByUrl('/landscape');
            break;
          default:
            console.error(error);
            throw error;
        }
        this.loading = false;
        this.isFetching = false;
        this.isSearching = false;
      }
    );
  }

  loadDocuments() {
    if (this.isSearching) return;

    this.isSearching = true;
    this.documents = undefined;
    this.pagination = undefined;
    const query = this.buildQuery();
    this.landscapeService.loadProfileDraft(this.profileID, query).then(
      response => {
        this.isSearching = false;
        response['documents'].forEach(function (v) {
          delete v.general.obfuscated;
        });
        if (response['status'] === 'FETCHING') {
          this.isFetching = true;
        } else {
          clearInterval(this.intervalToLoadDocuments);
          this.isFetching = false;
          this.loading = false;
          this.documents = response['documents'];
          this.pagination = response['page'] as PaginationMetadata;
          this.landscapeStoreService.searchHash = response['search_info']['search_hash'];
          this.landscapeStoreService.pagination = this.pagination;
          this.landscapeStoreService.search = {params: Object.assign({}, query)};
          if (!this.documentIds) {
            const payload = {
              search_hash: this.landscapeStoreService.searchHash,
              include_patent_id: true,
              search_filters: {}
            };
            const extractPatentList$ = this.patentNumberService.extractPatentList(payload)
              .pipe(take(1))
              .subscribe({
                next: response => {
                  this.documentIds = response.patent_id_list;
                }, error: error => {
                  console.error(error);
                }
              });
            this.subscriptions.add(extractPatentList$);
          }
        }
      }
    ).catch(
      err => {
        this.loading = false;
        this.isFetching = false;
        this.isSearching = false;
        this.documents = [];
      }
    );
  }

  onSort(val: SortParams) {
    this.sorting = val;
    this.loadDocuments();
  }

  onChangePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.activePage = 1;
    this.loadDocuments();
  }

  onComputeLandscape() {
    this.loading = true;
    this.landscapeService.computeLandscapeProfile(this.profileID).then(
      response => {
        this.landscapeStoreService.selectedPublications = [];
        this.router.navigateByUrl(`/landscape/profile/${this.profileID}`);
        this.loading = false;
      }
    ).catch(
      error => {
        console.error(error);
        this.loading = false;
        throw error;
      }
    );
  }

  onNavigatePage(page: number) {
    this.activePage = page;
    this.loadDocuments();
  }

  /**
   * onPatentChecked
   *
   * event listener for single patent row checkbox event in patent table component
   * @param event object with status and patent
   */
  onPatentChecked(event: { state: boolean, patent: Patent }) {
    this.fadedTable = true;
    const payload = [
      {
        id: event.patent.general.docdb_family_id,
        status: event.state ? 'ACTIVE' : 'INACTIVE'
      }
    ];

    this.landscapeService.updateDocumentStatus(this.profileID, payload).then(
      res => {
        event.patent['landscape'].status = event.state ? 'ACTIVE' : 'INACTIVE';
        this.fadedTable = false;
      }
    ).catch(
      err => {
        this.fadedTable = false;
      }
    );
  }

  /**
   * onCheckedAll
   *
   * event listener for checkALL event in patent table component
   * @param event object with status and patent list
   */
  onCheckedAll(event: { state: boolean, patents: Array<Patent>, allPatents?: any }) {
    if (event.state) {
      this.handleCheckAll(event)
    } else {
      this.handleUncheckAll(event)
    }
  }

  handleCheckAll(event: { state: boolean, patents: Array<Patent>, allPatents?: any }) {
    this.loading = true;
    const onlyVisible = !event.allPatents;
    const documentIds = onlyVisible ? this.documents.map(d => Number(d.general.docdb_family_id)): this.documentIds;
    this.updateDocumentStatuses(documentIds, 'ACTIVE');
  }

  handleUncheckAll(event: { state: boolean, patents: Array<Patent>, allPatents?: any }) {
    this.loading = true;
    const onlyVisible = !event.allPatents;
    const documentIds = onlyVisible ? this.documents.map(d => Number(d.general.docdb_family_id)): this.documentIds;
    this.updateDocumentStatuses(documentIds, 'INACTIVE');
  }

  private updateDocumentStatuses(documentIds: Array<number>, status: string) {
    const payload = [];
    this.loading = true;
    documentIds.forEach(did => {
      payload.push({
        id: did,
        status: status
      });
    });
    if (payload.length > 0) {
      this.landscapeService.updateDocumentStatus(this.profileID, payload).then(
        res => {
          this.documents.filter(doc => doc.landscape.status !== status).forEach(patent => {
            patent['landscape'].status = status;
          });
          this.loading = false;
        }
      ).catch(
        err => {
          this.loading = false;
        }
      );
    } else {
      this.loading = false;
    }
  }

  /**
   * onFilterRows
   */
  onFilterRows(queryParam: Object) {
    if (queryParam['source']) {
      this.landscapeStoreService.setFilters([{type: 'source', title: 'Source', value: queryParam['source']}]);
      this.activePage = 1;
    }
    this.loadDocuments();
  }

  /**
   * onFilterRemoved
   */
  onFilterRemoved(filter) {
    const filters = [...this.landscapeStoreService.filters].filter(o => o.type !== filter.type);
    this.landscapeStoreService.setFilters(filters);
    this.loadDocuments();
  }

  openUnmappedPatents(): void {
    const modal = this.modalService.open(UnmappedPatentDialogComponent, {size: 'lg'});
    modal.componentInstance.profile = this.profile;
    modal.componentInstance.unMappedPatents = this.unMappedPatents;
  }

  /**
   * buildQuery
   *
   * query builder for result table and chart section
   * @returns query array
   */
  private buildQuery(): Array<string> {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;
    query['show_general'] = 1;
    query['show_flags'] = 1;
    if (this.sorting.order) {
      query['sort_order'] = this.sorting.order;
    }
    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
    }
    const sourceFilter = this.landscapeStoreService.filters.filter(o => o.type === 'source');
    if (sourceFilter.length > 0) {
      query['source'] = this.landscapeStoreService.filters[0].value;
    }
    return query;
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.landscapeStoreService.resetAdvancedFilter();
    this.landscapeStoreService.patentTableSort = {field: null, order: null} as SortParams;
    this.profile = undefined;
    this.documents = undefined;
    this.landscapeStoreService.setFilters([], false);
  }
}
