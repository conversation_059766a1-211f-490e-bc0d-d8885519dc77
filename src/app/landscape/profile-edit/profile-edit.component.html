<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <!-- Analysis-->
  <div class="flex-fill monitoring-profiles">
    <app-spinner [hidden]="!loading"></app-spinner>
    <div [hidden]="loading" class="container">
      <form class="page-content p-4 container clt-patents-container border rounded mt-3 mb-3 landscape-profile-form" [formGroup]="form" (ngSubmit)="onSubmit()">
        <!--Title-->
        <div class="section-title">
          <div class="row d-flex align-items-center flex-wrap">
            <div class="col-md-6 col-md-pull-6">
              <h3>{{this.profile? 'UPDATE': 'CREATE'}} LANDSCAPE</h3>
            </div>
            <div class="col-md-6">
              <div class="d-flex justify-content-end">
                <div class="state active">
                  <div class="d-flex">
                    <img src="assets/images/landscape/info-icon.svg" alt="" class="me-3">
                    <span>
                      INFORMATION<br>SETUP
                    </span>
                  </div>
                </div>
                <div class="state">
                  <div class="d-flex">
                    <img src="assets/images/landscape/pencil-ruler-solid.png" alt="" class="me-3"/>
                    <span>
                      CONFIRM<br>SELECTION
                    </span>
                  </div>
                </div>
                <div class="state">
                  <div class="d-flex">
                    <img src="assets/images/landscape/analysis-icon.svg" alt="" class="me-3">
                    <span>
                      Analyze<br>Landscape
                    </span>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
        <!--./Title-->
        <hr>
        <!--Line-->

        <!--Input fields-->
        <div class="row">
          <div class="col-lg-12"  id="landscape-profile-error">
            <app-alert type="danger" *ngIf="errors.length>0" [message]="errors"></app-alert>
          </div>
          <div class="col-lg-6">
            <div class="mb-3">
              <label class="label2 form-label">NAME YOUR LANDSCAPE</label>
              <input type="text" formControlName="name" class="form-control" [ngClass]="{'is-invalid':form.get('name').touched && form.get('name').hasError('required')}" maxlength="256" required>
              <div *ngIf="form.get('name').touched && form.get('name').hasError('required')" class="invalid-feedback">
                Please enter profile name.
              </div>
            </div>
            <div class="mb-3">
              <label class="label2 form-label">Category</label>
              <input type="text" formControlName="category" class="form-control" [ngClass]="{'is-invalid':form.get('category').touched && form.get('category').hasError('required')}" maxlength="256" required>
              <div *ngIf="form.get('category').touched && form.get('category').hasError('required')" class="invalid-feedback">
                Please enter profile category.
              </div>
            </div>
          </div>
        </div>
        <!--./Input fields-->

        <!-- Nav tabs -->
        <div class="sticky-tab">
          <ul class="nav-tabs nav-2" role="tablist" id="c-and-r" ngbNav [destroyOnHide]="false" #profileInputTabs="ngbNav">
            <li [ngbNavItem]="'patentNumList'">
              <a ngbNavLink> Patent number list</a>
              <ng-template ngbNavContent>
                <div>
                  <b>CONFIGURE AND RUN LANDSCAPE</b> &nbsp;
                  <app-tooltip id="landscape_patent_numbers" [tooltipTitle]='tooltip.title.profileMethodConfigureText' [tooltipText]='tooltip.text.profileMethodConfigureText'></app-tooltip>
                <!-- Patent List Input box -->
                <div class="method-inputBox-wrap">
                  <div class="patent-list-box">
                    <app-patent-list-input [(patent_numbers)]="form.value.patent_numbers" (update)="onChangePatentList($event)"
                                           columnLabel="Select the column that contains publication or application numbers: "
                                           [(patentNumbersType)]="form.value.patentNumbersType" [storeService]="landscapeStoreService">
                    </app-patent-list-input>
                  </div>

                  <div class="btn-wrap">
                    <div class="row">
                      <div class="col-sm-4">
                        <a routerLink="/landscape" class="btn btn-primary btn-lg"><i class="fas fa-arrow-left p-0"></i>  Dashboard</a>
                      </div>
                      <div class="col-sm-8">
                        <div class="btn-wrap text-end">
                          <button type="button" (click)="onReset('list')" class="btn btn-primary  btn-lg me-3">CLEAR</button>
                          <button type="submit" class="btn btn-primary btn-lg">{{this.profile? 'UPDATE': 'PROCEED TO'}} LANDSCAPE</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- ./Patent List Input box -->
              </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="'Semantic'">
              <a ngbNavLink class="">Semantic</a>
              <ng-template ngbNavContent>
                <div>
                  <b>CONFIGURE AND RUN LANDSCAPE</b> &nbsp;
                  <app-tooltip id="landscape_semantic" [tooltipTitle]='tooltip.title.profileMethodConfigureText' [tooltipText]='tooltip.text.profileMethodConfigureText'></app-tooltip>
                <!-- Semantic Input box -->
                <div class="method-inputBox-wrap">
                  <app-semantic-input [form]="form" [textWeighting]="this.form.value.text_weighting" [storeService]="landscapeStoreService"
                     (submitInput)="onSubmit()">
                  </app-semantic-input>

                  <div class="btn-wrap mt-3">
                    <div class="row">
                      <div class="col-sm-4">
                        <a routerLink="/landscape" class="btn btn-primary btn-lg"><i class="fas fa-arrow-left p-0"></i>  Dashboard</a>
                      </div>
                      <div class="col-sm-8">
                        <div class="btn-wrap text-end">
                          <button type="button" (click)="onReset('semantic')" class="btn btn-primary btn-lg me-3">CLEAR</button>
                          <button type="submit" class="btn btn-primary btn-lg">{{this.profile? 'UPDATE': 'PROCEED TO'}} LANDSCAPE</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- ./Semantic Input box -->
              </div>
              </ng-template>
            </li>
            <li [ngbNavItem]="'Boolean'">
              <a ngbNavLink class="">Boolean</a>
              <ng-template ngbNavContent>
                <div>
                  <b>CONFIGURE AND RUN LANDSCAPE</b> &nbsp;
                  <app-tooltip id="landscape_boolean" [tooltipTitle]='tooltip.title.profileMethodConfigureText' [tooltipText]='tooltip.text.profileMethodConfigureText'></app-tooltip>
                <!-- Boolean Input box -->
                <div class="method-inputBox-wrap">
                  <div class="boolean-form-box ps-2" [ngClass]="booleanFromWrapper">
                    <app-boolean-input [booleanSearchService]="booleanService" [storeService]="landscapeStoreService"
                                       (doSubmit)="onSubmit()" [clearEmptyClause]="true">
                    </app-boolean-input>
                  </div>
                  <div class="btn-wrap">
                    <div class="row">
                      <div class="col-sm-4">
                        <a routerLink="/landscape" class="btn btn-primary btn-lg"><i class="fas fa-arrow-left p-0"></i>  Dashboard</a>
                      </div>
                      <div class="col-sm-8">
                        <div class="btn-wrap text-end">
                          <button type="button" (click)="onReset('boolean')" class="btn btn-primary btn-lg me-3">CLEAR</button>
                          <button type="submit" class="btn btn-primary btn-lg">{{this.profile? 'UPDATE': 'PROCEED TO'}} LANDSCAPE</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- ./Boolean Input box -->
              </div>
              </ng-template>
            </li>
          </ul>
        </div>
        <!-- ./Nav tabs -->

        <!-- Tab panes -->
        <div [ngbNavOutlet]="profileInputTabs"></div>
        <!-- ./Tab panes -->
      </form>
    </div>
  </div>
  <!-- ./Analysis-->

  <app-footer></app-footer>

</div>

<app-contact-us-banner></app-contact-us-banner>
