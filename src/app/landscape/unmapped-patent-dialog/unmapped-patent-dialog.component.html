
      <div class="modal-header">
        <div class="modal-title" id="unmappedModalLabel">Un-mapped patent number(s) </div>
        <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()"></button>
      </div>
      <div class="modal-body">
        <div class="row" *ngIf="unMappedPatents">
          <div class="col-md-4" *ngIf="unMappedPatents.length > 0">
            <h5>Sort by:</h5>
          </div>
          <div class="col-md-8" *ngIf="unMappedPatents.length > 0">
            &nbsp;<a class="badge" [ngClass]=" unMappedPatentsSort ==='default' ? 'bg-primary':'bg-secondary'"
              (click)="onUnMappedPatentSort('default')">Default</a>
            &nbsp;<a class="badge" [ngClass]=" unMappedPatentsSort ==='asc' ? 'bg-primary':'bg-secondary'"
              (click)="onUnMappedPatentSort('asc')">Ascending</a>
            &nbsp;<a class="badge" [ngClass]=" unMappedPatentsSort ==='desc' ? 'bg-primary':'bg-secondary'"
              (click)="onUnMappedPatentSort('desc')">Descending</a>
          </div>
          <div class="col-md-12 patent-list" [hidden]="unMappedPatents.length===0">
            <app-alert type="warning" message="Patent numbers which could not be mapped to a known family."></app-alert>
            <p class="row">
              <span *ngFor="let patent of unMappedPatents" class="col-sm-6 col-md-4 col-lg-3">{{patent}}</span>
            </p>
          </div>
          <div class="col-md-12" *ngIf="profile.unmapped_patent_numbers_count===0">
            <app-alert type="info" message="All patent numbers are mapped to a known family."></app-alert>
          </div>
          <div class="col-md-12" *ngIf="profile.unmapped_patent_numbers_count===-1">
          </div>
        </div>
        <div class="row" *ngIf="unMappedPatents === null">
          <div class="col-md-12">
            <app-alert type="danger" message="Please recreate this landscape profile."></app-alert>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary btn-md" (click)="activeModal.dismiss()">Close</button>
      </div>
