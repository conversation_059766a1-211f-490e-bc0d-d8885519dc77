import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import { UnmappedPatentDialogComponent } from './unmapped-patent-dialog.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('UnmappedPatentDialogComponent', () => {
  let component: UnmappedPatentDialogComponent;
  let fixture: ComponentFixture<UnmappedPatentDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UnmappedPatentDialogComponent ],
      imports: [HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UnmappedPatentDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
