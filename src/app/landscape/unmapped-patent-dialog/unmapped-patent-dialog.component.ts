import { Component, OnInit } from '@angular/core';
import { LandscapeProfile } from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-unmapped-patent-dialog',
  templateUrl: './unmapped-patent-dialog.component.html',
  styleUrls: ['./unmapped-patent-dialog.component.scss']
})
export class UnmappedPatentDialogComponent {

  public unMappedPatents: Array<string>;
  public unMappedPatentsSort: string = 'default';
  public profile: LandscapeProfile;

  constructor(
    public activeModal: NgbActiveModal,
  ) { }

  /**
   * onUnMappedPatentSort
   *
   * sort the un mapped patent array
   * @param type: type of sort option
   */
  public onUnMappedPatentSort(type: string) {
    if (this.unMappedPatentsSort !== type) {
      this.unMappedPatents = [...this.profile.processed_input.not_found];
      switch (type) {
        case 'asc':
          // Ascending
          this.unMappedPatents = this.unMappedPatents.sort();
          break;
        case 'desc':
          // Descending
          this.unMappedPatents = this.unMappedPatents.sort().reverse();
          break;
      }
      this.unMappedPatentsSort = type;
    }
  }
}
