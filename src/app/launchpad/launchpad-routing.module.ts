import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';
import { LaunchpadComponent } from './launchpad/launchpad.component';

const routes: Routes = [
  {path: '', component: LaunchpadComponent, canActivate: [AuthGuard], pathMatch: 'full'}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LaunchpadRoutingModule {
}
