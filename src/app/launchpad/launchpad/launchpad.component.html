 <div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="flex-fill row m-0 h-100 launchpad-container" data-intercom-target="Launchpad start">
      <div [ngClass]="isFreeUser ? 'col-12' : 'col-9'">
        <div  [ngClass]="{'container-fluid': isFreeUser}">
          <div class="pt-4 pb-4">
            <div class="d-flex justify-content-start align-items-center launchpad-title">
              <app-user-avatar [user]="user" [hasSubTitle]="false" [avatarSize]="80" [avatarFontSize]="35" [showTooltip]="false"></app-user-avatar>
              <div class="ms-3 d-flex flex-column justify-content-start align-items-start flex-fill">
                  <span class="mb-2 launchpad-user-title open-sans-semi-bold">Welcome, {{username}}</span>
                  <a routerLink="/profile" class="launchpad-user-subtitle"><i class="fas fa-cog"></i> Settings</a>
              </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-sm-12 col-md-6 col-xl-3 mb-3 mb-xl-0">
                    <div [routerLink]="['/search']"
                        class="launchpad-card launchpad-card-1 border-0 p-3 d-flex justify-content-start align-items-center cursor-pointer">
                      <img src="assets/images/launchpad/lp_search_icon.svg" class="cursor-pointer">
                      <div class="ms-3 d-flex flex-column justify-content-start align-items-start flex-fill" data-intercom-target="Search Item">
                        <div class="launchpad-card-title cursor-pointer">Search</div>
                        <div class="d-flex flex-wrap justify-content-start launchpad-card-subtitle w-100 pt-2 launchpad-card-actions">
                            <a class="cursor-pointer text-capitalize">Semantic</a>
                            <a routerLink="/boolean" (click)="$event.stopPropagation();">Boolean</a>
                            <a routerLink="/citation" (click)="$event.stopPropagation();">Citation</a>
                        </div>
                      </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-xl-3 mb-3 mb-xl-0">
                    <div class="launchpad-card launchpad-card-2 border-0 p-3 d-flex justify-content-start align-items-center"
                        (click)="navigateToPage($event, 'monitor', 'monitor')"
                         [ngClass]="{'cursor-pointer': userService.hasMonitor}">
                      <img src="assets/images/launchpad/lp_monitoring_icon.svg">

                      <div class="ms-3 d-flex flex-column justify-content-start align-items-start flex-fill">
                        <div class="launchpad-card-title cursor-pointer">Monitor</div>

                        <div class="d-flex flex-wrap justify-content-start launchpad-card-subtitle w-100 pt-2 launchpad-card-actions"
                             *ngIf="!userService.hasMonitor">
                          <a (click)="openHubSpotForm(true)" class="cursor-pointer">
                            {{ user?.ui_settings?.launchpad_request_access ? 'Request received' : 'Request access' }}
                          </a>
                          <div *ngIf="false" class="d-flex justify-content-start align-items-center cursor-pointer">
                            <i class="fa fa-play-circle text-decoration-none" aria-hidden="true"></i>
                            <a (click)="$event.stopPropagation();" class="ps-1">Watch video</a>
                          </div>
                        </div>
                        <div class="d-flex flex-wrap justify-content-start launchpad-card-subtitle w-100 pt-2 launchpad-card-actions"
                          *ngIf="userService.hasMonitor">
                          <a class="cursor-pointer text-capitalize">New publications</a>
                          <a routerLink="/monitor/legalStatus" (click)="$event.stopPropagation();">Legal status changes</a>
                        </div>
                      </div>

                      <div *ngIf="!userService.hasMonitor"
                           class="launchpad-card-lock d-flex justify-content-center align-items-center">
                        <i class="fa fa-lock" aria-hidden="true"></i>
                      </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6 col-xl-3 mb-3 mb-xl-0" *ngIf="userService.hasFeature('landscape')">
                  <div class="launchpad-card launchpad-card-4 border-0 p-3 d-flex justify-content-start align-items-center"
                       (click)="navigateToPage($event, 'landscape', 'landscape')"
                       [ngClass]="{'cursor-pointer': userService.hasFeature('landscape')}">
                    <img src="assets/images/launchpad/lp_landscape_icon.svg">
                    <div class="ms-3 d-flex flex-column justify-content-start align-items-start flex-fill">
                      <span class="launchpad-card-title">Landscape</span>

                      <div class="d-flex flex-wrap justify-content-start launchpad-card-subtitle w-100 pt-2 launchpad-card-actions"
                           *ngIf="!userService.hasFeature('landscape')">
                        <a (click)="openHubSpotForm(true)" class="cursor-pointer">
                          {{ user?.ui_settings?.launchpad_request_access ? 'Request received' : 'Request access' }}
                        </a>

                        <div *ngIf="false" class="d-flex justify-content-start align-items-center cursor-pointer">
                          <i class="fa fa-play-circle text-decoration-none" aria-hidden="true"></i>
                          <a (click)="$event.stopPropagation();" class="ps-1">Watch video</a>
                        </div>
                      </div>

                      <div *ngIf="!userService.hasFeature('landscape')"
                           class="launchpad-card-lock d-flex justify-content-center align-items-center">
                        <i class="fa fa-lock" aria-hidden="true"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-sm-12 col-md-6 col-xl-3 mb-3 mb-xl-0">
                  <div routerLink="/collections"
                       class="launchpad-card launchpad-card-3 border-0 p-3 d-flex justify-content-start align-items-center cursor-pointer">
                    <img src="assets/images/launchpad/lp_patentcollection_icon.svg">

                    <div class="ms-3 d-flex flex-column justify-content-start align-items-start flex-fill">
                      <span class="launchpad-card-title">Patent Collection</span>
                    </div>
                  </div>
                </div>
            </div>

            <ng-container *ngIf="!isFreeUser">
                <div >
                    <div class="row">
                        <div class="mt-5" [ngClass]="historyWidgetClass()">
                            <div class="widget-title mt-3 mb-2">Your last searches</div>
                            <app-search-history-widget class="widget-max-height-1 d-block" [pageSize]="historyWidgetPageSize()"></app-search-history-widget>
                        </div>
                        <div class="mt-5" *ngIf="userService.hasMonitor" [ngClass]="monitorWidgetClass()">
                            <div class="widget-title mt-3 mb-2">Monitoring reports</div>
                            <app-monitor-runs-widget class="widget-max-height-2 d-block"></app-monitor-runs-widget>
                        </div>
                    </div>
                    <div class="row">
                    </div>
                </div>
            </ng-container>

            <ng-container *ngIf="isFreeUser">
                <div class="row mt-5">
                    <div class="col-6 news">
                        <div class="widget-title mt-3 mb-2">News</div>
                        <app-cockpit-widget cockPitContentType="NEWS" widgetClass=""></app-cockpit-widget>
                    </div>
                </div>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="col-3 side-bar p-0 pe-2" *ngIf="!isFreeUser">
          <app-notifications headline="Latest notifications" data-intercom-target="latest-notifications" *ngIf="!isSingleUser"></app-notifications>
          <div class="mt-5 mb-3">
              <div class="widget-title ps-3">News</div>
              <app-cockpit-widget cockPitContentType="NEWS" widgetClass="p-1 news-long"></app-cockpit-widget>
          </div>
      </div>
  </div>

  <app-footer></app-footer>
</div>
