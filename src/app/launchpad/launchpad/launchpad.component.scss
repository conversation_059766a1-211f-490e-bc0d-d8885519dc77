@import 'scss/layout2021/variables';

.side-bar {
  background-color: #F8F8F8;
}

:host::ng-deep {
  .widget {
    &-max-height {
      &-1 {
        max-height: 500px;
      }

      &-2 {
        max-height: 400px;
      }

      &-3 {
        max-height: 300px;
      }

      &-4 {
        max-height: 200px;
      }

      &-5 {
        max-height: 175px;
      }

      &-100 {
        max-height: 100%;
      }
    }

    &-height-4 {
      height: 200px;
    }

    &-height-5 {
      height: 175px;
    }

    &-title {
      color: $color-text-04;
      font-size: 1.5rem;
      font-family: $font-open-sans-semi-bold;
    }
  }
}

.launchpad {
  &-user-title {
    font-size: 2rem;
    font-family: $font-open-sans-regular;
    font-weight: 600;
    color: #0F2C35;
  }

  &-user-subtitle {
    font-size: 0.875rem;
    font-weight: 600;
  }

  &-card {
    border-radius: 10px;
    height: 104px;
    position: relative;

    &-1 {
      background: #BEDACE 0 0 no-repeat padding-box;
      color: #43755F;

      img {
        max-width: 58px !important;
      }
    }

    &-2 {
      background: #E2D4BA 0 0 no-repeat padding-box;
      color: #90733C;
    }

    &-3 {
      background: #C1D1D7 0 0 no-repeat padding-box;
      color: #51707B;
    }

    &-4 {
      background: #D3CDD7 0 0 no-repeat padding-box;
      color: #7D6D88;
    }

    a {
      color: inherit;
      text-decoration: underline;
    }

    img {
      max-width: 48px;
    }

    &-title {
      font-size: 1.125rem;
      font-family: $font-open-sans-bold;
      display: block;
    }

    &-subtitle {
      font-size: 0.875rem;
      font-weight: 600;
    }

    &-lock {
      position: absolute;
      right: -10px;
      top: -10px;
      border-radius: 50%;
      height: 30px;
      width: 30px;
      border: 0px transparent;
      -webkit-box-shadow: 0px 0px 5px 0px gray;
      box-shadow: 0px 0px 5px 0px gray;
      background-color: white;
    }

    &-actions {
      gap: 3px 10px;
    }
  }
  &-title{
    app-user-avatar{
      min-width: 98px;
      display: flex;
      justify-content: center;
    }
  }
}

:host::ng-deep {
  .widget {
    &-container {
      overflow-y: auto;
      overflow-x: hidden;
      height: 100%;
    }

    &-no-content {
      text-align: center;
      font-size: 0.875rem !important;
      font-weight: bold;
    }

    &-underline {
      border-bottom: 1px solid #dee2e6;
    }

  }

  .news-long {
    max-height: 480px !important;
  }

  .news {
    .widget-content-news {
      a {
        padding: 5px 0 !important;
      }
    }
  }

  .widget-load-more {
    padding-right: 20px !important;
  }
}

.widget-onboarding-icon-lg {
  margin-top: -35px;
}
