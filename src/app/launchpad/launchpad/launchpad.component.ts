import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { UserService, UserProfile, UserSubscription} from '@core';
import { Subscription, take } from 'rxjs';
import { SupportComponent } from '@shared/components';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap/modal/modal-ref';
declare var $: any;

@Component({
  selector: 'app-launchpad',
  templateUrl: './launchpad.component.html',
  styleUrls: ['./launchpad.component.scss']
})
export class LaunchpadComponent implements OnInit, OnDestroy{

  user: UserProfile;
  subscription: UserSubscription;
  onboardingText = 'It doesn’t take much to get started with Octimine. Take a tour to see how easy any patent analysis related tasks can get with Octimine….';

  private subscriptions = new Subscription();
  private supportModalRef: NgbModalRef = null;

  constructor(
    public router: Router,
    public userService: UserService,
    private modalService: NgbModal
  ) { }

  get username() {
    if (!this.user) {
      return '';
    } else if (this.user.first_name || this.user.last_name) {
      return `${this.user.first_name || ''} ${this.user.last_name || ''}`.trim();
    } else {
      return this.user.email;
    }
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: u => {
        if (u) {
          this.user = u.profile;
          this.subscription = u.subscription;
        }
      }
    });
    this.subscriptions.add(user$);
  }

  get isFreeUser(): boolean {
    return this.userService.isFreeUser();
  }

  get isSingleUser(): boolean {
    return !this.userService.canUseWorkflowFeature();
  }

  get showTeamWidget(): boolean {
    return this.userService.canUseWorkflowFeature();
  }

  get showMonitorWidget(): boolean {
    return this.userService.hasMonitor;
  }

  isExpandedCollectionWidget(): boolean{
    if((!this.userService.hasMonitor && this.userService.hasTaskFeature()) || (this.userService.hasMonitor && !this.userService.hasTaskFeature())){
      return true;
    }
    return false;
  }

  historyWidgetClass(): string {
    return 'col-6';
  }

  historyWidgetPageSize(): number {
    if (this.showMonitorWidget) {
      return 4;
    }
    return 5;
  }

  collectionsWidgetClass(): string {
    if (this.isExpandedCollectionWidget()){
      return 'col-12';
    } else{
      return 'col-6';
    }
  }

  monitorWidgetClass(): string {
    if (true || this.showMonitorWidget && this.showTeamWidget) {
      return 'col-6';
    } else {
      return 'col-4';
    }
  }

  teamWidgetClass(): string {
    if (this.showMonitorWidget && this.showTeamWidget) {
      return 'col-6';
    } else {
      return 'col-4';
    }
  }

  teamWidgetSize(): string {
    if (this.showMonitorWidget && this.showTeamWidget) {
      return 'lg';
    }
    return 'sm';
  }

  openHubSpotForm(accessRequest: boolean): void {
    const launchpadRequestAccessSetting = this.userService.getUISetting('launchpad_request_access', false);
    if ((accessRequest && launchpadRequestAccessSetting) || this.supportModalRef) {
      return;
    }

    const self = this;
    this.supportModalRef = this.modalService.open(SupportComponent, {centered: true, windowClass: 'modal-support'});
    this.supportModalRef.componentInstance.title = accessRequest ? 'Request access' : 'Schedule a live demo';
    this.supportModalRef.componentInstance.onFormReadyCallback = (formId) => {
      self.setBookMessage(formId, accessRequest);
    };
    this.supportModalRef.componentInstance.onFormSubmittedCallback = (formId) => {
      if (accessRequest) {
        const updateUISettings$ = self.userService.updateUISettings({launchpad_request_access: true})
          .pipe(take(1))
          .subscribe({
          next: (data) => {
            self.user.ui_settings = data.profile.ui_settings;
          }, error: error => {
            console.error(error);
          }
        });
        self.subscriptions.add(updateUISettings$);
      }
    };
    this.supportModalRef.componentInstance.onFormExistedCallback = (formId) => {
      self.setBookMessage(formId, accessRequest);
    };
    this.supportModalRef.result.then(() => {
      self.supportModalRef = null;
    }, () => {
      self.supportModalRef = null;
    });
  }

  private setBookMessage(formId: string, accessRequest: boolean): void {
    let message = '';
    if (accessRequest) {
      if (!this.userService.hasMonitor) {
        message = 'Monitor';
      }
      if (!this.userService.hasFeature('landscape')) {
        message += !!(message) ?  ' and Landscape' : 'Landscape';
      }
    }
    $(`#message-${formId}`).val(message).trigger('change');
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  navigateToPage(event: MouseEvent, feature: string, url: string) {
    if (this.userService.hasFeature(feature)) {
      this.router.navigate([url]);
    } else {
      event.preventDefault();
    }
  }
}
