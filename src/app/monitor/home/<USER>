@use 'layout2021/variables' as variables;

h3{
    a{
        text-decoration: none;
        color: inherit;
        border: 1px solid black;
        padding: 5px 10px;
        border-radius: 3px;
    }
    .active{
        color: variables.$brand-green;
        border-color: variables.$brand-green;
    }
}

.clt-container {
  @import 'scss/components/input-group';

  .input-group {
    .form-control, .btn {
      height: 48px;
    }
  }

  .search-box {
    display: inline-block;

    input, button {
      border-color: #999;
      font-size: 13px;
      height: 48px;
    }

    input {
      width: 172px;
    }
  }
}

.profile-single.create-profile-box {
  border: 5px dashed #d8d8d8;
  padding: 0;
  display: flex;
  min-height: 133px;

  .fa-plus {
    color: #d8d8d8;
  }
  &:hover:not(.disabled) {
    border-color: variables.$brand-green;
    .fa-plus{
      color: variables.$brand-green;
    }
  }
  &.disabled {
    cursor: default;
  }
}
