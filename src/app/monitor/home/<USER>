import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MonitorHomeComponent } from './home.component';
import { SharedModule } from '@shared/shared.module';
import { ProfileCardComponent } from './profile-card/profile-card.component';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MonitorStoreService, SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorHomeComponent', () => {
  let component: MonitorHomeComponent;
  let fixture: ComponentFixture<MonitorHomeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        MonitorHomeComponent,
        ProfileCardComponent
      ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        NgbModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorHomeComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
