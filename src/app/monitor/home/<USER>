<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="bg-gray">
    <!-- Nav tabs -->
    <ul ngbNav #monitorTabs="ngbNav" [activeId]="monitorTab" class="pb-0 mb-0 nav-tabs main-tabs nav-1 container d-flex justify-content-center"
      (navChange)="toggleMonitorTab($event.nextId)">
      <li [ngbNavItem]="'newDocuments'">
        <a ngbNavLink>New publications</a>
      </li>
      <li [ngbNavItem]="'legalStatus'">
        <a ngbNavLink>Legal status changes</a>
      </li>
    </ul>
    <!-- /Nav tabs -->
  </div>

  <div class="flex-fill clt-container">
    <div class="page-content pb-4 container">
      <div [ngbNavOutlet]="monitorTabs"></div>
      <div [@fadeIn]>
          <!-- Tab panes -->
          <!-- /Tab panes -->
          <div class="users-profiles" *ngIf="!isFreeUser">
            <app-page-bar pageTitle="My Profiles">
              <div leftSide>
                <div class="d-flex justify-content-start align-items-center">
                  <div class="input-group justify-content-between align-items-end col-5 ps-0 me-3" >
                    <input type="text" class="form-control border-end-0 flex-fill filter-input" placeholder="Search for an existing profile" [(ngModel)]="filter" (ngModelChange)="onChangeFilter()">
                    <button ngbTooltip="Search for a term" class="btn border-start-0 filter-button" (click)="filterProfile()"><i class="fa fa-search icon"></i></button>
                  </div>
      
                  <span >SORT BY:</span>
                  <div ngbDropdown class="position-relative mx-2">
                    <div ngbDropdownToggle tabindex="2" class=" caret-off">
                      <input class="form-control" [value]="getSortLabel()" readonly style="cursor: pointer;">
                      <div class="dropdown-icon"></div>
                    </div>
                    <div ngbDropdownMenu>
                      <a *ngFor="let item of listSort" class="dropdown-item" [ngClass]="item.value === sortBy ? 'active' : ''"
                         (click)="onSortBy(item.value)">{{ item.label }}</a>
                    </div>
                  </div>
      
                  &nbsp;
                  <a (click)="onSortOrder(sortOrder!='desc' ? 'desc' : 'asc')"  class="sort-order " href="javascript:void(0)">
                    <i class="fas fa-sort-amount-down-alt" [hidden]="sortOrder!='desc'"></i>
                    <i class="fas fa-sort-amount-up-alt" [hidden]="sortOrder!='asc'"></i>
                  </a>
                </div>
              </div>
    
              <ng-container rightSide>
                <span *ngIf="pagination && pagination.total_hits > 0" class="d-flex align-content-center flex-wrap count-stat color-2">
                  {{ pagination | countStat }}
                </span>
              </ng-container>
            </app-page-bar>

            <app-spinner *ngIf="loadingProfile"></app-spinner>
            <div *ngIf="!loadingProfile">
            <div class="mb-2" *ngIf="isCollaboratorUser">
              <app-alert type="warning" message="Creating new monitor profiles is not possible for you, but you still have full access to any existing ones." 
              [headline]="'Collaborator’s user rights'" version="figma"></app-alert>
            </div>
            <app-alert type="info" headline="Disclaimer:" [hideText]="!isFirstTime" *ngIf="monitorTab === 'legalStatus'" [message]="disclaimer"
            [version]="isCollaboratorUser ? 'figma': 'current'"></app-alert>
            <app-alert type="danger" [message]="errors" *ngIf="errors.length > 0" [version]="isCollaboratorUser ? 'figma': 'current'"></app-alert>
            <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>
            <app-alert type="info" *ngIf="pagination && pagination.total_hits === 0 && !isCollaboratorUser"
                       [message]="noProfileMessage"></app-alert>
            <div *ngIf="visibleProfile" class="profiles-wrap monitor-boxes">
              <div class="cards-wrap">
                <a *ngIf="userService.canCreateMonitorProfile()" class="single-card profile-single create-profile-box"
                  [class.disabled]="isCollaboratorUser"
                  (click)="onCreateProfile()" href="javascript:void(0)" ngbTooltip="Add new monitoring profile"
                  data-intercom-target="new-monitoring-profile">
                  <span class=" m-auto"><i class="fa fa-plus fa-3x"></i></span>
                </a>
                <!-- Profiles item -->
                <app-profile-card class="single-card profile-single single-profile-card" [profile]="p" *ngFor="let p of  visibleProfile" [storeService]="storeService" (deleteEvent)="deleteProfile($event)"></app-profile-card>
                <!-- ./Profiles item -->
              </div>
            </div>
            <div class="row" *ngIf="pagination">
              <div class="col-lg-12">
                <app-pagination [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
              </div>
            </div>
            </div>
          </div>

          <div *ngIf="visibleSharedProfile">
            <app-page-bar pageTitle="Shared Profiles">

              <div leftSide>
                <div class="d-flex justify-content-start align-items-center" *ngIf="isFreeUser">
                  <div class="input-group justify-content-between align-items-end col-5 ps-0 me-3" >
                    <input type="text" class="form-control border-end-0 flex-fill filter-input" placeholder="Search for an existing profile" [(ngModel)]="filter" (ngModelChange)="onChangeFilter()">
                    <button ngbTooltip="Search for a term" class="btn border-start-0 filter-button" (click)="filterProfile()"><i class="fa fa-search icon"></i></button>
                  </div>
      
                  <span >SORT BY:</span>
                  <div ngbDropdown class="position-relative mx-2">
                    <div ngbDropdownToggle tabindex="2" class=" caret-off">
                      <input class="form-control" [value]="getSortLabel()" readonly style="cursor: pointer;">
                      <div class="dropdown-icon"></div>
                    </div>
                    <div ngbDropdownMenu>
                      <a *ngFor="let item of listSort" class="dropdown-item" [ngClass]="item.value === sortBy ? 'active' : ''"
                         (click)="onSortBy(item.value)">{{ item.label }}</a>
                    </div>
                  </div>
      
                  &nbsp;
                  <a (click)="onSortOrder(sortOrder!='desc' ? 'desc' : 'asc')"  class="sort-order " href="javascript:void(0)">
                    <i class="fas fa-sort-amount-down-alt" [hidden]="sortOrder!='desc'"></i>
                    <i class="fas fa-sort-amount-up-alt" [hidden]="sortOrder!='asc'"></i>
                  </a>
                </div>
              </div>
    
              <ng-container rightSide>
                <span *ngIf="sharedPagination && sharedPagination.total_hits > 0 " class="d-flex align-content-center flex-wrap count-stat color-2">
                  {{ sharedPagination | countStat }}
                </span>
              </ng-container>
            </app-page-bar>

            <app-spinner *ngIf="loadingSharedProfile"></app-spinner>
            <div *ngIf="!loadingSharedProfile">
            <app-alert type="info" [message]="filter.trim().length>0 ? 'No shared monitor results based on your filter. Try to clear or change your filter.': 'No monitor results have been shared with you yet'"
              *ngIf=" visibleSharedProfile.length === 0" [version]="isCollaboratorUser ? 'figma': 'current'"></app-alert>
            <div class="monitor-boxes">
                <div class="cards-wrap">
                  <app-shared-profile-card *ngFor="let p of visibleSharedProfile" [profile]="p" class="single-card profile-single single-profile-card" (deleteEvent)="deleteSharedProfile($event)"></app-shared-profile-card>
                </div>
            </div>                   
            <div class="row" *ngIf="sharedPagination && sharedPagination.last_page>1">
              <div class="col-lg-12">
                <app-pagination [pagination]="sharedPagination" (navigatePage)="navigateShared($event)"></app-pagination>
              </div>
            </div>
            </div>
          </div>
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<app-contact-us-banner></app-contact-us-banner>
