import { Component, OnDestroy, OnInit } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { finalize, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { BooleanSearchService, MatomoService, MonitorService, PaginationMetadata, ToastService, UserService } from '@core/services';
import { BaseMonitorStoreService, MonitorLegalStoreService, MonitorStoreService } from '@core/store';
import { MonitorProfile, MonitorSharedProfile } from '@core/models';

/**
 * Main home component of monitoring feature
 */
@Component({
  selector: 'app-monitor-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
    ]),
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ])
  ]
})
export class MonitorHomeComponent implements OnInit, OnDestroy {
  pagination: PaginationMetadata;
  sharedPagination: PaginationMetadata;
  loadingProfile: boolean;
  loadingSharedProfile: boolean;
  errors: Array<string> = [];
  successMessage: string = '';
  visibleProfile: Array<MonitorProfile>;
  visibleSharedProfile: MonitorSharedProfile[];
  disclaimer = `We currently cover the legal data for: AU, CA, CN, DE, EP, ES, FR, GB, JP and US. Note: We rely on
  legal status data that we obtain from our data provider IFI Claims. Octimine takes no responsibility
  for (i) the accuracy of the data, or the (ii) the accuracy or timeliness of status change notifications.
  In cases of legal importance please refer to official sources or consult a patent attorney.`;
  monitorTab = 'newDocuments';
  private debounce: any;
  filter = '';
  sortOrder = 'asc';
  sortBy: string = 'name';
  listSort = [{label: 'Alphabetically', value: 'name'},
    {label: 'Recently added', value: 'created_at'},
    {label: 'Latest updated', value: 'updated_at'}];

  private subscriptions = new Subscription();
  public storeService: BaseMonitorStoreService;

  constructor(
    public monitorService: MonitorService,
    private monitorStoreService: MonitorStoreService,
    private monitorLegalStoreService: MonitorLegalStoreService,
    private booleanSearchService: BooleanSearchService,
    public userService: UserService,
    private toastService: ToastService,
    public router: Router,
    private route: ActivatedRoute,
    private matomoService: MatomoService
  ) {
  }

  get profiles(): Array<MonitorProfile> {
    return this.storeService.monitorProfiles;
  }

  get sharedProfiles(): Array<MonitorSharedProfile> {
    return this.storeService.sharedProfiles;
  }

  get isFirstTime(): boolean {
    return !this.userService.getUISetting('legal_status_monitor', false);
  }

  get isFreeUser(): boolean {
    return this.userService.isFreeUser();
  }

  get isLegalStatusPage(): boolean {
    return this.route.snapshot.data?.isLegalStatus;
  }

  get isCollaboratorUser(): boolean {
    return this.userService.isCollaboratorUser();
  }

  get noProfileMessage(){
    if(this.userService.canCreateMonitorProfile()){
      if(this.filter.trim().length>0){
        return  'No monitor profile based on your filter. Try to clear or change your filter.';
      }
      return  'Please click on the plus card to create your first profile';
    }
    return 'No monitor profile available yet';
  }

  ngOnInit() {
    this.storeService = this.route.snapshot.data?.isLegalStatus ? this.monitorLegalStoreService : this.monitorStoreService;
    this.monitorTab = this.route.snapshot.data?.isLegalStatus ? 'legalStatus' : 'newDocuments';

    const errors$ = this.monitorService.errors.subscribe({
      next: x => { this.errors = x; },
      error: err => { console.log('monitor error Observer got a error' + err); },
      complete: () => { console.log('monitor error Observer got a complete notification'); }
    });
    this.subscriptions.add(errors$);

    if (!this.userService.getUser()?.profile) {
      return;
    }

    if (this.isFirstTime) {
      const updateUISettings$ = this.userService.updateUISettings({legal_status_monitor: true})
        .subscribe();
      this.subscriptions.add(updateUISettings$);
    }

    this.storeService.setFilters([], false);
    this.booleanSearchService.filterClauses = [];
    this.loadMonitorData();
    const logActivity$ = this.userService.logActivity('PREVIEW_MONITOR_PROFILES')
      .subscribe();
    this.subscriptions.add(logActivity$);
  }

  getSortLabel() {
    return this.listSort.find(item => item.value === this.sortBy).label;
  }

  onSortBy(val: string) {
    this.sortBy = val;
    this.loadMonitorData();
  }

  onSortOrder(val: string) {
    if (this.sortOrder !== val) {
      this.sortOrder = val;
      this.loadMonitorData();
    }
  }

  onChangeFilter() {
    if (this.debounce) {
      clearTimeout(this.debounce);
    }
    this.debounce = setTimeout(() => {
      this.loadMonitorData();
    }, 300);
  }

  loadMonitorData(): void {
    this.getProfiles();
    this.getSharedProfiles();
  }

  getProfiles(){
    if (!this.isFreeUser) {
      const params = {
        page: this.pagination ? this.pagination.current_page : 1,
        legal_status_active: this.isLegalStatusPage ? 1 : 0,
        sort_by: this.sortBy,
        sort_order: this.sortOrder
      };

      if (this.filter) {
        params['name'] = `like:%${this.filter.trim()}%`;
      }
      this.loadingProfile = true;
      const loadProfiles$ = this.monitorService.loadProfiles(this.storeService, false, params).pipe(finalize(() => this.loadingProfile = false)).subscribe({
          next: ({page}) => {
            this.pagination = page;
            this.sortProfiles();
          },
          error: (err) => {
            console.log(err);
            this.monitorService.setErrors(['Error while loading monitor profiles.']);
          }
        });
      this.subscriptions.add(loadProfiles$);
    }
  }

  /**
    * load shared run monitor profiles
    */
  getSharedProfiles(){
    const params = {
      page: this.sharedPagination ? this.sharedPagination.current_page : 1,
      legal_status_active: this.isLegalStatusPage ? 1 : 0,
      sort_by: this.sortBy,
      sort_order: this.sortOrder
    };

    if (this.filter) {
      params['name'] = `like:%${this.filter.trim()}%`;
    }
    this.loadingSharedProfile = true;
    const loadSharedProfiles$ = this.monitorService.loadSharedProfiles(params).pipe(finalize(() => this.loadingSharedProfile = false)).subscribe({
      next: ({profiles, page}) => {
        this.storeService.setSharedProfiles(profiles);
        this.sharedPagination = page;
        this.updateVisibleSharedProfile();
      },
      error: (err) => {
        this.monitorService.setErrors(['Error while loading monitor shared profiles.']);
      }
    });
    this.subscriptions.add(loadSharedProfiles$);
  }

  /**
   * Delete monitor profile
   *
   * @description Event listener for delete single monitor profile on monitor home page
   * @param ID ID of profile to be deleted
   */
  deleteProfile(ID: number) {
    if (this.isFreeUser) {
      return;
    }
    this.loadingProfile = true;
    this.successMessage = undefined;
    const deleteProfile$ = this.monitorService.deleteProfile(ID).pipe(finalize(() => this.loadingProfile = false))
      .subscribe({
        next: response => {
          this.successMessage = 'Profile deleted successfully.';
          this.storeService.monitorProfiles = this.profiles.filter(x => x.id !== ID);
          this.sortProfiles();
        }, error: error => {
          this.monitorService.setErrors(['Error while deleting profile.']);
        }
      });
    this.subscriptions.add(deleteProfile$);
  }

  /**
   * Delete monitor profile
   *
   * @description Event listener for delete single monitor profile on monitor home page
   * @param ID ID of profile to be deleted
   */
  deleteSharedProfile(ID: number) {
    this.loadingSharedProfile = true;
    this.successMessage = undefined;
    const deleteSharedProfile$ = this.monitorService.deleteSharedProfile(ID).pipe(finalize(() => this.loadingSharedProfile = false))
      .subscribe({
        next: data => {
          this.successMessage = 'Profile deleted successfully.';
          const sharedProfiles = this.sharedProfiles.filter(x => x.id !== ID);
          this.storeService.setSharedProfiles(sharedProfiles);
          this.sortProfiles();
        }, error: error => {
          this.monitorService.setErrors([error.error.status === 400 ? error.error.message : 'Error while deleting shared profile.']);
        }
      });
    this.subscriptions.add(deleteSharedProfile$);
  }

  /**
   * page navigation
   * @description event listener for age navigation on monitor home page for monitor profile
   * @param page Page number
   */
  navigate(page) {
    this.pagination.current_page = page;
    this.getProfiles();
  }

  navigateShared(page) {
    this.sharedPagination.current_page = page;
    this.getSharedProfiles();
  }

  toggleMonitorTab(tab: 'legalStatus' | 'newDocuments') {
    this.router.navigate([`/monitor/${tab === 'legalStatus' ? tab : ''}`]);
  }

  onCreateProfile() {
    if(this.isCollaboratorUser){
      return
    }
    this.storeService.monitorProfile = null;
    this.monitorService.openNewProfilePage(this.storeService.monitorProfile, this.route.snapshot.data?.isLegalStatus);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.storeService.monitorProfiles = null;
    this.storeService.setSharedProfiles(null);
    this.toastService.clear();
  }

  private sortProfiles() {
    if (this.profiles) {
      this.visibleProfile = this.profiles
    }
    this.updateVisibleSharedProfile();
  }

  private updateVisibleSharedProfile() {
    if (!this.sharedProfiles) {
      return;
    }
    const currentUserId = this.userService.getUser()?.profile?.id;
    this.visibleSharedProfile = this.sharedProfiles.filter(p => p.shared_by?.id !== currentUserId);
  }
}
