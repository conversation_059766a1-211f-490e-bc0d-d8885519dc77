<div class="cursor-pointer" [ngClass]="{'inactivated':!profile?.active}" (click)="openProfile(profile)"
     appSelectableTooltip [selectableTooltipPopover]="t1" #t1="ngbPopover"
     [ngbPopover]="dropdown.isOpen() ? null : cardTooltip"
     triggers="manual" [autoClose]="'outside'" popoverClass="popover-md">
<div class="abs" ngbDropdown placement="bottom-end" #dropdown="ngbDropdown">
  <span class="icon profile-dropdown-option" ngbDropdownToggle aria-haspopup="true" (click)="$event.stopPropagation();" aria-expanded="true">
    <i class="fas fa-ellipsis-v" fill="#4698b4"></i>
  </span>
  <div class=" shadow-lg" ngbDropdownMenu>
    <a (click)="$event.stopPropagation();" [routerLink]="[profile?.legal_status_active ? '/monitor/legalStatus/profile': '/monitor/profile',profile?.id ]" class="profile-open pt-1 pb-1 ps-4 dropdown-item" ngbTooltip="Open profile" container="body"><i class="fa fa-eye me-2 fa-fw"></i>Open</a>
    <a (click)="$event.stopPropagation();" [routerLink]="[profile?.legal_status_active ? '/monitor/legalStatus/profile': '/monitor/profile',profile?.id,'setting']" class="profile-edit pt-1 pb-1 ps-4 dropdown-item" ngbTooltip="Edit profile"><i class="fa fa-edit me-2 fa-fw"></i>Edit</a>
    <a (click)="cloneProfile($event, profile?.id)" class="profile-clone pt-1 pb-1 ps-4 dropdown-item" ngbTooltip="Duplicate profile"><i class="fa fa-copy me-2 fa-fw"></i>Duplicate</a>
    <div class="dropdown-divider mt-0 mb-0"></div>
    <a (click)="deleteProfile($event, profile?.id)" class="profile-remove pt-1 pb-1 ps-4 dropdown-item" href="javascript:void(0)"><i class="fa fa-trash me-2 fa-fw"></i>Remove</a>
  </div>
</div>
<div class="single-card-header">
  <span class="card-title m-r-spacing-xxx-big">{{profile?.name | uppercase}}</span>
</div>

<div class="single-card-footer d-flex justify-content-between">
  <div class="bottom-left">
    <span><img src="assets/images/round-update-24px.svg" alt=""> {{profile?.delivery_frequency}}</span>
  </div>
  <div class="bottom-right">
    <!-- Rounded switch -->
    <label class="check-on-off" (click)="$event.stopPropagation(); toggleProfile($event, profile)">
      <input type="checkbox" [checked]="profile?.active" class="left" />
      <span>{{ profile?.active ? 'ACTIVE': 'INACTIVE'}}</span>
    </label>
  </div>
</div>
</div>

<ng-template #cardTooltip>
  <div>
    <div class="d-flex content-heading-h6 m-b-spacing-sm text-break" *ngIf="isCardTitleTruncated()">{{profile?.name | uppercase }}</div>
    <div class="d-flex m-b-spacing-sm" *ngIf="profile?.company">{{profile?.company}}</div>
    <div class="d-flex m-b-spacing-sm text-break" *ngIf="profile?.description?.length">{{ profile?.description }}</div>
    <div class="d-flex m-b-spacing-sm method-list" *ngIf="!profile?.legal_status_active">
      <a class="badge to-deeplearning me-1" (click)="openProfile(profile, 'deeplearning')" href="javascript:void(0)"
        [ngClass]="!profile.machine_learning_active ? 'badge-light' : 'badge-primary'" ngbTooltip="Deep learning">DL</a>
      <a class="badge to-semantic me-1" *ngIf="!storeService.isPublicationProfile(profile)" (click)="openProfile(profile, 'semantic')" href="javascript:void(0)"
        [ngClass]="!profile.semantic_query_active ? 'badge-light' : 'badge-primary'" ngbTooltip="Semantic">S</a>
      <a class="badge to-boolean me-1" (click)="openProfile(profile, 'boolean')" href="javascript:void(0)"
        [ngClass]="!profile.boolean_query_active ? 'badge-light' : 'badge-primary'" ngbTooltip="Boolean">B</a>
    </div>

    <app-user-avatars *ngIf="hasWorkflowFeature() && profile && avatarService.hasUsersAndGroups(profile.users, profile.groups)"
                      [users]="profile.users" [groups]="profile.groups" [numberDisplayedUsers]="2" [numberDisplayedGroups]="2"
                      [avatarSize]="30" [distanceBetweenAvatars]="25" [avatarFontSize]="15"
                      avatarsTooltipPrefix="Shared with" (click)="openShareWith(profile)" class="cursor-pointer mt-2">
    </app-user-avatars>
  </div>
</ng-template>
