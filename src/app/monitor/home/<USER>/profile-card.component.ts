import { Component, ElementRef, EventEmitter, Input, Output } from '@angular/core';
import { BaseMonitorStoreService, MonitorStoreService } from '@core/store';
import { Router } from '@angular/router';
import { AvatarService, ConfirmationDialogService, MonitorService, ToastService, ToastTypeEnum, UserService } from '@core/services';
import { MonitorProfile} from '@core/models';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ShareDialogComponent, ShareDialogResult, ShareDialogShareTypeEnum } from '@shared/components';
import { Location } from '@angular/common';

/**
 * Single monitor profile card component of monitoring feature
 */
@Component({
  selector: 'app-profile-card',
  templateUrl: './profile-card.component.html',
  styleUrls: ['./profile-card.component.scss']
})
export class ProfileCardComponent {
  @Input() profile: MonitorProfile;
  @Input() storeService: BaseMonitorStoreService;

  @Output() deleteEvent: EventEmitter<number> = new EventEmitter();

  get loading(): boolean {
    return this.storeService.loading;
  }

  set loading(state: boolean) {
    this.storeService.loading = state;
  }

  get isLegalStatus(): boolean {
    return this.location.path().indexOf('legalStatus') > -1;
  }

  constructor(
    public monitorService: MonitorService,
    private ngbModal: NgbModal,
    private toastService: ToastService,
    private confirmationService: ConfirmationDialogService,
    private router: Router,
    public userService: UserService,
    public avatarService: AvatarService,
    private location: Location,
    private elementRef: ElementRef
  ) {
  }

  cloneProfile(event, ID: number) {
    event.preventDefault();
    event.stopPropagation();
    this.monitorService.cloneProfile(ID).subscribe({next: profile => {
      this.openProfile(profile, 'setting');
      }, error: err => {
        console.log(err);
        this.monitorService.setErrors([err.error.message]);
        this.loading = false;
    }});
  }

  /**
   * Delete monitoring profile
   * @description event listener for single monitoring profile deletion
   * @param ID Id of monitoring profile
   * @emits ID ID of monitor profile
   */
  deleteProfile(event, ID: number) {
    event.preventDefault();
    event.stopPropagation();
    this.confirmationService.confirm(
      'Delete monitor profile',
      'Do you really want to remove this profile?',
      'Yes',
      'No')
      .then((confirmed) => {
        if (confirmed) {
          this.deleteEvent.emit(ID);
        }
      }).catch(() => {
    });
  }

  /**
   * Toggle profile state
   * @description Event listener for toggling profile state to enable/disable
   * @param state State of profile to be update
   * @param profile profile to be update
   */
  toggleProfile(state, profile: MonitorProfile) {
    if(this.loading) return
    this.loading = true;
    this.monitorService.updateProfile(profile.id, {active: state.target.checked}).then(res => {
      profile.active = state.target.checked;
      this.loading = false;
    }).catch(
      err => {
        console.log(err);
        this.monitorService.setErrors(['Error while changing profile status.']);
        this.loading = false;
      });
  }

  openShareWith(profile: MonitorProfile) {
      if (!this.userService.canUseSaveAndShareFeature('share')) {
        return;
      }
    const modal = this.ngbModal.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.selectedTeamUsers = profile.users;
    modal.componentInstance.sharedTeamUsers = profile.users;
    modal.componentInstance.selectedGroups = profile.groups;
    modal.componentInstance.sharedGroups = profile.groups;
    modal.componentInstance.stopSharing = true;
    modal.componentInstance.createNewCollection = false;
    modal.componentInstance.shareText = 'Select';
    modal.componentInstance.headline = `Share monitor results`;
    modal.componentInstance.hideAgreementCheck = true;
    modal.componentInstance.shareType = ShareDialogShareTypeEnum.SHARE_WITH_TEAM;
    modal.componentInstance.canSwitchShareType = false;
    modal.componentInstance.storeService = this.storeService;

    modal.result.then((data: ShareDialogResult) => {
      if (profile) {
        this.loading = true;
        const payload = {user_ids: data.shared_users.map(u => u.id), group_ids: data.shared_groups.map(g => g.id)}
        this.monitorService.updateProfile(profile.id, payload).then(res => {
          profile.users = data.shared_users;
          profile.groups = data.shared_groups;
          this.loading = false;
        }).catch(
          err => {
            console.log(err);
            this.loading = false;
            this.monitorService.setErrors(['Error while saving updated shared users/groups.']);
          });
      }
    }, reason => { console.log(reason); });
  }

  openProfile(profile: MonitorProfile, section ?: string) {
    const u = profile.legal_status_active ? ['/monitor/legalStatus/profile', profile.id] :
              ['/monitor/profile', profile.id];
    if (section) {
      u.push(section);
    }
    this.router.navigate(u);
  }


  hasWorkflowFeature() {
    return this.userService.canUseWorkflowFeature();
  }

  isCardTitleTruncated(): boolean {
    const cardTitleEle = (this.elementRef.nativeElement as HTMLElement).querySelector<HTMLElement>('.card-title');
    if (!cardTitleEle) {
      return false;
    }
    return cardTitleEle.scrollHeight > cardTitleEle.clientHeight + 1;
  }
}
