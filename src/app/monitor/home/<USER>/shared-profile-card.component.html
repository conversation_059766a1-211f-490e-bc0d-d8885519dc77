<div class="cursor-pointer" (click)="openShareProfile(profile)" appSelectableTooltip [selectableTooltipPopover]="t1"
     #t1="ngbPopover" [ngbPopover]="isCardTitleTruncated() || profile?.description?.length ? cardTooltip : null"
     triggers="manual" [autoClose]="'outside'" popoverClass="popover-sm">
  <div class="single-card-header">
    <div class="card-title"> {{profile?.name | uppercase }} </div>
  </div>
  <div class="single-card-footer d-flex justify-content-between">
    <div class="bottom-left"></div>
    <div class="bottom-left">
      <div class="d-flex flex-row justify-content-end align-items-center cursor-pointer monitor-shared-by-avatar" *ngIf="profile?.shared_by">
        <div class="clt-collaboration-share-title">SHARED BY</div>
        <app-user-avatar [user]="profile.shared_by" [hasSubTitle]="false" [avatarSize]="20" [avatarFontSize]="10" class="clt-text-avatar"></app-user-avatar>
      </div>
    </div>
  </div>
</div>
<ng-template #cardTooltip>
  <div class="">
    <div class="d-flex content-heading-h6 m-b-spacing-sm text-break" *ngIf="isCardTitleTruncated()">{{profile?.name | uppercase }}</div>
    <div class="d-flex text-break" *ngIf="profile?.description?.length">{{ profile?.description }}</div>
  </div>
</ng-template>
