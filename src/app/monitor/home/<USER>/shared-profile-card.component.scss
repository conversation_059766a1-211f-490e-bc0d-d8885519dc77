@use 'layout2021/variables' as variables;

.profile-single.create-profile-box {
  border: 5px dashed #d8d8d8;
  padding: 0;
  display: flex;
  min-height: 250px;

  .fa-plus {
    color: #d8d8d8;
  }
  &:hover{
    border-color: variables.$brand-green;
    .fa-plus{
      color: variables.$brand-green;
    }
  }
}
.short-name {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  color: #fff;
  background: #b1b1b1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  margin: auto auto 10px;
  font-family: variables.$font-open-sans-bold;
}
.profile-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  color: variables.$color-text-04;
  text-decoration: none !important;
  font-family: variables.$font-open-sans-bold;
}
.profile-description, .profile-company {
  color: variables.$color-text-02;
  font-size: .8em;
}
.profile-info{
  position: relative;
  &:before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60px;
    height: 1px;
    background: #b1b1b1;
    margin-left: -30px;
  }
}

.dropdown.abs{
  z-index: 99;
}

.clt-collaboration-share-title {
  color: #00a083;
  margin-right: 3px;
}
