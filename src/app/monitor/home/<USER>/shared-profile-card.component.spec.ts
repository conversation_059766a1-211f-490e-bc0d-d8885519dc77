import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SharedProfileCardComponent } from './shared-profile-card.component';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

describe('SharedProfileCardComponent', () => {
  let component: SharedProfileCardComponent;
  let fixture: ComponentFixture<SharedProfileCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SharedProfileCardComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SharedProfileCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
