import { Component, ElementRef, EventEmitter, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import { CollaboratorTypeEnum, ConfirmationDialogService, MonitorSharedProfile } from '@core';

@Component({
  selector: 'app-shared-profile-card',
  templateUrl: './shared-profile-card.component.html',
  styleUrls: ['./shared-profile-card.component.scss']
})
export class SharedProfileCardComponent {
  @Input() public profile: MonitorSharedProfile;

  @Output() deleteEvent: EventEmitter<Object> = new EventEmitter();

  constructor(
    private router: Router,
    private confirmationService: ConfirmationDialogService,
    private elementRef: ElementRef
  ) { }

  /**
   * Delete monitoring profile
   * @description event listener for single monitoring profile deletion
   * @param ID Id of monitoring profile
   * @emits ID ID of monitor profile
   */
   deleteSharedProfile(ID: number) {
    this.confirmationService.confirm(
      'Delete monitor profile',
      'Do you really want to remove this profile?',
      'Yes',
      'No')
      .then((confirmed) => {
        if (confirmed) {
          this.deleteEvent.emit(ID);
        }
      }).catch(() => {
    });
  }

  openShareProfile(profile) {
    this.router.navigate([(profile.legal_status_active ? '/monitor/legalStatus' : '/monitor')  , 'shared-profile', profile.id]);
  }

  canDelete() {
    return !(this.profile?.collaborators || []).some(collaborator => collaborator.type === CollaboratorTypeEnum.GROUP);
  }

  isCardTitleTruncated(): boolean {
    const cardTitleEle = (this.elementRef.nativeElement as HTMLElement).querySelector<HTMLElement>('.card-title');
    if (!cardTitleEle) {
      return false;
    }
    return cardTitleEle.scrollHeight > cardTitleEle.clientHeight + 1;
  }
}
