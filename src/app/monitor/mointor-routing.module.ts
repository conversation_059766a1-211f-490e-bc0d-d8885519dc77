import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard, FeatureGuard } from '@core/guards';
import { MonitorHomeComponent } from './home/<USER>';
import { MonitorProfileComponent } from './monitor-profile/monitor-profile.component';
import { SharedProfileComponent } from './shared-profile/shared-profile.component';
import { SubscriptionComponent } from './subscription/subscription.component';

const routes: Routes = [
  {path: '', component: MonitorHomeComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'legalStatus', component: MonitorHomeComponent, canActivate: [AuthGuard, FeatureGuard], data: {isLegalStatus: true}},
  {path: 'profile', redirectTo: '', pathMatch: 'full'},
  {path: 'profile/:id', component: MonitorProfileComponent, canActivate: [AuthGuard, FeatureGuard, FeatureGuard]},
  {path: 'legalStatus/profile/:id', component: MonitorProfileComponent, canActivate: [AuthGuard, FeatureGuard, FeatureGuard], data: {isLegalStatus: true}},
  {path: 'profile/:id/:sectionName', component: MonitorProfileComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'legalStatus/profile/:id/:sectionName', component: MonitorProfileComponent, canActivate: [AuthGuard, FeatureGuard], data: {isLegalStatus: true}},
  {path: 'profile/:id/email/subscription', component: SubscriptionComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'shared-profile/:id', component: SharedProfileComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'legalStatus/shared-profile/:id', component: SharedProfileComponent, canActivate: [AuthGuard, FeatureGuard]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MonitorRoutingModule {
}
