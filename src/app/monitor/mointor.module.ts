import { NgModule } from '@angular/core';

import { SharedModule } from '../shared';
import { MonitorHomeComponent } from './home/<USER>';
import { MonitorRoutingModule } from './mointor-routing.module';
import { ProfileCardComponent } from './home/<USER>/profile-card.component';
import { MonitorProfileComponent } from './monitor-profile/monitor-profile.component';
import { MonitorSettingComponent } from './monitor-profile/monitor-setting/monitor-setting.component';
import { MonitoringProfileComponent } from './monitor-profile/monitoring-profile/monitoring-profile.component';
import { MonitorBooleanComponent } from './monitor-profile/monitor-boolean/monitor-boolean.component';
import { MonitorSemanticComponent } from './monitor-profile/monitor-semantic/monitor-semantic.component';
import { MonitorMachineLearningComponent } from './monitor-profile/monitor-machine-learning/monitor-machine-learning.component';
import { MonitorResultsComponent } from './monitor-profile/monitor-results/monitor-results.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ChartDashboardModule, MonitorChartsModule } from '@shared/charts';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedProfileComponent } from './shared-profile/shared-profile.component';
import { SharedProfileCardComponent } from './home/<USER>/shared-profile-card.component';
import { MonitorListComponent } from './monitor-profile/monitor-list/monitor-list.component';
import { MonitorLegalStatusResultComponent } from './monitor-profile/monitor-legal-status-result/monitor-legal-status-result.component';
import { SubscriptionComponent } from './subscription/subscription.component';
import { ChartsModule } from '@shared/charts/charts.module';

@NgModule({
  imports: [
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    NgxSliderModule,
    NgbModule,
    ChartsModule,
    MonitorChartsModule,
    ChartDashboardModule,
    MonitorRoutingModule
  ],
  declarations: [
    MonitorHomeComponent,
    ProfileCardComponent,
    MonitorProfileComponent,
    MonitorSettingComponent,
    MonitoringProfileComponent,
    MonitorBooleanComponent,
    MonitorSemanticComponent,
    MonitorMachineLearningComponent,
    MonitorResultsComponent,
    SharedProfileComponent,
    SharedProfileCardComponent,
    MonitorListComponent,
    MonitorLegalStatusResultComponent,
    SubscriptionComponent,
  ],
  exports: [
    ChartsModule,
    MonitorChartsModule,
    ChartDashboardModule
  ]
})
export class MonitorModule {
}
