<div class="container pb-4" data-intercom-target="boolean-input">
  <div class="d-flex justify-content-between align-items-center">
    <h4 class="profile-headline">
      Boolean profile
      <app-tooltip id="monitor_boolean" [tooltipTitle]='tooltip.BooleanTitle' [tooltipText]='tooltip.BooleanText'></app-tooltip>
      &nbsp;
      <label class="check-on-off method-state h-100 mb-0 align-middle" ngbTooltip="Enable/Disable boolean profile">
        <input type="checkbox" [checked]="profile?.boolean_query_active" class="left" (click)="checkStatus($event)" />
      </label>
    </h4>
    <app-boolean-query-options class="ms-auto" [(advancedMode)]="showAdvancedMode" [isBooleanSearch]="false"></app-boolean-query-options>
  </div>
  <form>
  <!-- Input box -->
  <div class="monitoring-inputBox-wrap">
    <ngb-alert *ngIf="validateMessage" (close)="validateMessage = null" type="danger">
      <div [innerHTML]="validateMessage"></div>
    </ngb-alert>

    <ngb-alert *ngIf="messageAlert" (close)="messageAlert = null" type="danger">
      <div [innerHTML]="messageAlert"></div>
    </ngb-alert>
    <div >
      <app-boolean-input *ngIf="!showAdvancedMode" [booleanSearchService]="booleanService"
                         [storeService]="storeService" [clearEmptyClause]="true"
                         (doSubmit)="onSubmit($event)">
      </app-boolean-input>
      <div class="d-flex justify-content-end mb-2">
        <app-tooltip id='monitor_boolean_advanced'
                   tooltipTitle='Search the patent database' [tooltipText]='tooltipText'
                    tooltipIconSize="sm" *ngIf="showAdvancedMode"></app-tooltip>
      </div>
      <app-boolean-advanced-mode *ngIf="showAdvancedMode" [booleanSearchService]="booleanService" [storeService]="storeService" ></app-boolean-advanced-mode>
      <div class="d-flex">
        <a href="javascript:void(0)" (click)="switchMode()">
          <span *ngIf="!showAdvancedMode">Switch to advanced mode >>></span>
          <span *ngIf="showAdvancedMode">Switch to simple mode >>></span>
        </a>
      </div>
      <!-- <hr /> -->
    </div>
    <div class=" btn-wrap text-end mt-4">
      <button type="button" class="btn btn-secondary-outline btn-md ms-3 btn-icon clear-profile-method" ngbTooltip="Click here to reset profile"
        (click)="onReset()"><i class="fa fa-times"></i></button>
      <button type="button" [ngbTooltip]="isFormEmpty ? '' :'Click here to view input results'" [disabled]="disableSearch()"
        data-intercom-target="boolean-input-button"
        class="btn btn-primary btn-md ms-3" (click)="onSubmit(true)">VIEW INPUT</button>
      <button type="button" class="btn btn-primary btn-md ms-3" (click)="onSetupProfile()" [disabled]="disableSearch() || disableMonitoringButton()"
        data-intercom-target="boolean-setup-button"
        [ngbTooltip]="isFormEmpty ? '' :'Click here to setup monitoring profile'">SETUP MONITORING</button>
    </div>
  </div>
  <!-- ./Input box -->
  </form>
</div>

<div class="container-fluid">
<!-- Table -->
<div class="bl-table pb-4" *ngIf="searchData && searchData?.documents.length !== 0">
  <h3 data-intercom-target="Search preview">{{searchData?.page?.total_hits < 100 ? searchData?.page?.total_hits :'Top 100'}} search preview</h3>
  <app-patent-table [patents]="searchData.documents" [showPatentSelection]="false"
                    [pagination]="searchData.page" [hasLinksToBooleanSearch]="true"
                    [hideColumns]="['monitor_feedback','monitor_label']"
                    [searchHash]="searchData.search_info.search_hash" pathUrl="/patent" [linkData]="linkData"
                    [allowFilteringByLegalStatus]="false"
                    backButtonTitle="Back to monitor"
                    (legalStatusSelected)="onSelectLegalStatus($event)"
                    [storeService]="storeService">
  </app-patent-table>
  <app-pagination class="d-flex justify-content-end" [pagination]="searchData.page" [maxPage]="4" (navigatePage)="navigatePage($event)"></app-pagination>
  <app-filters-bar *ngIf="storeService.filters.length" [alwaysBeSticky]="true" [storeService]="storeService"></app-filters-bar>
</div>
<!-- ./Table -->
<app-alert type="info" *ngIf="searchData && searchData.documents.length == 0"
  message="No results available yet against this profile query."></app-alert>
</div>
