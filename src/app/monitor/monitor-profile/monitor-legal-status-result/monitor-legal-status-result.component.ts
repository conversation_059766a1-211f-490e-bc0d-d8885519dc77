import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AdvancedFilterService, BaseMonitorStoreService,
  ChartsService,
  CollectionStoreService,
  ConfirmationDialogService,
  MatomoService,
  MonitorProfile,
  MonitorService,
  PaginationMetadata,
  SortParams,
  TaskModel,
  TaskService,
  UserService,
  UserProfile
} from '@core';
import { NgbCalendar, NgbDate, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { catchError, finalize, take } from 'rxjs/operators';
import { Subscription, of } from 'rxjs';
import { columnsToShow } from './columns';
import { Router } from '@angular/router';
import { ViewModeTypeEnum } from '@search/patent/types';

@Component({
  selector: 'app-monitor-legal-status-result',
  templateUrl: './monitor-legal-status-result.component.html',
  styleUrls: ['./monitor-legal-status-result.component.scss']
})
export class MonitorLegalStatusResultComponent implements OnInit, OnD<PERSON>roy {
  @Input() storeService: BaseMonitorStoreService;
  charts;
  user: UserProfile;
  public pagination: PaginationMetadata;
  public additionalExportParams = {};
  /**
   * reference for app-patent-control-bar component
   */
  public columnsToShow = columnsToShow;
  /**
   * reference for patent table size for app-page-size component
   */
  public pageSize = 25;
  /**
   * reference for sort order and sort by on patent table
   */
  public sorting: SortParams = {field: null, order: null} as SortParams;
  public selectedSnapshot;
  public loadingResult = false;
  savedTaskMessages: string[] = null;
  /**
   * page number for result table navigation
   */
  private activePage: number = 1;
  /**
   * page number for monitor run table navigation
   */
  private activeMonitorRunPage: number = 1;
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    public router: Router,
    private confirmationService: ConfirmationDialogService,
    public collectionsStoreService: CollectionStoreService,
    public monitorService: MonitorService,
    private advancedFilterService: AdvancedFilterService,
    private matomoService: MatomoService,
    private taskService: TaskService,
    private chartService: ChartsService,
  ) {
    this.pagination = {
      current_page: 1,
      total_hits: 0,
      last_page: 1,
      page_size: 25,
      origin_total_hits: 0
    }
  }

  public get linkData() {
    return this.monitorService.linkData;
  }

  /**
   * getter for monitor run list
   */
  get monitorRuns() {
    return this.storeService.monitorRuns;
  }

  /**
   * setter for monitor run list
   */
  set monitorRuns(list) {
    this.storeService.setMonitorRuns(list);
  }

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  get scope(): string {
    return this.storeService.monitorProfile.legal_status_profile['scope'];
  }

  get documents(){
    return this.storeService.resultSetDocuments;
  }

  get totalSelectedPatents() {
    return this.storeService.selectedPublications.length;
  }

  get loading(): Boolean{
    return this.storeService.loading;
  }

  get viewMode (): ViewModeTypeEnum {
    return this.storeService.patentListViewMode;
  }

  get isCombinedMode(): boolean {
    return this.storeService.isCombinedMode;
  }

  get isListVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get isChartVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get hasDocuments(): boolean {
    return this.documents?.length > 0;
  }

  ngOnInit(): void {
    this.storeService.pageSize = this.pageSize;

    if (this.storeService.backPatentSearch) {
      this.setFieldsAfterBack();
      if (!this.storeService.scrollTopPage) {
        this.storeService.elementIdToScrollPage = 'monitor-results';
      }
      this.scrollToResults();
      this.storeService.backPatentSearch = false;
    } else {
      this.clearStoredData();
      this.clearStoredValues();
    }

    if (this.storeService.resultSetPagination) {
      this.activePage = this.storeService.resultSetPagination.current_page;
    }

    if (this.storeService.selectedMonitorRun) {
      this.getResult(true);
    }

    const user$ = this.userService.user.subscribe({
      next: u => this.user = u.profile
    });
    this.subscriptions.add(user$);

    const firstMonitorRun$ = this.storeService.firstMonitorRun.subscribe({
      next: value => {
        if (value) {
          this.getMonitorRuns(false);
        }
      },
      error: err => {
        console.error('monitor run id Observer got a error' + err);
      },
      complete: () => {
        console.error('monitor run id Observer got a complete notification');
      }
    });
    this.subscriptions.add(firstMonitorRun$);

    const selectedMonitorRunOb$ = this.storeService.selectedMonitorRunOb.subscribe({
      next: id => {
        if (id) {
          this.getResult(true);
        }
      }
    });
    this.subscriptions.add(selectedMonitorRunOb$);

    const searchHash$ = this.storeService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  /**
   * event listener for delete monitor run icon
   * @param monitorRun object of monitor run to be deleted
   */
  public deleteMonitorResultProfile(monitorRun: Object) {
    this.confirmationService.confirm('Delete result set', 'Do you really want to delete the result set?',
      'Yes', 'Cancel', 'lg').then((confirmed) => {
      if (confirmed) {
        this.storeService.loading = true;
        this.monitorService.deleteMonitorRun(monitorRun['id']).subscribe({
          next: (res) => {
            this.getMonitorRuns(monitorRun['id'] === this.storeService.selectedMonitorRun ? true : false);
          },
          error: err => {
            console.log(err);
            this.monitorService.setErrors(['Error while deleting Result set.']);
            this.storeService.loading = false;
          }
        });
      }
    }).catch(() => {
    });
  }

  onSharePatents(event) {
    this.getResult(false);
  }

  /**
   * apply single monitoring run
   * @param snapShot Object of single monitor run
   */
  getMonitorResultProfile(snapShot: Object) {
    if (snapShot['status'] !== 'Finished') {
      return;
    }
    if (snapShot['id']) {
      this.monitorService.updateURLRunID(snapShot['id']);
      this.storeService.setSelectedMonitorRun(snapShot['id']);
      this.selectedSnapshot = snapShot;
      this.prepareAdditionalExportParams();
    }
    this.activePage = 1;
    this.clearStoredData();
    this.matomoService.monitorLegalStatusTimeIntervalButton();
  }

  /**
   * load the list of monitor run table
   * @param loadFirst optional - weather to load first row or not
   */
  public getMonitorRuns(loadFirst = true) {
    if (this.profile) {
      this.storeService.loading = true;
      const queryParam = {monitor_profile_id: this.profile.id, page: this.activeMonitorRunPage, page_size: this.monitorService.MONITOR_RUNS_PAGE_SIZE};
      const getMonitorRuns$ = this.monitorService.getMonitorRuns(queryParam)
        .pipe(
          finalize(() => this.storeService.loading = false)
        )
        .subscribe({
          next: ({data}) => {
            this.storeService.setMonitorRuns(data);
            this.monitorRuns = data;
            if (data.page.total_hits > 0) {
              if (loadFirst) {
                this.storeService.setSelectedMonitorRun(data.monitor_runs[0].id);
                this.selectedSnapshot = data.monitor_runs[0];
                this.prepareAdditionalExportParams();
              }
            }
          }, error: (err) => {
            console.error(err);
            this.monitorService.setErrors(['Error while getting result set list.']);
          }
        });
      this.subscriptions.add(getMonitorRuns$);
    }
  }

  /**
   * get legal status monitor results
   */
  public getResult(reloadCharts: boolean) {
    this.storeService.elementIdToScrollPage = 'monitor-results-container';
    this.loadingResult = true;
    const query = this.buildQuery();
    this.storeService.setResultSetDocuments([]);
    const getLegalStatusMonitorResult$ = this.monitorService.getLegalStatusMonitorResult(this.storeService.selectedMonitorRun, query)
      .subscribe({ next: ({data}) => {
        this.storeService.setResultSetDocuments(data.documents);
        this.storeService.state.total_hits = data.page.total_hits;
        this.pagination = data.page;
        if (data?.search_info?.search_hash && data?.search_info?.search_hash !== this.storeService.searchHash) {
          reloadCharts = false;
          this.storeService.searchHash = data.search_info.search_hash;
        }
        this.storeService.search= {params: Object.assign({}, query)};
        this.storeService.pagination = this.pagination;

        if (!this.selectedSnapshot && this.storeService.monitorRuns) {
          this.selectedSnapshot = this.storeService.monitorRuns['monitor_runs'].find(
            r => r.id === this.storeService.selectedMonitorRun);
        }
        if (reloadCharts && data.documents?.length > 0) {
          this.getCharts();
        } else {
          this.storeService.loading = false;
          this.loadingResult = false;
          if (this.storeService.docdbFamilyIdFromPatentViewer) {
            this.storeService.scrollToPatentFromPatentViewer();
          } else {
            this.scrollToResults();
          }
        }

      }, error: (err) => {
        console.error(err);
        this.monitorService.setErrors(['Error while getting result set publications.']);
        this.loadingResult = false;
      }});
    this.subscriptions.add(getLegalStatusMonitorResult$);
  }

  getCharts() {
    this.loadingResult = true;
    if (!this.isCombinedMode) {
      this.scrollToResults();
    }
    this.storeService.isCalculatingCharts = true;

    const payload = this.buildChartsPayload();

    this.chartService.resetCharts();
    const calculate$ = this.chartService.calculate(payload, this.storeService.searchHash)
      .pipe(
        take(1),
        catchError((err) => of({charts: []})),
        finalize(() => {
          this.loadingResult = false;
          this.storeService.isCalculatingCharts = false;
        })
      )
      .subscribe({
        next: ({charts}) => {
          this.charts = charts;
          this.storeService.loading = false;

          if (this.storeService.docdbFamilyIdFromPatentViewer) {
            this.storeService.scrollToPatentFromPatentViewer();
          } else {
            this.scrollToResults();
            this.scrollOnBack();
          }
        },
        error: (err) => {
          console.log(err);
          this.monitorService.setErrors(['Error while getting Chart data.']);
        }
      });
    this.subscriptions.add(calculate$);
  }

  private buildChartsPayload() {
    const payload = {
      charts: this.storeService.getChartActiveNames()
    };

    payload['parameters'] = {
      portfolio_analytics_profile_indicators: [{axis: 'x', property: 'tech_fields'}, {axis: 'y', property: 'citation_backward_count'}]
    }

    const freeTextQuery = this.storeService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    return payload;
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.getResult(true);
    }
  }

  onTaskSaved(data: { message: string; payload: TaskModel; savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessages = this.taskService.getTaskCreationSuccessMessage(data, 'search_results');
    }
  }

  public navigateMonitorRun(page) {
    this.activeMonitorRunPage = page;
    this.getMonitorRuns(false);
  }

  onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.storeService.pageSize = pageSize;
    this.activePage = 1;
    this.getResult(false);
  }

  public onSort(val: SortParams) {
    this.sorting = val;
    this.storeService.elementIdToScrollPage = 'monitor-results';
    this.getResult(false);
  }

  getAlertMessage(): string {
    const hasFilters = this.storeService.getAppliedFiltersQuery()?.length > 0;
    if (hasFilters) {
      return 'No documents match your filters. Try to clear your filters.';
    }

    return 'This monitor run does not have any documents yet.';
  }

  private clearStoredData() {
    this.storeService.setFilters([], false);
    this.storeService.selectedPublications = [];
  }

  private prepareAdditionalExportParams() {
    this.additionalExportParams = {
      title: 'Monitor REPORT',
      subtitle: `For profile "${this.profile.name}", based on documents published between ${this.selectedSnapshot['name']}`,
    };
  }

  /**
   * query builder for result table
   */
  private buildQuery() {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;

    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
      query['sort_order'] = this.sorting.order;
    }

    const filters = [this.storeService.advancedFilterAppliedQuery];

    this.storeService.filters.forEach((item) => {
      if (item.chart === 'monitor_venn_diagram') {
        query['type'] = item.value.split('∩');
      } else {
        filters.push(item.query);
      }
    });

    const filterQuery = filters.filter((q) => q && q.trim().length > 0)
      .join(` ${this.storeService.filtersOperator} `);

    if (filterQuery) {
      query['filters'] = filterQuery;
    }

    return query;
  }

  private scrollToResults() {
    if (!this.storeService.elementIdToScrollPage) {
      return;
    }
    setTimeout(() => {
      if (document.getElementById(this.storeService.elementIdToScrollPage)) {
        document.getElementById(this.storeService.elementIdToScrollPage)
          .scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
        this.storeService.elementIdToScrollPage = '';
      }
    });
  }

  private scrollOnBack() {
    if (this.storeService.scrollTopPage) {
      setTimeout(() => {
        window.scrollTo({
          top: this.storeService.scrollTopPage,
          behavior: 'smooth'
        });
        this.storeService.scrollTopPage = null;
      }, 100);
    }
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.storeService.resetAdvancedFilter();
    this.storeService.patentTableSort = {field: null, order: null} as SortParams;
  }

  private setFieldsAfterBack() {
    if (this.storeService.patentTableSort) {
      this.sorting = {...this.storeService.patentTableSort};
    }
  }

  onChangeViewMode(event){
    this.storeService.patentListViewMode = event.nextId;

    if(event.nextId === ViewModeTypeEnum.COMBINED){
      this.adjustTableColumnToDisplay();
    }

    if(this.isChartVisible){
      this.storeService.singleChartColumn = this.storeService.singleChartColumn;
    }
  }
  adjustTableColumnToDisplay(){
    if(this.storeService.selectedColumnsToShow && this.storeService.selectedColumnsToShow.length > 4 ){
      let columns = this.storeService.selectedColumnsToShow;
      columns = columns.filter(o => o.property !== 'priority_date');
      columns = columns.filter(o => o.property !== 'applicants');
      if(columns.length < this.storeService.selectedColumnsToShow.length){
        this.storeService.selectedColumnsToShow = columns;
      }
    }
  }

  navigatePage(page: number) {
    this.activePage = page;
    this.storeService.elementIdToScrollPage = 'monitor-results-container';
    this.getResult(false);
  }

  createMonitorResultProfile() {
    const now =  new Date().toISOString().split('T')[0];
    const payload = {
      from_date: `${now}`,
      to_date: `${now}`,
    };

    const clearSnapshot$ = this.monitorService.clearLegalStatusSnapshot(this.profile.id).subscribe({
        next: res => {
          const newRun$ = this.monitorService.createMonitorRun(payload, {monitor_profile_id: this.profile.id}).subscribe({
            next: res=>{
              this.getMonitorRuns(false);
              const intervalId = setInterval(() => {
                this.storeService.setMonitorRunID(res['data']['monitor_run_id']);
                if (this.monitorRuns) {
                  clearInterval(intervalId);
                }
              }, 5000);
            },
            error: err=>{
              console.log(err);
              this.monitorService.setErrors(['Error while creating result set.']);
            }
          });
          this.subscriptions.add(newRun$);
        }
      })
    this.subscriptions.add(clearSnapshot$);
  }
}
