<div class="container pb-4">
  <h4 class="profile-headline mb-0 mt-2">
    Setup legal alert
    <app-tooltip id="monitor_legal_status" class="ms-3" [tooltipTitle]='tooltip.ListTitle' [tooltipText]='tooltip.ListText' tooltipPosition="right"></app-tooltip>
  </h4>
  <!-- Patent List Input box -->
  <div class="method-inputBox-wrap" data-intercom-target="Input box">
    <app-patent-list-input [(patent_numbers)]="patentNumbers" (update)="onPatentInputUpdate($event)"
                           collectionsTitle="Select a list you want to use for legal monitoring status set up"  [(patentNumbersType)]="patentNumbersType"
                           [storeService]="storeService">
    </app-patent-list-input>
  </div>

  <div class="d-flex justify-content-between align-items-end">
    <div data-intercom-target="track-options" *ngIf="profile?.legal_status_profile?.scope">
      <label class="radio-button mb-1">
        <input type="radio" name="patentMonitorScope" class="me-1" [(ngModel)]="profile.legal_status_profile.scope" [value]="patentListScopeEnum.PUBLICATION">
        <span>Focus on entered patents only</span>
      </label>
      <label class="radio-button" id="option-keyword">
        <input type="radio" name="patentMonitorScope" class="me-1" [(ngModel)]="profile.legal_status_profile.scope" [value]="patentListScopeEnum.FAMILY">
        <span>I'm interested in changes to the family globally</span>
      </label>
    </div>
    <div class="btn-wrap">
      <button type="button" ngbTooltip="Click here to reset profile"
              class="btn btn-secondary-outline btn-md ms-3 mb-1 btn-icon clear-profile-method" (click)="reset()">
        <i class="fa fa-times"></i>
      </button>
      <button type="button" [ngbTooltip]="!patentNumbers ? '' :'Click here to view input results'"
              [disabled]="!patentNumbers" class="btn btn-primary btn-md ms-3 mb-1" (click)="submit()"
              data-intercom-target="view-input-button">
        VIEW INPUT
      </button>
      <button type="button" class="btn btn-primary btn-md ms-3 mb-1" (click)="submit(false, true)"
              data-intercom-target="setup-monitoring-button"
              [disabled]="!patentNumbers" [ngbTooltip]="!patentNumbers ? '' :'Click here to setup monitoring profile'">
        FINISH SETUP
      </button>
    </div>
  </div>
</div>

<div class="container-fluid">
  <div class="table-tabs pb-4" *ngIf="patentsResults">
    <ul class="nav-tabs machine-learning nav-2" ngbNav #patentListMethod="ngbNav" data-intercom-target="queried-patents-title">
      <li [ngbNavItem]="'patentSearch'">
        <a ngbNavLink>Queried patents <span class="badge rounded-pill badge-secondary">({{patentsResults.page.total_hits}})</span></a>
        <ng-template ngbNavContent>
          <div>
            <!-- Table -->
            <div class="ms-table">
              <app-patent-table [patents]="patentsResults.documents"
                                [showQueriedPublicationNumber]="true"
                                [pagination]="patentsResults.page" [hasLinksToBooleanSearch]="true"
                                pathUrl="/patent" [linkData]="linkData"
                                [searchHash]="patentsResults.search_info.search_hash"
                                backButtonTitle="Back to monitor" [storeService]="storeService"
                                [showRank]="false"
                                [showPatentSelection]="false">
              </app-patent-table>
              <app-pagination class="d-flex justify-content-end" [pagination]="patentsResults.page" (navigatePage)="navigatePage($event)"></app-pagination>
            </div>
            <!-- ./Table -->
          </div>
        </ng-template>
      </li>
    </ul>
    <div class="tab-content">
      <div [ngbNavOutlet]="patentListMethod" ></div>
    </div>
  </div>
</div>
