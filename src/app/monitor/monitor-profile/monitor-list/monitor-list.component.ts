import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { tooltip } from '@monitor/tooltip';
import { LegalStatusPatentListScopeEnum, MonitorProfile } from '@core/models';
import {
  BaseMonitorStoreService,
  ConfirmationDialogService,
  extractPublications,
  MatomoService,
  MonitorService, PatentNumberSearchRequest, PatentNumberSearchTypeEnum,
  PatentNumberService,
  PatentNumberTypeEnum
} from '@core';
import { ActivatedRoute, Router } from '@angular/router';
import { BehaviorSubject, Subscription } from 'rxjs';

@Component({
  selector: 'app-monitor-list',
  templateUrl: './monitor-list.component.html',
  styleUrls: ['./monitor-list.component.scss']
})
export class MonitorListComponent implements OnInit, OnDestroy {
  @Input() private notify = new BehaviorSubject<string>('');
  @Input() storeService: BaseMonitorStoreService;
  @Output() changeTab: EventEmitter<string> = new EventEmitter();

  patentNumbers: string = '';
  patentNumbersType: PatentNumberTypeEnum = PatentNumberTypeEnum.MIXED;
  patentListScopeEnum = LegalStatusPatentListScopeEnum;
  patentsResults: any;

  private subscriptions = new Subscription();

  get tooltip() {
    return tooltip;
  }

  constructor(
    private router: Router,
    public route: ActivatedRoute,
    private confirmationService: ConfirmationDialogService,
    private patentNumberService: PatentNumberService,
    public monitorService: MonitorService,
    private matomoService: MatomoService
  ) { }

  ngOnInit(): void {
    this.storeService.isPublications = true;
    this.storeService.selectedColumnsToShow = [];
    if (this.profile && !this.profile.legal_status_profile) {
      this.profile.legal_status_profile = { 'patent_numbers': [], 'scope': LegalStatusPatentListScopeEnum.PUBLICATION }
    }
    this.patentNumbers = this.profile?.legal_status_profile?.patent_numbers?.join('\n');

    const notify$ = this.notify.subscribe({
      next: v => {
        this.updateSection('list');
      }
    });
    this.subscriptions.add(notify$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }


  updateSection(newSection) {
    this.router.navigateByUrl(this.monitorService.getBaseUrl() + '/' + newSection);
    this.monitorService.setSection(newSection);
  }

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  get processPatentText(): string[] {
    const { publications, remainingText } = extractPublications(this.patentNumbers);
    return publications
      ? publications
      : [];
  }

  reset() {
    this.confirmationService.confirm(
      'Legal status monitoring profile message',
      'Do you really want to clear patent list in legal stats monitoring profile?',
      'Yes')
      .then((confirmed) => {
        if (confirmed) {
          this.storeService.loading = true;
          const payload = {
            'legal_status_profile': null
          };
          this.monitorService.updateProfile(this.profile.id, payload)
            .then((response) => {
              this.patentsResults = undefined;
              this.patentNumbers = '';
              this.profile.legal_status_profile.patent_numbers = [];
              this.storeService.loading = false;
            })
            .catch((e) => {
              console.error(e);
              this.monitorService.setErrors(['Error while resetting patent list profile.']);
              this.storeService.loading = false;
            });
        }
      }).catch(() => {
    });
  }

  /**
   * save profile
   * @param search whether to run search event
   * @param setupList whether to run setup List event
   */
  public submit(search: boolean = true, setupList: boolean = false) {
    this.monitorService.setErrors([]);
    this.profile.legal_status_profile.patent_numbers = this.processPatentText;

    if (this.profile.legal_status_profile.patent_numbers.length === 0) {
      this.monitorService.setErrors(['Please enter publication number.']);
      return;
    }
    this.storeService.loading = true;
    this.patentsResults = undefined;
    const payload = {
      'legal_status_profile': {
        'patent_numbers': this.profile.legal_status_profile.patent_numbers,
        'scope': this.profile.legal_status_profile.scope
      }
    };
    this.matomoService.monitorLegalStatusFinishButton();
    this.monitorService.updateProfile(this.profile.id, payload).then(
      (response) => {
        if (setupList) {
          this.onSetupProfile(response);
        }
        if (search) {
          this.searchList(1);
        }
        if (!search && !setupList) {
          this.storeService.loading = false;
        }
      }
    ).catch((e) => {
      console.error(e);
      this.monitorService.setErrors(['Error while saving legal status patent list.']);
      this.storeService.loading = false;
    });
  }
  /**
    * Search patent list result
    * @param page Page number to navigate
    */
  private searchList(page = 1) {
    this.storeService.loading = true;
    this.patentsResults = undefined;
    const payload = {
      patent_numbers: this.profile.legal_status_profile.patent_numbers,
      skip_invalid: 1,
      patent_numbers_type: this.patentNumbersType,
      search_type: PatentNumberSearchTypeEnum.PUBLICATION

    } as PatentNumberSearchRequest;
    const getPatents$ = this.patentNumberService.getPatents(payload, { page: page })
      .subscribe({next: ({data}) => {
        this.patentsResults = data;
        this.storeService.loading = false;
      }, error: err => {
        console.log(err);
        this.monitorService.setErrors(['Error while searching legal status patent list.']);
        this.storeService.loading = false;
      }});
    this.subscriptions.add(getPatents$);
  }

  onPatentInputUpdate(event: {patent_numbers : string, patentNumbersType: string}) {
    this.patentNumbers = event.patent_numbers;
    this.patentNumbersType = event.patentNumbersType as PatentNumberTypeEnum;
  }

  public onSetupProfile(profile) {
    const message = ['Your legal status profile has been setup successfully with given patent list.'];
    if (profile['legal_status_summary'] && profile['legal_status_summary']['unsupported_patent_numbers']) {
      let unsupportedAuthorities = new Set();
      for (let pn of profile['legal_status_summary']['unsupported_patent_numbers']) {
        unsupportedAuthorities.add(pn.substring(0, 2));
      }
      const warning = `
          <i class="fas fa-exclamation-triangle"></i>
          However it seems that you also provided patents from authorities that we currently do not cover
          in terms of the legal status (${Array.from(unsupportedAuthorities).join(", ")}).
          Please refer to what we cover <a href="/data-coverage" target="_blank">here</a>
      `;
      message.push(warning);
    }
    this.confirmationService.alert(
      'Legal status Monitoring has been setup successfully',
      message,
      'Close')
      .then((confirmed) => {
        this.changeTab.emit('Monitoring-result');
        this.storeService.loading = false;
      }).catch(() => { });
  }

   /**
   *
   * @param page page number
   */
  public navigatePage(page) {
    if (page !== this.patentsResults.page.current_page) {
      this.searchList(page);
    }
  }
}
