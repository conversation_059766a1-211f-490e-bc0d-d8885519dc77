<div class="container pb-4">
  <h4 class="profile-headline">
    Deep learning&nbsp;
    <app-tooltip id="monitor_deeplearning" [tooltipTitle]='tooltip.DLTitle' [tooltipText]='tooltip.DLText'></app-tooltip>
    &nbsp;
    <label class="check-on-off method-state h-100 mb-0 align-middle" ngbTooltip="Enable/Disable Deep learning profile">
      <input type="checkbox" [checked]="profile?.machine_learning_active" class="left" (click)="checkStatus($event)"/>
    </label>
    &nbsp;
    <span class="badge rounded-pill badge-primary font-small align-middle">{{profile?.machine_learning_status}}</span>
    <span class="badge rounded-pill badge-primary font-small ms-1 align-middle">{{profile?.machine_learning_message}}</span>
  </h4>
  <!-- Input box -->
  <div class="monitoring-inputBox-wrap">
    <app-patent-list-input [(patent_numbers)]="patentNumbers" (update)="updatePatents($event)" (save)="submit()"
                           collectionsTitle="Select a list you want to use for patent monitoring set up" [(patentNumbersType)]="patentNumbersType"
                           [storeService]="storeService">
    </app-patent-list-input>

    <div class=" btn-wrap text-end mt-4">
      <button type="reset" class="btn btn-secondary-outline btn-md ms-3 mb-1 btn-icon clear-profile-method"
        [ngbTooltip]="freezeProfile? 'Please wait. Deep learning is in progress': 'Click here to reset profile'"
        [disabled]="freezeProfile" (click)="reset()"><i class="fa fa-times"></i></button>
      <button type="button" (click)="submit()" class="btn btn-primary btn-md ms-3 mb-1" data-intercom-target="view-input-button"
        [ngbTooltip]="freezeProfile? 'Please wait. Deep learning is in progress': isInputEmpty ? '':'Click here to save and preview profile results'"
        [disabled]="isInputEmpty">VIEW INPUT</button>
      <button type="button" class="btn btn-primary btn-md ms-3 mb-1" [disabled]="totalSelection()===0 || isInputEmpty"
        [ngbTooltip]="freezeProfile? 'Please wait. Deep learning is in progress': isInputEmpty ? '':'Click here to setup monitoring profile'"
        (click)="onSetupProfile()" data-intercom-target="setup-monitoring-button">SETUP MONITORING</button>
    </div>
  </div>
  <!-- ./Input box -->
</div>
<div class="container-fluid">
  <!-- Table tabs wrap -->
  <div class="table-tabs  pb-4" *ngIf="patentsResults?.documents?.length">
    <ul class="nav-tabs machine-learning nav-2" ngbNav #mlMethod="ngbNav" data-intercom-target="queried-patents-title">
      <li [ngbNavItem]="'PublicationNumbers'">
        <a ngbNavLink>Queried patents <span class="badge rounded-pill badge-secondary">({{profile.machine_learning_profile.selected_number_publications.length}})</span></a>
        <ng-template ngbNavContent>
          <div class="ms-table">
            <app-patent-table [patents]="patentsResults.documents" [secondaryIsPublicationSource]="false"
                              [secondaryPatentSelection]="profile.machine_learning_profile.selected_number_publications"
                              [showQueriedPublicationNumber]="true"
                              [pagination]="patentsResults.page" [hasLinksToBooleanSearch]="true"
                              pathUrl="/patent" [linkData]="linkData"
                              [hideColumns]="['monitor_feedback','monitor_label']"
                              [searchHash]="patentsResults.search_info.search_hash"
                              [allowFilteringByLegalStatus]="false"
                              [showRank]="false"
                              backButtonTitle="Back to monitor"
                              [storeService]="storeService">
            </app-patent-table>
            <app-pagination class="d-flex justify-content-end" [pagination]="patentsResults.page" (navigatePage)="navigatePage($event,'patent')"></app-pagination>
          </div>
        </ng-template>
      </li>
    </ul>
    <div class="tab-content">
      <div [ngbNavOutlet]="mlMethod" ></div>
    </div>
  </div>
</div>
