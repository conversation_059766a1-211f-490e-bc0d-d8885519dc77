import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { tooltip } from '@monitor/tooltip';
import { columnsToShow, publicationColumnsToShow } from '../monitor-results/columns';
import { BaseMonitorStoreService } from '@core/store';
import {
  ConfirmationDialogService,
  extractPublications,
  MatomoService,
  MonitorService, PatentNumberSearchRequest,
  PatentNumberService,
  PatentNumberTypeEnum,
  PatentQueryParams,
  PatentTableService
} from '@core/services';
import { MonitorProfileMachineLearningStatusEnum, MonitorProfile } from '@core/models';

@Component({
  selector: 'app-monitor-machine-learning',
  templateUrl: './monitor-machine-learning.component.html',
  styleUrls: ['./monitor-machine-learning.component.scss']
})
export class MonitorMachineLearningComponent implements OnInit, OnD<PERSON>roy {
  @Input() storeService: BaseMonitorStoreService;
  @Output() resultClick = new EventEmitter();
  @Output() changeMethod: EventEmitter<string> = new EventEmitter();

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  patentsResults: any;
  patentNumbers = '';
  patentNumbersType: PatentNumberTypeEnum = PatentNumberTypeEnum.MIXED;

  private callCount = 0;
  private refreshIntervalID;
  private isUpdatingModel = false;
  private subscriptions = new Subscription();

  get tooltip() {
    return tooltip;
  }

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    private confirmationService: ConfirmationDialogService,
    private patentNumberService: PatentNumberService,
    public monitorService: MonitorService,
    public patentTableService: PatentTableService,
    private matomoService: MatomoService
  ) {
  }

  public get linkData() {
    return this.monitorService.linkData;
  }

  /**
   * get reference for ML profile freeze state
   */
  get freezeProfile() {
    return this.storeService.freezeML;
  }

  /**
   * set reference for ML profile freeze state
   */
  set freezeProfile(state) {
    this.storeService.setFreezeML(state);
  }

  ngOnInit() {
    this.storeService.selectedColumnsToShow = this.storeService.isPublications ? publicationColumnsToShow : columnsToShow;
    this.patentNumbers = this.profile?.machine_learning_profile?.patent_numbers ?
    this.profile.machine_learning_profile.patent_numbers.join(', ') : '';

    this.freezeProfile = this.isTraining(this.profile);
    const self = this;
    setTimeout(function () {
      if (self.freezeProfile) {
        self.refreshML();
      }
    });

    const setupMonitoringEvent$ = this.storeService.setupMonitoringEvent.subscribe({
      next: value => {
        this.isUpdatingModel = value;
        this.confirmation();
      }
    });
    this.subscriptions.add(setupMonitoringEvent$);
  }

  public processPatentText() {
    let term = '', publication = [];
    if (this.patentNumbers) {
      const {publications, remainingText} = extractPublications(this.patentNumbers);
      publication = publication.concat(publications);
      term += ' ';
      term += remainingText;
    }
    this.profile.machine_learning_profile.patent_numbers = publication;
    this.profile.machine_learning_profile.search_input = '';
  }

  public submit(search = true, setupDL: boolean = false) {
    if (this.storeService.freezeML) {
      this.confirmationService.alert(
        'Deep learning profile message',
        'Deep learning profile is frozen because last training request is in progress. \nPlease wait!');
      return;
    }
    this.storeService.loading = true;
    this.processPatentText();
    this.patentsResults = undefined;

    const payload = {
      'machine_learning_active': this.profile.machine_learning_active,
      'machine_learning_profile': this.profile.machine_learning_profile
    };
    this.monitorService.updateProfile(this.profile.id, payload).then(
      (response) => {
        if (setupDL) {
          this.setup();
        }
        if (search) {
          this.searchPatents(true, 1);
        }
        if (this.callCount === 0) {
          this.storeService.loading = false;
        }
      }
    ).catch(e => {
      console.error(e);
      this.monitorService.setError(e, 'Error while saving deep learning profile.');
      this.storeService.loading = false;
    });
  }

  /**
   * search patent numbers
   * @param save whether to save the patent number is patent number selection list
   * @param page Page number to navigate
   * @param setupDL whether to run setup DL event
   */
  private searchPatents(save: boolean = true, page: number = 1) {
    if (this.profile.machine_learning_profile.patent_numbers.length > 0) {
      this.storeService.loading = true;
      this.callCount++;
      const payload = {patent_numbers: this.profile.machine_learning_profile.patent_numbers, skip_invalid: 1, patent_numbers_type: this.patentNumbersType} as PatentNumberSearchRequest;
      const params = {page} as PatentQueryParams;
      const getPatents$ = this.patentNumberService.getPatents(payload, params).subscribe({next: ({data}) => {
        if (save) {
          const param = {
            search_hash: data['search_info']['search_hash'],
            source: 'PATENT_NUMBER'
          };
          this.monitorService.mlSelectPatents(this.profile.id, param).subscribe({
            next: (response) => {
              this.profile.machine_learning_profile.selected_number_publications =
                response['data']['machine_learning_profile']['selected_number_publications'];
              this.callCount--;
              this.patentsResults = data;
              if (this.callCount === 0) {
                this.storeService.loading = false;
              }
            },
            error: err => {
              console.error(err);
              this.monitorService.setErrors(['Error while searching publication number.']);
              this.storeService.loading = false;
            }
          });
          const machineLearningProfile = {
            ...this.profile.machine_learning_profile,
            patent_numbers: data['documents']?.length > 0 ? data['documents'].map((d) => d.general.original_number) : []
          };

          const payload = {
            'machine_learning_active': this.profile.machine_learning_active,
            'machine_learning_profile': machineLearningProfile
          };
          this.monitorService.updateProfile(this.profile.id, payload).then();
        } else {
          this.callCount--;
          this.patentsResults = data;
          if (this.callCount === 0) {
            this.storeService.loading = false;
          }
        }

        if ((this.callCount === 0 || save) && !data['documents']?.length) {
          const isPlural = this.profile.machine_learning_profile.patent_numbers.length > 1;
          const plural = isPlural ? 's' : '';
          const subject = isPlural ? 'their' : 'its';
          const errorMessage = `The patent number${plural} you provided seem${isPlural ? '' : 's'} to be incorrect.
          Please verify ${subject} accuracy and provide the correct one${plural} to view input and receive result${plural}.`;
          this.monitorService.setErrors([errorMessage]);
        }
      }, error: err => {
        console.error(err);
        this.monitorService.setErrors(['Error while searching publication number.']);
        this.storeService.loading = false;
      }});
    this.subscriptions.add(getPatents$);
    } else {
      this.monitorService.setErrors(['Enter patent numbers instead of plain text to view input and receive results.']);
    }
  }

  /**
   * Reset the deep learning profile
   */
  public reset() {
    if (this.storeService.freezeML) {

      this.confirmationService.alert(
        'Deep learning profile message',
        'Deep learning profile is frozen because last training request is still in progress. Please wait!');
      return;
    }
    this.confirmationService.confirm(
      'Deep learning profile message',
      'Do you really want to clear Deep learning profile?',
      'Yes')
      .then((confirmed) => {
        if (confirmed) {
          this.storeService.loading = true;
          const payload = {
            'machine_learning_active': false,
            'machine_learning_profile': {
              'patent_numbers': [],
              'search_input': '',
              'selected_text_publications': [],
              'selected_number_publications': []
            },
            'machine_learning_status': MonitorProfileMachineLearningStatusEnum.PENDING,
            'machine_learning_message': 'New profile',
            'ipc_codes': null
          };
          this.monitorService.updateProfile(this.profile.id, payload)
            .then((response) => {
              this.patentsResults = undefined;
              this.profile.machine_learning_profile = response['machine_learning_profile'];
              this.profile.machine_learning_active = response['machine_learning_active'];
              this.profile.machine_learning_status = response['machine_learning_status'];
              this.profile.machine_learning_message = response['machine_learning_message'];
              this.patentNumbers = '';
              this.storeService.loading = false;
            })
            .catch((e) => {
              console.error(e);
              this.monitorService.setErrors(['Error while resetting deep learning profile.']);
              this.storeService.loading = false;
            });
        }
      }).catch(() => {
    });
  }

  /**
   * isInputEmpty
   *
   * check if input form is empty
   */
  get isInputEmpty() {
    if (!this.patentNumbers.trim()) {
      return true;
    }
    return false;
  }

  /**
   * getter for checking if profile input container is empty
   */
  get isMLEmpty(): boolean {
    if (
      (!this.profile.machine_learning_profile?.search_input && !this.profile.machine_learning_profile?.patent_numbers) || (this.profile.machine_learning_profile.search_input.trim() === '' &&
      this.profile.machine_learning_profile.patent_numbers.length === 0)
    ) {
      return true;
    }
    return false;
  }

  /**
   * getter for checking if Machine learning profile have some publication selected
   */
  get isMLSelectionEmpty(): boolean {
    if (
      (!this.profile.machine_learning_profile?.selected_text_publications &&
       !this.profile.machine_learning_profile?.selected_number_publications)
      || (this.profile.machine_learning_profile.selected_text_publications.length === 0 &&
      this.profile.machine_learning_profile.selected_number_publications.length === 0)
    ) {
      return true;
    }
    return false;
  }

  /**
   * event listener for Profile status checkbox
   * @param event Click event on Checkbox
   */
  public checkStatus(event) {
    if (event !== undefined) {
      if (event.target.checked && (this.isMLEmpty || this.isMLSelectionEmpty)) {
        this.profile.machine_learning_active = false;
        this.monitorService.setErrors([this.isMLEmpty
          ? 'Please enter Deep learning profile.'
          : 'Please select some Publication and save the selection.']);
        event.preventDefault();
      } else {
        this.storeService.loading = true;
        this.monitorService.updateProfile(this.profile.id, {'machine_learning_active': event.target.checked})
          .then((response) => {
            this.profile.machine_learning_active = response['machine_learning_active'];
            this.storeService.loading = false;
          })
          .catch((e) => {
            console.error(e);
            this.monitorService.setErrors(['Error while updating deep learning state.']);
            this.storeService.loading = false;
          });
      }
    }
  }

  /**
   * get total number of Publication is selection list
   * @return total publication in list
   */
  public totalSelection(): number {
    return this.profile?.machine_learning_profile?.selected_text_publications?.length +
      this.profile?.machine_learning_profile?.selected_number_publications?.length;
  }

  /**
   *
   * @param page page number
   * @param type type of navigation call
   */
  public navigatePage(page, type: string) {
    switch (type) {
      case 'patent':
        this.searchPatents(false, page);
        break;
    }
  }

  /**
   * Check if given profile is in training mode
   * @param profile profile Object
   * @return Boolean state of profile
   */
  private isTraining(profile): boolean {
    return profile?.machine_learning_status === MonitorProfileMachineLearningStatusEnum.CREATING ||
      profile?.machine_learning_status === MonitorProfileMachineLearningStatusEnum.TRAINING ||
      (profile?.machine_learning_status === MonitorProfileMachineLearningStatusEnum.PENDING && profile?.machine_learning_message === 'In Queue');
  }

  /**
   * refresh deep learning profile after 3 second
   */
  private refreshML() {
    const self = this;
    this.refreshIntervalID = setTimeout(function () {
      const getProfile$ = self.monitorService.getProfile(self.profile.id)
        .subscribe({
          next: (profile) => {
            if (self.isTraining(profile)) {
              self.refreshML();
            } else {
              self.monitorService.loadMonitorRuns(self.storeService).then();
              self.freezeProfile = false;
            }
            self.profile.machine_learning_status = profile.machine_learning_status;
            self.profile.machine_learning_message = profile.machine_learning_message;
          }, error: err => {
            console.log(err);
          }});
      self.subscriptions.add(getProfile$);
    }, 3000);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    if (this.refreshIntervalID) {
      clearInterval(this.refreshIntervalID);
    }
  }

  /**
   * Event listener for Result btn
   */
  public onResultsBtn() {
    this.resultClick.emit();
  }

  /**
   * onSetupProfile
   *
   * Event listener for setup monitoring button
   */
  public onSetupProfile() {
    this.profile.machine_learning_active = true;
    this.submit(false, true);
    this.matomoService.monitorTechnologyDlSetupButton();
  }

  /**
   * setup
   */
  public setup() {
    if (this.isInputEmpty) {
      this.confirmationService.alert(
        'Deep learning profile message',
        'Please fill out the deep learning profile!');
      return;
    }
    if (this.totalSelection() === 0) {
      this.confirmationService.alert(
        'Deep learning profile message',
        'Please select some publications for training.');
      return;
    }
    if (this.storeService.freezeML) {
      this.confirmationService.alert(
        'Deep learning profile message',
        'Deep learning profile is frozen because last training request is in progress. \n\nPlease wait!');
      return;
    }
    if (this.isUpdatingModel) {
      this.setupDL();
    } else {
      const msg = 'Do you want to continue with ' + this.totalSelection() + ' selected documents for training?';
      const btnYes = msg.substring(0, 14) === 'Do you want to' ? 'Yes' : 'Select more documents';
      const btnNoText = msg.substring(0, 14) === 'Do you want to' ? 'No' : 'Setup deep learning monitoring';
      this.confirmationService.confirm(
        'Deep learning profile message',
        msg,
        btnYes,
        btnNoText)
        .then((confirmed) => {
          if ((confirmed && btnYes === 'Yes') || (!confirmed && btnYes !== 'Yes')) {
            this.setupDL();
          }
        }).catch(() => {
      });
    }
  }

  private setupDL() {
    this.isUpdatingModel = false;
    if (this.profile.machine_learning_active) {
      this.confirmation();
    } else {
      this.storeService.loading = true;
      this.monitorService.updateProfile(this.profile.id, {machine_learning_active: true})
        .then((response) => {
          this.profile.machine_learning_active = response['machine_learning_active'];
          this.confirmation();
        })
        .catch((e) => {
          console.error(e);
          this.monitorService.setErrors(['Error activating deep learning profile.']);
          this.storeService.loading = false;
        });
    }
  }

  private confirmation() {
    const self = this;
    this.monitorService.requestTraining(this.profile.id,
      this.profile.machine_learning_profile, this.isUpdatingModel).subscribe({
        next: (res) => {
          this.freezeProfile = true;
          this.refreshML();
          this.storeService.loading = false;
          if (!this.isUpdatingModel) {
            this.confirmationService.confirm(
              'Deep Learning monitoring method',
              [
                'We are creating a cutting edge deep neural network which is highly computationally intensive.' +
                ' You will receive an email as soon as we have the first set of most recent results ready for you.',
                'In the meantime, you can click on Results button to analyze previous results. OR click on Other ' +
                'button to setup another monitoring method.',
                'Note: Another monitoring method means a different way to look at the same profile.'
              ],
              'Other',
              'Results')
              .then((confirmed) => {
                if (confirmed) {
                  this.changeMethod.emit(this.storeService.isPublicationProfile(this.profile)? 'boolean' :'semantic');
                } else {
                  this.onResultsBtn();
                }
              }).catch(() => {
            });
          } else {

            this.confirmationService.alert('Deep learning profile message',
              'We will update your model with the latest feedback. This can take some time as we train a highly sophisticated neural ' +
              'network for you. You will have your latest results as soon as we have them ready.'
            );
          }
        },
        error: e => {
          if (e.error && e.error.status === 409) {
            self.freezeProfile = true;
            self.confirmationService.alert('Deep learning profile message',
              'Deep learning profile is still being updated. \n\nPlease wait until it finishes.'
            );
          } else {
            console.error(e);
            self.monitorService.setErrors([e.error.message + '.']);
            self.storeService.loading = false;
            self.confirmationService.alert('Deep learning profile message',
              'Could not update deep learning profile. The error is: ' + e.error.message
            );
          }
        }
      });
  }
  updatePatents(event: {patent_numbers : string, patentNumbersType: string}) {
    this.patentNumbers = event.patent_numbers;
    this.patentNumbersType = event.patentNumbersType as PatentNumberTypeEnum;
  }
}
