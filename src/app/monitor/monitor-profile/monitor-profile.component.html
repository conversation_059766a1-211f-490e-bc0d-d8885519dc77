<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="flex-fill">
    <app-spinner [hidden]="!loading"></app-spinner>
    <div [hidden]="loading">
      <div class="bg-gray">
        <div class="container">
          <!-- Nav tabs -->
          <ul ngbNav #mainTabs="ngbNav" [(activeId)]="mainTab" class="nav-tabs main-tabs monitor-profile-tabs nav-1 mb-0 d-flex justify-content-center" [destroyOnHide]="false"
            (navChange)="onChangeTab($event)">
            <li [ngbNavItem]="'profile-setting'">
              <a ngbNavLink>1. General setup</a>
              <ng-template ngbNavContent>
                <app-monitor-setting *ngIf="profile || newProfile" id="profile-setting"
                                     [storeService]="storeService"
                                     (changeTab)="changeTab($event)"></app-monitor-setting>
              </ng-template>
            </li>
            <li [ngbNavItem]="'Monitoring-profile'" [ngClass]="{'disable-tab': newProfile }">
              <a ngbNavLink>2. {{isLegalStatusProfile? 'Patent entry':'Monitoring method'}}</a>
              <ng-template ngbNavContent>
                <app-monitoring-profile *ngIf="profile && !isLegalStatusProfile"
                                        [storeService]="storeService"
                                        [notify]="notify" id="Monitoring-profile"
                  (changeTab)="changeTab($event)"></app-monitoring-profile>
                <app-monitor-list *ngIf="profile && isLegalStatusProfile"
                                  [storeService]="storeService"
                                  [notify]="notify" id="Monitoring-profile"
                (changeTab)="changeTab($event)"></app-monitor-list>
              </ng-template>
            </li>
            <li [ngbNavItem]="'Monitoring-result'" [ngClass]="{'disable-tab': disableResultTab}" >
              <a ngbNavLink>3. {{isLegalStatusProfile? 'Status change reports':'Analyze result'}}</a>
              <ng-template ngbNavContent>
                <app-monitor-results *ngIf="haveMonitorResults && !isLegalStatusProfile"
                                     [storeService]="storeService"
                                     id="Monitoring-result"></app-monitor-results>
                <app-monitor-legal-status-result *ngIf="haveLegalStatusMonitorResults"
                                                 [storeService]="storeService"></app-monitor-legal-status-result>
              </ng-template>
            </li>
          </ul>
          <!-- /Nav tabs -->
        </div>
      </div>
      <div class="page-content mt-3">
        <!-- Profile folders tabs -->
        <div class="prof-folders-wrap monitoringTabs" >
          <div class="container">

            <app-alert type="danger" [message]="errors" *ngIf="errors.length > 0"></app-alert>
          </div>
          <!-- Tab panes -->
          <div [ngbNavOutlet]="mainTabs"></div>
          <!-- /Tab panes -->
        </div>
        <!-- ./Profile folders tabs -->
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<app-contact-us-banner></app-contact-us-banner>
