import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MonitorProfileComponent } from './monitor-profile.component';
import { SharedModule } from '@shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { MonitorBooleanComponent } from './monitor-boolean/monitor-boolean.component';
import { MonitorMachineLearningComponent } from './monitor-machine-learning/monitor-machine-learning.component';
import { MonitorSettingComponent } from './monitor-setting/monitor-setting.component';
import { MonitorSemanticComponent } from './monitor-semantic/monitor-semantic.component';
import { MonitoringProfileComponent } from './monitoring-profile/monitoring-profile.component';
import { MonitorResultsComponent } from './monitor-results/monitor-results.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BasicChartsModule, ClassificationChartsModule, MonitorChartsModule } from '@shared/charts';
import { MonitorListComponent, MonitorLegalStatusResultComponent } from './';
import { MonitorStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorProfileComponent', () => {
  let component: MonitorProfileComponent;
  let fixture: ComponentFixture<MonitorProfileComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        MonitorProfileComponent,
        MonitorMachineLearningComponent,
        MonitorSemanticComponent,
        MonitorBooleanComponent,
        MonitorSettingComponent,
        MonitoringProfileComponent,
        MonitorResultsComponent,
        MonitorListComponent,
        MonitorLegalStatusResultComponent,
      ],
      imports: [
        SharedModule,
        HighchartsChartModule,
        FormsModule,
        NgbModule,
        NgxSliderModule,
        ReactiveFormsModule,
        BasicChartsModule,
        ClassificationChartsModule,
        MonitorChartsModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([{path: 'monitor', component: MonitorProfileComponent}])
      ],
      providers: [
        MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorProfileComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
