import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BooleanSearchService, MonitorService, NotificationsService, ToastService } from '@core/services';
import { MLProfile, MonitorProfile, SearchFilter } from '@core/models';
import { finalize, map, Observable, of, Subject, Subscription, switchMap } from 'rxjs';
import { BaseMonitorStoreService, MonitorLegalStoreService, MonitorStoreService } from '@core/store';


@Component({
  selector: 'app-monitor-profile',
  templateUrl: './monitor-profile.component.html',
  styleUrls: ['./monitor-profile.component.scss']
})
export class MonitorProfileComponent implements OnInit, OnDestroy {
  mainTab = 'profile-setting';
  errors: Array<string> = [];
  notify: Subject<string> = new Subject();
  newProfile: boolean = false;
  storeService: BaseMonitorStoreService;

  private sectionArr = ['setting', 'result', 'boolean', 'semantic', 'deeplearning', 'citation', 'list'];
  /**
   * Error subscription
   */
  private queryParams: any;
  private subscriptions = new Subscription();

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    private monitorStoreService: MonitorStoreService,
    private toastService: ToastService,
    private monitorLegalStoreService: MonitorLegalStoreService,
    private notificationsService: NotificationsService,
    public monitorService: MonitorService,
    private booleanSearchService: BooleanSearchService
  ) {
  }

  // section: string = 'setting';
  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  get isLegalStatusProfile(): boolean {
    return this.monitorService.isLegalStatusProfile(this.profile, this.route.snapshot.data?.isLegalStatus);
  }

  get pageTitle(): string {
    return 'Monitor';
  }

  get section(): string {
    return this.monitorService.getSection();
  }

  set section(section: string) {
    this.monitorService.setSection(section);
  }

  get loading(): boolean {
    return this.storeService.loading;
  }

  set loading(state: boolean) {
    this.storeService.loading = state;
  }

  get haveMonitorResults(): boolean {
    return this.storeService.totalMonitorRuns !== 0;
  }

  get disableResultTab() {
    if (this.profile) {
      if (this.isLegalStatusProfile) {
        return !(this.profile?.legal_status_profile?.patent_numbers?.length > 0);
      } else {

        return !this.haveMonitorResults;
      }
    }
    return true;
  }

  get haveLegalStatusMonitorResults() {
    return this.profile && this.isLegalStatusProfile && !!this.profile?.legal_status_profile;
  }

  get isResultActive() {
    if (this.profile) {
      if (this.profile.boolean_query_active ||
        this.profile.semantic_query_active ||
        this.profile.machine_learning_active) {
        return true;
      }
    }
    return false;
  }

  get runIdQueryParam(): number {
    const val = this.route.snapshot.queryParams.runID;
    return val ? Number(val) : null;
  }

  get profileIdParam(): number {
    const val = this.route.snapshot.params.id;
    const valNumber = Number(val);
    return valNumber && Number.isInteger(valNumber) ? valNumber : null;
  }

  ngOnInit() {
    this.storeService = this.route.snapshot.data?.isLegalStatus ? this.monitorLegalStoreService : this.monitorStoreService;

    const errors$ = this.monitorService.errors.subscribe({
      next: x => { this.errors = x; },
      error: err => { console.log('monitor error Observer got a error' + err); },
      complete: () => { console.log('monitor error Observer got a complete notification'); }
    });
    this.subscriptions.add(errors$);
    const queryParamSubscription$ = this.route.queryParamMap?.subscribe({
      next: params => {
        if (this.section && this.section !== 'result' && params.get('view-mode')) {
          this.queryParams = params['params'];
          const urlTree = this.router.createUrlTree([this.monitorService.getBaseUrl(), this.section], {
            queryParamsHandling: 'preserve',
            preserveFragment: true
          });
          this.router.navigateByUrl(urlTree);
        }
      }
    });
    this.subscriptions.add(queryParamSubscription$);

    if (this.route.snapshot.params.id === 'new') {
      this.section = 'setting';
      this.newProfile = true;
      this.resetStoreService();
    } else if (this.profileIdParam > 0) {
      if (!this.storeService.backPatentSearch) {
        this.resetStoreService();
        window.scrollTo({top: 0, behavior: 'smooth'});
      }
      this.mainTab = 'Monitoring-profile';
      if (this.route.snapshot.params.sectionName) {
        if (this.sectionArr.indexOf(this.route.snapshot.params.sectionName) === -1) {
          this.goToMonitor();
          return;
        }
        this.section = this.route.snapshot.params.sectionName;
        switch (this.section) {
          case 'result':
            this.mainTab = 'Monitoring-result';
            break;
          case 'setting':
            this.mainTab = 'profile-setting';
            break;
        }
      }
      if (this.runIdQueryParam > 0) {
        this.storeService.setSelectedMonitorRun(this.runIdQueryParam);
      }
      this.loadProfile(this.profileIdParam);
      const markAsReadForResource$ = this.notificationsService.markAsReadForResource(this.profileIdParam, 'MONITOR_PROFILE').subscribe();
      this.subscriptions.add(markAsReadForResource$);
    } else {
      this.goToMonitor();
    }
  }

  goToMonitor() {
    this.router.navigate(['/monitor']);
  }

  updateSection(newSection: string) {
    if (this.section === 'result' && newSection !== 'result') {
      this.queryParams = this.router.parseUrl(this.router.url).queryParams;
    }
    if (newSection === 'profile') {
      this.notify.next('profile');
    } else {
      const newUrl = this.monitorService.getBaseUrl()  + '/' + newSection;
      if (newSection === 'result') {
        this.router.navigate([newUrl], {queryParams: this.queryParams, replaceUrl: true});
      } else {
        this.router.navigate([newUrl]);
      }
      this.section = newSection;
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.monitorService.purgeErrors();
    this.storeService.resetFreezeML();
    this.storeService.selectedColumnsToShow = [];
    this.toastService.clear();
  }

  /**
   * onChange($event)
   */
  onChangeTab(event) {
    this.changeMainTab(event.nextId);
  }

  changeTab(tabID: string) {
    this.mainTab = tabID;
    this.changeMainTab(tabID);
  }

  changeMainTab(tab: string) {
    switch (tab) {
      case 'profile-setting':
        this.updateSection('setting');
        break;
      case 'Monitoring-profile':
        this.updateSection('profile');
        break;
      case 'Monitoring-result':
        this.updateSection('result');
        break;
    }
  }

  private loadProfile(profileId: number) {
    this.loading = true;
    const getProfile$ = this.monitorService.getProfile(profileId)
      .subscribe({
        next: (profile) => {
          this.startNullFields(profile);
          this.storeService.monitorProfile = profile;
          this.storeService.isPublications = profile?.scope!=='Families';
          if (this.runIdQueryParam > 0) {
            const findMonitorRunsPage$ = this.monitorService.findMonitorRunsPage(profileId, this.runIdQueryParam, 1)
              .pipe(finalize(() => this.loading = false))
              .subscribe({
                next: (val) => this.loadMonitorRuns(profile, val)
              });
            this.subscriptions.add(findMonitorRunsPage$);
          } else {
            this.loading = false;
            const pageNumber = this.storeService.monitorRuns && this.storeService.monitorRuns.page.current_page ?
              this.storeService.monitorRuns.page.current_page : 1;
            this.loadMonitorRuns(profile, pageNumber);
          }
        }, error: err => {
          this.loading = false;
          this.goToMonitor();
        }
      });
    this.subscriptions.add(getProfile$);
  }

  private startNullFields(profile) {
    if (profile?.legal_status_active) {
      return;
    }
    if (!profile.machine_learning_profile) {
      profile.machine_learning_profile = {};
    }
    if (!profile?.semantic_query) {
      profile.semantic_query = {}
    }
  }

  private loadMonitorRuns(profile: MonitorProfile, pageNumber) {
    const queryParam = {monitor_profile_id: profile['id'], page: pageNumber, page_size: this.monitorService.MONITOR_RUNS_PAGE_SIZE};
    const getMonitorRuns$ = this.monitorService.getMonitorRuns(queryParam)
      .subscribe({
        next: (response) => {
          this.storeService.setMonitorRuns(response.data);
          if (response.data.page.total_hits > 0) {
            if (this.storeService.selectedMonitorRun === null) {
              this.storeService.setSelectedMonitorRun(response.data.monitor_runs[0].id);
            }
            const selectedSnapshot = this.storeService.monitorRuns['monitor_runs'].find(r => r.id === this.storeService.selectedMonitorRun);
            if (selectedSnapshot) {
              this.storeService.additionalExportParams = {
                title: 'Monitor REPORT',
                subtitle: `For profile "${this.profile.name}", based on documents published between ${selectedSnapshot['name']}`
              }
            }
            if (!this.route.snapshot.params.sectionName) {
              this.mainTab = !this.monitorService.getSection() || this.monitorService.getSection() === 'result' ?
                'Monitoring-result' : 'Monitoring-profile';
              this.section = this.monitorService.getSection() ?? 'result';
            }
          }
          this.loading = false;
          if (!this.route.snapshot.params.sectionName) {
            const section = this.section ?? (this.isLegalStatusProfile ? 'list' : 'setting');
            const urlTree = this.router.createUrlTree([this.monitorService.getBaseUrl(), section], {
              queryParamsHandling: 'preserve',
              preserveFragment: true
            });
            this.router.navigateByUrl(urlTree, { skipLocationChange: true });
          }
        }, error: (err) => {
          console.log(err);
          this.monitorService.setErrors(['Error while getting result set list.']);
          this.loading = false;
        }
      });
    this.subscriptions.add(getMonitorRuns$);
  }

  private resetStoreService() {
    this.storeService.resetMonitorRuns();
    this.storeService.purgeResultSetPagination();
    if(this.storeService.getCloneProfile()){
      this.storeService.monitorProfile = this.storeService.getCloneProfile();
    } else {
      this.storeService.purgeMonitorProfile();
    }
    this.storeService.purgeMonitorRunID();
    this.storeService.resetSelectedMonitorRun();
    this.storeService.chartSelectedValues = {};
    this.storeService.chartQuantity = 5;
    this.booleanSearchService.filterClauses = [];
  }
}
