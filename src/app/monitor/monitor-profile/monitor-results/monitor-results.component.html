<div class="container" data-intercom-target="monitor-results-container">
  <div class="row custom-result-section mb-5" *ngIf="user?.is_admin && userService.canCreateMonitorProfile()">
    <div class="col-md-12">
      <div class="row align-items-center">
      <div class="col-auto">
        <label >Create result profile:</label>
      </div>
      <div class="col-auto">
        <input type="text" class="form-control name ms-3 filter-option" ngbDatepicker #daterange="ngbDatepicker" [value]="selectedRange"
          (click)="showDatepicker = true" readonly />
        <ngb-datepicker #daterange *ngIf="showDatepicker" style="position: absolute; z-index: 2;background: white;"
          (dateSelect)="onDateSelection($event)" [displayMonths]="2" [dayTemplate]="t" [showWeekNumbers]="true"
          [maxDate]="maxDate" [markDisabled]="isDisabled"></ngb-datepicker>
        <ng-template #t let-date let-focused="focused">
          <span class="custom-day" [class.focused]="focused" [class.range]="isRange(date)" [class.disabled]="isDisable(date)"
            [class.faded]="isHovered(date) || isInside(date)" (mouseenter)="hoveredDate = date"
            (mouseleave)="hoveredDate = null">
            {{ date.day }}
          </span>
        </ng-template>
      </div>
      <div class="col-auto">
      <button type="button" class="btn btn-primary btn-md ms-3" (click)="createMonitorResultProfile()"
      ngbTooltip="Create result set">Create</button>
        </div>
      </div>
    </div>
  </div>
  <div class="row profile-state text-uppercase pb-4" data-intercom-target="profile-status">
    <div class="col-md-6">
      <label class="open-sans-bold me-2">DEEP LEARNING</label>
      <span class="badge rounded-pill badge-primary me-1">{{profile?.machine_learning_status}}</span>
      <span class="badge rounded-pill badge-primary">{{profile?.machine_learning_message}}</span>
    </div>
    <div class="col-md-6" *ngIf="!storeService.isPublicationProfile(profile)">
      <label class="open-sans-bold me-2">SEMANTIC</label>
      <span class="badge rounded-pill " [ngClass]="{'badge-primary':profile?.semantic_query_active,'badge-secondary':!profile?.semantic_query_active }">{{profile?.semantic_query_active? 'PROFILE SETUP SUCCESS' : 'PROFILE SETUP INCOMPLETE'}}</span>
    </div>
    <div class="col-md-6">
      <label class="open-sans-bold  me-2 ">BOOLEAN</label>
      <span class="badge rounded-pill " [ngClass]="{'badge-primary':profile?.boolean_query_active,'badge-secondary':!profile?.boolean_query_active }">{{profile?.boolean_query_active? 'PROFILE SETUP SUCCESS' : 'PROFILE SETUP INCOMPLETE'}}</span>
    </div>
  </div>
  <div class="monitor-run--section" *ngIf="monitorRuns">
    <table class="table table-hover monitor-run-table" [hidden]="monitorRuns?.monitor_runs?.length===0">
      <thead >
        <tr>
          <th scope="col" class="text-center">#</th>
          <th scope="col">Reported period</th>
          <th scope="col" data-intercom-target="monitoring-method-field">Monitoring method</th>
          <th scope="col">Number of results</th>
          <th scope="col">Notification sent</th>
          <th scope="col"><i class="fas fa-tools"></i></th>
        </tr>
      </thead>
      <tbody>
        <tr [class]="'monitor-run '+singleRun.status"
            *ngFor="let singleRun of monitorRuns?.monitor_runs;index as indexI"
            [ngClass]="{'table-active open-sans-bold':singleRun.id==selectedMonitorRun}">
          <td scope="row"  class="text-center open-sans-bold p-y-spacing-big" (click)="getMonitorResultProfile(singleRun)" >
            {{(monitorRuns.page.page_size*(monitorRuns.page.current_page-1))+indexI+1}}
          </td>
          <td class=" p-y-spacing-big" (click)="getMonitorResultProfile(singleRun)" data-intercom-target="Period">{{singleRun.name | dateFormat}}</td>
          <td class=" p-y-spacing-big" (click)="getMonitorResultProfile(singleRun)">
            <div class="short-char" *ngIf="singleRun.status==='Finished' || singleRun.status==='Error'">
              <span ngbTooltip="Deep learning search" class="badge me-1"
                    [ngClass]="singleRun.ml_status!=='Finished' ? 'badge-light' : 'badge-primary'">DL</span>
              <span ngbTooltip="Semantic search" class="badge me-1"  *ngIf="!storeService.isPublicationProfile(profile)"
                    [ngClass]="singleRun.semantic_status!=='Finished' ? 'badge-light' : 'badge-primary'">S</span>
              <span ngbTooltip="Boolean search" class="badge me-1"
                    [ngClass]="singleRun.boolean_status!=='Finished' ? 'badge-light' : 'badge-primary'">B</span>
            </div>
            <div class="short-char" *ngIf="singleRun.status==='Pending'">
              <span ngbTooltip="Pending"><i class="far fa-hourglass"></i></span>
            </div>
            <div class="short-char" *ngIf="singleRun.status==='Processing'">
              <img class="spinner-octimine monitor-table" src="assets/images/octimine_blue_spinner.gif">
            </div>
          </td>
          <td (click)="getMonitorResultProfile(singleRun)" class="monitor-run-result-count p-y-spacing-big">
            {{singleRun.number_of_documents.TOTAL}}
          </td>
          <td (click)="getMonitorResultProfile(singleRun)" class="monitor-run-result-date p-y-spacing-big">{{singleRun.created_at | dateFormat: 'ShortDate'}}</td>
          <td class=" p-y-spacing-big">
            <a class="btn btn-primary btn-sm btn-icon"
               *ngIf="singleRun.status==='Finished' || singleRun.status==='Error'"
               (click)="deleteMonitorResultProfile(singleRun)"
               ngbTooltip="Click here to delete this result set">
              <i class="fas fa-trash-alt"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </table>
    <div *ngIf="monitorRuns.page.last_page>1">
      <app-pagination class="d-flex justify-content-end" [pagination]="monitorRuns.page"  (navigatePage)="navigateMonitorRun($event)"></app-pagination>
      <br>
    </div>
  </div>
  <app-alert type="info" [hidden]="monitorRuns" message="No results generated yet for this profile."></app-alert>
</div>

<app-patent-list-layout id="monitor-results-container"
                        [storeService]="storeService"
                        [documents]="documents"
                        [isLoading]="loading || loadingResult"
                        [showDashboardActionBar]="true"
                        (viewModeChange)="onChangeViewMode($event)">
  <ng-container alertMessages [ngTemplateOutlet]="noDocumentsMessage"></ng-container>

  <ng-container documentsControlBar [ngTemplateOutlet]="monitorControlBar"></ng-container>

  <ng-container documentsTable [ngTemplateOutlet]="monitorList"></ng-container>

  <ng-container documentsVisual>
    <app-charts-container [isColumnLayout]="isCombinedMode" [storeService]="storeService"></app-charts-container>
  </ng-container>
</app-patent-list-layout>

<app-filters-bar
  [showOperator]="true"
  [storeService]="storeService"
  [alwaysBeSticky]="true"
  (filterRemoved)="removeFilter($event)"
  (clearAll)="clearAllFilters()">
</app-filters-bar>

<app-spinner *ngIf="loadingResult || loading" id="monitor-loading" hidden></app-spinner>

<app-zoom-chart [showFavoriteOption]="false" [storeService]="storeService"></app-zoom-chart>

<ng-template #monitorControlBar>
  <div class="me-control-bar sticky-top" *ngIf="monitoringData && !loadingResult" [hidden]="!hasDocuments" data-intercom-target="control-bar">
    <div class="container-fluid d-flex justify-content-between align-items-center">
      <app-patent-control-bar class="d-flex" [searchService]="monitorService"
                              [columnsToShow]="columnsToShow"
                              [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                              [hasHarmonizeControl]="true" [hasExportControl]="true"
                              [exportDisplayOptions]="['csv','xlsx','pdf']"
                              [exportAdditionalParams]="additionalExportParams"
                              [hasTemporaryLinkControl]="true" [saveSearchTextInput]="selectedSnapshot?.name"
                              (sharePatentsEvent)="onSharePatents($event)"
                              [hasSaveToCollectionControl]="true"
                              [hasOctiAIControl]="true"
                              [hasFilterListControl]="true" (filterListEvent)="onAdvancedFilter($event)"
                              [taskShowCreationButton]="true"
                              [taskResourceId]="selectedMonitorRun"
                              [taskResourceType]="taskResourceTypeEnum.MONITOR_RUN"
                              (taskSaved)="onTaskSaved($event)" [storeService]="storeService"
                              [patentListScope]="isPublicationsScope ? patentListScopeEnum.PUBLICATION : patentListScopeEnum.FAMILY">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #monitorFooter>
  <div class="d-flex justify-content-between align-items-center" *ngIf="monitoringData && !loadingResult && hasDocuments">
    <div></div>
    <div class="d-flex justify-content-end align-items-center">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                     [pageOptions]="[25,50,100]"></app-page-size>
      <app-pagination [pagination]="monitoringData?.page" (navigatePage)="navigatePage($event)"></app-pagination>
    </div>
  </div>
</ng-template>

<ng-template #monitorList>
  <!-- TABLE -->
  <div class="container-fluid" [ngClass]="{'pe-0': isCombinedMode}">
    <div *ngIf="monitoringData && !loadingResult" class="monitor-run-data-box" id="monitor-results">
      <div [hidden]="!hasDocuments">
        <div class="d-flex justify-content-between align-items-center" data-intercom-target="Monitoring result">
          <div class="monitor-results-title new-layout-headline">
            <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{monitoringData['page']?.total_hits}}
            <span *ngIf="profile?.scope == 'Families'">
              {{ 'patent family' | pluralize: monitoringData['page']?.total_hits }}
              ({{monitoringData['page']?.total_publications}} {{ 'publication' | pluralize: monitoringData['page']?.total_publications }})
            </span>
            <span *ngIf="profile?.scope != 'Families'">
              {{ 'patent publication' | pluralize: monitoringData['page'].total_publications }}
            </span>
          </div>

          <div class="d-flex justify-content-end align-items-center">
            <button type="button" class="btn btn-primary btn-md" data-intercom-target="update-feedback-button"
              [disabled]="countFeedBack('POSITIVE') + countFeedBack('NEGATIVE') === 0" (click)="updateModel()">UPDATE
              MODEL / LIKES({{countFeedBack('POSITIVE')}}) DISLIKES({{countFeedBack('NEGATIVE')}})</button>

            <div class="ms-3 text-end tools-bar expand-all">
              <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()">
                {{ patentTable.openedPatent.length === monitoringData?.documents?.length ? 'Collapse all': 'Expand all'}}
                <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < monitoringData?.documents?.length"></i>
                <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === monitoringData?.documents?.length"></i>
              </a>
            </div>
          </div>
        </div>
        <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()"
                   [message]="collectionsStoreService.getSaveToCollectionSuccess()" class="mb-2"></app-alert>
        <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages" class="mb-2"></app-alert>

        <div class="psr-patent-table">
          <app-patent-table #patentTable [patents]="documents" (sort)="onSort($event)"
                            [showPatentSelection]="true" [pagination]="monitoringData ? monitoringData['page'] : 0" [hasLinksToBooleanSearch]="true"
                            [searchService]="monitorService" pathUrl="/patent" [linkData]="linkData" backButtonTitle="Back to monitor"
                            [srcMonitorRunId]="selectedSnapshot?.id" [storeService]="storeService"
                            [showSmartHighlight]="true" [showHighlight]="true" (feedbackClick)="updateFeedback($event)">
          </app-patent-table>
        </div>
      </div>

      <ng-container [ngTemplateOutlet]="monitorFooter"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #noDocumentsMessage>
  <div *ngIf="!hasDocuments && !loading && !loadingResult && selectedMonitorRun" class="container-fluid my-4">
    <div appNoResults [content]="getAlertMessage()"></div>
  </div>
</ng-template>
