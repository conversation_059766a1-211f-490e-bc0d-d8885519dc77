import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MonitorResultsComponent } from './monitor-results.component';
import { SharedModule } from '@shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HighchartsChartModule } from 'highcharts-angular';
import { BasicChartsModule, ClassificationChartsModule, MonitorChartsModule } from '@shared/charts';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { MonitorStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorResultsComponent', () => {
  let component: MonitorResultsComponent;
  let fixture: ComponentFixture<MonitorResultsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        MonitorResultsComponent,
      ],
      imports: [
        SharedModule,
        HighchartsChartModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        BasicChartsModule,
        ClassificationChartsModule,
        MonitorChartsModule,
        NgxSliderModule,
        RouterModule.forRoot([])
      ],
      providers: [
        MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorResultsComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
