import { Component, HostListener, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { BehaviorSubject, of, Subscription, switchMap } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, finalize, skip, take, tap } from 'rxjs/operators';
import { columnsToShow, publicationColumnsToShow, selectedColumnsCombinedMode } from './columns';
import {
  MonitorProfileMachineLearningStatusEnum,
  MonitorProfile, MonitorRunMLStatusEnum,
  MonitorRunStatusEnum,
  Patent,
  TaskModel,
  TaskResourceTypeEnum,
  UserProfile
} from '@core/models';
import {
  AdvancedFilterService,
  ApplicantsAliasesService,
  ChartsService,
  ConfirmationDialogService,
  MatomoService,
  MonitorService, PaginationMetadata, PatentListScopeEnum,
  PatentTableService,
  PublicationService,
  TaskService,
  UserService
} from '@core/services';
import { BaseMonitorStoreService, CollectionStoreService, SortParams } from '@core/store';
import { NgbCalendar, NgbDate, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { ViewModeTypeEnum } from '@search/patent/types';
import { PatentTableComponent } from '@shared/components';
import { ActionType } from '@shared/charts/chart-dashboard/types';

@Component({
  selector: 'app-monitor-results',
  templateUrl: './monitor-results.component.html',
  styleUrls: ['./monitor-results.component.scss']
})
export class MonitorResultsComponent implements OnInit, OnDestroy {
  @Input() storeService: BaseMonitorStoreService;

  createSnapShotDate: any;
  charts;
  user: UserProfile;
  monitoringData: { documents: Patent[], page: any };
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;
  sorting: SortParams = {field: null, order: null} as SortParams;
  selectedSnapshot;
  loadingResult = false;
  showDatepicker = false;
  hoveredDate: NgbDate | null = null;
  maxDate: NgbDateStruct;
  taskResourceTypeEnum = TaskResourceTypeEnum;
  savedTaskMessages: string[];
  patentListScopeEnum = PatentListScopeEnum;

  @ViewChild('patentTable') patentTable: PatentTableComponent;

  private activePage: number = 1;
  private activeMonitorRunPage: number = 1;
  private refreshIntervalID;
  private subscriptions = new Subscription();
  private startingPage = true;
  private feedbackDocuments: Patent[] = [];

  private loadMonitorResultsSubject = new BehaviorSubject<boolean>(null);

  constructor(
    public userService: UserService,
    public route: ActivatedRoute,
    public router: Router,
    private chartService: ChartsService,
    private calendar: NgbCalendar,
    private confirmationService: ConfirmationDialogService,
    private applicantAliasesService: ApplicantsAliasesService,
    public monitorService: MonitorService,
    public collectionsStoreService: CollectionStoreService,
    private advancedFilterService: AdvancedFilterService,
    private taskService: TaskService,
    private patentTableService: PatentTableService,
    private matomoService: MatomoService,
    private publicationService: PublicationService
  ) {

  }

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  get totalSelectedPatents() {
    return this.storeService.selectedPublications.length;
  }

  get linkData() {
    return this.monitorService.linkData;
  }

  get columnsToShow(){
    const columns = this.storeService.isPublications ? publicationColumnsToShow: columnsToShow;

    const isDLProfile = this.profile?.machine_learning_active &&
      this.profile?.machine_learning_status === MonitorProfileMachineLearningStatusEnum.READY;
    const isDLRunFinished = this.selectedSnapshot?.ml_status === MonitorRunMLStatusEnum.FINISHED;

    if (isDLProfile && isDLRunFinished) {
      return columns;
    } else {
      return columns.filter(c => !['monitor_feedback', 'monitor_label'].includes(c.property));
    }
  }

  /**
   * getter for monitor run list
   */
  get monitorRuns() {
    return this.storeService.monitorRuns;
  }

  set additionalExportParams(value) {
    this.storeService.additionalExportParams = value;
  }
  get additionalExportParams() {
    return this.storeService.additionalExportParams;
  }

  /**
   * setter for monitor run list
   */
  set monitorRuns(list) {
    this.storeService.setMonitorRuns(list);
  }

  get monitorRunRequestArr() {
    return this.storeService.monitorRunRequestArr;
  }

  get selectedRange(): string {
    if (this.createSnapShotDate?.startDate && this.createSnapShotDate?.endDate) {
      return `${this.formateDate(this.createSnapShotDate.startDate)} - ${this.formateDate(this.createSnapShotDate.endDate)}`;
    }
    return '';
  }

  get loading(): Boolean{
    return this.storeService.loading;
  }

  get selectedMonitorRun(): number {
    return this.storeService.selectedMonitorRun;
  }

  get documents(){
    return this.storeService.resultSetDocuments;
  }

  get isPublicationsScope(): boolean {
    return this.profile?.scope && this.profile?.scope !== 'Families';
  }

  get viewMode (): ViewModeTypeEnum{
    return this.storeService.patentListViewMode;
  }
  get isCombinedMode(): boolean {
    return this.storeService.isCombinedMode;
  }

  get hasDocuments(): boolean {
    return this.monitoringData?.documents?.length > 0;
  }

  get hasFilter(): boolean {
    return this.storeService.getAppliedFiltersQuery()?.length > 0;
  }

  get pageSize(): number {
    return this.storeService.pageSize;
  }

  set pageSize(value: number) {
    this.storeService.pageSize = value;
  }

  isDisabled = (date: NgbDate, current: { month: number }) => {
    if (this.maxDate) {
      return date.after(this.maxDate);
    }
    return false;
  };

  @HostListener('document:keydown.escape', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.showDatepicker = false;
  }

  ngOnInit() {
    const skipPipeAfterBack = this.storeService.backPatentSearch ? 1 : 0;
    if (this.storeService.backPatentSearch) {
      this.setFieldsAfterBack();
      if (!this.storeService.scrollTopPage) {
        this.storeService.elementIdToScrollPage = 'monitor-results-container';
      }
      this.scrollToResults();
      this.storeService.backPatentSearch = false;
      this.selectedSnapshot = this.getSelectedMonitorRun();
    } else {
      this.clearStoredData();
      this.clearStoredResults();
      this.clearAdvancedFilter();
      this.storeService.purgeResultSetDocuments();
      this.storeService.patentListViewMode = ViewModeTypeEnum.COMBINED;
    }

    if (this.storeService.resultSetPagination) {
      this.activePage = this.storeService.resultSetPagination.current_page;
    }

    if (this.storeService.selectedMonitorRun) {
      this.adjustTableColumnToDisplay();
      this.loadMonitorResultsSubject.next(true);
    }

    const changeApplicantsEvent$ = this.applicantAliasesService.changeApplicantsEvent
      .subscribe({
        next: () => {
          this.loadMonitorResultsSubject.next(true);
        }
      });
    this.subscriptions.add(changeApplicantsEvent$);

    const user$ = this.userService.user.subscribe({
      next: u => this.user = u.profile
    });
    this.subscriptions.add(user$);

    const firstMonitorRun$ = this.storeService.firstMonitorRun
      .pipe(
        skip(skipPipeAfterBack)
      )
      .subscribe({
        next: value => {
          if (value) {
            if (this.refreshIntervalID) {
              this.monitorRunRequestArr.push(value);
            } else {
              if (this.monitoringData === undefined) {
                this.getMonitorRuns(false);
              }
              this.refreshMonitorRun(value);
            }
          }
        },
        error: err => {
          console.log('monitor run id Observer got a error' + err);
        },
        complete: () => {
          console.log('monitor run id Observer got a complete notification');
        }
      });
    this.subscriptions.add(firstMonitorRun$);

    const filters$ = this.storeService.filters$
      .pipe(
        skip(skipPipeAfterBack)
      )
      .subscribe({
        next: (item) => {
          this.applyFilter();
        }
      });
    this.subscriptions.add(filters$);

    const searchHash$ = this.storeService.searchHash$
      .subscribe({
        next: (hash) => {
          if (hash) {
            this.getCharts();
          }
        }
      });
    this.subscriptions.add(searchHash$);

    const loadMonitorResults$ = this.loadMonitorResultsSubject.asObservable()
      .pipe(
        switchMap((val) => {
          if (val !== null) {
            return this.loadMonitorResults(val);
          }
        }),
        catchError((err) => of(null))
      )
      .subscribe();
    this.subscriptions.add(loadMonitorResults$);

    const chartDashboardAction$ = this.storeService.chartDashboardAction$
      .pipe(
        skip(skipPipeAfterBack)
      )
      .subscribe({
        next: (msg) => {
          this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);

          if (msg.action === ActionType.Add) {
            this.storeService.searchHash = this.storeService.searchHash;
          }
        }
      });
    this.subscriptions.add(chartDashboardAction$);
  }

  getMonitorRuns(loadFirst = true) {
    this.clearStoredData();
    this.clearStoredResults();
    if (this.profile) {
      this.storeService.loading = true;

      const queryParam = {monitor_profile_id: this.profile.id, page: this.activeMonitorRunPage, page_size: this.monitorService.MONITOR_RUNS_PAGE_SIZE};
      const getMonitorRuns$ = this.monitorService.getMonitorRuns(queryParam)
        .pipe(
          finalize(() => this.storeService.loading = false)
        )
        .subscribe({
          next: ({data}) => {
            this.storeService.setMonitorRuns(data);
            this.monitorRuns = data;
            if (data.page.total_hits > 0) {
              if (loadFirst) {
                this.storeService.setSelectedMonitorRun(data.monitor_runs[0].id);
                this.selectedSnapshot = data.monitor_runs[0];
                this.prepareAdditionalExportParams();
                this.loadMonitorResultsSubject.next(true);
              }
            }
          },
          error: (err) => {
            console.log(err);
            this.monitorService.setErrors(['Error while getting result set list.']);
          }
        });
      this.subscriptions.add(getMonitorRuns$);
    }
  }

  loadMonitorResults(reloadCharts: boolean) {
    if (!this.storeService.selectedMonitorRun) {
      return of(null);
    }

    this.storeService.elementIdToScrollPage = 'monitor-results-container';
    this.loadingResult = true;
    const query = this.buildQuery();
    return this.monitorService.getMonitorRunResults(this.storeService.selectedMonitorRun, query)
      .pipe(
        catchError((error) => {
          console.warn(error);
          this.monitoringData = null;
          this.monitorService.setErrors(['Error while getting result set publications.']);
          this.storeService.loading = false;
          this.loadingResult = false;
          this.startingPage = false;
          return of({data: null});
        }),
        tap(({data}) => {
          if (!data) {
            return;
          }

          if (this.isPublicationsScope) {
            data['documents'] = this.publicationService.publicationsToDocuments(data['publications']);
          }

          this.monitoringData = data;
          this.storeService.setResultSetDocuments(this.monitoringData['documents']);
          this.storeService.resultSetPagination = this.monitoringData['page'];
          this.storeService.pagination = this.storeService.resultSetPagination;

          if (this.monitoringData['search_info'].search_hash !== this.storeService.searchHash) {
            this.storeService.searchHash = this.monitoringData['search_info'].search_hash;
          }
          const payload = 'filters' in query ? {search_filters: {free_text_query: query['filters']}}: {}
          this.storeService.search = {payload, params: Object.assign({}, query)};
          this.storeService.state.total_hits = data.page.total_hits;

          this.monitoringData['documents'].forEach((patent) => {
            this.updateFeedback(patent);
          });

          if (!this.selectedSnapshot) {
            this.selectedSnapshot = this.getSelectedMonitorRun();
          }

          if (reloadCharts && data.documents?.length > 0) {
            this.getCharts();
          } else {
            this.storeService.loading = false;
            this.loadingResult = false;
            this.startingPage = false;
            if (this.storeService.docdbFamilyIdFromPatentViewer) {
              this.storeService.scrollToPatentFromPatentViewer();
            } else {
              this.scrollToResults();
            }
          }
        })
      );
  }

  onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.activePage = 1;
    this.storeService.elementIdToScrollPage = 'monitor-results';
    this.loadMonitorResultsSubject.next(false);
  }

  deleteMonitorResultProfile(monitorRun: Object) {
    this.confirmationService.confirm('Delete result set', 'Do you really want to delete the result set?',
      'Yes', 'Cancel', 'lg').then((confirmed) => {
      if (confirmed) {
        this.storeService.loading = true;
        this.monitorService.deleteMonitorRun(monitorRun['id']).subscribe({
          next: (res) => {
            this.getMonitorRuns(monitorRun['id'] === this.storeService.selectedMonitorRun);
          },
          error: err => {
            console.log(err);
            this.monitorService.setErrors(['Error while deleting Result set.']);
            this.storeService.loading = false;
          }
        });
      }
    }).catch(() => {
    });
  }

  createMonitorResultProfile() {
    if (!this.createSnapShotDate.startDate || !this.createSnapShotDate.endDate) {
      this.confirmationService.alert('Octimine', 'Please select date range for monitor run');
      return;
    }
    const d1 = this.createSnapShotDate.startDate;
    const d2 = this.createSnapShotDate.endDate;
    const payload = {
      from_date: `${d1.year}-${('0' + d1.month).slice(-2)}-${('0' + d1.day).slice(-2)}`,
      to_date: `${d2.year}-${('0' + d2.month).slice(-2)}-${('0' + d2.day).slice(-2)}`,
    };
    const newRun$ = this.monitorService.createMonitorRun(payload, {monitor_profile_id: this.profile.id}).subscribe({
      next: res=>{
        this.createSnapShotDate = null;
        this.getMonitorRuns(false);
        setTimeout(() => {
          this.storeService.setMonitorRunID(res['data']['monitor_run_id']);
        }, 1000);
      },
      error: err=>{
        console.log(err);
        this.monitorService.setErrors(['Error while creating result set.']);
      }
    });
    this.subscriptions.add(newRun$);
  }

  onDateSelection(date: NgbDate) {
    if (!this.createSnapShotDate) {
      this.createSnapShotDate = {};
    }
    if (!this.createSnapShotDate?.startDate && !this.createSnapShotDate?.endDate) {
      this.createSnapShotDate['startDate'] = date;
    } else if (this.createSnapShotDate?.startDate && !this.createSnapShotDate?.endDate && date.after(this.createSnapShotDate.startDate)) {
      this.createSnapShotDate['endDate'] = date;
    } else {
      this.createSnapShotDate = {startDate: date, endDate: null};
    }
    if (this.createSnapShotDate?.startDate && this.createSnapShotDate?.endDate) {
      this.showDatepicker = false;
    }
    this.changeDateLimits();
  }

  isHovered(date: NgbDate) {
    return this.createSnapShotDate?.startDate && !this.createSnapShotDate?.endDate && this.hoveredDate &&
      date.after(this.createSnapShotDate?.startDate) && date.before(this.hoveredDate);
  }

  isInside(date: NgbDate) {
    return this.createSnapShotDate?.endDate &&
      date.after(this.createSnapShotDate?.startDate) &&
      date.before(this.createSnapShotDate?.endDate);
  }

  isRange(date: NgbDate) {
    return date.equals(this.createSnapShotDate?.startDate) || (this.createSnapShotDate?.endDate &&
      date.equals(this.createSnapShotDate?.endDate)) || this.isInside(date) || this.isHovered(date);
  }

  isDisable(date: NgbDate) {
    return this.maxDate && date.after(this.maxDate);
  }

  changeDateLimits() {
    if (this.createSnapShotDate?.startDate) {
      this.maxDate = this.calendar.getNext(
        this.createSnapShotDate.startDate,
        "d",
        29
      );
    } else {
      this.maxDate = null;
    }
  }

  formateDate(date: NgbDate) {
    const dateFormatting = this.userService.getLocale().dateFormatting;
    if (dateFormatting === 'MM/dd/yyyy') {
      return `${date.month}/${date.day}/${date.year}`;
    } else if (dateFormatting === 'dd.MM.yyyy') {
      return `${date.day}.${date.month}.${date.year}`;
    }
    return `${date.day}/${date.month}/${date.year}`;
  }

  getCharts() {
    if (!this.isCombinedMode) {
      this.scrollToLoading();
    }
    this.storeService.isCalculatingCharts = true;

    const payload = this.buildChartsPayload();

    this.chartService.resetCharts();
    const calculate$ = this.chartService.calculate(payload, this.storeService.searchHash)
      .pipe(
        take(1),
        catchError((err) => of({charts: []})),
        finalize(() => {
          this.loadingResult = false;
          this.startingPage = false;
          this.storeService.isCalculatingCharts = false;
        })
      )
      .subscribe({
        next: ({charts}) => {
          this.charts = charts;
          this.storeService.loading = false;

          if (this.storeService.docdbFamilyIdFromPatentViewer) {
              this.storeService.scrollToPatentFromPatentViewer();
          } else {
            this.scrollToResults();
            this.scrollOnBack();
          }
        },
        error: (err) => {
          console.log(err);
          this.monitorService.setErrors(['Error while getting Chart data.']);
        }
      });
    this.subscriptions.add(calculate$);
  }

  /**
   * apply single monitoring run
   * @param snapShot Object of single monitor run
   */
  getMonitorResultProfile(snapShot: Object) {
    if (snapShot['status'] !== 'Finished') {
      return;
    }
    if (snapShot['id']) {
      this.monitorService.updateURLRunID(snapShot['id']);
      this.storeService.setSelectedMonitorRun(snapShot['id']);
      this.selectedSnapshot = snapShot;
      this.prepareAdditionalExportParams();
    }
    this.activePage = 1;
    this.clearStoredData();
    this.storeService.chartQuantity = 5;
    this.storeService.chartSelectedValues = {};
    this.chartService.resetCharts();
    this.matomoService.monitorTechnologyTimeIntervalButton();
    this.storeService.setFilters([]);
  }

  /**
   * reload data on filter application
   */
  applyFilter() {
    if (this.loadingResult) {
      return;
    }

    this.activePage = 1;
    this.storeService.elementIdToScrollPage = 'monitor-results-container';
    this.loadMonitorResultsSubject.next(true);
  }

  navigatePage(page: number) {
    this.activePage = page;
    this.storeService.elementIdToScrollPage = 'monitor-results-container';
    this.loadMonitorResultsSubject.next(false);
  }

  navigateMonitorRun(page) {
    this.activeMonitorRunPage = page;
    this.getMonitorRuns(false);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.clearStoredData();
    this.storeService.chartSelectedValues = {};
    this.chartService.resetCharts();
    this.patentTableService.selectedLegalStatus = [];
  }

  countFeedBack(matchType): number {
    if (this.monitoringData && this.monitoringData['documents']) {
      return this.feedbackDocuments.filter((value: Patent) => value.machine_learning_feedback &&
        value.machine_learning_feedback.match === matchType).length;
    }
    return 0;
  }

  updateModel() {
    this.storeService.setupMonitoringEvent.emit(true);
    this.matomoService.monitorTechnologyUpdateModelButton();
  }

  onSort(val: SortParams) {
    this.sorting = val;
    this.storeService.elementIdToScrollPage = 'monitor-results';
    this.loadMonitorResultsSubject.next(false);
  }

  onSharePatents(event) {
    this.storeService.elementIdToScrollPage = 'monitor-results';
    this.loadMonitorResultsSubject.next(false);
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.loadMonitorResultsSubject.next(true);
      this.storeService.searchHash = this.storeService.searchHash;
    }
  }

  onTaskSaved(data: { message: string, payload: TaskModel, savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessages = this.taskService.getTaskCreationSuccessMessage(data, 'monitor_run');

      setTimeout(() => {
        this.router.navigate(['/ratings', data.savedTasks[0].id]);
      }, 3000);
    }
  }

  getAlertMessage(): string {
    if (this.hasFilter) {
      return 'It seems we can’t find any result based on your selection.<br>Try to clear or change your filters.';
    }

    return 'This monitor run does not have any documents yet.';
  }

  /**
   * query builder for result table and chart section
   */
  private buildQuery() {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;

    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
      query['sort_order'] = this.sorting.order;
    }

    const filters = [this.storeService.advancedFilterAppliedQuery];

    this.storeService.filters.forEach((item) => {
      if (item.chart === 'monitor_venn_diagram') {
        query['type'] = item.value.split('∩');
      } else {
        filters.push(item.query);
      }
    });

    const filterQuery = filters.filter((q) => q && q.trim().length > 0)
      .join(` ${this.storeService.filtersOperator} `);

    if (filterQuery) {
      query['filters'] = filterQuery;
    }

    return query;
  }

  /**
   * refresh the monitor run
   * @param ID ID for MOnitor run to be fetched
   */
  private refreshMonitorRun(ID) {
    const self = this;
    this.refreshIntervalID = setTimeout(function () {
      const getMonitorRun$ = self.monitorService.getMonitorRun(ID)
        .subscribe({
          next: (data) => {
            if (data.status !== MonitorRunStatusEnum.FINISHED) {
              self.refreshMonitorRun(ID);
              if (self.monitorRuns) {
                const foundIndex = self.monitorRuns.monitor_runs.findIndex(x => x.id === ID);
                if (foundIndex > -1) {
                  self.monitorRuns.monitor_runs[foundIndex] = data;
                }
              }
            } else {
              self.storeService.purgeMonitorRunID();
              clearInterval(self.refreshIntervalID);
              self.refreshIntervalID = undefined;
              let loadFirst = false;
              if (self.monitorRunRequestArr.length > 0) {
                self.refreshMonitorRun(self.monitorRunRequestArr.shift());
              } else if (!self.monitoringData) {
                loadFirst = true;
              }
              self.getMonitorRuns(loadFirst);
            }
          },
          error: (err) => {
            console.log(err);
          }
        });
      self.subscriptions.add(getMonitorRun$);
    }, 3000);
  }

  private prepareAdditionalExportParams() {
    this.additionalExportParams = {
      title: 'Monitor REPORT',
      subtitle: `For profile "${this.profile.name}", based on documents published between ${this.selectedSnapshot['name']}`,
    };
  }

  private scrollToLoading() {
    if (this.startingPage) return;

    setTimeout(() => {
      if (document.getElementById('monitor-loading')) {
        document.getElementById('monitor-loading')
          .scrollIntoView({behavior: 'smooth', block: 'end', inline: 'nearest'});
      }
    });
  }

  private scrollToResults() {
    if (this.startingPage || !this.storeService.elementIdToScrollPage) {
      return;
    }
    setTimeout(() => {
      if (document.getElementById(this.storeService.elementIdToScrollPage)) {
        document.getElementById(this.storeService.elementIdToScrollPage)
          .scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
        this.storeService.elementIdToScrollPage = '';
      }
    });
  }

  private scrollOnBack() {
    if (this.storeService.scrollTopPage) {
      setTimeout(() => {
        window.scrollTo({
          top: this.storeService.scrollTopPage,
          behavior: 'smooth'
        });
        this.storeService.scrollTopPage = null;
      }, 100);
    }
  }

  private clearStoredData() {
    this.storeService.setFilters([], false);
    this.storeService.selectedPublications = [];
    this.storeService.patentTableSort = {field: null, order: null} as SortParams;
  }

  private clearStoredResults() {
    this.storeService.setResultSetDocuments([]);
    this.storeService.resultSetPagination = {} as PaginationMetadata;
    this.monitoringData = null;
  }

  private clearAdvancedFilter() {
    this.advancedFilterService.reset();
    this.storeService.resetAdvancedFilter();
  }

  private buildChartsPayload() {
    const payload = {
      charts: this.storeService.getChartActiveNames()
    };

    payload['parameters'] = {
      portfolio_analytics_profile_indicators: [{axis: 'x', property: 'tech_fields'}, {axis: 'y', property: 'citation_backward_count'}]
    }

    const freeTextQuery = this.storeService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    return payload;
  }

  private setFieldsAfterBack() {
    if (this.storeService.patentTableSort) {
      this.sorting = {...this.storeService.patentTableSort};
    }

    this.monitoringData = {
      documents: this.storeService.resultSetDocuments,
      page: this.storeService.resultSetPagination
    };
  }
  onChangeViewMode(mode: ViewModeTypeEnum){
    if(mode === ViewModeTypeEnum.COMBINED){
      this.adjustTableColumnToDisplay();
    }
  }
  adjustTableColumnToDisplay(){
    if(this.storeService.selectedColumnsToShow && this.storeService.selectedColumnsToShow.length > 4 ){
      let columns = this.storeService.selectedColumnsToShow;
      columns = columns.filter(o => o.property !== 'priority_date');
      columns = columns.filter(o => o.property !== 'applicants');
      if(columns.length < this.storeService.selectedColumnsToShow.length){
        this.storeService.selectedColumnsToShow = columns;
      }
    }
  }
  removeFilter(item) {
    if (item.type === "venn-chart") {
      const filters = [...this.storeService.filters];
      const index = filters.findIndex((filter) => filter.chart === item.chart);

      if (index > -1) {
        filters.splice(index, 1);
        this.storeService.setFilters(filters);
      }
    }
  }

  clearAllFilters() {
    this.storeService.resetAdvancedFilter();
    this.storeService.setFilters([]);
    this.advancedFilterService.reset();
  }

  private getSelectedMonitorRun() {
    return this.storeService.monitorRuns['monitor_runs'].find(r => r.id === this.storeService.selectedMonitorRun);
  }

  updateFeedback(patent: Patent) {
    let document;
    if (this.isPublicationsScope) {
      const publicationNumber = this.patentTableService.getPublicationNumber(patent);
      document = this.feedbackDocuments.find(p => this.patentTableService.getPublicationNumber(p) === publicationNumber);
    } else {
      document = this.feedbackDocuments.find(p => p.general.docdb_family_id === patent.general.docdb_family_id);
    }

    if (!document) {
      this.feedbackDocuments.push(patent);
    } else {
      Object.assign(document, patent);
    }
  }
}
