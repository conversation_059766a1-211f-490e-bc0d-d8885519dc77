<div class="container pb-4">
  <h4 class="profile-headline"> Semantic profile&nbsp;
    <app-tooltip id="monitor_semantic" [tooltipTitle]='tooltip.SemanticTitle' [tooltipText]='tooltip.SemanticText'></app-tooltip>
    &nbsp;
    <label class="check-on-off method-state  h-100 mb-0 align-middle" ngbTooltip="Enable/Disable semantic profile">
      <input type="checkbox" [checked]="profile?.semantic_query_active" class="left" (click)="checkStatus($event)" />
    </label>
  </h4>
  <!-- Input box -->
  <div class="monitoring-inputBox-wrap">
    <app-semantic-input [form]="form" [textWeighting]="profile?.semantic_query?.search_filters?.text_weighting || 1" [storeService]="storeService"
      (inputChanged)="inputChanged(form)" (weightingChange)="weightingChange($event)" (submitInput)="submit()">
    </app-semantic-input>
    <div class=" btn-wrap text-end mt-4">
      <button type="button" ngbTooltip="Click here to reset profile" class="btn btn-secondary-outline btn-md ms-3 btn-icon clear-profile-method"
        (click)="reset()"><i class="fa fa-times"></i></button>
      <button type="button" [ngbTooltip]="isFormEmpty ? '' :'Click here to view input results'" [disabled]="isFormEmpty"
        class="btn btn-primary btn-md ms-3" (click)="submit()"
        data-intercom-target="view-input-button">VIEW INPUT</button>
      <button type="button" class="btn btn-primary btn-md ms-3" (click)="onSetupProfile()" [disabled]="isFormEmpty"
        data-intercom-target="Semantic setup monitoring"
        [ngbTooltip]="isFormEmpty ? '' :'Click here to setup monitoring profile'">SETUP MONITORING</button>
    </div>
  </div>
  <!-- ./Input box -->
</div>
<div class="container-fluid">
<div class="table-tabs pb-4" *ngIf="semanticResults || patentsResults">
  <ul class="nav-tabs nav-2" ngbNav #semanticMethod="ngbNav" data-intercom-target="queried-patents-title">
    <li [ngbNavItem]="'semanticsearch'" *ngIf="semanticResults">
      <a ngbNavLink>Top 100 search preview </a>
      <ng-template ngbNavContent>
        <div>
          <app-patent-table [patents]="semanticResults.documents" [secondaryPatentSelection]="selectionList"
                            (patentChecked)="onPatentChecked($event)" [pagination]="semanticResults.page" [hasLinksToBooleanSearch]="true"
                            [showSelectAll]="false" [selectionLimit]="5" pathUrl="/patent" [linkData]="linkData"
                            [hideColumns]="['monitor_feedback','monitor_label']" [searchHash]="semanticResults.search_info.search_hash"
                            [allowFilteringByLegalStatus]="false"
                            (error)="onPatentTableError($event)" backButtonTitle="Back to monitor"
                            (legalStatusSelected)="onSelectLegalStatus($event)" [storeService]="storeService">
          </app-patent-table>
          <app-pagination *ngIf="semanticResults?.documents?.length" class="d-flex justify-content-end" [pagination]="semanticResults?.page" [maxPage]="4" (navigatePage)="navigatePage($event)">
          </app-pagination>
          <app-filters-bar *ngIf="storeService.filters.length" [alwaysBeSticky]="true" [storeService]="storeService"></app-filters-bar>
        </div>
      </ng-template>
    </li>
    <li [ngbNavItem]="'PublicationNumber'" *ngIf="patentsResults">
      <a ngbNavLink>Queried patents</a>
      <ng-template ngbNavContent>
        <div *ngIf="patentsResults && semanticResults">
          <app-patent-table [patents]="patentsResults.documents" pathUrl="/patent"
                            [linkData]="linkData" [showPatentSelection]="false" [pagination]="patentsResults.page"
                            [hasLinksToBooleanSearch]="true" [hideColumns]="['monitor_feedback','monitor_label']"
                            [allowFilteringByLegalStatus]="false"
                            [searchHash]="semanticResults?.search_info?.search_hash" backButtonTitle="Back to monitor"
                            [allowSelectLegalStatus]="false" [storeService]="storeService">
          </app-patent-table>
        </div>
      </ng-template>
    </li>
  </ul>
  <div class="tab-content">
    <div [ngbNavOutlet]="semanticMethod" ></div>
  </div>
</div>
</div>
