import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  ConfirmationDialogService,
  extractPublications,
  MatomoService,
  MonitorService,
  PatentNumberService,
  PatentTableService,
  SemanticSearchService
} from '@core/services';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { BaseMonitorStoreService } from '@core/store';
import { tooltip } from '@monitor/tooltip';
import { columnsToShow } from '../monitor-results/columns';
import { MonitorProfile } from '@core/models';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-monitor-semantic',
  templateUrl: './monitor-semantic.component.html',
  styleUrls: ['./monitor-semantic.component.scss']
})
export class MonitorSemanticComponent implements OnInit, OnD<PERSON>roy {
  @Input() storeService: BaseMonitorStoreService;
  @Output() resultClick = new EventEmitter();
  @Output() changeMethod: EventEmitter<string> = new EventEmitter();

  semanticResults: any;
  patentsResults: any;
  form: UntypedFormGroup;
  selectionList: Array<string> = [];
  private callCount = 0;
  private subscriptions = new Subscription();

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    private confirmationService: ConfirmationDialogService,
    private semanticSearchService: SemanticSearchService,
    private patentNumberService: PatentNumberService,
    public monitorService: MonitorService,
    private patentTableService: PatentTableService,
    private matomoService: MatomoService
  ) {
  }

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  get tooltip() {
    return tooltip;
  }

  get linkData() {
    return this.monitorService.linkData;
  }

  get processPatentText(): string[] {
    const {publications, remainingText} = extractPublications(this.form.value.publications);
    return publications
      ? publications
      : [];
  }

  get isSemanticEmpty() {
    return (!this.profile.semantic_query?.search_input && !this.profile.semantic_query?.patent_numbers)
      || (this.profile.semantic_query?.search_input === '' && this.profile.semantic_query.patent_numbers.length === 0);
  }

  /**
   * check if form input is empty
   */
  get isFormEmpty() {
    return !this.form.get('term').value.trim() && !this.form.get('publications').value.trim();
  }

  ngOnInit() {
    this.storeService.setFilters([], false);
    this.storeService.selectedColumnsToShow = columnsToShow;

    this.form = new UntypedFormGroup({
      term: new UntypedFormControl(this.profile?.semantic_query?.search_input || ''),
      publications: new UntypedFormControl(this.profile?.semantic_query?.patent_numbers ?
        this.profile['semantic_query'].patent_numbers.join(', ') : '')
    });
  }

  /**
   * save profile
   * @param search whether to run search event
   * @param setupSemantic whether to run setup Semantic event
   */
  submit(search: boolean = true, setupSemantic: boolean = false) {
    this.monitorService.setErrors([]);
    this.profile['semantic_query'].patent_numbers = this.processPatentText;
    this.profile['semantic_query'].search_input = this.form.value.term;
    this.selectionList = this.profile['semantic_query'].patent_numbers;

    const {publications, remainingText} = extractPublications(this.profile['semantic_query'].search_input);
    if (remainingText === '' && publications.length > 0) {
      this.profile['semantic_query'].patent_numbers = publications;
      this.profile['semantic_query'].search_input = remainingText;
    }
    if (this.profile['semantic_query'].patent_numbers.length > 5) {
      this.profile['semantic_query_active'] = false;
      this.monitorService.setErrors(['The Publication numbers should be less then 6.']);
      return;
    }
    this.storeService.loading = true;
    this.semanticResults = this.patentsResults = undefined;
    // this.profile['semantic_query_active'] = !this.isSemanticEmpty ? true : false;
    const payload = {
      'semantic_query_active': this.profile['semantic_query_active'],
      'semantic_query': this.profile['semantic_query']
    };
    this.monitorService.updateProfile(this.profile.id, payload).then(
      (response) => {
        if (setupSemantic && !this.isSemanticEmpty) {
          this.setup();
        }
        if (search && !this.isSemanticEmpty) {
          this.searchSemantic(1);
          if (this.profile['semantic_query'].patent_numbers.length > 0) {
            this.searchPatents(1);
          }
        }
        if (!search && !setupSemantic) {
          this.storeService.loading = false;
        }
      }
    ).catch((e) => {
      console.error(e);
      this.monitorService.setErrors(['Error while saving semantic profile.']);
      this.storeService.loading = false;
    });
  }

  reset() {
    this.confirmationService.confirm(
      'Semantic profile message',
      'Do you really want to clear Semantic profile?',
      'Yes')
      .then((confirmed) => {
        if (confirmed) {
          this.storeService.loading = true;
          const payload = {
            'semantic_query_active': false,
            'semantic_query': {
              'patent_numbers': [],
              'search_input': '',
              'search_filters': {
                'text_weighting': 1
              }
            }
          };
          this.monitorService.updateProfile(this.profile.id, payload)
            .then((response) => {
              this.form.get('publications').setValue('');
              this.form.get('term').setValue('');
              this.profile['semantic_query'] = response['semantic_query'];
              this.profile['semantic_query_active'] = response['semantic_query_active'];
              this.semanticResults = this.patentsResults = undefined;
              this.storeService.loading = false;
            })
            .catch((e) => {
              console.error(e);
              this.monitorService.setErrors(['Error while saving semantic profile.']);
              this.storeService.loading = false;
            });
        }
      }).catch(() => {
    });
  }

  checkStatus(event) {
    if (event !== undefined) {
      if (event.target.checked && this.isSemanticEmpty) {
        this.profile.semantic_query_active = false;
        this.monitorService.setErrors(['Please fill out the semantic profile!']);
        event.preventDefault();
      } else {
        this.storeService.loading = true;
        this.monitorService.updateProfile(this.profile.id, {'semantic_query_active': event.target.checked})
          .then((response) => {
            this.profile.semantic_query_active = response['semantic_query_active'];
            this.storeService.loading = false;
          })
          .catch((e) => {
            console.error(e);
            this.monitorService.setErrors(['Error while updating semantic profile state.']);
            this.storeService.loading = false;
          });
      }
    }
  }

  navigatePage(page) {
    this.searchSemantic(page);
  }

  /**
   * event listener for semantic input component
   * @param form updated form with values
   */
  inputChanged(form) {
    this.selectionList = this.processPatentText;
  }

  /**
   * event listener for weighting slider
   * @param weight weighting value between text and patent
   */
  weightingChange(weight) {
    this.profile.semantic_query.search_filters = {text_weighting: weight};
  }

  /**
   * onSelectionListUpdate
   */
  onSelectionListUpdate(event) {
    if (event.action === 'add') {
      this.form.get('publications').setValue(this.form.value.publications + ' ' + event.value);
    } else if (event.action === 'remove') {
      this.form.get('publications').setValue(this.form.value.publications.replace(event.value, ''));
    }
  }

  /**
   * Event emitter for resultClick
   */
  onResultsBtn() {
    this.resultClick.emit();
  }

  /**
   * onSetupProfile
   *
   * Event listener for semantic method in monitoring feature
   */
  onSetupProfile() {
    this.profile['semantic_query_active'] = true;
    this.submit(false, true);
    this.matomoService.monitorTechnologySemanticSetupButton();
  }

  /**
   * setup
   */
  setup() {
    if (this.isFormEmpty) {
      this.monitorService.setErrors(['Please fill out the semantic profile!']);
    } else {
      if (this.profile['semantic_query_active']) {
          this.storeService.loading = false;
          this.confirmation();
        return;
      }
      this.storeService.loading = true;
      this.monitorService.updateProfile(this.profile.id, {semantic_query_active: true})
        .then((response) => {
          this.profile['semantic_query_active'] = response['semantic_query_active'];
          this.storeService.loading = false;
          this.confirmation();
        })
        .catch((e) => {
          console.error(e);
          this.monitorService.setErrors(['Error activating semantic profile.']);
          this.storeService.loading = false;
        });
    }
  }

  /**
   * onPatentChecked
   */
  onPatentChecked(event) {
    this.form.get('publications').setValue(this.selectionList.join());
  }

  /**
   * onPatentTableError
   */
  onPatentTableError(message: string) {
    this.confirmationService.alert('Monitor message', message);
  }

  onSelectLegalStatus(status: string): void {
    this.searchSemantic();
  }

  ngOnDestroy(): void {
    this.patentTableService.selectedLegalStatus = [];
    this.subscriptions.unsubscribe();
  }

  /**
   * Search semantic result
   * @param page Page number to navigate
   */
  private searchSemantic(page = 1) {
    this.storeService.loading = true;
    this.semanticResults = undefined;
    this.callCount++;
    if (this.storeService.getResultTableFilterQuery()) {
      const search_filters = {
        ...this.profile['semantic_query']['search_filters'],
        free_text_query: this.storeService.getResultTableFilterQuery()
      }

      this.profile['semantic_query']['search_filters'] = search_filters
    } else {
      this.profile['semantic_query']['search_filters'] = {'text_weighting': 1};
    }
    const search$ = this.semanticSearchService.search(this.profile['semantic_query'], {page: page, show_general: 1}).subscribe({
      next: res=>{
        this.callCount--;
        this.semanticResults = res['data'];
        if (this.callCount === 0) {
          this.storeService.loading = false;
        }
      }, error: err=>{
        console.log(err);
        this.monitorService.setErrors(['Error while searching semantic profile.']);
        this.storeService.loading = false;
      },
    });
    this.subscriptions.add(search$);
  }

  /**
   * Search semantic result for Text query
   * @param page Page number to navigate
   */
  private searchPatents(page = 1) {
    this.storeService.loading = true;
    this.patentsResults = undefined;
    this.callCount++;
    this.patentNumberService.search({'patent_numbers': this.profile['semantic_query'].patent_numbers}, {show_general: 1}).subscribe({
      next: resp=>{
        this.callCount--;
        this.patentsResults = resp['data'];
        if (this.callCount === 0) {
          this.storeService.loading = false;
        }
      },
      error: err=>{
        console.log(err);
        this.monitorService.setErrors(['Error while searching publications.']);
        this.storeService.loading = false;
      }
    })
  }

  private confirmation() {
    this.confirmationService.confirm(
      'Semantic Monitoring has been setup successfully',
      ['Please click on Results button to immediately analyse the most recent results.' +
      ' OR click on Other button to setup another monitoring method.',
        'Note: Another monitoring method means a different way to look at the same profile.'],
      'Other',
      'Results')
      .then((confirmed) => {
        if (confirmed) {
          this.changeMethod.emit('boolean');
        } else {
          const init$ = this.monitorService.initMonitorRun(this.profile.id).subscribe({
            next: (res)=>{
              if (this.monitorService.getErrors().length === 0) {
                this.monitorService.loadMonitorRuns(this.storeService, false).then(() => {
                  this.onResultsBtn();
                }).catch(() => { })
              } else {
                this.onResultsBtn();
              }
            }, error: (err) => {
              console.log(err);
              this.monitorService.setErrors(['Error while generating initial result set list.']);
            }
          })
          this.subscriptions.add(init$);
        }
      }).catch(() => {
    });
  }
}
