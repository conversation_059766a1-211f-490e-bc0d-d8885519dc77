<div>
  <div class="profile-details-form-wrap">
    <form name="formMonitoringProfile" id="formMonitoringProfile" [formGroup]="form" (ngSubmit)="onSubmit()">
      <h4 class="mb-0">{{profile?'Profile details':'New profile'}} for {{ isLegalStatusProfile ? 'legal status monitor' : 'new publications monitor'}}</h4>
      <hr />
      <div class="mb-3">
        <label class="label-text form-label">
          Name of your profile
          <sup>*</sup>
        </label>
        <input type="text" formControlName="name" class="form-control profile-name" placeholder="e.g. Chemical (max. 50 characters)" maxlength="50"
          data-intercom-target="profile-name-input" [ngClass]="{'is-invalid':form.get('name').touched && form.get('name').hasError('required')}" required
          (keydown.enter)="closeProductTour()"
        />
          <div *ngIf="form.get('name').touched && form.get('name').hasError('required')" class="invalid-feedback">
            Please enter profile name.
          </div>
      </div>
      <div class="mb-3">
        <label class="label-text form-label">
          Profile description (optional)
        </label>
        <textarea rows="5" class="form-control profile-description" formControlName="description"
          placeholder="e.g. Monitoring chemical industry patents profile"></textarea>
      </div>
      <div class="mb-3" [hidden]="isLegalStatusProfile">
        <label class="label-text form-label">
          What to watch for <sup>*</sup>
        </label>
        <div class="d-flex flex-column align-items-stretch justify-content-start">
          <div class="custom-radio-button">
            <input id="families-scope" class="custom-radio-input" type="radio" name="scope" value="Families" formControlName="scope"
                   (change)="changeProfileScope('Families')" [checked]="scopeFormControlValue === 'Families'">
            <label class="custom-radio-label" for="families-scope">New patent families only</label>
          </div>

          <div class="d-flex align-items-center justify-content-start mb-2">
            <div class="custom-radio-button me-5">
              <input id="publications-scope" class="custom-radio-input" type="radio" name="scope" value="Publications"
                     (change)="changeProfileScope('Publications')" [checked]="scopeFormControlValue !== 'Families'">
              <label class="custom-radio-label" for="publications-scope">Patent publications:</label>
            </div>

            <div ngbDropdown class="flex-fill">
              <div class="cursor-pointer" ngbDropdownToggle id="publicationScopeDropdown">
                <input class="form-control cursor-pointer" type="text"
                       [value]="publicationScopes[scopeFormControlValue !== 'Families' ? scopeFormControlValue: 'Publications']"
                       [readOnly]="scopeFormControlValue !== 'Families' && !profile?.id"
                       [disabled]="scopeFormControlValue === 'Families'"/>
                <div class="dropdown-icon" (click)="onPublicationScopeClicked($event);"></div>
              </div>
              <div ngbDropdownMenu aria-labelledby="publicationScopeDropdown">
                <a *ngFor="let item of publicationScopeValues;" ngbDropdownItem
                   [ngClass]="{'active': scopeFormControlValue === item}" (click)="setScopeFormControlValue(item);">
                  {{ publicationScopes[item] }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <h4 class="mb-0" [hidden]="isLegalStatusProfile">Report delivery</h4>
      <hr [hidden]="isLegalStatusProfile" />
      <div class="mb-3">
        <label  class="label-text form-label">
          Interval of alerts
          <sup>*</sup>
        </label>
        <div class="checkbox-wrap custom-radio" data-intercom-target="interval-of-alerts">
          <label class="radio-button occurrence-weekly d-inline-block me-2" >
            <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="Weekly"/>
            <span>Weekly</span>
          </label>
          <label class="radio-button occurrence-biweekly d-inline-block me-2">
            <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="BiWeekly"/>
            <span>Every two weeks</span>
          </label>
          <label class="radio-button occurrence-monthly d-inline-block me-2">
            <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="Monthly"/>
            <span>Monthly</span>
          </label>
        </div>
      </div>
      <div class="mb-3" *ngIf="!profile?.id || profile?.email_enabled" [hidden]="isLegalStatusProfile">
        <label class="label-text form-label">
          User settings
        </label>
        <div class="checkbox-wrap form-check">
          <label class="checkbox">
            <input type="checkbox" formControlName="send_emails" name="send_emails">
            <span>Receive email alerts</span>
          </label>
        </div>
      </div>
      <div class="mb-3" [hidden]="isLegalStatusProfile">
        <label class="label-text form-label">
          Email attachment
        </label>
        <div class="checkbox-wrap form-check">
          <label *ngFor="let item of methods.controls; let i=index;" [class]="'checkbox  me-2 delivery-'+dMethod[i].name">
            <input type="checkbox" [formControl]="item" (change)="onMethodChange($event, i)">
            <span>{{dMethod[i].name}}</span>
          </label>
        </div>
      </div>

      <div class="mb-3">
        <label class="label-text form-label">
          Automatically share reports with (optional):
        </label>
        <div class="d-flex justify-content-start align-items-stretch">
          <span class="save-collection-name">
            <app-user-avatars *ngIf="(selectedUsers || selectedGroups) && avatarService.hasUsersAndGroups(selectedUsers, selectedGroups)"
                        [users]="selectedUsers" [groups]="selectedGroups" [numberDisplayedUsers]="2" [numberDisplayedGroups]="2"
                        [avatarSize]="30" [distanceBetweenAvatars]="25" [avatarFontSize]="15"
                        avatarsTooltipPrefix="Shared with" (click)="openShareWith($event)" class="cursor-pointer ms-3">
            </app-user-avatars>
          </span>
          <a href="javascript:void(0)" ngbTooltip="Share monitor results with others"
            class="btn btn-primary btn-md" (click)="openShareWith($event)">
            Share <i class="fas fa-share-alt icon-right"></i>
          </a>
        </div>
      </div>
      <div class="mb-3">
        <label class="label-text form-label">
          Automatically save results to:
        </label>
        <div class="d-flex justify-content-start align-items-stretch">
          <span class="save-collection-name justify-content-between">
            <a *ngIf="form.value.collection_id" [routerLink]="['/collections/', form.value.collection_id]" target="_blank" ngbTooltip="Open collection in new tab">{{(collection_name || '') | truncate : 30}}</a>
            <span *ngIf="form.value.collection_id" class="cursor-pointer" ngbTooltip="clear selection" (click)="onClearCollection($event)"><i class="fa-solid fa-circle-xmark"></i></span>
          </span>
          <a href="javascript:void(0)" ngbTooltip="Save monitor results to selected collection"
            class="btn btn-primary btn-md" (click)="onBrowseCollections($event)">
            Browse collections list
          </a>
        </div>
      </div>
      <hr />
      <div class="mb-3 btns text-end">
        <button type="button" class="btn btn-primary btn-md ms-3" (click)="cancel()">CANCEL</button>
        <button type="submit" class="btn btn-primary btn-md ms-3" data-intercom-target="next-button" [disabled]="form.invalid">NEXT</button>
      </div>
    </form>
  </div>
</div>
