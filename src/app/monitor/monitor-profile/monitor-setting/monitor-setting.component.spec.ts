import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MonitorSettingComponent } from './monitor-setting.component';
import { SharedModule } from '@shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { MonitorStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorSettingComponent', () => {
  let component: MonitorSettingComponent;
  let fixture: ComponentFixture<MonitorSettingComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [MonitorSettingComponent],
      imports: [
        SharedModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorSettingComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
