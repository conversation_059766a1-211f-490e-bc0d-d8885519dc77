<div class="monitor-methods-tab">
  <!-- Nav tabs -->
  <div class="container">
    <ul ngbNav #methodTabs="ngbNav" [(activeId)]="methodTab" class="nav-tabs methods-tabs  nav-1" [destroyOnHide]="false"
        (navChange)="onChangeTab($event)"  id="c-and-r" data-intercom-target="monitoring-methods">
      <li [ngbNavItem]="'deeplearning'">
        <a ngbNavLink [ngClass]="{'method-active':profile?.machine_learning_active}">Deep learning</a>
        <ng-template ngbNavContent>
          <app-monitor-machine-learning [storeService]="storeService"
                                        (changeMethod)="onChangeMethod($event)"
                                        (resultClick)="onResultsBtn()"></app-monitor-machine-learning>
        </ng-template>
      </li>
      <li [ngbNavItem]="'semantic'" [hidden]="storeService.isPublicationProfile(profile)">
        <a ngbNavLink [ngClass]="{'method-active':profile?.semantic_query_active}">Semantic</a>
        <ng-template ngbNavContent>
          <app-monitor-semantic *ngIf="!storeService.isPublicationProfile(profile)" [storeService]="storeService"
                                (changeMethod)="onChangeMethod($event)"
                                (resultClick)="onResultsBtn()"></app-monitor-semantic>
        </ng-template>
      </li>
      <li [ngbNavItem]="'boolean'">
        <a ngbNavLink [ngClass]="{'method-active':profile?.boolean_query_active}">Boolean</a>
        <ng-template ngbNavContent>
          <app-monitor-boolean [storeService]="storeService"
                               (changeMethod)="onChangeMethod($event)"
                               (resultClick)="onResultsBtn()">
          </app-monitor-boolean>
        </ng-template>
      </li>
    </ul>
  </div>
  <!-- /Nav tabs -->

  <!-- Tab panes -->
  <div [ngbNavOutlet]="methodTabs" class="c-and-r"></div>
  <!-- /Tab panes -->
</div>
