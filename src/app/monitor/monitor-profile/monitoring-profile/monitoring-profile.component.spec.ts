import { ComponentFix<PERSON>, TestBed, waitForAsync } from '@angular/core/testing';

import { SharedModule } from '@shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { HttpClientModule } from '@angular/common/http';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BasicChartsModule, ClassificationChartsModule, MonitorChartsModule } from '@shared/charts';
import { MonitorStoreService } from '@core/store';
import {
  MonitorBooleanComponent,
  MonitoringProfileComponent,
  MonitorMachineLearningComponent,
  MonitorResultsComponent,
  MonitorSemanticComponent
} from '@monitor/monitor-profile';
import { MonitorService } from '@core/services';
import { CoreModule } from '@core/core.module';
import { provideMatomo } from 'ngx-matomo-client';
import { of } from 'rxjs';

describe('MonitoringProfileComponent', () => {
  let component: MonitoringProfileComponent;
  let fixture: ComponentFixture<MonitoringProfileComponent>;
  let monitorService: MonitorService;
  let monitorStoreService: MonitorStoreService;
  let countCallService = 0;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        MonitoringProfileComponent,
        MonitorMachineLearningComponent,
        MonitorSemanticComponent,
        MonitorBooleanComponent,
        MonitorResultsComponent,
      ],
      imports: [
        CoreModule,
        SharedModule,
        HighchartsChartModule,
        FormsModule,
        ReactiveFormsModule,
        NgbModule,
        BasicChartsModule,
        ClassificationChartsModule,
        MonitorChartsModule,
        NgxSliderModule,
        HttpClientModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        MonitorService, MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));


  beforeEach(() => {
    monitorService = TestBed.inject(MonitorService);
    monitorStoreService = TestBed.inject(MonitorStoreService);

    fixture = TestBed.createComponent(MonitoringProfileComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);

    countCallService = 0;

    spyOn(monitorService, 'createMonitorRun').and.callFake((arg) => {
      countCallService++;
      return of({ data: {monitor_run_id: Math.floor(Math.random() * 6)}})
    });

    spyOn(monitorStoreService, 'setMonitorRunID').and.callThrough();

    component.ngOnInit();
  });

  beforeEach(() => {
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

});
