import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseMonitorStoreService } from '@core/store';
import { BooleanSearchService, MonitorService } from '@core/services';
import { MonitorProfileMachineLearningStatusEnum, MonitorProfile } from '@core/models';
import { Location } from '@angular/common';

@Component({
  selector: 'app-monitoring-profile',
  templateUrl: './monitoring-profile.component.html',
  styleUrls: ['./monitoring-profile.component.scss']
})
export class MonitoringProfileComponent implements OnInit, OnDestroy {
  @Input() private notify = new BehaviorSubject<string>('');
  @Input() storeService: BaseMonitorStoreService;

  @Output() changeTab: EventEmitter<string> = new EventEmitter();

  private subscriptions = new Subscription();

  get profile(): MonitorProfile {
    return this.storeService.monitorProfile;
  }

  methodTab = 'deeplearning';
  private methodTabIDArray = ['boolean', 'semantic', 'deeplearning', 'citation'];

  constructor(
    private router: Router,
    public route: ActivatedRoute,
    private monitorService: MonitorService,
    private booleanSearchService: BooleanSearchService,
    private location: Location,
  ) {
  }

  ngOnInit() {
    this.storeService.setFilters([], false);
    this.booleanSearchService.filterClauses = [];
    if (this.methodTabIDArray.indexOf(this.section) > -1) {
      this.methodTab = this.monitorService.getSection();
    }
    const notify$ = this.notify.subscribe({
      next: v => {
        this.updateSection(this.methodTab);
      }
    });
    this.subscriptions.add(notify$);
  }

  get section(): string {
    return this.monitorService.getSection();
  }

  updateSection(newSection) {
    if (this.profile) {
      this.router.navigateByUrl(this.monitorService.getBaseUrl() + '/' + newSection);
    }
    this.monitorService.setSection(newSection);
  }

  /**
   * getter for Result btn State
   */
  get isResultBtn() {
    return this.profile.boolean_query_active ||
      this.profile.semantic_query_active ||
      this.profile.machine_learning_active ||
      this.storeService.totalMonitorRuns !== 0;
  }

  private switchToResultsTab(shouldSwitch) {
    if (shouldSwitch) {
      this.changeTab.emit('Monitoring-result');
    }
  }

  /**
   * Event listener for Result btn click
   */
  onResultsBtn() {
    this.switchToResultsTab(this.storeService.totalMonitorRuns > 0);

    if (this.storeService.totalMonitorRuns > 0) {
      return;
    }

    if (this.profile.machine_learning_active) {
      let msg;
      if (this.profile.machine_learning_status !== MonitorProfileMachineLearningStatusEnum.READY) {
        msg = 'Deep learning Model has not been requested to trained and DL results will not be available.' +
          '\n\nDo you still want to proceed anyway?';
      }
      if (this.profile.machine_learning_status === MonitorProfileMachineLearningStatusEnum.ERROR) {
        msg = 'Deep learning Model training got error and DL results will not be available.' +
          '\n\nDo you still want to proceed anyway?';
      }
      if (this.storeService.freezeML) {
        msg = 'Deep learning Model is still getting trained and DL results will not be available.' +
          '\n\nDo you still want to proceed anyway?';
      }
      if (msg) {
        const conrimation = confirm(msg);
        if (!conrimation) {
          return;
        }
      }
    }

    if (this.profile.machine_learning_status !== MonitorProfileMachineLearningStatusEnum.READY) {
      return;
    }
    this.switchToResultsTab(true);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  /**
   * onChange($event)
   */
  onChangeTab(event) {
    this.updateSection(event.nextId);
  }

  /**
   * onChangeMethod($event)
   */
  onChangeMethod(method: string) {
    this.methodTab = method;
  }
}
