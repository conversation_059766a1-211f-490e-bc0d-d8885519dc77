<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <div class="flex-fill">
    <app-spinner *ngIf="loading"></app-spinner>
    <div [hidden]="loading">
      <div class="bg-gray">
        <div class="container">
          <nav aria-label="breadcrumb" *ngIf="!loading">
            <ol class="breadcrumb">
              <li class="breadcrumb-item"><a [routerLink]="sharedRuns?.profile_info?.legal_status_active ? '/monitor/legalStatus': '/monitor'" id="monitor-home" class="text-green">Monitor
                  Shared Profile</a></li>
              <li class="breadcrumb-item active" aria-current="page">{{sharedRuns?.profile_info?.name}}</li>
            </ol>
          </nav>
          <!-- Nav tabs -->
          <ul ngbNav #mainTabs="ngbNav" class="nav-tabs main-tabs nav-1">
            <li ngbNavItem>
              <a ngbNavLink>Analyze result</a>
              <ng-template ngbNavContent>
                <div class="container">
                  <div class="mt-3 mb-5" *ngIf="userService.isIpLounge()">
                    <div class="h5 fw-bold">Welcome to your personalized patent monitor!</div>
                    <div class="d-flex justify-content-center align-items-center">
                      <div class="h6 m-0">
                        We have created an automated alert system which reports new patent publications that may be relevant to you based on the patent families that Dennemeyer manages for you.
                      </div>

                      <button class="btn btn-primary btn-lg watch-video-button ms-2" (click)="showTutorialModal()">
                        <i class="fa fa-play icon-left me-4"></i> Watch video to learn more
                      </button>
                    </div>
                  </div>
                  <div class="monitor-run--section" [hidden]="sharedRuns?.shared_runs?.length===0">
                    <table class="table table-hover monitor-run-table">
                      <thead >
                        <tr>
                          <th scope="col" class="text-center">#</th>
                          <th scope="col">Reported period</th>
                          <th scope="col">Monitoring method</th>
                          <th scope="col">Number of results </th>
                          <th scope="col">Last shared</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr [class]="'cursor-pointer monitor-run '+singleRun.status"
                          *ngFor="let singleRun of sharedRuns?.shared_runs;index as indexI"
                          [ngClass]="{'table-active open-sans-bold':singleRun.id==this.storeService.selectedMonitorRun}"
                          (click)="getMonitorResultProfile(singleRun)">
                          <td scope="row" class="text-center open-sans-bold">
                            {{(sharedRuns?.page.page_size*(sharedRuns?.page.current_page-1))+indexI+1}}
                          </td>
                          <td>{{singleRun.name | dateFormat}}</td>
                          <td>
                            <div class="short-char" *ngIf="singleRun.status==='Finished' || singleRun.status==='Error' ">
                              <span class="badge me-1"
                                    [hidden]="sharedRuns?.profile_info?.legal_status_active"
                                    [ngClass]="singleRun.ml_status!=='Finished' ? 'badge-light' : 'badge-primary'"
                                    [ngbTooltip]="userService.isIpLounge() ? monitorLabelTooltip : 'Deep learning search'"
                                    tooltipClass="monitor-label-tooltip">DL</span>
                              <span class="badge me-1"
                                    [hidden]="sharedRuns?.profile_info?.legal_status_active"
                                    [ngClass]="singleRun.semantic_status!=='Finished' ? 'badge-light' : 'badge-primary'"
                                    [ngbTooltip]="userService.isIpLounge() ? monitorLabelTooltip : 'Semantic search'">S</span>
                              <span class="badge me-1"
                                    [hidden]="sharedRuns?.profile_info?.legal_status_active"
                                    [ngClass]="singleRun.boolean_status!=='Finished' ? 'badge-light' : 'badge-primary'"
                                    [ngbTooltip]="userService.isIpLounge() ? monitorLabelTooltip : 'Boolean search'">B</span>
                              <span ngbTooltip="Legal status list search" class="badge me-1"
                                [ngClass]="singleRun.legal_status_status!=='Finished' ? 'badge-light' : 'badge-primary'"
                                [hidden]="!sharedRuns?.profile_info?.legal_status_active" [ngbTooltip]="userService.isIpLounge() ? monitorLabelTooltip : 'Patent List'">L</span>
                            </div>
                            <div class="short-char" *ngIf="singleRun.status==='Pending'">
                              <span ngbTooltip="Pending"><i class="far fa-hourglass"></i></span>
                            </div>
                            <div class="short-char" *ngIf="singleRun.status==='Processing'">
                              <img class="spinner-octimine monitor-table" src="assets/images/octimine_blue_spinner.gif">
                            </div>
                          </td>
                          <td class="monitor-run-result-count">
                            {{sharedRuns?.profile_info?.legal_status_active ? singleRun?.number_of_documents?.LEGAL_STATUS : singleRun?.number_of_documents?.TOTAL}}
                          </td>
                          <td class="monitor-run-result-date">
                            {{singleRun.shared_at | dateFormat: 'ShortDate'}}</td>
                        </tr>
                      </tbody>
                    </table>
                    <div *ngIf="sharedRuns?.page.last_page>1">
                      <app-pagination class="d-flex justify-content-end" [pagination]="sharedRuns?.page"
                        (navigatePage)="navigateMonitorRun($event)"></app-pagination>
                      <br>
                    </div>
                  </div>
                </div>

                <app-patent-list-layout id="monitor-results-container"
                                        [storeService]="storeService"
                                        [documents]="documents"
                                        [isLoading]="loading"
                                        [showDashboardActionBar]="true"
                                        [alwaysShowDocumentsControlBar]="true"
                                        (viewModeChange)="onChangeViewMode($event)">
                  <ng-container alertMessages [ngTemplateOutlet]="noDocumentsMessage"></ng-container>

                  <ng-container documentsControlBar [ngTemplateOutlet]="monitorControlBar"></ng-container>

                  <ng-container documentsTable [ngTemplateOutlet]="monitorList"></ng-container>

                  <ng-container documentsVisual>
                    <app-charts-container [isColumnLayout]="isCombinedMode"
                                          [storeService]="storeService"></app-charts-container>
                  </ng-container>
                </app-patent-list-layout>

                <app-filters-bar [showOperator]="true" [storeService]="storeService" [alwaysBeSticky]="true"
                (clearAll)="clearAllFilters()" (filterRemoved)="removeFilter($event)"></app-filters-bar>

                <a class="support-button" *ngIf="userService.isIpLounge() && !loading && documents?.length > 0" (click)="openSupportDialog()">
                  <div class="w-100 h-100 position-relative">
                    <div class="w-100 h-100" ngbTooltip="Contact us!" placement="start" tooltipClass="support-button-tooltip">
                      <i class="fa fa-question" aria-hidden="true"></i>
                    </div>
                  </div>
                </a>
              </ng-template>
            </li>
          </ul>
          <!-- /Nav tabs -->
        </div>
      </div>
      <div class="page-content ">
        <!-- Profile folders tabs -->
        <div class="prof-folders-wrap monitoringTabs">
          <!-- Tab panes -->
          <div [ngbNavOutlet]="mainTabs"></div>
          <!-- /Tab panes -->
        </div>
        <!-- ./Profile folders tabs -->
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<app-contact-us-banner></app-contact-us-banner>

<ng-template #monitorControlBar>
  <div class="me-control-bar sticky-top" *ngIf="documents?.length > 0">
    <div class="container-fluid d-flex justify-content-between align-items-center">
      <app-patent-control-bar class="d-flex" [searchService]="monitorService"
                              [hasHarmonizeControl]="true" [hasExportControl]="true"
                              [columnsToShow]="columnsToShow"
                              [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                              [exportDisplayOptions]="['csv','xlsx','pdf']" [exportAdditionalParams]="additionalExportParams"
                              [hasTemporaryLinkControl]="true" [saveSearchTextInput]="selectedSnapshot?.name"
                              (sharePatentsEvent)="onSharePatents($event)"
                              [hasSaveToCollectionControl]="true"
                              [taskShowCreationButton]="true"
                              (taskSaved)="onTaskSaved($event)"
                              [storeService]="storeService"
                              [patentListScope]="isPublicationsScope ? patentListScopeEnum.PUBLICATION : patentListScopeEnum.FAMILY">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #monitorFooter>
  <div class="d-flex justify-content-between align-items-center" *ngIf="!loading && documents?.length > 0">
    <div></div>
    <div class="d-flex justify-content-end align-items-center">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                     [pageOptions]="[25,50,100]"></app-page-size>
      <app-pagination [pagination]="documentsPage" (navigatePage)="navigatePage($event)"></app-pagination>
    </div>
  </div>
</ng-template>

<ng-template #monitorList>
  <!-- TABLE -->
  <div class="container-fluid" [ngClass]="{'pe-0': isCombinedMode}">
    <div class="monitor-run-data-box" id="monitor-results">
      <div *ngIf="documents?.length > 0">
        <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages" class="mb-2"></app-alert>
        <div class="d-flex justify-content-between align-items-center" data-intercom-target="Monitoring result">
          <div class="monitor-results-title new-layout-headline">
            <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{storeService.resultSetPagination.total_hits}}
            <span *ngIf="!isPublicationsScope">
              {{ 'patent family' | pluralize: storeService.resultSetPagination.total_hits }}
              ({{storeService.resultSetPagination.total_publications}} {{ 'publication' | pluralize: storeService.resultSetPagination.total_publications }})
            </span>
            <span *ngIf="isPublicationsScope">
              {{ 'patent publication' | pluralize: storeService.resultSetPagination.total_publications }}
            </span>
          </div>

          <div class="d-flex justify-content-end align-items-center">
            <div class="ms-3 text-end tools-bar expand-all">
              <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()">
                {{ patentTable.openedPatent.length === documents.length ? 'Collapse all': 'Expand all'}}
                <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < documents.length"></i>
                <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === documents.length"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="psr-patent-table">
          <app-patent-table #patentTable [patents]="documents" (sort)="onSort($event)"
                            [showPatentSelection]="true" [pagination]="documentsPage" [hasLinksToBooleanSearch]="true"
                            [searchService]="monitorService" pathUrl="/patent" [linkData]="linkData"
                            backButtonTitle="Back to monitor" [monitorLabelTooltip]="userService.isIpLounge() ? monitorLabelTooltip : null"
                            [srcMonitorRunId]="selectedSnapshot?.id" [storeService]="storeService"
                            [showSmartHighlight]="true" [showHighlight]="true" [showRank]="false">
          </app-patent-table>
        </div>

        <ng-container [ngTemplateOutlet]="monitorFooter"></ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #noDocumentsMessage>
  <div *ngIf="!documents?.length && !loading" class="container-fluid my-4">
    <app-alert type="warning" [hideCloseBtn]="true" [message]="getAlertMessage()">
    </app-alert>
  </div>
</ng-template>
