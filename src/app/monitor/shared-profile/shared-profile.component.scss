@use 'layout2021/layout2021' as variables;

.filter-bar-section{
  background-color: variables.$chart-section-background;
}

.watch-video-button {
  width: 300px !important;
  min-width: 300px !important;
}

::ng-deep {
  .monitor-label-tooltip {
    .tooltip-inner {
      max-width: 450px !important;
    }
  }
}
.support-button {
  position: fixed;
  border-radius: 50%;
  text-align: center;
  transition: all ease .2s;
  cursor: pointer;
  z-index: 99999;

  &:hover {
    -webkit-box-shadow: 2px 0px 17px rgba(0, 0, 0, 0.51);
    -moz-box-shadow: 2px 0px 17px rgba(0, 0, 0, 0.51);
    box-shadow: 2px 0px 17px rgba(0, 0, 0, 0.51);
  }

  right: 22.5px;
  bottom: 95px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background-color: #FF6700;
  border: 1px solid #FF6700;
  color: variables.$white;

  &.active {
    background-color: #FF6700;
    color: white;
  }

  &:hover {
    background-color: #FF6700;
    border: 1px solid #FF6700;
    color: variables.$white;
  }

  .support-button-tooltip {
    width: 100px !important;
    left: -105px !important;
  }
}

.expand-all {
  background: transparent;
}
