import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SharedProfileComponent } from './shared-profile.component';
import { SharedModule } from '@shared/shared.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MonitorStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('SharedProfileComponent', () => {
  let component: SharedProfileComponent;
  let fixture: ComponentFixture<SharedProfileComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SharedProfileComponent ],
      imports: [
        SharedModule,
        FormsModule,
        NgbModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([{path: 'monitor', component: SharedProfileComponent}])
      ],
      providers: [
        MonitorStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SharedProfileComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(MonitorStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
