import { Component, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AdvancedFilterService,
  ApplicantsAliasesService, BaseMonitorStoreService,
  ChartsService, MonitorLegalStoreService,
  MonitorService,
  MonitorSharedRuns,
  MonitorStoreService,
  NotificationsService,
  PaginationMetadata,
  Patent,
  PatentListScopeEnum,
  PublicationService,
  SortParams,
  TaskModel,
  TaskService,
  UserService
} from '@core';
import { columnsToShow, selectedColumnsCombinedMode } from './columns';
import { columnsToShow as LsColumnsToShow } from './../monitor-profile/monitor-legal-status-result/columns';
import { catchError, finalize, take } from 'rxjs/operators';
import { of, Subscription } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SupportComponent, TutorialComponent } from '@shared/components';
import { ViewModeTypeEnum } from '@search/patent/types';
import { ActionType } from '@shared/charts/chart-dashboard/types';

declare var $: any;

@Component({
  selector: 'app-shared-profile',
  templateUrl: './shared-profile.component.html',
  styleUrls: ['./shared-profile.component.scss']
})
export class SharedProfileComponent implements OnInit, OnDestroy {
  storeService: BaseMonitorStoreService;
  pageSize = 25;
  additionalExportParams = {};
  sharedRuns: MonitorSharedRuns;
  columnsToShow = [];
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;
  selectedSnapshot;
  savedTaskMessages: string[];
  charts;
  sorting: SortParams = {field: null, order: null} as SortParams;
  monitorLabelTooltip = 'In the full version of Octimine, you can use multiple methods to setup your monitoring profile. ' +
    'Click "Contact us" below to find out more.';

  patentListScopeEnum = PatentListScopeEnum;
  isPublicationsScope = false;

  private startingPage = true;
  private combinedModePosition: number;

  private activePage: number = 1;
  private subscriptions = new Subscription();
  private activeMonitorRunPage = 1;

  constructor(
    public route: ActivatedRoute,
    public router: Router,
    private chartService: ChartsService,
    private notificatonsService: NotificationsService,
    public monitorStoreService: MonitorStoreService,
    public monitorLegalStoreService: MonitorLegalStoreService,
    public monitorService: MonitorService,
    private taskService: TaskService,
    public userService: UserService,
    private advancedFilterService: AdvancedFilterService,
    private modalService: NgbModal,
    private applicantAliasesService: ApplicantsAliasesService,
    private publicationService: PublicationService,
  ) {
  }

  get linkData() {
    return this.monitorService.linkData;
  }

  get loading(): boolean {
    return this.storeService.loading;
  }

  set loading(state: boolean) {
    this.storeService.loading = state;
  }

  get documents(): Patent[] {
    return this.storeService.resultSetDocuments as Patent[];
  }

  get documentsPage(): PaginationMetadata {
    return this.storeService.resultSetPagination;
  }

  get noDocumentMsg(): string {
    if (this.storeService.filters.length > 0) {
      return 'No results available for this interval with applied filter.';
    }
    return 'No results have been generated for this time interval.';
  }

  get viewMode (): ViewModeTypeEnum{
    return this.storeService.patentListViewMode;
  }
  get isCombinedMode(): boolean {
    return this.storeService.isCombinedMode;
  }
  get isListVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }
  get isChartVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get totalSelectedPatents() {
    return this.storeService.selectedPublications.length;
  }

  get runIdQueryParam(): number {
    const val = this.route.snapshot.queryParams.runID;
    return val ? Number(val) : null;
  }

  get profileIdParam(): number {
    const val = this.route.snapshot.params.id;
    const valNumber = Number(val);
    return valNumber && Number.isInteger(valNumber) ? valNumber : null;
  }

  @HostListener('window:scroll', ['$event'])
  onScroll($event: any) {
    const pageTop = $(window).scrollTop();
    const rightSideContainer = $('.monitor-results-content-container .right-side-container');

    if (pageTop > 96) {
      rightSideContainer.addClass('was-sticky');
    } else {
      rightSideContainer.removeClass('was-sticky');
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.storeService.setFilters([], false);
    this.storeService.selectedColumnsToShow = [];
    this.storeService.setSelectedMonitorRun(null);
    this.chartService.resetCharts();
  }

  ngOnInit(): void {
    this.storeService = this.router.url.includes('legalStatus') ? this.monitorLegalStoreService : this.monitorStoreService;
    this.storeService.pageSize = this.pageSize;

    const filters$ = this.storeService.filters$.subscribe({
      next: item => {
        if (this.sharedRuns && !this.loading && !(this.storeService instanceof MonitorLegalStoreService)) {
          this.applyFilter();
        }
      }
    });
    this.subscriptions.add(filters$);

    if (this.storeService.backPatentSearch) {
      this.setFieldsAfterBack();
    } else {
      this.clearStoredValues();
      this.clearStoredResults();
    }

    if (this.profileIdParam > 0) {
      this.loading = true;
      if (!this.storeService.backPatentSearch) {
        window.scrollTo({top: 0, behavior: 'smooth'});
      }
      if (this.runIdQueryParam > 0) {
        const findSharedMonitorRunsPage$ = this.monitorService.findSharedMonitorRunsPage(this.profileIdParam, this.runIdQueryParam, 1)
          .pipe(finalize(() => this.loading = false))
          .subscribe({
            next: (val) => {
              this.activeMonitorRunPage = val;
              this.getProfile();
            }
          });
        this.subscriptions.add(findSharedMonitorRunsPage$);
      } else {
        this.getProfile();
      }

    } else {
      this.goToMonitor();
    }

    this.showTutorial();

    const changeApplicantsEvent$ = this.applicantAliasesService.changeApplicantsEvent.subscribe({
      next: () => {
        this.getDocuments(true);
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);

    const searchHash$ = this.storeService.searchHash$.subscribe({
      next: hash => {
        if (hash) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);

    const chartDashboardAction$ = this.storeService.chartDashboardAction$.subscribe({
      next: msg => {
        this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);

        if (msg.action === ActionType.Add) {
          this.storeService.searchHash = this.storeService.searchHash;
        }
      }
    });
    this.subscriptions.add(chartDashboardAction$);

    this.storeService.patentListViewMode = ViewModeTypeEnum.COMBINED;
  }

  get showCharts(): boolean {
    return this.documents && !this.sharedRuns?.profile_info?.legal_status_active;
  }

  goToMonitor() {
    this.router.navigate(['/monitor']);
  }

  getProfile() {
    this.clearStoredValues();
    this.clearStoredResults();
    const params = {page: this.activeMonitorRunPage, page_size: this.monitorService.MONITOR_RUNS_PAGE_SIZE};
    const loadProfile$ = this.monitorService.getSharedProfile(this.profileIdParam, params).subscribe({
      next: sharedRuns => {
        if(sharedRuns?.profile_info?.legal_status_active){
          this.storeService.patentListViewMode = ViewModeTypeEnum.LIST;
          this.columnsToShow = LsColumnsToShow;
          this.selectedColumnsCombinedMode = [];
        } else {
          this.columnsToShow = columnsToShow;
          this.selectedColumnsCombinedMode = selectedColumnsCombinedMode;
        }
        this.sharedRuns = sharedRuns;
        if (this.runIdQueryParam > 0) {
          this.storeService.setSelectedMonitorRun(this.runIdQueryParam);
          this.selectedSnapshot = this.sharedRuns.shared_runs.find(r => r.id === this.runIdQueryParam);
        } else {
          this.selectedSnapshot = this.sharedRuns.shared_runs[0];
          this.storeService.setSelectedMonitorRun(this.selectedSnapshot.id);
        }
        this.getDocuments(true);
        this.prepareAdditionalExportParams();
        this.markRunAsRead(this.storeService.selectedMonitorRun);
      }, error: (err) => {
        console.log(err);
        this.loading = false;
      }
    });
    this.subscriptions.add(loadProfile$);
  }

  getDocuments(loadCharts: boolean) {
    if (!this.storeService.selectedMonitorRun) {
      return;
    }
    this.storeService.setResultSetDocuments([]);
    this.loading = true;
    const query = this.buildQuery();
    this.isPublicationsScope = false;
    const loadDocs$ = this.monitorService.loadSharedProfileDocuments(this.storeService.selectedMonitorRun, query).subscribe({
      next: response => {
        this.isPublicationsScope = response['profile']['scope'] !== 'Families';
        this.storeService.isPublications = this.isPublicationsScope;

        if (this.isPublicationsScope) {
          response['documents'] = this.publicationService.publicationsToDocuments(response['publications']);
        }

        this.storeService.setResultSetDocuments(response['documents']);
        this.storeService.resultSetPagination = response['page'];
        if (response['search_info'].search_hash !== this.storeService.searchHash) {
          loadCharts = false;
          this.storeService.searchHash = response['search_info'].search_hash;
        }
        this.storeService.search= {params: Object.assign({}, query)};

        if(this.startingPage){
          this.adjustTableColumnToDisplay();
        }
        if (loadCharts) {
          this.getCharts();
        } else {
          this.scrollToResults();
          this.startingPage = false;
          this.loading = false;
        }
      }, error: (err) => {
        this.loading = false;
        this.startingPage = false;
      }
    });
    this.subscriptions.add(loadDocs$);
  }

  /**
   * get chart dataSource form the Microservice
   */
  public getCharts() {
    this.loading = true;
    const payload = {
      charts: this.storeService.getChartActiveNames()
    };
    if (!this.isCombinedMode) {
      this.scrollToLoading();
    }
    let freeTextQuery = this.storeService.getChartFilterQuery();
    if (freeTextQuery) {
      payload['search_filters'] = {free_text_query: freeTextQuery};
    }

    this.chartService.resetCharts();
    const calculate$ = this.chartService.calculate(payload, this.storeService.searchHash)
      .pipe(take(1), catchError((err) => of({charts: []})), finalize(() => { this.startingPage = false; }))
      .subscribe({
        next: ({charts}) => {
          this.charts = charts;
          this.loading = false;
          if (this.isCombinedMode) {
            this.scrollToCombinedModePosition();
          } else {
            this.scrollToResults();
            this.scrollOnBack();
          }
        }, error: (err) => {
          console.log(err);
          this.monitorService.setErrors(['Error while getting Chart data.']);
          this.storeService.loading = false;
        }
      });
    this.subscriptions.add(calculate$);
  }

  /**
   * apply single monitoring run
   * @param snapShot Object of single monitor run
   */
  getMonitorResultProfile(snapShot: Object) {
    if (snapShot['status'] !== 'Finished') {
      return;
    }
    if (snapShot['id']) {
      this.monitorService.updateURLRunID(snapShot['id']);
      this.storeService.setSelectedMonitorRun(snapShot['id']);
      this.selectedSnapshot = snapShot;
      this.prepareAdditionalExportParams();
      this.markRunAsRead(snapShot['id']);
    }
    this.activePage = 1;
    this.storeService.setFilters([], false);
    this.getDocuments(true);
  }

  /**
   * Event listener for monitor run table navigation
   * @param page page number for  monitor run table
   */
  public navigateMonitorRun(page) {
    this.activeMonitorRunPage = page;
    this.getProfile();
  }

  /**
   * onChangePageSize
   *
   * Event listener for page size component
   * @param pageSize patent table size
   */
  public onChangePageSize(pageSize: number) {
    this.pageSize = pageSize;
    this.storeService.pageSize = pageSize;
    this.activePage = 1;
    this.getDocuments(false);
  }

  public onSort(val: SortParams) {
    this.sorting = val;
    this.getDocuments(false);
  }

  onSharePatents(event) {
    this.storeService.elementIdToScrollPage = 'monitor-results';
    this.getDocuments(false);
  }

  /**
   * Event listener for result table navigation
   * @param page page number for result table
   */
  public navigatePage(page: number) {
    this.activePage = page;
    this.getDocuments(false);
  }

  /**
   * reload data on filter application
   */
  applyFilter() {
    this.activePage = 1;
    if (this.isCombinedMode) {
      this.storeScrollCombinedModePosition();
    } else {
      this.storeService.elementIdToScrollPage = 'monitor-charts';
    }
    this.getDocuments(this.showCharts);
  }

  onAdvancedFilter(result: boolean) {
    if (result) {
      this.activePage = 1;
      this.getDocuments(true);
    }
  }

  onTaskSaved(data: { message: string, payload: TaskModel, savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessages = this.taskService.getTaskCreationSuccessMessage(data, 'monitor_run');

      setTimeout(() => {
        this.router.navigate(['/ratings', data.savedTasks[0].id]);
      }, 3000);
    }
  }

  showTutorial() {
    if (this.userService.isIpLounge()) {
      const monitorSharedProfileSettings = this.userService.getUISetting('monitor_shared_profile', {});
      const displayedTutorial = monitorSharedProfileSettings['displayed_tutorial'] || false;

      if (!displayedTutorial) {
        setTimeout(() => {
          this.showTutorialModal();
          this.updateUISettings();
        }, 5000);
      }
    }
  }

  showTutorialModal() {
    const modalRef = this.modalService.open(TutorialComponent, {centered: true, windowClass: 'modal-tutorial'});
    modalRef.componentInstance.title = 'Welcome to your personalized patent monitor!';
    modalRef.componentInstance.description = '<h5>Find out what it\'s all about and how to use it in this short video intro:</h5>';
    modalRef.componentInstance.src = 'https://www.youtube.com/embed/D1mw1qdGjR8';
  }

  private prepareAdditionalExportParams() {
    this.additionalExportParams = {
      title: 'Monitor REPORT',
      subtitle: `For profile "${this.sharedRuns?.profile_info?.name}", based on documents published between ${this.selectedSnapshot?.name}`,
    };
  }

  /**
   * query builder for result table
   */
  private buildQuery() {
    const query = [];
    query['page'] = this.activePage;
    query['page_size'] = this.pageSize;
    query['show_general'] = 1;
    query['show_flags'] = 1;

    if (this.sorting.field) {
      query['sort_by'] = this.sorting.field;
      query['sort_order'] = this.sorting.order;
    }

    const filters = [this.storeService.advancedFilterAppliedQuery];

    this.storeService.filters.forEach((item) => {
      if (item.chart === 'monitor_venn_diagram') {
        query['type'] = item.value.split('∩');
      } else {
        filters.push(item.query);
      }
    });

    const filterQuery = filters.filter((q) => q && q.trim().length > 0)
      .join(` ${this.storeService.filtersOperator} `);

    if (filterQuery) {
      query['filters'] = filterQuery;
    }

    return query;
  }


  private scrollToLoading() {
    if (this.startingPage) return;

    setTimeout(() => {
      if (document.getElementById('monitor-loading')) {
        document.getElementById('monitor-loading')
          .scrollIntoView({behavior: 'smooth', block: 'end', inline: 'nearest'});
      }
    });
  }

  private scrollToResults() {
    if (!this.userService.isIpLounge()) {
      setTimeout(() => {
        if (document.getElementById('monitor-results')) {
          document.getElementById('monitor-results')
            .scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
        }
      });
    }
  }

  private markRunAsRead(runId: number) {
    if (runId) {
      const markAsReadForResource$ = this.notificatonsService.markAsReadForResource(runId, 'MONITOR_RUN').subscribe();
      this.subscriptions.add(markAsReadForResource$);
    }
  }

  private clearStoredValues() {
    this.advancedFilterService.reset();
    this.storeService.resetAdvancedFilter();
    this.storeService.patentTableSort = {field: null, order: null} as SortParams;
  }

  private clearStoredResults() {
    this.storeService.setResultSetDocuments([]);
    this.storeService.resultSetPagination = {} as PaginationMetadata;
  }

  private updateUISettings() {
    const settings = {monitor_shared_profile: {displayed_tutorial: true}};
    const updateUISettings$ = this.userService.updateUISettings(settings).subscribe({
      next: (user) => { },
      error: (error) => { console.error(error); }
    });
    this.subscriptions.add(updateUISettings$);
  }

  private setFieldsAfterBack() {
    if (this.storeService.patentTableSort) {
      this.sorting = {...this.storeService.patentTableSort};
    }
  }
  private storeScrollCombinedModePosition(): void {
    this.combinedModePosition = document.documentElement.scrollTop;
  }
  private scrollToCombinedModePosition(): void {
    setTimeout(() => {
      document.documentElement.scroll(0, this.combinedModePosition);
    }, 100);
  }
  private scrollOnBack() {
    if (this.storeService.scrollTopPage) {
      setTimeout(() => {
        window.scrollTo({
          top: this.storeService.scrollTopPage,
          behavior: 'smooth'
        });
        this.storeService.scrollTopPage = null;
      }, 100);
    }
  }
  onChangeViewMode(mode: ViewModeTypeEnum) {
    if(mode === ViewModeTypeEnum.COMBINED){
      this.adjustTableColumnToDisplay();
    }
  }
  adjustTableColumnToDisplay(){
    if(this.storeService.selectedColumnsToShow && this.storeService.selectedColumnsToShow.length > 3 ){
      let columns = this.storeService.selectedColumnsToShow;
      columns = columns.filter(o => o.property !== 'priority_date');
      columns = columns.filter(o => o.property !== 'applicants');
      if(columns.length < this.storeService.selectedColumnsToShow.length){
        this.storeService.selectedColumnsToShow = columns;
      }
    }
  }

  openSupportDialog() {
    const supportModalRef = this.modalService.open(SupportComponent, {centered: true, windowClass: 'modal-support'});
    supportModalRef.componentInstance.title = 'Contact us!';
    supportModalRef.componentInstance.message = 'I would like to find out more about my automatically generated monitoring profile.';
    supportModalRef.result.then(() => { }, () => { });
  }

  getAlertMessage(): string {
    const hasFilters = this.storeService.getAppliedFiltersQuery()?.length > 0;
    if (hasFilters) {
      return 'No documents match your filters. Try to clear your filters.';
    }

    return 'This monitor run does not have any documents yet.';
  }
  removeFilter(item) {
    if (item.type === "venn-chart") {
      const filters = [...this.storeService.filters];
      const index = filters.findIndex((filter) => filter.chart === item.chart);

      if (index > -1) {
        filters.splice(index, 1);
        this.storeService.setFilters(filters);
      }
    }
  }

  clearAllFilters() {
    this.storeService.resetAdvancedFilter();
    this.storeService.setFilters([]);
    this.advancedFilterService.reset();
  }
}
