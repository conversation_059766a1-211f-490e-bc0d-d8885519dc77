import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  ConfirmationDialogService, MonitorProfile,
  MonitorProfileUserSettings,
  MonitorService,
  MonitorUserSettingsService
} from '@core';
import { catchError, finalize, map, switchMap, tap } from 'rxjs/operators';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, of, Subscription } from 'rxjs';

@Component({
  selector: 'app-subscription',
  templateUrl: './subscription.component.html',
  styleUrls: ['./subscription.component.scss']
})
export class SubscriptionComponent implements OnInit, OnDestroy {

  isLoading: boolean = false;
  isUpdatedSuccessfully: boolean = false;
  profile: MonitorProfile = null;
  isSharedProfile: boolean = false;

  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    public route: ActivatedRoute,
    private monitorService: MonitorService,
    private monitorUserSettingsService: MonitorUserSettingsService,
    private confirmationDialogService: ConfirmationDialogService
  ) {
  }

  get profileId(): number {
    return this.route.snapshot.params.id;
  }

  ngOnInit(): void {
    this.getMonitorProfile();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getSuccessMessage(): string {
    return `Email settings for monitor profile "<b>${this.profile.name}</b>" have been updated successfully.
            You will be navigated to monitor profile page in one seconds.`;
  }

  private getMonitorProfile() {
    this.isLoading = true;
    const getProfile$ = this.monitorService.getProfile(this.profileId)
      .pipe(
        tap((profile: MonitorProfile) => this.profile = profile),
        switchMap((profile: MonitorProfile) => {
          return this.getMonitorUserSettings(profile);
        }),
        catchError((err) => {
          console.error(err);
          if (err.status === 404) {
            return this.getSharedMonitorProfile();
          }
          return of(null);
        })
      ).subscribe();
    this.subscriptions.add(getProfile$);
  }

  private getSharedMonitorProfile(): Observable<MonitorProfile> {
    return this.monitorService.getSharedProfile(this.profileId, {page: 1, page_size: 1})
      .pipe(
        map((data) => {
          return {
            id: data.profile_info.id,
            name: data.profile_info.name,
            description: data.profile_info.description
          } as MonitorProfile;
        }),
        tap((profile: MonitorProfile) => {
          this.profile = profile;
          this.isSharedProfile = true;
        }),
        switchMap((profile: MonitorProfile) => {
          return this.getMonitorUserSettings(profile);
        }),
        catchError((err) => {
          console.error(err);
          if (err.status === 404) {
            this.router.navigate(['error/404']);
          }
          return of(null);
        })
      );
  }

  private getMonitorUserSettings(profile: MonitorProfile): Observable<any> {
    return this.monitorUserSettingsService.getUserSettings(profile.id)
      .pipe(
        tap((settings: MonitorProfileUserSettings) => {
          this.showConfirmationDialog(profile, settings);
        }),
        finalize(() => this.isLoading = false),
        catchError((err) => {
          console.error(err);
          if (err.status === 404) {
            this.router.navigate(['error/404']);
          }
          return of(null);
        })
      );
  }

  private showConfirmationDialog(profile: MonitorProfile, settings: MonitorProfileUserSettings) {
    const title = 'Update email alerts for monitor profile';
    const message = settings.send_emails ?
      `You have been subscribed to receive email alerts for monitor profile "<b>${profile.name}</b>".
        Would you like to disable email settings?` :
      `You have been unsubscribed to receive email alerts for monitor profile "<b>${profile.name}</b>".
        Would you like to enable email settings?`;

    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'md');

    modalRef.then(val => {
      if (val) {
        settings.send_emails = !settings.send_emails;
        this.updateMonitorUserSettings(profile.id, settings);
      } else {
        this.navigateToMonitorProfile(profile.id);
      }
    }, rejected => {
      this.navigateToMonitorProfile(profile.id);
    });
  }

  private updateMonitorUserSettings(profileId: number, settings: MonitorProfileUserSettings) {
    this.isLoading = true;
    const updateUserSettings$ = this.monitorUserSettingsService.updateUserSettings(profileId, settings)
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: () => {
          this.isUpdatedSuccessfully = true;
          setTimeout(() => {
            this.navigateToMonitorProfile(profileId);
          }, 2000);
        }
      });
    this.subscriptions.add(updateUserSettings$);
  }

  private navigateToMonitorProfile(profileId: number) {
    const url = this.isSharedProfile ? '/monitor/shared-profile' : '/monitor/profile';
    this.router.navigate([url, profileId]);
  }
}
