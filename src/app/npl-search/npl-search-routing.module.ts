import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';
import { NplComponent } from './npl/npl.component';

const routes: Routes = [
  {path: '', component: NplComponent, canActivate: [AuthGuard], pathMatch: 'full'}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class NPLSearchRoutingModule {
}
