import { NgModule } from '@angular/core';

import { NPLChartsModule, SharedModule } from '../shared';

import { NplComponent } from './npl/npl.component';
import { NplTableComponent } from './npl/npl-table/npl-table.component';
import { NPLSearchRoutingModule } from './npl-search-routing.module';
import { NplChartsComponent } from './npl/npl-charts/npl-charts.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

@NgModule({
  imports: [
    SharedModule,
    HighchartsChartModule,
    NgxSliderModule,
    NPLChartsModule,
    NPLSearchRoutingModule,
  ],
  declarations: [
    NplComponent,
    NplTableComponent,
    NplChartsComponent
  ],
  exports: [
    NplComponent,
    NplTableComponent,
    NplChartsComponent,
    NPLChartsModule,
  ]
})
export class NPLSearchModule {
}
