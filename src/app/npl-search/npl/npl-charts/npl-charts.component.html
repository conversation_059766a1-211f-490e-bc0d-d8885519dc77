<div class="chart-section tabs-container">
    <div class="container-fluid chart-container" [ngClass]="{'pe-0': isCombinedMode}">
      <div *ngIf="isAnalysisMode" class="d-flex mb-0 justify-content-between align-items-center">
        <a href="javascript:void(0)" ngbTooltip="Grid layout"
           class="item-bar chart-icon chart-icon-grid-2columns border-0 pe-0"
           [ngClass]="{ active: !singleChartColumn }" (click)="setSingleChartColumn(false)">
        </a>
        <a href="javascript:void(0)" ngbTooltip="Single column layout"
           class="item-bar chart-icon chart-icon-grid-1column border-0 pe-0"
           [ngClass]="{ active: singleChartColumn }"
           (click)="setSingleChartColumn(true)">
        </a>
      </div>

      <div *ngIf="isCombinedMode" class="d-flex mb-0 justify-content-start align-items-center">
        <div class="tabs-title section-title-text">Visual analysis</div>
      </div>

      <div class="chart-wrap">
        <div class="row">
          <div [ngClass]="isColumnLayout || singleChartColumn ? 'col-md-12' : 'col-md-6'" class="mb-4">
            <app-npl-technology-time-line [storeService]="nplSearchStoreService"></app-npl-technology-time-line>
          </div>
          <div [ngClass]="isColumnLayout || singleChartColumn ? 'col-md-12' : 'col-md-6'" class="mb-4">
            <app-npl-technological-fields [storeService]="nplSearchStoreService"></app-npl-technological-fields>
          </div>
          <div [ngClass]="isColumnLayout || singleChartColumn ? 'col-md-12' : 'col-md-6'" class="mb-4">
            <app-npl-top-authors [storeService]="nplSearchStoreService"></app-npl-top-authors>
          </div>
          <div [ngClass]="isColumnLayout || singleChartColumn ? 'col-md-12' : 'col-md-6'" class="mb-4">
            <app-npl-citations-references [storeService]="nplSearchStoreService"></app-npl-citations-references>
          </div>
        </div>
      </div>
    </div>
</div>
<app-zoom-chart [showFavoriteOption]="false" [storeService]="nplSearchStoreService"></app-zoom-chart>
