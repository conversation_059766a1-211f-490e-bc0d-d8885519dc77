import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { HighchartsChartModule } from 'highcharts-angular';

import { NplChartsComponent } from './npl-charts.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('NplChartsComponent', () => {
  let component: NplChartsComponent;
  let fixture: ComponentFixture<NplChartsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ NplChartsComponent ],
      imports: [
        HighchartsChartModule,
        NgxSliderModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ], providers: [ provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NplChartsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
