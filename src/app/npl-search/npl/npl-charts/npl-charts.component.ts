import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ChartsService, NPLRequestPayload, NplSearchService, NplSearchStoreService } from '@core';
import { skip, take } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-npl-charts',
  templateUrl: './npl-charts.component.html',
  styleUrls: ['./npl-charts.component.scss']
})
export class NplChartsComponent implements OnInit, OnDestroy {
  @Input() isColumnLayout?: boolean;
  @Input() externalSearchPayload?: NPLRequestPayload;

  get singleChartColumn(): boolean {
    return this.nplSearchStoreService.singleChartColumn;
  }

  get isAnalysisMode(): boolean {
    return this.nplSearchStoreService.isAnalysisMode;
  }

  get isCombinedMode(): boolean {
    return this.nplSearchStoreService.isCombinedMode;
  }

  private subscriptions = new Subscription();

  constructor(
    private service: NplSearchService,
    private chartsService: ChartsService,
    public nplSearchStoreService: NplSearchStoreService
  ) { }

  ngOnInit(): void {
    if(this.externalSearchPayload && Object.keys(this.externalSearchPayload).length > 0){
      this.getCharts(this.externalSearchPayload);
    }
  }


  public getCharts(search) {
    if(this.nplSearchStoreService.isCalculatingCharts){
      return
    }
    this.nplSearchStoreService.isCalculatingCharts = true;
    const payload = {...search, ...this.buildPayload()};
    const calculateCharts$ = this.service.calculateCharts(payload)
      .pipe(take(1))
      .subscribe({
        next: ({charts}) => {
          this.nplSearchStoreService.isCalculatingCharts = false;
          this.chartsService.setCharts(charts);
        },
        error: (err) => {
          console.log(err);
          this.nplSearchStoreService.isCalculatingCharts = false;
          throw err;
        }
      });
    this.subscriptions.add(calculateCharts$);
  }

  private buildPayload() {
    const payload = {
      charts: [
        'npl_top_authors',
        'npl_technological_fields',
        'npl_technology_timeline',
        'npl_citations_references'
      ],
      'parameters': {}
    };

    return payload;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.chartsService.resetCharts();
  }

  setSingleChartColumn(value) {
    this.nplSearchStoreService.singleChartColumn = value;
  }
}
