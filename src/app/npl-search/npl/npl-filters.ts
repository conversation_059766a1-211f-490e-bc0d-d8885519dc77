export const filterFields = {
    author: {filterName: 'author', title: 'Author', type: "search-filter"},
    venue: {filterName: 'venue', title: 'Venue', type: "search-filter"},
    study: {filterName: 'field_of_study', title: 'Field of study', type: "search-filter"},
    minYear: {filterName: 'min_year', title: 'Min Year', type: "search-filter"},
    maxYear: {filterName: 'max_year', title: 'Max Year', type: "search-filter"},
    openAccess: {filterName: 'open_access', title: 'Open Access', type: "search-filter"},
    influential: {filterName: 'influential', title: 'Influential', type: "search-filter"}
}

export function getFieldNameByFilterName(filterName: string): string | undefined {
  return Object.keys(filterFields).find(key => filterFields[key].filterName === filterName);
}
