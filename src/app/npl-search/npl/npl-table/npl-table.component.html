<div class="overflow-auto publication-table results-table-container">
    <table class="table table-condensed publication-table w-100 table-hover mb-0 mt-0">
        <thead class="w-100">
            <tr>
                <th width="30"><span>#</span></th>
                <th><span>Title</span></th>
                <th class=""><span>Publication year</span></th>
                <th width="300"><span>Authors</span></th>
                <th class=""><span>Fields of study</span></th>
                <th width="105"></th>
            </tr>
        </thead>
        <tbody *ngFor="let document of documents  | paginate:
    { itemsPerPage: pagination?.page_size,
      id: idPagination,
      currentPage: pagination?.current_page,
      totalItems: pagination?.total_hits
    }; index as indexi"
          [ngClass]="{'preview-border': expandedRows.indexOf(document?.corpus_id) > -1}">
            <tr (click)="openDetail(document?.corpus_id)">
                <td class="number-col" nowrap="nowrap">{{ document?.rank }}</td>
                <td class="text-break">{{ document?.title }}</td>
                <td class="">{{ document?.year }}</td>
                <td><span class="truncate-author">{{getAuthorsNames(document)}}</span></td>
                <td class="">{{getTechFields(document)}}</td>
                <td>
                  <div class="d-flex align-items-center justify-content-center gap-spacing-sm">
                    <a [href]="document?.url" target="_blank" *ngIf="document?.url"
                      class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square">
                      <span>
                        <i class="fa-regular fa-arrow-up-right-from-square"></i>
                      </span>
                    </a>
                    <a href="javascript:void(0)"
                      class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square"
                      [ngClass]="{'preview-close': expandedRows.indexOf(document?.corpus_id) > -1}">
                      <span *ngIf="expandedRows.indexOf(document?.corpus_id) === -1"><i class="fa-regular fa-chevron-down"></i></span>
                      <span *ngIf="expandedRows.indexOf(document?.corpus_id) > -1"><i class="fa-regular fa-chevron-up"></i></span>
                    </a>
                  </div>
                </td>
            </tr>
            <tr *ngIf="expandedRows.indexOf(document?.corpus_id)>-1">
                <td colspan="7" class="npl-detail">
                    <div class="row">
                        <div class="col-5">
                            <div class="row mb-3">
                                <div class="col-3">
                                    <div class="detail-title">Abstract</div>
                                </div>
                                <div class="col-9">
                                    <div class="detail-content paragraph-2 mb-0 ">
                                        <div [class.expandable]="expandedAbstract.indexOf(document.corpus_id) === -1">{{ document?.abstract ? document?.abstract:'N/A' }}</div>

                                        <a class="text-green open-sans-bold cursor-pointer" (click)="toggleAbstract(document.corpus_id)" *ngIf="document?.abstract?.length > ABSTRACT_LENGTH_THRESHOLD">{{expandedAbstract.indexOf(document.corpus_id)> -1? 'Collapse':'Expand'}}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-7">
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Authors</div>
                                </div>
                                <div class="col-8">
                                    <div class="detail-content author-content paragraph-2 mb-0">
                                        <div [class.expandable]="expandedAuthors.indexOf(document.corpus_id) === -1">
                                        <ng-container *ngFor="let a of document?.authors; let first = first; let last = last;">
                                            <a target="_blank" [ngClass]="first? '': 'ms-2'"
                                                [href]="'https://www.semanticscholar.org/author/'+a.name.split(' ').join('-') + '/' +a.author_id">
                                                {{a.name}}</a>{{last?'':', '}}
                                        </ng-container>
                                        </div>
                                        <a class="text-green open-sans-bold cursor-pointer" (click)="toggleAuthors(document.corpus_id)" *ngIf="document?.authors?.length > AUTHORS_LENGTH_THRESHOLD">{{expandedAuthors.indexOf(document.corpus_id)> -1? 'Collapse':'Expand'}}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Citation count</div>
                                </div>
                                <div class="col-8">
                                    <div class="detail-content paragraph-2 mb-0">
                                        {{ document.citation_count?document.citation_count:'N/A' }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Reference count</div>
                                </div>
                                <div class="col-8">
                                    <div class="detail-content paragraph-2 mb-0">
                                        {{ document.reference_count?document.reference_count:'N/A' }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Influential citation count</div>
                                </div>
                                <div class="col-8">
                                    <div class="detail-content paragraph-2 mb-0">
                                        {{ document.influential_citation_count?document.influential_citation_count:'N/A' }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Has open access</div>
                                </div>
                                <div class="col-8">
                                    <div class="detail-content mb-0">{{ document.is_open_access?'Yes':'NO' }}
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <div class="detail-title">Additional attributes</div>
                                </div>
                                <div class="col-8">
                                  <ng-container *ngIf="!document?.external_ids?.length">N/A</ng-container>
                                  <table class="paragraph-2 mb-0" *ngIf="document?.external_ids?.length">
                                    <ng-container *ngFor="let item of document?.external_ids">
                                      <tr *ngIf="item.source_id">
                                        <td class="p-0 text-right pe-1">{{ item.source }} : </td>
                                        <td class="p-0 open-sans-bold">
                                          <ng-container *ngIf="item.source_id">
                                            <ng-container [ngSwitch]="item.source">
                                              <a *ngSwitchCase="'DOI'" [href]="'https://doi.org/' + item.source_id"
                                                 class="text-green" target="_blank">
                                                {{ item.source_id }}
                                              </a>
                                              <ng-container *ngSwitchDefault>{{ item.source_id }}</ng-container>
                                            </ng-container>
                                          </ng-container>
                                        </td>
                                      </tr>
                                    </ng-container>
                                  </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
