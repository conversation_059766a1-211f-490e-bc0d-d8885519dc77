@import 'scss/layout2021/variables';
@import 'scss/components/publication-table';

.detail-title {
  color: $brand-dark;
  font-size: 18px;
  font-family: $font-open-sans-bold;
  text-align: right;
}

.detail-content {
  width: 100%;
  color: $color-text-03;

  &.author-content {
    word-break: break-word;
    width: 100%;
    color: $light-green-color;

    a {
      color: $light-green-color;
    }
  }
  .expandable {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 10; /* number of lines to show */
    -webkit-box-orient: vertical;
 }
}

.truncate-author {
  display: -webkit-box;
  width: 100%;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3; /* number of lines to show */
  -webkit-box-orient: vertical;
  word-wrap: break-word;
}