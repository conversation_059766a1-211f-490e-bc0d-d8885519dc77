import { Component, Input, OnDestroy } from '@angular/core';
import { NPL, NplSearchService, PaginationMetadata } from '@core';

@Component({
  selector: 'app-npl-table',
  templateUrl: './npl-table.component.html',
  styleUrls: ['./npl-table.component.scss']
})
export class NplTableComponent implements  OnDestroy  {
  @Input() documents: NPL[];
  @Input() pagination: PaginationMetadata;

  @Input() idPagination: string;

  expandedAbstract: Array<number> = [];
  expandedAuthors: <AUTHORS>
  

  ABSTRACT_LENGTH_THRESHOLD = 600;
  AUTHORS_LENGTH_THRESHOLD = 35;

  constructor(public service: NplSearchService) { }
  ngOnDestroy(): void {
    this.expandedRows.length = 0;
  }
  get expandedRows() :number[]{
    return this.service.expandedRows;
  }
  getAuthorsNames(doc){
    const authors = doc?.authors?.map(a =>{ return a.name});
    return authors ? authors.join(', ') : 'N/A';
  }
  getTechFields(doc){
    return doc?.fields_of_study ? doc?.fields_of_study.map(f => f.category).join(', ') : 'N/A';
  }
  openDetail(corpus_id: number){
    const elIndex = this.expandedRows.indexOf(corpus_id);
    if (elIndex === -1) {
      this.expandedRows.push(corpus_id);
    } else {
      this.expandedRows.splice(elIndex, 1);
    }
  }

  toggleAbstract(corpus_id: number){
    const elIndex = this.expandedAbstract.indexOf(corpus_id);
    if (elIndex === -1) {
      this.expandedAbstract.push(corpus_id);
    } else {
      this.expandedAbstract.splice(elIndex, 1);
    }
  }

  toggleAuthors(corpus_id: number){
    const elIndex = this.expandedAuthors.indexOf(corpus_id);
    if (elIndex === -1) {
      this.expandedAuthors.push(corpus_id);
    } else {
      this.expandedAuthors.splice(elIndex, 1);
    }
  }

  getYear(date: string): string {
    if (!date) return ''

    return date.substring(0, 4);
  }
}
