<div class="d-flex flex-column justify-content-start min-vh-100">
  <div>
    <app-header></app-header>
    <app-search-menu searchTab="npl"></app-search-menu>
  </div>

  <div class="flex-fill">
    <div class="page-content container patent-search-form-container pb-4" [@fadeIn] >
      <app-alert type="warning" [message]="messageCollaboratorQuotas" [headline]="'Collaborator’s user rights'" version="figma" *ngIf="messageCollaboratorQuotas"></app-alert>

      <div class="d-flex justify-content-between align-items-center">
        <app-search-title headline="Search by patent number or text">
          <app-tooltip id='npl_search_info'
                      [tooltipTitle]='tooltipTitle' [tooltipText]='tooltipText'
                      tooltipPosition="left" tooltipIconSize="sm"></app-tooltip>
        </app-search-title>
        <button class="btn btn-ghost btn-filters" (click)="openFilters()" [ngClass]="{'has-errors': isFiltersFormGroupInvalid}">
          <div *ngIf="filtersNumber > 0 else iconFilter" class="number-filters me-2">{{filtersNumber}}</div>
          <ng-template #iconFilter>
            <i class="fa-light fa-filter me-2"></i>
          </ng-template>
          Filters
        </button>
      </div>
      <app-alert type="danger" [message]="messageAlert" *ngIf="messageAlert"></app-alert>

      <div id="search-form">
        <div class="mb-5 text-end">
            <textarea #searchText id="searchText" data-intercom-target="Keywords" [(ngModel)]="search_term"
            class="form-control nplsf-textarea" rows="3"
            placeholder="Please enter a patent number (e.g. EP2049363, US8697359 or WO2018158104) or any English text (e.g. the abstract of a patent, a scientific article or a product description)."
            [readonly]="userService.isExternalUser()" (keydown.control.enter)="onSubmit()">
            </textarea>
            <a href="https://www.semanticscholar.org/" target="_blank">powered by Semantic Scholar</a>
        </div>

        <div class="m-0 d-flex justify-content-end align-items-center search-hint-container">

          <button [disabled]="!search_term.trim() || searchStarting" (click)="onRefresh()" *ngIf="userService.isNotExternalUser()"
                  class="btn btn-primary-outline btn-sm btn-icon me-3">
            <i class="fa fa-times"></i>
          </button>

          <button id="search-button" type="button" [disabled]="!search_term.trim() || searchStarting || isCollaboratorQuotasExceeded" (click)="onSubmit()" class="btn btn-secondary btn-xl" *ngIf="userService.isNotExternalUser()">
            <i class="fa fa-search icon-left"></i>Search
          </button>
        </div>

        <div class="mt-2 d-flex justify-content-end search-hint" *ngIf="userService.isNotExternalUser()">
          You can also press&nbsp;<span class="fw-bold">Ctrl</span>&nbsp;+&nbsp;<span class="fw-bold">Enter</span>&nbsp;to begin the search.
        </div>
      </div>
    </div>

    <app-spinner *ngIf="searchStarting"></app-spinner>

    <div *ngIf="!searchStarting" class="pt-4">
      <app-patent-list-layout [storeService]="nplSearchStoreService"
                              [documents]="documents"
                              [isLoading]="searchStarting"
                              [showDashboardActionBar]="false"
                              [showMainTabs]="true"
                              (viewModeChange)="onChangeViewMode($event)">
        <ng-container documentsTable [ngTemplateOutlet]="nplList"></ng-container>

        <ng-container documentsVisual *ngIf="documents?.length>0">
          <app-npl-charts id="charts" [externalSearchPayload]="chartPayload" [isColumnLayout]="true"></app-npl-charts>
        </ng-container>

      </app-patent-list-layout>
      <app-filters-bar [alwaysBeSticky]="true" [storeService]="nplSearchStoreService" (clearAll)="clearAllFilters(); search()"></app-filters-bar>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #nplFooter>
  <div class="d-flex justify-content-between align-items-center psr-pagination-controls" *ngIf="documents?.length>0">
    <div></div>
    <div class="d-flex justify-content-end align-items-center">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" class="me-4"></app-page-size>
      <app-pagination id="npl-search-pagination" [pagination]="pagination" (navigatePage)="onPageChange($event)"></app-pagination>
    </div>
  </div>
</ng-template>

<ng-template #nplList>
  <div id="sectionList" [@fadeIn] *ngIf="documents?.length>0">
    <div class="page-content mb-4 container-fluid" [ngClass]="{'pe-0': isCombinedMode}" id="search-results-container" >
      <div class="search-results-container" [hidden]="nplSearchStoreService.state.total_hits === 0" [@fadeInOut]>

        <div class="d-flex justify-content-between align-items-center psr-title-bar" data-intercom-target="Title">
          <div class="new-layout-headline">[{{pagination?.total_hits}}] Most similar scientific articles</div>
          <div class="d-flex justify-content-between align-items-center">
            <div class="tools-bar expand-all">
              <a href="javascript:void(0)" class="item-bar" (click)="toggleExpandAllRows()">
                {{ service.expandedRows.length > 0 ? 'Collapse all': 'Expand all'}}
                <i class="fas fa-angle-double-down" *ngIf="service.expandedRows.length === 0"></i>
                <i class="fas fa-angle-double-up" *ngIf="service.expandedRows.length > 0"></i>
              </a>
            </div>
          </div>
        </div>

        <div class="psr-patent-table" id="result-table">
          <app-npl-table [documents]="documents" [pagination]="pagination" idPagination="npl-search-pagination">
          </app-npl-table>
        </div>

        <ng-container [ngTemplateOutlet]="nplFooter"></ng-container>
      </div>
    </div>
  </div>
</ng-template>
