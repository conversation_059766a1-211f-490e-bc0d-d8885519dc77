@import 'scss/layout2021/variables';

.patent-search-form-container .search-hint {
    color: #698A95;
    font-size: 12px;
}

.expand-all {
  background: transparent;
  margin-top: 10px;
}

#search-form {
  a {
    color: #698A95;
  }
}

.btn-ghost {
  font-family: $font-open-sans-regular;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.25rem;
  letter-spacing: 0em;
  border-radius: 20px;
  padding: 8px 15px 8px 15px;
  color: #00A085;
  border: 1px solid transparent;

  &.btn-boost {
    border-color: #00A085;
    &.active {
      background-color: $brand-green !important;
      color: $color-text-01;
    }
  }
  &.btn-filters {
    color: $color-text-03;
    &:hover {
      color: $brand-green;
    }
    &.has-errors {
      .number-filters {
        background: $invalid-color;
        color: $color-text-01;
      }
    }
  }
}

.number-filters {
  border-radius: 20px;
  background: rgba(0, 160, 131, 0.2);
  font-size: 12px;
  font-weight: 400;
  line-height: 21px;
  width: 20px;
  height: 20px;
  float: left;
}