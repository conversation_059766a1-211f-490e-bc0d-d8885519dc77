import { Component, Input, ChangeDetectorRef, OnDestroy, OnInit, AfterViewInit } from '@angular/core';
import {
  AdvancedFilterService,
  extractPublications,
  MatomoService,
  NplSearchService,
  NplSearchStoreService,
  SearchHistoryService,
  PaginationMetadata,
  UserService
} from '@core';
import { Location } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { finalize, skip, filter } from 'rxjs/operators';
import { tooltip } from './tooltip';
import { ViewModeTypeEnum } from '@search/patent/types';
import { Subscription } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NplFiltersDialogComponent } from '@shared/components/npl-filters-dialog/npl-filters-dialog.component';
import { filterFields, getFieldNameByFilterName } from './npl-filters';

@Component({
  selector: 'app-npl',
  templateUrl: './npl.component.html',
  styleUrls: ['./npl.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
    ]),
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ])
  ]
})
export class NplComponent implements OnInit, OnDestroy, AfterViewInit {

  @Input() section: 'ALL'|'INPUT'|'CHARTS'|'LIST' = 'ALL';

  pageSize = 50;
  search_term: string = '';
  messageAlert: string;
  pagination: PaginationMetadata;

  tooltipTitle = tooltip.title;
  tooltipText = tooltip.text;

  defaultViewMode: ViewModeTypeEnum = ViewModeTypeEnum.COMBINED;

  isFiltersFormGroupInvalid = false;
  filters = [];
  chartPayload = {};

  private subscriptions = new Subscription();
  private canSaveSearchHistory: boolean = true;

  constructor(
    private route: ActivatedRoute,
    public service: NplSearchService,
    public userService: UserService,
    public nplSearchStoreService: NplSearchStoreService,
    private location: Location,
    private searchHistoryService: SearchHistoryService,
    private advancedFilterService: AdvancedFilterService,
    private matomoService: MatomoService,
    private modalService: NgbModal,
    private changeDetectorRef: ChangeDetectorRef,
  ) {
  }

  get documents(){
    return this.service.getDocuments();
  }

  get searchStarting(): boolean {
    return this.nplSearchStoreService.searching;
  }

  get isCombinedMode(): boolean {
    return this.nplSearchStoreService.isCombinedMode;
  }

  get messageCollaboratorQuotas(): string {
    return this.userService.messageCollaboratorQuotas;
  }

  get isCollaboratorQuotasExceeded(): boolean {
    return this.userService.isCollaboratorQuotasExceeded;
  }

  get filtersNumber(): number {
    return this.filters?.length;
  }

  ngOnInit() {
    this.getUserStatistics();
    this.nplSearchStoreService.state.total_hits = 0;
    this.clearAdvancedFilter();
    this.nplSearchStoreService.singleChartColumn = true;
    this.clearAllFilters();

    const filters$ = this.nplSearchStoreService.filters$.pipe(skip(1)).subscribe({
      next: () => {
        if (!this.nplSearchStoreService.searching) {
          this.search();
        }
      }
    });
    this.subscriptions.add(filters$);

    const filterRemoved$ = this.nplSearchStoreService.filterRemoved$.pipe(skip(1)).subscribe({
      next: item => {
        if (item['type'] === 'search-filter') {
          this.filters = this.filters.filter(filter => filter.filterName !== item['filterName']);
          this.search();
        }
      }
    });
    this.subscriptions.add(filterRemoved$);

    this.loadSavedFilters();
  }


  ngAfterViewInit(): void {
    this.parseParams();
    this.changeDetectorRef.detectChanges();
  }

  onRefresh() {
    this.search_term = '';
    this.service.resetDocuments();
  }

  onSubmit() {
    if (this.isCollaboratorQuotasExceeded) {
      return
    }

    if (this.search_term.trim()) {
      this.nplSearchStoreService.searching = true;
      this.refreshSearch();
      this.search();
      this.matomoService.nplSearchButton();
    }
  }

  onChangeViewMode(mode: ViewModeTypeEnum): void {
    this.nplSearchStoreService.patentListViewMode = mode;

    if (this.isCombinedMode) {
      this.nplSearchStoreService.singleChartColumn = true;
    }
  }

  private canLoadSavedSearch(): boolean {
    return this.route.snapshot.queryParams['search'] && this.route.snapshot.queryParams['search'] > 0;
  }

  private parseParams(): void {
    if (this.canLoadSavedSearch()) {
      if(!this.nplSearchStoreService.savedSearch){
        const searchHistoryService$ = this.searchHistoryService.get(this.route.snapshot.queryParams['search']).subscribe({
            next: (data) => {
              this.nplSearchStoreService.savedSearch = data;
              this.searchBySavedSearch();
            }
          });
        this.subscriptions.add(searchHistoryService$);
      } else {
        this.searchBySavedSearch();
      }
    }
  }

  private searchBySavedSearch(){
    this.searchHistoryService.restoreSavedValues(this.nplSearchStoreService.savedSearch);
    this.setFieldsSavedSearch(this.nplSearchStoreService.savedSearch);
    this.canSaveSearchHistory = false;
    this.search();

    setTimeout(() => { this.location.replaceState('/npl-search'); }, 500);
    this.nplSearchStoreService.savedSearch = null;
  }


  private setFieldsSavedSearch(savedSearch): void {
    this.search_term = savedSearch.search_input || "";
    if (savedSearch.patent_numbers?.length) {
      this.search_term += " " + savedSearch.patent_numbers.join(" ")
    }

    Object.keys(savedSearch.search_filters || {}).forEach(key => {
      const fieldName = getFieldNameByFilterName(key)
      if (filterFields[fieldName]) {
        this.filters.push({...filterFields[fieldName], value: savedSearch.search_filters[key] })
      }
    });
  }

  search() {
    this.nplSearchStoreService.searching = true;
    const payload = this.buildPayload();
    const params = this.buildParams();
    const search$ = this.service.search(payload, params)
      .pipe(
        finalize(() => {
          this.nplSearchStoreService.searching = false;
        })
      )
      .subscribe({next: ({data}) => {
        this.getUserStatistics();
        this.pagination = data.page;
        if ((this.pagination?.total_hits || 0) === 0) {
          this.messageAlert = "Your search returned no results";
        }
        this.nplSearchStoreService.searchInput = this.search_term;
        this.nplSearchStoreService.state.total_hits = this.documents.length;
        setTimeout(() => {
          this.scrollToResults('charts');
        }, 50);
      }, error: ({error}) => {
        console.error(error);
        if (error.message) {
          this.messageAlert = error.message;
        } else {
          this.messageAlert = "Your search returned an error";
        }
      }});
    this.subscriptions.add(search$);
  }

  buildPayload() {
    const payload = {};
    const {remainingText, publications} = extractPublications(this.search_term);

    if (remainingText) {
      payload['search_input'] = remainingText;
    }

    if (publications.length > 0) {
      payload['patent_numbers'] = publications;
    }

    const filters = this.buildFilters();
    if (filters) {
      payload['search_filters'] = filters;
    }
    this.chartPayload = payload;
    return payload;
  }

  private buildFilters() {
    if (this.filters.length === 0 && this.nplSearchStoreService.filters.length === 0) {
        return null;
    }

    const searchFilters = this.buildSearchFilters();
    const filterCharts = this.getFilterCharts();
    this.applyChartFilters(searchFilters, filterCharts);

    const uniqueFilters = this.getUniqueFilters(filterCharts);
    const allFilters = [...uniqueFilters, ...filterCharts];

    this.nplSearchStoreService.setFilters(allFilters);
    return searchFilters;
  }

  private buildSearchFilters() {
    const searchFilters = {};
    this.filters.forEach((filter) => {
      if (filter['value']) {
        const value = this.shouldTitleCase(filter['filterName']) ? this.toTitleCase(filter['value']) : filter['value'];
        searchFilters[filter['filterName']] = value;
      }
    });
    return searchFilters;
  }

  private shouldTitleCase(filterName: string): boolean {
    return ['author', 'venue', 'field_of_study'].includes(filterName);
  }

  private getFilterCharts() {
    return this.nplSearchStoreService.filters.filter(filter => filter.type === 'chart');
  }

  private applyChartFilters(searchFilters: any, filterCharts: any[]) {
    filterCharts.forEach(filter => {
      switch (filter.chart) {
        case 'npl_technology_timeline':
          searchFilters['max_year'] = filter.value;
          searchFilters['min_year'] = filter.value;
          break;
        case 'npl_technological_fields':
          searchFilters['field_of_study'] = filter.value;
          break;
        case 'npl_top_authors':
          searchFilters['author'] = filter.value;
          break;
        case 'npl_citations_references':
          let range = [];
          if (filter.value.includes('>100')) {
            range.push('100');
            range.push('999');
          } else {
            range = filter.value.split('=')[1].split('-');
            if (range[0] === '0') {
              range.push('0');
            }
          }
          const field = filter.value.includes('CITATION') ? 'citation_count' : 'reference_count';
          searchFilters[`min_${field}`] = range[0];
          searchFilters[`max_${field}`] = range[1];
          break;
      }
    });
  }

  private getUniqueFilters(filterCharts: any[]) {
    const filterNames = new Set(filterCharts.map(filter => filter.chart));
    return this.filters.filter(filter => {
      if ((filter.filterName === 'min_year' || filter.filterName === 'max_year') && filterNames.has('npl_technology_timeline')) {
        return false;
      }
      if (filter.filterName === 'field_of_study' && filterNames.has('npl_technological_fields')) {
        return false;
      }
      if (filter.filterName === 'author' && filterNames.has('npl_top_authors')) {
        return false;
      }
      return true;
    });
  }

  private buildParams() {
    const params = {};
    params['page'] = this.pagination?.current_page || 1;
    params['page_size'] = this.pageSize;
    params['save_history'] = this.canSaveSearchHistory ? 1 : 0;
    return params;
  }

  onPageChange(page: number) {
    this.pagination.current_page = page;
    this.search();
  }

  onChangePageSize(page_size: number) {
    this.pageSize = page_size;
    this.search();
  }

  private refreshSearch() {
    this.service.resetDocuments();
    this.pagination = null;
    this.messageAlert = null;
    this.canSaveSearchHistory = true;
  }

  private scrollToResults(elementId = '') {
    if (!elementId) {
      elementId = 'result-table';
      if (!document.getElementById(elementId)) {
        elementId = 'searchText';
      }
    }
    if (document.getElementById(elementId)) {
      document.getElementById(elementId).scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
    }
  }

  private clearAdvancedFilter() {
    this.advancedFilterService.reset();
    this.nplSearchStoreService.resetAdvancedFilter();
  }

  toggleExpandAllRows(){
    if(this.service.expandedRows.length> 0){
      this.service.expandedRows.length = 0;
    } else {
      this.service.expandAllRows();
    }
  }
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.service.resetService();
  }

  private getUserStatistics() {
    if (!this.userService.isCollaboratorUser()) {
      return;
    }

    const userStatistics$ = this.userService.getStatistics().subscribe();
    this.subscriptions.add(userStatistics$);
  }

  openFilters() {
    if (this.userService.isFreeUser()) return;
    const modalOptions = {
      scrollable: true,
      windowClass: 'right-side-modal',
    };

    const filtersModal = this.modalService.open(NplFiltersDialogComponent, modalOptions);
    filtersModal.componentInstance.filters = this.filters;
    filtersModal.result.then((result) => {
      this.filters = result;
    }, (reason) => {
      console.log(reason);
    });
  }

  clearAllFilters() {
    this.filters = [];
    this.nplSearchStoreService.setFilters([]);
  }

  private toTitleCase(str: string): string {
    return str.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

  private loadSavedFilters() {
    if (!this.userService.getUISetting('npl_search_filters', false)) {
      return;
    }
    const searchFiltersSaved = this.userService.getUISetting('npl_search_filters', {});
    Object.keys(searchFiltersSaved).forEach(key => {
      if (searchFiltersSaved[key]) {
        this.filters.push({...filterFields[key], value: searchFiltersSaved[key] })
      }
    });
  }
}
