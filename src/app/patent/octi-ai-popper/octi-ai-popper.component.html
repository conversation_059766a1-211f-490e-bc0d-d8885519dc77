<div class="input-container">
  <div class="octi-icon-container chat-headline">
    <i class="octi-icon-v3"></i>
  </div>
  <div class="input-field content-body-medium">
    <div [contentEditable]="true" class="input-field-text input-field-text-icon-right " #octiEditor
      [attr.data-placeholder]="'Ask anything about this patent...'"
      (keydown.enter)="onChatSubmit(); $event.preventDefault()">
    </div>
    <i class="fa-regular fa-paper-plane chat-submit-btn button-main-primary button-square button-small" (click)="onChatSubmit()"></i>
  </div>
</div>

<div class="quick-actions">
  <div class="content-heading-h6 content-color-tertiary p-y-spacing-x-s p-x-spacing-sm">Quick actions</div>
  <div class="actions-list">
    <button class="action-item" (click)="onRefineSearch()" *ngIf="isSemanticSearch">
      <i class="fa-regular fa-magnifying-glass-waveform"></i>
      <span>Refine current search with this section</span>
    </button>
    <button class="action-item" (click)="onExplainSection()">
      <i class="fa-regular fa-book-sparkles"></i>
      <span>Explain this part</span>
    </button>
    <button class="action-item" (click)="onSummarize()">
      <i class="fa-regular fa-sparkles"></i>
      <span>Summarize this patent</span>
    </button>
  </div>
</div>
