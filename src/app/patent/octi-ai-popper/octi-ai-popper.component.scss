@import 'scss/figma2023/variables';

.input-container {
  width: 392px;
  display: flex;
  align-items: center;
  padding: $spacing-system-spacing-md $spacing-system-spacing-sm;
  border-bottom: 1px solid $colours-border-minimal;
  gap: $spacing-system-spacing-xx-s;

  .octi-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    .octi-icon-v3 {
      width: 40px;
      height: 40px;
      border: 1px solid $colours-content-content-active;
      border-radius: 200px;
      padding: 4px;
    }
  }

  .input-field {
    position: relative;
    width: 100%;

    .input-field-text {
      width: 100%;
      min-height: 24px;
      outline: none;
      word-break: break-all;

      &:empty:not(:focus) {
        background: rgba(0, 0, 0, 0.02);
        border-radius: 8px;
        padding: 8px 12px;

        &:before {
          content: attr(data-placeholder);
          color: rgba(0, 0, 0, 0.35);
          pointer-events: none;
          font-size: 14px;
          font-weight: 400;
        }
      }

      &:empty ~ .chat-submit-btn {
        opacity: 0.35;
      }

      &:not(:empty) ~ .chat-submit-btn {
        opacity: 1;
      }
    }
  }

  .chat-submit-btn {
    bottom: .4rem;
    right: .5rem;
    position: absolute;
    transition: opacity 0.2s ease;
  }
}

.quick-actions {
  padding: $spacing-system-spacing-sm;

  .actions-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-system-spacing-x-s;

    .action-item {
      display: flex;
      align-items: center;
      gap: $spacing-system-spacing-xx-s;
      padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
      border: none;
      background: none;
      border-radius: $radius-sm;
      cursor: pointer;
      transition: background-color $transition-delay-time;

      &:hover {
        background-color: $colours-buttons-main-tertiary-grey-bg-hover;
      }

      i {
        font-size: $font-size-sm;
        color: $colours-content-content-tertiary;
      }

      span {
        font-size: $font-size-sm;
        color: $colours-content-content-primary;
      }
    }
  }
}
