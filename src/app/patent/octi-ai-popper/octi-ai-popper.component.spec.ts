import { ComponentFixture, TestBed } from '@angular/core/testing';

import { OctiAIPopperComponent } from './octi-ai-popper.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('OctiAiPopperComponent', () => {
  let component: OctiAIPopperComponent;
  let fixture: ComponentFixture<OctiAIPopperComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [OctiAIPopperComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(OctiAIPopperComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
