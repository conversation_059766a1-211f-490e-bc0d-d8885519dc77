import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, HostListener } from '@angular/core';
import { OctiQuestion, PromptType, SemanticSearchStoreService, TextHighlightService } from '@core';
import { PopperComponent } from '@shared/components/popper/popper.component';

@Component({
  selector: 'app-octi-ai-popper',
  templateUrl: './octi-ai-popper.component.html',
  styleUrl: './octi-ai-popper.component.scss'
})
export class OctiAIPopperComponent implements AfterViewInit {
  @Input() mouseUpElementId: string;
  @Input() octiAIPopper: PopperComponent;
  @Output() popperClosed = new EventEmitter<void>();
  @Output() octiSubmit = new EventEmitter<OctiQuestion>();

  @ViewChild('octiEditor') octiEditor: ElementRef;

  constructor(
    private textHighlightService: TextHighlightService,
    public semanticSearchStoreService: SemanticSearchStoreService,
    private elementRef: ElementRef
  ) {}

  @HostListener('document:mousedown', ['$event'])
  onClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const isOctiAIButton = target.closest('.button-octi-ai');
    const isPopperElement = this.elementRef.nativeElement.contains(event.target);

    if ((!isOctiAIButton && !isPopperElement && this.octiAIPopper.isOpen)) {
      this.popperClosed.emit();
    }
  }

  get isSemanticSearch(){
    return this.semanticSearchStoreService.patentNumbers?.length > 0 || this.semanticSearchStoreService.searchInput?.length > 0;
  }

  ngAfterViewInit() {
    this.octiEditor.nativeElement.addEventListener('paste', (event: ClipboardEvent) => {
      event.preventDefault();
      this.octiEditor.nativeElement.innerHTML += event.clipboardData.getData('text/plain');
      const range = document.createRange();
      range.selectNodeContents(this.octiEditor.nativeElement);
      range.collapse(false);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    });

    this.octiEditor.nativeElement.addEventListener('input', () => {
      const content = this.octiEditor.nativeElement.textContent.trim();
      if (!content) {
        this.octiEditor.nativeElement.innerHTML = '';
      }
    });
  }

  onRefineSearch(){
    let searchText = this.textHighlightService.getSelectedText(this.mouseUpElementId, ' ');
    if (this.isSemanticSearch) {
      searchText = this.semanticSearchStoreService.searchInput + '\n' + searchText + '\n' + this.semanticSearchStoreService.patentNumbers;
    }
    window.open(`search/patent?publication=${encodeURIComponent(searchText)}`, '_blank');
    this.popperClosed.emit();
  }

  onExplainSection() {
    this.octiSubmit.emit({promptType: PromptType.EXPLAIN, text: this.textHighlightService.getSelectedText(this.mouseUpElementId, ' ')});
    this.popperClosed.emit();
  }

  onSummarize() {
    this.octiSubmit.emit({promptType: PromptType.SUMMARY});
    this.popperClosed.emit();
  }

  onChatSubmit() {
    const content = this.octiEditor.nativeElement.textContent.trim();
    if (content) {
      this.octiEditor.nativeElement.innerHTML = '';
      this.octiSubmit.emit({promptType: PromptType.DEFAULT, text: content});
      this.popperClosed.emit();
    }
  }
}
