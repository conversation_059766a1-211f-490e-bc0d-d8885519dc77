<div class="w-100 comment-box comments-user-mentioned" id="cm-{{comment.id}}" [class.other-family-comment]="otherFamilyComment">
  <div [hidden]="editComment?.id === comment.id" class="d-flex flex-column gap-spacing-sm">
    <div class="comment-head d-flex justify-content-between align-items-center">
      <app-user-avatar [user]="comment.user" [hasSubTitle]="false" size="small" class="comment-avatar" [showTooltip]="false">
      </app-user-avatar>
      <div class="flex-grow-1 ms-2">
        <div class="content-heading-h5">{{ (comment.user | userTitle) }}</div>
        <div class="content-heading-h7 content-color-tertiary">{{ comment.created_at | timeReadable }} <span
            *ngIf="comment.created_at !== comment.updated_at" >(edited)</span>
        </div>
      </div>
      <div class="comment-icon d-flex">
        <span *ngIf="comment.private" class="button-main-tertiary-grey button-square button-small" ngbTooltip="Private comment"><i class="fa-regular fa-lock"></i></span>
        <a href="javascript:void(0)" class="figma-dropdown" *ngIf="isCommentOwner(comment.user_id)">
          <div class="figma-dropdown-btn button-main-tertiary-grey button-square button-small" appDropdownToggle>
            <i class="fa-regular fa-ellipsis-vertical"></i>
          </div>
          <div class="figma-dropdown-content p-x-spacing-none p-y-spacing-md figma-dropdown-content-right radius-big">
            <div class=" p-x-spacing-md p-b-spacing-xx-s">
              <div class="figma-dropdown-item figma-dropdown-item-hover" (click)="onEditComment(comment)"><i
                  class="fa-regular fa-pen  m-r-spacing-sm"></i> Edit</div>
            </div>
            <div class="figma-dropdown-item-divider"></div>
            <div class="p-spacing-md">
              <div class="content-heading-h6 content-color-secondary p-x-spacing-xx-s p-b-spacing-sm">Who has access</div>
              <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column"
                [class.active]="!comment.private" (click)="onMakeCommentPublic(comment)">
                <div><i class="fa-regular fa-globe  m-r-spacing-sm"></i> Public</div>
                <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
              </div>
              <div [ngbTooltip]="disablePrivate(comment)? 'Reply or mentioning someone else is only allowed in public comments':null">
                <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column"
                  [class.active]="comment.private" [class.disabled]="disablePrivate(comment)"
                  (click)="onMakeCommentPrivate(comment)">
                  <div><i class="fa-regular fa-lock  m-r-spacing-sm"></i> Private</div>
                  <div class="content-body-xsmall" [class.content-color-tertiary]="!disablePrivate(comment)">Only you</div>
                </div>
              </div>
            </div>
            <div class="figma-dropdown-item-divider"></div>
            <div class="p-x-spacing-md p-t-spacing-xx-s">
              <div class="figma-dropdown-item figma-dropdown-item-hover" (click)="onDeleteComment(comment)"><i
                  class="fa-regular fa-trash m-r-spacing-sm"></i> Delete comment</div>
            </div>
          </div>
        </a>
      </div>
    </div>
    <div class="comment-publication-number" *ngIf="otherFamilyComment">
      <span class="content-body-small m-r-spacing-sm">Family member:</span> <span ngbTooltip="Open this document comment in new tab" tooltipClass="white-tooltip" (keydown.enter)="loadPublication(comment)" role="button" tabindex="0" (click)="loadPublication(comment)" class="tag-label-primary content-label-small cursor-pointer"><span *ngIf="comment.publication_number" class="m-r-spacing-xx-s" [ngClass]="getFlagIcon(comment.publication_number)"></span>{{comment.publication_number || 'Family representative'}}</span>
    </div>
    <div class="comment-section-reference content-quotes-02 content-color-tertiary" *ngIf="comment.source !== commentSource.GENERAL"
      [innerHTML]="comment.text" (click)="scrollToCommentHighlight(comment)"
      (mouseleave)="onMouseLeaveComment(comment)" (mouseover)="onMouseOverComment(comment)" (mouseenter)="onMouseOverComment(comment)" >
    </div>
    <div class=" content-body-small d-flex flex-column justify-content-start align-items-start m-b-spacing-xx-s">
      <span appTextEllipsis class=" ellipsis-text-3" [innerHTML]="comment.comment | safeHtml: null: null: {allowedTags: []} | tagParser: '14.6px/22px' : true : '@' | bypassSecurity: 'html'"></span>
    </div>
    <div class="d-flex content-label-small m-t-spacing-xx-s m-b-spacing-sm" [class.justify-content-between]="comment.replies?.length > 0"
      [class.justify-content-end]="comment.replies?.length === 0" *ngIf="!comment.private">
      <div class="button-main-tertiary-grey button-small" (click)="toggleReplies($event)" [hidden]="comment.replies?.length === 0">
        <i class="fa-regular fa-message"></i> {{showReplies? 'Hide': comment.replies?.length}} {{'Reply' | pluralize: comment.replies?.length}}</div>
      <div class="button-main-tertiary-grey button-small" (click)="onAddReply($event)" [hidden]="addReply">Reply</div>
    </div>
  </div>

  <div *ngIf="editComment?.id === comment.id">
    <app-patent-comment-editor class="comments-add d-flex align-items-center w-100" [allTeamUsers]="teamUsers" [allTeamGroups]="teamGroups"
      [edit]="editComment" (updateComment)="onUpdateComment($event, editComment)" (cancelComment)="onCancelComment()" [focused]="true"  [hasOuterBorder]="true"></app-patent-comment-editor>
  </div>
  <div class="comment-replies comment-replies-id-{{comment.id}} m-l-spacing-big" *ngIf="showReplies" [class.m-b-spacing-md]="!(!editComment && addReply)">
    <div *ngIf="comment.replies?.length > 0">
      <ng-container *ngFor="let v of comment.replies; let last = last; index as i;" [ngTemplateOutlet]="reply"
        [ngTemplateOutletContext]="{reply: v, index: i, last: last}">
      </ng-container>
    </div>
  </div>
  <div class="reply-editor" *ngIf="!editComment && addReply">
      <app-patent-comment-editor #replyEditor class="comments-add d-flex align-items-center w-100" [allTeamUsers]="teamUsers" [allTeamGroups]="teamGroups" [focused]="true"
        [reply]="comment" (saveComment)="onSaveReply($event)" placeholder="Reply..."  (cancelComment)="onCancelComment(comment)" [hasOuterBorder]="true"></app-patent-comment-editor>
  </div>
</div>
<ng-template #reply let-reply="reply" let-index="index" let-last="last">
  <div class="w-100" [hidden]="editComment?.id === reply.id" id="cm-{{reply.id}}">
    <div class="comment-head d-flex justify-content-between align-items-center mb-2">
      <app-user-avatar [user]="reply.user" [hasSubTitle]="false" size="small" class="comment-avatar" [showTooltip]="false">
      </app-user-avatar>
      <div class="flex-grow-1 ms-2">
        <div class="content-heading-h5">{{ reply.user | userTitle }}</div>
        <div class="content-heading-h7 content-color-tertiary">{{ reply.created_at | timeReadable }} <span
            *ngIf="reply.created_at !== reply.updated_at" >(edited)</span></div>
      </div>
      <div class="comment-icon">
        <a href="javascript:void(0)" class="figma-dropdown" *ngIf="isCommentOwner(reply.user_id)">
          <div class="figma-dropdown-btn button-main-tertiary-grey button-square button-small" appDropdownToggle>
            <i class="fa-regular fa-ellipsis-vertical"></i>
          </div>
          <div class="figma-dropdown-content p-spacing-md figma-dropdown-content-right radius-big">
            <div class="figma-dropdown-item figma-dropdown-item-hover" (click)="onEditComment(reply)"><i class="fa-regular fa-pen  m-r-spacing-sm"></i> Edit</div>
            <div class="figma-dropdown-item figma-dropdown-item-hover" (click)="onDeleteComment(reply)"><i class="fa-regular fa-trash m-r-spacing-sm"></i> Delete comment</div>
          </div>
        </a>
      </div>
    </div>
    <div class="comment-body content-body-small "
      [innerHTML]="reply.comment | safeHtml: null: null: {allowedTags: []} | tagParser: '14.6px/22px' : true : '@' | bypassSecurity: 'html'">
    </div>
  </div>
  <div *ngIf="editComment?.id === reply.id">
    <app-patent-comment-editor class="comments-add d-flex align-items-center w-100" [allTeamUsers]="teamUsers" [allTeamGroups]="teamGroups"
      [edit]="editComment" [reply]="comment" (updateComment)="onUpdateComment($event, editComment)" (cancelComment)="onCancelComment()" [focused]="true"  [hasOuterBorder]="true"></app-patent-comment-editor>
  </div>
  <div class="m-y-spacing-sm w-100" *ngIf="showDivider && !last">
    <div class="comment-divider"></div>
  </div>
</ng-template>
