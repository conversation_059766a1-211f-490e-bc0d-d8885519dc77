@use 'scss/figma2023/variables' as variables;
.comment {
  &-box {
    display: flex;
    flex-direction: column;
    padding: variables.$spacing-system-spacing-md variables.$spacing-system-spacing-md variables.$spacing-system-spacing-big variables.$spacing-system-spacing-md;
    transition: all ease-in-out .2s;
    border-bottom: 1px solid variables.$colours-border-minimal;
    &:hover{
      background-color: variables.$colours-background-bg-transition;
      border-color: variables.$colours-background-bg-transition;
    }
  }

  &-head {
    display: flex;
    padding: variables.$spacing-system-spacing-xx-s 0;
  }
  &-publication-number{
    padding-bottom: variables.$spacing-system-spacing-xx-s;
    margin-bottom: variables.$spacing-system-spacing-sm;
  }

  &-section-reference {
    cursor: pointer;
    padding-left: variables.$spacing-system-spacing-sm;
    margin-left: variables.$spacing-system-spacing-x-big;
    border-left: 2px solid variables.$colour-global-dennemeyer-orange;
    color: variables.$colours-content-content-tertiary;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &-body {
    margin: variables.$spacing-system-spacing-sm 0;
  }

  &-footer {
    margin-bottom: variables.$spacing-system-spacing-md;
  }

  &-replies {
    padding-left: variables.$spacing-system-spacing-md;
    border-left: 2px solid variables.$colours-border-subtle;
  }

  &-divider {
    border-bottom: 0.0625rem solid variables.$colours-border-subtle;
  }
}

:host::ng-deep{
  .ta-user{
    width: 2rem !important;
    .ta-avatar{
      width: 2rem !important;
      height: 2rem !important;
      font-size: 1rem !important;
    }
  }
  .reply-editor{
    .input-dialog-container{
      padding-top: variables.$spacing-system-spacing-md;
      margin-bottom: variables.$spacing-system-spacing-md;
      &.focused{
        padding-top: 0;
        padding-left: variables.$spacing-system-spacing-md;
        border-left: 2px solid variables.$colours-border-subtle;
      }
    }
  }
}
