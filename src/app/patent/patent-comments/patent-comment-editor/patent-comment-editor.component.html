<div class="input-dialog scrollbar-2024" [class.outer-border]="hasOuterBorder" [class.input-dialog-focused]="focused" #commentDialog >
  <div class="input-dialog-body">
    <div class="show-on-focus justify-content-between position-relative">
      <div class="d-flex align-items-center flex-grow-1">
        <app-user-avatar [user]="edit ? edit.user: userService.getUser().profile" [hasSubTitle]="false" size="small" class="input-user-avatar" [showTooltip]="false"></app-user-avatar>
        <div class="flex-grow-1 ms-2 input-user-name">
          <div class="content-heading-h5">{{ (edit ? edit.user: userService.getUser().profile) | userTitle }}</div>
        </div>
      </div>
      <div *ngIf="!reply">
        <div class="figma-dropdown">
          <div class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small content-color-tertiary" appDropdownToggle [closeOnClick]="true">
            <span>
              <i class="fa-regular fa-lock  m-r-spacing-xx-s" *ngIf="commentPrivate"></i>
              <i class="fa-regular fa-globe  m-r-spacing-xx-s" *ngIf="!commentPrivate"></i>
              <i class="fas fa-caret-down"></i>
            </span>
          </div>
          <div class="figma-dropdown-content p-x-spacing-none p-y-spacing-md figma-dropdown-content-right radius-big">
            
            <div class="p-spacing-md">
              <div class="content-heading-h6 content-color-secondary p-x-spacing-xx-s p-b-spacing-sm">Who has access</div>
              <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column" [class.active]="!commentPrivate" (click)="onChangePermission(false)">
                <div><i class="fa-regular fa-globe  m-r-spacing-sm"></i> Public</div>
                <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
              </div>
              <div [ngbTooltip]="disablePrivate? 'Reply or mentioning someone else is only allowed in public comments':null">
                <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column" [class.active]="commentPrivate" [class.disabled]="disablePrivate" (click)="onChangePermission(true)">
                  <div><i class="fa-regular fa-lock  m-r-spacing-sm"></i> Private</div>
                  <div class="content-body-xsmall" [class.content-color-tertiary]="!disablePrivate">Only you</div>
                </div>
              </div>
            </div>
            <div class="figma-dropdown-item-divider" *ngIf="!edit"></div>
            <div class="p-x-spacing-md p-t-spacing-xx-s"  *ngIf="!edit">
              <div class="figma-dropdown-item figma-dropdown-item-hover" (click)="onChangePermission(defaultCommentPermission)"  [class.disabled]="defaultCommentPermission && disablePrivate">
                <i class="fa-regular fa-gear"></i> Default setting
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="input-dialog-textarea comments-user-mentioned d-flex align-items-center gap-spacing-sm flex-fill" (click)="focusEditor()">
      <app-user-avatar [user]="edit ? edit.user: userService.getUser().profile" [hasSubTitle]="false" size="xsmall" class="input-user-avatar p-0"
                       [showTooltip]="true" *ngIf="!focused">
      </app-user-avatar>
      <div [contentEditable]="!isSavingComment" #commentEditor (paste)="onPaste($event)" class="text-editor"
        [innerHTML]="commentText | safeHtml: null: null: {allowedTags: []} | tagParser: '16.6px/25px' : true : '@' | bypassSecurity: 'html'"
        [attr.data-placeholder]="placeholder" (focus)="focused=true" [mention]="visibleCollaborators" [mentionListTemplate]="mentionListTemplate"
        [mentionConfig]="mentionConfig" (opened)="mentionListOpen = true" (blur)="updateTaggedCollaborators()"
        (input)="onInput($event)" (itemSelected)="updateTaggedCollaborators($event)" (mentionClosed)="onMentionListClosed()"></div>
      <small class="hide-on-focus show-remaining-count" hidden>{{inputChar}}/{{maxChar}}</small>
    </div>
  </div>
  <div class="input-contorl show-on-focus">
    <button class="button-main-secondary-grey button-small m-r-spacing-md" (click)="onCancelComment()"
      [class.disabled]="isSavingComment">Cancel</button>
    <button class="button-main-primary button-small" (click)="onSaveComment()"
      [class.disabled]="isSavingComment || isEditorEmpty">{{ getSubmitText() }}</button>
  </div>
</div>

<ng-template #mentionListTemplate let-item="item">
  <app-user-avatar [user]="item" [hasSubTitle]="false" [showTooltip]="false" class="comment-avatar scrollbar-2024"></app-user-avatar>
  <span>{{item.name}}</span>
</ng-template>
