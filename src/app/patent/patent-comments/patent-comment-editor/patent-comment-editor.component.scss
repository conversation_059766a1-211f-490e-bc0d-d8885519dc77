@import 'scss/figma2023/variables';
@import 'scss/figma2023/mixins';

:host::ng-deep{
  mention-list{
    left: 30px !important;
  }
  .mention-menu{
    border-radius: $radius-big !important;
    padding: $spacing-system-spacing-sm !important;
    background-color: $colours-background-bg-primary !important;
    color: $colours-content-content-primary !important;
    &::before{
      display: block;
      content: 'Mention users';
      display: block;
      color: $colours-content-content-tertiary;
      padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
      @include add-properties(map-get(map-get($typography, 'heading'), 'h6'), true);
    }
  }
  .scrollable-menu{
    background: $colours-background-bg-primary !important;
  }
  .mention-item{
    display: flex !important;
    flex-direction: row;
    gap: $spacing-system-spacing-sm;
    padding: $spacing-system-spacing-sm $spacing-system-spacing-big !important;
    min-width: 215px !important;
    @include add-properties(map-get(map-get($typography, 'body'), 'medium'), true);
    &:hover{
      background-color: $colours-background-bg-secondary !important;
      color: $colours-content-content-primary !important;
    }
    &:focus{
      border: 2px solid $colours-border-subtle !important;
      color: $colours-content-content-primary !important;
    }
  }
  .mention-active a{
    color: inherit !important;
    background-color: inherit !important;
  }

  .text-mentioned {
    color: #135a9a;
  }

  ::-webkit-scrollbar-thumb {
    cursor: pointer;
  }
  
  .text-editor {
    display: inline-block;
    width: 100%;
    min-height: 1.5rem;
    white-space: pre-wrap;
    word-break: break-word;
    color: $colours-content-content-primary;
    
    input.lcd-tagged-user,
    input.lcd-tagged-group {
      color: $colours-content-content-active;
    }
    
    span:not(.text-mentioned) {
      color: $colours-content-content-primary;
    }
    
    .normal-text {
      color: $colours-content-content-primary !important;
      font-weight: normal !important;
    }
    
    @-moz-document url-prefix() {
      br {
        display: none;
      }
      input.lcd-tagged-user,
      input.lcd-tagged-group {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
}
.comment-permission-dropdown.dropdown{
  position: unset;
  .dropdown-menu{
    max-width: 150px;
  }
}

.figma-dropdown.show{
  .fas.fa-caret-down{
    transition: transform 0.5s;
    transform: rotate(180deg);
  }
}