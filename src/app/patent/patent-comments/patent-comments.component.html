<div class="p-spacing-md">
  <app-patent-side-section-header [title]="sideSectionEnum.COMMENTS" (closeEvent)="onCloseSectionClicked()" [tooltip]="tooltip" [tooltipClass]="tooltipClass" 
    class="m-b-spacing-big d-block">
  </app-patent-side-section-header>
  <div class="w-100 d-flex justify-content-between comments-menu m-b-spacing-x-big">
    <div [ngbPopover]="popoverPermissionCommentsTemplate" [autoClose]="true" popoverClass="context-menu-popper"
      container="body" placement="bottom-left">
      <button ngbTooltip="Default settings" [autoClose]="true" triggers="hover"
        class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small">
        <i class="fa-regular fa-lg fa-gear"></i>
      </button>
    </div>
    <div class="d-flex gap-spacing-xx-s">
      <div class="figma-dropdown comment-filter-by">
        <button [ngbPopover]="popoverFilterCommentsTemplate" [autoClose]="true" popoverClass="context-menu-popper"
          container="body" placement="bottom-right"
          class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small content-color-tertiary">
          <i class="fa-regular fa-lg fa-bars-filter"></i> {{annotationService.commentsFilterBy}}
        </button>
      </div>
      <div class="figma-dropdown comment-sort-by">
        <button [ngbPopover]="popoverSortCommentsTemplate" [autoClose]="true" popoverClass="context-menu-popper"
          container="body" placement="bottom-right"
          class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small button-square">
          <i class="fa-regular fa-lg fa-arrow-up-arrow-down"></i>
        </button>
      </div>
    </div>
  </div>

  <div class="w-100 d-flex justify-content-between comments-top m-b-spacing-x-big">
    <span class="tab-pill" (click)="setSelectedTab(selectedTabEnum.GENERAL)" [ngClass]="{'active': isGeneralTab}">
      <span class="content-label-medium">General <span *ngIf="generalComments.length !== 0" class="comment-count"
          [ngClass]="{'active': isGeneralTab}"> {{generalComments.length}}</span></span>
    </span>
    <span class="tab-pill" (click)="setSelectedTab(selectedTabEnum.SECTION)" [ngClass]="{'active': isSectionTab}">
      <span class="content-label-medium">On section <span *ngIf="sectionComments.length !== 0" class="comment-count"
          [ngClass]="{'active': isSectionTab}"> {{sectionComments.length}}</span></span>
    </span>
  </div>

  <app-patent-comment-editor class="comments-add d-flex align-items-center w-100" *ngIf="isGeneralTab"
    [allTeamUsers]="teamUsers" [allTeamGroups]="teamGroups" (cancelComment)="onCancelComment()"
    (saveComment)="onSaveGeneralComment($event)"></app-patent-comment-editor>
</div>

<div class="figma-side-section-content w-100 flex-grow-1">
  <div class="w-100 h-100 d-flex flex-column align-items-stretch comments-result" [class.loading-comments]="isLoading" [class.disabled-comments]="isDisabledClick">
  
    <div class="comments-main-content flex-grow-1 m-b-spacing-md p-r-spacing-xxx-s">
      <div *ngIf="!isLoading">
        <div class="comments-no-comment p-spacing-big" *ngIf="isCommentsEmpty">
          <div class="d-flex flex-column align-items-center">
            <div class="p-b-spacing-xx-big">
              <div class="feature-container feature-xl"><i class="feature-icon fa-light fa-message-lines" [ngClass]="isSectionTab? 'fa-message-pen':'fa-message-lines'"></i></div>
            </div>
            <div class="content-color-tertiary p-y-spacing-sm">
              <ng-container *ngIf="isFiltered else noFiltered">
                <span class="content-heading-h4 d-block p-b-spacing-sm">No results found.</span>
                <span class="d-block p-b-spacing-sm">It seems we can’t find any result based on your selection.</span>
                <span>Try to clear or change your filters.</span>
              </ng-container>
              <ng-template #noFiltered>
                <ng-container *ngIf="isGeneralTab">
                  <span class="d-block p-b-spacing-sm">Currently, there are no general comments on this patent.</span>
                  <span>Be the first to add a comment!</span>
                </ng-container>
                <span *ngIf="isSectionTab">Highlight a piece of text and make a comment. We will display here.</span>
              </ng-template>
            </div>
          </div>
        </div>
        <div class="comments-list">
          <app-patent-comment-box class="w-100" *ngFor="let v of currentDisplayedComments; let last = last; index as i;"
            [comment]="v" (deleteComment)="onDeleteComment($event)"
            [teamUsers]="teamUsers" [teamGroups]="teamGroups" [showDivider]="!last">
          </app-patent-comment-box>
  
          <div *ngIf="otherComments.length > 0" class="comments-list-other">
            <div class="m-y-spacing-big m-x-spacing-big">
              <span class="content-heading-h5">Other family members </span>
              <span style="font-size: 0.75rem; width: 1rem; height: 1rem;" ><i class="fa-light fa-info-circle" [ngbTooltip]="otherCommentsTooltipTemplate"
                tooltipClass="tooltip-md" container="body"></i></span>
            </div>
            <app-patent-comment-box class="w-100" [otherFamilyComment]="true" *ngFor="let v of otherComments; let last = last; index as i;"
              [comment]="v" (deleteComment)="onDeleteComment($event)"
              [teamUsers]="teamUsers" [teamGroups]="teamGroups" [showDivider]="!last">
            </app-patent-comment-box>
          </div>
        </div>
      </div>
      <div class="loading-message" *ngIf="isLoading">
        Comments are loading...
      </div>
    </div>
  </div>
</div>

<ng-template #otherCommentsTooltipTemplate>
  <div class="text-left p-spacing-sm">
    <div class="content-heading-h6 m-b-spacing-md">Other family members</div>
    <div class="content-body-small">
      Here you can view comments on other documents of the same family. Click on each to jump to the related document.
    </div>
  </div>
</ng-template>

<ng-template #popoverFilterCommentsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary">Filter comments by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium" *ngFor="let f of annotationService.commentsFilterByOption"
   (click)="filterComments(f)" [class.active]="annotationService.commentsFilterBy === f">
    <span class=" p-r-spacing-xxx-big">{{ f }} </span>
  </div>
</ng-template>

<ng-template #popoverSortCommentsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Sort by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover p-r-spacing-s"
       *ngFor="let s of annotationService.commentsSortByOption" (click)="sortComments(s);"
       [class.active]="annotationService.commentsSortBy === s">
    <span class=" p-r-spacing-xxx-big">{{ s }} </span>
  </div>
</ng-template>

<ng-template #popoverPermissionCommentsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary p-x-spacing-xx-s p-y-spacing-none m-b-spacing-sm">Default settings</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column" [class.active]="!defaultCommentPermission" (click)="onSetCommentPermission(false)">
    <div><i class="fa-regular fa-globe  m-r-spacing-sm"></i> Public</div>
    <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
  </div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check flex-column" [class.active]="defaultCommentPermission" (click)="onSetCommentPermission(true)">
    <div><i class="fa-regular fa-lock  m-r-spacing-sm"></i> Private</div>
    <div class="content-body-xsmall content-color-tertiary">Only you</div>
  </div>
  <div class="figma-dropdown-item-divider"></div>
  <div class="content-body-xsmall content-color-tertiary d-flex p-t-spacing-md gap-spacing-xx-s">
    <i class="fa-regular fa-circle-info"></i>
    <span>Permission settings can be adjusted for each individual comment.</span>
  </div>
</ng-template>
