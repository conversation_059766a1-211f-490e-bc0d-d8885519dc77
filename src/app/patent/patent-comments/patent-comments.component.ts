import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { Patent, TeamUser, UserGroup } from '@core/models';
import { AnnotationService, DocumentAnnotation, DocumentCommentSourceEnum, PatentViewService, ToastService, ToastTypeEnum, UserService } from '@core/services';
import { AnnotationStoreService, BaseStoreService } from '@core/store';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PatentCommentsActionSourceEnum, PatentCommentsFilterEnum, PatentCommentsSelectedTabEnum, SideSectionEnum } from '@patent/types';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { Subscription, finalize } from 'rxjs';

@Component({
  selector: 'app-patent-comments',
  templateUrl: './patent-comments.component.html',
  styleUrls: ['./patent-comments.component.scss']
})
export class PatentCommentsComponent implements OnInit, OnDestroy {
  @Input() patent: Patent;
  @Input() storeService: BaseStoreService;
  @Input() teamUsers: TeamUser[];
  @Input() teamGroups: UserGroup[];
  @Input() focusedComment?: DocumentAnnotation;
  @Input() isLoading: boolean = true;

  @Input() tooltip: { title: string, description: string } = {title: null, description: null};
  @Input() tooltipClass: string = '';
  isDisabledClick: boolean;
  @Output() closeEvent: EventEmitter<void> = new EventEmitter<void>();

  selectedTabEnum = PatentCommentsSelectedTabEnum;
  commentsFilterEnum = PatentCommentsFilterEnum;
  private selectedTab: PatentCommentsSelectedTabEnum = PatentCommentsSelectedTabEnum.GENERAL;
  private _actionSource: PatentCommentsActionSourceEnum = PatentCommentsActionSourceEnum.CONTROL_BAR;
  
  readonly sideSectionEnum = SideSectionEnum;

  @Input() set actionSource(val: PatentCommentsActionSourceEnum) {
    this._actionSource = val;

    switch (val) {
      case PatentCommentsActionSourceEnum.CONTROL_BAR:
      case PatentCommentsActionSourceEnum.GENERAL_COMMENT:
        this.selectedTab = PatentCommentsSelectedTabEnum.GENERAL;
        break;
      case PatentCommentsActionSourceEnum.SECTION_COMMENT:
        this.selectedTab = PatentCommentsSelectedTabEnum.SECTION;
        break;
    }
  }

  private subscriptions = new Subscription();
  constructor(
    private ngbModal: NgbModal,
    public annotationService: AnnotationService,
    private patentViewer: PatentViewService,
    public annotationStore: AnnotationStoreService,
    private toastService: ToastService,
    public userService: UserService,
  ) {
  }
  ngOnInit() {
    this.initComments();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get defaultCommentPermission(){
    return this.userService.defaultCommentPermission;
  }

  get publicationNumber(): string{
    return this.patentViewer.activePublicationName;
  }

  get isGeneralTab(): boolean{
    return this.selectedTab === PatentCommentsSelectedTabEnum.GENERAL;
  }

  get isSectionTab(): boolean{
    return this.selectedTab === PatentCommentsSelectedTabEnum.SECTION;
  }

  get isFiltered(): boolean {
    return this.annotationService.commentsFilterBy !== this.commentsFilterEnum.SEE_ALL;
  }

  get currentDisplayedComments(){
    if (this.publicationNumber) {
      return this.commentsBySelectedTab.filter(c => c.publication_number === this.publicationNumber);
    } else {
      return this.commentsBySelectedTab.filter(c => !c.publication_number);
    }
  }

  get generalComments(): DocumentAnnotation[] {
    return this.annotationStore.generalComments;
  }

  get sectionComments(): DocumentAnnotation[] {
    return this.annotationStore.sectionComments;
  }

  get otherComments(): DocumentAnnotation[] {
    if (this.publicationNumber) {
      return this.commentsBySelectedTab.filter(c => c.publication_number !== this.publicationNumber);
    } else {
      return this.commentsBySelectedTab.filter(c => !!c.publication_number);
    }
  }

  get commentsBySelectedTab() {
    return this.isSectionTab ? this.sectionComments : this.generalComments;
  }

  get isCommentsEmpty(): boolean{
    return this.currentDisplayedComments?.length === 0;
  }

  setSelectedTab(tab: PatentCommentsSelectedTabEnum) {
    this.selectedTab = tab;
  }

  onCloseSectionClicked() {
    this.closeEvent.emit();
  }

  initComments(){
    this.loadUsers();
    if(this.focusedComment) {
        this.annotationService.scrollToComment(this.focusedComment);
        this.annotationService.scrollToHighlightedElement(this.focusedComment);
    }
  }

  loadUsers() {
    const userIds = [...new Set(this.annotationStore.listComments.filter(cm => !cm.user).map(a => a.user_id))];

    if (userIds.length > 0) {
      const teamUsersPayload = { id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1, include_blocked: 1 };

      const getTeamUsers$ = this.userService.getTeamUsers(teamUsersPayload)
        .subscribe({
          next: ({ users }) => {
            const mappedUser = new Map();
            for (let u of users) {
              mappedUser.set(u.id, u);
            }
            this.annotationStore.listComments.forEach(a => {
              if (mappedUser.has(a.user_id)) {
                a.user = mappedUser.get(a.user_id)
              }
            });
            this.isLoading = false;
          }, error: error => console.log('error in fetching user')
        });
      this.subscriptions.add(getTeamUsers$);
    }
  }

  onDeleteComment(comment){
    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'md', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Delete comment',
      description: 'Deleting this comment will also remove the linked answers.',
      question: 'Would you like to delete this comment?',
      confirmButton: 'Delete',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.isDisabledClick = true;
          const deleteDocumentComment$ = this.annotationService.deleteDocumentComment(Number(this.patent.general.docdb_family_id), comment.id)
            .subscribe({next: () => {
              this.annotationStore.listComments = this.annotationStore.listComments.filter(cm => cm.id !== comment.id);
              this.annotationService.removeCommentHighlight(comment.id);
              this.annotationService.highlightComments();
              this.toastService.show({
                type: ToastTypeEnum.SUCCESS,
                header: 'Comment deleted',
                body: `The comment was deleted successfully`,
                delay: 3000
              });
              this.isDisabledClick = false;
              this.annotationStore.removeAnnotation = 'comments';
            }, error: err => {
              console.log(err);
              this.isDisabledClick = false;
              this.toastService.show({
                type: ToastTypeEnum.ERROR,
                header: 'Failed to delete a comment',
                body: `There was an error in deleting the comment.<br/>${err.error.message}`,
                delay: 5000
              });
            }});
          this.subscriptions.add(deleteDocumentComment$);
      }
    }, () => {});
  }

  onSaveGeneralComment(event){
    this.isDisabledClick = true;
    const payload: DocumentAnnotation = {
      end_pos: 0,
      field: 'general',
      publication_number: this.publicationNumber || null,
      start_pos: 0,
      source: DocumentCommentSourceEnum.GENERAL,
      text: '',
      color: '',
      comment: event.comment,
      private: event.private,
      parent_comment_id: event.parent_comment_id ? event.parent_comment_id : null,
      tagged_user_ids: event.tagged_user_ids,
      tagged_group_ids: event.tagged_group_ids
    };
    const saveDocumentComment$ = this.annotationService.saveDocumentComment(payload, Number(this.patent.general.docdb_family_id))
        .pipe(finalize(() => {this.isDisabledClick = false;this.onCancelComment();})).subscribe({next:({data}) => {
          data.user = this.userService.getUser().profile;
          this.annotationStore.addComment(data);
          this.annotationService.highlightComments(true, data);
        }, error: err => {
          console.error(err);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to save a comment',
            body: `There was an error in saving the comment.<br/>${err.error.message}`,
            delay: 5000
          });
        }});
      this.subscriptions.add(saveDocumentComment$);
  }

  onCancelComment(){
    document.body.click();
  }

  sortComments(sortBy){
    this.annotationService.commentsSortBy = sortBy;
    this.annotationService.sortAndFilterComments();
  }

  filterComments(filterBy: PatentCommentsFilterEnum){
    this.annotationService.commentsFilterBy = filterBy;
    this.annotationService.sortAndFilterComments();
  }
  onSetCommentPermission(defaultValue: boolean){
    const updateUISettings$ = this.userService.setDefaultCommentPermission(defaultValue).subscribe({
      next: (user) => { },
      error: (error) => { console.error(error); }
    });
    this.subscriptions.add(updateUISettings$);
  }
}
