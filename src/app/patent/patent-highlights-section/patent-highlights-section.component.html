<ng-container *ngIf="!isSavingHighlight else loadingHighlights">
  <div class="d-flex justify-content-end m-b-spacing-x-big">
    <div ngbTooltip="Sort by" tooltipClass="white-tooltip" class="figma-dropdown">
      <button [ngbPopover]="popoverSortHighlightsTemplate" [autoClose]="true"
              popoverClass="context-menu-popper" container="body" placement="bottom-right"
              class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small">
        <span class="p-x-spacing-xx-s menu-text">{{ currentSort }}</span>
        <i class="fa-regular fa-arrow-down-arrow-up ms-2"></i>
      </button>
    </div>
  </div>

  <div class="w-100 h-100 overflow-auto"
       [ngClass]="{'p-r-spacing-xxx-s': highlightList?.length}">
      <ng-container *ngIf="highlightList?.length else noHighlights">
        <div class="highlight-section mb-4" *ngFor="let highlight of highlightList">
          <div class="highlight-section-head d-flex justify-content-between align-items-center mb-2">
            <app-user-avatar [user]="highlight.user" [hasSubTitle]="false" [showTooltip]="false" size="small"></app-user-avatar>
            <div class="flex-grow-1 ms-2">
              <div class="content-heading-h5">{{ getNameOfHighlightAuthor(highlight) }}</div>
              <div class="content-heading-h7 content-color-tertiary">{{ highlight.created_at | timeReadable }}</div>
            </div>
            <div class="button-main-tertiary-grey button-square button-small cursor-pointer"
                 ngbTooltip="Colour" tooltipClass="white-tooltip" (click)="onChangeHighlightColor($event, highlight)">
              <div class="highlight-icon" [style.background-color]="'#' + highlight.label.color">
                <i class="fa-regular fa-highlighter fa-1x"></i>
              </div>
            </div>
          </div>
          <div class="highlight-section-body d-flex justify-content-between">
            <div class="highlight-section-bar"></div>
            <div class="highlight-section-text content-quotes-02 content-color-tertiary flex-fill cursor-pointer ellipsis-text-3" (click)="goToHighlight(highlight)">
              {{ highlight.text }}
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #noHighlights>
        <div class="no-highlights text-center">
          <div class="no-highlights-icon p-4">
            <span>
              <i class="fa-light fa-highlighter"></i>
            </span>
          </div>
        </div>
        <div class="text-center pt-4 px-3">
          <span class="no-highlights-text content-body-medium py-2">
            Select the text you want to highlight. All highlighted text in this patent will appear here.
          </span>
        </div>
      </ng-template>
  </div>
</ng-container>

<ng-template #loadingHighlights>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch highlights-loading-template">
    <div class="d-flex justify-content-between highlights-loading-top"></div>
    <div class="highlights-loading-main-content flex-grow-1 d-flex align-items-center justify-content-center">
      <div class="content-heading-h6 content-color-secondary">Highlights are loading...</div>
    </div>
  </div>
</ng-template>

<ng-template #popoverSortHighlightsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Sort by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium p-r-spacing-s"
       *ngFor="let op of sortOptions"
       (click)="onSortHighlightsClicked(op)"
       [class.active]="op === currentSort">
    <span class="p-r-spacing-xxx-big">{{ op }} </span>
  </div>
</ng-template>
