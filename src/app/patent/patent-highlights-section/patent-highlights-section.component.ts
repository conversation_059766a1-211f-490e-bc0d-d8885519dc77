import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DocumentAnnotation, PatentViewService } from '@core';
import { SortHighlightsOptionEnum } from '@patent/types';

@Component({
  selector: 'app-patent-highlights-section',
  templateUrl: './patent-highlights-section.component.html',
  styleUrls: ['./patent-highlights-section.component.scss']
})
export class PatentHighlightsSectionComponent {

  @Input() highlightList: DocumentAnnotation[];
  @Input() isSavingHighlight: boolean;

  @Output() changeHighlightColor = new EventEmitter<{event: MouseEvent, highlight: DocumentAnnotation}>(null);
  @Output() highlightsSorted = new EventEmitter<SortHighlightsOptionEnum>();

  sortOptions: SortHighlightsOptionEnum[] = [
    SortHighlightsOptionEnum.RECENTLY_ADDED,
    SortHighlightsOptionEnum.OLDEST_ADDED,
    SortHighlightsOptionEnum.CREATOR,
    SortHighlightsOptionEnum.COLOR
  ];

  constructor(
    private patentViewService: PatentViewService
  ) {
  }

  get currentSort(): SortHighlightsOptionEnum {
    return this.patentViewService.currentHighlightsSort;
  }

  getNameOfHighlightAuthor(highlight: DocumentAnnotation): string {
    return highlight.user?.first_name + ' ' + highlight.user?.last_name;
  }

  onChangeHighlightColor(event: MouseEvent, highlight: DocumentAnnotation) {
    this.changeHighlightColor.emit({event: event, highlight: highlight});
  }

  goToHighlight(highLight) {
    const highlightEle = document.querySelector(`[data-id="hl-${highLight.id}"]`) as HTMLElement;
    if (highlightEle) {
      highlightEle.scrollIntoView({behavior: 'smooth'});
      document.querySelectorAll('#patent-result .highlight').forEach((ele: HTMLElement) => {
        ele.style.boxShadow = 'none';
      });

      document.querySelectorAll(`[data-id="hl-${highLight.id}"]`).forEach((ele: HTMLElement) => {
        ele.style.boxShadow = '0 0 2px #135a9a4d, 0 2px 4px #135a9a4d';
        setTimeout(() => {
          ele.style.boxShadow = 'none';
        }, 5000);
      });
    }
  }

  onSortHighlightsClicked(sortOption: SortHighlightsOptionEnum) {
    this.patentViewService.currentHighlightsSort = sortOption;
    this.highlightsSorted.emit(sortOption);
  }
}
