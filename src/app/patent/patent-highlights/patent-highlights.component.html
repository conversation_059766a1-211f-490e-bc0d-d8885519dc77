<app-popper #highlightPopper [hidden]="hiddenPopper">
    <div [hidden]="addComment" class="highlight-popper d-flex align-items-center justify-content-start" (mousedown)="$event.preventDefault(); $event.stopPropagation()">
      <div *ngIf="userService.hasOctiAiFeature()"
           class="button-main-tertiary button-small p-x-spacing-md button-octi-ai"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.OCTI_AI}"
           (click)="setPopperAction(patentHighlightActionEnum.OCTI_AI)"
           ngbTooltip="Get answers, understand patents, and more."
           tooltipClass="white-tooltip" container="body">
        <i class="fa-light octi-icon"></i><span class="p-x-spacing-x-s content-label-small">Octi AI</span>
      </div>
      <div #btnHighlight class="button-main-tertiary-grey button-small p-x-spacing-md"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.HIGHLIGHT}"
           (click)="setPopperAction(patentHighlightActionEnum.HIGHLIGHT, $event)">
        <span class="popper-color-highlight-icon" [style.background-color]="selectedColor">
          <i class="fa-light fa-highlighter"></i>
        </span>
        <span class="p-x-spacing-x-s content-label-small">Highlight</span>
      </div>
      <div class="button-main-tertiary-grey button-small p-x-spacing-md"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.COMMENTS}"
           (click)="setPopperAction(patentHighlightActionEnum.COMMENTS, $event)">
        <i class="fa-light fa-message"></i><span class="p-x-spacing-x-s content-label-small">Comments</span>
      </div>
    </div>
</app-popper>

<app-popper #colorsPopper placement="bottom-start" [showArrow]="false">
    <div class="colors-popper">
        <div class="menu-colors">
            <a class="color-option" *ngFor="let color of highlights" [style.background-color]="color.color" [ngClass]="{'active': selectedColor == color.color}"
                (click)="toggleColor(color)" [ngbTooltip]="color.name" tooltipClass="white-tooltip">
                <span *ngIf="selectedColor == color.color"><i class="fa-solid fa-check"></i></span>
            </a>
            <a class="color-option none d-flex justify-content-center align-items-center button-main-tertiary-grey button-square"
               [ngClass]="{'disabled': disableEraserButton()}" (click)="removeHighlights()"
               [ngbTooltip]="isHighlightChanging ? 'Remove' : 'Erase'" tooltipClass="white-tooltip">
                <i class="fa-light fa-eraser"></i>
            </a>
        </div>
        <ng-container *ngIf="!isHighlightChanging">
        <div class="divider"></div>
        <a class="button-main-tertiary-grey open-all-highlights" (click)="onOpenSideSection()">
            <span class="open-all-highlights-icon"><i class="fa-light fa-eye"></i></span>
            <span class="open-all-highlights-title">See all highlights</span>
        </a>
        <div class="information d-flex align-items-top m-t-spacing-big" *ngIf="isTopMenuColorsPopperOpen">
            <i class="fa-regular fa-circle-info m-r-spacing-xx-s m-t-spacing-xx-s"></i>
            <span>Click ESC on your keyboard to cancel the highlights</span>
        </div>
        </ng-container>
    </div>
</app-popper>

<div style="position: fixed; pointer-events: none;" [style.top.px]="cursorPosition.y" [style.left.px]="cursorPosition.x" *ngIf="isOverTextArea && isCustomCursorEnabled">
    <div class="custom-cursor">
      <div class="custom-cursor-point">
        <img src="assets/images/figma/cursor-highlights.svg">
      </div>
      <span class="custom-cursor-icon" [style.background-color]="selectedColor">
        <i class="fa-solid fa-highlighter-line custo-cursor-icon" *ngIf="colorCursor"></i>
        <i class="fa-solid fa-eraser custo-cursor-icon" *ngIf="eraserCursor"></i>
      </span>
    </div>
</div>
