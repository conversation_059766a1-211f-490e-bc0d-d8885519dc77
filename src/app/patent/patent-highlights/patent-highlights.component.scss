@import "scss/figma2023/variables";

:host::ng-deep {
  .pa-text-highlight {
    background-color: $colour-blue-brand-100;
  }
}

.colors-popper {
  max-width: 12.5rem;
  .menu-colors {
    display: flex;
    padding: $spacing-system-spacing-xx-s;
    align-items: center;
    align-content: center;
    gap: $spacing-system-spacing-sm;
    align-self: stretch;
    flex-wrap: wrap;
    border-radius: $radius-sm;

    .color-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      color: $colours-content-content-primary;
      width: 1.5rem;
      height: 1.5rem;
      border-radius: $radius-sm;
      border: 1px solid $colours-border-moderate;
      cursor: pointer;

      &.active {
        border: 1.5px solid $colours-border-contrast;
      }
      span {
        width: 1.25rem;
        height: 1.25rem;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;

        i {
          font-weight: 900;
          line-height: normal;
          width: 1rem;
          flex-shrink: 0;
        }
      }

      &.none {
        color: $colours-buttons-main-tertiary-grey-content;
        border: none;
        line-height: 1.5rem;
        &.disabled {
          cursor: default;
          color: $colours-content-content-disabled;
        }
      }
    }

  }
  .divider {
    height: 1px;
    width: 100%;
    margin: $spacing-system-spacing-sm 0;
    background: $colours-border-subtle;
  }

  .open-all-highlights {
    display: flex;
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-md;
    justify-content: center;
    align-items: center;
    gap: $spacing-system-spacing-sm;
    flex: 1 0 0;
    border-radius: $spacing-system-spacing-xx-s;
    color: $colours-buttons-main-tertiary-grey-content;
    cursor: pointer;

    &-title {
      text-align: center;
      font-family: $font-family-base;
      font-size: $font-size-sm;
      font-weight: 500;
      line-height: 1.25rem;
    }

    &-icon {
      text-align: center;
      i {
        display: flex;
        width: 20px;
        height: 20px;
        flex-direction: column;
        justify-content: center;
        flex-shrink: 0;

        font-size: $font-size-md;
        line-height: normal;
      }
    }
  }

  .information {
    color: $colours-content-content-tertiary;
    font-size: $font-size-xs;
    font-weight: 400;
    line-height: $font-size-x-md;
  }
}

.popper-color-highlight-icon {
  border-radius: $radius-sm;
  text-align: center;
  i {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.custom-cursor {
  width: 20px;
  height: 28px;
  position: relative;

  &-point {
    display: inline-flex;
    align-items: flex-start;
    gap: 10px;
    width: 8.165px;
    height: 11.2px;
    position: absolute;
    left: 1px;
    top: 2.301px;
  }

  &-icon {
    width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    position: absolute;
    left: 4px;
    top: 10px;

    i {
      font-size: 0.75rem;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 12px;
      height: 11px;
    }
  }
}

.octi-icon {
  width: 1rem;
  height: 1rem;
}
