import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentHighlightsComponent } from './patent-highlights.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentHighlightsComponent', () => {
  let component: PatentHighlightsComponent;
  let fixture: ComponentFixture<PatentHighlightsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentHighlightsComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentHighlightsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
