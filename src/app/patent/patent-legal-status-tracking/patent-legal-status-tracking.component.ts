import { Component, OnDestroy } from '@angular/core';
import { MatomoService, MonitorService } from '@core/services';
import { MonitorStoreService } from '@core/store';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription, finalize } from 'rxjs';

@Component({
  selector: 'app-patent-legal-status-tracking',
  templateUrl: './patent-legal-status-tracking.component.html',
  styleUrls: ['./patent-legal-status-tracking.component.scss']
})
export class PatentLegalStatusTrackingComponent implements OnDestroy {

  private subscriptions = new Subscription();
  successMessage: string;
  processingRequest: boolean;
  publications: Array<string>;

  constructor(
    public activeModal: NgbActiveModal,
    private monitorStoreService: MonitorStoreService,
    private monitorService: MonitorService,
    private matomoService: MatomoService) {
  }
  get trackablePublications(){
    return this.monitorStoreService.trackablePublications;

  }
  get checkedPublications(){
    return this.monitorStoreService.checkedPublications;

  }
  get untrackablePublications(){
    return this.monitorStoreService.untrackablePublications;
  }
  get alreadyTrackedPublications(){
    return this.monitorStoreService.alreadyTrackedPublications;
  }

  get ready(): boolean{
    return this.monitorStoreService.isLegalStatusReady;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  isPublicationChecked(publication: string) {
    return this.checkedPublications.indexOf(publication) != -1;
  }
  areAllPublicationsChecked() {
    return this.checkedPublications.length == this.trackablePublications.length;
  }

  trackLegalStatus() {
    this.successMessage = undefined;
    this.processingRequest = true;
    const trackLegalStatus$ = this.monitorService.trackLegalStatus(this.checkedPublications).pipe(finalize(() => this.processingRequest = false) ).subscribe({
      next: response => {
        this.successMessage = response.data.message;
        if(this.publications?.length>0){
          this.monitorService.loadLegalStatusTracking(this.publications);
        }
      }
    });
    this.subscriptions.add(trackLegalStatus$);
  }

  checkPublication(publication: string) {
    const index = this.checkedPublications.indexOf(publication);
    if (index == -1) {
      this.checkedPublications.push(publication);
    } else {
      this.checkedPublications.splice(index, 1);
    }
  }

  checkAllPublications() {
    if(this.areAllPublicationsChecked()){
      this.checkedPublications.length = 0;
    } else {
      this.checkedPublications.length = 0;
      this.checkedPublications.push(...this.trackablePublications);
    }
  }
}
