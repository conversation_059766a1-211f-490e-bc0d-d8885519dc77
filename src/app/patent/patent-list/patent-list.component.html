<div class="w-100 h-100 d-flex flex-column align-items-stretch current-list"
  [class.loading-current-list]="isLoading" [class.disabled]="isDisabledClick">

  <div class="current-list-header m-b-spacing-x-big">
    <div class="d-flex justify-content-between align-items-center current-list-menu">
      <div class="content-heading-h6 content-color-tertiary">{{documentsCount}} {{ 'result' | pluralize: documentsCount }}</div>
      <div>
        <div class="figma-dropdown">
          <div class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small" appDropdownToggle>
            <i class="fa-light fa-lg fa-bars-filter"></i> <span [class.tag-label-primary.tag-label-rounded]="!isAllFilterByLegalStatus">{{!isAllFilterByLegalStatus? appliedLegalStatusFilter.length : 'All'}}</span>
          </div>
          <div class="figma-dropdown-content figma-dropdown-content-right radius-big p-y-spacing-big p-x-spacing-md">
            <div class="figma-dropdown-label content-heading-h6 content-color-tertiary">Filter by</div>
            <div class="">
              <label *ngFor="let s of legalStatusFilter" [class.active]="s.selected" class=" figma-checkbox figma-dropdown-item figma-dropdown-item-hover content-capitalize content-body-medium">
                  <input type="checkbox" [checked]="s.selected" (change)="filterListByLegalStatus($event, s)" /><span class=" p-r-spacing-xxx-big">{{s.name}}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="current-list-content flex-grow-1 p-r-spacing-xxx-s">
    <div class="w-100" *ngIf="!isLoading">
      <div id="current-list-documents">
        <div class="current-list-document current-list-family-id-{{!d.general.obfuscated ? d.general.docdb_family_id : 'xxx'}} current-list-publication-{{d.general.raw_publication_number}}"
             *ngFor="let d of documents; let last = last; index as i;"
             [class.active-document]="isActiveDocument(d)"
             [class.obfuscated-document]="d.general.obfuscated"
             (click)="openPatent(d, i)">
          <div class="current-list-document-top justify-content-between">
            <div class="pubnumber d-flex gap-spacing-xx-s">
              <span *ngIf="d.general?.rank && !this.userService.isFreeUser()"
                class="tag-label-secondary tag-label-rounded rank">{{d.general?.rank}}</span>
              <div *ngIf="hasLinksToBooleanSearch" class="tag-label-rounded-blue" [ngbTooltip]="!d.general.obfuscated ? 'Open in a new tab' : ''" tooltipClass="white-tooltip" (click)="openPatentNewTab($event, d, i);">
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
              </div>

              <span *ngIf="d?.general?.raw_publication_number" class="content-color-tertiary content-heading-h6 d-flex align-items-center">
                <span class="m-r-spacing-xx-s" [ngClass]="getFlagIcon(d?.general?.raw_publication_number)"></span>
                <span #pubNumber [ngbTooltip]="isTruncated(pubNumber) ? d?.general?.raw_publication_number : ''" tooltipClass="white-tooltip">{{d?.general?.raw_publication_number}}</span>
              </span>              
            </div>

            <div class="status">
              <span *ngFor="let ls of legalStatuses[getDocumentKey(d)]" class="badge-{{ls.name | lowercase}} badge-small m-r-spacing-x-s"
                [ngbTooltip]="!d.general.obfuscated ? ls.tooltip : ''" tooltipClass="white-tooltip">{{ ls.name | titlecase}}</span>
            </div>
          </div>
          <div class="current-list-document-title" [ngClass]="{'content-heading-h5': !isScreenSmall(), 'content-heading-h6': isScreenSmall()}">
            <span>{{d.bibliographic.title}}</span>
          </div>
          <div class="current-list-document-detail">
            <div *ngIf="isActiveDocument(d)" class="currently-viewing content-label-medium">
              Currently viewing
            </div>
            <div *ngIf="!isActiveDocument(d)">
              <div class="d-flex align-items-center" [class.justify-content-between]="d.general?.similarity_index">
                <span class="content-color-tertiary" [ngClass]="{'content-label-xsmall': !isScreenSmall(), 'content-body-small': isScreenSmall()}">Priority date - {{d?.bibliographic?.priority_date || 'N/A'}}</span>
                <span class="tag-label-primary tag-label-rounded content-label-xsmall" *ngIf="d.general?.similarity_index"
                  [ngbTooltip]="!d.general.obfuscated ? 'Based on your search, this patent is highly similar in terms of both its semantic and lexical aspects.' : ''"
                  tooltipClass="white-tooltip">{{d.general?.similarity_index}}</span>
              </div>
              <div class="m-t-spacing-x-s content-color-tertiary" [ngClass]="{'content-label-xsmall': !isScreenSmall(), 'content-body-small': isScreenSmall()}">Applicants -
                {{d?.bibliographic?.applicants?.join(', ') || 'N/A'}}</div>
            </div>
          </div>
        </div>
        <div class="empty-current-ist" *ngIf="documents.length === 0">
          <div class="d-flex flex-column align-items-center">
            <div class="p-b-spacing-xx-big">
              <div class="feature-container feature-xl"><i class="feature-icon fa-light fa-list-ul"></i></div>
            </div>
            <div class="content-color-tertiary p-y-spacing-sm">
              <span class="d-block p-b-spacing-sm">There are no document available in visible current list.</span>
              <span>Change the filter to see other documents</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="loading-message" *ngIf="isLoading">
      Current list are loading...
    </div>
  </div>
  <div class="current-list-navigation d-flex gap-3 pt-3 pb-1" *ngIf="isPreviousAvailable || isNextAvailable || isBackToFirstAvailable">
    <div id="button-patent-previous" class="button-main-secondary-grey w-100 gap-2" [class.button-small]="isScreenSmall()" (click)="loadPreviousPage()" *ngIf="isPreviousAvailable"><i class="fa-regular fa-arrow-left"></i><span>Previous page</span></div>
    <div id="button-patent-next" class="button-main-primary w-100 gap-2" [class.button-small]="isScreenSmall()" (click)="loadNextPage()" *ngIf="isNextAvailable"><span>Next page</span><i class="fa-regular fa-arrow-right"></i></div>
    <div id="button-patent-first" class="button-main-primary w-100 gap-2" [class.button-small]="isScreenSmall()" (click)="loadFirstPage()" *ngIf="isBackToFirstAvailable"><span>Back to first</span></div>
  </div>
</div>
