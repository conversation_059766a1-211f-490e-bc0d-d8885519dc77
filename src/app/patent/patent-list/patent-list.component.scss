@import 'scss/figma2023/index';

.current{
    &-list{
        &-content{
            display: flex;
            overflow: auto;
            width: 100%;
        }
        &-header{
            display: flex;
            flex-direction: column;
            width: 100%;
        }
        &-document{
            padding: $spacing-system-spacing-big $spacing-system-spacing-md;
            margin-bottom: $spacing-system-spacing-sm;
            border-radius: $radius-sm;
            border: 1px solid $colours-border-subtle;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            transition: all ease-in-out .2s;
            &-top{
                display: flex;
                .truncate {
                    max-width: 90px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: inline-block;
                    vertical-align: middle;
                }
            }
            &-title{
                padding-top: $spacing-system-spacing-md;
                @include add-ellipsis(2);
            }
            &-detail{
                margin-top: $spacing-system-spacing-md;
                border-top: 1px solid $colours-border-subtle;
                padding-top: $spacing-system-spacing-md;
                .currently-viewing{
                    border-radius: $spacing-system-spacing-xx-s;
                    color: $colours-content-content-primary;
                    background-color: $colours-background-bg-tertiary;
                    padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
                    width: 100%;
                    text-align: center;
                }
            }

            .tag-label-rounded.rank {
                min-width: 24px;
                height: 24px;
            }

            .tag-label-rounded-blue {
                border-radius: 1rem;
                width: 1.5rem;
                height: 1.5rem;
                gap: 0.625rem; 
                background-color: $colour-blue-brand-500; 
                color: $colour-grey-050; 
                box-shadow: 0px 1px 3px 0px $colour-transparency-15grey;
                display: none;
                font-size: $font-size-sm;
            }

            &.active-document {
                cursor: auto;
                border: 1.5px solid $colours-border-contrast;
            }

            &:hover{
                background-color: $colour-grey-100;
                border-color: $colour-grey-700;
            }

            &:not(.active-document):hover {
                .tag-label-secondary.tag-label-rounded {
                    display: none;
                }
                .tag-label-rounded-blue {
                    display: flex;
                    align-items: center;
                    justify-content: center
                }
            }

            &.obfuscated-document {
                filter: blur(3px);
                opacity: .6;
                cursor: default;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
            }
        }
    }
    
}
.filter-option{
    min-width: 240px;
    .active{
        font-weight: 700;
        &::after{
          font-family: "Font Awesome 6 Pro";
          font-weight: 300;
          box-sizing: border-box;
          content: "\f00c";
          float: right;
        }
    }
}
.loading-current-list {
    .current-list-search,
    .current-list-menu,
    .current-list-content {
      * {
        display: none !important;
      }
      min-height: 56px;

      @include loading-box-style();
    }

  .loading-message{
    display: flex;
    height: 100%;
    align-items: center;
    justify-content: center;
  }
}

.current-list-navigation{
    border-radius: 4px 4px 0 0;
    background: $colours-background-bg-primary;
    box-shadow: 0px 0px 6px 0px #00000040;

    .button-main-primary{
        border: 1px solid transparent;
    }
}