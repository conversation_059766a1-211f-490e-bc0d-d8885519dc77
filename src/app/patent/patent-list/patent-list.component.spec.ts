import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { PatentListComponent } from './patent-list.component';
import { SemanticSearchStoreService } from '@core/store';
import { SemanticSearchService } from '@core/services';
import { CountSerialPipe } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentListComponent', () => {
  let component: PatentListComponent;
  let fixture: ComponentFixture<PatentListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentListComponent ],
      providers: [CountSerialPipe, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentListComponent);
    component = fixture.componentInstance;
    component.searchService = TestBed.inject(SemanticSearchService);
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    component.patentViewer.documents = [];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
