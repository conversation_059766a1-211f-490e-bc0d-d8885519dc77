import { AfterViewChecked, AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { CountSerialPipe, PaginationMetadata, Patent, PatentTableService, PublicationService } from '@core';
import { BooleanSearchService, CitationSearchService, PatentService, PatentViewService, SemanticSearchService, UserService } from '@core/services';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { BaseStoreService } from '@core/store';
import { Subscription } from 'rxjs';
import { CurrentListFilterEnum, PatentLegalStatusEnum } from './../types';

@Component({
  selector: 'app-patent-list',
  templateUrl: './patent-list.component.html',
  styleUrls: ['./patent-list.component.scss']
})
export class PatentListComponent implements OnInit, OnDestroy, AfterViewInit, AfterViewChecked {
  @Input() searchService: SemanticSearchService | CitationSearchService | BooleanSearchService;
  @Input() storeService: BaseStoreService;
  @Output() openDocument: EventEmitter<{ documentId: number, publicationNumber: string, currentListIndex: number }> = new EventEmitter();
  @Output() openDocumentNewTab: EventEmitter<{ documentId: number, publicationNumber: string, currentListIndex: number }> = new EventEmitter();

  listFilterEnum = CurrentListFilterEnum;
  isDisabledClick: boolean;
  isLoading: boolean;
  subscriptions = new Subscription();
  hasLinksToBooleanSearch: boolean = true;
  currentListPageSize: number = 25;
  legalStatuses = {};
  legalStatusFilter: {name: string, selected?: boolean}[] = [];

  private originalParams;

  @ViewChildren('pubNumber') pubNumbers: QueryList<ElementRef>;

  constructor(
    public patentViewer: PatentViewService,
    public userService: UserService,
    private publicationService: PublicationService,
    private patentService: PatentService,
    private patentTableService: PatentTableService,
    private countSerialPipe: CountSerialPipe,
    private cdr: ChangeDetectorRef
  ) {
    this.hasLinksToBooleanSearch = this.userService.isNotExternalUser();
  }

  get documents() {
    return this.patentViewer.documents;
  }
  get activeDocumentID(): number {
    return this.patentViewer.activeDocumentId;
  }
  get activePublicationName(): string {
    return this.patentViewer.activePublicationName
  }
  get appliedLegalStatusFilter(){
    return this.legalStatusFilter.filter(o => !!o.selected)
  }
  get isAllFilterByLegalStatus(): boolean{
    return this.legalStatusFilter.every(o => o.selected);
  }

  get isPreviousAvailable(){
    if(this.patentViewer.isFocalPatent || this.documents.length ==0){
      return false;
    }
    return this.patentViewer.searchParams['params']?.page != 1;
  }

  get isNextAvailable(){
    if(this.patentViewer.isFocalPatent || this.documents.length ==0){
      return false;
    }
    return this.patentViewer.searchParams['params']?.page != this.patentViewer.pagination?.last_page;
  }

  get isBackToFirstAvailable(){
    if(this.patentViewer.isFocalPatent || this.documents.length ==0){
      return false;
    }
    return this.patentViewer.searchParams['params']?.page > 1 &&
            this.patentViewer.searchParams['params']?.page == this.patentViewer.pagination?.last_page;
  }

  get documentsCount(): number{
    return this.pagination?.total_hits;
  }

  ngOnInit(): void {
    this.originalParams = structuredClone(this.patentViewer.searchParams);
    if (!this.patentViewer.searchParams['params']) {
      return;
    }
    this.patentViewer.searchParams['params'].page_size = this.currentListPageSize;
    if(this.patentViewer.currentListIndex > this.currentListPageSize){
      this.patentViewer.searchParams['params'].page = Math.ceil(this.patentViewer.currentListIndex / this.currentListPageSize);
    }
    this.loadPage();
  }

  ngOnDestroy(): void {
    if (this.subscriptions) {
      this.subscriptions.unsubscribe();
    }
    this.patentViewer.searchParams = structuredClone(this.originalParams);
  }
  ngAfterViewInit(): void {
    if (this.activeDocumentID) {
      this.scrollDocument(this.activeDocumentID);
    }
    this.checkTruncation();
    if (this.storeService.isPublications) {
      this.legalStatusFilter.push(...[{name: 'Granted', selected: true}, {name: 'Pending', selected: true}, {name: 'Dead', selected: true}, {name: 'Unknown', selected: true}]);
    } else {
      this.legalStatusFilter.push(...[{name: 'Alive', selected: true}, {name: 'Dead', selected: true}, {name: 'Unknown', selected: true}]);
    }
  }

  ngAfterViewChecked() {
    this.checkTruncation();
  }

  private checkTruncation() {
    this.pubNumbers.forEach(pubNumber => {
      const gap = 5;
      const element = pubNumber.nativeElement;
      const parentElement = element.closest('.current-list-document-top');
      const pubNumberWidth = element.parentElement.clientWidth;
      const statusElement = parentElement.querySelector('.status');
      const statusWidth = statusElement.clientWidth;
      const openNewTab = parentElement.querySelector('.fa-arrow-up-right-from-square');
      const openNewTabWidth = openNewTab && openNewTab ? openNewTab.clientWidth + gap : 0;

      const availableWidth = parentElement.clientWidth;
      const requiredWidth = pubNumberWidth + statusWidth + openNewTabWidth + gap;

      if (availableWidth < requiredWidth) {
        element.setAttribute('ngbTooltip', element.textContent);
        element.classList.add('truncate');
      } else {
        element.removeAttribute('ngbTooltip');
        element.classList.remove('truncate');
      }
    });
    this.cdr.detectChanges();
  }

  isTruncated(element: HTMLElement) {
    return element.classList.contains('truncate');
  }

  loadPage(): void {
    this.isLoading = true;
    if(this.storeService.searchHash){
      const params = this.patentViewer.searchParams['params'];
      params['show_general'] = 1;
      params['show_bibliographic'] = 1;
      const payload = this.patentViewer.searchParams['payload'];
      this.subscriptions.add(
        this.searchService.getCachedSearch(this.storeService.searchHash, payload, params)
          .subscribe({
            next: ({data}) => {
              this.loadDocumentsNewPage(data.publications? this.publicationService.publicationsToDocuments(data.publications) : data.documents);
              this.patentViewer.pagination = data.page;
            }
          })
      );
    }

    this.updateLegalStatues();
  }

  private loadDocumentsNewPage(documents) {
    this.patentViewer.documents = documents;
    this.isLoading = false;
    this.scrollDocument(this.activeDocumentID);
    this.updateLegalStatues();
  }

  getFlagIcon(publicationNumber: string) {
    return this.patentService.getFlagCssByPublication(publicationNumber, FlagSizeEnum.MD).toLowerCase();
  }

  openPatent(doc: Patent, patentIndex: number) {
    if (doc.general.obfuscated) {
      return;
    }
    const publicationNumber = this.getPublicationNumber(doc);
    const docId = Number(doc.general.docdb_family_id);
    if (docId == this.activeDocumentID) {
      if (this.storeService.isPublications) {
        if (this.patentViewer.activePublicationName === publicationNumber) {
          return;
        }
      } else {
        return;
      }
    }
    this.patentViewer.setActiveDocumentId(docId);
    this.patentViewer.setPublicationName(publicationNumber);
    this.openDocument.emit({documentId: docId, publicationNumber, currentListIndex: this.getCurrentListIndex(patentIndex)});
  }

  loadPreviousPage() {
    this.patentViewer.searchParams['params'].page--;
    this.loadPage();
  }

  loadNextPage(){
    this.patentViewer.searchParams['params'].page++;
      this.loadPage();
  }

  loadFirstPage(){
    this.patentViewer.searchParams['params'].page = 1;
    this.loadPage();
  }

  get pagination(): PaginationMetadata {
    return this.patentViewer.pagination ?? this.storeService.pagination;
  }

  filterListByLegalStatus(event, status){
    event.preventDefault();
    event.stopPropagation();
    if(this.legalStatusFilter){
      this.legalStatusFilter.forEach(o => { if(o.name === status.name){ o.selected = !o.selected; } });
    }
    let freeFilters = this.patentViewer.searchParams['payload']?.search_filters?.free_text_query;

    const legalStatusQuery = `LEGAL_STATUS=(${this.appliedLegalStatusFilter.map(o => o.name.toUpperCase()).join(' OR ')})`;

    if (!freeFilters) {
      const payload = this.patentViewer.searchParams['payload'] ?? {search_filters: {}};
      if(!('search_filters' in payload)){
        payload['search_filters'] = {};
      }
      payload['search_filters']['free_text_query'] = legalStatusQuery;
      this.patentViewer.searchParams['payload'] = payload;
    } else {
      const regex = /LEGAL_STATUS(=|<>)(\([^\)]+\)|[^\s]+)/;

      if (this.appliedLegalStatusFilter.length===0) {
        const q = `LEGAL_STATUS<>(${this.legalStatusFilter.map(o => o.name.toUpperCase()).join(' OR ')})`;
        freeFilters = freeFilters.replace(regex, q);
      } else if (regex.test(freeFilters)) {
        freeFilters = freeFilters.replace(regex, legalStatusQuery);
      } else {
        freeFilters += ` AND ${legalStatusQuery}`;
      }
      this.patentViewer.searchParams['payload']['search_filters']['free_text_query'] = freeFilters;
    }
    this.loadPage();
  }

  scrollDocument(documentId) {
    setTimeout(() => {
      const el = document.getElementsByClassName(`current-list-family-id-${documentId}`);
      if (el && el[0]) {
        el[0].scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
      }
    }, 500);
  }

  openPatentNewTab(event, doc: Patent, patentIndex: number) {
    if (doc.general.obfuscated) {
      return;
    }
    event.preventDefault();
    event.stopPropagation();
    this.storeService.storeSearchData(this.documents, this.pagination);
    this.openDocumentNewTab.emit({
      documentId: Number(doc.general.docdb_family_id),
      publicationNumber: this.getPublicationNumber(doc),
      currentListIndex: this.getCurrentListIndex(patentIndex)
    });
  }

  isActiveDocument(document): boolean{
    return this.activePublicationName ? this.getPublicationNumber(document) == this.activePublicationName :document.general.docdb_family_id == this.activeDocumentID;
  }

  private getPublicationNumber(doc: Patent): string {
    return this.storeService.isPublications ? this.patentTableService.getPublicationNumber(doc, true) : null;
  }

  getDocumentKey(document) {
    return this.storeService.isPublications ? document.general.raw_publication_number : document.general.docdb_family_id;
  }

  private getCurrentListIndex(patentIndex: number): number {
    return this.countSerialPipe.transform(this.pagination, patentIndex);
  }

  private updateLegalStatues() {
    for (const document of this.documents) {
      this.legalStatuses[this.getDocumentKey(document)] = this.patentService.getLegalStatuses(document).map(ls => {
        if (this.storeService.isPublications) {
          return this.patentService.getPatentLegalStatusIcon(ls);
        }
        return this.patentService.getFamilyLegalStatusIcon(ls);
      });
    }
  }

  isScreenSmall(): boolean {
    return window.innerWidth < 1024;
  }
}
