<app-patent-side-section-header title="Requested" [isSubHeader]="true" (closeEvent)="onCloseClicked()"
                                class="p-spacing-md d-block">
</app-patent-side-section-header>

<div class="w-100 divider m-spacing-none"></div>

<div appScrollbarDetector
     class="radius-sm d-flex flex-column figma-bg-primary flex-grow-1 overflow-auto">
  <app-patent-request-base-view [task]="task" [hasActions]="canEdit || canDelete" [showStatus]="false"
                                [showTopics]="!!task.deadline" avatarSize="small"
                                class="p-x-spacing-md p-y-spacing-sm">
    <ng-container actions>
      <div *ngIf="canEdit" class="p-x-spacing-sm p-t-spacing-sm"
           [ngClass]="{'p-b-spacing-xx-s': canDelete, 'p-b-spacing-sm': !canDelete}"
           (click)="onEditClicked()">
        <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
          <i class="fa-regular fa-pen fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Edit request
        </div>
      </div>
      <div *ngIf="canEdit && canDelete" class="popover-divider"></div>
      <div *ngIf="canDelete" class="p-x-spacing-sm p-b-spacing-sm"
           [ngClass]="{'p-t-spacing-xx-s': canEdit, 'p-t-spacing-sm': !canEdit}"
           (click)="onDeleteClicked($event)">
        <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
          <i class="fa-regular fa-trash-can fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Delete
        </div>
      </div>
    </ng-container>
  </app-patent-request-base-view>

  <div class="w-100 divider m-spacing-none m-b-spacing-md"></div>

  <ng-container *ngFor="let ta of myAssignments;">
    <ng-container [ngTemplateOutlet]="taskTemplate" [ngTemplateOutletContext]="{ task: ta, canAnswerRequest: true }"></ng-container>
  </ng-container>

  <div *ngIf="myAssignments.length && notMyAssignments.length" class="w-100 divider m-spacing-none m-b-spacing-md"></div>

  <ng-container *ngFor="let ta of notMyAssignments;">
    <ng-container [ngTemplateOutlet]="taskTemplate" [ngTemplateOutletContext]="{ task: ta, canAnswerRequest: false }"></ng-container>
  </ng-container>

</div>

<ng-template #taskTemplate let-task="task" let-canAnswerRequest="canAnswerRequest">
  <div class="d-flex flex-column align-items-stretch p-spacing-md">
    <div class="d-flex flex-column gap-spacing-sm">
      <div class="d-flex gap-spacing-sm align-items-center">
        <div *ngIf="isGroupTaskAssignment(task)" class="flex-fill">
          <app-teams-selector [showMe]="true"
                              [formControl]="getAssigneeFormControl(task)"
                              [onlyDisplay]="true"
                              [initialTeams]="[task.assignee]"
                              [placeholder]="task.assignee | userTitle"
                              [ngbTooltip]="task.answered_user ? 'The rating was done by ' + (task.answered_user | userTitle) + '.' : null"
                              tooltipClass="white-tooltip" placement="top">
          </app-teams-selector>
        </div>

        <ng-container *ngIf="isUserTaskAssignment(task)">
          <app-user-avatar [user]="task.assignee" [hasSubTitle]="false" [showTooltip]="false" size="small">
          </app-user-avatar>

          <div class="flex-fill d-flex flex-column">
            <div class="content-heading-h5 content-color-primary">{{ task.assignee | userTitle }}</div>

            <div *ngIf="!isLoadingReadDocuments" class="content-body-xsmall content-color-tertiary">
                <span *ngIf="isMe(task.assignee)">
                  Now
                </span>
              <span *ngIf="!isMe(task.assignee) && getReadDocument(task.assignee)?.last_read">
                  {{ getReadDocument(task.assignee).last_read.toString() | timeReadable }}
                </span>
            </div>
          </div>
        </ng-container>

        <div *ngIf="canAnswerRequest">
          <span class="button-main-tertiary-grey content-label-small button-square d-flex align-items-center"
                (click)="onAnswerRequestButtonClicked($event, task)">
            <span>Start</span>
            <div class="m-l-spacing-xx-s"><i class="fa-regular fa-chevron-right"></i></div>
          </span>
        </div>

        <div *ngIf="!canAnswerRequest" class="patent-task-status content-label-xsmall cursor-default badge-grey badge-small"
             [ngbTooltip]="getTaskAssignmentStatusTooltip(task.status)" tooltipClass="white-tooltip">
          {{ getTaskAssignmentStatusLabel(task.status) }}
        </div>
      </div>

    </div>
  </div>
</ng-template>
