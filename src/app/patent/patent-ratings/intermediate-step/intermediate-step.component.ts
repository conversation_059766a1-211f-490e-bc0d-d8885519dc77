import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
  PluralizePipe,
  ReadDocument,
  ReadDocumentsService,
  TaskAssignmentModel,
  TaskModel,
  TaskService,
  TeamUser,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core';
import {
  getTaskAssignmentStatusLabel,
  getTaskAssignmentStatusTooltip,
  ShowRatingsEventParams
} from '@patent/patent-ratings/shared/types';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { finalize } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';


@Component({
  selector: 'app-intermediate-step',
  templateUrl: './intermediate-step.component.html',
  styleUrl: './intermediate-step.component.scss'
})
export class IntermediateStepComponent implements OnInit, OnDestroy {
  @Input() patentFamilyId: number;
  @Output() hideIntermediateStepEvent: EventEmitter<ShowRatingsEventParams> = new EventEmitter<ShowRatingsEventParams>();
  @Output() answerRequestEvent: EventEmitter<TaskModel> = new EventEmitter<TaskModel>();

  isDeleting: boolean = false;
  isLoadingReadDocuments: boolean = true;

  readDocuments: ReadDocument[] = [];
  readDocumentUsers: TeamUser[] = [];

  readonly getTaskAssignmentStatusLabel = getTaskAssignmentStatusLabel;
  readonly getTaskAssignmentStatusTooltip = getTaskAssignmentStatusTooltip;

  private subscriptions = new Subscription();

  constructor(
    public taskService: TaskService,
    private toastService: ToastService,
    private userService: UserService,
    private ngbModal: NgbModal,
    private readDocumentsService: ReadDocumentsService
  ) {
  }

  private _task: TaskModel = null;

  get task(): TaskModel {
    return this._task;
  }

  @Input()
  set task(task: TaskModel) {
    this._task = task || {} as TaskModel;
  }

  get canEdit(): boolean {
    return this.taskService.isTaskCreatedByMe(this.task);
  }

  get canDelete(): boolean {
    return this.taskService.isTaskCreatedByMe(this.task);
  }

  get myAssignments(): TaskAssignmentModel[] {
    return this.taskService.myAssignments(this.task);
  }

  get notMyAssignments(): TaskAssignmentModel[] {
    return this.taskService.notMyAssignments(this.task);
  }

  ngOnInit() {
    this.loadTeamReadDocuments();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onCloseClicked() {
    this.hideIntermediateStepEvent.emit({refresh: true} as ShowRatingsEventParams);
  }

  isUserTaskAssignment(ta: TaskAssignmentModel): boolean {
    return this.taskService.isUserTaskAssignment(ta);
  }

  isGroupTaskAssignment(ta: TaskAssignmentModel): boolean {
    return this.taskService.isGroupTaskAssignment(ta);
  }

  onEditClicked() {
    this.hideIntermediateStepEvent.emit({showRatingForm: true, currentRatingTask: this.task} as ShowRatingsEventParams);
  }

  getAssigneeFormControl(ta: TaskAssignmentModel): FormControl {
    return new FormControl({value: [ta.assignee], disabled: true});
  }

  onDeleteClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isDeleting) {
      return;
    }

    this.isDeleting = true;
    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'lg', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Delete rating request',
      description: 'This request will be removed from the profiles of the current pending assignees',
      question: 'Are you sure you want to delete this request?',
      confirmButton: 'Delete',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.deleteTask();
      } else {
        this.isDeleting = false;
      }
    }, () => {
      this.isDeleting = false;
    });
  }

  getReadDocument(user: TeamUser): ReadDocument {
    return this.readDocuments.find(doc => doc.user_id === user.id);
  }

  isMe(user: TeamUser): boolean {
    return this.userService.isMe(user);
  }

  onAnswerRequestButtonClicked(event: MouseEvent, ta: TaskAssignmentModel) {
    event.preventDefault();
    event.stopPropagation();

    const clonedTask = this.taskService.cloneTaskWithAssignments(this.task, [ta]);
    this.answerRequestEvent.emit(clonedTask);
  }

  private deleteTask() {
    const subscription = this.taskService.deleteTask(this.task.id)
      .pipe(
        finalize(() => this.isDeleting = false)
      )
      .subscribe({
        next: () => {
          this.hideIntermediateStepEvent.emit({refresh: true} as ShowRatingsEventParams);
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: 'The request was deleted',
            body: 'Your request was deleted successfully!',
            delay: 5000
          });
        },
        error: ({error}) => {
          console.log(error);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to delete the request',
            body: `There was an error in deleting the request.<br/>${error.message}`,
            delay: 10000
          });
        }
      });
    this.subscriptions.add(subscription);
  }

  private loadTeamReadDocuments() {
    this.isLoadingReadDocuments = true;
    this.readDocumentUsers = [];
    this.readDocuments = [];
    const payload = {
      document_id: this.patentFamilyId,
      load_all: 1,
      sort_by: 'last_read',
      sort_order: 'desc',
    };
    const subscription = this.readDocumentsService.getTeamReadDocuments(payload)
      .subscribe({
        next: ({read_documents}) => {
          this.readDocuments = read_documents;
          this.readDocumentUsers = read_documents.map(doc => doc.user).filter(user => user);
          this.isLoadingReadDocuments = false;
        },
        error: (error) => {
          console.error(error);
          this.isLoadingReadDocuments = false;
        }
      });
    this.subscriptions.add(subscription);
  }
}
