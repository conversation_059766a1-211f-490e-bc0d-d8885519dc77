<ng-container *ngIf="showRatingsList">
  <app-patent-side-section-header [title]="sideSectionEnum.RATINGS"
                                  (closeEvent)="onCloseSectionClicked()"
                                  class="p-t-spacing-md p-x-spacing-md m-b-spacing-big d-block">
  </app-patent-side-section-header>

  <div class="figma-side-section-content w-100 flex-grow-1">
    <ng-container *ngIf="!isLoading; else fullLoadingTemplate">
      <ng-container
        *ngIf="displayedRatings.length || answerableRequests.length || createdRequests.length || otherRequests.length || (allRatings?.length > 0 && !isDefaultFilter) ; then ratingsTemplate; else noRatingsTemplate">
      </ng-container>
    </ng-container>
  </div>
</ng-container>

<app-patent-rating-form *ngIf="showRatingForm"
                        [task]="currentRatingTask"
                        [originalTask]="getOriginalRequest(currentRatingTask)"
                        [patentFamilyId]="documentId"
                        [storeService]="storeService"
                        (hideRatingFormEvent)="displayRatingsList($event)"
                        class="figma-side-section-content w-100 h-100 d-flex flex-column">
</app-patent-rating-form>

<app-patent-request-view *ngIf="showRatingView"
                         [task]="currentRatingTask"
                         (hideRequestViewEvent)="onHideRequestViewEvent($event)"
                         class="figma-side-section-content w-100 h-100 d-flex flex-column">
</app-patent-request-view>

<app-patent-request-answer *ngIf="showRatingAnswer"
                           [task]="currentRatingTask"
                           (hideRequestAnswerEvent)="displayRatingsList($event)"
                           class="figma-side-section-content w-100 h-100 d-flex flex-column">
</app-patent-request-answer>

<app-intermediate-step *ngIf="showIntermediateStep"
                       [task]="currentRatingTask"
                       [patentFamilyId]="documentId"
                       (hideIntermediateStepEvent)="displayRatingsList($event)"
                       (answerRequestEvent)="onAnswerRequestEvent($event)"
                       class="figma-side-section-content w-100 h-100 d-flex flex-column">
</app-intermediate-step>

<ng-template #fullLoadingTemplate>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch ratings-loading-template p-x-spacing-md">
    <div class="d-flex justify-content-between ratings-loading-top"></div>
    <div class="ratings-loading-main-content flex-grow-1 d-flex align-items-center justify-content-center">
      <div class="content-heading-h6 content-color-secondary">Ratings are loading...</div>
    </div>
  </div>
</ng-template>

<ng-template #noRatingsTemplate>
  <div class="d-flex flex-column align-items-stretch ratings-no-results p-x-spacing-md">
    <div class="d-flex flex-column align-items-center justify-content-start m-x-spacing-big">
      <i class="fa-regular fa-circle-star fa-3x fa-border"></i>

      <div class="content-body-medium content-color-tertiary text-center m-t-spacing-xx-big">
        There are no ratings for this patent yet.
      </div>
      <div class="content-body-medium content-color-tertiary text-center m-t-spacing-sm m-b-spacing-sm">
        This section will display all ratings assigned to you or requested by you.
      </div>

      <button class="button-main-primary button-pill button-xlarge content-label-large m-t-spacing-xx-big w-100"
              (click)="onRateMyselfClicked()">
        Rate it myself
      </button>

      <button class="button-main-secondary-grey button-pill button-xlarge content-label-large m-t-spacing-md w-100"
              (click)="onRequestRatingClicked()">
        Request a rating
      </button>
    </div>
  </div>
</ng-template>

<ng-template #ratingsTemplate>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch">
    <div class="w-100 d-flex justify-content-between align-items-center p-b-spacing-md p-x-spacing-md">
      <div class="content-heading-h6 content-color-tertiary m-r-spacing-sm">
        {{ displayedRatings?.length }} {{ 'Rating' | pluralize: displayedRatings?.length }}
      </div>

      <div class="d-flex justify-content-end align-items-center">
        <div class="p-r-spacing-sm d-flex gap-spacing-xx-s border-0 border-r-1 border-r-subtle">
          <div [ngbTooltip]="tooltipTextForFilter" tooltipClass="white-tooltip" class="figma-dropdown">
            <button [ngbPopover]="popoverFilterRatingsTemplate" [autoClose]="true"
                    popoverClass="context-menu-popper" container="body" placement="bottom-right"
                    class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small">
              <i class="fa-regular fa-lg fa-bars-filter"></i>
              <ng-container *ngIf="isDefaultFilter">All</ng-container>
              <span *ngIf="!isDefaultFilter" class="badge-circle content-label-small content-color-reversed-grey figma-bg-reversed">1</span>
            </button>
          </div>

          <div [ngbTooltip]="tooltipTextForSort" tooltipClass="white-tooltip" class="figma-dropdown">
            <button [ngbPopover]="popoverSortRatingsTemplate" [autoClose]="true"
                    popoverClass="context-menu-popper" container="body" placement="bottom-right"
                    class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small button-square">
              <i class="fa-regular fa-lg fa-arrow-up-arrow-down"></i>
            </button>
          </div>
        </div>

        <div ngbTooltip="Assign rating" tooltipClass="white-tooltip">
          <button #popoverAddRatingTask="ngbPopover" [ngbPopover]="popoverAddRatingTaskTemplate" [autoClose]="true"
                  popoverClass="context-menu-popper" container="body" placement="bottom-right"
                  class="button-main-primary button-pill m-l-spacing-md" style="width: 2rem"
                  [ngClass]="{'disabled': isFindingSelfRating}">
            <i class="fa-regular" [ngClass]="popoverAddRatingTask.isOpen() ? 'fa-xmark' : 'fa-plus'"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="w-100 divider m-spacing-none"></div>

    <div id="ratings-list" appScrollbarDetector
         class="ratings-list flex-grow-1 overflow-auto m-y-spacing-md p-b-spacing-md d-flex flex-column gap-spacing-sm"
         (scroll)="onRatingsListScrolled($event)">
      <div *ngIf="answerableRequests.length || createdRequests.length || otherRequests.length"
           class="p-x-spacing-md gap-spacing-sm">
        <div class="radius-sm p-y-spacing-xx-s p-x-spacing-sm d-flex justify-content-between figma-bg-secondary cursor-pointer"
             [ngbTooltip]="isRequestsExpanded ? 'Collapse' : 'Expand'" tooltipClass="white-tooltip"
             appMouseHover hoverClass="figma-bg-tertiary"
             (click)="collapseRequests()">
          <div class="content-heading-h6 content-style-semi-bold d-flex align-items-center">
            {{ answerableRequests.length + createdRequests.length + otherRequests.length }} {{ 'Request' | pluralize: answerableRequests.length + createdRequests.length + otherRequests.length }}
          </div>
          <span class="button-square button-small d-flex justify-content-center align-items-center">
            <i class="fa-regular fa-chevron-up cursor-pointer p-spacing-s fa-fw fa-1x"
               [ngClass]="{'fa-chevron-up': isRequestsExpanded, 'fa-chevron-down': !isRequestsExpanded}"></i>
          </span>
        </div>

        <div *ngIf="isRequestsExpanded"
             class="w-100 d-flex flex-column align-items-stretch gap-spacing-sm p-spacing-sm">
          <app-patent-request-card *ngFor="let request of answerableRequests;"
                                   [task]="request"
                                   [isAnswerable]="true"
                                   (viewRequestEvent)="onViewAnswerableRequestEvent($event)"
                                   class="d-block">
          </app-patent-request-card>

          <app-patent-request-card *ngFor="let request of createdRequests;"
                                   [task]="request"
                                   [isAnswerable]="false"
                                   (viewRequestEvent)="onViewCreatedRequestEvent($event)"
                                   class="d-block">
          </app-patent-request-card>

          <app-patent-request-card *ngFor="let request of otherRequests;"
                                   [task]="request"
                                   [isAnswerable]="false"
                                   (viewRequestEvent)="onViewOtherRequestEvent($event)"
                                   class="d-block">
          </app-patent-request-card>
        </div>
      </div>

      <div *ngIf="displayedRatings.length">
        <ng-container *ngFor="let rating of displayedRatings; let first = first;">
          <div *ngIf="!first" class="w-100 divider m-spacing-none"></div>
          <app-patent-request-result-card [task]="rating"
                                          (editRatingEvent)="onEditRatingEvent(rating)"
                                          (ratingDeletedEvent)="displayRatingsList($event)"
                                          [attr.data-assignment-id]="rating.assignments[0].id"
                                          class="d-block p-spacing-md figma-bg-primary"
                                          appMouseHover hoverClass="figma-bg-transition">
          </app-patent-request-result-card>
        </ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #popoverFilterRatingsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary">Filter ratings by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium"
       *ngFor="let op of filterOptions" [class.active]="currentFilter === op"
       (click)="onFilterOptionClicked(op)">
    {{ op }}
  </div>
</ng-template>

<ng-template #popoverSortRatingsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Sort by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium p-r-spacing-s"
       *ngFor="let op of sortOptions" [class.active]="currentSort === op"
       (click)="onSortOptionClicked(op)">
    <span class="p-r-spacing-xxx-big">{{ op }} </span>
  </div>
</ng-template>

<ng-template #popoverAddRatingTaskTemplate>
  <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium"
       (click)="onRequestRatingClicked()">
    <span class="p-r-spacing-xxx-big">Request a rating</span>
  </div>
  <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium"
       (click)="onRateMyselfClicked()">
    <span class="p-r-spacing-xxx-big">Rate it myself</span>
  </div>
</ng-template>
