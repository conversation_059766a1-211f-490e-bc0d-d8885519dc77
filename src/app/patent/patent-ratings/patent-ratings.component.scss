@import 'scss/figma2023/index';
@import 'scss/layout2021/mixins';
@import 'shared/style';

$noRatingsPadding: (
  'xs': $spacing-system-spacing-xx-s,
  'sm': $spacing-system-spacing-sm,
  'md': $spacing-system-spacing-md,
  'lg': $spacing-system-spacing-big,
  'xl': $spacing-system-spacing-x-lg,
  'xxl': $spacing-system-spacing-xx-lg
);

@mixin template-box-style() {
  background: $colour-grey-200;
  border-radius: $radius-sm;
}

:host {
  .ratings-loading-template {
    .ratings-loading-top {
      height: $spacing-system-spacing-x-lg;
      @include template-box-style();
    }

    .ratings-loading-main-content {
      margin-top: $spacing-system-spacing-big;
      @include template-box-style();
    }
  }

  .ratings-no-results {
    padding-top: $spacing-system-spacing-x-xlg;
  }

  .badge-circle {
    height: 1.25rem;
    width: 1.25rem;
    line-height: 1.125rem;
    padding: 0 !important;
  }

  .fa-circle-star {
    color: $colour-blue-brand-500;
    background: $colour-blue-brand-200;
    --fa-border-radius: 50%;
    --fa-border-width: 1rem;
    --fa-border-color: #E0F7FF;
    --fa-border-padding: 1rem;
  }

  .ratings-list {
    &.has-shadow {
      @include box-shadow(0 -5px 5px -5px #CCC);
      @include border-top-radius($radius-xs);
    }
  }
}

::ng-deep {
  @each $size, $padding in $noRatingsPadding {
    .figma-screen-#{$size} {
      .ratings-no-results {
        padding-top: $padding !important;
      }
    }
  }
}

