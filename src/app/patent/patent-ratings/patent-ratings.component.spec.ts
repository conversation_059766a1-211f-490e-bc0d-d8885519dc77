import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentRatingsComponent } from './patent-ratings.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { Patent } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentRatingsComponent', () => {
  let component: PatentRatingsComponent;
  let fixture: ComponentFixture<PatentRatingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentRatingsComponent ],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentRatingsComponent);
    component = fixture.componentInstance;
    const patent = {general: {}} as Patent;
    patent.general['docdb_family_id'] = '123';
    component.patent = patent;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
