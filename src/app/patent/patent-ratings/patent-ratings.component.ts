import { AfterViewInit, Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import {
  BaseStoreService, HtmlElementUtil,
  PatentViewService,
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TaskService,
  TaskStatusEnum,
  TaskTypeEnum,
  ToastService,
  ToastTypeEnum,
  TopicService,
  UserService
} from '@core';
import {
  FilterRatingsOptionEnum,
  RatingNavigationParam,
  ShowRatingsEventParams,
  SortRatingsOptionEnum,
} from './shared/types';
import { Observable, Subscription, switchMap } from 'rxjs';
import { catchError, filter, finalize, mergeMap, take, tap } from 'rxjs/operators';
import { SideSectionEnum } from '@patent/types';
import { RatingSorter } from './shared/rating-sorter';

@Component({
  selector: 'app-patent-ratings',
  templateUrl: './patent-ratings.component.html',
  styleUrls: ['./patent-ratings.component.scss']
})
export class PatentRatingsComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() storeService: BaseStoreService;
  @Output() closeEvent: EventEmitter<void> = new EventEmitter<void>();

  currentRatingTask: TaskModel = null;
  showRatingsList: boolean = true;
  showRatingForm: boolean = false;
  showRatingView: boolean = false;
  showRatingAnswer: boolean = false;
  showIntermediateStep: boolean = false;

  allRatings: TaskModel[] = [];
  displayedRatings: TaskModel[] = [];
  answerableRequests: TaskModel[] = [];
  createdRequests: TaskModel[] = [];
  otherRequests: TaskModel[] = [];

  sortOptions: SortRatingsOptionEnum[] = [
    SortRatingsOptionEnum.RECENTLY_ADDED, SortRatingsOptionEnum.OLDEST_ADDED, SortRatingsOptionEnum.HIGHEST_RATINGS,
    SortRatingsOptionEnum.LOWEST_RATINGS
  ];

  filterOptions: FilterRatingsOptionEnum[] = [
    FilterRatingsOptionEnum.ALL, FilterRatingsOptionEnum.ASSIGNED_TO_ME, FilterRatingsOptionEnum.CREATED_BY_ME
  ];

  isRequestsExpanded: boolean = false;
  isFindingSelfRating: boolean = false;

  readonly sideSectionEnum = SideSectionEnum;

  private subscriptions = new Subscription();

  constructor(
    private toastService: ToastService,
    public userService: UserService,
    private taskService: TaskService,
    private patentViewService: PatentViewService,
    private topicService: TopicService
  ) {
  }

  private _patent: any;

  get patent() {
    return this._patent;
  }

  @Input() set patent(value) {
    this._patent = value;
  }

  get documentId() {
    return this.patent.general.docdb_family_id;
  }

  get currentFilter(): FilterRatingsOptionEnum {
    return this.patentViewService.currentRatingsFilter;
  }

  set currentFilter(val: FilterRatingsOptionEnum) {
    this.patentViewService.currentRatingsFilter = val;
  }

  get currentSort(): SortRatingsOptionEnum {
    return this.patentViewService.currentRatingsSort;
  }

  set currentSort(val: SortRatingsOptionEnum) {
    this.patentViewService.currentRatingsSort = val;
  }

  get isLoading(): boolean {
    return this.patentViewService.isLoadingRatings;
  }

  set isLoading(val: boolean) {
    this.patentViewService.isLoadingRatings = val;
  }

  get isDefaultFilter(): boolean {
    return this.currentFilter === FilterRatingsOptionEnum.ALL;
  }

  get tooltipTextForFilter(): string {
    if (!this.isDefaultFilter) {
      return `Filter by ${this.currentFilter.toLowerCase()}`;
    }
    return 'Filter by';
  }

  get tooltipTextForSort(): string {
    return `Sort by ${this.currentSort.toLowerCase()}`;
  }

  ngOnInit() {
    const refreshRatings$ = this.patentViewService.refreshRatings$
      .pipe(
        filter((val) => val),
        switchMap(() => this.loadRatings())
      )
      .subscribe();
    this.subscriptions.add(refreshRatings$);

    const ratingNavigation$ = this.patentViewService.ratingNavigation$
      .pipe(
        filter((val) => !!val?.task?.id),
        tap((val) => this.navigateToRatingOrRequest(val))
      )
      .subscribe();
    this.subscriptions.add(ratingNavigation$);

    const createGeneralTopic$ = this.topicService.createGeneralTopic()
      .pipe(
        switchMap(() => this.loadRatings())
      )
      .subscribe();
    this.subscriptions.add(createGeneralTopic$);
  }

  ngAfterViewInit() {
    this.updateRatingsListStyle();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onRequestRatingClicked(ratingType: TaskTypeEnum = null) {
    const rating = {
      resource_id: Number(this.documentId),
      resource_type: TaskResourceTypeEnum.DOCUMENT,
      task_type: ratingType,
      status: TaskStatusEnum.NEW
    } as TaskModel;
    this.setCurrentRating(rating, {showRatingForm: true});
  }

  onFilterOptionClicked(op: FilterRatingsOptionEnum) {
    this.currentFilter = op;
    this.updateDisplayedRatings();
  }

  onSortOptionClicked(op: SortRatingsOptionEnum) {
    this.currentSort = op;
    this.updateDisplayedRatings();
  }

  myAssignment(rating: TaskModel): TaskAssignmentModel {
    return this.taskService.myAssignment(rating);
  }

  onRatingsListScrolled(event: Event) {
    this.updateRatingsListStyle();
  }

  setCurrentRating(rating: TaskModel, showField: {
    showRatingsList?: boolean,
    showRatingForm?: boolean,
    showRatingView?: boolean,
    showRatingAnswer?: boolean
    showIntermediateStep?: boolean
  }) {
    this.resetRatingVariables();
    this.currentRatingTask = rating;
    this.showRatingsList = showField.showRatingsList || false;
    this.showRatingForm = showField.showRatingForm || false;
    this.showRatingView = showField.showRatingView || false;
    this.showRatingAnswer = showField.showRatingAnswer || false;
    this.showIntermediateStep = showField.showIntermediateStep || false;
  }

  displayRatingsList(params: ShowRatingsEventParams) {
    this.resetRatingVariables();
    if (params.showRatingForm) {
      this.setCurrentRating(params.currentRatingTask, {showRatingForm: true});
    } else {
      this.showRatingsList = true;
      if (params.refresh) {
        this.patentViewService.refreshRatings = true;
        this.patentViewService.refreshCountRatings = true;
        this.loadRatings();
      }
      this.updateRatingsListStyle();
    }
  }

  onCloseSectionClicked() {
    this.closeEvent.emit();
  }

  onHideRequestViewEvent(params: ShowRatingsEventParams) {
    if (params.showRatingForm) {
      this.setCurrentRating(params.currentRatingTask, {showRatingForm: true});
    } else {
      this.displayRatingsList(params);
    }
  }

  onAnswerRequestEvent(event: TaskModel) {
    this.setCurrentRating(event, {showRatingAnswer: true});
  }

  collapseRequests() {
    this.isRequestsExpanded = !this.isRequestsExpanded;
    this.updateRatingsListStyle();
  }

  onViewAnswerableRequestEvent(task: TaskModel) {
    if (task.assignments?.length === 1 && !this.taskService.isTaskCreatedByMe(task)) {
      this.setCurrentRating(task, {showRatingAnswer: true});
    } else {
      this.setCurrentRating(task, {showIntermediateStep: true});
    }
  }

  onViewCreatedRequestEvent(task: TaskModel) {
    if (this.taskService.isTaskAssignedToMe(task)) {
      if (task.assignments?.length === 1 && !this.taskService.isTaskCreatedByMe(task)) {
        this.setCurrentRating(task, {showRatingAnswer: true});
      } else {
        this.setCurrentRating(task, {showIntermediateStep: true});
      }
      return;
    }
    this.setCurrentRating(task, {showRatingView: true});
  }

  onViewOtherRequestEvent(task: TaskModel) {
    this.setCurrentRating(task, {showIntermediateStep: true});
  }

  onRateMyselfClicked() {
    if (this.isFindingSelfRating) {
      return;
    }
    this.isFindingSelfRating = true;
    const findPendingSelfRatingOrNew$ = this.taskService.findPendingSelfRatingOrNew(Number(this.documentId))
      .pipe(
        tap((task) => {
          this.setCurrentRating(task, {showRatingAnswer: true})
        }),
        finalize(() => this.isFindingSelfRating = false)
      )
      .subscribe();
    this.subscriptions.add(findPendingSelfRatingOrNew$);
  }

  getOriginalRequest(rating: TaskModel): TaskModel {
    return this.allRatings.find(t => t.id === rating.id);
  }

  onEditRatingEvent(task: TaskModel) {
    this.setCurrentRating(task, {showRatingAnswer: true});
  }

  private updateDisplayedRequests(ratings: TaskModel[]) {
    const {answerableRequests, createdRequests, otherRequests} = this.taskService.splitTasksIntoRequestsAndRatings(ratings);
    this.answerableRequests = answerableRequests.sort(RatingSorter.sortByDeadline);
    this.createdRequests = createdRequests.sort(RatingSorter.sortByDeadline);
    this.otherRequests = otherRequests.sort(RatingSorter.sortByDeadline);
    this.isRequestsExpanded = this.answerableRequests.length > 0;
  }

  private loadRatings(): Observable<TaskModel[]> {
    this.isLoading = true;

    this.allRatings = [];
    this.displayedRatings = [];
    this.answerableRequests = [];
    this.createdRequests = [];
    this.otherRequests = [];

    const payload = {
      document_id: this.documentId,
      load_all: 1
    };

    return this.taskService.getRatings(payload, true)
      .pipe(
        take(1),
        mergeMap(({tasks}) => {
          return this.taskService.getAuthorAndAssigneesForTasks(tasks);
        }),
        tap((tasks) => {
          this.allRatings = tasks;

          this.updateDisplayedRequests(this.allRatings);
          this.updateDisplayedRatings();
          this.navigateToRatingOrRequest(this.patentViewService.ratingNavigation);
        }),
        finalize(() => {
          this.isLoading = false;
        }),
        catchError((error) => {
          console.warn(error);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Error in loading ratings',
            body: 'An error occurred while loading ratings',
            delay: 5000
          });
          throw error;
        })
      );
  }

  private updateDisplayedRatings() {
    const filteredRatings = [...this.allRatings].filter((rating) => {
      return this.isRatingFiltered(rating);
    });
    const {ratings} = this.taskService.splitTasksIntoRequestsAndRatings(filteredRatings);
    this.displayedRatings = ratings.sort((a, b) => {
      return RatingSorter.sortByOption(a, b, this.currentSort, this.taskService.answerToRating);
    });
    this.updateRatingsListStyle();
  }

  private isRatingFiltered(rating: TaskModel): boolean {
    switch (this.currentFilter) {
      case FilterRatingsOptionEnum.ASSIGNED_TO_ME:
        return this.taskService.isTaskAssignedToMe(rating);
      case FilterRatingsOptionEnum.CREATED_BY_ME:
        return this.taskService.isTaskCreatedByMe(rating);
      default:
        return true;
    }
  }

  private navigateToRatingOrRequest(param: RatingNavigationParam) {
    const foundRating = this.allRatings.find(t => t.id === param?.task?.id);
    if (foundRating) {
      this.patentViewService.ratingNavigation = null;
      if (param.assignment) {
        this.scrollToRatingCard(param.assignment);
      } else {
        this.openRequestCard(foundRating);
      }
    }
  }

  private resetRatingVariables() {
    this.showRatingsList = false;
    this.showRatingForm = false;
    this.showRatingView = false;
    this.showRatingAnswer = false;
    this.showIntermediateStep = false;
    this.currentRatingTask = null;
    this.currentFilter = FilterRatingsOptionEnum.ALL;
  }

  private updateRatingsListStyle() {
    setTimeout(() => {
      const ratingsListEle = document.getElementById('ratings-list');
      if (ratingsListEle) {
        if (ratingsListEle.scrollTop > 0) {
          ratingsListEle.classList.add('has-shadow');
        } else {
          ratingsListEle.classList.remove('has-shadow');
        }
      }
    }, 50);
  }

  private scrollToRatingCard(assignment: TaskAssignmentModel) {
    setTimeout(() => {
      const ratingCardElements = document.querySelectorAll(`[data-assignment-id="${assignment.id}"]`);
      if (ratingCardElements.length > 0) {
        ratingCardElements.forEach(element => element.classList.add('figma-bg-secondary'));
        const firstRatingEle = ratingCardElements[0] as HTMLElement;
        const ratingListEle = document.getElementById('ratings-list');
        HtmlElementUtil.scrollBy(ratingListEle, firstRatingEle, 10);
        setTimeout(() => {
          ratingCardElements.forEach(element => element.classList.remove('figma-bg-secondary'));
        }, 2000);
      }
    }, 500);
  }

  private openRequestCard(request: TaskModel) {
    const { answerableRequests, createdRequests, otherRequests } = this.taskService.splitTaskIntoRequestsAndRatings(request);
    if (answerableRequests.length > 0) {
      const answerableRequest = answerableRequests[0];
      if (answerableRequest.assignments.length === 1) {
        this.setCurrentRating(answerableRequest, {showRatingAnswer: true});
      } else {
        this.setCurrentRating(answerableRequest, {showIntermediateStep: true});
      }
    } else {
      if (createdRequests.length > 0) {
        this.setCurrentRating(createdRequests[0], {showRatingView: true});
      } else {
        if (otherRequests.length > 0) {
          this.setCurrentRating(otherRequests[0], {showIntermediateStep: true});
        }
      }
    }
  }
}
