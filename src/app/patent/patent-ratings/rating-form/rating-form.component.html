<app-patent-side-section-header title="Request a rating"
                                [isSubHeader]="true" (closeEvent)="onCloseButtonClicked()"
                                [showCloseButton]="showCloseButton"
                                class="p-spacing-md d-block">
</app-patent-side-section-header>

<div class="w-100 divider m-spacing-none"></div>

<div class="d-flex flex-column justify-content-start align-items-stretch flex-grow-1 overflow-auto"
     [ngClass]="{'m-t-spacing-xx-big': !inlineMode, 'm-t-spacing-md': inlineMode}">
  <div appScrollbarDetector [formGroup]="ratingForm"
       class="rating-form-main p-x-spacing-md content-body-small flex-grow-1 figma-form figma-form-medium"
       [ngClass]="{'p-b-spacing-xxx-big': !inlineMode, 'p-b-spacing-md': inlineMode}">
    <div class="figma-form-control">
      <label>Assignee<sup>*</sup></label>
      <app-teams-selector #teamsSelector class="w-100" formControlName="assignees"
                          [showGroups]="true" [multiple]="true" [showMe]="false"
                          [(teams)]="selectedTeams"
                          [excludedTeams]="doneAssignees"
                          [excludedTooltipFunc]="excludedTooltipFunc"
                          label="Select an assignee">
      </app-teams-selector>
    </div>

    <div class="figma-form-control">
      <label>
	      Topic<sup>*</sup>

	      <sup class="m-l-spacing-xx-s">
          <app-tooltip tooltipText='Topics define the context in which you are rating and help others to understand it better.'
                       tooltipPosition="top" tooltipIconSize="xs" [tooltipClickable]="false">
          </app-tooltip>
	      </sup>
      </label>
      <app-topics-selector #topicSelector formControlName="topic_ids" class="w-100">
      </app-topics-selector>
    </div>

    <div class="figma-form-control">
      <label for="deadline">Due date</label>
      <div class="figma-datepicker">
        <input ngbDatepicker appMaskDate id="deadline"
               class="form-control p-spacing-sm" name="dp" formControlName="deadline"
               #dp="ngbDatepicker" autocomplete="off" placement="bottom" container="body"
               [minDate]="minDeadline" (click)="dp.open()"
               [ngClass]="{
               'border-1': !ratingForm?.disabled,
               'border-subtle figma-bg-secondary': !dp.isOpen(),
               'border-contrast figma-bg-primary': dp.isOpen(),
               'cursor-pointer': !isSaving && ratingForm?.enabled
               }"/>
        <i class="toggle-icon fa-regular" [ngClass]="dp.isOpen() ? 'fa-chevron-up' : 'fa-chevron-down'"
           (click)="dp.toggle()"></i>
      </div>
    </div>

    <div class="figma-form-control">
      <app-user-dialogue formControlName="description"
                         placeholder="Leave a message to the assignee"
                         [handleSubmit]="true" [maxLength]="1000">
      </app-user-dialogue>
    </div>
  </div>

  <div class="m-t-spacing-none m-b-spacing-sm w-100 divider"></div>

  <div class="rating-form-footer d-flex align-items-center w-100 justify-content-end p-x-spacing-md p-b-spacing-md">
    <button class="button-main-secondary-grey button-medium m-r-spacing-sm content-label-medium"
            (click)="onCloseButtonClicked()" [disabled]="isSaving">
      Cancel
    </button>
    <button class="button-main-primary button-medium content-label-medium" (click)="onSendButtonClicked()"
            [disabled]="!canSaveRating || ratingForm.invalid || isSaving || (isEditing && !ratingForm.dirty)">
      {{ isEditing ? 'Update' : 'Send' }}
    </button>
  </div>
</div>
