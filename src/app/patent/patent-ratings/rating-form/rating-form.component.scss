@import 'scss/figma2023/index';
@import '../shared/style';

:host {
  .rating-form-main {
    overflow: auto;

    textarea {
      resize: none;
    }

    #deadline {
      &:hover {
        border-color: $colours-border-contrast !important;
      }
    }
  }

  .rating-form-footer {
  }
}

:host-context(.button-rating-form-popper) {
  .figma-form-control {
    app-teams-selector {
      ::ng-deep {
        .teams-selector-container {
          max-height: calc((32px * 4) + 16px) !important;
          overflow-y: auto !important;
          
          .tag-item {
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
