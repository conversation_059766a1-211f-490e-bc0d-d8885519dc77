import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import {
  BaseStoreService,
  dateStr2NgbDate,
  ngbDate2DateStr,
  PatentViewService,
  TaskModel,
  TaskResourceTypeEnum,
  TaskService,
  TaskTypeEnum,
  TeamUser,
  ToastService,
  ToastTypeEnum,
  UserService,
  UsersTitleTextPipe
} from '@core';
import { Observable, of, Subscription } from 'rxjs';
import { concatMap, finalize, map } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { ShowRatingsEventParams } from '@patent/patent-ratings/shared/types';
import { TopicsSelectorComponent } from '@shared/components/topics-selector/topics-selector.component';
import { TeamsSelectorComponent } from '@shared/components/teams-selector/teams-selector.component';

@Component({
  selector: 'app-patent-rating-form',
  templateUrl: './rating-form.component.html',
  styleUrls: ['./rating-form.component.scss']
})
export class RatingFormComponent implements OnInit, OnDestroy {
  @ViewChild('teamsSelector') private teamsSelector: TeamsSelectorComponent;
  @ViewChild('topicSelector') private topicSelector: TopicsSelectorComponent;

  @Input() patentFamilyId: number;
  @Input() storeService: BaseStoreService;
  @Input() originalTask: TaskModel = null;
  @Input() showCloseButton = true;
  @Input() refreshTaskAfterDone = false;
  @Input() inlineMode = false;
  @Input() resourceId: number;
  @Input() resourceType: TaskResourceTypeEnum;
  @Output() hideRatingFormEvent: EventEmitter<ShowRatingsEventParams> = new EventEmitter<ShowRatingsEventParams>();
  ratingForm: FormGroup = null;
  isSaving: boolean = false;
  canSaveRating: boolean = false;

  minDeadline: any;

  selectedTeams: TeamUser[] = [];
  excludedTooltipFunc = (team: TeamUser): string => {
    return `<div class="content-style-bold">${this.usersTitleTextPipe.transform([team])} has already posted a rating for this patent.</div>`;
  };

  private subscriptions = new Subscription();

  constructor(
    private toastService: ToastService,
    public userService: UserService,
    public taskService: TaskService,
    private formBuilder: FormBuilder,
    private patentViewService: PatentViewService,
    private usersTitleTextPipe: UsersTitleTextPipe,
  ) {
    this.buildRatingForm();
  }

  private _task: TaskModel = null;

  get task(): TaskModel {
    return this._task;
  }

  @Input()
  set task(task: TaskModel) {
    this._task = task || {} as TaskModel;
    this.canSaveRating = !this.isEditing || this.taskService.canEditRatingTask(this.task);
    this.buildRatingForm();
  }

  get taskTopicIds(): number[] {
    return this.task?.topics?.map(topic => topic.id) || [];
  }

  get isEditing(): boolean {
    return !!(this.task?.id);
  }

  get doneAssignees(): TeamUser[] {
    return this.taskService.getDoneAnswers(this.originalTask, null)
      .map(answer => answer.assignee);
  }

  get hasBinaryRating(): boolean {
    return this.userService.hasBinaryRating;
  }

  ngOnInit() {
    const today = new Date();
    this.minDeadline = {year: today.getFullYear(), month: today.getMonth() + 1, day: today.getDate()};
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onCloseButtonClicked() {
    this.hideRatingFormEvent.emit({refresh: false} as ShowRatingsEventParams);
  }

  onSendButtonClicked() {
    if (this.ratingForm.invalid || this.isSaving) {
      return;
    }

    this.isSaving = true;
    this.ratingForm.disable();

    const teamNames = this.usersTitleTextPipe.transform(this.selectedTeams);
    const payload = this.buildRatingPayload();

    const obs = this.isEditing ? this.taskService.updateTask(this.task.id, payload) : this.taskService.createTask(payload);
    const obs$ = obs
      .pipe(
        concatMap((task) => this.refreshTask(task)),
        finalize(() => {
          this.isSaving = false;
          this.ratingForm.enable();
        })
      )
      .subscribe({
        next: (task) => {
          const header = this.isEditing ? 'Request updated' : 'Rating has been requested.';
          const body = this.isEditing ? 'The request has been updated successfully!' : `You have successfully requested a rating from ${teamNames}`
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: header,
            body: body,
            delay: 20000
          });
          this.hideRatingFormEvent.emit({refresh: true, currentRatingTask: task} as ShowRatingsEventParams);
          this.patentViewService.refreshCountTasks = true;
          this.reset();
        },
        error: ({error}) => {
          console.log(error);
          const header = this.isEditing ? 'Failed to update request' : 'Failed to assign rating';
          const body = this.isEditing ? 'An error occurred while updating the request!' : `An error occurred while requesting a rating to ${teamNames}!`;
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: header,
            body: body,
            delay: 10000
          });
        }
      });
    this.subscriptions.add(obs$);
  }

  private getTask(taskId: number): Observable<TaskModel> {
    return this.taskService.getRatings({id: taskId})
      .pipe(
        map(({tasks}) => tasks[0])
      );
  }

  private buildRatingForm() {
    const deadline = this.task?.deadline ? dateStr2NgbDate(moment.utc(this.task.deadline).local().format('YYYY-MM-DD')) : null;
    this.ratingForm = this.formBuilder.group({
      assignees: [this.task?.assignees, [Validators.required, Validators.minLength(1)]],
      topic_ids: [this.taskTopicIds, [Validators.required, Validators.minLength(1)]],
      deadline: [deadline],
      description: [this.task?.description, [Validators.maxLength(1000)]],
    });
  }

  private buildRatingPayload(): TaskModel {
    const data = this.ratingForm.value;
    const deadline = data.deadline ?
      moment(ngbDate2DateStr(data.deadline, 'T23:59:59')).utc().format('YYYY-MM-DDTHH:mm:ss') : null;

    const payload = {
      description: data.description,
      deadline: deadline,
      assignees: data.assignees,
      topic_ids: data.topic_ids
    } as TaskModel;

    if (!this.isEditing) {
      payload['subject'] = 'Rating task';
      payload['task_type'] = this.hasBinaryRating ? TaskTypeEnum.YES_NO_ANSWER : TaskTypeEnum.STAR_RATING;

      if (this.patentFamilyId) {
        payload['resource_type'] = TaskResourceTypeEnum.DOCUMENT;
        payload['resource_id'] = this.patentFamilyId;
        payload['document_ids'] = [this.patentFamilyId];
      } else {
        payload['resource_id'] = this.resourceId;
        payload['resource_type'] = this.resourceType;
        payload['document_ids'] = [...this.storeService.selectedPatentIds];
      }
    }

    return payload;
  }

  private refreshTask(task: TaskModel): Observable<TaskModel> {
    if (!this.refreshTaskAfterDone) {
      return of(task);
    }

    return this.getTask(task.id)
      .pipe(
        concatMap((val) => this.taskService.getAuthorAndAssigneesForTasks([val])),
        map((tasks) => tasks[0])
      );
  }

  reset() {
    this.ratingForm.reset();
    this.topicSelector.reset();
    this.teamsSelector.reset();
  }

}
