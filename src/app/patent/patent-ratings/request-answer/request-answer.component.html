<app-patent-side-section-header [title]="title"
                                [isSubHeader]="true" (closeEvent)="onCloseClicked()"
                                [showCloseButton]="showCloseButton"
                                class="p-spacing-md d-block">
</app-patent-side-section-header>

<div class="w-100 divider m-spacing-none"></div>

<div appScrollbarDetector
     class="radius-sm d-flex flex-column figma-bg-primary gap-spacing-sm flex-grow-1 overflow-auto"
      [ngClass]="{'p-t-spacing-md p-b-spacing-xxx-big m-t-spacing-sm': !inlineMode, 'p-y-spacing-md': inlineMode}">
  <ng-container *ngIf="!isSelfRatingTask && !isNewTask">
    <app-patent-request-base-view [task]="task" [hasActions]="false" [showStatus]="true"
                                  [showDeadline]="!myTaskAssignment?.answered_at"
                                  [showAnsweredAt]="!!myTaskAssignment?.answered_at"
                                  class="p-x-spacing-md">
    </app-patent-request-base-view>

    <hr class="w-100 divider m-spacing-none">
  </ng-container>

  <div [formGroup]="answerForm"
       class="rating-form-main content-body-small p-x-spacing-md  flex-grow-1 figma-form figma-form-medium"
       [ngClass]="{'p-t-spacing-md': !inlineMode}">
    <div class="figma-form-control">
      <label>Assignee<sup>*</sup></label>
      <app-teams-selector class="w-100" formControlName="assignees"
                          [showGroups]="true" [multiple]="false" [showMe]="true"
                          [initialTeams]="task?.team_users"
                          [ngbTooltip]="isSelfRatingTask ? 'This shows your self-assessment.' : 'Only the reporter can change the assignee.'"
                          tooltipClass="white-tooltip"
                          label="Select an assignee">
      </app-teams-selector>
    </div>

    <div class="figma-form-control">
      <label>Rating
        <sup>*</sup>
        <sup class="m-l-spacing-xx-s">
          <app-tooltip
            tooltipText='Rate this patent based on your criteria.'
            tooltipPosition="top" tooltipIconSize="xs" [tooltipClickable]="false">
          </app-tooltip>
        </sup>
      </label>

      <div *ngIf="hasBinaryRating"
           class="min-h-spacing-x-lg p-r-spacing-sm p-y-spacing-x-s d-flex align-items-center gap-spacing-xx-s">
        <span class="button-main-tertiary-grey button-small content-label-small" (click)="setRatingFormValue(taskService.PASS_VALUE)"
              [ngClass]="{'content-color-active': taskService.isPassValue(ratingFormValue)}">
          <i class="fa-regular fa-thumbs-up m-r-spacing-xx-s" [ngClass]="{'fa-solid': taskService.isPassValue(ratingFormValue)}"></i> Pass
        </span>
        <span class="button-main-tertiary-grey button-small content-label-small" (click)="setRatingFormValue(taskService.NO_PASS_VALUE)"
              [ngClass]="{'content-color-danger': taskService.isNoPassValue(ratingFormValue)}">
          <i class="fa-regular fa-thumbs-down m-r-spacing-xx-s" [ngClass]="{'fa-solid': taskService.isNoPassValue(ratingFormValue)}"></i> No pass
        </span>
      </div>

      <div *ngIf="!hasBinaryRating" appMouseHover hoverClass="border-contrast rating-hovered"
           class="min-h-spacing-x-lg p-spacing-sm p-y-spacing-x-s d-flex align-items-center gap-spacing-sm radius-sm border-1 border-subtle figma-bg-secondary">
        <ngb-rating [max]="5" [(rate)]="selectedRate" (hover)="hoveredRate = $event"
                    (mouseenter)="isHoveringOnStars = true"
                    (mouseleave)="isHoveringOnStars = false"
                    formControlName="rating" class="d-flex gap-spacing-sm rating-star">
          <ng-template let-fill="fill" let-index="index">
            <span *ngIf="fill === 100" class="fa-stack fa-2x fa-stack-star fa-stack-lg"
                  [ngClass]="'rated-star-' + (isHoveringOnStars ? hoveredRate : selectedRate)">
              <i class="fa-regular fa-circle fa-stack-2x"></i>
              <i class="fa-solid fa-star-circle fa-stack-1x"></i>
            </span>
              <span *ngIf="fill === 0" class="fa-stack fa-2x fa-stack-star fa-stack-lg">
              <i class="fa-regular fa-circle fa-stack-2x"></i>
              <i class="fa-solid fa-star-circle fa-stack-1x"></i>
            </span>
          </ng-template>
        </ngb-rating>

        <div class="content-label-xsmall content-color-tertiary flex-grow-1 text-left">
          {{ selectedRate }} {{ 'Point' | pluralize: selectedRate }}
        </div>
      </div>
    </div>

    <div class="figma-form-control">
      <label>
        Topic<sup>*</sup>

        <sup class="m-l-spacing-xx-s">
          <app-tooltip
            tooltipText='Topics define the context in which you are rating and help others to understand it better.'
            tooltipPosition="top" tooltipIconSize="xs" [tooltipClickable]="false">
          </app-tooltip>
        </sup>
      </label>
      <app-topics-selector formControlName="topic_ids" class="w-100">
      </app-topics-selector>
    </div>

    <div class="figma-form-control">
      <label>Review</label>
      <app-user-dialogue formControlName="message" [showUserAvatar]="false"
                         placeholder="Describe the importance of this patent for your research"
                         minHeight="5rem" [handleSubmit]="true" [maxLength]="1000"
                         (submitEvent)="onMessageSubmittedEvent($event)">
      </app-user-dialogue>
    </div>
  </div>
</div>

<div class="m-t-spacing-none m-b-spacing-sm w-100 divider"></div>

<div class="rating-form-footer d-flex align-items-center w-100 justify-content-end p-x-spacing-md p-b-spacing-md">
  <button class="button-main-secondary-grey button-medium m-r-spacing-sm content-label-medium"
          (click)="onCloseClicked()" [disabled]="isSavingRate || isSavingMessage">
    {{ canAnswerAssignment ? 'Cancel' : 'Close' }}
  </button>
  <button *ngIf="canAnswerAssignment"
          class="button-main-primary button-medium content-label-medium"
          (click)="onPostButtonClicked()"
          [disabled]="answerForm.invalid || isSavingRate || isSavingMessage">
    {{ myTaskAssignment.answered_at ? 'Update' : 'Post' }}
  </button>
</div>

