import { ComponentFixture, discardPeriodicTasks, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { RequestAnswerComponent } from './request-answer.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import {
  AssigneeTypeEnum, PluralizePipe,
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TaskService,
  TaskStatusEnum,
  TaskTypeEnum,
  ToastService,
  UsersTitleTextPipe,
  UserTitlePipe
} from '@core';
import { delay, of } from 'rxjs';
import { provideMatomo } from 'ngx-matomo-client';

describe('RequestAnswerComponent', () => {
  let component: RequestAnswerComponent;
  let fixture: ComponentFixture<RequestAnswerComponent>;
  let taskService: jasmine.SpyObj<TaskService>;
  let toastService: jasmine.SpyObj<ToastService>;

  const mockTask: TaskModel = {
    id: 1,
    subject: 'Test Task',
    task_type: TaskTypeEnum.STAR_RATING,
    resource_id: 123,
    resource_type: TaskResourceTypeEnum.DOCUMENT,
    document_ids: [1, 2],
    assignee: {id: 1, type: AssigneeTypeEnum.USER},
    status: TaskStatusEnum.OPEN,
    assignments: [
      {
        id: 1,
        answer: '',
        answered_at: '2021-09-01T00:00:00',
        status: TaskAssignmentStatusEnum.OPEN,
        message: 'Initial message',
        assignee_id: 1
      } as unknown as TaskAssignmentModel
    ],
    assignees: [{id: 1, type: AssigneeTypeEnum.USER}],
    topics: [{id: 1, name: 'Topic 1'}, {id: 2, name: 'Topic 2'}],
    created_at: '2021-09-01T00:00:00',
    updated_at: '2021-09-01T00:00:00'
  } as any as TaskModel;

  beforeEach(async () => {
    taskService = jasmine.createSpyObj('TaskService', [
      'updateTaskAssignment',
      'answerTaskAssignment',
      'createTask',
      'getRatings',
      'updateTask',
      'myAssignment',
      'myAssignments',
      'isSelfRatingTaskAssignment',
      'isTaskNew',
      'isTaskAssignmentNew',
      'isYesNoAnswerTaskType',
      'markTaskAssignmentAsOpen',
      'markMyTaskAssignmentsAsOpen',
    ]);
    toastService = jasmine.createSpyObj('ToastService', ['show']);

    await TestBed.configureTestingModule({
      declarations: [RequestAnswerComponent],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [
        UsersTitleTextPipe,
        UserTitlePipe,
        PluralizePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true}),
        {provide: TaskService, useValue: taskService},
        {provide: ToastService, useValue: toastService}
      ]
    }).compileComponents();

    setupDefaultSpyBehaviors();

    fixture = TestBed.createComponent(RequestAnswerComponent);
    component = fixture.componentInstance;
    component.task = {...mockTask};
    fixture.detectChanges();
  });

  function setupDefaultSpyBehaviors() {
    taskService.myAssignment.and.returnValue(mockTask.assignments[0]);
    taskService.myAssignments.and.returnValue(mockTask.assignments);
    taskService.isSelfRatingTaskAssignment.and.returnValue(false);
    taskService.isTaskNew.and.returnValue(false);
    taskService.isTaskAssignmentNew.and.returnValue(false);
    taskService.isYesNoAnswerTaskType.and.returnValue(false);
    taskService.updateTaskAssignment.and.returnValue(of(mockTask.assignments[0]));
    taskService.markMyTaskAssignmentsAsOpen.and.returnValue(of([mockTask]));
    taskService.answerTaskAssignment.and.returnValue(of(mockTask.assignments[0]));
    taskService.updateTask.and.returnValue(of(mockTask));
    taskService.createTask.and.returnValue(of(mockTask));
    taskService.getRatings.and.returnValue(of({tasks: [mockTask], page: null}));
  }

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with correct form values', () => {
      spyOn<any>(component, 'canAnswerAssignment').and.returnValue(true);

      expect(component.answerForm.get('rating').value).toBe(0);
      expect(component.answerForm.get('message').value).toBe('Initial message');
      expect(component.answerForm.get('topic_ids').value).toEqual([1, 2]);
    });

    it('should handle null task initialization', () => {
      component.task = null;
      fixture.detectChanges();
      expect(component.taskTopicIds).toEqual([]);
      expect(component.answerForm.get('topic_ids').value).toEqual([]);
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      component.task = {...mockTask};
      fixture.detectChanges();
    });

    it('should validate rating range', () => {
      const ratingControl = component.answerForm.get('rating');

      ratingControl.setValue(0);
      expect(ratingControl.errors?.['min']).toBeTruthy();

      ratingControl.setValue(6);
      expect(ratingControl.errors?.['max']).toBeTruthy();

      ratingControl.setValue(3);
      expect(ratingControl.errors).toBeNull();
    });

    it('should require at least one topic', () => {
      const topicIdsControl = component.answerForm.get('topic_ids');

      topicIdsControl.setValue([]);
      expect(topicIdsControl.errors?.['required']).toBeTruthy();

      topicIdsControl.setValue([1]);
      expect(topicIdsControl.errors).toBeNull();
    });

    it('should disable form when canAnswerAssignment is false', () => {
      taskService.myAssignment.and.returnValue(null);
      taskService.isSelfRatingTaskAssignment.and.returnValue(false);

      component.task = {...mockTask};
      fixture.detectChanges();

      expect(component.answerForm.get('rating').disabled).toBeTrue();
      expect(component.answerForm.get('message').disabled).toBeTrue();
      expect(component.answerForm.get('topic_ids').disabled).toBeTrue();
    });
  });

  describe('Message Queue Processing', () => {
    beforeEach(() => {
      // Set valid form values
      component.answerForm.patchValue({
        rating: 5,
        topic_ids: [1],
        message: 'Initial message'
      });
    });

    it('should process messages in order even during rapid submissions', fakeAsync(() => {
      const messages = ['message1', 'message2', 'message3'];
      taskService.updateTaskAssignment.and.returnValue(of({} as any as TaskAssignmentModel).pipe(delay(500)));

      // First message processing
      component.onMessageSubmittedEvent(messages[0]);
      tick(1001);
      expect(taskService.updateTaskAssignment).toHaveBeenCalledWith(1, {message: 'message1'});

      // Second message processing
      component.onMessageSubmittedEvent(messages[1]);
      tick(1001);
      expect(taskService.updateTaskAssignment).toHaveBeenCalledWith(1, {message: 'message2'});

      // Third message processing
      component.onMessageSubmittedEvent(messages[2]);
      tick(1001);
      expect(taskService.updateTaskAssignment).toHaveBeenCalledWith(1, {message: 'message3'});

      expect(taskService.updateTaskAssignment).toHaveBeenCalledTimes(3);
      discardPeriodicTasks();
    }));

    it('should handle message submissions during rate posting', fakeAsync(() => {
      taskService.createTask.and.returnValue(of({id: 1} as any as TaskModel).pipe(delay(1000)));
      taskService.updateTask.and.returnValue(of({id: 1} as any as TaskModel).pipe(delay(1000)));

      // Start posting
      component.onPostButtonClicked();
      tick(0);

      // Submit new message while posting
      component.onMessageSubmittedEvent('New message');
      tick(1001);

      expect(taskService.answerTaskAssignment).toHaveBeenCalledWith(
        1,
        jasmine.objectContaining({answer: '5', message: 'New message'})
      );
      discardPeriodicTasks();
    }));
  });

  describe('Task Creation Limits', () => {
    it('should not call createTask more than once in a session', fakeAsync(() => {
      // Remove task ID to simulate new task
      component.task = {...mockTask, id: null};

      // Trigger multiple actions that might create task
      component.onMessageSubmittedEvent('message1');
      tick(1000);
      component.onMessageSubmittedEvent('message2');
      tick(1000);
      component.onPostButtonClicked();
      tick(1000);

      expect(taskService.createTask).toHaveBeenCalledTimes(1);
      discardPeriodicTasks();
    }));

    it('should not create task for existing tasks', fakeAsync(() => {
      // Submit message for existing task
      component.onMessageSubmittedEvent('new message');
      tick(1000);

      expect(taskService.createTask).not.toHaveBeenCalled();
      discardPeriodicTasks();
    }));
  });

  describe('Post Button Behavior', () => {
    beforeEach(() => {
      component.answerForm.patchValue({
        rating: 5,
        topic_ids: [1],
        message: 'Test message'
      });
    });

    it('should not get stuck waiting for message processing', fakeAsync(() => {
      // Simulate slow message processing
      taskService.updateTaskAssignment.and.returnValue(of({} as any as TaskAssignmentModel).pipe(delay(2000)));

      // Submit message and immediately try to post
      component.onMessageSubmittedEvent('test message');
      component.onPostButtonClicked();

      tick(2000);

      expect(taskService.answerTaskAssignment).toHaveBeenCalledTimes(1);
      expect(taskService.answerTaskAssignment).toHaveBeenCalledWith(
        1,
        jasmine.objectContaining({answer: '5', message: 'Test message'})
      );
      discardPeriodicTasks();
    }));

    it('should complete post operation even with pending messages', fakeAsync(() => {
      taskService.answerTaskAssignment.and.returnValue(of({} as any as TaskAssignmentModel).pipe(delay(1000)));

      // Start posting
      component.onPostButtonClicked();
      tick(100);

      // Submit messages while posting
      component.onMessageSubmittedEvent('message1');
      tick(500);
      component.onMessageSubmittedEvent('message2');

      tick(1000);

      expect(taskService.answerTaskAssignment).toHaveBeenCalledTimes(1);
      expect(taskService.answerTaskAssignment).toHaveBeenCalledWith(
        1,
        jasmine.objectContaining({answer: '5', message: 'Test message'})
      );
      discardPeriodicTasks();
    }));
  });

  describe('Task Assignment Updates', () => {
    it('should process task assignment updates without getting stuck', fakeAsync(() => {
      // Simulate slow assignment update
      taskService.updateTaskAssignment.and.returnValue(of({} as any as TaskAssignmentModel).pipe(delay(2000)));

      // Submit message
      component.onMessageSubmittedEvent('test message');
      tick(2000);

      // Verify update completed
      expect(taskService.updateTaskAssignment).toHaveBeenCalledWith(
        1,
        {message: 'test message'}
      );

      // Submit another message
      component.onMessageSubmittedEvent('another message');
      tick(2000);

      // Verify second update completed
      expect(taskService.updateTaskAssignment).toHaveBeenCalledWith(
        1,
        {message: 'another message'}
      );

      expect(taskService.updateTaskAssignment).toHaveBeenCalledTimes(2);
      discardPeriodicTasks();
    }));
  });
});
