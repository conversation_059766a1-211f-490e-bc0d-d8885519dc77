import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import {
  TaskAssignmentModel,
  TaskModel,
  TaskService,
  TaskStatusEnum,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core';
import { ShowRatingsEventParams, ShowTasksEventParams } from '@patent/patent-ratings/shared/types';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, mergeMap, Observable, of, Subject, Subscription, timer } from 'rxjs';
import { catchError, concatMap, debounce, filter, finalize, first, map, tap } from 'rxjs/operators';
import _ from 'lodash';

@Component({
  selector: 'app-patent-request-answer',
  templateUrl: './request-answer.component.html',
  styleUrls: ['./request-answer.component.scss']
})
export class RequestAnswerComponent implements OnDestroy {
  @Input() showCloseButton = true;
  @Input() refreshTaskAfterDone = false;
  @Input() inlineMode = false;
  @Output() hideRequestAnswerEvent: EventEmitter<ShowRatingsEventParams> = new EventEmitter<ShowRatingsEventParams>();
  answerForm: FormGroup = null;
  isSavingRate: boolean = false;
  selectedRate: number = 0;
  hoveredRate: number = 0;
  isHoveringOnStars: boolean = false;

  myTaskAssignment: TaskAssignmentModel = {} as TaskAssignmentModel;

  private subscriptions = new Subscription();

  private messageQueue$ = new Subject<string>();
  private isSavingMessage$ = new BehaviorSubject<boolean>(false);
  private pendingMessage: string | null = null;

  constructor(
    private toastService: ToastService,
    public userService: UserService,
    public taskService: TaskService,
    private formBuilder: FormBuilder
  ) {
    this.buildAnswerForm();
    this.initializeMessageQueue();
  }

  private _task: TaskModel = null;

  get task(): TaskModel {
    return this._task;
  }

  @Input()
  set task(task: TaskModel) {
    this.setTask(task);
    this.buildAnswerForm();
  }

  get taskTopicIds(): number[] {
    return this.task?.topics?.map(topic => topic.id) || [];
  }

  get ratingFormControl(): AbstractControl {
    return this.answerForm.get('rating');
  }

  get ratingFormValue(): number {
    return this.ratingFormControl.value;
  }

  get messageFormValue(): string {
    return this.answerForm.get('message').value;
  }

  get topicIdsFormValue(): number {
    return this.answerForm.get('topic_ids').value;
  }

  get canAnswerAssignment(): boolean {
    return !!this.myTaskAssignment?.id || this.isSelfRatingTask;
  }

  get isSelfRatingTask(): boolean {
    return this.taskService.isSelfRatingTaskAssignment(this.task, this.myTaskAssignment);
  }

  get isNewTask(): boolean {
    return !this.task?.id;
  }

  get isSavingMessage(): boolean {
    return this.isSavingMessage$.value;
  }

  get title(): string {
    if (this.myTaskAssignment?.answered_at) {
      return 'Edit rating';
    }
    return this.isSelfRatingTask ? 'Rate it myself' : 'Requested';
  }

  get hasBinaryRating(): boolean {
    return (this.isNewTask && this.userService.hasBinaryRating) ||
      (this.task && this.taskService.isYesNoAnswerTaskType(this.task));
  }

  ngOnDestroy() {
    this.isSavingMessage$.complete();
    this.messageQueue$.complete();
    this.subscriptions.unsubscribe();
  }

  onCloseClicked() {
    this.hideRequestAnswerEvent.emit({refresh: true} as ShowRatingsEventParams);
  }

  onPostButtonClicked() {
    if (!this.canAnswerAssignment || this.answerForm.invalid) {
      return;
    }

    if (this.isSavingRate) {
      return;
    }

    this.isSavingRate = true;

    const subscription = this.waitForSavingMessageDone()
      .pipe(
        concatMap(() => this.createSelfRatingTaskWhenNeeded(false)),
        filter((task) => !!task?.id),
        concatMap(() => this.updateTaskTopicIds()),
        concatMap(() => this.answerTaskAssignments()),
        concatMap(() => this.refreshTask(this.task)),
        finalize(() => {
          this.isSavingRate = false;
          this.pendingMessage = null;
        })
      )
      .subscribe({
        next: (data) => {
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: 'Rating request completed',
            body: 'Your rating has been posted successfully!',
            delay: 5000
          });
          this.hideRequestAnswerEvent.emit({refresh: true, currentRatingTask: data} as ShowRatingsEventParams);
        },
        error: (error) => {
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Error submitting your rate',
            body: 'An error occurred while submitting your rate. Please try again.',
            delay: 10000
          });
        }
      });
    this.subscriptions.add(subscription);
  }

  onMessageSubmittedEvent(msg: string) {
    if (!this.canAnswerAssignment) {
      return;
    }

    const trimmedMessage = (msg || '').trim();

    if (this.isSavingRate) {
      this.pendingMessage = trimmedMessage;
      return;
    }

    this.messageQueue$.next(trimmedMessage);
  }

  setRatingFormValue(val: number) {
    this.ratingFormControl.setValue(val);
    this.ratingFormControl.markAsDirty();
  }

  private createSelfRatingTaskWhenNeeded(skipCreating: boolean): Observable<TaskModel> {
    if (skipCreating) {
      return of(null);
    }

    if (!this.isNewTask) {
      return of(this.task);
    }

    const payload = {
      subject: this.task.subject,
      task_type: this.task.task_type,
      resource_id: this.task.resource_id,
      resource_type: this.task.resource_type,
      document_ids: this.task.document_ids,
      assignees: this.task.assignees,
      status: TaskStatusEnum.OPEN
    } as TaskModel;

    return this.taskService.createTask(payload)
      .pipe(
        catchError((error) => {
          console.error('Error creating task', error);
          throw error;
        }),
        concatMap((task) => this.getTask(task.id)),
        tap((task) => {
          const taskAssignment = {
            ...task.assignments[0],
            answer: this.ratingFormValue?.toString(10),
            message: this.messageFormValue
          } as TaskAssignmentModel;
          const newTask = {
            ...task,
            topic_ids: this.task.topic_ids,
            assignments: [taskAssignment],
          } as TaskModel;
          this.setTask(newTask);
        }),
      );
  }

  private getTask(taskId: number): Observable<TaskModel> {
    return this.taskService.getRatings({id: taskId})
      .pipe(
        map(({tasks}) => tasks[0])
      );
  }

  private initializeMessageQueue() {
    const subscription = this.messageQueue$
      .pipe(
        debounce(() => timer(this.answerForm.valid ? 1000 : 0)),
        concatMap(message => {
          if (this.isSavingRate) {
            this.pendingMessage = message;
            return of(null);
          }

          this.isSavingMessage$.next(true);
          return this.updateTaskAssignmentMessage(message).pipe(
            finalize(() => {
              this.isSavingMessage$.next(false);
            })
          );
        })
      )
      .subscribe();

    this.subscriptions.add(subscription);
  }

  private waitForSavingMessageDone(): Observable<void> {
    return this.isSavingMessage$.pipe(
      filter(isSaving => !isSaving),
      first(),
      map(() => void 0),
    );
  }

  private updateTaskAssignmentMessage(message: string): Observable<TaskAssignmentModel> {
    return this.createSelfRatingTaskWhenNeeded(message.length === 0)
      .pipe(
        filter((task) => !!task?.id),
        concatMap(() => {
          return this.taskService.updateTaskAssignment(this.myTaskAssignment.id, {message});
        }),
        catchError((error) => {
          console.error('Error updating task assignment message', error);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Error saving review message',
            body: 'An error occurred while submitting your review message. Please try again.',
            delay: 10000
          });
          throw error;
        })
      );
  }

  private updateTaskTopicIds(): Observable<TaskModel> {
    const areTopicsNotChanged = _.difference(this.topicIdsFormValue, this.taskTopicIds).length === 0 &&
      _.difference(this.taskTopicIds, this.topicIdsFormValue).length === 0;

    if (areTopicsNotChanged) {
      return of(this.task);
    }

    const payload = {
      topic_ids: this.topicIdsFormValue
    };

    return this.taskService.updateTask(this.task.id, payload)
      .pipe(
        catchError((error) => {
          console.error('Error updating task', error);
          return of(this.task);
        }),
        tap((task) => {
          this.task.topic_ids = task.topic_ids;
          this.task.topics = task.topics;
        })
      );
  }

  private buildAnsweringTaskPayload(): { answer: string, message: string } {
    let currentMessage = this.messageFormValue;

    if (this.pendingMessage !== null) {
      currentMessage = this.pendingMessage;
    }

    return {
      answer: this.ratingFormValue?.toString(10),
      message: currentMessage
    };
  }

  private buildAnswerForm() {
    const answerValue = this.myTaskAssignment?.answer?.trim()?.length ? this.myTaskAssignment.answer :
      (this.hasBinaryRating ? null : 0);
    const isDisabled = !this.canAnswerAssignment;
    const binaryValueRegex = new RegExp(`^[${this.taskService.NO_PASS_VALUE}${this.taskService.PASS_VALUE}]$`);
    const ratingValidator = this.hasBinaryRating ? [Validators.required, Validators.pattern(binaryValueRegex)] :
      [Validators.required, Validators.min(1), Validators.max(5)];
    this.answerForm = this.formBuilder.group({
      rating: [
        {value: answerValue, disabled: isDisabled},
        ratingValidator
      ],
      assignees: [{value: this.task?.assignees, disabled: true}],
      topic_ids: [
        {value: this.taskTopicIds, disabled: isDisabled},
        [Validators.required, Validators.minLength(1)]
      ],
      message: [
        {value: this.myTaskAssignment?.message, disabled: isDisabled},
        [Validators.maxLength(1000)]
      ]
    });
  }

  private setTask(task: TaskModel) {
    this._task = task || {} as TaskModel;
    this.myTaskAssignment = this.taskService.myAssignment(this.task);
  }

  private answerTaskAssignments(): Observable<TaskAssignmentModel> {
    const payload = this.buildAnsweringTaskPayload();
    return of(...this.taskService.myAssignments(this.task))
      .pipe(
        mergeMap((ta) => {
          return this.taskService.answerTaskAssignment(ta.id, payload);
        })
      );
  }

  private refreshTask(task: TaskModel): Observable<TaskModel> {
    if (!this.refreshTaskAfterDone) {
      return of(task);
    }

    return this.getTask(task.id)
      .pipe(
        concatMap((val) => this.taskService.getAuthorAndAssigneesForTasks([val])),
        map((tasks) => tasks[0])
      );
  }
}
