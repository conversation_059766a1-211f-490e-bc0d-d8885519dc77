<div class="d-flex p-t-spacing-md p-b-spacing-xx-s gap-spacing-sm align-items-start">
  <app-user-avatar [user]="task.author" [hasSubTitle]="false" [showTooltip]="false" [size]="avatarSize">
  </app-user-avatar>

  <div class="flex-fill d-flex flex-column">
    <div class="content-heading-h5 content-color-primary">{{ task.author | userTitle }}</div>
    <div class="content-body-xsmall content-color-tertiary">
      <span ngbTooltip="Creation date" tooltipClass="white-tooltip">
        {{ task.created_at + '+00:00' | timeReadable }}
      </span>
    </div>
  </div>

  <div *ngIf="hasActions" [ngbPopover]="popoverActionsTemplate" [autoClose]="true"
       popoverClass="white-popover inline-menu-popper" container="body" placement="bottom-right"
       class="figma-dropdown-btn content-label-small button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center">
    <i class="fa-regular fa-ellipsis-vertical cursor-pointer p-spacing-s fa-fw fa-1x"></i>
  </div>
</div>

<div class="m-y-spacing-md">
  <div appTextEllipsis class="content-body-small ellipsis-text-4"
       toggleCssClass="ellipsis-text-4"
       [ngClass]="{'content-color-primary': task.description?.length, 'content-color-quartary': !task.description?.length}">
    {{ task.description ? task.description : 'No description' }}
  </div>
</div>

<div *ngIf="showStatus" class="d-flex align-items-center gap-spacing-xx-s">
  <div class="request-base-view-name d-flex p-y-spacing-x-s p-x-spacing-xx-s align-items-center gap-spacing-xx-s">
    <i class="fa-regular fa-loader fa-sm fa-fw content-color-quartary"></i>
    <div class="content-body-small content-color-tertiary">
      Status
    </div>
  </div>
  <div class="patent-task-status content-label-xsmall badge-grey badge-small"
       [ngbTooltip]="getTaskAssignmentStatusTooltip(this.taskAssignment.status)" tooltipClass="white-tooltip">
    {{ getTaskAssignmentStatusLabel(this.taskAssignment.status) }}
  </div>
</div>

<div *ngIf="showTopics" class="d-flex align-items-center gap-spacing-xx-s">
  <div class="request-base-view-name d-flex p-y-spacing-x-s p-x-spacing-xx-s align-items-center gap-spacing-xx-s">
    <i class="fa-regular fa-circle-caret-down fa-sm fa-fw content-color-quartary"></i>
    <div class="content-body-small content-color-tertiary">
      Topic
    </div>
  </div>

  <app-topics-selector *ngIf="task.topics?.length"
                       [formControl]="topicIdsFormControl"
                       [topics]="task.topics"
                       [initializeGeneralTopic]="false"
                       [onlyDisplay]="true"
                       [showTooltip]="false"
                       class="flex-grow-1">
  </app-topics-selector>
</div>

<div *ngIf="showDeadline && task.deadline" class="d-flex align-items-center gap-spacing-xx-s">
  <div class="request-base-view-name d-flex p-y-spacing-x-s p-x-spacing-xx-s align-items-center gap-spacing-xx-s">
    <i class="fa-regular fa-calendar-clock fa-sm fa-fw content-color-quartary"></i>
    <div class="content-body-small content-color-tertiary">
      Due date
    </div>
  </div>
  <div class="content-body-small content-color-primary">
    {{ task.deadline + '+00:00' | dateFormat: 'ShortDate' }}
  </div>
</div>

<div *ngIf="showAnsweredAt && taskAssignment.answered_at" class="d-flex align-items-center gap-spacing-xx-s">
  <div class="request-base-view-name d-flex p-y-spacing-x-s p-x-spacing-xx-s align-items-center gap-spacing-xx-s">
    <i class="fa-regular fa-calendar-clock fa-sm fa-fw content-color-quartary"></i>
    <div class="content-body-small content-color-tertiary">
      Completed
    </div>
  </div>
  <div class="content-body-small content-color-primary">
    {{ taskAssignment.answered_at + '+00:00' | dateFormat: 'ShortDate' }}
  </div>
</div>

<ng-template #popoverActionsTemplate>
  <ng-content select="[actions]"></ng-content>
</ng-template>
