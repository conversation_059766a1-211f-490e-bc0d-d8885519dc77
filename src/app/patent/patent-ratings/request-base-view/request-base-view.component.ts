import { Component, Input } from '@angular/core';
import { TaskAssignmentModel, TaskModel } from '@core';
import { getTaskAssignmentStatusLabel, getTaskAssignmentStatusTooltip } from '@patent/patent-ratings/shared/types';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-patent-request-base-view',
  templateUrl: './request-base-view.component.html',
  styleUrls: ['./request-base-view.component.scss']
})
export class RequestBaseViewComponent {
  @Input() task: TaskModel = null;
  @Input() hasActions: boolean = false;
  @Input() showStatus: boolean = false;
  @Input() showTopics: boolean = false;
  @Input() showDeadline: boolean = true;
  @Input() showAnsweredAt: boolean = false;
  @Input() avatarSize: 'xxxsmall' | 'xxsmall' | 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge' | 'xxlarge' | 'huge' | 'xhuge' = 'small';

  readonly getTaskAssignmentStatusLabel = getTaskAssignmentStatusLabel;
  readonly getTaskAssignmentStatusTooltip = getTaskAssignmentStatusTooltip;

  readonly topicIdsFormControl = new FormControl({value: [], disabled: true});

  get taskAssignment(): TaskAssignmentModel {
    return this.task.assignments[0];
  }
}
