<div class="radius-sm d-flex flex-column figma-bg-primary border-1 border-subtle"
     (click)="onViewRequestClicked()" appMouseHover hoverClass="border-bold card-hovered"
     [ngClass]="{'cursor-pointer': showActionButton}">
  <div class="d-flex p-x-spacing-sm p-t-spacing-sm p-b-spacing-xx-s gap-spacing-xx-s align-items-start">
    <app-user-avatar [user]="task.author" [hasSubTitle]="false" [showTooltip]="showTooltip"></app-user-avatar>
    <div class="content-body-small content-color-primary flex-fill" [innerHtml]="cardTitle"></div>

    <div *ngIf="openNewTab"
         class="button-main-tertiary-grey button-square button-small cursor-pointer open-new-tab-button invisible"
         ngbTooltip="Open request in new window" tooltipClass="white-tooltip"
         (click)="onOpenNewTabButtonClicked($event, task)">
      <i class="fa-regular fa-arrow-up-right-from-square cursor-pointer fa-fw fa-1x"></i>
    </div>

    <span *ngIf="showActionButton" [ngbTooltip]="isAnswerable ? 'Answer this request' : 'View request'" tooltipClass="white-tooltip"
          class="button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center no-hover">
      <i class="fa-regular fa-chevron-right cursor-pointer p-spacing-s fa-fw fa-1x"></i>
    </span>
  </div>

  <div class="request-card-footer d-flex p-spacing-sm gap-spacing-sm align-items-center justify-content-between border-0 border-t-1 border-t-subtle">
    <div class="d-flex gap-spacing-xx-s align-items-center" ngbTooltip="Due date" tooltipClass="white-tooltip">
      <ng-container *ngIf="task.deadline">
        <i class="fa-regular fa-calendar-clock fa-sm fa-fw content-color-quartary"></i>
        <div class="content-body-xsmall content-color-quartary">
          {{ task.deadline + '+00:00' | dateFormat: 'ShortDate' }}
        </div>
      </ng-container>
    </div>

    <div class="patent-task-status content-label-xsmall badge-grey badge-small"
         [ngbTooltip]="getTaskAssignmentStatusTooltip(representativeTaskAssignmentStatus)" tooltipClass="white-tooltip">
      {{ getTaskAssignmentStatusLabel(representativeTaskAssignmentStatus) }}
    </div>
  </div>
</div>
