import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RequestCardComponent } from './request-card.component';
import { PluralizePipe, TaskAssignmentModel, TaskModel, UsersTitleTextPipe, UserTitlePipe } from '@core';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('RatingCardComponent', () => {
  let component: RequestCardComponent;
  let fixture: ComponentFixture<RequestCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RequestCardComponent ],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [
        UsersTitleTextPipe,
        UserTitlePipe,
        PluralizePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RequestCardComponent);
    component = fixture.componentInstance;
    component.task = {
      assignee: {},
      assignments: [
        {
          answer: '',
          answered_at: '2021-09-01T00:00:00',
          status: 'DONE'
        } as unknown as TaskAssignmentModel
      ],
      team_users: []
    } as unknown as TaskModel;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
