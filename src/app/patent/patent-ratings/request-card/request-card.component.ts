import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { TaskAssignmentStatusEnum, TaskModel, TaskService } from '@core';
import {
  getTaskAssignmentStatusLabel,
  getTaskAssignmentStatusTooltip
} from '@patent/patent-ratings/shared/types';
import { finalize, Subscription } from 'rxjs';

@Component({
  selector: 'app-patent-request-card',
  templateUrl: './request-card.component.html',
  styleUrls: ['./request-card.component.scss']
})
export class RequestCardComponent implements OnDestroy {
  @Input() task: TaskModel = null;
  @Input() isAnswerable: boolean = false;
  @Input() showActionButton = true;
  @Input() openNewTab = false;
  @Input() showTooltip = true;

  @Output() viewRequestEvent: EventEmitter<TaskModel> = new EventEmitter<TaskModel>();

  readonly getTaskAssignmentStatusLabel = getTaskAssignmentStatusLabel;
  readonly getTaskAssignmentStatusTooltip = getTaskAssignmentStatusTooltip;

  private isOpening = false;
  private subscriptions = new Subscription();

  constructor(
    private taskService: TaskService
  ) {
  }

  get isMyTask(): boolean {
    return this.taskService.isTaskCreatedByMe(this.task);
  }

  get representativeTaskAssignmentStatus(): TaskAssignmentStatusEnum {
    return this.taskService.representativeTaskAssignmentStatus(this.task);
  }

  get cardTitle(): string {
    return this.taskService.getRequestTitle(this.task, this.isAnswerable);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onViewRequestClicked() {
    if (this.taskService.isTaskCreatedByMe(this.task)) {
      this.viewRequestEvent.emit(this.task);
      return;
    }

    if (this.isOpening) {
      return;
    }

    this.isOpening = true;
    const markMyTaskAssignmentsAsOpen$ = this.taskService.markMyTaskAssignmentsAsOpen([this.task])
      .pipe(
        finalize(() => {
          this.isOpening = false;
          this.viewRequestEvent.emit(this.task);
        }),
      )
      .subscribe();
    this.subscriptions.add(markMyTaskAssignmentsAsOpen$);
  }

  onOpenNewTabButtonClicked(event: MouseEvent, task: TaskModel) {
    event.preventDefault();
    event.stopPropagation();
    window.open(`/patent/view/${task.document_id}?rating_id=${task.id}`, '_blank');
  }
}
