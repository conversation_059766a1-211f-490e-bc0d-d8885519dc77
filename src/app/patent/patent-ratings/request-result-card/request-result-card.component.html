<div class="radius-sm d-flex flex-column radius-none"
     appMouseHover hoverClass="rating-card-hovered">
  <div class="d-flex p-b-spacing-xx-s gap-spacing-sm align-items-start">
    <app-user-avatar [user]="taskAssignment?.answered_user" [hasSubTitle]="false" [showTooltip]="false" size="small">
    </app-user-avatar>

    <div class="flex-fill d-flex flex-column">
      <div *ngIf="!isGroupAssignee" class="content-heading-h5 content-color-primary">{{ taskAssignment?.answered_user | userTitle }}</div>

      <div *ngIf="isGroupAssignee" class="d-flex">
        <app-teams-selector [showMe]="true"
                            [formControl]="assigneesFormControl"
                            [onlyDisplay]="true"
                            [initialTeams]="task?.team_users"
                            [placeholder]="taskAssignment?.answered_user | userTitle"
                            [ngbTooltip]="'The rating was done by ' + (taskAssignment?.answered_user | userTitle) + '.'"
                            tooltipClass="white-tooltip" placement="top">
        </app-teams-selector>
      </div>

      <div class="content-body-xsmall content-color-tertiary">
        <span *ngIf="taskAssignment?.edited_at">
          {{ taskAssignment.edited_at + '+00:00' | timeReadable }} (edited)
        </span>
        <span *ngIf="taskAssignment?.answered_at && !taskAssignment?.edited_at">
          {{ taskAssignment.answered_at + '+00:00' | timeReadable }}
        </span>
      </div>
    </div>

    <div class="d-flex gap-spacing-sm align-items-center">
      <div *ngIf="openNewTab" class="button-main-tertiary-grey button-square button-small cursor-pointer open-new-tab-button invisible"
           ngbTooltip="Open rating in new window" tooltipClass="white-tooltip" (click)="onOpenNewTabButtonClicked($event, task)">
        <i class="fa-regular fa-arrow-up-right-from-square cursor-pointer fa-fw fa-1x"></i>
      </div>

      <app-tooltip
        *ngIf="!canEdit && isTaskAssignmentAnsweredByMe && isNotAdminOrManager && showDeletableInfo"
        tooltipText='This rating was posted over 24 hours ago and can no longer be edited or deleted.<br/>If you wish to delete it, please ask an admin to delete it for you.'
        tooltipPosition="left" tooltipIconSize="sm"
        [tooltipClickable]="false" tooltipTextClass="text-left" class="d-none">>
      </app-tooltip>

      <div *ngIf="showActionButton && (canEdit || canDelete)"
           [ngbPopover]="popoverActionsTemplate" [autoClose]="true"
           popoverClass="white-popover inline-menu-popper" container="body" placement="bottom-right"
           class="figma-dropdown-btn content-label-small button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center">
        <i class="fa-regular fa-ellipsis-vertical cursor-pointer p-spacing-s fa-fw fa-1x"></i>
      </div>
    </div>
  </div>

  <div *ngIf="!isYesNoAnswerTaskType" class="d-flex justify-content-between align-items-center p-y-spacing-x-s p-x-spacing-sm gap-spacing-sm"
       appMouseHover hoverClass="figma-bg-secondary rating-result-hovered" [hoverDisabled]="!showStarsTooltip">
    <div class="d-flex justify-content-start align-items-center gap-spacing-sm">
      <ngb-rating [max]="5" [rate]="ratingValue" [readonly]="true"
                  class="d-flex gap-spacing-sm rating-star">
        <ng-template let-fill="fill" let-index="index">
          <span *ngIf="fill === 100" class="fa-stack fa-2x fa-stack-star fa-stack-lg" [ngClass]="'rated-star-' + ratingValue">
            <i class="fa-regular fa-circle fa-stack-2x"></i>
            <i class="fa-solid fa-star-circle fa-stack-1x"></i>
          </span>
          <span *ngIf="fill === 0" class="fa-stack fa-2x fa-stack-star fa-stack-lg">
            <i class="fa-regular fa-circle fa-stack-2x"></i>
            <i class="fa-solid fa-star-circle fa-stack-1x"></i>
          </span>
        </ng-template>
      </ngb-rating>

      <div class="content-label-xsmall content-color-tertiary">
        {{ ratingValue }} {{ 'Point' | pluralize: ratingValue }}
      </div>
    </div>

    <app-tooltip *ngIf="showStarsTooltip"
      tooltipText='Ratings assigned to this patent may vary in meaning for each user depending on their research context.'
      tooltipPosition="left" tooltipIconSize="sm"
      [tooltipClickable]="false" tooltipTextClass="text-left" class="d-none">
    </app-tooltip>
  </div>

  <div *ngIf="isYesNoAnswerTaskType" class="d-flex justify-content-between align-items-center p-y-spacing-x-s p-r-spacing-sm gap-spacing-sm">
    <div class="d-flex justify-content-start align-items-center gap-spacing-sm">
      <span *ngIf="taskService.isPassValue(taskAssignment?.answer)" class="button-main-tertiary-grey button-small content-label-small content-color-active no-hover">
          <i class="fa-solid fa-thumbs-up m-r-spacing-xx-s"></i> Pass
        </span>
      <span *ngIf="taskService.isNoPassValue(taskAssignment?.answer)" class="button-main-tertiary-grey button-small content-label-small content-color-danger no-hover">
          <i class="fa-solid fa-thumbs-down m-r-spacing-xx-s"></i> No pass
        </span>
    </div>
  </div>

  <app-topics-selector *ngIf="task.topics?.length"
                       [formControl]="topicIdsFormControl"
                       [topics]="task.topics"
                       [initializeGeneralTopic]="false"
                       [onlyDisplay]="true"
                       [showTooltip]="showTopicsTooltip"
                       class="w-100 m-t-spacing-sm">
  </app-topics-selector>

  <div *ngIf="taskAssignment?.message?.length" class="content-body-small content-color-primary m-t-spacing-xxx-s">
    <div appTextEllipsis class="ellipsis-text-2" toggleCssClass="ellipsis-text-2">
      {{ taskAssignment.message }}
    </div>
  </div>

  <div *ngIf="task.description?.length" class="m-t-spacing-sm radius-sm border-1 border-subtle cursor-pointer"
       [ngClass]="{'figma-bg-subtle p-x-spacing-sm p-y-spacing-xx-s ': isCollapsed, 'figma-bg-transition p-spacing-sm': !isCollapsed}"
       (click)="isCollapsed = !isCollapsed">
    <div *ngIf="isCollapsed else taskDescriptionTemplate"
         class="d-flex justify-content-start align-items-center gap-spacing-sm">
      <i class="fa-regular fa-message-lines fa-fw fa-1x"></i>

      <div class="d-flex justify-content-start align-items-center flex-grow-1 gap-spacing-xx-s">
        <app-user-avatar [user]="task.author" [hasSubTitle]="false"></app-user-avatar>
        <div class="d-flex gap-spacing-xx-s content-body-small content-color-tertiary">See reporter’s message</div>
      </div>

      <span class="button-square button-small d-flex justify-content-center align-items-center">
        <i class="fa-regular fa-chevron-down fa-fw fa-1x"></i>
      </span>
    </div>
  </div>

</div>

<ng-template #popoverActionsTemplate>
  <div *ngIf="canEdit" class="p-x-spacing-sm" (click)="onEditClicked()"
       [ngClass]="{'p-t-spacing-sm p-b-spacing-xx-s': canDelete, 'p-y-spacing-sm': !canDelete}">
    <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
      <i class="fa-regular fa-pen fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Edit review
    </div>
  </div>
  <div *ngIf="canEdit && canDelete" class="popover-divider"></div>
  <div *ngIf="canDelete" class="p-x-spacing-sm" (click)="onDeleteClicked($event)"
       [ngClass]="{'p-b-spacing-sm p-t-spacing-xx-s': canEdit, 'p-y-spacing-sm': !canEdit}">
    <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
      <i class="fa-regular fa-trash-can fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Delete
    </div>
  </div>
</ng-template>

<ng-template #taskDescriptionTemplate>
  <div class="d-flex justify-content-start align-items-start gap-spacing-sm radius-sm">
    <app-user-avatar [user]="task.author" [hasSubTitle]="false" [showTooltip]="false"></app-user-avatar>

    <div class="d-flex flex-column justify-content-center align-items-start flex-grow-1">
      <div class="content-heading-h6 content-color-primary">{{ task.author | userTitle }}</div>

      <div class="d-flex gap-spacing-xx-s content-body-xsmall content-color-tertiary">
        {{ task.created_at + '+00:00' | timeReadable }}
      </div>
    </div>
    <span class="button-square button-small d-flex justify-content-center align-items-center">
      <i class="fa-regular fa-chevron-up fa-fw fa-1x"></i>
    </span>
  </div>

  <div class="m-t-spacing-xx-s content-body-small content-color-primary">
    {{ task.description }}
  </div>
</ng-template>
