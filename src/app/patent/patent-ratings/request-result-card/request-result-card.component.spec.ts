import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RequestResultCardComponent } from './request-result-card.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { PluralizePipe, TaskAssignmentModel, TaskModel, UsersTitleTextPipe, UserTitlePipe } from '@core';
import { provideMatomo } from 'ngx-matomo-client';
import { RouterModule } from '@angular/router';

describe('RequestResultCardComponent', () => {
  let component: RequestResultCardComponent;
  let fixture: ComponentFixture<RequestResultCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RequestResultCardComponent ],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [
        UsersTitleTextPipe,
        UserTitlePipe,
        PluralizePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RequestResultCardComponent);
    component = fixture.componentInstance;
    component = fixture.componentInstance;
    component.task = {
      assignee: {},
      assignments: [
        {
          answer: '',
          answered_at: '2021-09-01T00:00:00',
          status: 'DONE'
        } as unknown as TaskAssignmentModel
      ],
      team_users: []
    } as unknown as TaskModel;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
