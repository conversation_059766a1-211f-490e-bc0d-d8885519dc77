import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import {
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskModel,
  TaskService,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core';
import { FormControl } from '@angular/forms';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize } from 'rxjs/operators';
import { ShowRatingsEventParams } from '@patent/patent-ratings/shared/types';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-patent-request-result-card',
  templateUrl: './request-result-card.component.html',
  styleUrls: ['./request-result-card.component.scss']
})
export class RequestResultCardComponent implements OnDestroy {
  @Input() showActionButton = true;
  @Input() openNewTab = false;
  @Input() showDeletableInfo = true;
  @Input() showStarsTooltip = true;
  @Input() showTopicsTooltip = true;
  @Output() editRatingEvent: EventEmitter<TaskModel> = new EventEmitter<TaskModel>();
  @Output() ratingDeletedEvent: EventEmitter<ShowRatingsEventParams> = new EventEmitter<ShowRatingsEventParams>();

  isCollapsed: boolean = true;

  topicIdsFormControl = new FormControl({value: [], disabled: true});
  assigneesFormControl = new FormControl({value: this.task?.assignees, disabled: true});

  private isDeleting: boolean = false;
  private subscriptions = new Subscription();

  constructor(
    public taskService: TaskService,
    private ngbModal: NgbModal,
    private toastService: ToastService,
    private userService: UserService
  ) {
  }

  private _task: TaskModel = null;

  get task(): TaskModel {
    return this._task;
  }

  @Input()
  set task(task: TaskModel) {
    this._task = task || {} as TaskModel;
    this.assigneesFormControl.setValue(this.task?.assignees);
  }

  get taskAssignment(): TaskAssignmentModel {
    return this.task.assignments[0];
  }

  get ratingValue(): number {
    return this.taskAssignment ? Math.round(this.taskService.answerToRating(this.taskAssignment.answer)) : 0;
  }

  get isGroupAssignee(): boolean {
    return this.taskService.isGroupTaskAssignment(this.taskAssignment);
  }

  get canEdit(): boolean {
    return this.taskService.canEditRatingTaskAssignment(this.taskAssignment);
  }

  get canDelete(): boolean {
    return this.taskService.canDeleteRatingTaskAssignment(this.taskAssignment);
  }

  get isTaskAssignmentAnsweredByMe(): boolean {
    return this.taskService.isTaskAssignmentAnsweredByMe(this.taskAssignment);
  }

  get isNotAdminOrManager(): boolean {
    return !(this.userService.isManager() || this.userService.isAdmin());
  }

  get isYesNoAnswerTaskType(): boolean {
    return this.taskService.isYesNoAnswerTaskType(this.task);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onEditClicked() {
    this.editRatingEvent.emit(this.task);
  }

  onDeleteClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isDeleting) {
      return;
    }

    this.isDeleting = true;
    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'lg', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Delete rating request',
      description: 'This rating will be removed from the list of ratings in this document and other assignees.',
      question: 'Are you sure you want to delete this rating?',
      confirmButton: 'Delete',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.deleteTaskAssignment();
      } else {
        this.isDeleting = false;
      }
    }, () => {
      this.isDeleting = false;
    });
  }

  private deleteTaskAssignment() {
    const subscription = this.taskService.deleteTaskAssignment(this.taskAssignment.id)
      .pipe(
        finalize(() => this.isDeleting = false)
      )
      .subscribe({
        next: () => {
          this.ratingDeletedEvent.emit({refresh: true} as ShowRatingsEventParams);
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: 'The rating was deleted',
            body: 'The rating was deleted successfully!',
            delay: 5000
          });
        },
        error: ({error}) => {
          console.log(error);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to delete the rating',
            body: `There was an error in deleting the rating.<br/>${error.message}`,
            delay: 10000
          });
        }
      });
    this.subscriptions.add(subscription);
  }

  onOpenNewTabButtonClicked(event: MouseEvent, task: TaskModel) {
    event.preventDefault();
    event.stopPropagation();
    window.open(`/patent/view/${task.document_id}?rating_id=${task.id}&assignment_id=${this.taskAssignment.id}`, '_blank');
  }
}
