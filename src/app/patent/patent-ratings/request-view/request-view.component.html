<app-patent-side-section-header [title]="headerTitle" [isSubHeader]="true" (closeEvent)="onCloseClicked()"
                                class="p-spacing-md d-block">
</app-patent-side-section-header>

<div class="w-100 divider m-spacing-none"></div>

<div appScrollbarDetector
     class="radius-sm d-flex flex-column figma-bg-primary flex-grow-1 overflow-auto">
  <app-patent-request-base-view [task]="task" [hasActions]="canEdit || canDelete" [showStatus]="false"
                                [showTopics]="!!task.deadline"
                                class="p-x-spacing-md p-y-spacing-sm">
    <ng-container actions>
      <div *ngIf="canEdit" class="p-x-spacing-sm p-t-spacing-sm"
           [ngClass]="{'p-b-spacing-xx-s': canDelete, 'p-b-spacing-sm': !canDelete}"
           (click)="onEditClicked()">
        <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
          <i class="fa-regular fa-pen fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Edit request
        </div>
      </div>
      <div *ngIf="canEdit && canDelete" class="popover-divider"></div>
      <div *ngIf="canDelete" class="p-x-spacing-sm p-b-spacing-sm"
           [ngClass]="{'p-t-spacing-xx-s': canEdit, 'p-t-spacing-sm': !canEdit}"
           (click)="onDeleteClicked($event)">
        <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium content-color-primary">
          <i class="fa-regular fa-trash-can fa-fw fa-1x m-r-spacing-x-s content-color-primary"></i>Delete
        </div>
      </div>
    </ng-container>
  </app-patent-request-base-view>

  <div class="w-100 divider m-spacing-none m-b-spacing-md"></div>

  <ng-container *ngFor="let ta of task.assignments; let last = last;">
    <div class="d-flex flex-column align-items-stretch p-spacing-md">
      <div class="d-flex flex-column gap-spacing-sm">
        <div class="d-flex gap-spacing-sm align-items-start">
          <div *ngIf="isGroupTaskAssignment(ta)" class="flex-fill">
            <div class="d-flex flex-column align-items-start">
              <app-teams-selector [showMe]="true"
                                  [formControl]="getAssigneeFormControl(ta)"
                                  [onlyDisplay]="true"
                                  [initialTeams]="[ta.assignee]"
                                  [placeholder]="ta.assignee | userTitle"
                                  [ngbTooltip]="ta?.answered_user ? 'The rating was done by ' + (ta?.answered_user | userTitle) + '.' : null"
                                  tooltipClass="white-tooltip" placement="top">
              </app-teams-selector>

              <div *ngIf="ta.answered_at || ta.edited_at" class="content-body-xsmall content-color-tertiary">
                <span *ngIf="ta.edited_at">
                  {{ ta.edited_at + '+00:00' | timeReadable }} (edited)
                </span>
                <span *ngIf="ta.answered_at && !ta.edited_at">
                  {{ ta.answered_at + '+00:00' | timeReadable }}
                </span>
              </div>
            </div>
          </div>

          <ng-container *ngIf="isUserTaskAssignment(ta)">
            <app-user-avatar [user]="ta.assignee" [hasSubTitle]="false" [showTooltip]="false">
            </app-user-avatar>

            <div class="flex-fill d-flex flex-column">
              <div class="content-heading-h5 content-color-primary">{{ ta.assignee | userTitle }}</div>

              <div *ngIf="ta.answered_at || ta.edited_at" class="content-body-xsmall content-color-tertiary">
                <span *ngIf="ta.edited_at">
                  {{ ta.edited_at + '+00:00' | timeReadable }} (edited)
                </span>
                <span *ngIf="ta.answered_at && !ta.edited_at">
                  {{ ta.answered_at + '+00:00' | timeReadable }}
                </span>
              </div>
            </div>
          </ng-container>

          <div class="patent-task-status content-label-xsmall cursor-default badge-grey badge-small"
               [ngbTooltip]="getTaskAssignmentStatusTooltip(ta.status)" tooltipClass="white-tooltip">
            {{ getTaskAssignmentStatusLabel(ta.status) }}
          </div>
        </div>

        <div *ngIf="!hasBinaryRating"
             class="d-flex justify-content-start align-items-center m-y-spacing-x-s p-x-spacing-sm gap-spacing-sm">
          <ngb-rating [max]="5" [rate]="getTaskAssignmentRatingValue(ta)" [readonly]="true"
                      class="d-flex gap-spacing-sm rating-star">
            <ng-template let-fill="fill" let-index="index">
              <span *ngIf="fill === 100" class="fa-stack fa-2x fa-stack-star fa-stack-lg"
                    [ngClass]="'rated-star-' + getTaskAssignmentRatingValue(ta)">
                <i class="fa-regular fa-circle fa-stack-2x"></i>
                <i class="fa-solid fa-star-circle fa-stack-1x"></i>
              </span>
                  <span *ngIf="fill === 0" class="fa-stack fa-2x fa-stack-star fa-stack-lg">
                <i class="fa-regular fa-circle fa-stack-2x"></i>
                <i class="fa-solid fa-star-circle fa-stack-1x"></i>
              </span>
            </ng-template>
          </ngb-rating>

          <div class="content-label-xsmall content-color-tertiary">
            {{ getTaskAssignmentRatingValue(ta) }} {{'Point' | pluralize: getTaskAssignmentRatingValue(ta)}}
          </div>
        </div>

        <div *ngIf="hasBinaryRating"
             class="d-flex justify-content-start align-items-center m-y-spacing-x-s p-r-spacing-sm gap-spacing-sm">
          <span class="button-main-tertiary-grey button-small content-label-small no-hover">
            <i class="fa-regular fa-thumbs-up m-r-spacing-xx-s"></i> Pass
          </span>
          <span class="button-main-tertiary-grey button-small content-label-small no-hover">
            <i class="fa-regular fa-thumbs-down m-r-spacing-xx-s"></i> No pass
          </span>
        </div>

      </div>
    </div>

    <div *ngIf="!last" class="w-100 divider m-spacing-none m-b-spacing-sm"></div>
  </ng-container>

</div>
