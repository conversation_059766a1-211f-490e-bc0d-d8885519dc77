import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import {
  PluralizePipe,
  TaskAssignmentModel,
  TaskModel,
  TaskService,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core';
import { ShowRatingsEventParams, getTaskAssignmentStatusLabel, getTaskAssignmentStatusTooltip } from '@patent/patent-ratings/shared/types';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { finalize } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-patent-request-view',
  templateUrl: './request-view.component.html',
  styleUrls: ['./request-view.component.scss']
})
export class RequestViewComponent implements OnDestroy {
  @Output() hideRequestViewEvent: EventEmitter<ShowRatingsEventParams> = new EventEmitter<ShowRatingsEventParams>();

  headerTitle: string = '';
  isDeleting: boolean = false;

  readonly getTaskAssignmentStatusLabel = getTaskAssignmentStatusLabel;
  readonly getTaskAssignmentStatusTooltip = getTaskAssignmentStatusTooltip;

  private subscriptions = new Subscription();

  constructor(
    public taskService: TaskService,
    private pluralizePipe: PluralizePipe,
    private toastService: ToastService,
    private userService: UserService,
    private ngbModal: NgbModal
  ) {
  }

  private _task: TaskModel = null;

  get task(): TaskModel {
    return this._task;
  }

  @Input()
  set task(task: TaskModel) {
    this._task = task || {} as TaskModel;
    this.headerTitle = this.getHeaderTitle();
  }

  get canEdit(): boolean {
    return this.taskService.isTaskCreatedByMe(this.task);
  }

  get canDelete(): boolean {
    return this.taskService.isTaskCreatedByMe(this.task);
  }

  get hasBinaryRating(): boolean {
    return this.userService.hasBinaryRating;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getTaskAssignmentRatingValue(taskAssignment: TaskAssignmentModel): number {
    return this.taskService.answerToRating(taskAssignment.answer);
  }

  onCloseClicked() {
    this.hideRequestViewEvent.emit({refresh: true} as ShowRatingsEventParams);
  }

  isUserTaskAssignment(ta: TaskAssignmentModel): boolean {
    return this.taskService.isUserTaskAssignment(ta);
  }

  isGroupTaskAssignment(ta: TaskAssignmentModel): boolean {
    return this.taskService.isGroupTaskAssignment(ta);
  }

  onEditClicked() {
    this.hideRequestViewEvent.emit({showRatingForm: true, currentRatingTask: this.task} as ShowRatingsEventParams);
  }

  getAssigneeFormControl(ta: TaskAssignmentModel): FormControl {
    return new FormControl({value: [ta.assignee], disabled: true});
  }

  onDeleteClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isDeleting) {
      return;
    }

    this.isDeleting = true;
    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'lg', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Delete rating request',
      description: 'This request will be removed from the profiles of the current pending assignees',
      question: 'Are you sure you want to delete this request?',
      confirmButton: 'Delete',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.deleteTask();
      } else {
        this.isDeleting = false;
      }
    }, () => {
      this.isDeleting = false;
    });
  }

  private getHeaderTitle() {
    if (!this.taskService.isTaskCreatedByMe(this.task)) {
      return 'Your rating';
    }
    const assigneeNames = this.taskService.splitTeamUsers(this.task)
      .filter(({items}) => items.length > 0)
      .map(({title, items}) => `${items.length} ${this.pluralizePipe.transform(title, items.length)}`)
      .join(' and ') || 'No one';
    return `${assigneeNames} rating`;
  }

  private deleteTask() {
    const subscription = this.taskService.deleteTask(this.task.id)
      .pipe(
        finalize(() => this.isDeleting = false)
      )
      .subscribe({
        next: () => {
          this.hideRequestViewEvent.emit({refresh: true} as ShowRatingsEventParams);
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: 'The request was deleted',
            body: 'Your request was deleted successfully!',
            delay: 5000
          });
        },
        error: ({error}) => {
          console.log(error);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to delete the request',
            body: `There was an error in deleting the request.<br/>${error.message}`,
            delay: 10000
          });
        }
      });
    this.subscriptions.add(subscription);
  }
}
