import { RatingSorter } from './rating-sorter';
import { SortRatingsOptionEnum } from './types';
import { TaskAssignmentModel, TaskModel } from '@core/models/task.model';

describe('RatingSorter', () => {
  describe('sortByDeadline', () => {
    const createTask = (deadline: string | null, created_at: string) => ({
      deadline,
      created_at: new Date(created_at),
      assignments: []
    }) as any as TaskModel;

    describe('deadline comparisons', () => {
      it('should prioritize tasks with deadlines over those without', () => {
        const withDeadline = createTask('2024-01-01', '2023-12-01');
        const noDeadline = createTask(null, '2023-12-01');

        expect(RatingSorter.sortByDeadline(withDeadline, noDeadline)).toBe(-1);
        expect(RatingSorter.sortByDeadline(noDeadline, withDeadline)).toBe(1);
      });

      it('should sort by newest created_at when both tasks have no deadlines', () => {
        const newerTask = createTask(null, '2023-12-01');
        const olderTask = createTask(null, '2023-11-01');

        const result = RatingSorter.sortByDeadline(newerTask, olderTask);
        expect(result).toBeLessThan(0);
      });
    });

    describe('deadline ordering', () => {
      it('should order tasks by earliest deadline first', () => {
        const tasks = [
          createTask('2024-03-01', '2023-12-01'),
          createTask('2024-01-01', '2023-12-01'),
          createTask('2024-02-01', '2023-12-01')
        ];

        const sortedTasks = [...tasks].sort(RatingSorter.sortByDeadline);
        expect(sortedTasks.map(t => t.deadline)).toEqual([
          tasks[1].deadline,
          tasks[2].deadline,
          tasks[0].deadline
        ]);
      });
    });

    describe('creation date ordering', () => {
      it('should sort by newest created_at when deadlines are equal', () => {
        const tasks = [
          createTask('2024-01-01', '2023-11-01'),
          createTask('2024-01-01', '2023-12-01'),
          createTask('2024-01-01', '2023-10-01')
        ];

        const sortedTasks = [...tasks].sort(RatingSorter.sortByDeadline);
        expect(sortedTasks.map(t => t.created_at.toISOString())).toEqual([
          tasks[1].created_at.toISOString(),
          tasks[0].created_at.toISOString(),
          tasks[2].created_at.toISOString()
        ]);
      });
    });

    describe('edge cases', () => {
      it('should handle invalid dates', () => {
        const validTask = createTask('2024-01-01', '2023-12-01');
        const taskWithInvalidDeadline = {
          ...createTask(null, '2023-12-01'),
          deadline: new Date('invalid')
        } as any as TaskModel

        expect(RatingSorter.sortByDeadline(validTask, taskWithInvalidDeadline)).toBe(-1);
      });

      it('should handle undefined assignments array', () => {
        const task1 = {...createTask('2024-01-01', '2023-12-01'), assignments: undefined};
        const task2 = createTask('2024-01-01', '2023-12-01');

        expect(() => RatingSorter.sortByDeadline(task1, task2)).not.toThrow();
      });
    });
  });

  describe('sortByOption', () => {
    const createTaskWithAssignment = (answered_at: string, answer: string | number): TaskModel => ({
      deadline: null,
      created_at: new Date('2023-12-01'),
      assignments: [{
        answered_at: new Date(answered_at),
        answer: answer.toString()
      } as any as TaskAssignmentModel]
    }) as any as TaskModel;

    const mockRatingFunc = (answer: string) => Number(answer);

    describe('RECENTLY_ADDED sorting', () => {
      it('should sort by most recent answered_at first', () => {
        const tasks = [
          createTaskWithAssignment('2023-11-01', '5'),
          createTaskWithAssignment('2023-12-01', '3'),
          createTaskWithAssignment('2023-10-01', '4')
        ];

        const sortedTasks = [...tasks].sort((a, b) =>
          RatingSorter.sortByOption(a, b, SortRatingsOptionEnum.RECENTLY_ADDED, mockRatingFunc)
        );

        expect(sortedTasks.map(t => t.assignments[0].answered_at.toISOString()))
          .toEqual([
            tasks[1].assignments[0].answered_at.toISOString(),
            tasks[0].assignments[0].answered_at.toISOString(),
            tasks[2].assignments[0].answered_at.toISOString()
          ]);
      });
    });

    describe('points-based sorting', () => {
      const setupPointsTasks = () => [
        createTaskWithAssignment('2023-12-01', '3'),
        createTaskWithAssignment('2023-12-01', '5'),
        createTaskWithAssignment('2023-12-01', '1')
      ];

      it('should sort by ASCENDING_POINTS', () => {
        const tasks = setupPointsTasks();
        const sortedTasks = [...tasks].sort((a, b) =>
          RatingSorter.sortByOption(a, b, SortRatingsOptionEnum.HIGHEST_RATINGS, mockRatingFunc)
        );

        expect(sortedTasks.map(t => t.assignments[0].answer))
          .toEqual(['5', '3', '1']);
      });

      it('should sort by DESCENDING_POINTS', () => {
        const tasks = setupPointsTasks();
        const sortedTasks = [...tasks].sort((a, b) =>
          RatingSorter.sortByOption(a, b, SortRatingsOptionEnum.LOWEST_RATINGS, mockRatingFunc)
        );

        expect(sortedTasks.map(t => t.assignments[0].answer))
          .toEqual(['1', '3', '5']);
      });
    });

    describe('edge cases', () => {
      it('should handle invalid answer values', () => {
        const validTask = createTaskWithAssignment('2023-12-01', '5');
        const invalidTask = createTaskWithAssignment('2023-12-01', 'invalid');

        expect(() => RatingSorter.sortByOption(
          validTask,
          invalidTask,
          SortRatingsOptionEnum.HIGHEST_RATINGS,
          mockRatingFunc
        )).not.toThrow();
      });

      it('should handle missing assignments', () => {
        const task1 = {...createTaskWithAssignment('2023-12-01', '5'), assignments: []};
        const task2 = createTaskWithAssignment('2023-12-01', '3');

        expect(() => RatingSorter.sortByOption(
          task1,
          task2,
          SortRatingsOptionEnum.RECENTLY_ADDED,
          mockRatingFunc
        )).toThrow();
      });
    });
  });
});
