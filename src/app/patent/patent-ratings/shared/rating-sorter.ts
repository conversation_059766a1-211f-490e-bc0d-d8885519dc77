import { TaskModel } from '@core';
import { SortRatingsOptionEnum } from './types';

export class RatingSorter {
  private static readonly BEFORE = -1;
  private static readonly AFTER = 1;
  private static readonly EQUAL = 0;

  static sortByDeadline(a: TaskModel, b: TaskModel): number {
    // Handle invalid dates first
    const aValid = a.deadline && !isNaN(new Date(a.deadline).getTime());
    const bValid = b.deadline && !isNaN(new Date(b.deadline).getTime());

    // Tasks with valid deadlines come before invalid ones
    if (aValid !== bValid) {
      return aValid ? RatingSorter.BEFORE : RatingSorter.AFTER;
    }

    // If neither has valid deadlines, sort by creation date (newest first)
    if (!aValid && !bValid) {
      return RatingSorter.toTimestamp(b.created_at) - RatingSorter.toTimestamp(a.created_at);
    }

    // If both have valid deadlines, compare them
    const deadlineDiff = RatingSorter.toTimestamp(a.deadline) - RatingSorter.toTimestamp(b.deadline);
    return deadlineDiff || RatingSorter.toTimestamp(b.created_at) - RatingSorter.toTimestamp(a.created_at);
  }

  static sortByOption(a: TaskModel, b: TaskModel, sortOption: SortRatingsOptionEnum, answerToRatingFunc: any): number {
    const aAssignment = a.assignments[0];
    const bAssignment = b.assignments[0];
    switch (sortOption) {
      case SortRatingsOptionEnum.RECENTLY_ADDED:
        return RatingSorter.toTimestamp(bAssignment.answered_at) - RatingSorter.toTimestamp(aAssignment.answered_at);
      case SortRatingsOptionEnum.OLDEST_ADDED:
        return RatingSorter.toTimestamp(aAssignment.answered_at) - RatingSorter.toTimestamp(bAssignment.answered_at);
      case SortRatingsOptionEnum.HIGHEST_RATINGS:
        return answerToRatingFunc(bAssignment.answer) - answerToRatingFunc(aAssignment.answer);
      case SortRatingsOptionEnum.LOWEST_RATINGS:
        return answerToRatingFunc(aAssignment.answer) - answerToRatingFunc(bAssignment.answer);
    }
  }

  private static toTimestamp(date: string | Date | null): number {
    if (!date) {
      return 0;
    }
    const timestamp = new Date(date).getTime();
    return isNaN(timestamp) ? 0 : timestamp;
  }
}
