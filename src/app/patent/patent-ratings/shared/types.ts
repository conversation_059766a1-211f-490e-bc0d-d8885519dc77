import { TaskModel, TaskAssignmentStatusEnum, TaskStatusEnum, TaskAssignmentModel } from "@core/models/task.model";

export const TASK_STATUS_TOOLTIPS = [
  {tooltip: 'The assignee hasn’t opened this request yet.', value: TaskStatusEnum.NEW},
  {tooltip: 'The assignee saw this request.', value: TaskStatusEnum.OPEN},
  {tooltip: 'The assignees completed this request', value: TaskStatusEnum.DONE},
  {tooltip: 'Closed', value: TaskStatusEnum.CLOSED},
];

export const TASK_ASSIGNMENT_STATUSES = [
  {label: 'To do', value: TaskAssignmentStatusEnum.NEW},
  {label: 'In progress', value: TaskAssignmentStatusEnum.OPEN},
  {label: 'Done', value: TaskAssignmentStatusEnum.DONE},
  {label: 'Closed', value: TaskAssignmentStatusEnum.CLOSED},
  {label: 'In progress', value: TaskAssignmentStatusEnum.OVERDUE},
];

export const TASK_ASSIGNMENT_STATUS_TOOLTIPS = [
  {tooltip: 'The assignee hasn’t opened this request yet.', value: TaskAssignmentStatusEnum.NEW},
  {tooltip: 'The assignee saw this request.', value: TaskAssignmentStatusEnum.OPEN},
  {tooltip: 'The assignee completed this request.', value: TaskAssignmentStatusEnum.DONE},
  {tooltip: 'Closed', value: TaskAssignmentStatusEnum.CLOSED},
  {tooltip: 'The assignee saw this request.', value: TaskAssignmentStatusEnum.OVERDUE},
];

export const getTaskAssignmentStatusLabel = (status: TaskAssignmentStatusEnum): string => {
  return TASK_ASSIGNMENT_STATUSES.find(type => type.value === status)?.label;
}

export const getTaskAssignmentStatusTooltip = (status: TaskAssignmentStatusEnum): string => {
  return TASK_ASSIGNMENT_STATUS_TOOLTIPS.find(type => type.value === status)?.tooltip;
}
export interface ShowTasksEventParams {
  refresh: boolean;
}

export enum FilterRatingsOptionEnum {
  ALL = 'All ratings',
  ASSIGNED_TO_ME = 'Assigned to me',
  CREATED_BY_ME = 'Created by me',
}

export enum SortRatingsOptionEnum {
  RECENTLY_ADDED = 'Recently added',
  OLDEST_ADDED = 'Oldest added',
  HIGHEST_RATINGS = 'Highest ratings',
  LOWEST_RATINGS = 'Lowest ratings'
}

export interface ShowRatingsEventParams {
  refresh?: boolean;
  showRatingForm?: boolean;
  currentRatingTask?: TaskModel;
}

export interface RatingNavigationParam {
  task: TaskModel,
  assignment: TaskAssignmentModel
}
