<div *ngIf="!isLoadingRelevanceParts else loadingRelevanceParts">
  <div class="d-flex justify-content-end m-b-spacing-x-big">
    <div ngbTooltip="Filter by" tooltipClass="white-tooltip" class="figma-dropdown">
      <button [ngbPopover]="popoverFilterRelevanceTemplate" [autoClose]="true"
              popoverClass="context-menu-popper" container="body" placement="bottom-right"
              class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small">
        <i class="fa-regular fa-lg fa-bars-filter me-2"></i>
        <span class="p-x-spacing-xx-s menu-text">{{ isDefaultFilter ? 'All' : currentFilter }}</span>
      </button>
    </div>
  </div>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch overflow-auto"
      [ngClass]="{'p-r-spacing-xxx-s': hasSmartHighlights}">
      <div class="relevance-section" *ngIf="showSmartHighlights(filterRelevanceOptionEnum.ONLY_CLAIMS) && smartHighlights?.claims?.length">
          <div class="content-heading-h5">On claims</div>
          <div class="relevance-section-content content-quotes-02 content-color-tertiary" *ngFor="let claim of smartHighlights?.claims"
              (click)="goToSmartHighlight(claim)">
              {{ claim.paragraph.text }}
          </div>
      </div>
      <div class="relevance-section" *ngIf="showSmartHighlights(filterRelevanceOptionEnum.ONLY_DESCRIPTION) && smartHighlights?.description?.length">
          <div class="content-heading-h5">On description</div>
          <div class="relevance-section-content content-quotes-02 content-color-tertiary" *ngFor="let description of smartHighlights?.description"
              (click)="goToSmartHighlight(description)">
              {{ description.paragraph.text }}
          </div>
      </div>

      <ng-container *ngIf="!showSmartHighlights(currentFilter)">
        <div class="no-highlights text-center">
          <div class="no-highlights-icon">
            <span>
              <i class="fa-light fa-search"></i>
            </span>
          </div>
        </div>
        <div class="text-center pt-4 px-3">
          <p class="content-heading-h4 mb-2">Not results found.</p>
          <p class="content-body-medium content-color-tertiary">
            It seems we can’t find any result based on your selection.
          </p>
          <p class="content-body-medium content-color-tertiary">
            Try to clear or change your filters.
          </p>
        </div>
      </ng-container>
  </div>
</div>

<ng-template #loadingRelevanceParts>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch relevance-loading-template">
    <div class="d-flex justify-content-between relevance-loading-top"></div>
    <div class="relevance-loading-main-content flex-grow-1 d-flex align-items-center justify-content-center">
      <div class="content-heading-h6 content-color-secondary">Relevance parts are loading...</div>
    </div>
  </div>
</ng-template>

<ng-template #popoverFilterRelevanceTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary">Filter by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium"
       *ngFor="let op of filterOptions" [class.active]="currentFilter === op"
       (click)="onFilterOptionClicked(op)">
    {{ op }}
  </div>
</ng-template>
