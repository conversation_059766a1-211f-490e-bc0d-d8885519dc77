@import 'scss/figma2023/index';

@mixin template-box-style() {
  background: $colour-grey-200;
  border-radius: $radius-sm;
}

.relevance-section {
  margin-bottom: $spacing-system-spacing-xx-big;
  &-content {
      cursor: pointer;
      margin-top: $spacing-system-spacing-md;
      padding-left: $spacing-system-spacing-sm;
      border-left: 2px solid $colour-global-dennemeyer-orange;
      color: $colours-content-content-tertiary;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
  }
}

.no-highlights {
  margin-top: $spacing-system-spacing-xxx-big;

  &-icon {
    border-radius: 6.25rem;
    border: $spacing-system-spacing-sm solid $colour-blue-brand-100;
    background-color: $colour-blue-brand-200;
    display: inline-flex;
    align-items: center;
    padding: $spacing-system-spacing-x-big;
    gap: .625rem;

    span {
      display: flex;
      width: 4.25rem;
      height: 4.25rem;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      i {
        color: $colour-blue-brand-500;
        text-align: center;
        font-size: $spacing-system-spacing-x-lg;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
      }
    }
  }
  
  &-text {
    color: $colours-content-content-tertiary;
  }
}

.relevance-loading-template {
  .relevance-loading-top {
    height: $spacing-system-spacing-x-lg;
    @include template-box-style();
  }

  .relevance-loading-main-content {
    margin-top: $spacing-system-spacing-big;
    @include template-box-style();
  }
}
