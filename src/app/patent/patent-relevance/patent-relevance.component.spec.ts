import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentRelevanceComponent } from './patent-relevance.component';

describe('PatentRelevanceComponent', () => {
  let component: PatentRelevanceComponent;
  let fixture: ComponentFixture<PatentRelevanceComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentRelevanceComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentRelevanceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
