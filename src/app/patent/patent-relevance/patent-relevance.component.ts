import { Component, EventEmitter, Input, Output } from '@angular/core';
import { PatentViewService, SmartHighlightItem, SmartHighlights } from '@core';
import { FilterRelevanceOptionEnum } from '@patent/types';

@Component({
  selector: 'app-patent-relevance',
  templateUrl: './patent-relevance.component.html',
  styleUrls: ['./patent-relevance.component.scss']
})
export class PatentRelevanceComponent {

  @Input() smartHighlights: SmartHighlights;
  @Input() isLoadingRelevanceParts: boolean;
  @Output() relevanceFiltered = new EventEmitter<FilterRelevanceOptionEnum>();

  filterOptions: FilterRelevanceOptionEnum[] = [
    FilterRelevanceOptionEnum.ALL,
    FilterRelevanceOptionEnum.ONLY_CLAIMS,
    FilterRelevanceOptionEnum.ONLY_DESCRIPTION
  ];

  readonly filterRelevanceOptionEnum = FilterRelevanceOptionEnum;

  constructor(
    private patentViewService: PatentViewService
  ) {
  }

  get isDefaultFilter(): boolean {
    return this.currentFilter === FilterRelevanceOptionEnum.ALL;
  }

  get currentFilter(): FilterRelevanceOptionEnum {
    return this.patentViewService.currentRelevanceFilter;
  }

  get hasSmartHighlights(): boolean {
    return this.smartHighlights?.claims?.length > 0 || this.smartHighlights?.description?.length > 0;
  }

  showSmartHighlights(filterOption: FilterRelevanceOptionEnum) {
    if (this.isDefaultFilter) {
      return this.hasSmartHighlights;
    }

    const field = this.getRelevanceFilterField(filterOption);
    return this.currentFilter === filterOption && this.smartHighlights && this.smartHighlights[field]?.length > 0;
  }

  goToSmartHighlight(item: SmartHighlightItem) {
    const elementID = `highlight-box-${item.doc_field}-${item.paragraph.id}`;
    const element = document.getElementById(elementID) ||
      document.querySelector('data[data-highlight-id="' + elementID + '"]');
    if (element) {
      element.scrollIntoView({behavior: 'smooth', block: 'start'});
      const originalBg = element.style.backgroundColor;
      element.style.backgroundColor = '#EBEBEB';
      setTimeout(() => {
        element.style.backgroundColor = originalBg;
      }, 2000);
    }
  }

  onFilterOptionClicked(op: FilterRelevanceOptionEnum) {
    this.patentViewService.currentRelevanceFilter = op;
    this.relevanceFiltered.emit(op);
  }

  private getRelevanceFilterField(filterOption: FilterRelevanceOptionEnum): string {
    switch (filterOption) {
      case FilterRelevanceOptionEnum.ONLY_CLAIMS:
        return 'claims';
      case FilterRelevanceOptionEnum.ONLY_DESCRIPTION:
        return 'description';
      default:
        return null;
    }
  }
}
