import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard, SharedGuard } from '@core/guards';
import { PatentViewComponent } from './patent-view/patent-view.component';
import { FigmaElementsComponent } from './figma-elements/figma-elements.component';

const routes: Routes = [
  {path: 'view/:familyId', component: PatentViewComponent, canActivate: [AuthGuard]},
  {path: 'view/:familyId/publication/:publication_number', component: PatentViewComponent, canActivate: [AuthGuard]},
  {path: 'view/shared/:familyId', component: PatentViewComponent, canActivate: [SharedGuard]},
  {path: 'view/shared/:familyId/publication/:publication_number', component: PatentViewComponent, canActivate: [SharedGuard]},
  {path: 'figma', component: FigmaElementsComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PatentRoutingModule {
}
