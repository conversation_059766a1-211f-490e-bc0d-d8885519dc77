@import 'scss/figma2023/index';

.figma-gallery-container {
  display: flex;
  align-self: stretch;
  flex-direction: column;
  align-items: flex-start;
  gap: $spacing-system-spacing-md;
  background-color: $colours-background-bg-primary;

  .figma-gallery-title {
    display: flex;
    align-items: center;
    align-self: stretch;
  }

  .figma-gallery-box, .figma-gallery-items {
    max-width: 19rem;
    padding-right: 2px;
  }

  .figma-gallery-box {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-system-spacing-none;
    align-self: stretch;

    .figma-gallery-item {
      margin: 0 auto;
    }
  }

  .figma-gallery-items {
    overflow: auto;
    display: flex;
    align-self: stretch;

    .figma-gallery-items-wrapper {
      gap: $spacing-system-spacing-sm;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: flex-start;
      padding: $spacing-system-spacing-none;
      opacity: 1;
      display: flex;
      width: 100%;
    }

    .figma-gallery-item {
      display: flex;
      overflow: hidden;
      flex-direction: column;
      align-items: flex-start;
      border-radius: $spacing-system-spacing-xxx-s;
      border: 0.0625rem solid $colours-border-subtle;
      cursor: pointer;
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      padding: $spacing-system-spacing-sm;
      width: calc(50% - $spacing-system-spacing-sm / 2);

      &.selected {
        border: 0.0625rem solid $colour-global-dennemeyer-orange !important;
      }

      &:hover {
        box-shadow: inset 0 0 0 99999rem $colour-transparency-8blue-brand;
      }
    }
  }

  &.collapsed-gallery {
    .figma-gallery-box {
      flex-direction: row;
      align-items: flex-end;
      gap: $spacing-system-spacing-md;
      max-width: 6rem;
    }

    .figma-gallery-items {
      .figma-gallery-items-wrapper {
        display: flex;
        width: 100%;
        padding: $spacing-system-spacing-none;
        align-items: flex-start;
        align-content: flex-start;
        gap: $spacing-system-spacing-sm;
        flex-wrap: wrap;
      }

      .figma-gallery-item {
        width: 100%;
        height: 4.75rem;
      }
    }
  }

  .figma-gallery-divider-dashed {
    width: 100%;
    border-bottom: $spacing-system-spacing-xxx-s dashed $colours-border-moderate;
    height: $spacing-system-spacing-xxx-s;
  }
}

:host::ng-deep {
  .figma-gallery-box-item {
    width: 100% !important;

    app-patent-image {
      flex: 1 1 auto !important;
      display: flex !important;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      align-items: stretch;
      border: 0.0625rem solid $colours-border-moderate;
      border-radius: $spacing-system-spacing-xxx-s;

      .patent-image-container {
        border-radius: $spacing-system-spacing-xx-s;
        padding: 0 !important;
        margin: 0 !important;
        display: flex !important;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        justify-content: center;
        align-items: center;

        .image {
          flex: 1 1 auto !important;
          border-radius: $spacing-system-spacing-xx-s;
          display: flex !important;
          flex-direction: column;
          align-content: center;
          justify-content: center;
          align-items: stretch;
          width: 100%;

          &.loading-spinner {
            align-items: center !important;
            padding: unset !important;
          }

          a {
            flex: 1 1 auto !important;

            .patent-image {
              height: 100% !important;
              width: 100% !important;
              background-position: center !important;
            }
          }
        }

        .no-image {
          padding: 0 !important;
          border: none !important;
        }
      }

      .patent-image-container, .image, .patent-image {
        height: 100%;
        width: 100%;
      }

      .image-source {
        color: $colours-content-content-tertiary !important;
      }
    }

    &:hover {
      box-shadow: inset 0 0 0 99999rem $colour-transparency-8blue-brand;

      .patent-image, .image-source {
        box-shadow: inset 0 0 0 99999rem $colour-transparency-8blue-brand;
      }
    }
  }

  .collapsed-gallery {
    .patent-image-container, .image, .patent-image {

      &.loading-spinner img {
        height: 3rem !important;
        width: 3rem !important;
      }
    }
  }
}

@include mobile-screen() {
  @import "patent-gallery-mobile";
}

@include small-screen() {
  @import "patent-gallery-small";
}

@include medium-screen() {
  @import "patent-gallery-medium";
}

@include large-screen() {
  @import "patent-gallery-large";
}
