@import 'scss/figma2023/index';


.figma-side-section-container {
  display: flex;
  width: 24.0625rem;
  align-self: stretch;
  min-width: 24.0625rem;
  max-width: 24.0625rem;
  flex-direction: column;
  gap: $spacing-system-spacing-md;
  background-color: $colours-background-bg-primary;
}

.figma-screen-xxl {
  .figma-side-section-container {
    width: 27% !important;
    min-width: 27% !important;
    max-width: 27% !important;
  }
}

::ng-deep {
  .figma-side-section-container {
    .alert-box {
      color: $colour-grey-700;
      text-align: center;
    }

    .figma-side-section-content {
      overflow-y: auto;
    }
  }

  .highlight-section-head {
    .ta-user {
      width: 2rem !important;
      .ta-avatar {
        width: 2rem !important;
        height: 2rem !important;
      }
    }
  }

  .highlight {
    * {
      background-color: inherit;
    }
  }

  .tooltip-octi-ai {
    .tooltip-inner {
      padding: 0;
    }
  }
}

.highlight-section {
  padding: 0 $spacing-system-spacing-x-s;

  &-body {
    gap: $spacing-system-spacing-sm;
  }

  &-bar {
    min-width: 2px;
    max-width: 2px;
    background-color: #FB6F09;
    border-radius: $radius-xs;
  }

  &-text {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}

.menu-sorting {
  position: absolute;
  border-radius: $radius-big;
  background: $colour-grey-050;
  padding: $spacing-system-spacing-sm;
  box-shadow: 0px 8px 24px -3px #2828283D;
  min-width: 16.25rem;
  right: 0;

  li, div {
    padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
    cursor: pointer;
    display: block;
    white-space: nowrap;
    border-radius: $radius-sm;
  }
  li:hover {
    background-color: $colour-grey-200;
  }
}
