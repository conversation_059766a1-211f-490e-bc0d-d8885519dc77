@import 'scss/figma2023/index';
@import 'scss/components/highlight';

.figma {
  &-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  &-header {
    color: $colour-grey-900;
    background-color: $colours-background-bg-secondary;
    border-bottom: 1px solid $colours-border-subtle;

    &-menu {
      &-item {
        border-radius: $radius-sm;
        cursor: pointer;

        &.bttn:hover,
        &.bttn:active,
        &.bttn.active {
          background-color: $colour-grey-200;
        }

        .figma-dropdown-content.p-spacing-md {
          padding: $spacing-system-spacing-md;
          .figma-dropdown-label {
            padding: 0 $spacing-system-spacing-md;
          }
          .figma-dropdown-item {
            margin-bottom: 0;
          }
        }

      }

      &-divider {
        width: 1px;
        border-left: 1px solid $colour-grey-300;
      }

      &-group {
        .open-family-new-tab {
          visibility: hidden;
        }

        .figma-dropdown-item {
          &:hover {
            .open-family-new-tab {
              visibility: visible;
            }
          }
        }
      }

    }
  }

  &-body {
    display: flex;
    flex: 1 1;
    align-items: flex-start;
    gap: $spacing-system-spacing-sm;
    overflow: hidden;
    background-color: $colours-background-bg-tertiary;
  }

  &-patent {
    &-container {
      display: flex;
      min-width: 10rem;
      flex-direction: column;
      align-self: stretch;
      gap: $spacing-system-spacing-none;
      flex: 1 1;
      background-color: $colours-background-bg-primary;
    }

    &-body {
      position: relative;
      overflow: auto;
      &.loading-patent{
        > * {
          opacity: 0;
        }
        position: relative;

        &::before{
          position: absolute; // :before becomes absolute
          top: 0;
          left: 0;
          background-image: url('/assets/images/octimine_blue_spinner.gif');
          background-repeat: no-repeat; // don't repeat background
          background-position: center;
          display: inline-block;
          content: " ";
          background-size: 160px;
          width: 100%;
          height: 100%;
        }
      }
    }

    &-menu {
      display: flex;
      align-self: stretch;
      align-items: flex-end;
      justify-content: flex-end;
      gap: $spacing-system-spacing-none;
    }

    &-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      align-self: stretch;
      gap: $spacing-system-spacing-sm;
      flex: 1 1 auto;
      overflow: auto;
      word-wrap: break-word;
    }
  }

  &-divider-dashed {
    width: 100%;
    border-bottom: $spacing-system-spacing-xxx-s dashed $colours-border-subtle;
    height: $spacing-system-spacing-xxx-s;
  }
}
.no-back-button{
  padding-bottom: 42px; // helper class for top header menu
}

.patent-dropdown{
  .figma-dropdown-content{
    max-height: unset;
    width: 25.3rem;
    padding: $spacing-system-spacing-xxx-s;
    > div{
      max-height: 70vh;
    }
  }
  .figma-dropdown-btn{
    border: 1px solid transparent;
    &:hover{
      background-color: $colours-background-bg-primary;
      border: 1px solid $colours-content-content-primary;
    }
  }
}
.patent-content-dropdown{
  .figma-dropdown-content{
    width: max-content;
  }
}
.header-menu-dropdown{
  .figma-dropdown-content{
    min-width: 17.1rem;
  }
}
.tracking-legal-status-btn.button-small{
  padding:4px;
  height: auto !important;
  width: auto !important;
}
.figma-dropdown-btn.button-main-tertiary-grey.button-small{
  height: 2rem;
}

.btn-got-top {
  position: sticky;
  bottom: 1.25rem;
  right: 1rem;
  float: right;

  .circle {
    position: relative;
    top: 5px;
    left: 5px;
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    border-radius: 100%;
    background-color: $colour-grey-050;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .circle-border {
    width: 3rem;
    height: 3rem;
    z-index: 99;
    cursor: pointer;
    border-radius: 100%;
    transition: all ease-in-out .2s;
    border-radius: 100%;
    background: linear-gradient(270deg, $colour-grey-200 50%, transparent 50%), linear-gradient(-90deg, $colour-grey-200 50%, $colour-grey-200 50%);
    @for $i from 1 through 100 {
      &.circle-border-#{$i} {
        @if $i <= 50 {
          background: linear-gradient((($i * 3.6) + 270deg), $colour-grey-200 50%, transparent 50%), linear-gradient(-90deg, $colour-global-dennemeyer-orange 50%, $colour-grey-200 50%);
        } @else {
          background: linear-gradient((($i * 3.6) + 90deg), $colour-global-dennemeyer-orange 50%, transparent 50%), linear-gradient(-90deg, $colour-global-dennemeyer-orange 50%, $colour-grey-200 50%);
        }
      }
    }
  }
}

.figma-innerHTML ::ng-deep{
    h1, h2, h3{
      justify-content: flex-start !important;
      font-family: $font-family-base;
      font-style: normal;
      font-weight: 700;
    }
    h1{
      margin-bottom: 0;
      margin-top: $spacing-system-spacing-x-big;
      font-size: $font-size-xx-md;
      line-height: $font-size-x-big;
    }
    h2{
      margin-bottom: $spacing-system-spacing-sm;
      margin-top: $spacing-system-spacing-big;
      font-size: $font-size-md;
      line-height: $font-size-big;
      .button-small{
        padding: 0.125rem;
        height: 1.5rem;
        width: 1.5rem;
        margin-left: 0.25rem !important;
      }
    }
    h3{
      margin-bottom: $spacing-system-spacing-x-s;
      margin-top: $spacing-system-spacing-x-s;
      font-size: $font-size-sm;
      line-height: $font-size-xx-md;
    }
    .heading-title {
      margin-left: 2px;
      font-weight: 600;
    }

    section > .figma-section-content > .desc-paragraph, article > .desc-paragraph:first-child {
      margin-top: $spacing-system-spacing-big;
    }
}

.patent-menu-bar{
  width: 100%;
}

.priority-menu{
  &-dropdown{
    .figma-header-menu-item{
      display: block !important;
    }
    > span {
      display: block !important;
    }
  }
}

.search-box {
  position: absolute;
  top: 8px;
  background-color: $colours-background-bg-primary;
  box-shadow: 0px 8px 24px -3px rgba(40, 40, 40, 0.24);
  z-index: 1000;
  .input-frame {
    border-radius: $radius-sm;
    background:  $colours-background-bg-secondary;
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-md;
    gap: $spacing-system-spacing-md;
    display: flex;
    align-self: stretch;
    align-items: center;
    min-width: 17rem;

    .form-control {
      border: none;
      padding: 0;
      font-size: 0.875rem;
      line-height: 1.25rem;
      background:  $colours-background-bg-secondary;

      &::placeholder {
        color: $colours-content-content-disabled;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.25rem !important;
      }
    }

    .counters {
      color: $colours-content-content-tertiary;
      font-size: 0.875rem;
      font-weight: 400;
      line-height: 1.25rem;
    }
  }

  .buttons-search {
    padding-left: $spacing-system-spacing-sm;
    gap: $spacing-system-spacing-sm;
    border-left: 1px solid $colours-border-subtle;

    .buttons {
      display: flex;
      padding: $spacing-system-spacing-x-s;
      justify-content: center;
      align-items: center;
      gap: $spacing-system-spacing-sm;

      color: $colours-content-content-primary;
      line-height: 16px;
      border-radius: $spacing-system-spacing-xx-s;
      cursor: pointer;

      span {
        width: 20px;
        height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: $spacing-system-spacing-sm;
      }

      &:hover, &.active {
        background: $colours-buttons-main-tertiary-grey-bg-hover;
      }
    }
  }
}
#sec-legal-events{
  overflow-x: auto;
}

#sec-bibliographic-info {
  ::selection {
    background: var(--selection-bg, $colour-blue-brand-200) !important;
  }

  ::-moz-selection {
    background: var(--selection-bg, $colour-blue-brand-200) !important;
  }

}

:host::ng-deep {
  mark {
    padding: 0;
    background-color: yellow;

    &.current {
      background-color: orange;
    }
  }
  .legal-events-title .le-title{
    @include add-properties(map-get(map-get($typography, 'heading'), 'h3'), true);
  }
  @include small-screen() {
    .legal-events-title{
      flex-direction: column;
      align-items: flex-start;
    }

    .figma-side-section-container {
      position: absolute;
      right: 3px;
      box-shadow: -8px 12px 20px 8px #2828281F;
      border-radius: $radius-lg 0 0 $radius-lg !important;
    }
  }

  .section-content {
    &.pa-text-highlight {
      background: $colour-blue-brand-100;
    }
  }
  .highlight{
    scroll-margin: 85px;
  }
  .pa-comment-highlight {
    border-bottom: solid 1.8px;
    background-color:  $colours-background-bg-secondary;
    scroll-margin: 85px;
    cursor: pointer;
    &:hover{
      background-color: $colours-background-bg-tertiary;
      border-radius: 2px;
    }
  }
  .comment-hovered{
    background-color: $colours-background-bg-tertiary;
    border-radius: 2px;
  }
  .comment-focused{
    box-shadow: 0px 2px 4px 2px #28282833;
  }
  .desc-paragraph {
    margin-bottom: $spacing-system-spacing-sm;
  }
}

::ng-deep {
  .button-small-tooltip .tooltip-inner {
    display: none !important;
  }
  .button-small-tooltip .arrow::before {
    display: none !important;
  }

  .figma-container-sm {
    cursor: var(--cursor, auto);

    ::selection {
      background: var(--selection-bg, $colour-blue-brand-200) !important;
    }

    ::-moz-selection {
      background: var(--selection-bg, $colour-blue-brand-200) !important;
    }

    .pa-text-highlight {
      background: var(--selection-bg, $colour-blue-brand-200) !important;
    }
  }
}

@include small-screen() {
  .priority-menu {
    .menu-text {
      display: none;
    }
    .button-small{
      padding: $spacing-system-spacing-x-s;
      height: 2rem;
      width: 2rem;
    }
  }

  ::ng-deep {
    .button-small-tooltip .tooltip-inner {
      display: block !important;
    }
    .button-small-tooltip .arrow::before {
      display: block !important;
    }
  }
}

.bibliographic-info-column{
  display: flex;
  align-items: flex-start;
  align-self: stretch;
  &-left{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 12.25rem;
    margin-right: $spacing-system-spacing-xx-big;
  }
  &-right{
    display: flex;
    flex-direction: column;
    max-width: calc(100% - (12.25rem + $spacing-system-spacing-xx-big));
  }
  .field{
    &-info{
      margin: $spacing-system-spacing-sm;
    }
    &-title{
      @include add-properties(map-deep-get($typography, heading, h7));
      margin-bottom: $spacing-system-spacing-xx-s;
      color: $colours-content-content-tertiary;
    }
  }
}

.tooltip-icon {
  color: #707070 !important; // $colours-content-content-tertiary !important;
  &:hover {
    color: $colours-content-content-primary !important;
  }
}

::ng-deep {
  .noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  .authority-popover {
      max-width: 390px;
  }

  .displayed-paragraph-number {
    display: inline;
  }
}

@media (max-width: 1024px) {
  .title-box {
    &.content-heading-h4 {
      font-size: 1rem;
    }
  }
}

/* ------------- start comment section css -------------------- */
.comments-highlighted{
  position: relative;
  &-container{
    position: absolute;
    top: 0;
    bottom: 0;
    right: -50px;
  }
  &-note{
    position: absolute;
    color: $colours-content-content-tertiary;
    display: flex;
    gap: 5px;
    cursor: pointer;
    &::before{
      font-family: "Font Awesome 6 Pro";
      font-weight: 300;
      box-sizing: border-box;
      content: "\f27a";
    }
  }
}

::ng-deep {
  .comment-tooltip {
    .popover-body {
      width: fit-content;
      max-width: fit-content;

      .popover-descriptions {
        width: fit-content;
        max-width: fit-content;
      }
    }
  }
}
:host::ng-deep {
  .dynamic-comment-dialog mention-list{
    position: unset !important;
  }
}

.position-absolute.start-50.translate-middle-x{
  z-index: 1;
}
/* ------------- end comment section css -------------------- */
/* ------------- start octi ai css -------------------------- */
.octi-icon{
  width: 16px;
  height: 17px;
}
:host::ng-deep {
  .octi-ai-popper {
    padding: 0 !important;
    z-index: 10;
  }
}
/* ------------- end octi ai css ---------------------------- */
