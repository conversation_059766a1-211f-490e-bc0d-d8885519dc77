import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { PatentViewComponent } from './patent-view.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentViewComponent', () => {
  let component: PatentViewComponent;
  let fixture: ComponentFixture<PatentViewComponent>;

  beforeEach(async () => {
    TestBed.configureTestingModule({
      declarations: [ PatentViewComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
