import {
  AfterContentChecked,
  AfterViewInit,
  ChangeDetector<PERSON>ef,
  Component,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  BaseStoreService,
  BooleanSearchStoreService,
  CitationSearchStoreService,
  CollectionStoreService,
  LandscapeStoreService,
  MonitorStoreService,
  SearchDataModel,
  SemanticSearchStoreService
} from '@core/store';
import {
  CollaborationResourceTypeEnum,
  Patent,
  ReadDocument,
  TaskAssignmentModel,
  TaskAssignmentStatusEnum,
  TaskModel
} from '@core/models';
import {
  AnalysisService,
  BooleanSearchService,
  CitationSearchService,
  CollaborationService,
  ConfirmationDialogService,
  DocumentService,
  EspacenetService,
  MonitorService,
  PatentNumberService,
  PatentService,
  PatentTableService,
  PatentViewModeService,
  PublicationService,
  ReadDocumentsService,
  SemanticSearchService,
  TaskService,
  UserService
} from '@core/services';
import {
  AnnotationService,
  AnnotationStoreService,
  CitationSearchRequest,
  CollectionService,
  DateFormatPipe,
  DocumentAnnotation,
  DocumentCommentSourceEnum,
  GroupService, HtmlElementUtil,
  LandscapeService,
  MatomoService,
  NotificationsService,
  OctiAiService,
  OctiQuestion,
  PaginationMetadata,
  PatentListScopeEnum,
  PatentViewService,
  SmartHighlightRequest,
  SmartHighlights,
  TeamUser,
  TextHighlightService,
  ToastService,
  ToastTypeEnum,
  UserGroup,
  UserTitlePipe
} from '@core';
import { PatentSideBarViewModeEnum, PatentViewReferralType } from '@core/services/patent/types';
import {
  BehaviorSubject,
  catchError,
  concatMap,
  debounceTime,
  filter,
  finalize,
  map,
  mergeMap,
  Observable,
  of,
  Subscription,
  switchMap,
  take,
  tap
} from 'rxjs';
import { Location } from '@angular/common';
import {
  AddToCollectionComponent,
  PatentImageComponent,
  PdfNotFoundDialogComponent,
  PdfOtherErrorsDialogComponent,
  ShareDialogComponent,
  ShareDialogShareTypeEnum
} from '@shared/components';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { animate, AUTO_STYLE, state, style, transition, trigger } from '@angular/animations';
import * as Mark from 'mark.js';
import { MediaObserver } from '@angular/flex-layout';
import {
  PatentLegalStatusTrackingComponent
} from '@patent/patent-legal-status-tracking/patent-legal-status-tracking.component';
import {
  ColorsPopperSourceEnum,
  FilterTableOptionEnum,
  PatentCommentsActionSourceEnum,
  PatentHighlightActionEnum,
  PatentLegalStatusEnum,
  SideSectionEnum,
  SortHighlightsOptionEnum
} from '@patent/types';
import { PatentViewHighlightService } from '@core/services/patent-view-highlight/patent-view-highlight.service';
import { PatentHighlightsComponent } from '@patent/patent-highlights/patent-highlights.component';
import { TagModel } from '@core/models/tag.model';
import { PopperComponent } from '@shared/components/popper/popper.component';
import { RatingNavigationParam } from '@patent/patent-ratings/shared/types';

enum fields { IPC = 'ipc', CPC = 'cpc', INVENTORS = 'inventors', APPLICANTS = 'applicants', ASSIGNEES = 'assignees',
  INVENTORS_ORIGINAL = 'inventors_original', APPLICANTS_ORIGINAL = 'applicants_original', ASSIGNEES_ORIGINAL = 'assignees_original'};

@Component({
  selector: 'app-patent-view',
  templateUrl: './patent-view.component.html',
  styleUrls: [
    './patent-view.component.scss',
    './patent-gallery.scss',
    './patent-side-section.scss',
  ],
  animations: [
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ]),
    trigger('collapse', [
      state('false', style({ height: AUTO_STYLE, visibility: AUTO_STYLE })),
      state('true', style({ height: '0', visibility: 'hidden', overflow: 'hidden' })),
      transition('false => true', animate('300ms ease-in')),
      transition('true => false', animate('300ms ease-out'))
    ])
  ]
})
export class PatentViewComponent implements OnInit, AfterViewInit, AfterContentChecked, OnDestroy  {
  storeService: BaseStoreService;
  searchService: SemanticSearchService | CitationSearchService | BooleanSearchService;
  private loadHighlightSubject = new BehaviorSubject<Patent>(null);
  private calculateSmartHighlightsSubject = new BehaviorSubject<Patent>(null);
  private markAsReadSubject = new BehaviorSubject<number>(null);
  private subscriptions = new Subscription();
  private publicationsDocumentInfo;
  private currentQueryRatingId: number = null;
  private currentQueryAssignmentId: number = null;

  backButtonTitle: string = 'Back to current list';
  hasLinksToBooleanSearch = true;
  showAbstract: boolean = true;
  showClaims: boolean = true;
  showDescription : boolean = true;
  showIpcFamily: boolean;
  showCpcFamily : boolean;
  smartHighlights: Record<string, SmartHighlights> = {};
  isDownloadingPdfs = {};
  patent: Patent;
  selectedPatent: Patent;
  isLoadingPatent: boolean;
  isGalleryCollapsed: boolean = false;
  showComments: boolean;
  showHighlightsSession: boolean;
  showGallery: boolean;
  lessBibliographicInfo: boolean = true;
  showClaimsTree = true;
  searchingCitation = true;
  isLoadingSmartHighlights = false;
  paginationCitation: PaginationMetadata = {current_page: 1} as PaginationMetadata;
  collaboration: { users: TeamUser[], groups: UserGroup[] };
  sideSection: SideSectionEnum;
  selectedImage: any;
  selectedImageIndex: number = 0;
  isLoadingImage: boolean = false;
  legalStatus;
  documentInfo;
  activeDocumentInfo;
  scrollPercent: number = 0;
/* search box */
  searchTerm = '';
  currentIndex = -1;
  matches = [];
  isSearchBoxOpen = false;
  isWholeWord = false;

  sideSectionEnum = SideSectionEnum;
  patentCommentsActionSource: PatentCommentsActionSourceEnum = null
  colorsPopperSourceEnum = ColorsPopperSourceEnum;

    screenSizeAlias: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | string = 'md';

  @ViewChild('sectionCommentEditor') private sectionCommentEditor: PopperComponent;
  @ViewChild('patentHighlights') patentHighlights: PatentHighlightsComponent;
  @ViewChild('galleryBox') galleryBox: PatentImageComponent;
  @ViewChild('octiAIPopper') private octiAIPopper: PopperComponent;

  filterCitationOptions: FilterTableOptionEnum[] = [FilterTableOptionEnum.CURRENT_DOCUMENT, FilterTableOptionEnum.ALL_FAMILY];
  selectedCitationFilter = FilterTableOptionEnum.CURRENT_DOCUMENT;

  blockSelection = false;
  isOverTextArea = false;
  isLoadingDocumentHighlights = true;

  focusedComment;
  teamUsers: TeamUser[];
  teamGroups: UserGroup[];
  addComment: boolean;

  showOctiAIPopper: boolean = false;
  octiChatID: string;

  ratings: TaskModel[] = [];
  countDoneRatings = 0;
  isRatingsCountedFirstTime: boolean = false;

  readDocuments: ReadDocument[] = [];
  readDocumentUsers: TeamUser[] = [];

  readonly ORIGINAL_TOOLTIP_TEXT = 'Octimine provides automatic name standardization that detects and corrects name variations. View the original names below.';
  readonly patentLegalStatusEnum = PatentLegalStatusEnum;

  constructor(
    public patentViewService: PatentViewService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private location: Location,
    private semanticSearchService: SemanticSearchService,
    private booleanSearchService: BooleanSearchService,
    private citationSearchService: CitationSearchService,
    private semanticSearchStoreService: SemanticSearchStoreService,
    private booleanSearchStoreService: BooleanSearchStoreService,
    private citationSearchStoreService: CitationSearchStoreService,
    private monitorService: MonitorService,
    private collectionService: CollectionService,
    private landscapeService: LandscapeService,
    private collectionStoreService: CollectionStoreService,
    private monitorStoreService: MonitorStoreService,
    private landscapeStoreService: LandscapeStoreService,
    private analysisService: AnalysisService,
    private documentService: DocumentService,
    private ngbModal: NgbModal,
    private matomoService: MatomoService,
    private espacenetService: EspacenetService,
    private confirmationDialogService: ConfirmationDialogService,
    private mediaObserver: MediaObserver,
    private textHighlightService: TextHighlightService,
    public annotationService: AnnotationService,
    public annotationStore: AnnotationStoreService,
    private patentViewHighlightService: PatentViewHighlightService,
    private userTitlePipe: UserTitlePipe,
    private toastService: ToastService,
    private groupService: GroupService,
    private taskService: TaskService,
    private dateFormatPipe: DateFormatPipe,
    private notificationsService: NotificationsService,
    private patentNumberService: PatentNumberService,
    private publicationService: PublicationService,
    private patentService: PatentService,
    public patentTableService: PatentTableService,
    public userService: UserService,
    private collaborationService: CollaborationService,
    private changeDetectorRef: ChangeDetectorRef,
    private octiAiService: OctiAiService,
    private patentViewModeService: PatentViewModeService,
    private readDocumentsService: ReadDocumentsService
  ) {
    document.body.style.overflow = "hidden";
    this.detectScreenSize();
    this.hasLinksToBooleanSearch = this.userService.isNotExternalUser();
  }

  get searchHash(): string {
    return this.activatedRoute.snapshot.queryParams.search_hash;
  }

  get highlightSearchHash(): string {
    return this.activatedRoute.snapshot.queryParams.search_hash;
  }

  get showHighlight(): boolean {
    return this.activatedRoute.snapshot.queryParams.show_highlight === '1';
  }

  get smartHighlightSearchHash(): string {
    return this.activatedRoute.snapshot.queryParams.smart_highlight_search_hash;
  }

  get showSmartHighlight(): boolean {
    return this.activatedRoute.snapshot.queryParams.show_smart_highlight === '1';
  }

  get monitorRunId(): number {
    return this.activatedRoute.snapshot.queryParams.monitor_run_id;
  }

  get filtersQuery(): number {
    return this.activatedRoute.snapshot.queryParams.filters_query;
  }

  get hasGallery(){
    return 'images' in this.selectedPatent;
  }

  get familyID(): number{
    return this.patentViewService.activeDocumentId;
  }

  get isFamilyView(): boolean{
    return this.patentViewService.activeDocumentId && !this.patentViewService.activePublicationName;
  }

  get isPublicationView(): boolean{
    return !!this.patentViewService.activeDocumentId && !!this.patentViewService.activePublicationName;
  }

  get activePublicationNumber(): string{
    return this.patentViewService.activePublicationName ? this.patentViewService.activePublicationName: this.selectedPatent?.general?.raw_publication_number;
  }

  get mouseDownElementId(): string {
    return this.patentViewHighlightService.mouseDownElementId;
  }

  get highlightList(): DocumentAnnotation[] {
    return this.patentViewHighlightService.highlightList;
  }

  set highlightList(value: DocumentAnnotation[]) {
    this.patentViewHighlightService.highlightList = value;
  }

  get currentSelectedContainer(): { range: any; content: any; highlightNode: any; } {
    return this.patentViewHighlightService.currentSelectedContainer;
  }

  get mouseUpElementId(): string {
    return this.patentHighlights?.mouseUpElementId;
  }

  get commentCount(): number {
    return this.annotationStore.generalComments.length + this.annotationStore.sectionComments.length;
  }

  set isCountingRatings(val: boolean) {
    this.patentViewService.isCountingRatings = val;
  }

  get publication(){
    return this.patentViewService.publication
  }

  get octiAIHistory(){
    return this.octiAiService.octiAIHistory[this.octiChatID];
  }

  get canBack(): boolean {
    if (this.storeService?.isSearchStore()) {
      return this.storeService.patentViewerDocuments?.length > 0 || this.patentViewService.isFocalPatent;
    }
    return this.hasResultList || this.patentViewService.previousRoute?.length>0;
  }

  get backDataQueryParam() {
    const data = this.activatedRoute.snapshot.queryParams['back_data'];

    if (!data) {
      return {};
    }

    try {
      return JSON.parse(data);
    } catch (e) {
      console.warn(e);
      return {};
    }
  }

  @HostListener('window:resize', ['$event'])
  onResizeBrowser(event) {
    this.annotationService.displayCommentsNote();
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.ctrlKey && event.key === 'f') {
      event.preventDefault();
      this.openSearchBox();
    }
  }

  @HostListener('document:mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    const isLeftMouse = event.button === 0;
    if (isLeftMouse && !this.patentHighlights.popperClicked(event)) {
      this.patentHighlights.onMouseDown(event);
      if (this.addComment) {
        this.addComment = false;
      }
    }
  }

  @HostListener('document:click', ['$event'])
  onClick(event: MouseEvent) {
    const head = this.headClicked(event);
    if (!head || (event.target as HTMLElement).classList.contains('heading-actions')) {
      return;
    }
    const collapseEle = head.querySelector('.section-collapse');
    ['fa-caret-down', 'fa-caret-right'].forEach((c) => collapseEle.classList.toggle(c));

    const headingTags = ['section', 'h1', 'h2', 'h3'];

    let sectionContentEle = head.nextElementSibling;
    if (!sectionContentEle || headingTags.includes(sectionContentEle.tagName.toLowerCase())) {
      sectionContentEle = head.closest('section')?.nextElementSibling;
    }

    while (sectionContentEle && !headingTags.includes(sectionContentEle.tagName.toLowerCase())) {
      sectionContentEle.classList.toggle('d-none');
      sectionContentEle = sectionContentEle.nextElementSibling;
    }
  }

  private headClicked(event: MouseEvent) {
    const tags = ['H1', 'H2', 'H3']
    for (const tag of event.composedPath()) {
      const element = tag as HTMLElement;
      if (tags.includes(element.tagName)) {
        return element;
      }
    }
    return null;
  }

  ngOnInit(): void {
    this.initializePage();
    setTimeout(() => {
      if (this.backDataQueryParam.storeCurrentStateId) {
        this.storeService.backPatentSearch = true;
        this.storeService.restoreCurrentState(this.backDataQueryParam.storeCurrentStateId);
        this.removeFieldsFromQueryParams();
      }
      this.patentHighlights.getHighlights();
    });
  }

  ngAfterViewInit() {
    if (this.hasResultList) {
      document.documentElement.scrollTop = 0;
    }
    if (this.showComments) {
      this.toggleSideSection(this.sideSectionEnum.COMMENTS);
    }
    if (this.showHighlightsSession) {
      this.toggleSideSection(this.sideSectionEnum.HIGHLIGHTS);
    }
  }

  ngAfterContentChecked() {
    this.changeDetectorRef.detectChanges();
  }

  initializePage(){
    const referral = this.backDataQueryParam.referral || window.history.state?.data?.referral || this.activatedRoute.snapshot.data?.referral;
    this.setService(referral as PatentViewReferralType);

    const loadPatent$ = this.patentViewService.$documentId.pipe(filter((val) => !!val)).subscribe({next: docId => this.getFamily()});
    this.subscriptions.add(loadPatent$);

    const activatedRoute$ = this.activatedRoute.data.subscribe({
      next: (data) => {
        if (window.history.state && window.history.state.data) {
          this.activatedRoute.snapshot.data = window.history.state.data;
        }

        const referral = window.history.state?.data?.referral || this.activatedRoute.snapshot.data?.referral;
        this.setService(referral as PatentViewReferralType);
      }
    });
    this.subscriptions.add(activatedRoute$);

    const loadHighlight$ = this.loadHighlightSubject.asObservable()
      .pipe(
        debounceTime(500),
        mergeMap((val) => this.loadHighlight(val)),
      )
      .subscribe();
    this.subscriptions.add(loadHighlight$);

    const calculateSmartHighlights$ = this.calculateSmartHighlightsSubject.asObservable()
      .pipe(
        filter((val) => !!val),
        debounceTime(500),
        mergeMap((val) => this.calculateSmartHighlights(val))
      )
      .subscribe();
    this.subscriptions.add(calculateSmartHighlights$);

    const markAsRead$ = this.markAsReadSubject.asObservable().subscribe({
      next: (id) => {
        if (id) {
          this.notificationsService.markAsReadForResource(id, 'PATENT').subscribe();
        }
      }
    });
    this.subscriptions.add(markAsRead$);

    const params$ = this.activatedRoute.params.subscribe({
      next: params => {
        if (params.publication_number) {
          this.patentViewService.setPublicationName(params.publication_number);
        }
        this.patentViewService.setActiveDocumentId(Number(params.familyId));
        this.markAsReadSubject.next(Number(params.familyId));
      }
    });
    this.subscriptions.add(params$);

    const queryParams$ = this.activatedRoute.queryParams.subscribe({
      next: params => {
        if (params.focalPatent) {
          this.patentViewService.isFocalPatent = params.focalPatent;
        }
        if (params.previousUrl) {
          this.patentViewService.previousRoute = params.previousUrl;
        }
        if (params.search_hash) {
          this.initializeCurrentList(params);
        }

        if (params.backButtonTitle) {
          this.backButtonTitle = params.backButtonTitle;
        }

        if (params.anchorId) {
          this.scrollTo(params.anchorId, 100);
        }

        if (params.comment_id) {
          this.annotationService.focusCommentId = params.comment_id as number;

          setTimeout(() => {
            this.loadHighlightSubject.next(this.selectedPatent);
            if (this.sideSection != SideSectionEnum.COMMENTS) {
              this.toggleSideSection(SideSectionEnum.COMMENTS);
            }
          }, 500);
        }

        if (params.mode) {
          this.updateSideBarViewModeUI(params.mode as PatentSideBarViewModeEnum);
        }
        if(params.gallery_index){
          this.selectedImageIndex = Number(params.gallery_index);
          this.showGallery = true;
        }

        if (!params['anchorId']) {
          window.scrollTo({top: 0, behavior: 'smooth'});
        }

        this.currentQueryRatingId = params.rating_id ? Number(params.rating_id) : null;
        this.currentQueryAssignmentId = params.assignment_id ? Number(params.assignment_id) : null;
      }
    });
    this.subscriptions.add(queryParams$);

    this.loadTeamUsers();
    this.loadTeamGroups();

    const showCommentView$ = this.patentViewService.showCommentView$
                              .pipe(filter((val) => !!val))
                              .subscribe({next: val => this.onCommentNoteClick(val)});
    this.subscriptions.add(showCommentView$);

    const removeAnnotation$ = this.annotationStore.removeAnnotation$.subscribe({
      next: (annotationType) => {
        if ((annotationType == 'comments' && this.annotationStore.listComments.length === 0) ||
            (annotationType == 'labels' && this.highlightList.length === 0)) {
          this.updateListAnnotations(annotationType, false)
        }
      }
    });
    this.subscriptions.add(removeAnnotation$);

    const listComments$ = this.annotationStore.listComments$
      .pipe(
        debounceTime(50)
      )
      .subscribe({
        next: (val) => {
          this.annotationService.sortAndFilterComments();
        }
      });
    this.subscriptions.add(listComments$);

    const refreshCountRatings$ = this.patentViewService.refreshCountRatings$
      .pipe(
        filter((val) => val),
        filter((val) => !!this.selectedPatent?.general?.docdb_family_id),
        filter((val) => this.hasWorkflowFeature()),
        switchMap(() => this.countRatings())
      )
      .subscribe();
    this.subscriptions.add(refreshCountRatings$);

    const logActivity$ = this.userService.logActivity('VIEW_PATENT').subscribe();
    this.subscriptions.add(logActivity$);
  }

  private startLoadingHighlights() {
    if (this.showSmartHighlight) {
      this.calculateSmartHighlightsSubject.next(this.patent);
    } else {
      this.loadHighlightSubject.next(this.selectedPatent);
    }

  }

  private setService(referral: PatentViewReferralType): void {
    if (this.storeService) {
      return;
    }

    switch (referral) {
      case PatentViewReferralType.REFERRAL_CITATION:
        this.searchService = this.citationSearchService;
        this.storeService = this.citationSearchStoreService;
        break;
      case PatentViewReferralType.REFERRAL_BOOLEAN:
        this.searchService = this.booleanSearchService;
        this.storeService = this.booleanSearchStoreService;
        break;
      case PatentViewReferralType.REFERRAL_COLLECTIONS: // Comparison in collections view temporary disabled
        this.searchService = this.semanticSearchService;
        this.storeService = this.collectionStoreService;
        break;
      case PatentViewReferralType.REFERRAL_MONITOR:
        this.searchService = this.semanticSearchService;
        this.storeService = this.monitorStoreService;
        break;
      case PatentViewReferralType.REFERRAL_LANDSCAPE:
        this.searchService = this.semanticSearchService;
        this.storeService = this.landscapeStoreService;
        break;
      default:
        this.searchService = this.semanticSearchService;
        this.storeService = this.semanticSearchStoreService;
        break;
    }
  }

  get hasResultList(): boolean {
    return this.patentViewService.hasResultList;
  }

  get isClaimsFormatted(): boolean {
    if (!this.selectedPatent?.fulltext?.claims) {
      return false;
    }
    return !!document.querySelectorAll('.claims').length;
  }

  get patentTitle(): string {
    return this.patentTableService.getPatentTitle(this.selectedPatent, this.showHighlight);
  }

  get patentAbstract(): string {
    return this.patentTableService.getPatentAbstract(this.selectedPatent, this.showHighlight);
  }

  get patentClaims(): string {
    return this.patentTableService.getPatentClaims(this.selectedPatent, this.showHighlight);
  }

  get patentDescription(): string {
    return this.patentTableService.getPatentDescription(this.selectedPatent, this.showHighlight);
  }

  get sectionComments(){
    return this.annotationStore.sectionComments.filter(c => c.publication_number === (this.patentViewService.activePublicationName || null));
  }

  get isCitationsFilteredByCurrentDocument(): boolean {
    return this.selectedCitationFilter === FilterTableOptionEnum.CURRENT_DOCUMENT;
  }

  public getPriorityClaims(): string {
    const priorityClaims = this.publication?.bibliographic?.priority_claims.map(claim => {
      return `${claim.priority_number.replace(/-/g, '')} (${this.dateFormatPipe.transform(claim.priority_date, 'ShortDate')})`;
    });
    return priorityClaims.join(', ');
  }

  getSmartHighlights(patent: Patent): SmartHighlights {
    return this.smartHighlights[patent?.general?.docdb_family_id];
  }

  hasSmartHighlights(patent: Patent): boolean {
    const smartHighlights = this.getSmartHighlights(patent);
    if (smartHighlights && (smartHighlights.claims?.length || smartHighlights.description?.length) ) {
      return true;
    }
    return false;
  }

  scrollTo(elementID: string, timeout: number = null) {
    const el = document.getElementById(elementID);
    if (el) {
      if (timeout) {
        const self = this;
        setTimeout(() => self.scrollToElement(el), timeout);
      } else {
        this.scrollToElement(el);
      }
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.storeService.patentViewerDocuments = [];
    this.storeService.selectedPublications = [];
    this.storeService.selectedPatentIds = [];
    this.storeService.patentViewerPatent = null;
    this.patentViewService.reset()
    this.monitorStoreService.resetPatentLegalStatusTracking();
    this.annotationService.reset();
    document.body.style.overflow = "unset";
    this.removeCustomToast();
    this.toastService.clear();
  }

  getFlagIcon(publicationNumber: string) {
    return this.patentService.getFlagCssByPublication(publicationNumber).toLowerCase();
  }

  getPatentLegalStatus(publication: string): PatentLegalStatusEnum {
    const p = this.documentInfo?.find((d) => d.publication_number?.replace(/-|-/gi, '') === publication);
    return this.patentService.getPatentLegalStatus(p);
  }

  getFamilyMemberLegalStatusIcon(publication: string, isFamily = false) {
    if (isFamily) {
      return this.patentService.getFamilyLegalStatusIcon(this.selectedPatent?.bibliographic);
    }
    const p = this.documentInfo?.find((d) => d.publication_number?.replace(/-|-/gi, '') === publication);
    return this.patentService.getPatentLegalStatusIcon(p);
  }

  isLegalStatusTracked(publication: string): boolean {
    return Object.keys(this.monitorStoreService.alreadyTrackedPublications).length > 0 && publication in this.monitorStoreService.alreadyTrackedPublications;
  }
  isTrackingAvailable(publication: string): boolean {
    return this.monitorStoreService.trackablePublications.includes(publication) || publication in this.monitorStoreService.alreadyTrackedPublications;
  }

  getFamilyMemberTitle(publication: string) {
    return this.documentInfo?.find((d) => d.publication_number?.replace(/-|-/gi, '') === publication)?.title;
  }

  getFamilyMember(publication: string)  {
    return this.publicationsDocumentInfo?.find(obj => obj.publication_number?.replace(/-|-/gi, '') === publication);
  }

  loadPublication(event: MouseEvent, publicationNumber: string, newTab: boolean) {
    if (newTab) {
      event.preventDefault();
      event.stopPropagation();

      const pagination = this.patentViewService.pagination ?? this.storeService.pagination;
      this.storeService.storeSearchData(this.patentViewService.documents, pagination);
      const queryParams = this.getCurrentQueryParam(this.patentViewService.currentListIndex);
      const baseUrl = this.patentViewService.getPatentViewerBaseUrl(this.patentViewService.activeDocumentId, publicationNumber);
      const newUrl = `${baseUrl}?${queryParams.join('&')}`;

      window.open(newUrl, '_blank');
    } else {
      this.patentViewService.setPublicationName(publicationNumber);
      this.updateCurrentPatentUrl(this.patentViewService.activeDocumentId, publicationNumber, this.patentViewService.currentListIndex);
      this.annotationStore.resetAnnotation();
      this.getPublication();
    }
  }

  private getPublication(){
    this.isLoadingPatent = true;
    const pub = this.patentViewService.activePublicationName ? this.patentViewService.activePublicationName: this.patent.general.raw_publication_number;
    this.removeCustomToast();
    const obs$ = this.patentNumberService.getPublication(pub).subscribe({
      next: publication => {
        this.patentViewService.publication = publication;
        if(this.patentViewService.activePublicationName){
          this.setSelectedPatent(this.publicationService.publicationToDocument(publication));
        } else {
          this.setSelectedPatent(this.patent);
        }
      },
      error: error => {console.error(error);this.isLoadingPatent = false;}
    })
    this.subscriptions.add(obs$);
  }

  private getFamily(){
    this.isLoadingPatent = true;
    const obs$ = this.patentNumberService.getDocument(this.patentViewService.activeDocumentId).subscribe({
      next: doc => {
        this.patent = doc;
        this.patentViewService.patent = doc;
        this.getPublication();
        this.loadFamilyLegalStatus();
      },
      error: error => {console.error(error);this.isLoadingPatent = false;}
    })
    this.subscriptions.add(obs$);
  }

  private setSelectedPatent(p: Patent) {
    this.octiChatID = `${this.familyID}${!this.isFamilyView? '_'+this.patentViewService.activePublicationName: ''}`;
    this.selectedPatent = p;
    this.storeService.patentViewerPatent = p;
    this.legalStatus = this.patent.legal_statuses;
    this.documentInfo = this.patent.document_info;
    this.activeDocumentInfo = this.documentInfo?.find((d) => d.publication_number === this.activePublicationNumber)
    this.isLoadingPatent = false;
    this.loadListImages();
    this.loadCitationReferences();
    this.loadCollaborations(p.general.docdb_family_id);
    this.updateClaims();
    this.loadClassifications();
    if(this.userService.hasMonitor){
      this.monitorService.loadLegalStatusTracking(this.patent.bibliographic.also_published_as);
    }
    this.storeService.selectedPublications = [ this.activePublicationNumber];
    this.storeService.selectedPatentIds = [Number(p.general.docdb_family_id)];
    this.patentViewService.refreshCountRatings = true;
    this.selectedPatent['custom_tags'] = this.patent.custom_tags ?? [];
    this.startLoadingHighlights();
    this.loadTeamReadDocuments();
    this.showCustomToast();
  }

  private loadListImages() {
    if (!this.selectedPatent || this.selectedPatent['images'] || !this.activePublicationNumber) {
      return;
    }
    const obs$ = this.patentService.getListImage(this.buildAttachmentPayload())
      .pipe(
        catchError((error) => {
          console.error(error);
          return of({attachments: []});
        })
      )
      .subscribe({
        next: (list) => {
          const attachments = [];
          let images = list.attachments;
          if (this.patentViewService.activePublicationName) {
            images = images.filter(l => l.patent_number === this.patentViewService.activePublicationName)
          }

          for (const attachment of images) {
            for (const item of attachment.attachments) {
              if (item.image && item.image.bytes) {
                const image64 = 'data:image/png;base64,' + window.btoa(window.atob(item.image.bytes));
                attachments.push({
                  path: item.path,
                  downloadUrl: image64,
                  src: image64,
                  patentNumber: attachment.patent_number,
                  thumb: image64
                });
              }
            }
          }
          if (attachments.length) {
            this.selectedImage = attachments[this.selectedImageIndex];
            this.selectedPatent['images'] = attachments;
            window.dispatchEvent(new Event('resize')); // for priority+ menu
          }
          setTimeout(() => {
            if(this.showGallery){
              this.showGallery = false;
              this.removeFieldsFromQueryParams();
              this.galleryBox.showGallery();
            }
          }, 500);
        }
      });

    this.subscriptions.add(obs$);
  }
  toggleGalery(){
    this.isGalleryCollapsed = !this.isGalleryCollapsed;
    window.dispatchEvent(new Event('resize'));
  }

  loadHighQualityImage(img) {
    if (!img) {
      return;
    }

    this.selectedImage = img;
    this.selectedImageIndex = this.selectedPatent['images'].findIndex(image => image.path === img.path);
    if (!img.loadedHighQuality && img.path) {
      this.isLoadingImage = true;
      const payload = this.buildAttachmentPayload();
      payload['path'] = img.path;
      const getPatentImage$ = this.patentService.getPatentImage(payload)
        .pipe(finalize(() => {
          this.isLoadingImage = false;
        }))
        .subscribe({
          next: ({data}) => {
            if (data && data.bytes) {
              const imgData = 'data:image/png;base64,' + data.bytes;

              img.src = imgData;
              img.downloadUrl = imgData;
              img.loadedHighQuality = true;
              img.patentNumber = data['source-document'];

              this.selectedImage = img;
            }
          }
        });
      this.subscriptions.add(getPatentImage$);
    }
  }

  loadCitationReferences() {
    if (this.selectedPatent['general'].obfuscated) {
      return;
    }
    this.searchingCitation = true;
    const payload: CitationSearchRequest = {
      direction: 'bidirectional',
      level: 1,
      search_type: PatentListScopeEnum.PUBLICATION
    };

    if (this.isCitationsFilteredByCurrentDocument) {
      payload.patent_numbers = [this.activePublicationNumber];
    } else {
      payload.docdb_family_ids = [this.selectedPatent.general.docdb_family_id];
    }

    const search$ = this.citationSearchService.search(payload, {
      show_general: 1,
      page: this.paginationCitation.current_page,
      skip_log_search: 1
    })
      .pipe(
        take(1)
      ).subscribe({
        next:({data}) => {
          this.paginationCitation = data.page;
          this.searchingCitation = false;
        }
      });
    this.subscriptions.add(search$);
  }

  loadCollaborations(docdb_family_id) {
    this.collaboration = {users: [], groups: []}
    const getUsers$ = this.collaborationService.getUsers([docdb_family_id], CollaborationResourceTypeEnum.PATENT)
      .subscribe({
        next: (users) => {
          this.collaboration.users = users;
        }
      });
    this.subscriptions.add(getUsers$);

    const getGroups$ = this.collaborationService.getGroups([docdb_family_id], CollaborationResourceTypeEnum.PATENT)
      .subscribe({
        next: (groups) => {
          this.collaboration.groups = groups;
        }
      });
    this.subscriptions.add(getGroups$);
  }
  private updateClaims(): void {
    setTimeout(() => {
      this.showClaimsTree = this.isClaimsFormatted;
    });
  }

  loadClassifications() {
    const loadIpcClassifications$ = this.patentTableService.loadIpcClassifications(this.selectedPatent);
    this.subscriptions.add(loadIpcClassifications$);
    const loadCpcClassifications$ = this.patentTableService.loadCpcClassifications(this.selectedPatent);
    this.subscriptions.add(loadCpcClassifications$);
  }

  getDescription(multipleValues: boolean, field, value) {
    let description = '';
    let singular = '';
    let plural = '';

    switch (field) {
      case fields.IPC:
        description = this.patentTableService.getIpcDescription(value);
        singular = 'IPC code';
        plural = 'IPC codes';
        break;
      case fields.CPC:
        description = this.patentTableService.getCpcDescription(value);
        singular = 'CPC code';
        plural = 'CPC codes';
        break;
      case fields.INVENTORS:
      case fields.INVENTORS_ORIGINAL:
        singular = 'inventor';
        plural = 'inventors';
        break;
      case fields.APPLICANTS:
      case fields.APPLICANTS_ORIGINAL:
        singular = 'applicant';
        plural = 'applicants';
        break;
      case fields.ASSIGNEES:
      case fields.ASSIGNEES_ORIGINAL:
        singular = 'assignee';
        plural = 'assignees';
        break;
      default:
        return null;
    }

    if (multipleValues) {
      return `Show more documents from these ${plural}`;
    }

    if (!description) {
      return `Show more documents from this ${singular}`;
    }
    return description;
  }

  toggleSideSection(toggleSection: SideSectionEnum){
    this.clearSortAndFilterForComments();

    switch (toggleSection) {
      case SideSectionEnum.COMMENTS:
        this.patentCommentsActionSource = PatentCommentsActionSourceEnum.CONTROL_BAR;
        this.focusedComment = null;
        break;
    }

    this.sideSection = toggleSection === this.sideSection  ? undefined : toggleSection;
    this.patentHighlights.updateHighlightPopper();

    switch (this.sideSection) {
      case SideSectionEnum.RATINGS:
        this.patentViewService.refreshCountRatings = true;
        break;
    }

    if (this.sideSection) {
      this.isGalleryCollapsed = true;
    }
    setTimeout(() => { window.dispatchEvent(new Event('resize')); }, 20);
  }


  downloadPdf(event = null) {
    let isDownloading = false;
    for (const key of Object.keys(this.isDownloadingPdfs)) {
      if (this.isDownloadingPdfs[key]) {
        isDownloading = true;
        break;
      }
    }

    if (this.userService.isExternalUser() || isDownloading) {
      return;
    }

    const patentNumber = this.activePublicationNumber;
    this.isDownloadingPdfs[patentNumber] = true;

    const getPdf$ = this.patentService.getPdf(this.buildAttachmentPayload())
      .pipe(
        take(1),
        finalize(() => this.isDownloadingPdfs[patentNumber] = false)
      )
      .subscribe({
        next: (file) => {
          const blob = new Blob([file], {type: file.type});
          if (blob.type === 'application/pdf') {
            this.patentService.downloadFile(blob, patentNumber, this.selectedPatent);
          }
        },
        error: (err) => {
          console.error(err);
          if (err.status === 404) {
            this.showPdfNotFoundError(patentNumber, this.selectedPatent);
          } else {
            this.showPdfOtherErrors(patentNumber, this.selectedPatent);
          }
        }
      });
    this.subscriptions.add(getPdf$);
  }

  private showPdfNotFoundError(patentNumber, patent) {
    const modalRef = this.ngbModal.open(PdfNotFoundDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = patentNumber;
    modalRef.componentInstance.patent = patent;
  }

  private showPdfOtherErrors(patentNumber, patent) {
    const modalRef = this.ngbModal.open(PdfOtherErrorsDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = patentNumber;
    modalRef.componentInstance.patent = patent;
  }

  onSharePatentClicked(patent: Patent) {
    if(!this.userService.canUseSaveAndShareFeature('share')){
      return;
    }
    const title = `${patent.general.raw_publication_number}-${patent.bibliographic.title}`;
    const modal = this.ngbModal.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.hasSharedLink = true;
    modal.componentInstance.headline = `Share patent document ${title}`;
    modal.componentInstance.hideAgreementCheck = true;
    modal.componentInstance.resourceId = patent.general.docdb_family_id;
    modal.componentInstance.resourceType = CollaborationResourceTypeEnum.PATENT;
    modal.componentInstance.shareType = ShareDialogShareTypeEnum.SHARE_WITH_TEAM;
    modal.componentInstance.canSwitchShareType = true;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.shareUrl = '/patent/view/';
  }

  openPublication(publication: string) {
    if (this.userService.isExternalUser()) {
      return;
    }
    window.open(publication);
  }

  hasWorkflowFeature(): boolean {
    return this.userService.canUseWorkflowFeature();
  }

  backToSearch() {
    this.patentViewModeService.currentPatentUrl = null;
    this.router.navigateByUrl(decodeURI(this.patentViewService.previousRoute));
  }

  private updateSideBarViewModeUI(mode: PatentSideBarViewModeEnum) {
    this.showComments = mode === PatentSideBarViewModeEnum.MODE_COMMENTS;
    this.showHighlightsSession = mode === PatentSideBarViewModeEnum.MODE_HIGHLIGHTS;
  }

  private calculateSmartHighlights(patent: Patent): Observable<Patent> {
    const shouldLoadSmartHighlights = (this.monitorRunId || this.smartHighlightSearchHash) && this.showSmartHighlight;

    if (!shouldLoadSmartHighlights) {
      return of(patent);
    }

    const payload = {
      document_id: Number(patent.general.docdb_family_id)
    } as SmartHighlightRequest;

    if (this.activePublicationNumber !== patent?.general?.raw_publication_number) {
      payload.publication_number = this.activePublicationNumber;
    }

    if (this.monitorRunId) {
      payload.monitor_run_id = this.monitorRunId;
    } else {
      if (this.smartHighlightSearchHash) {
        payload.search_hash = this.smartHighlightSearchHash;
      }
    }

    this.isLoadingSmartHighlights = true;

    return this.analysisService.calculateSmartHighlights(payload)
      .pipe(
        tap((res) => {
          const smartRes = res as SmartHighlights;
          if (smartRes && (smartRes.claims?.length || smartRes.description?.length)) {
            this.smartHighlights[this.selectedPatent.general.docdb_family_id] = res;
          } else {
            this.smartHighlights[this.selectedPatent.general.docdb_family_id] = null;
          }
        }),
        map(() => patent),
        catchError((error) => {
          console.error(error);
          return of(null);
        }),
        finalize(() => {
          this.loadHighlightSubject.next(this.selectedPatent);
          this.isLoadingSmartHighlights = false;
        })
      );
  }

  private loadHighlight(patent: Patent): Observable<Patent> {
    if (!patent) {
      this.getDocumentHighlights();
      return of(null);
    }
    const shouldLoadHighlight = this.highlightSearchHash && this.showHighlight && patent?.general?.docdb_family_id;

    if (!shouldLoadHighlight) {
      this.getDocumentHighlights();
      return of(patent);
    }

    const highlightPayload = {
      search_hash: this.highlightSearchHash,
      boolean_query: this.filtersQuery
    };

    if (this.patentViewService.activePublicationName) {
      highlightPayload['publication_number'] = this.patentViewService.activePublicationName;
    } else {
      highlightPayload['document_id'] = this.patentViewService.activeDocumentId;
    }

    return this.documentService.getHighlight(highlightPayload)
      .pipe(
        tap((res) => {
          patent.highlight = res;
        }),
        map(() => patent),
        catchError((error) => {
          console.error(error);
          return of(patent);
        }),
        tap(() => this.getDocumentHighlights())
      );
  }

  onScroll(event: any) {
    const h: HTMLElement = event.target;
    this.scrollPercent  = (h.scrollTop / (h.scrollHeight - h.clientHeight)) * 100;

    this.patentHighlights.onScrollColorMenu(event);
  }

  get scrollBorder(): string{
    return `circle-border-${Math.floor(this.scrollPercent)}`;
  }

  openSearchBox() {
    if (this.isSearchBoxOpen) {
      const searchBoxElement = document.getElementById('inputSearchTerm') as HTMLInputElement;
      searchBoxElement.select();
      return;
    }
    this.isSearchBoxOpen = true;

    const bodyWidth = document.body.clientWidth;
    const searchButtonElement = document.getElementById('btnSearchBox');
    const searchBoxElement = document.getElementById('searchBox');

    if (searchButtonElement && searchBoxElement) {
        const searchButtonPosition = searchButtonElement.getBoundingClientRect();
        searchBoxElement.style.right = `${bodyWidth - searchButtonPosition.right}px`;
    }
    this.highlightSearchTerm();
    setTimeout(() => {
      document.getElementById('inputSearchTerm').focus();
    });

  }

  closeSearchBox() {
    this.isSearchBoxOpen = false;
    const markInstance = this.getMarkInstanceForSearch();
    markInstance.unmark();
  }

  private getMarkInstanceForSearch() {
    const instance = new Mark(document.getElementById('patent-result'));
    instance.markRegExp(new RegExp(this.searchTerm, "gi"), {"exclude": [ ".ignore-search-mark" ]});
    return instance;
  }

  highlightSearchTerm() {
    this.matches = [];
    this.currentIndex = -1;
    const markInstance = this.getMarkInstanceForSearch();

    if (this.searchTerm.length < 2) {
      markInstance.unmark();
      return;
    }

    markInstance.unmark({
      done: () => {
        markInstance.mark(this.searchTerm, {
          separateWordSearch: false,
          exclude: [ ".ignore-search-mark" ],
          each: (node) => {
            this.matches.push(node);
          },
          done: () => {
            this.nextMatch();
          },
          accuracy: this.isWholeWord ? 'exactly' : 'partially'
        });
      }
    });
  }

  nextMatch() {
    if (this.matches.length > 0) {
      this.currentIndex = (this.currentIndex + 1) % this.matches.length;
      this.updateHighlight();
      this.scrollToCurrentMatch();
    }
  }

  previousMatch() {
    if (this.matches.length > 0) {
      this.currentIndex = (this.currentIndex - 1 + this.matches.length) % this.matches.length;
      this.updateHighlight();
      this.scrollToCurrentMatch();
    }
  }

  private scrollToCurrentMatch() {
    this.scrollToElement(this.matches[this.currentIndex], 100);
  }

  private updateHighlight() {
    this.matches.forEach((match, index) => {
      if (index === this.currentIndex) {
        match.classList.add('current');
      } else {
        match.classList.remove('current');
      }
    });
  }

  private detectScreenSize() {
    const mediaObserver$ = this.mediaObserver
      .asObservable()
      .subscribe({
        next: (change) => {
          change.forEach((item) => {
            this.screenSizeAlias = item.mqAlias;
            if (['sm', 'xs'].includes(item.mqAlias)) {
              this.isGalleryCollapsed = true;
            }
          });
        }
      });
    this.subscriptions.add(mediaObserver$);
  }
  onSaveToCollectionClicked(event) {
    (event.target as HTMLElement).blur();
    if(!this.userService.canUseSaveAndShareFeature('save')){
        return;
    }
    const modal = this.ngbModal.open(AddToCollectionComponent, {size: 'lg'});
    modal.componentInstance.searchService = this.searchService;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.patentListScope = this.patentViewService.activePublicationName ? PatentListScopeEnum.PUBLICATION:PatentListScopeEnum.FAMILY;
    modal.result.then(msg => {
      if (msg) {
        this.confirmationDialogService.alert('Patent saved', msg);
      }
    }, reason => { });
  }
  openEspacenet() {
    window.open(this.espacenetService.getWorldwideLinkByPublicationNumber(this.activePublicationNumber, this.selectedPatent.general.docdb_family_id), '_blank').focus();
  }

  openWipoPatentscope() {
    window.open(this.espacenetService.getWipoLinkByPublicationNumber(this.activePublicationNumber), '_blank').focus();
  }

  openGlobalDossier() {
    window.open(this.espacenetService.getGlobalDossierByPublicationNumber(this.activePublicationNumber), '_blank').focus();
  }

  openLegalStatusTrackingModal(publication: string){
    if (!this.isLegalStatusTracked(publication)) {
      this.matomoService.monitorLegalStatusTrackButton();
    }
    const modalRef = this.ngbModal.open(PatentLegalStatusTrackingComponent, {size: 'lg'});
    modalRef.componentInstance.publications = this.patent.bibliographic.also_published_as;
  }

  getSideSectionTooltip(): {title: string, description: string} {
    switch (this.sideSection) {
      case SideSectionEnum.OCTI_AI:
        return {
          'title': '',
          'description': `Octi AI is an AI chatbot and offers general information about patents or lists of patents. <br />
              <br />
              Its purpose is to simplify and improve patent searches within Octimine by extracting patent information and it is designed not to access personal data. <br />
              Due to technical requirements Oct AI is forced to use Microsoft Azure's globally distributed hosting service. <br />
              This means, that while OctiAI itself will not store or use input data for training purposes, input data may be processed outside of the European Union. <br />
              We strive for accuracy, however, the information provided should not be considered legal advice or a substitute for consulting a human representative for further assistance. <br />
              Users are responsible for their interactions with Octi AI and any reliance on its responses. <br />
              Octimine disclaims liability for errors or omissions in the chatbot's information and for any consequences, including direct or indirect damages, arising from its use.<br />
              <br />
              Given the description above, we consider that Octi AI complies with data protection requirements and regulations. <br />
              Nevertheless, we follow legal developments in the field of AI and data privacy and if required, we will implement the required adaptions. <br />
              In any case, we recommend removing personal data and confidential information of any kind from your inputs.`
        };
      case SideSectionEnum.COMMENTS:
        return {
          'title': 'Comments',
          'description': 'Here you can view the comments referring to the whole document (general) or to a specific part of it (on section)'
        };

      case SideSectionEnum.HIGHLIGHTS:
        return {
          'title': 'Highlights',
          'description': null
        };
      case SideSectionEnum.RELEVANCE:
        return {
          'title': 'Relevant parts',
          'description': 'Here we suggest the main passages of the document that are the most similar to your search.'
        };
      case SideSectionEnum.LIST:
        return {
          'title': SideSectionEnum.LIST,
          'description': 'Here you can access the list of documents from which you opened the one you are currently viewing. Click on each to open it.'
        };
      case SideSectionEnum.RATINGS:
        return {
          'title': 'Ratings',
          'description': null
        };
    }
  }

  getSideSectionTooltipClass(): string {
    switch (this.sideSection) {
      case SideSectionEnum.OCTI_AI:
        return 'tooltip-large';
      default:
        return 'tooltip-md';
    }
  }

  canAddNumbersToSearch(): boolean {
    return this.userService.isNotExternalUser() && this.enableToAdd();
  }

  enableToAdd(): boolean {
    if (this.storeService.typedPublications.indexOf(this.activePublicationNumber) > -1) {
      return false;
    }
    return this.storeService.typedPublications.length + 1 <= 5;
  }
  get isSemanticReferral(): boolean{
    return this.searchService === this.semanticSearchService && this.storeService === this.semanticSearchStoreService;
  }

  onAddToSearchClicked() {
    let publications = [this.activePublicationNumber];
    if(this.semanticSearchStoreService.patentNumbers){
      publications = [this.semanticSearchStoreService.patentNumbers, this.activePublicationNumber];
    }
    let params = publications.join('  ');
    if(this.semanticSearchStoreService.searchInput){
      params += `  ${this.semanticSearchStoreService.searchInput}`;
    }
    this.makeSemanticSearch(params);
  }

  private makeSemanticSearch(searchText: string){
    if(searchText.length > 1500) searchText = searchText.substring(0,1500);
    window.open(`search/patent?publication=${decodeURI(searchText)}`, '_blank');
  }


  toggleDescription(){
    this.showDescription=!this.showDescription;
    this.annotationService.highlightComments();
  }

  toggleClaims(){
    this.showClaims=!this.showClaims;
    this.annotationService.highlightComments();
  }

  /*----------------------------------------- start HIGHLIGHTS functions -------------------------------------------*/

  setPopperAction(event: {action: PatentHighlightActionEnum, event: MouseEvent}) {
    this.patentHighlights.patentHighlightAction = event.action;

    if ([PatentHighlightActionEnum.HIGHLIGHT].indexOf(event.action) === -1) {
      this.patentHighlights.hidePoppers();
    }

    if (event.action !== PatentHighlightActionEnum.OCTI_AI) {
      this.octiAIPopper.hide();
    }

    switch (event.action) {
      case PatentHighlightActionEnum.OCTI_AI:
        this.toggleOctiAIPopper();
        break;
      case PatentHighlightActionEnum.HIGHLIGHT:
        this.patentHighlights.openColorsPopper(event.event, ColorsPopperSourceEnum.POPPER_MENU);
        break;
      case PatentHighlightActionEnum.COMMENTS:
        this.toggleCommentEditor();
        break;
    }
  }

  private clearHighlightData() {
    this.patentHighlights.clearHighlightData();
  }

  private clearSortAndFilterForComments() {
    this.annotationService.resetSortAndFilterOptions();
    this.annotationService.sortAndFilterComments();
  }

  onCloseSideSectionClicked() {
    this.sideSection = undefined;
    this.clearHighlightData();
    this.clearSortAndFilterForComments();
    window.dispatchEvent(new Event('resize'));
  }

  private getDocumentHighlights() {
    if (!this.selectedPatent?.general?.docdb_family_id) {
      return;
    }

    this.isLoadingDocumentHighlights = true;
    const getLabelsAndComments$ = this.annotationService.getLabelsAndComments(parseInt(this.selectedPatent?.general?.docdb_family_id))
      .pipe(
        take(1),
        switchMap(({data}) => {
          this.highlightList = data.labels.filter(lb => lb.label.is_highlight);
          this.initializeComments(data.comments);
          this.sortHighlightsList(this.patentViewService.currentHighlightsSort);
          return of(this.highlightList);
        }),
        concatMap(highlights => {
          const teamUserIds = [...new Set(highlights.map(h => h.user_id))]
          if(teamUserIds.length>0){
            const teamUsersPayload = {
              id: 'in:' + teamUserIds.join(','),
              load_all: 1,
              include_me: 1,
              include_blocked: 1
            };

            return this.userService.getTeamUsers(teamUsersPayload)
              .pipe(
                catchError(error => {
                  console.error(error);
                  return of(null);
                }),
                map(({users}) => {
                  if (users) {
                    const usersByIds = {};
                    users.forEach(user => usersByIds[user.id] = user);

                    highlights.forEach(highlight => {
                      highlight.user = usersByIds[highlight.user_id];
                      this.patentViewHighlightService.setHighlightSavedAnnotation(highlight);
                    });
                  }
                  return highlights;
                })
              );
          } else {
            return of(null);
          }
        }),
        catchError(error => {
          console.error(error);
          return of([]);
        }),
        finalize(() => {
          this.blockSelection = true;
          this.isLoadingDocumentHighlights = false;
        })
      )
      .subscribe();
    this.subscriptions.add(getLabelsAndComments$);
  }

  sortHighlightsList(sortOption: SortHighlightsOptionEnum) {
    this.highlightList.sort((a, b) => {
      switch (sortOption) {
        case SortHighlightsOptionEnum.RECENTLY_ADDED:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case SortHighlightsOptionEnum.OLDEST_ADDED:
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case SortHighlightsOptionEnum.CREATOR:
          return (a.user.first_name + a.user.last_name).localeCompare(b.user.first_name + b.user.last_name);
        case SortHighlightsOptionEnum.COLOR:
          return a.label.color.localeCompare(b.label.color);
      }
    });
  }

  changeHighlightColor(event) {
    this.patentHighlights.changeHighlightColor(event);
  }

  onSaveHighlight() {
    this.sortHighlightsList(this.patentViewService.currentHighlightsSort);
    this.updateListAnnotations('labels', true);
  }

  highlightClosePoppers() {
    this.addComment = false;
  }

  /* ---------------------------------------------------start comment functions------------------------------- */

  initializeComments(comments: DocumentAnnotation[]){
    this.annotationStore.listComments = comments;
    this.annotationService.highlightComments();
    this.loadUsers();
    if(this.annotationService.focusCommentId){
      const comment = this.annotationStore.listComments.find(c => c.id == this.annotationService.focusCommentId);
      if(comment){
        this.openCommentSection(comment);
      }
    }
  }
  toggleCommentEditor(){
    if(this.addComment){
      this.sectionCommentEditor.hide();
      this.addComment = false;
    } else {
      const selectedNode = document.getElementById(this.currentSelectedContainer.highlightNode.id) as HTMLElement;
      this.addComment = true;
      this.sectionCommentEditor.show(selectedNode);
    }
  }

  toggleOctiAIPopper(){
    if (!this.userService.hasOctiAiFeature()) {
      return;
    }

    this.showOctiAIPopper = !this.showOctiAIPopper;
    if(this.showOctiAIPopper){
      const selectedNode = document.getElementById(this.currentSelectedContainer.highlightNode.id) as HTMLElement;
      this.octiAIPopper.show(selectedNode);
    } else {
      this.octiAIPopper.hide();
    }
  }

  onCommentNoteClick(comment) {
    if(this.sideSection !== this.sideSectionEnum.COMMENTS){
      this.openCommentSection(comment);
    }
    setTimeout(() => { this.annotationService.scrollToComment(comment); }, 200);
  }

  private openCommentSection(comment = null){
    this.focusedComment = comment;
    this.sideSection = SideSectionEnum.COMMENTS;
    this.patentCommentsActionSource = comment.source === DocumentCommentSourceEnum.GENERAL? PatentCommentsActionSourceEnum.GENERAL_COMMENT: PatentCommentsActionSourceEnum.SECTION_COMMENT;
    this.isGalleryCollapsed = true;

    if (comment) {
      setTimeout(() => {
        this.annotationService.scrollToComment(comment);
        this.annotationService.focusinComment(comment);
        setTimeout(() => {
          this.annotationService.scrollToHighlightedElement(comment);
        }, 500);
      }, 1000);

      setTimeout(() => {
        this.annotationService.focusoutComment(comment);
      }, 5000);
    }
    window.dispatchEvent(new Event('resize'));
  }

  private loadTeamUsers(): void {
    const getTeamUsers$ = this.userService.getTeamUsers({'load_all': 1})
      .subscribe({
        next: ({users}) => {
          users.forEach((u) => u.display_name = this.userTitlePipe.transform(u));
          this.teamUsers = users;
        }
      });
    this.subscriptions.add(getTeamUsers$);
  }

  private loadTeamGroups(): void {
    const getGroups$ = this.groupService.getGroups({'load_all': 1})
      .subscribe({
        next: ({groups}) => {
          this.teamGroups = groups;
        }
      });
    this.subscriptions.add(getGroups$);
  }

  onMouseOverComment(comment){
    this.annotationService.focusinComment(comment);
  }

  onMouseLeaveComment(comment){
    this.annotationService.focusoutComment(comment);
  }
  addSectionComment(event){
    const tag = document.getElementById(this.mouseDownElementId);
    const {startPos, endPos} = this.textHighlightService.getSelectedTextPosition(tag, TextHighlightService.HIGHLIGHT_TEXT_TMP_CSS_CLASS);
    const payload: DocumentAnnotation = {
      end_pos: endPos,
      field: this.mouseDownElementId.split('-')[0],
      start_pos: startPos,
      text: this.textHighlightService.getSelectedText(this.mouseDownElementId, ''),
      color: '',
      publication_number: this.patentViewService.activePublicationName || null,
      comment: event.comment,
      private: event.private,
      parent_comment_id: event.parent_comment_id ? event.parent_comment_id : null,
      tagged_user_ids: event.tagged_user_ids,
      tagged_group_ids: event.tagged_group_ids
    };
    const saveSectionComment$ = this.annotationService.saveDocumentComment(payload, Number(this.selectedPatent.general.docdb_family_id))
      .pipe(finalize(() => {this.patentHighlights.hidePoppers();})).subscribe({next:({data}) => {
        this.clearHighlightData()
        data.user = this.userService.getUser().profile;
        this.annotationStore.addComment(data);
        this.annotationService.highlightComments(true, data);
        this.updateListAnnotations('comments', true);
      }, error: err => {
        console.log(err);
        this.toastService.show({
          type: ToastTypeEnum.ERROR,
          header: 'Comment submission failed',
          body: `The comment submission has been failed.<br/>${err.error.message}`,
          delay: 5000
        });
      }});
    this.subscriptions.add(saveSectionComment$);
  }
  updateListAnnotations(property: string, value: boolean) {
    let doc;
    if (this.patentViewService.isFocalPatent) {
      doc = this.patentNumberService.getDocuments().find(d => d.general.docdb_family_id === this.selectedPatent.general.docdb_family_id);
    } else {
      doc = this.searchService.getDocuments().find(d => d['general'].docdb_family_id === this.selectedPatent.general.docdb_family_id);
    }

    if (doc) {
      doc['annotations'][property] = value;
    }
  }

  loadUsers() {
    const userIds = [...new Set(this.annotationStore.listComments.filter(cm => !cm.user).map(a => a.user_id))];

    if (userIds.length > 0) {
      const teamUsersPayload = { id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1, include_blocked: 1 };

      const getTeamUsers$ = this.userService.getTeamUsers(teamUsersPayload)
        .subscribe({
          next: ({ users }) => {
            const mappedUser = new Map();
            for (let u of users) {
              mappedUser.set(u.id, u);
            }
            this.annotationStore.listComments.forEach(a => {
              if (mappedUser.has(a.user_id)) {
                a.user = mappedUser.get(a.user_id)
              }
            });
          }, error: error => console.log('error in fetching user')
        });
    }
  }
  /* ---------------------------------------------------end comment functions------------------------------- */
  /* ---------------------------------------------------start current list function------------------------- */
  initializeCurrentList(queryParams){
    this.setDocumentsFromPreviousRoute();
    if(queryParams.patent_index){
      this.patentViewService.currentListIndex = queryParams.patent_index;
    }
    this.patentViewService.documents = this.patentViewService.isFocalPatent ? this.patentNumberService.getDocuments() : this.searchService.getDocuments();
    this.patentViewService.searchParams = JSON.parse(JSON.stringify(this.storeService.search));
    this.storeService.patentViewerDocuments = this.patentViewService.documents;
    this.patentViewService.hasResultList = this.patentViewService.documents?.length > 0;
  }
  private setDocumentsFromPreviousRoute() {
    if (this.loadDocumentsFromLocalStorage()) {
      return;
    }
    if (this.patentViewService.previousRouteIsCollection()) {
      this.searchService.setDocuments(this.collectionService.getDocuments() ? this.collectionService.getDocuments() : []);
    } else if (this.patentViewService.previousRouteIsMonitor()) {
      this.searchService.setDocuments(this.monitorStoreService.resultSetDocuments);
    } else if (this.patentViewService.previousRouteIsLandscape()) {
      this.searchService.setDocuments(this.landscapeService.getDocuments());
    }
  }
  private loadDocumentsFromLocalStorage(): boolean {
    return this.storeService.restoreSearchData((data: SearchDataModel) => {
      if (this.patentViewService.isFocalPatent) {
        this.patentNumberService.setDocuments(data.documents);
      } else {
        this.searchService.setDocuments(data.documents);
      }
    });
  }
  onCurrentListOpenDocument(data: { documentId: number, publicationNumber: string, currentListIndex: number}){
    this.updateCurrentPatentUrl(data.documentId, data.publicationNumber, data.currentListIndex);
  }

  private getCurrentQueryParam(currentListIndex: number){

    const queryParams = [];

    if (this.searchHash) {
      queryParams.push(`search_hash=${this.searchHash}`);
    }

    if (this.monitorRunId) {
      queryParams.push(`monitor_run_id=${this.monitorRunId}`);
    }

    if (this.smartHighlightSearchHash) {
      queryParams.push(`smart_highlight_search_hash=${this.smartHighlightSearchHash}`);
    }

    if (this.showSmartHighlight) {
      queryParams.push('show_smart_highlight=1');
    }

    if (this.showHighlight) {
      queryParams.push('show_highlight=1');
    }

    if (this.filtersQuery) {
      queryParams.push(`filters_query=${encodeURIComponent(this.filtersQuery)}`);
    }

    if (currentListIndex) {
      queryParams.push(`patent_index=${currentListIndex}`);
    }

    return queryParams;
  }

  private updateCurrentPatentUrl(documentId: number, publicationNumber: string, currentListIndex: number) {
    let baseUrl = this.router.url.split('?')[0];
    baseUrl = baseUrl.split('/publication/')[0];
    baseUrl = baseUrl.substring(0, baseUrl.lastIndexOf('/'));
    baseUrl = `${baseUrl}/${documentId}`;
    if (publicationNumber) {
      baseUrl = `${baseUrl}/publication/${publicationNumber}`;
    }
    const currentPatentUrl = `${baseUrl}?${this.getCurrentQueryParam(currentListIndex).join('&')}`;
    this.location.replaceState(currentPatentUrl);
    this.patentViewModeService.currentPatentUrl = baseUrl;
  }
  /* ---------------------------------------------------end current list function--------------------------- */

  onPatentTagsChanged(tags: TagModel[]) {
    if (!this.selectedPatent.custom_tags) {
      this.selectedPatent.custom_tags = [];
    }

    this.selectedPatent.custom_tags = tags;
    this.selectedPatent = {...this.selectedPatent};
    if (this.patentViewService.documents?.length) {
      const doc = this.patentViewService.documents.find(doc => doc['general'].docdb_family_id === this.selectedPatent.general.docdb_family_id);
      if (doc) {
        doc['custom_tags'] = tags;
      }
    }
  }

  hidePoppers() {
    this.patentHighlights.hidePoppers();
  }

  getOwners(field) {
    if (this.selectedPatent.bibliographic[field]?.length) {
      return {values: this.getOwnersValues(field), field: 'OWNER_IDS', labels: this.getOwnersLabels(field)}
    }
    if (field === 'owners') {
      return {values: this.selectedPatent.bibliographic['owner_ids'], field: 'OWNER_IDS', labels:this.selectedPatent.bibliographic['assignees']}
    }
  }

  getOwnersLabels(field) {
    if (field === 'assignees_original') {
      return this.selectedPatent.bibliographic[field];
    }
    return this.selectedPatent.bibliographic[field].map(ow => ow.name);
  }

  getOwnersValues(field) {
    return this.selectedPatent.bibliographic[field].map(ow => ow.id);
  }

  onHighlightsSorted(sortOption: SortHighlightsOptionEnum) {
    this.sortHighlightsList(sortOption);
  }

  filterCitationList(filterVal) {
    if (this.paginationCitation) {
      this.paginationCitation.current_page = 1;
    }
    this.selectedCitationFilter = filterVal;
    this.loadCitationReferences();
  }

  onCurrentListOpenNewTab(data: { documentId: number, publicationNumber: string, currentListIndex: number }) {
    const queryParams = this.getCurrentQueryParam(data.currentListIndex);
    const baseUrl = this.patentViewService.getPatentViewerBaseUrl(data.documentId, data.publicationNumber);
    const newUrl = `${baseUrl}?${queryParams.join('&')}`;

    window.open(newUrl, '_blank');
  }

  onCitationsPageChange(page: number) {
    const {current_page} = this.paginationCitation;

    if (current_page === page) {
      return;
    }

    this.paginationCitation.current_page = page;
    this.loadCitationReferences();
  }

  private countRatings(): Observable<any> {
    this.ratings = [];
    this.countDoneRatings = 0;
    this.isCountingRatings = true;

    const payload = {
      document_id: this.selectedPatent?.general?.docdb_family_id,
      load_all: 1
    };

    return this.taskService.getRatings(payload, true)
      .pipe(
        take(1),
        tap(({tasks}) => {
          const {ratings, answerableRequests, createdRequests, otherRequests} = this.taskService.splitTasksIntoRequestsAndRatings(tasks);
          this.ratings = [...ratings, ...answerableRequests, ...createdRequests, ...otherRequests];
          this.countDoneRatings = ratings.length;

          const queriedTask = this.currentQueryRatingId ? this.ratings.find(r => r.id === this.currentQueryRatingId) : null;
          const queriedAssignment = this.currentQueryAssignmentId ? queriedTask.assignments[0] : null;
          this.navigateToRating(queriedTask, queriedAssignment);

          if (answerableRequests.length > 0) {
            this.openRatingsSection();
          }
        }),
        finalize(() => this.isCountingRatings = false),
        catchError((error) => {
          console.log(error);
          throw error;
        })
      );
  }

  private openRatingsSection() {
    if (!this.isRatingsCountedFirstTime && !this.currentQueryRatingId && !this.sideSection) {
      this.isRatingsCountedFirstTime = true;
      this.toggleSideSection(SideSectionEnum.RATINGS);
    }
  }

  private navigateToRating(task: TaskModel, assignment: TaskAssignmentModel) {
    if (task) {
      if (this.sideSection !== SideSectionEnum.RATINGS) {
        this.toggleSideSection(SideSectionEnum.RATINGS);
      }
      this.patentViewService.ratingNavigation = {task, assignment} as RatingNavigationParam;
      this.removeFieldsFromQueryParams();
    }
  }

  private loadFamilyLegalStatus() {
    if (!this.patent) {
      return;
    }
    const getDocumentInfo$ = this.patentService.getDocumentInfo(this.patent.general.docdb_family_id)
      .subscribe({next:({data}) => {
        this.publicationsDocumentInfo = data['document_info'];
      }, error: (err) => { console.error(err); }});
    this.subscriptions.add(getDocumentInfo$);
  }

  getAmountOfPublicationByAuthority(authority: string): number {
    return this.patentService.getAmountOfPublicationByAuthority(this.patent.bibliographic.also_published_as, authority);
  }

  getPublicationsAuthority(authority: string): string[] {
    return this.patent.bibliographic.also_published_as.filter(pub => pub.startsWith(authority));
  }

  private showCustomToast() {
    if (!this.patentViewService.publication?.custom_toaster) {
      return;
    }
    this.toastService.show({
      type: ToastTypeEnum.WARNING,
      header: 'U.S. application review',
      body: this.patentViewService.publication.custom_toaster,
      autohide: false,
    });
  }

  private removeCustomToast() {
    const toast = this.toastService.toasts.find( t => t.header == 'U.S. application review');
    if (toast) {
      this.toastService.remove(toast);
    }
  }

  private loadTeamReadDocuments() {
    this.readDocumentUsers = [];
    this.readDocuments = [];
    const payload = {
      document_id: this.selectedPatent.general.docdb_family_id,
      load_all: 1,
      sort_by: 'last_read',
      sort_order: 'desc',
    };
    const subscription = this.readDocumentsService.getTeamReadDocuments(payload)
      .subscribe({
        next: ({read_documents}) => {
          this.readDocuments = read_documents;
          this.readDocumentUsers = read_documents.map(doc => doc.user).filter(user => user);
        },
        error: (error) => {
          console.error(error);
        }
      });
    this.subscriptions.add(subscription);
  }

  getReadDocument(user: TeamUser): ReadDocument {
    return this.readDocuments.find(doc => doc.user_id === user.id);
  }

  isMe(user: TeamUser): boolean {
    return this.userService.isMe(user);
  }

  private removeFieldsFromQueryParams() {
    const queryParams = {...this.activatedRoute.snapshot.queryParams};
    delete queryParams['rating_id'];
    delete queryParams['assignment_id'];
    delete queryParams['task_id'];
    delete queryParams['gallery_index'];
    delete queryParams['back_data'];

    this.currentQueryRatingId = null;
    this.currentQueryAssignmentId = null;

    const urlTree = this.router.createUrlTree([], {
      queryParams: queryParams,
      preserveFragment: true
    });
    this.location.replaceState(urlTree.toString());
  }

  closeOctiAIPopper() {
    this.showOctiAIPopper = false;
    this.octiAIPopper.hide();
    this.patentViewHighlightService.clearCurrentHighlight();
    this.annotationService.clearHighlightedExplainingText();
  }

  onSubmitPopper(content: OctiQuestion) {
    if (!this.userService.hasOctiAiFeature()) {
      return;
    }
    if (this.sideSection !== SideSectionEnum.OCTI_AI) {
      this.toggleSideSection(SideSectionEnum.OCTI_AI);
    }
    this.patentViewService.setOctiAIPopper(content);
  }
  onNewChat() {
    this.octiAiService.setNewChat();
  }

  private buildAttachmentPayload() {
    return  this.isPublicationView ? {patent_number: this.patentViewService.activePublicationName} : {document_id: this.selectedPatent.general.docdb_family_id};
  }

  private scrollToElement(ele: HTMLElement, topBuffer: number = 0){
    const patentBodyEle = document.getElementById('figma-patent-body');
    HtmlElementUtil.scrollBy(patentBodyEle, ele);
  }
}
