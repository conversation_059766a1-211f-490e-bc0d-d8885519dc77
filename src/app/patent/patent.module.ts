import { NgModule } from '@angular/core';
import { SharedModule } from '../shared';
import { PatentRoutingModule } from './patent-routing.module';
import { PatentViewComponent } from './patent-view/patent-view.component';
import { FigmaElementsComponent } from './figma-elements/figma-elements.component';
import { PatentLegalStatusTrackingComponent } from './patent-legal-status-tracking/patent-legal-status-tracking.component';
import { PatentCommentsComponent } from './patent-comments/patent-comments.component';
import { PatentCommentEditorComponent } from './patent-comments/patent-comment-editor/patent-comment-editor.component';
import { MentionModule } from 'angular-mentions';
import { PatentCommentBoxComponent } from './patent-comments/patent-comment-box/patent-comment-box.component';
import { PatentRelevanceComponent } from './patent-relevance/patent-relevance.component';
import { PatentHighlightsSectionComponent } from './patent-highlights-section/patent-highlights-section.component';
import { PatentHighlightsComponent } from './patent-highlights/patent-highlights.component';
import { PatentListComponent } from './patent-list/patent-list.component';
import { TreeViewClaimsComponent } from './tree-view-claims/tree-view-claims.component';
import { NgOptimizedImage } from '@angular/common';
import { PatentRatingsComponent } from './patent-ratings/patent-ratings.component';
import { CountSerialPipe } from '@core';
import { RequestViewComponent } from './patent-ratings/request-view/request-view.component';
import { OctiAIPopperComponent } from './octi-ai-popper/octi-ai-popper.component';

@NgModule({
  imports: [
    SharedModule,
    MentionModule,
    NgOptimizedImage,
    PatentRoutingModule,
  ],
  providers: [CountSerialPipe],
  declarations: [
    PatentViewComponent,
    FigmaElementsComponent,
    PatentLegalStatusTrackingComponent,
    PatentCommentsComponent,
    PatentCommentEditorComponent,
    PatentCommentBoxComponent,
    PatentRelevanceComponent,
    PatentHighlightsSectionComponent,
    PatentHighlightsComponent,
    PatentListComponent,
    PatentRelevanceComponent,
    TreeViewClaimsComponent,
    PatentRatingsComponent,
    RequestViewComponent,
    OctiAIPopperComponent
  ]
})
export class PatentModule {
}
