<div *ngIf="!isSubHeader" class="figma-side-section-header d-flex justify-content-between align-items-center">
  <div class="d-flex justify-content-start">
    <div class="content-heading-h4 content-color-primary">
      {{ title }}
    </div>
    <span *ngIf="tooltip.description" class="d-flex justify-content-center align-items-center tooltip-title-section">
      <i class="fa-regular fa-circle-info tooltip-icon"
         [ngbTooltip]="sideSectionHeaderTooltipTemplate"
         [tooltipClass]="tooltipClass"
         container="body"></i>
    </span>
  </div>

  <div class="d-flex justify-content-end align-items-center" *ngIf="showCloseButton">
      <span class="content-label-small"><ng-content select="[sideButtons]"></ng-content></span>
      <span ngbTooltip="Close" tooltipClass="white-tooltip"
            (click)="onCloseClicked($event)"
            class="button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center">
        <i class="fa-regular fa-xmark cursor-pointer p-spacing-s fa-fw fa-lg color-grey-900"></i>
      </span>
  </div>
</div>

<div *ngIf="isSubHeader" class="figma-side-section-header d-flex justify-content-between align-items-center">
  <div class="d-flex justify-content-end align-items-center" *ngIf="showCloseButton">
      <span class="content-label-small"><ng-content select="[sideButtons]"></ng-content></span>
      <span ngbTooltip="Close" tooltipClass="white-tooltip"
            (click)="onCloseClicked($event)"
            class="button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center">
        <i class="fa-regular fa-xmark cursor-pointer p-spacing-s fa-fw fa-lg color-grey-900"></i>
      </span>
  </div>

  <div class="d-flex justify-content-center flex-fill">
    <div class="content-heading-h5 content-color-primary">
      {{ title }}
    </div>
    <span *ngIf="tooltip.description" class="d-flex justify-content-center align-items-center tooltip-title-section">
      <i class="fa-regular fa-circle-info tooltip-icon"
         [ngbTooltip]="sideSectionHeaderTooltipTemplate"
         tooltipClass="tooltip-md"
         container="body"></i>
    </span>
  </div>

  <div class="d-flex justify-content-end align-items-center invisible" *ngIf="showCloseButton">
      <span class="button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center">
        <i class="fa-regular fa-xmark cursor-pointer p-spacing-s fa-fw fa-lg color-grey-900"></i>
      </span>
  </div>
</div>

<ng-template #sideSectionHeaderTooltipTemplate>
  <div class="text-left p-spacing-x-s">
    <div class="content-heading-h6 m-b-spacing-md" *ngIf="tooltip.title">{{ tooltip.title }}</div>
    <div class="content-body-small" [innerHTML]="tooltip.description"></div>
  </div>
</ng-template>
