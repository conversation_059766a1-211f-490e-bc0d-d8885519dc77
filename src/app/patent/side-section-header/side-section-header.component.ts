import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-patent-side-section-header',
  templateUrl: './side-section-header.component.html',
  styleUrls: ['./side-section-header.component.scss']
})
export class SideSectionHeaderComponent {
  @Input() title: string;
  @Input() tooltip: { title: string, description: string } = {title: null, description: null};
  @Input() isSubHeader = false;
  @Input() showCloseButton = true;
  @Input() tooltipClass: string = '';
  @Output() closeEvent: EventEmitter<void> = new EventEmitter<void>();

  onCloseClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.closeEvent.emit();
  }
}
