@import 'scss/layout2021/variables';
@import 'scss/figma2023/variables';

::ng-deep {
  .claims-tree {
    .claim-node {

      &.claim-root {
        > .claim > .claim-toggler {
          &:before {
            display: none;
          }
        }

        &.claim-no-children {
          .claim-no-toggler {
            display: none;
          }

          .claim-wrap {
            &:before {
              left: -1.6rem !important;
              top: 0 !important;
              border-left: 1px solid #BCCACE !important;
            }
          }
        }
      }

      &.claim-child.claim-has-children:not(:last-child) {
        position: relative;

        &::before {
          content: "";
          position: absolute;
          border-left: 1px solid #BCCACE;
          left: -4.2rem;
          height: calc(100% + 20px);
          top: -10px;
        }
      }

      .claim-toggler {
        float: left;
        margin-left: -2.6rem;
        font-weight: bold;
        position: relative;

        &.sub-level::before {
          content: '';
          border-bottom: 1px solid #BCCACE;
          border-left: 1px solid #BCCACE;
          position: absolute;
          width: 1.4rem;
          left: -1.6rem;
          top: -1.4rem;
          height: 2.2rem;
        }

        > span {
          margin-top: 5px;
          content: url(/assets/images/minus-square-regular.svg);
          width: 15px;
          height: 15px;
          cursor: pointer;
          float: left;
        }

        &.collapsed > span {
          content: url(/assets/images/plus-square-solid.svg);
        }
      }

      .claim-no-toggler {
        float: left;
        margin-left: -2.8rem;
        font-weight: bold;
        position: relative;

        &::before {
          content: '';
          border-bottom: 1px solid #BCCACE;
          border-left: 1px solid #BCCACE;
          position: absolute;
          width: 1.4rem;
          left: 0.7rem;
          top: -1.5rem;
          height: 2.4rem;
        }

        &~p {
          padding-left: 32px;
        }
      }

      .claim-wrap {
        position: relative;

        &.collapsed {
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        &::before {
          content: "";
          position: absolute;
          border-left: 1px solid #BCCACE;
        }

        .claim-ordering-number {
          font-family: $font-open-sans-bold;
        }
      }

      &.claim-has-children {
        .claim-wrap {
          &::before {
            left: -2.2rem;
            height: calc(100% - 25px);
            top: 19px;
          }
        }
      }

      &.claim-no-children {
        .claim-wrap {
          &::before {
            left: -4.2rem;
            height: 100%;
            top: 14px;
          }
        }

        &:last-child {
          .claim-wrap {
            &::before {
              border-left: none;
            }
          }
        }

        .claim-no-toggler {
          &::before {
            left: -1.4rem;
          }
        }
      }
    }

    ul {
      padding-left: $spacing-system-spacing-x-big;
      display: block;
      list-style-type: none;
    }

    ul.collapsed {
      display: none;
    }

    &.figma-claims-tree {
      .claim-node {
        border-left: 2px solid $colours-border-moderate;
        padding-left: $spacing-system-spacing-md;
        margin-left: -$spacing-system-spacing-big;

        ul {
          padding-left: $spacing-system-spacing-big !important;
        }

        .claim-toggler, .claim-no-toggler {
          display: none;
        }

        &.claim-child, .claim-wrap {
          &::before {
            display: none;
          }
        }
      }
    }
  }

  .no-claims-tree {
    ul {
      list-style-type: none;
      padding-left: 0px;
    }

    .claim-toggler {
      display: none;
    }

    .claim-no-toggler {
      display: none;
    }
  }
}
