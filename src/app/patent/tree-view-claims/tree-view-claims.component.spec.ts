import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';

import { TreeViewClaimsComponent } from './tree-view-claims.component';

describe('TreeViewClaimsComponent', () => {
  let component: TreeViewClaimsComponent;
  let fixture: ComponentFixture<TreeViewClaimsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TreeViewClaimsComponent ],
      imports: [
        HttpClientTestingModule,
        SharedModule,
        RouterModule.forRoot([])
      ]

    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TreeViewClaimsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
