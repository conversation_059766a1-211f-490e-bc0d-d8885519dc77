import { AfterViewInit, Component, ElementRef, Input } from '@angular/core';
import { SmartHighlightItem, TextHighlightService } from '@core';

@Component({
  selector: 'app-tree-view-claims',
  templateUrl: './tree-view-claims.component.html',
  styleUrls: ['./tree-view-claims.component.scss']
})
export class TreeViewClaimsComponent implements AfterViewInit {

  @Input() showClaimsTree = true;
  @Input() smartHighlightItems: SmartHighlightItem[] = [];
  @Input() figmaClass = false;

  constructor(
    private eleRef: ElementRef,
  ) {
  }

  private _claims: string;

  get claims(): string {
    return this._claims;
  }

  @Input() set claims(value: string) {
    this._claims = this.renderTreeClaim(value);
  }

  ngAfterViewInit(): void {
    this.eleRef.nativeElement.removeEventListener('click', () => {
    });
    this.eleRef.nativeElement.addEventListener('click', (evt) => {
      const clickedEle = evt.target as HTMLElement;
      if (clickedEle.classList.contains('claim-toggler') || clickedEle.parentElement.classList.contains('claim-toggler')) {
        const clickedToggleEle = clickedEle.classList.contains('claim-toggler') ? clickedEle : clickedEle.parentElement;
        clickedToggleEle.classList.toggle('collapsed');

        const siblings = Array.from(clickedToggleEle.parentElement.children).filter(ch => ch !== clickedToggleEle);
        siblings.forEach(sbl => {
          sbl.classList.toggle('collapsed');
        });
      }
    }, false);
  }

  private renderTreeClaim(claimsText: string): string {
    const textNode = document.createElement('div');
    textNode.innerHTML = claimsText;

    const claimsContainerEle = textNode.querySelector('.claims');
    if (!claimsContainerEle) {
      return claimsText;
    }

    Array.from(claimsContainerEle.children).forEach((childEle) => {
      childEle.className = 'claim-root claim-node';
      if (childEle.querySelector('ul > li')) {
        childEle.classList.add('claim-has-children');
      } else {
        childEle.classList.add('claim-no-children');
      }
    });

    const claimElements = Array.from(claimsContainerEle.getElementsByClassName('claim'));
    claimElements.forEach((claimEle) => {
      if (claimEle.querySelector('ul') !== null || claimEle.querySelector(':scope > p') !== null) {
        const wrap = document.createElement('div');
        wrap.className = 'claim-wrap';

        Array.from(claimEle.childNodes).forEach((childEle) => {
          if (childEle.textContent?.trim()?.length) {
            if (childEle.nodeName.toLowerCase() !== 'ul') {
              wrap.appendChild(childEle);
            }
          } else {
            childEle.remove();
          }
        });

        Array.from(wrap.children).forEach((childEle) => {
          childEle.innerHTML = childEle.innerHTML
            .replace(/^(\s*\d+)\s+(\d+)\./g, (match, p1, p2) => {
              const combinedNumber = `${p1}${p2}.`;
              return `<span class="claim-ordering-number ${TextHighlightService.HIGHLIGHT_TEXT_NON_SELECT_CSS_CLASS}">${combinedNumber}</span><span class="d-none">${match}</span>`;
            })
            .replace(/^(\s*\d+\.)/g, '<span class="claim-ordering-number">$1</span>');
        });
        claimEle.prepend(wrap);

        Array.from(claimEle.querySelectorAll(':scope > ul > li')).forEach((childEle) => {
          childEle.className = 'claim-child claim-node';
          if (childEle.querySelector('ul > li')) {
            childEle.classList.add('claim-has-children');
          } else {
            childEle.classList.add('claim-no-children');
          }
        });

        const toggleEle = document.createElement('div');

        if (claimEle.getElementsByClassName('claim').length) {
          toggleEle.className = 'claim-toggler';
          const expandCollapse = document.createElement('span');
          if (claimEle.parentElement.className !== 'claim-root') {
            toggleEle.classList.add('sub-level');
          }
          toggleEle.prepend(expandCollapse);
        } else {
          toggleEle.className = 'claim-no-toggler';
        }

        claimEle.prepend(toggleEle);
      }
    });

    return textNode.innerHTML;
  }
}
