export enum SideSectionEnum {
  COMMENTS = 'Comments',
  TASKS = 'Tasks',
  OCTI_AI = 'Octi AI',
  HIGHLIGHTS = 'Highlights',
  LIST = 'Current list',
  RELEVANCE = 'Relevant parts',
  RATINGS = 'Ratings'
}

export enum PatentHighlightActionEnum {
  OCTI_AI = 'octi_ai',
  HIGHLIGHT = 'highlight',
  COMMENTS = 'comments',
  TASKS = 'tasks',
}

export enum ColorsPopperSourceEnum {
  TOP_MENU = 'top-menu',
  HIGHLIGHT_LIST = 'highlight-list',
  POPPER_MENU = 'popper-menu',
}

export enum PatentCommentsSelectedTabEnum {
  GENERAL = 'general',
  SECTION = 'section',
}

export enum PatentCommentsActionSourceEnum {
  CONTROL_BAR = 'control-bar',
  GENERAL_COMMENT = 'general-comment',
  SECTION_COMMENT = 'section-comment',
}

export enum PatentCommentsFilterEnum {
  SEE_ALL = 'See all',
  THIS_PATENT_ONLY = 'This publication only',
  MY_COMMENTS_ONLY = 'My comments only'
}

export enum PatentCommentsSortEnum {
  RECENTLY_ADDED = 'Recently added',
  OLDEST_ADDED = 'Oldest added'
}

export enum SortHighlightsOptionEnum {
  RECENTLY_ADDED = 'Recently added',
  OLDEST_ADDED = 'Oldest added',
  CREATOR = 'Creator',
  COLOR = 'Color',
}

export enum FilterRelevanceOptionEnum {
  ALL = 'All relevant parts',
  ONLY_CLAIMS = 'Only claims',
  ONLY_DESCRIPTION = 'Only description'
}

export enum FilterTableOptionEnum {
  CURRENT_DOCUMENT = 'Current document',
  ALL_FAMILY = 'Entire family',
}

export enum CurrentListFilterEnum {
  ALL = 'All',
  ALIVE = 'Alive',
  UNKNOWN = 'Unknown',
  DEAD = 'Dead',
  DEAD_UNKNOWN = 'Dead Unknown',
}

export enum PatentLegalStatusEnum {
  DEAD = 'Dead',
  GRANTED = 'Granted',
  PENDING = 'Pending',
  UNKNOWN = 'Unknown',
}
