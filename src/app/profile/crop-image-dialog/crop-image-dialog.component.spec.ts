import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CropImageDialogComponent } from './crop-image-dialog.component';
import { NgbActiveModal, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared/shared.module';
import { ProfileRoutingModule } from '@profile/profile-routing.module';
import { ImageCropperComponent } from 'ngx-image-cropper';
import { provideMatomo } from 'ngx-matomo-client';

describe('CropImageDialogComponent', () => {
  let component: CropImageDialogComponent;
  let fixture: ComponentFixture<CropImageDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CropImageDialogComponent],
      providers: [NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
      imports: [
        CommonModule, SharedModule, ProfileRoutingModule, ImageCropperComponent, NgbTooltipModule
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CropImageDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
