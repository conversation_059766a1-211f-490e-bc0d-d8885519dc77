import { Component, OnInit } from '@angular/core';
import { ImageCroppedEvent, ImageTransform, LoadedImage } from 'ngx-image-cropper';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-crop-image-dialog',
  templateUrl: './crop-image-dialog.component.html',
  styleUrls: ['./crop-image-dialog.component.scss']
})
export class CropImageDialogComponent implements OnInit {
  imageFile: Blob = null;
  croppedImage: Blob = null;
  isLoadingImage = false;
  isTransformingImage = false;
  errorMessage: string;
  transform: ImageTransform = {};
  resizeToDim = 512;

  private zoomValue = 1;
  private zoomStep = 0.2;
  private maxZoomValue = 10;
  private rotate = 0;
  private finalDim = 150;
  private isFinalResize = false;

  constructor(
    public activeModal: NgbActiveModal,
  ) {
  }

  imageCropped(event: ImageCroppedEvent) {
    this.croppedImage = event.blob;
    this.isTransformingImage = false;
        this.isLoadingImage = false;

        this.croppedImage = event.blob;
        this.errorMessage = null;

        if (this.isFinalResize) {
          this.activeModal.close(event.blob);
        }
  }

  imageLoaded(event: LoadedImage) {
    this.isTransformingImage = false;
    this.isLoadingImage = false;
  }

  cropperReady() {

  }

  loadImageFailed() {
    this.croppedImage = null;
  }

  ngOnInit(): void {
    this.isLoadingImage = true;
  }

  onSaveClicked() {
    this.isFinalResize = true;
    this.imageFile = this.croppedImage;
    this.resizeToDim = this.finalDim;
  }

  onRotateLeftClicked(btn: HTMLButtonElement) {
    btn.blur();

    this.isTransformingImage = true;
    this.rotate = (this.transform.rotate || 0) - 90;
    this.transform = {...this.transform, rotate: this.rotate};
  }

  onRotateRightClicked(btn: HTMLButtonElement) {
    btn.blur();

    this.isTransformingImage = true;
    this.rotate = (this.transform.rotate || 0) + 90;
    this.transform = {...this.transform, rotate: this.rotate};
  }

  onZoomOutClicked(btn: HTMLButtonElement) {
    btn.blur();

    if (this.canZoomOut()) {
      this.zoomValue -= this.zoomStep;
      this.transform = {...this.transform, scale: this.zoomValue, rotate: this.rotate};
    }
  }

  canZoomOut(): boolean {
    return this.zoomValue - this.zoomStep > this.zoomStep;
  }

  onZoomInClicked(btn: HTMLButtonElement) {
    btn.blur();

    if (this.zoomValue < this.maxZoomValue) {
      this.zoomValue += this.zoomStep;
      this.transform = {...this.transform, scale: this.zoomValue, rotate: this.rotate};
    }
  }

  canZoomIn(): boolean {
    return this.zoomValue < this.maxZoomValue;
  }

  onCropClicked(btn: HTMLButtonElement) {
    btn.blur();
    this.imageFile = this.croppedImage;
  }
}
