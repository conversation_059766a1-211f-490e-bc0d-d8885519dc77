import { first } from 'rxjs/operators';
import { Component, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, ValidatorFn, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CropImageDialogComponent } from '@profile/crop-image-dialog/crop-image-dialog.component';
import { tooltip } from './tooltip';
import { COUNTRIES, UserProfile, UserStatistics, UserSubscription } from '@core/models';
import { AutocompleteComponent } from '@shared/components';
import { AuthorityService, ChangePasswordRequest, ConfirmationDialogService, UserService, IpLoungeService } from '@core/services';
import { PhotosBrowserData } from '@core/directives';
import { Locale, locales } from '@core';
import { Subscription } from 'rxjs';
import _ from 'lodash';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  public openedTab = 'profile-info';

  editMode = false;
  preferencesModified = false;
  user: UserProfile;
  subscription: UserSubscription;
  statistics: UserStatistics = new UserStatistics();
  countries = null;
  passwordForm: UntypedFormGroup;
  passwordErrors = [];
  passwordChanged = false;
  personalInformationChanged = false;
  personalInformationErrors = [];
  changeAvatarErrors = [];
  preferencesChanged = false;
  preferencesErrors = [];

  showGdpr = false;
  savedFilters = [];
  defaultFilters = {
    authorities_plus: '',
    authorities_minus: 'CN,KR,JP',
  };

  authorities_plus = '';
  authorities_minus = 'CN';
  tooltipTitle = tooltip.title;
  tooltipText = tooltip.text;
  locale: Locale;
  listLocales = locales;

  labelFilters = {
    quantity_cut_off: 'Quantity Cut-Off',
    similarity_cut_off: 'Similarity Cut-Off',
    inclusion_and: 'Inclusion (AND)',
    inclusion_or: 'Inclusion (OR)',
    exclusion_and: 'Exclusion (AND)',
    exclusion_or: 'Exclusion (OR)',
    applicants_plus: 'Applicant (+)',
    applicants_minus: 'Applicant (-)',
    cpc_plus: 'CPC Code (+)',
    cpc_minus: 'CPC Code (-)',
    ipc_plus: 'IPC Code (+)',
    ipc_minus: 'IPC Code (-)',
    earliest_priority_date: 'Earliest Priority Date',
    latest_priority_date: 'Latest Priority Date',
    earliest_publication_date: 'Earliest Publication Date',
    latest_publication_date: 'Latest Publication Date',
    authorities_plus: 'Authority (+)',
    authorities_minus: 'Authority (-)',
    ma1: 'Electrical Engineering',
    ma2: 'Instruments',
    ma3: 'Chemistry',
    ma4: 'Mechanical Engineering',
    ma5: 'Other Fields',
  };

  userAvatarBlob: Blob = null;
  refreshAvatar = false;
  hasAvatar = false;
  twoFactorAuthenticationErrors = [];
  twoFactorAuthenticationSuccess = [];

  @ViewChild('autoCompleteAuthoritiesPlus') autoCompleteAuthoritiesPlus: AutocompleteComponent;
  @ViewChild('autoCompleteAuthoritiesMinus') autoCompleteAuthoritiesMinus: AutocompleteComponent;

  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    private router: Router,
    private modalService: NgbModal,
    private confirmationDialogService: ConfirmationDialogService,
    private ipLoungeService: IpLoungeService
  ) {
  }

  get pfc() {
    return this.passwordForm.controls;
  }

  get enabled2FA(): boolean {
    return this.user.two_factor_authentication_enabled;
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: u => {
        if (u && u.profile) {
          this.user = _.cloneDeep(u.profile);
          this.subscription = u.subscription;
          this.loadGdpr(this.user.country);
          this.loadSavedFilters();
          this.loadDefaultFilters();
          this.loadLocale();
        }
      }
    });
    this.subscriptions.add(user$);

    const getStatistics$ = this.userService.getStatistics()
      .subscribe({
        next: (s) => {
          this.statistics = s;
        }
      });
    this.subscriptions.add(getStatistics$);

    this.countries = COUNTRIES;
    this.buildPasswordForm();
  }

  loadGdpr(country) {
    if (country) {
      for (const [key, value] of Object.entries(COUNTRIES)) {
        const conObj = value.find(x => x.name === country);
        if (conObj) {
          this.showGdpr = conObj.gdpr;
          break;
        }
      }
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onSwitchTabs(event) {
    // Reset previous to initial state when switching between tabs
    switch (this.openedTab) {
      case 'profile-info':
        this.cancelProfile();
        break;
      case 'profile-preferences':
        if (this.preferencesModified) {
          this.cancelPreferences();
        }
        break;
      case 'profile-account':
        this.resetPasswordForm();
        break;
      default:
        break;
    }
  }

  switchMode() {
    if (this.editMode) {
      this.cancelProfile();
    } else {
      this.editMode = true;
    }
  }

  saveProfile() {
    this.personalInformationChanged = false;
    this.personalInformationErrors = [];
    const updateProfile$ = this.userService.updateProfile(this.user)
      .subscribe({
        next: () => {
          const user = this.userService.getUser();
          user.profile = this.user;
          this.userService.setUser(user);
          this.editMode = false;
          this.personalInformationChanged = true;
        },
        error: (error)=> {
          console.log(error);
          const {message} = error;
          if (/Validation error\:/ig.test(message)) {
            this.personalInformationErrors = [message.replace('Validation error: ', '')];
          } else {
            this.personalInformationErrors = [message];
          }
        }
      });
    this.subscriptions.add(updateProfile$);
  }

  cancelProfile() {
    this.user = _.cloneDeep(this.userService.getUser().profile);
    this.editMode = false;
  }

  modify() {
    this.preferencesModified = true;
  }

  savePreferences() {
    this.preferencesChanged = false;
    this.preferencesErrors = [];

    this.setSearchFilterToSave();
    this.user.ui_settings['default_filters'] = this.defaultFilters;
    this.user.locale = this.locale?.language;

    const updateProfile$ = this.userService.updateProfile(this.user)
      .subscribe({
        next: () => {
          const user = this.userService.getUser();
          user.profile = this.user;
          this.userService.setUser(user);
          this.userService.refreshToken();
          this.editMode = false;
          this.preferencesModified = false;
          this.preferencesChanged = true;
        },
        error: (err) => {
          console.log(err);
          const {message} = err;
          if (/Validation error\:/ig.test(message)) {
            this.preferencesErrors = [message.replace('Validation error: ', '')];
          } else {
            this.preferencesErrors = [message];
          }
        }
      });
    this.subscriptions.add(updateProfile$);
  }

  cancelPreferences() {
    this.user = _.cloneDeep(this.userService.getUser().profile);
    this.preferencesModified = false;
    this.loadSavedFilters();
    this.loadDefaultFilters();
  }

  validate(field: string) {
    const control = this.passwordForm.get(field);
    return (!control.valid && control.touched);
  }

  changePassword() {
    this.passwordErrors = [];
    Object.keys(this.passwordForm.controls).forEach(key => {
      this.passwordForm.controls[key].updateValueAndValidity();
      this.passwordForm.get(key).markAsTouched();
    });
    if (this.passwordForm.get('new_password').value !== this.passwordForm.get('new_password2').value) {
      this.passwordErrors.push('Passwords do not match');
      return;
    }
    if (this.passwordForm.valid) {
      const payload: ChangePasswordRequest = {
        email: this.user.email,
        terms_and_conditions: this.user.terms_and_conditions,
        gdpr: this.user.gdpr,
        current_password: this.passwordForm.get('current_password').value,
        new_password: this.passwordForm.get('new_password').value,
      };
      const changePassword$ = this.userService.changePassword(payload)
        .pipe(first())
        .subscribe({
          next: () => {
            const signin$ = this.userService.signin({
              username: this.user.email,
              password: this.passwordForm.get('new_password').value
            }).subscribe({
              next: () => { },
              error: (error) => console.log(error)
            });
            this.subscriptions.add(signin$);

            this.resetPasswordForm();
            this.passwordChanged = true;
          },
          error: ({error})=> {
            this.passwordErrors = [];
            const {message} = error;
            if (/Validation error\:/ig.test(message)) {
              this.passwordErrors = [message.replace('Validation error: ', '')];
            } else if (message === 'Invalid username and password combination') {
              this.passwordErrors = ['Please enter correct current password'];
            } else {
              this.passwordErrors = [message];
            }
            console.log(this.passwordErrors);
          }
        });
      this.subscriptions.add(changePassword$);
    }
  }

  deleteAccount() {
    if (confirm('Are you sure you want to delete your account?\n' +
      'All your stored data will be lost, with no way to restore them later!')) {
      const deleteAccount$ = this.userService.deleteAccount()
        .pipe(first())
        .subscribe({
          next: () => this.router.navigate(['auth/login'])
        });
      this.subscriptions.add(deleteAccount$);
    }
  }

  loadSavedFilters() {
    this.savedFilters = [];
    const filters = this.user.ui_settings['search_filters'];
    if (!filters) {
      return;
    }
    Object.keys(filters).forEach(key => {
      if ((filters[key] && key.indexOf('ma') === -1) || (!filters[key] && key.indexOf('ma') > -1)) {
        if (this.labelFilters[key]) {
          this.savedFilters.push({
            label: this.labelFilters[key],
            key: key,
            value: Array.isArray(filters[key]) ? filters[key].join(', '): filters[key]
          })
        }
      }
    });
  }

  updateDefaultFilters(event, field) {
    this.defaultFilters[field] = event;
    if (this.defaultFilters === this.user.ui_settings['default_filters']) {
      this.preferencesModified = false;
      return;
    }
    this.preferencesModified = true;
  }

  removeFilter(index) {
    this.savedFilters.splice(index, 1);
    this.preferencesModified = true;
  }

  setSearchFilterToSave() {
    if (!this.savedFilters.length) {
      delete this.user.ui_settings['search_filters'];
      return;
    }

    const searchFilters = {};
    this.savedFilters.forEach(item => {
      searchFilters[item.key] = item.value;
    });
    this.user.ui_settings['search_filters'] = searchFilters;
  }

  removeAvatarClicked() {
    this.changeAvatarErrors = [];
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> your avatar image ?</div>`;

    const confirmRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'md');
    confirmRef.then((val) => {
      if (val) {
        this.showAvatarLoading();
        const deleteAvatar$ = this.userService.deleteAvatar()
          .subscribe({
            next: (resp) => {
              this.userAvatarBlob = null;
            },
            error: ({error}) => {
              this.userAvatarBlob = null;
              this.refreshAvatar = true;
              this.changeAvatarErrors.push('Error when deleting avatar. ' + error?.message);
            }
          });
        this.subscriptions.add(deleteAvatar$);
      }
    }, error => {
    });
  }

  onPhotosSelected(event: PhotosBrowserData) {
    this.changeAvatarErrors = [];

    const file = event.selectedFiles[0];

    const modalRef = this.modalService.open(CropImageDialogComponent, {size: 'md'});
    modalRef.componentInstance.imageFile = file.data;
    modalRef.result.then((blob: Blob) => {
      if (blob) {
        this.saveAvatar(blob);
      } else {
        this.changeAvatarErrors.push('Error in processing the image. Please try again.');
      }
    }, (error) => {
    });
  }

  getAuthorityCodes(codes: string | Array<string>): Array<{ value: string, label: string }> {
    if (typeof (codes) === 'string') {
      codes = codes.split(',');
    }

    return AuthorityService.filterAuthoritiesByCodes(codes).map((value) => {
      return {value: value.id, label: `${value.id}-${value.name}`};
    });
  }

  authoritiesAutocompleteFunc: (term: string) => Array<{ value: string, label: string }> = (term) => {
    return AuthorityService.filterAuthorities(term).map((value) => {
      return {value: value.id, label: `${value.id}-${value.name}`};
    });
  }

  private loadDefaultFilters() {
    if (!this.user.ui_settings['default_filters']) {
      this.preferencesModified = true;
      return;
    }
    this.defaultFilters = Object.assign({}, this.user.ui_settings['default_filters']);
  }

  private showAvatarLoading() {
    fetch('/assets/images/octimine_blue_spinner.gif')
      .then(res => res.blob())
      .then(blob => {
        this.userAvatarBlob = blob;
      });
  }

  private saveAvatar(blob: Blob) {
    this.showAvatarLoading();

    const saveAvatar$ = this.userService.saveAvatar(this.user.id, blob)
      .subscribe({
        next: (val) => {
          this.userAvatarBlob = blob;
        },
        error: ({error}) => {
          this.userAvatarBlob = null;
          this.refreshAvatar = true;
          this.changeAvatarErrors.push(error.message);
        }
      });
    this.subscriptions.add(saveAvatar$);
  }

  private buildPasswordForm(): void {
    const PasswordMatchValidator: ValidatorFn = (fg: UntypedFormGroup) => {
      const newPassword = fg.get('new_password').value;
      const repeatPassword = fg.get('new_password2').value;
      return newPassword === repeatPassword ? null : {mismatch: true};
    };
    this.passwordForm = new UntypedFormGroup({
      current_password: new UntypedFormControl('', [Validators.required]),
      new_password: new UntypedFormControl('', [Validators.required]),
      new_password2: new UntypedFormControl('', [Validators.required]),
      code2FA: new UntypedFormControl('', [Validators.minLength(6), Validators.maxLength(6)])
    }, {
      validators: [PasswordMatchValidator]
    });
  }

  private resetPasswordForm(): void {
    this.passwordErrors = [];
    Object.keys(this.passwordForm.controls).forEach(key => {
      this.passwordForm.controls[key].setValue('');
      this.passwordForm.controls[key].setErrors(null);
    });
    this.passwordChanged = false;
  }

  get isProfileReadonly(): boolean {
    return this.ipLoungeService.hasIPLoungeAccess();
  }

  get isDelegatedUser(): boolean {
    return this.userService.isDelegatedUser();
  }

  enable2FA(): void {
    this.twoFactorAuthenticationSuccess = [];
    const enable2FA$ = this.userService.enable2FA()
      .subscribe({
        next: result => {
          if (result['status'] && result['status'] !== 200) {
            this.twoFactorAuthenticationSuccess.push(result['message']);
            return;
          }

          this.saveQrCode(result['totp_uri']);
        },
        error: ({error}) => {
          this.twoFactorAuthenticationErrors.push(error.message);
        }
      });
    this.subscriptions.add(enable2FA$);
  }

  private validateCode2FA(): boolean {
    const codeControl = this.passwordForm.get('code2FA');
    if (!codeControl.value || this.validate('code2FA')) {
      codeControl.markAsTouched();
      codeControl.setErrors({'incorrect': true});
      return false;
    }

    return true;
  }

  activate2FA(): void {
    this.twoFactorAuthenticationErrors = [];

    if (!this.validateCode2FA()) {
      return;
    }

    const payload = {
      totp_uri: this.user.ui_settings['uri_qr_code_2FA'],
      verification_code: this.passwordForm.get('code2FA').value
    };
    const activate2FA$ = this.userService.activate2FA(payload).subscribe({
      next: result => {
        if (result['status'] && result['status'] !== 201) {
          console.log(result['message']);
          return;
        }

        this.twoFactorAuthenticationSuccess.push(result['message']);
        this.user.two_factor_authentication_enabled = true;
      },
      error: ({error})=> {
        this.twoFactorAuthenticationErrors.push(error.message);
      }
    });
    this.subscriptions.add(activate2FA$);
  }

  disable2FA(): void {
    this.twoFactorAuthenticationErrors = [];

    if (!this.validateCode2FA()) {
      return;
    }

    const disable2FA$ = this.userService.disable2FA(this.passwordForm.get('code2FA').value)
      .subscribe({
        next: result => {
          if (result['status'] && result['status'] !== 200) {
            console.log(result['message']);
            return;
          }
          this.twoFactorAuthenticationSuccess.push(result['message']);
          this.user.two_factor_authentication_enabled = false;
          this.saveQrCode('');
        },
        error: ({error}) => {
          this.twoFactorAuthenticationErrors.push(error.message);
        }
      });
    this.subscriptions.add(disable2FA$);
  }

  saveQrCode(uriQrCode: string) {
    const updateUISettings$ = this.userService.updateUISettings({uri_qr_code_2FA: uriQrCode})
      .subscribe({
        next: (data) => {
          this.user.ui_settings = data.profile.ui_settings;
        },
        error: error => {
          console.error(error);
        }
      });
    this.subscriptions.add(updateUISettings$);
  }

  private loadLocale(): void {
    if (this.user.locale) {
      this.locale = this.listLocales.find(lc => lc.language === this.user.locale);
    } else {
      this.locale = this.listLocales.find(lc => lc.language === navigator.language);
    }
  }

}
