export const tooltip = {
  title: {
    authoritiesPlus: 'Authority (+)',
    authoritiesMinus: 'Authority (-)'
  },

  text: {
    authoritiesPlus: `
      Type in one or more Authorities which are relevant for your search.
      Patent families without a member in your specified Authority(/ies) are excluded.
      Authorities will be automatically suggested when you start typing the Authority name or abbreviation.
      By clicking on the authority or hitting “enter”, you confirm the selected Authority to be filtered for.
      To add another, simply start typing again once you confirmed the previous selection.`,

    authoritiesMinus: `
      Type in one or more Authorities which you want to exclude from your search.
      Patent families where only a member lies in your specified Authority(/ies) are still included.
      Authorities will be automatically suggested when you start typing the Authority name or abbreviation.
      By clicking on the authority or hitting “enter”, you confirm the selected Authority to be filtered for.
      To add another, simply start typing again once you confirmed the previous selection.`,
  }
};
