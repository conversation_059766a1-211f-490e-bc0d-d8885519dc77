export const columnsToShow = [
  {
    property: 'legal_status',
    label: 'Legal status',
    default: true
  }, {
    property: 'ipc4',
    label: 'IPC 4',
    default: true
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'owners',
    label: 'Owners',
    default: false
  }, {
    property: 'ultimate_owners',
    label: 'Ultimate owners',
    default: false
  }, {
    property: 'priority_date',
    label: 'Priority date',
    default: true
  }, {
    property: 'risk',
    label: 'Risk',
    default: false
  }, {
    property: 'impact',
    label: 'Patent Value',
    default: false
  }, {
    property: 'similarity_index',
    label: 'Similarity Index',
    default: true
  }, {
    property: 'ratings',
    label: 'Ratings',
    default: false,
    requireCompany: true
  }
];

export const columnsToShowFocal = [
  {
    property: 'ipc4',
    label: 'IPC 4',
    default: true
  }, {
    property: 'applicants',
    label: 'Applicants',
    default: true
  }, {
    property: 'publication_date',
    label: 'Publication date',
    default: true
  },
];

export const selectedColumnsCombinedMode = ['legal_status', 'applicants'];

