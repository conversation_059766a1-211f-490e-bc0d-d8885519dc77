<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header #header></app-header>
  <app-search-menu></app-search-menu>

  <div class="flex-fill">
    <div class="page-content container patent-search-form-container pb-4" [@fadeIn]>
      <app-alert type="warning" [message]="messageCollaboratorQuotas" [headline]="'Collaborator’s user rights'" version="figma" *ngIf="messageCollaboratorQuotas"></app-alert>
      <div class="d-flex justify-content-between align-items-center">
        <app-search-title headline="Semantic search" [hideIcon]="true">
          <app-tooltip id='patent_search'
                      [tooltipTitle]='isSemanticSearch ? tooltipTitle.patentSearch : tooltipNpl.title'
                      [tooltipText]='isSemanticSearch ? tooltipText.patentSearch : tooltipNpl.text'
                      tooltipPosition="right" tooltipIconSize="sm"></app-tooltip>
        </app-search-title>
        <div class="pe-1 d-flex gap-spacing-x-s">
          <button id="button-boost" class="button-boost button-main-secondary button-medium button-pill" (click)="openBoostSearch()" data-intercom-target="boost-button">
            <div *ngIf="amountBoostFactors > 0 && !isBoostOpen else iconBoost" class="number-filters me-2">{{amountBoostFactors}}</div>
            <ng-template #iconBoost>
              <i class="fa-regular fa-rocket"></i>
            </ng-template>
            Boost
          </button>
          <button id="button-filters" *ngIf="!userService.isFreeUser()" class="button-boost button-main-tertiary-grey button-medium button-pill" (click)="openFilters()" [ngClass]="{'has-errors': isFiltersFormGroupInvalid}">
            <div *ngIf="amountSearchFilters > 0 else iconFilter" class="number-filters me-2">{{amountSearchFilters}}</div>
            <ng-template #iconFilter>
              <i class="fa-regular fa-filter"></i>
            </ng-template>
            Filters
          </button>
        </div>
      </div>
      <app-alert type="danger" [message]="messageAlert" *ngIf="messageAlert" [hideCloseBtn]="true"></app-alert>

      <app-alert type="warning" *ngIf="userService.isExternalUser()"
                 message="You are viewing results of a shared search. <a href='/auth/signup'>Create an account</a> to execute your own searches, analyse results and more.">
      </app-alert>

      <div class="alert alert-danger" role="alert" *ngIf="exceededQuotasMessage">
        <p>{{exceededQuotasMessage[0]}} <a href="javscript:void(0)" (click)="openUpgradeForm()">{{exceededQuotasMessage[1]}}</a> {{exceededQuotasMessage[2]}}</p>
      </div>
      <form id="search-form" [formGroup]="form" (ngSubmit)="onSubmit()">
        <app-boost-search [form]="form" *ngIf="isBoostOpen"></app-boost-search>

        <app-semantic-input #semanticInput [form]="form"
                            [searchStarting]="searchStarting()"
                            [textWeighting]="textWeighting"
                            [storeService]="semanticSearchStoreService"
                            [(selectedLanguage)]="selectedLanguage"
                            (selectedLanguageChanged)="onSelectedLanguageChanged($event)"
                            [(detectedLanguage)]="detectedLanguage"
                            (detectedLanguageChanged)="onDetectedLanguageChanged($event)"
                            (translationConfirmed)="onTranslationConfirmed($event)"
                            (detectingLanguage)="isDetectingLanguage = $event"
                            (weightingChange)="textWeighting = $event"
                            [placeholder]="searchInputHolder"
                            (submitInput)="onSubmit()">
        </app-semantic-input>

        <div class="d-flex justify-content-end align-items-center search-hint-container">
          <button id="search-button" type="submit" data-intercom-target="search-button" [disabled]="!form.valid || searchStarting() || isCollaboratorQuotasExceeded"
            ngbTooltip="You can also press Ctrl + Enter to begin the search."
            class="button-main-primary button-large content-label-large" *ngIf="userService.isNotExternalUser()">Search
          </button>

          <a class="btn btn-secondary btn-xl" routerLink="/auth/signup" (click)="onSignupClicked()" *ngIf="userService.isExternalUser()">
            Sign up for free
          </a>
        </div>
      </form>

      <div class="mt-3" *ngIf="messageAdvice || messageAdviceAlert">
        <app-alert type="warning" headline="Important advice:" [message]="messageAdvice" *ngIf="messageAdvice"></app-alert>
        <app-alert type="danger" headline="Important advice:" [message]="messageAdviceAlert" *ngIf="messageAdviceAlert"></app-alert>
      </div>
    </div>

    <div *ngIf="!searching else loader">
      <div class="page-content pt-4 pb-0 container" *ngIf="semanticSearchStoreService.chartFilterDisabled$ | async">
        <app-alert type="warning" headline="Important advice:"
                  message="You're seeing the results of a previous <strong>saved search</strong>. You <strong>can't use the charts as filter</strong> in this modus.">
        </app-alert>
      </div>

      <ng-container [ngTemplateOutlet]="focalPatents"></ng-container>

      <app-patent-list-layout [storeService]="semanticSearchStoreService"
                              [documents]="searchService.documents | async"
                              [isLoading]="searching"
                              [showDashboardActionBar]="isSemanticSearch"
                              (viewModeChange)="onChangeViewMode($event)">
        <ng-container customTabs [ngTemplateOutlet]="customTabs"></ng-container>

        <ng-container documentsControlBar [ngTemplateOutlet]="semanticControlBar"></ng-container>

        <ng-container documentsTable *ngIf="isNPLSearch; then nplList; else semanticList"></ng-container>

        <ng-container documentsVisual>
          <app-charts-container id="charts" *ngIf="isSemanticSearch && (searchService.documents | async).length" [isColumnLayout]="isCombinedMode" [@fadeIn]
                                [storeService]="semanticSearchStoreService">
          </app-charts-container>
          <app-npl-charts id="charts" *ngIf="isNPLSearch" [externalSearchPayload]="getNPLPayload()"
                          [isColumnLayout]="true">
          </app-npl-charts>
        </ng-container>
        <ng-container alertMessages *ngIf="!(searchService.documents | async).length && this.hasExtraFilters(semanticSearchStoreService.filters)">
          <div appNoResults content="It seems we can’t find any result based on your selection.<br>Try to clear or change your filters."></div>
        </ng-container>
      </app-patent-list-layout>

      <app-filters-bar [alwaysBeSticky]="true" [storeService]="semanticSearchStoreService" (clearAll)="clearAllFilters()"></app-filters-bar>
    </div>
  </div>
  <app-footer></app-footer>
</div>

<ng-template #loader>
  <app-spinner class="py-5 d-block"></app-spinner>
</ng-template>

<ng-template #customTabs>
  <ul ngbNav [(activeId)]="selectedSearch" class="nav-tabs" (navChange)="onCustomTabChanged($event.nextId)" data-intercom-target="custom-tabs">
    <li [ngbNavItem]="SearchTypeEnum.SEMANTIC_SEARCH" class="p-0">
      <a ngbNavLink class="no-icon">Patent</a>
    </li>
    <li [ngbNavItem]="SearchTypeEnum.NPL_SEARCH" class="p-0">
      <a ngbNavLink class="no-icon" appBeta>Non-patent literature</a>
      <ng-template ngbNavContent></ng-template>
    </li>
  </ul>
</ng-template>
<ng-template #focalPatents>
  <div class="page-content queried-patent-container" [@fadeIn] id="queried-patent-container" *ngIf="!!this.viewMode"
       [ngClass]="isCombinedMode ? 'combined-mode-layout': 'container-fluid'">
    <ng-container *ngIf="!isSearchingFocal else loader">
      <div class="search-results-container" *ngIf="(patentNumberService.documents | async).length" [@fadeInOut]>
        <div class="new-layout-headline">
          Patent{{(patentNumberService.documents | async).length > 1 ? 's' : ''}} found in your search input
        </div>
        <div class="position-relative">
          <app-focal-table [columnsToShow]="columnsToShowFocal" [patents]="patentNumberService.documents | async"
                           [linkData]="linkData" pathUrl="/patent"
                           [storeService]="semanticSearchStoreService">
          </app-focal-table>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #semanticControlBar>
  <div class="psr-control-bar sticky-top" *ngIf="isSemanticSearch && ((searchService.documents | async).length)">
    <div class="container-fluid d-flex justify-content-between align-items-center" data-intercom-target="control-bar">
      <app-patent-control-bar [columnsToShow]="columnsToShow"
                              [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                              [searchService]="searchService"
                              [hasHarmonizeControl]="true"
                              [hasTemporaryLinkControl]="true"
                              [hasExportControl]="true" [hasSaveToCollectionControl]="true"
                              (export)="export($event)" [saveSearchTextInput]="form.value.term"
                              [hasFilterListControl]="!userService.isFreeUser()" (filterListEvent)="onAdvancedFilter($event)"
                              [hasAddToSearchControl]="true"
                              [hasOctiAIControl]="true"
                              [hasMonitor]="isMonitorOwner"
                              [taskShowCreationButton]="true"
                              [storeService]="semanticSearchStoreService"
                              (taskSaved)="onTaskSaved($event)"
                              (monitor)="onSetupAlert()">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #semanticList>
  <div class="results-container" id="search-results-container" [@fadeIn] *ngIf="!searchStarting()">
    <div class="search-results-container" *ngIf="(searchService.documents | async).length" [@fadeInOut]
         [ngClass]="{'container-fluid': !isCombinedMode, 'pe-0': isCombinedMode}">
      <div class="d-flex justify-content-between align-items-center">
        <div class="psr-results-title new-layout-headline">
          Showing
          <span
          [hidden]="!totalSelectedPatents"
          class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{ pagination.total_hits }}
          most similar
          {{ 'patent family' | pluralize: pagination?.total_hits }}
          ({{pagination?.total_publications}} {{ 'publication' | pluralize: pagination?.total_publications }})
        </div>
        <div class="tools-bar expand-all">
          <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()">
            {{ patentTable.openedPatent.length === searchService.getDocuments().length ? 'Collapse all' : 'Expand all'}}
            <i class="fas fa-angle-double-down"
               *ngIf="patentTable.openedPatent.length < searchService.getDocuments().length"></i>
            <i class="fas fa-angle-double-up"
               *ngIf="patentTable.openedPatent.length === searchService.getDocuments().length"></i>
          </a>
        </div>

      </div>

      <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()"
                 [message]="collectionsStoreService.getSaveToCollectionSuccess()"></app-alert>

      <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages"></app-alert>

      <div class="psr-patent-table">
        <app-patent-table #patentTable [patents]="searchService.documents | async" [pagination]="pagination"
                          (sort)="onSort($event)" [hasLinksToBooleanSearch]="true" [showAddToSearchButton]="true"
                          [linkData]="linkData" pathUrl="/patent" backButtonTitle="Back to search"
                          [storeService]="semanticSearchStoreService"
                          [showSmartHighlight]="true" [showHighlight]="true">
        </app-patent-table>
      </div>

      <ng-container [ngTemplateOutlet]="semanticFooter"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #nplList>
  <div [ngClass]="{'container-fluid': !isCombinedMode, 'pe-0': isCombinedMode}">
    <app-spinner *ngIf="semanticSearchStoreService.searching" class="py-5 d-block"></app-spinner>

    <div class="results-container" *ngIf="!semanticSearchStoreService.searching && NPLDocuments && NPLPagination" >
      <div class="d-flex justify-content-between align-items-center">
        <div class="nplser-results-title new-layout-headline">[{{NPLPagination?.total_hits}}] Most similar scientific articles
        </div>
        <div class="tools-bar expand-all">
          <a href="javascript:void(0)" class="item-bar" (click)="NPLExpandAllRows()">
            {{ this.nplService.expandedRows.length === NPLDocuments.length ? 'Collapse all': 'Expand all'}}
            <i class="fas fa-angle-double-up" *ngIf="this.nplService.expandedRows.length < NPLDocuments.length"></i>
            <i class="fas fa-angle-double-down" *ngIf="this.nplService.expandedRows.length === NPLDocuments.length"></i>
        </a>
        </div>
      </div>
      <div class="psr-patent-table overflow-x-auto">
        <app-npl-table [documents]="NPLDocuments" [pagination]="NPLPagination" idPagination="npl-search-pagination">
        </app-npl-table>
      </div>

      <ng-container [ngTemplateOutlet]="nplFooter"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #semanticFooter>
  <div class="py-4">
    <div class="d-flex justify-content-end align-items-center psr-pagination-controls"
         [hidden]="!(searchService.documents | async).length || semanticSearchStoreService.searching">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" class="me-3"></app-page-size>
      <pagination-controls id="search-pagination"
                           (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
      </pagination-controls>
    </div>
  </div>
</ng-template>

<ng-template #nplFooter>
  <div class="py-4">
    <div class="d-flex justify-content-end align-items-center psr-pagination-controls"
         *ngIf="!semanticSearchStoreService.searching">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangeNPLPageSize($event)" class="me-3"></app-page-size>
      <pagination-controls id="npl-search-pagination"
                           (pageChange)="onNPLPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
      </pagination-controls>
    </div>
  </div>
</ng-template>
