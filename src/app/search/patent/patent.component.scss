@import 'scss/figma2023/variables';
@import 'scss/layout2021/variables';

::ng-deep {
  .patent-search-form-container {

    .psf-conjunction-label {
      font-size: 1rem;
      color: #4B5D66;
    }

    .psf-filters-link {
      color: #389A85;
      font-family: $font-open-sans-bold;
      font-size: 14px;
      cursor: pointer;

    }
    .psf-filters-link.disabled {
      color: #738993 !important;
      cursor: default;
      text-decoration: none;
    }
    .psf-filter-container {
      .psf-filter-group {
        padding-right: 3.125rem;

        .psf-filter-title {
          color: #0F2C35;
          font-family: $font-open-sans-bold;
          font-size: 16px;
          padding-bottom: 15px;
        }

        .psf-filter-field-label {
          color: #698A95;
          font-family: $font-open-sans-regular;
          font-size: 14px;
          padding-bottom: 0;
          margin-bottom: 0;
        }
      }
    }

    .search-hint {
      color: #698A95;
      font-size: 12px;
    }
  }

  .autocomplete-filters {
    max-width: 100%;
    .ng-value {
      max-width: 87%;
    }
  }

  .psf-source-language {
    width: 200px;
  }
}

.queried-patent-container {
  margin-top: 0;
  margin-bottom: 30px;
}

.search-results-container {
  .psr-queried-patents-title{
    font-family: $font-open-sans-light;
    font-size: 2rem;
    line-height: 2rem;
    color: #0F2C35;
  }

  .psr-queried-patents-title {
    margin-bottom: 30px;
  }

  .psr-pagination-controls {
    height: 40px;
  }
}

.invalid-message {
  color: #a94442;
  margin-top: -12px;
  margin-bottom: 16px;
}

.date-filter {
  margin-top: 13px;
}

.cursor-pointer {
  cursor: pointer !important;
}

a.disable{
  pointer-events: none;
  color: $disabled-color !important;
  cursor: auto;
}

.w-60 {
  min-width: 60%;
}

.psf-filter-container {
  .dropdown-icon {
    font-weight: 900;
    font-family: "Font Awesome 6 Pro";
    color: #C5C5C5;
    transition: all .2s;
    position: absolute;
    width: 30px;
    background-color: transparent;
    right: 2px;
    top: 2px;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 4px 4px 0;
    border: 0;

    &:active {
      background-color: transparent;
    }

    &.more-icon {
      color: #00A083;
      cursor: pointer;
    }

    &::after {
      content: '' !important;
    }
  }
}

.feedback-bar {
  border-left: 1px solid;
  height: 201.99px;
  width: 5px;
  border-radius: 8px;
}

.expand-all {
  background: transparent;
  margin-top: 10px;
}

.semanticscholar {
  color: #698A95;
}

.button-boost {
  display: flex;
  align-items: center;
  gap: $spacing-system-spacing-sm;
  font-size: 14px !important;
  font-weight: 600 !important;
  &.button-pill {
    border-radius: $radius-xxx-huge;
  }
}

.btn-ghost {
  font-family: $font-open-sans-regular;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.25rem;
  letter-spacing: 0em;
  border-radius: 20px;
  padding: 8px 15px 8px 15px;
  color: #00A085;
  border: 1px solid transparent;

  &.btn-boost {
    border-color: #00A085;
    &.active {
      background-color: $brand-green !important;
      color: $color-text-01;
    }
  }
  &.btn-filters {
    color: $color-text-03;
    &:hover {
      color: $brand-green;
    }
    &.has-errors {
      .number-filters {
        background: $invalid-color;
        color: $color-text-01;
      }
    }
  }
}

.button-master {
  border-radius: 4px;
}

.number-filters {
  border-radius: 20px;
  background: rgba(0, 160, 131, 0.2);
  font-size: 12px;
  font-weight: 400;
  line-height: 21px;
  width: 20px;
  height: 20px;
  float: left;
}
