import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { PatentComponent } from './patent.component';
import { SharedModule, WeightingBarComponent } from '../../shared';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { provideMatomo } from 'ngx-matomo-client';


import {
  AnalyticsChartsModule,
  BasicChartsModule,
  ChartDashboardModule,
  ClassificationChartsModule,
  CompetitionChartsModule,
  TrendLandscapeChartsModule
} from '@shared/charts';

describe('PatentComponent', () => {
  let component: PatentComponent;
  let fixture: ComponentFixture<PatentComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PatentComponent,
        WeightingBarComponent,
      ],
      imports: [
        BasicChartsModule,
        ClassificationChartsModule,
        CompetitionChartsModule,
        AnalyticsChartsModule,
        TrendLandscapeChartsModule,
        ChartDashboardModule,
        SharedModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        NgxSliderModule,
        RouterModule.forRoot([])
      ],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
