import {
  catchError,
  concatMap,
  debounceTime,
  finalize,
  map,
  skip,
  take,
  tap
} from 'rxjs/operators';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, isDevMode, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ExtraFilters, MAIN_TECHNOLOGY_AREA_KEYS, Patent, TaskModel, User } from '@core/models';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { tooltip } from './tooltip';
import { columnsToShow, selectedColumnsCombinedMode, columnsToShowFocal } from './columns';
import {
  AutocompleteComponent,
  DoubleMappedPatentNumbersDialogComponent,
  MonitorDialogComponent,
  PatentTableComponent,
  SemanticInputComponent,
  TranslateLanguageDialogComponent
} from '@shared/components';
import { BehaviorSubject, Observable, Subscription, of } from 'rxjs';
import {
  AdvancedFilterService,
  ApplicantsAliasesService,
  extractPublications,
  MatomoService,
  NplSearchService,
  PaginationMetadata,
  PatentNumberService, PatentNumberTypeEnum,
  PatentQueryParams,
  PatentTableService,
  SearchHistoryService,
  SearchQueryParams,
  SemanticSearchFilters,
  SemanticSearchRequest,
  SemanticSearchService,
  TaskService,
  UserService
} from '@core/services';
import { CollectionStoreService, NplSearchStoreService, SemanticSearchStoreService, SortParams } from '@core/store';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ENGLISH_CODE, ENGLISH_LANG, translatableLanguages } from '@core/data';
import { SearchTypeEnum, ViewModeTypeEnum } from './types';
import { PatentNumberSearchTypeEnum } from '@core/services/patent-number';
import { tooltip as tooltipNpl } from 'app/npl-search/npl/tooltip';
import { BooleanSearchService, ChartsService, DateFormatPipe, ToastService } from '@core';
import { ActionType } from '@shared/charts/chart-dashboard/types';
import { SemanticFiltersDialogComponent } from '@shared/components/semantic-filters-dialog/semantic-filters-dialog.component';

@Component({
  selector: 'app-patent',
  templateUrl: './patent.component.html',
  styleUrls: ['./patent.component.scss'],
  animations: [
    trigger('fadeIn', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
    ]),
    trigger('fadeInOut', [
      state('void', style({opacity: 0})),
      transition('void => *', animate(500)),
      transition('* => void', animate(300))
    ]),
  ]
})
export class PatentComponent implements OnInit, OnDestroy, AfterViewInit {
  form: UntypedFormGroup;
  payload: SemanticSearchRequest;
  sorting: SortParams = {field: null, order: null} as SortParams;
  search_hash = null;
  filtersVisible = false;
  publicationsToRemove = [];

  debounce: any;
  selectedLanguage: {code: string, name: string};
  detectedLanguage: {code: string, name: string};
  private expandedLanguageCode;
  previousDetectedLanguage: {code: string, name: string};
  isDetectingLanguage = false;
  wasTranslationConfirmed = false;

  labelFilters = {
    quantity_cut_off: 'Quantity Cut-Off',
    similarity_cut_off: 'Similarity Cut-Off',
    inclusion_and: 'Inclusion (AND)',
    inclusion_or: 'Inclusion (OR)',
    exclusion_and: 'Exclusion (AND)',
    exclusion_or: 'Exclusion (OR)',
    applicants_plus: 'Applicant (+)',
    applicants_minus: 'Applicant (-)',
    cpc_plus: 'CPC Code (+)',
    cpc_minus: 'CPC Code (-)',
    ipc_plus: 'IPC Code (+)',
    ipc_minus: 'IPC Code (-)',
    earliest_priority_date: 'Earliest Priority Date',
    latest_priority_date: 'Latest Priority Date',
    earliest_publication_date: 'Earliest Publication Date',
    latest_publication_date: 'Latest Publication Date',
    authorities_plus: 'Authority (+)',
    authorities_minus: 'Authority (-)',
    legal_status: 'Legal Status',
    ma1: 'Electrical Engineering',
    ma2: 'Instruments',
    ma3: 'Chemistry',
    ma4: 'Mechanical Engineering',
    ma5: 'Other Fields'
  };

  fromCommaToArray = [
    'inclusion_and', 'inclusion_or', 'exclusion_and', 'exclusion_or',
    'applicants_plus', 'applicants_minus', 'authorities_plus', 'authorities_minus',
    'legal_status'
  ];

  weightingArray = [5.0, 4.0, 3.0, 2.0, 1.0, 0.8, 0.6, 0.4, 0.2];

  messageAlert = '';
  messageAdvice = '';
  messageAdviceAlert = '';

  hasBooleanQuery = false;

  tooltipTitle = tooltip.title;
  tooltipText = tooltip.text;
  tooltipNpl = tooltipNpl;

  user: User;

  @ViewChild('searchFeedback') searchFeedbackEle: ElementRef;
  @ViewChild('searchText') searchTextEle: ElementRef;
  @ViewChild('patentNumbersText') patentNumbersTextEle: ElementRef;
  @ViewChild('patentTable') patentTable: PatentTableComponent;
  @ViewChild('semanticInput') semanticInput: SemanticInputComponent;

  columnsToShow = columnsToShow;
  columnsToShowFocal = columnsToShowFocal;
  selectedColumnsCombinedMode = selectedColumnsCombinedMode;

  savedTaskMessages: string[];
  isSearchingFocal = false;
  isBoostOpen = false;

  private refreshingChartFilter = false;
  private refreshingFilter = false;
  private isPerformingAdvancedFilter = false;

  selectedSearch: SearchTypeEnum = SearchTypeEnum.SEMANTIC_SEARCH;
  SearchTypeEnum = SearchTypeEnum;
  amountSearchFilters = 0;
  amountBoostFactors = 0;

  private showTranslateBoxSubject = new BehaviorSubject<boolean>(false);
  private readonly showTranslateBox$ = this.showTranslateBoxSubject.asObservable();

  private combinedModePosition: number;

  private subscriptions = new Subscription();

  exceededQuotasMessage = null;
  @ViewChild('header', {read: ElementRef, static: false}) header: ElementRef;

  searchInputHolder = 'Please enter a patent publication number (e.g. EP2049363, US8697359 or WO2018158104) or any English text (e.g. the abstract of a patent, a scientific article or a product description).';

  isFiltersFormGroupInvalid: boolean = true;
  private canSaveSearchHistory: boolean = true;

  private readonly MAX_WORDS_FOR_IMPROVEMENT = 25;

  constructor(
    public searchService: SemanticSearchService,
    public patentNumberService: PatentNumberService,
    public semanticSearchStoreService: SemanticSearchStoreService,
    private route: ActivatedRoute,
    private location: Location,
    private chartService: ChartsService,
    public userService: UserService,
    private toastService: ToastService,
    public collectionsStoreService: CollectionStoreService,
    private router: Router,
    private modalService: NgbModal,
    private advancedFilterService: AdvancedFilterService,
    private patentTableService: PatentTableService,
    private taskService: TaskService,
    public nplService: NplSearchService,
    private matomoService: MatomoService,
    private applicantsAliasesService: ApplicantsAliasesService,
    private searchHistoryService: SearchHistoryService,
    private dateFormatPipe: DateFormatPipe,
    private nplSearchStoreService: NplSearchStoreService,
    private changeDetectorRef: ChangeDetectorRef,
  ) {
  }

  get NPLDocuments(){
    return this.nplService.getDocuments();
  }

  get NPLPagination(){
    return this.nplService.getPagination();
  }

  public get linkData() {
    return this.searchService.linkData;
  }

  public get totalSelectedPatents() {
    return this.semanticSearchStoreService.selectedPublications.length;
  }

  get viewMode (): ViewModeTypeEnum{
    return this.semanticSearchStoreService.patentListViewMode
  }
  get isListVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }
  get isChartVisible(): boolean{
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }
  get isCombinedMode(): boolean{
    return this.semanticSearchStoreService.isCombinedMode;
  }

  get isListMode(): boolean{
    return this.semanticSearchStoreService.isListMode;
  }

  get isSemanticSearch(): boolean{
    return this.selectedSearch === SearchTypeEnum.SEMANTIC_SEARCH;
  }

  get isNPLSearch(): boolean{
    return this.selectedSearch === SearchTypeEnum.NPL_SEARCH;
  }

  get filtersFormGroup(): UntypedFormGroup {
    return this.form.get('search_filters') as UntypedFormGroup;
  }

  get messageCollaboratorQuotas(): string {
    return this.userService.messageCollaboratorQuotas;
  }

  get isCollaboratorQuotasExceeded(): boolean {
    return this.userService.isCollaboratorQuotasExceeded;
  }

  get pageSize(): number {
    return this.semanticSearchStoreService.pageSize;
  }

  get pagination(): PaginationMetadata {
    return this.semanticSearchStoreService.pagination || {} as PaginationMetadata;
  }

  set pagination(value: PaginationMetadata) {
    this.semanticSearchStoreService.pagination = value;
  }

  get weighting(): number {
    return this.semanticSearchStoreService.weighting || 4;
  }

  set weighting(value: number) {
    this.semanticSearchStoreService.weighting = value;
  }

  get textWeighting(): number {
    return this.semanticSearchStoreService.textWeighting || 1.0;
  }

  set textWeighting(value: number) {
    this.semanticSearchStoreService.textWeighting = value;
  }

  ngOnInit(): void {
    this.user = this.userService.getUser();
    this.buildForm();
    this.getUserStatistics();

    const filters$ = this.semanticSearchStoreService.filters$
      .pipe(skip(1))
      .subscribe({
        next: item => {
          this.countSearchFilters();
          if (!this.form.valid || this.semanticSearchStoreService.searching || this.refreshingChartFilter || this.refreshingFilter) {
            if (this.refreshingChartFilter) {
              this.refreshingChartFilter = false;
            }
            this.refreshingFilter = false;
            return;
          }
          if (this.pagination) {
            this.pagination.current_page = 1;
          }
          setTimeout(() => this.semanticSearchStoreService.isCalculatingCharts = true);
          this.search_hash = null;
          this.search(null);
        }
      });
    this.subscriptions.add(filters$);

    const filterRemoved$ = this.semanticSearchStoreService.filterRemoved$
      .pipe(skip(1))
      .subscribe({
        next: item => {
          if (item['type'] === 'search-filter') {
            const filterName = item['filterName'];
            const filterFormControl = (<UntypedFormControl>this.filtersFormGroup.get(filterName));
            if (!MAIN_TECHNOLOGY_AREA_KEYS.includes(filterName)) {
              filterFormControl.setValue(filterName.indexOf('date') > -1 ? null : '');

              if (this[filterName] instanceof AutocompleteComponent) {
                this[filterName].reset();
              }
            } else {
              filterFormControl.setValue(false);
              if (MAIN_TECHNOLOGY_AREA_KEYS.every(key => !this.filtersFormGroup.get(key).value)) {
                MAIN_TECHNOLOGY_AREA_KEYS.forEach(key => {
                  this.filtersFormGroup.get(key).setValue(true);
                });
              }
            }

            this.countSearchFilters();
            this.semanticSearchStoreService.isCalculatingCharts = true;
            this.search(null);
          }
        }
      });
    this.subscriptions.add(filterRemoved$);

    const publicationsToRemove$ = this.semanticSearchStoreService.publicationsToRemove$
      .pipe(skip(1))
      .subscribe({
        next: publications => {
          this.publicationsToRemove = publications;
          this.search();
        }
      });
    this.subscriptions.add(publicationsToRemove$);

    const isCalculatingCharts$ = this.semanticSearchStoreService.isCalculatingCharts$.subscribe({
      next: value => {
        if (!value) {
          this.scrollCombinedModePosition();
        }
      }
    });
    this.subscriptions.add(isCalculatingCharts$);

    const changeApplicantsEvent$ = this.applicantsAliasesService.changeApplicantsEvent.subscribe({
      next: (res) => {
        this.canSaveSearchHistory = false;
        this.search();
      }
    });
    this.subscriptions.add(changeApplicantsEvent$);

    const searchHash$ = this.semanticSearchStoreService.searchHash$.subscribe({
      next: hash => {
        if (hash && this.isSemanticSearch) {
          this.getCharts();
        }
      }
    });
    this.subscriptions.add(searchHash$);

    const chartDashboardAction$ = this.semanticSearchStoreService.chartDashboardAction$.subscribe({
      next: msg => {
        this.semanticSearchStoreService.customChartCategories = this.userService.getChartCategories(this.semanticSearchStoreService.chartDashboardType);

        if (msg.action === ActionType.Add) {
          this.semanticSearchStoreService.searchHash = this.semanticSearchStoreService.searchHash;
        }
      }
    });
    this.subscriptions.add(chartDashboardAction$);

    const showTranslateBox$ = this.showTranslateBox$
      .pipe(
        debounceTime(500)
      )
      .subscribe({
        next: (show) => {
          if (show) {
            this.openTranslateBox();
          }
        }
      });
    this.subscriptions.add(showTranslateBox$);
  }

  ngAfterViewInit(): void {
    this.parseParams();
    this.changeDetectorRef.detectChanges();
  }

  onRefresh(): void {
    this.refreshTerm();
    this.refreshFilters();
    this.refreshSearch();

    if (!this.route.snapshot.queryParams['search']) {
      this.semanticSearchStoreService.chartFilterDisabled = false;
    }
  }

  refreshTerm(): void {
    const {controls} = this.form;
    controls.term.setValue('');
    controls.publications.setValue('');
  }

  refreshFilters(): void {
    this.refreshSearchFilters();
    this.semanticSearchStoreService.setFilters([], false);
    this.semanticSearchStoreService.extraFilters = new ExtraFilters();
  }

  refreshSearchFilters(): void {
    const {controls} = this.form;
    controls.search_filters.reset();
    controls.search_filters.patchValue({
      ma1: true,
      ma2: true,
      ma3: true,
      ma4: true,
      ma5: true
    });
  }

  refreshChartFilters(): void {
    const filters = [...this.semanticSearchStoreService.filters].filter(filter => filter.type !== 'chart');
    this.semanticSearchStoreService.chartSelectedValues = {};
    this.semanticSearchStoreService.chartQuantity = 0;
    if (filters.length !== this.semanticSearchStoreService.filters.length) {
      this.refreshingChartFilter = true;
      this.semanticSearchStoreService.searchHash = null;
      if (this.semanticSearchStoreService.extraFilters.top_applicants) {
        this.semanticSearchStoreService.extraFilters = {top_applicants: this.semanticSearchStoreService.extraFilters.top_applicants};
      } else {
        this.semanticSearchStoreService.extraFilters = new ExtraFilters();
      }
      this.semanticSearchStoreService.setFilters(filters);
    }
  }

  refreshListFilter(): void {
    this.refreshingFilter = true;
  }

  refreshSearch(): void {
    this.searchService.resetDocuments();
    this.pagination = {} as PaginationMetadata;
    this.sorting = {field: null, order: null} as SortParams;
    this.search_hash = null;
    this.patentNumberService.resetDocuments();
    this.semanticSearchStoreService.selectedPublications = [];
    this.semanticSearchStoreService.altSearchDocumentIds = [];
    this.semanticSearchStoreService.patentTableSort = {field: null, order: null} as SortParams;
    this.semanticSearchStoreService.advancedFilterAppliedQuery = '';
    this.semanticSearchStoreService.advancedFilterAdvancedQuery = '';
    this.semanticSearchStoreService.advancedFilterAppliedAdvancedQuery = '';
    this.semanticSearchStoreService.advancedFilterBooleanClauses = [];
    this.canSaveSearchHistory = true;
  }

  onSubmit(): void {
    if (this.shouldShowTranslateBox()) {
      this.showTranslateBoxSubject.next(true);
      return;
    }

    this.refreshSearch();
    this.nplService.resetService();
    this.semanticSearchStoreService.chartFilterDisabled = false;
    this.semanticSearchStoreService.savedSearch = null;
    this.semanticSearchStoreService.isAdvancedFilterAdvancedMode = false;
    this.refreshListFilter();
    this.refreshChartFilters();
    this.search();
    this.matomoService.semanticSearchButton();
  }

  get searching(): boolean{
    return this.semanticSearchStoreService.searching;
  }

  searchStarting(): boolean {
      return this.search_hash === null && this.semanticSearchStoreService.searching;
  }

  search(elementId?) {
    if (!this.form.valid) {
      return;
    }

    if (!this.validTextInput()) {
      return;
    }

    if (!this.semanticInput.isTextEnough && this.userService.isFirstTimeExpandSearchInput()) {
      this.semanticInput.toggleExpansionPopper();
      return;
    }

    this.semanticSearchStoreService.searching = true;
    if (this.isDetectingLanguage) {
      setTimeout(() => {this.search()}, 100);
      return;
    }

    this.messageAlert = '';
    this.exceededQuotasMessage = null;
    this.messageAdviceAlert = '';

    this.payload = this.buildPayload();

    const queryParams = this.buildQueryParams();

    this.semanticSearchStoreService.newSearchEvent.emit(true);

    this.storeScrollCombinedModePosition();
    this.semanticSearchStoreService.patentListViewMode = this.semanticSearchStoreService.patentListViewMode || ViewModeTypeEnum.COMBINED;
    const search$ = this.validatePatentNumbers().pipe(
      concatMap((isValid) => {
        if (isValid) {
          this.canSaveSearchHistory = true;
          return this.searchService.search(this.payload, queryParams)
        } else {
          return of({data: null});
        }
      }),
      take(1),
      finalize(() => {
        if(this.isSemanticSearch){
          this.isPerformingAdvancedFilter = false;
          this.semanticSearchStoreService.searching = false;
        }
      }))
      .subscribe({
        next: ({data}) => {
          if (data) {
            this.getUserStatistics();

            this.search_hash = data.search_info.search_hash;

            this.pagination = data.page;
            const values = {
              queryParams,
              payload: this.payload,
              searchHash: data.search_info.search_hash,
              totalHits: this.pagination.total_hits,
              pagination: this.pagination,
              patentNumbers: this.form.value.publications,
              searchInput: this.form.value.term,
              weighting: this.weighting,
              filterDisabled: false,
              originalSearchInput: this.form.value.term,
              expandSearchInput: data.search_info.details?.semantic?.expanded_search_input || ''
            };

            this.storeValues(values);
            if(this.isNPLSearch){
              this.checkNPLTab()
            }

            if (data.search_info.details?.semantic?.expanded_search_input) {
              this.form.get('term').setValue(data.search_info.details.semantic.expanded_search_input);
              this.semanticInput.updateTextAreaHeight(null);
            }

            if (data.documents.length) {
              if (elementId !== null) {
                setTimeout(() => this.scrollToResults(elementId));
              } else {
                if (this.semanticSearchStoreService.docdbFamilyIdFromPatentViewer) {
                  this.semanticSearchStoreService.scrollToPatentFromPatentViewer();
                }
              }
            } else {
              if (!this.hasExtraFilters(this.semanticSearchStoreService.filters) && !this.payload.patent_numbers?.length) {
                this.messageAlert = 'Your search query is too short and generic. Please provide a longer text with meaningful keywords.<br/>';
              }
            }
          }
        },
        error: (err) => {
          this.semanticSearchStoreService.searching = false;
          switch (err.status) {
            case 429:
              this.exceededQuotasMessage = err.error.message.split(/(upgrading)/);
              break;
            case 400:
              let message = '<p>Invalid search request</p>';
              if ('details' in err.error) {
                message += this.searchService.extractErrorMessage(err.error.details, this.payload);
              }
              this.messageAlert = message;
              break;
            case 503:
              this.messageAlert = `<p>Search engine was not able to process your request, please try again in few minutes.</p>
            <p>Please contact the <a href="mailto:<EMAIL>">customer support</a> if the problem persists.</p>`;
              break;
            case 500:
              this.messageAlert = `<p>Something went wrong. Please contact the <a href="mailto:<EMAIL>">customer support</a>.</p>`;
              break;
            case 0:
              this.messageAlert = `<p>The server cannot be reached. Please check your internet connection.</p>`;
              break;
            default:
              this.messageAlert = `<p>Search engine was not able to process your request.</p>
            <p>Please contact the <a href="mailto:<EMAIL>">customer support</a> if the problem persists.</p>`;
              break;
          }
        },
        complete: () => {
          if (this.hasBooleanQuery) {
            setTimeout(() => {
              (<HTMLElement>document.getElementsByClassName('boolean-query')[0]).onclick = (_) => {
                this.router.navigateByUrl(`boolean?search=semantic`);
              };
            });
          }
        }
      });
    this.subscriptions.add(search$);
  }

  onPageChange($event): void {
    const {current_page} = this.pagination;

    if (current_page === $event) {
      return;
    }

    this.pagination.current_page = $event;
    // this.scrollToResults();
    this.semanticSearchStoreService.shouldCalculateCharts = false;
    this.canSaveSearchHistory = false;
    this.search('search-results-container');
  }

  onSort(val: SortParams): void {
    this.sorting = val;
    // this.scrollToResults();
    this.semanticSearchStoreService.shouldCalculateCharts = false;
    this.canSaveSearchHistory = false;
    this.search('search-results-container');
  }

  openFilters() {
    if (this.userService.isFreeUser()) return;
    const modalOptions = {
      scrollable: true,
      windowClass: 'right-side-modal',
    };

    const currentFilterValues = {...this.filtersFormGroup.value};
    const filtersModal = this.modalService.open(SemanticFiltersDialogComponent, modalOptions);
    filtersModal.componentInstance.form = this.form;

    filtersModal.result.then((result) => {
      this.countSearchFilters();
      this.isFiltersFormGroupInvalid = this.filtersFormGroup.invalid;
    }, (reason) => {
      this.filtersFormGroup.patchValue(currentFilterValues);
      this.isFiltersFormGroupInvalid = this.filtersFormGroup.invalid;
    });

  }

  export(type) {
    let families = [];
    if (this.semanticSearchStoreService.selectedPublications.length) {
      this.semanticSearchStoreService.selectedPublications.forEach(number => {
        this.searchService.getDocuments().map(doc => {
          if (doc['general'].raw_publication_number === number) {
            families.push(doc['general'].docdb_family_id);
          }
        });
      });
    } else {
      families = this.searchService.getDocuments().map(doc => doc['general'].docdb_family_id);
    }

    const export$ = this.searchService.export(this.search_hash, {patent_documents_ids: families}, {format: type})
      .pipe(take(1))
      .subscribe({
        next: (file) => {
          const blob = new Blob([file], {type: file.type});
          this.downloadFile(blob);
        }
      });
    this.subscriptions.add(export$);
  }

  public downloadFile(blob: Blob, name: string = null) {
    let type = '';
    switch (blob.type) {
      case 'application/pdf':
        type = 'pdf';
        break;
      case 'text/csv':
        type = 'csv';
        break;

      default:
        type = 'xlsx';
        break;
    }
    if (!name) {
      name = 'export';
    }

    const ua = navigator.userAgent;

    const msie = ua.indexOf('MSIE');
    const trident = ua.indexOf('Trident/');
    const edge = ua.indexOf('Edge/');

    if (msie > 0 || trident > 0 || edge > 0) {
      (navigator as any).msSaveOrOpenBlob(blob, name + '.' + type);
      return;
    }

    if (ua.toLowerCase().indexOf('mozilla') > -1) {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      if (blob.type === 'application/pdf') {
        link.setAttribute('target', '_blank');
      }
      link.download = name + '.' + type;
      document.body.appendChild(link);
      link.click();
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      }, 1000);
      return;
    }

    if (blob.type === 'application/pdf') {
      const link = document.createElement('a');
      link.setAttribute('target', '_blank');
      link.href = URL.createObjectURL(blob);
      link.click();
    }
  }

  isProduction() {
    return !isDevMode();
  }

  onChangePageSize(event: number): void {
    this.semanticSearchStoreService.shouldCalculateCharts = false;
    this.semanticSearchStoreService.pageSize = event;
    this.pagination.current_page = 1;
    this.canSaveSearchHistory = false;
    this.search('search-results-container');
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.semanticSearchStoreService.typedPublications = [];
    this.patentTableService.selectedLegalStatus = [];
    this.nplService.resetService();
    this.toastService.clear();
  }

  onSignupClicked() {
    this.userService.purgeUser();
  }

  private validatePatentNumbers(): Observable<any> {
    if (this.payload.patent_numbers?.length && !this.semanticSearchStoreService.altSearchDocumentIds.length) {
      this.isSearchingFocal = true;
      const payload = {
        patent_numbers: this.payload.patent_numbers,
        patent_numbers_type: PatentNumberTypeEnum.PUBLICATION_NUMBER,
        search_type: PatentNumberSearchTypeEnum.PUBLICATION,
        skip_invalid: 1
      };
      const queryParams = {show_analytics: 1, show_fulltext: 1, show_general: 1, show_tags: 1} as PatentQueryParams;

      return this.patentNumberService.search(payload, queryParams)
        .pipe(
          take(1),
          finalize(() => setTimeout(() => this.isSearchingFocal = false)),
          map( ({data}) => {
              const foundPatentNumbers = data.publications?.flatMap(o => [
                o.general.publication_number, o.general.original_number, o.general.original_number_normalized
              ]);
              const notFoundPatentNumbers = this.payload.patent_numbers.filter(o => !foundPatentNumbers.includes(o));
              if (notFoundPatentNumbers.length) {
                this.messageAlert = `Sorry about that! It seems like the patent number${notFoundPatentNumbers.length > 1 ? 's' : ''} ${notFoundPatentNumbers.join(', ')} you entered ${notFoundPatentNumbers.length > 1 ? 'aren\'t' : 'isn\'t'} valid.
                                      Make sure you're using publication numbers only.`;

                if (!this.payload.search_input && !foundPatentNumbers.length) {
                  return false;
                }
              }

              const noEnglishTextPatentNumbers = this.payload.patent_numbers.filter(n => {
                return data.publications?.findIndex(o => {
                  const isExisted = [
                    o.general.publication_number, o.general.original_number, o.general.original_number_normalized
                  ].includes(n);

                  const isNoText = [o.bibliographic?.abstract, o.fulltext?.claims, o.fulltext?.description]
                    .filter(v => v?.trim()?.length > 0).length === 0;

                  return isExisted && isNoText;
                }) > -1;
              });

              if (noEnglishTextPatentNumbers.length) {
                if (this.messageAlert) {
                  this.messageAlert += '<br/>';
                }
                this.messageAlert += noEnglishTextPatentNumbers.length > 1 ?
                  'We do not have an English text for patent numbers:' : 'We do not have an English text for patent number:';
                this.messageAlert += ` ${noEnglishTextPatentNumbers.join(', ')}`;
              }

              return this.showDoubleMappedPatentNumbersDialog(data.publications);
            }),
            catchError((err) => {
              console.log(err);
              this.messageAlert = `Sorry about that! It seems like the patent number${payload.patent_numbers.length > 1 ? 's' : ''}
                ${payload.patent_numbers.join(', ')} you entered ${payload.patent_numbers.length > 1 ? 'aren\'t' : 'isn\'t'} valid.
                Make sure you're using publication numbers only.`;
              this.patentNumberService.resetDocuments();
              return of(false);
            })
        );
    }
    return of(true);
  }

  private showDoubleMappedPatentNumbersDialog(patents: Patent[]): boolean {
    const patentNumbers = patents.map((p) => this.patentTableService.getQueriedPublicationNumber(p));

    if (patentNumbers.length !== new Set(patentNumbers).size) {
      const modalRef = this.modalService.open(DoubleMappedPatentNumbersDialogComponent,
        {centered: true, windowClass: 'modal-double-mapped', size: 'xl'}
      );
      modalRef.componentInstance.patents = patents;
      modalRef.result.then((documentIds: number[]) => {
        if (documentIds.length > 0) {
          this.refreshSearch();
          this.semanticSearchStoreService.altSearchDocumentIds = documentIds;
          this.patentNumberService.setDocuments(patents.filter((p) => documentIds.includes(Number(p.general.docdb_family_id))));
          this.search();
        }
      }, reason => {
        return false;
      });
    }
    return true;
  }

  private hasSavedFilters(): boolean {
    return this.userService.getUISetting('search_filters', false);
  }

  private buildForm(): void {
    this.form = new UntypedFormGroup({
      term: new UntypedFormControl('', [Validators.required]),
      publications: new UntypedFormControl(),
      search_filters: new UntypedFormGroup({
        quantity_cut_off: new UntypedFormControl('', [Validators.max(1000)]),
        similarity_cut_off: new UntypedFormControl(),
        inclusion_and: new UntypedFormControl(),
        inclusion_or: new UntypedFormControl(),
        exclusion_and: new UntypedFormControl(),
        exclusion_or: new UntypedFormControl(),
        applicants_plus: new UntypedFormControl(),
        applicants_minus: new UntypedFormControl(),
        cpc_plus: new UntypedFormControl([]),
        cpc_minus: new UntypedFormControl([]),
        ipc_plus: new UntypedFormControl([]),
        ipc_minus: new UntypedFormControl([]),
        earliest_priority_date: new UntypedFormControl(null, this.validatorDate()),
        latest_priority_date: new UntypedFormControl(null, this.validatorDate()),
        earliest_publication_date: new UntypedFormControl(null, this.validatorDate()),
        latest_publication_date: new UntypedFormControl(null, this.validatorDate()),
        authorities_plus: new UntypedFormControl(),
        authorities_minus: new UntypedFormControl(),
        ma1: new UntypedFormControl(true),
        ma2: new UntypedFormControl(true),
        ma3: new UntypedFormControl(true),
        ma4: new UntypedFormControl(true),
        ma5: new UntypedFormControl(true),
        legal_status: new UntypedFormControl(),
      }),
      boost_factors: new UntypedFormGroup({
        keywords: new UntypedFormControl([]),
        ipc_codes: new UntypedFormControl([]),
        cpc_codes: new UntypedFormControl([]),
      }),
    });
  }

  private validatorDate(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      if (typeof control.value !== 'string' && control.value.year?.toString().length < 4) {
        return {date: control.value};
      }
      return null;
    };
  }

  private loadSavedFilters() {
    if (!this.hasSavedFilters()) {
      return;
    }
    const searchFiltersSaved = this.userService.getUISetting('search_filters', {});

    Object.keys(searchFiltersSaved).forEach(key => {
      const filterValues = searchFiltersSaved[key]
      if (this.filtersFormGroup.get(key) && filterValues) {
        if (key.indexOf('date') > -1) {
          const date = searchFiltersSaved[key].split('-');
          this.filtersFormGroup.get(key).setValue({
            day: parseInt(date[2], 10),
            month: parseInt(date[1], 10),
            year: parseInt(date[0], 10)
          });
        } else if (key.indexOf('cpc') > -1 || key.indexOf('ipc') > -1) {
          let value = filterValues;
          if (!Array.isArray(value)) {
            value = value.split(',');
          }
          if (value.length) {
            this.filtersFormGroup.get(key).setValue(value);
          }
        } else {
          this.filtersFormGroup.get(key).setValue(filterValues);
        }

        this.updateFiltersVisible(filterValues, key);
      }
    });
    this.countSearchFilters();
    this.isFiltersFormGroupInvalid = this.filtersFormGroup.invalid;
  }

  private parseParams(): void {
    if (this.semanticSearchStoreService.backPatentSearch && this.user && this.user.profile) {
      this.setFieldsAfterBack();
      this.semanticSearchStoreService.backPatentSearch = false;
      setTimeout(() => this.semanticSearchStoreService.searchHash = this.semanticSearchStoreService.searchHash);

      if (this.semanticSearchStoreService.addNumberToSearch.length > 0) {
        const term = this.form.value.term;
        const publications = this.form.value.publications ? this.form.value.publications : [];

        const numbersToAdd = this.semanticSearchStoreService.addNumberToSearch.filter(number => term.indexOf(number) === -1 &&
          publications.indexOf(number) === -1);

        this.semanticSearchStoreService.addNumberToSearch = [];
        if (numbersToAdd.length > 0) {
          this.form.get('term').setValue(term + ',' + numbersToAdd.join(','));
        }
      }

      this.search(null);
      return;
    }

    this.onRefresh();
    this.resetStoreService(true);
    this.clearAdvancedFilter();

    const publicationNumber = this.route.snapshot.queryParams['publication'];
    if (publicationNumber) {
      this.form.get('term').setValue(publicationNumber);
      this.search();
      return;
    }

    if (this.canLoadSavedSearch()) {
      if(!this.semanticSearchStoreService.savedSearch){
        const searchHistoryService$ = this.searchHistoryService.get(this.route.snapshot.queryParams['search']).subscribe({
            next: (data) => {
              this.semanticSearchStoreService.savedSearch = data;
              this.searchBySavedSearch();
            }
          });
        this.subscriptions.add(searchHistoryService$);
      } else {
        this.searchBySavedSearch();
      }
    } else {
      this.loadSavedFilters();
    }
  }

  private searchBySavedSearch(){
    this.searchHistoryService.restoreSavedValues(this.semanticSearchStoreService.savedSearch);
    this.setFieldsSavedSearch(this.semanticSearchStoreService.savedSearch);

    this.canSaveSearchHistory = false;
    this.semanticInput.updateTextAreaHeight();
    this.search();

    setTimeout(() => { this.location.replaceState('/search/patent'); }, 500);
    this.semanticSearchStoreService.savedSearch = null;
  }

  private canLoadSavedSearch(): boolean {
    return this.route.snapshot.queryParams['search'] && this.route.snapshot.queryParams['search'] > 0;
  }

  private setFieldsSavedSearch(savedSearch): void {
    if (savedSearch.expanded_search_input) {
      this.form.get('term').setValue(savedSearch.expanded_search_input);
    } else {
      if (!savedSearch.search_input) {
        this.form.get('term').setValue(savedSearch.patent_numbers.join(','));
      } else {
        this.form.get('term').setValue(savedSearch.search_input);

        if (savedSearch.patent_numbers) {
          this.form.get('publications').setValue(savedSearch.patent_numbers.join(','));
        }
      }
    }

    if (savedSearch.search_filters?.text_weighting) {
      this.weighting = this.weightingArray.indexOf(savedSearch.search_filters.text_weighting);
      this.textWeighting = savedSearch.search_filters.text_weighting;
    }

    this.filtersVisible = false;

    Object.keys(savedSearch.search_filters || {}).forEach(key => {
      if (this.filtersFormGroup.get(key)) {
        const savedFilters = this.removeDefaultFilters(savedSearch.search_filters, key);
        const filterValues = savedFilters[key];
        if (this.fromCommaToArray.indexOf(key) > -1) {
          this.filtersFormGroup.get(key).setValue(filterValues.join(','));
        } else if (key.indexOf('date') > -1) {
          const date = filterValues.split('-');
          this.filtersFormGroup.get(key).setValue({
            day: parseInt(date[2], 10),
            month: parseInt(date[1], 10),
            year: parseInt(date[0], 10)
          });
        } else {
          this.filtersFormGroup.get(key).setValue(filterValues);
        }

        this.updateFiltersVisible(filterValues, key);
      }
    });

    if (savedSearch.boost_factors?.keywords?.length || savedSearch.boost_factors?.ipc_codes?.length || savedSearch.boost_factors?.cpc_codes?.length) {
      this.form.patchValue({
        boost_factors: {
          keywords: savedSearch.boost_factors?.keywords ?? [],
          ipc_codes: savedSearch.boost_factors?.ipc_codes ?? [],
          cpc_codes: savedSearch.boost_factors?.cpc_codes ?? []
        }
      });
      this.isBoostOpen = true;
    }

    this.selectedLanguage = translatableLanguages.find((l) => l.code === savedSearch.source_language);

    this.countSearchFilters();
  }

  private updateFiltersVisible(filterValues, filterField) {
    let hasFilterValues: boolean;
    switch (true) {
      case Array.isArray(filterValues):
        hasFilterValues = filterValues.filter(v => v?.length > 0).length > 0;
        break;
      case typeof (filterValues) === 'string':
        hasFilterValues = filterValues?.toString()?.trim().length > 0;
        break;
      case typeof (filterValues) === 'boolean':
        hasFilterValues = filterValues;
        break;
      default:
        hasFilterValues = filterValues !== null && filterValues !== undefined;
        break;
    }

    const isMainAreaFilter = MAIN_TECHNOLOGY_AREA_KEYS.includes(filterField);

    if ((hasFilterValues && !isMainAreaFilter) || (!hasFilterValues && isMainAreaFilter)) {
      this.filtersVisible = true;
    }
  }

  private removeDefaultFilters(savedFilters: Object, filterField: string): Object {
    const defaultFilters = this.userService.getDefaultFilters(this.user.profile);
    if (!defaultFilters || !defaultFilters[filterField]) {
      return savedFilters;
    }

    const arrDefaultFilters = defaultFilters[filterField].split(',');

    arrDefaultFilters.forEach( df => {
      const index = savedFilters[filterField].indexOf(df);
      if (index > -1) {
        savedFilters[filterField].splice(index, 1);
      }
    });
    return savedFilters;
  }

  private storeValues(values) {
    this.semanticSearchStoreService.state = {...this.semanticSearchStoreService.state, total_hits: values.totalHits};
    this.semanticSearchStoreService.search= {payload: values.payload, params: values.queryParams};
    this.semanticSearchStoreService.searchHash = values.searchHash;
    this.semanticSearchStoreService.patentNumbers = values.patentNumbers;
    this.semanticSearchStoreService.searchInput = values.searchInput;
    this.semanticSearchStoreService.weighting = values.weighting;
    this.semanticSearchStoreService.chartFilterDisabled = values.filterDisabled;
    this.semanticSearchStoreService.originalSearchInput = values.originalSearchInput;
    this.semanticSearchStoreService.expandSearchInput = values.expandSearchInput;
  }

  private setFieldsAfterBack(): void {
    this.semanticSearchStoreService.selectedPublications = [];
    if (this.semanticSearchStoreService.originalSearchInput && this.semanticSearchStoreService.expandSearchInput) {
      this.form.get('term').setValue(this.semanticSearchStoreService.expandSearchInput);
    } else {
      this.form.get('term').setValue(this.semanticSearchStoreService.searchInput);
    }
    this.semanticInput.updateTextAreaHeight();
    if (this.semanticSearchStoreService.patentNumbers && this.semanticSearchStoreService.patentNumbers.length) {
      this.form.get('publications').setValue(this.semanticSearchStoreService.patentNumbers);
      this.weighting = this.semanticSearchStoreService.weighting;
    }

    this.refreshSearchFilters();
    const searchFilters = this.filtersFormGroup;

    if (this.semanticSearchStoreService.filters.length > 0) {
      this.semanticSearchStoreService.filters.forEach(filter => {
        if (filter.type === 'search-filter') {
          this.filtersVisible = true;
          if (filter.filterName.indexOf('date') > -1) {
            const [year, month, day] = filter.value.split('-').map(Number);
            const dateFilter = {day, month, year}
            searchFilters.get(filter.filterName).setValue(dateFilter);
          } else {
            searchFilters.get(filter.filterName).setValue(filter.value);
          }
        }
      });

      this.countSearchFilters();
    }

    this.payload = this.semanticSearchStoreService.search['payload'];

    if (this.payload?.translation) {
      this.selectedLanguage = translatableLanguages.find((l) => l.code === this.payload.translation.source_language);
    }


    if (this.payload?.boost_factors?.keywords?.length || this.payload?.boost_factors?.ipc_codes?.length || this.payload?.boost_factors?.cpc_codes?.length) {
      this.form.patchValue({
        boost_factors: {
          keywords: this.payload.boost_factors?.keywords ?? [],
          ipc_codes: this.payload.boost_factors?.ipc_codes ?? [],
          cpc_codes: this.payload.boost_factors?.cpc_codes ?? []
        }
      });
      this.isBoostOpen = true;
    }

    this.search_hash = this.semanticSearchStoreService.searchHash;

    if (this.semanticSearchStoreService.patentTableSort) {
      this.sorting = {...this.semanticSearchStoreService.patentTableSort};
    }
  }

  private buildPayload(): SemanticSearchRequest {
    let {term} = this.form.value;

    if (this.semanticSearchStoreService.originalSearchInput &&
      this.semanticSearchStoreService.expandSearchInput === term && !this.form.value.publications) {
      term = this.semanticSearchStoreService.originalSearchInput;
      this.form.get('term').setValue(term);
      this.semanticSearchStoreService.expandSearchInput = '';
      this.selectedLanguage = translatableLanguages.find((l) => l.code === this.expandedLanguageCode);
    }

    const {remainingText, publications} = extractPublications(term);
    let search_filters = this.buildFilters();
    search_filters = Object.assign({}, search_filters, this.buildExtraFilters());

    let publTerm = publications;
    if (!publications.length && this.form.value.publications) {
      publTerm = this.form.value.publications.split(',').filter(i => i);
    }
    const patent_numbers =  {
      ...publTerm.length && {patent_numbers: publTerm}
    };

    const docdb_family_ids = this.semanticSearchStoreService.altSearchDocumentIds?.length > 0 ? {
      docdb_family_ids: this.semanticSearchStoreService.altSearchDocumentIds
    } : {};

    const payload = {
      ...patent_numbers,
      ...docdb_family_ids,
      ...Object.keys(search_filters).length && {search_filters},
      additional_params: { }
    };

    const freeTextQuery = this.semanticSearchStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload.search_filters = {...payload.search_filters, free_text_query: freeTextQuery};
    }

    const chartTextQuery = this.semanticSearchStoreService.getChartFilterQuery();

    if (chartTextQuery) {
      payload.additional_params['chart_filters'] = this.semanticSearchStoreService.filters.filter(f => f.type === 'chart');
    }

    if (this.semanticSearchStoreService.advancedFilterAppliedQuery?.length) {
      payload.additional_params['advanced_filter_applied_query'] = this.semanticSearchStoreService.advancedFilterAppliedQuery;

      if (this.semanticSearchStoreService.advancedFilterAppliedBooleanClauses?.length) {
        const clauses = this.semanticSearchStoreService.advancedFilterAppliedBooleanClauses.map(cl => cl.clone());
        payload.additional_params['advanced_filter_boolean_clauses'] = BooleanSearchService.clausesToJsonQuery(clauses);
      }
    }

    if (this.patentTableService.selectedLegalStatus?.length > 0) {
      payload.additional_params['results_table_filter'] = {
        selected_legal_statuses: this.patentTableService.selectedLegalStatus
      };
    }

    if (this.textWeighting !== 1.0) {
      payload.search_filters = {...payload.search_filters, text_weighting: this.textWeighting};
    }

    if (remainingText) {
      payload['search_input'] = remainingText;
    }

    payload['top_term_weights'] = 100;

    if (!this.userService.isFreeUser() && this.selectedLanguage?.code && this.selectedLanguage.code !== ENGLISH_CODE) {
      payload['translation'] = {
        'source_language': this.selectedLanguage.code
      };
    }

    payload['search_filters'] = this.searchService.mergeDefaultFilterWithSavedFilters(
      payload['search_filters'], this.userService.getDefaultFilters(this.user.profile)
    );

    if (this.hasBoostFactors()) {
      const boost_factors = {...this.form.get('boost_factors').value};
      const properties = ['keywords', 'ipc_codes', 'cpc_codes'];

      properties.forEach(property => {
        if (!boost_factors[property].length) {
          delete boost_factors[property];
        }
      });

      payload['boost_factors'] = boost_factors;
    }

    if (this.semanticSearchStoreService.executeExpandSearchInput && payload['search_input'] && !this.semanticSearchStoreService.savedSearch &&
      (!payload['patent_numbers'] || payload['patent_numbers'].length === 0) &&
        payload['search_input'].split(' ').length < this.MAX_WORDS_FOR_IMPROVEMENT ) {
      payload['expand_search_input'] = true;
      this.expandedLanguageCode = this.selectedLanguage?.code;
    }

    return payload;
  }

  private buildFilters(): SemanticSearchFilters {
    let filters = this.semanticSearchStoreService.filters;

    const search_filters = {...this.filtersFormGroup.value};

    Object.keys(search_filters).forEach(key => {
      const value = key.indexOf('date') === -1 ? search_filters[key] : this.convertDate(search_filters[key]);
      const index = filters?.findIndex((item) => item.filterName === key);

      if (!MAIN_TECHNOLOGY_AREA_KEYS.includes(key)) {

        if (value === null || (typeof value === 'string' && !value.length) ||
            key === 'free_text_query' ||
            key === 'text_weighting' || Object.keys(this.semanticSearchStoreService.extraFilters).indexOf(key) > -1) {
          delete search_filters[key];
          if (index > -1) {
            filters.splice(index, 1);
          }
          return;
        }

        if (index > -1) {
          filters[index].value = value;
        } else {
          filters.push({
            filterName: key,
            title: this.labelFilters[key],
            type: 'search-filter',
            value: value
          });
        }
      } else {
        if (value) {
          delete search_filters[key];
          filters = filters.filter((item) => item.filterName !== key );
        } else {
          if (index === -1) {
            filters.push({
              filterName: key,
              title: this.labelFilters[key],
              type: 'search-filter',
              value: value
            });
          }
        }
      }

      if (key.indexOf('date') > -1) {
        search_filters[key] = value;
      }
      if (this.fromCommaToArray.includes(key) && !Array.isArray(value)) {
        search_filters[key] = value.split(',');
      }
    });
    this.semanticSearchStoreService.setFilters(filters);
    return search_filters;
  }
  convertDate(date): string {
    if (!date) {
      return '';
    }
    if (typeof date === 'string') {
      return date;
    }
    return date.year + '-' + date.month.toString().padStart(2, '0') + '-' + date.day.toString().padStart(2, '0');
  }

  private buildExtraFilters(): ExtraFilters {
    const extraFilters = this.semanticSearchStoreService.extraFilters;
    Object.keys(extraFilters).forEach(key => {
      if (!extraFilters[key]) {
        delete extraFilters[key];
      }
    });

    return extraFilters;

  }

  private buildQueryParams(): SearchQueryParams {
    const sorting = {
      ...this.sorting.field && {
        sort_by: this.sorting.field,
        ...this.sorting.order && {
          sort_order: this.sorting.order
        }
      }
    };

    return {
      page: this.pagination.current_page || 1,
      page_size: this.pageSize,
      show_analytics: 1,
      show_general: 1,
      show_bibliographic: 1,
      show_fulltext: 0,
      show_tags: 1,
      save_history: this.canSaveSearchHistory ? 1 : 0,
      prefetch_images: 1,
      ...sorting
    };
  }

  private scrollToResults(elementId = '') {
    if (!elementId) {
      elementId = 'queried-patent-container';
      if (!document.getElementById(elementId)) {
        elementId = 'charts';
      }
    }
    if (document.getElementById(elementId)) {
      document.getElementById(elementId).scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
    }
  }

  private validTextInput(): boolean {
    this.messageAdvice = '';
    this.messageAlert = '';
    this.hasBooleanQuery = false;

    const terms = extractPublications(this.form.value.term);
    if (terms.publications.length > 5 || (this.form.value.publications && this.form.value.publications.split(',').length > 5)) {
      this.messageAlert = `Our <strong>semantic search engine</strong> currently only supports a combination of up to <strong>five</strong>
      patent numbers.`;
      return false;
    }

    if (this.searchService.detectBooleanQuery(terms.remainingText)) {
      this.messageAdvice = `It seems that you are trying to execute boolean query. Would you like to try our
      <a class="cursor-pointer boolean-query">Boolean search</a> instead?`;
      this.hasBooleanQuery = true;
      return true;
    }

    return true;
  }

  private resetStoreService(updateFilter = false) {
    this.semanticSearchStoreService.chartSelectedValues = {};
    this.semanticSearchStoreService.searchHash = null;
    this.semanticSearchStoreService.extraFilters = new ExtraFilters();
    this.semanticSearchStoreService.setFilters([], updateFilter);
    this.semanticSearchStoreService.pagination = null;
    this.semanticSearchStoreService.searchInput = null;
    this.semanticSearchStoreService.patentNumbers = null;
    this.semanticSearchStoreService.selectedColumnsToShow = [];
    this.semanticSearchStoreService.patentListViewMode = null;
    this.semanticSearchStoreService.originalSearchInput = null;
    this.semanticSearchStoreService.expandSearchInput = null;
  }

  private hasExtraFilters(filters: Array<any>): boolean {
    const extraFields = Object.keys(this.labelFilters);
    return this.semanticSearchStoreService.getAppliedFiltersQuery()?.length > 0 ||
      filters?.findIndex(k => extraFields.includes(k.filterName) &&
        (k.value instanceof Array ? k.value.length > 0 : k.value instanceof String ? k.value?.trim()?.length > 0 : k.value)) > -1;
  }

  /**
   * check if user have right to create monitor profile
   */
  get isMonitorOwner(): boolean {
    return this.userService.hasMonitor && !this.userService.isFreeUser() && !this.userService.isCollaboratorUser();
  }

  onSetupAlert() {
    const modal = this.modalService.open(MonitorDialogComponent);
    modal.componentInstance.semantic_query = {
      patent_numbers: this.payload.patent_numbers,
      search_input: this.payload.search_input
    };
    modal.result.then(message => {
      // successfully performed the event and closed the model
    }, reason => {
      // model closed
    });
  }

  @HostListener('document:keyup', ['$event'])
  onCtrlEnter(event: KeyboardEvent) {
    if (!this.form.valid || this.searchStarting() || this.isCollaboratorQuotasExceeded) {
      return;
    }
    if (event.ctrlKey && event.key === 'Enter') {
      if (!this.isPerformingAdvancedFilter) {
        this.onSubmit();
      }

      this.isPerformingAdvancedFilter = false;
    }
  }

  onAdvancedFilter(val: boolean) {
    if (val) {
      this.pagination.current_page = 1;
      this.isPerformingAdvancedFilter = true;
      this.semanticSearchStoreService.isCalculatingCharts = true;
      this.search();
    }
  }

  onTaskSaved(data: { message: string; payload: TaskModel; savedTasks: TaskModel[] }) {
    if (data?.savedTasks?.length > 0) {
      this.savedTaskMessages = this.taskService.getTaskCreationSuccessMessage(data, 'search_results');
    }
  }

  private clearAdvancedFilter() {
    this.advancedFilterService.reset();
    this.semanticSearchStoreService.resetAdvancedFilter();
  }

  onCustomTabChanged(tab: SearchTypeEnum): void {
    this.selectedSearch = tab;
    this.checkNPLTab();

    if (this.isSemanticSearch) {
      this.getCharts();
    }
  }

  onChangeViewMode(mode: ViewModeTypeEnum): void {
    this.nplSearchStoreService.patentListViewMode = mode;

    this.checkNPLTab();

    if (this.isCombinedMode) {
      this.nplSearchStoreService.singleChartColumn = true;
    }
  }

  checkNPLTab(){
    if(this.NPLDocuments.length === 0 && this.isNPLSearch && this.isListVisible){
      this.NPLSearch();
    }
  }
  getNPLPayload(){
    let {remainingText, publications} = extractPublications(this.form.value.term);
    const payload = {
      search_hash: this.search_hash
    };

    if (!publications.length && this.form.value.publications) {
      publications = this.form.value.publications.split(',').filter(i => i);
    }

    if (remainingText) {
      payload['search_input'] = remainingText;
    }

    if (publications.length > 0) {
      payload['patent_numbers'] = publications;
    }

    return payload;
  }
  getNPLParams(){
    const params = {};
    params['page'] = this.NPLPagination ? this.NPLPagination.current_page : 1;
    params['page_size'] = this.NPLPagination ? this.NPLPagination.page_size : this.pageSize;
    return params;
  }

  onNPLPageChange(page: number){
    this.NPLPagination.current_page = page;
    this.NPLSearch();
  }

  onChangeNPLPageSize(page_size:number){
    this.NPLPagination.current_page = 1;
    this.NPLPagination.page_size = page_size;
    this.NPLSearch();
  }

  NPLSearch(){
    this.semanticSearchStoreService.searching = true;
    const npl_payload = this.getNPLPayload();
    const params = this.getNPLParams();
    const search$ = this.nplService.search(npl_payload, params)
      .pipe(
        take(1),
        finalize(() => this.semanticSearchStoreService.searching = false)
      )
      .subscribe({
        next: (data) => {
          if (this.NPLPagination.total_hits === 0) {
            this.messageAlert = "Your non-patent literature search returned no results";
          }
          if ('search_input' in npl_payload) {
            this.semanticSearchStoreService.searchInput = npl_payload['search_input'] as string;
          }
          if ('patent_numbers' in npl_payload) {
            this.semanticSearchStoreService.patentNumbers = npl_payload['patent_numbers'] as string;
          }
        },
        error: ({error}) => {
          console.error(error);
          this.messageAlert = "Your non-patent literature  search returned an error";
          this.nplService.resetService();
        }
      });
    this.subscriptions.add(search$);
  }

  private storeScrollCombinedModePosition(): void {
    if (!this.isCombinedMode) {
      return;
    }
    const chartsContainer = document.querySelector(".charts-container");
    if (!chartsContainer) {
      return;
    }
    this.combinedModePosition = chartsContainer.scrollTop;
  }

  private scrollCombinedModePosition(): void {
    if (!this.isCombinedMode) {
      return;
    }
    const chartsContainer = document.querySelector(".charts-container");
    if (!chartsContainer) {
      return;
    }
    setTimeout(() => {
      chartsContainer.scroll(0, this.combinedModePosition);
    });
  }

  NPLExpandAllRows(){
    if(this.nplService.expandedRows.length> 0){
      this.nplService.expandedRows.length = 0;
    } else {
      this.nplService.expandAllRows();
    }
  }

  private buildChartPayload() {
    const payload = {
      charts: this.semanticSearchStoreService.getChartActiveNames(),
    };
    const searchFilters = {};

    if (this.semanticSearchStoreService.chartSelectedValues) {
      payload['parameters'] = this.semanticSearchStoreService.chartSelectedValues;
    }
    if (this.semanticSearchStoreService.search && this.semanticSearchStoreService.search['payload'] !== undefined) {
      Object.assign(searchFilters, this.semanticSearchStoreService.extraFilters)
      delete searchFilters['top_applicants'];
    }

    const freeTextQuery = this.semanticSearchStoreService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      searchFilters['free_text_query'] = freeTextQuery;
    }

    if (searchFilters) {
      payload['search_filters'] = searchFilters;
    }

    return payload;
  }

  private getCharts() {
    this.semanticSearchStoreService.isCalculatingCharts = true;
    const calculate$ = this.chartService.calculate(this.buildChartPayload(), this.semanticSearchStoreService.searchHash)
      .pipe(
        take(1),
        finalize(() => this.semanticSearchStoreService.isCalculatingCharts = false)
      )
      .subscribe({
        next: ({ charts }) => { },
        error: (err) => {
          console.error(err);
          this.refreshSearch();
        }
      });
    this.subscriptions.add(calculate$);
  }

  onSelectedLanguageChanged(event) {
    if (this.selectedLanguage?.code && this.selectedLanguage.code !== event?.code) {
      this.wasTranslationConfirmed = false;
    }
  }

  onDetectedLanguageChanged(event) {
    if (event?.code && this.previousDetectedLanguage?.code !== event?.code) {
      this.wasTranslationConfirmed = false;
    }

    if (event) {
      this.previousDetectedLanguage = event;
    }
  }

  onTranslationConfirmed(event) {
    this.wasTranslationConfirmed = event;
  }

  private countSearchFilters(): void {
    let count = 0;
    const searchFilters = this.filtersFormGroup;
    Object.keys(searchFilters['controls']).forEach(key => {
      if (MAIN_TECHNOLOGY_AREA_KEYS.includes(key)) {
        return;
      }

      if (key.indexOf('date') > -1 && searchFilters.get(key).value?.day > 0) {
        count++;
      } else if (searchFilters.get(key)?.value?.length > 0) {
        count++;
      }
    });
    if (!MAIN_TECHNOLOGY_AREA_KEYS.every(key => searchFilters.get(key).value)) {
      count++;
    }
    this.amountSearchFilters = count;
  }

  private countBoostFactors(): void {
    let count = 0;
    const boostFactors = this.form.get('boost_factors');
    Object.keys(boostFactors['controls']).forEach(key => {
      if (boostFactors.get(key)?.value?.length > 0) {
        count++;
      }
    });
    this.amountBoostFactors = count;
  }

  clearAllFilters() {
    this.refreshFilters();
    this.refreshChartFilters();
    this.clearAdvancedFilter();
    this.countSearchFilters();
    this.countBoostFactors();
    this.search();
  }

  private shouldShowTranslateBox(): boolean {
    const canTranslateAutomatically = this.userService.getUISetting('semantic_automatic_translations', false);
    const isLanguageNotSelected = !this.selectedLanguage?.code;
    const isEnglishSelected = this.selectedLanguage?.code === ENGLISH_CODE;
    return !(this.userService.isFreeUser() || isLanguageNotSelected || isEnglishSelected || canTranslateAutomatically ||
      this.canLoadSavedSearch() || this.wasTranslationConfirmed);
  }

  private openTranslateBox() {
    if (this.modalService.hasOpenModals()) {
      return;
    }

    this.wasTranslationConfirmed = false;
    const modal = this.modalService.open(TranslateLanguageDialogComponent, {
      modalDialogClass: 'translate-language-dialog',
      scrollable: false
    });
    modal.componentInstance.selectedLanguage = this.selectedLanguage;
    modal.componentInstance.detectedLanguage = this.detectedLanguage;
    modal.result.then((result) => {
      if (result) {
        this.selectedLanguage = result;

        if (this.detectedLanguage?.code !== this.selectedLanguage.code) {
          this.detectedLanguage = null;
          this.previousDetectedLanguage = null;
        }

        this.wasTranslationConfirmed = true;
        this.search();
      } else {
        this.wasTranslationConfirmed = false;
      }
    }, (reason) => {
      this.wasTranslationConfirmed = false;

      if (reason?.useOriginal) {
        this.selectedLanguage = ENGLISH_LANG;
        this.detectedLanguage = null;
        this.previousDetectedLanguage = null;
        this.search();
      }
    });
  }

  hasBoostFactors() {
    return this.form.get('boost_factors.keywords').value.length > 0 ||
           this.form.get('boost_factors.ipc_codes').value.length > 0 ||
           this.form.get('boost_factors.cpc_codes').value.length > 0;
  }

  openBoostSearch() {
    this.countBoostFactors();
    this.isBoostOpen = !this.isBoostOpen;
  }

  openUpgradeForm() {
    const button = this.header.nativeElement.querySelector('#btnUpgrade');
    if (button?.click) {
      button.click();
    }
  }

  private getUserStatistics() {
    if (!this.userService.isCollaboratorUser()) {
      return;
    }

    const userStatistics$ = this.userService.getStatistics().subscribe();
    this.subscriptions.add(userStatistics$);
  }
}
