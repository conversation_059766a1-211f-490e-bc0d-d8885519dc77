export const tooltip = {
  title: {
    patentSearch: 'Semantic Search',
    quantityCutOff: 'Quantity Cut-Off',
    similarityCutOff: 'Similarity Cut-Off',
    inclusionAnd: 'Inclusion (AND)',
    inclusionOr: 'Inclusion (OR)',
    exclusionAnd: 'Exclusion (AND)',
    exclusionOr: 'Exclusion (OR)',
    applicantPlus: 'Applicant (+)',
    applicantMinus: 'Applicant (-)',
    cpcCodePlus: 'CPC Code (+)',
    cpcCodeMinus: 'CPC Code (-)',
    ipcCodePlus: 'IPC Code (+)',
    ipcCodeMinus: 'IPC Code (-)',
    earliestPriorityDate: 'Earliest Priority Date',
    latestPriorityDate: 'Latest Priority Date',
    earliestPublicationDate: 'Earliest Publication Date',
    latestPublicationDate: 'Latest Publication Date',
    authoritiesPlus: 'Authority (+)',
    authoritiesMinus: 'Authority (-)',
    legalStatus: 'Legal Status'
  },

  text: {
    patentSearch: `<p><b>Patent number search</b></p>
        <p>Enter up to 5 patent numbers separated by commas to find patents with similar text.</p>
        <p><b>Text search</b></p>
        <p>Search by pasting English text, even if it's from non-English patents.</p>
        <p><b>Combination of text and publication numbers</b></p>
        <p>Simultaneously enter text and patent publication numbers, adjust weighting for emphasis.</p>`,

    quantityCutOff: `Enter the number of similar patents to display. You can make cuts from 2 to 1000 results.`,

    similarityCutOff: `Enter a similarity value to display the results with higher similarity score than this threshold.`,

    inclusionAnd: ` Enter keywords separated by commas. Patents with all entered keywords will be displayed.`,

    inclusionOr: `Enter keywords separated by commas. Patents with any of the entered keywords will be displayed.`,

    exclusionAnd: `Enter keywords separated by commas to exclude patents containing all of them.`,

    exclusionOr: `Enter keywords separated by commas to exclude patents containing any of them.`,

    applicantPlus: `Exclude patents from other applicants. Enter applicant names to display only relevant results, e.g., 'Siemens, General Electric, Samsung.`,

    applicantMinus: `Enter names to exclude patents from certain applicants, e.g., 'Siemens, General Electric, Samsung.`,

    cpcCodePlus: `Enter CPC-Codes separated by commas (e.g., G01J, G01K, G01L) to display relevant patents from section to subclass level.`,

    cpcCodeMinus: `Exclude CPC-Codes separated by commas (e.g., G01J, G01K, G01L) to refine your search.`,

    ipcCodePlus: `Enter IPC-Codes separated by commas (e.g., G01J, G01K, G01L) to filter relevant patents from section to subclass level.`,

    ipcCodeMinus: ` Exclude IPC-Codes separated by commas (e.g., G01J, G01K, G01L) to refine your search.`,

    earliestPriorityDate: `Enter a priority date to filter patents. For date ranges, use 'Earliest Priority Date' and 'Latest Priority Date' filters.`,

    latestPriorityDate: `Enter a priority date to filter patents. For date ranges, use both 'Earliest Priority Date' and 'Latest Priority Date' filters.`,

    earliestPublicationDate: `Enter a publication date to filter patents published after this date.`,

    latestPublicationDate: `Enter a publication date to filter patents published before this date.`,

    authoritiesPlus: `Enter relevant Authorities to exclude patents without them. Choose from suggested options.`,

    authoritiesMinus: `Exclude Authorities from your search. Patent families with any member in the specified Authority(/ies) will still be included. Choose from suggested options.`,
    legalStatus: `
        <p>
            Use the legal status of a patent family to filter your results. On the family level, these statuses can be:
        </p>

        <ul>
            <li><b>Alive:</b> The family has at least one granted or pending member.</li>
            <li><b>Dead:</b> The family has no granted or pending members.</li>
            <li><b>Unknown:</b> The family has only unknown members.</li>
        </ul>`,
  }
};
