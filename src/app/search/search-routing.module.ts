import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@core/guards';
import { PatentComponent } from './patent';
import { SharedGuard } from '@core/guards/shared.guard';

const routes: Routes = [
  {path: 'patent/shared', component: PatentComponent, canActivate: [SharedGuard]},
  {path: 'patent/:id', component: PatentComponent, canActivate: [AuthGuard], pathMatch: 'full'},
  {path: 'patent', component: PatentComponent, canActivate: [AuthGuard], pathMatch: 'full'},
  {path: '', redirectTo: 'patent', pathMatch: 'full'},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SearchRoutingModule {
}
