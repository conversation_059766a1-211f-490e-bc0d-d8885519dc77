import { NgModule } from '@angular/core';

import { SharedModule } from '../shared';
import { SearchRoutingModule } from './search-routing.module';

import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgbDatepickerModule, NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';

import { PatentComponent } from './patent';
import {
  ChartDashboardModule,
} from '@shared/charts';
import { NPLSearchModule } from 'app/npl-search/npl-search.module';
import { ChartsModule } from '@shared/charts/charts.module';

@NgModule({
  imports: [
    SharedModule,
    SearchRoutingModule,
    ChartsModule,
    ChartDashboardModule,
    NgxSliderModule,
    NgbDatepickerModule,
    NgbTypeaheadModule,
    NPLSearchModule
  ],
  exports: [
    ChartDashboardModule,
  ],
  declarations: [
    PatentComponent,
  ]
})
export class SearchModule {
}
