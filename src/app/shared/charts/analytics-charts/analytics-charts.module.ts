import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { SharedModule } from '@shared/shared.module';
import { MarketCoverageComponent } from './market-coverage/market-coverage.component';
import { PatentBroadnessComponent } from './patent-broadness/patent-broadness.component';
import { PatentValueComponent } from './patent-value/patent-value.component';
import { PatentQualityIndicationComponent } from './patent-quality-indication/patent-quality-indication.component';
import {
  NumberCitationsReferencesComponent
} from './number-citations-references/number-citations-references.component';
import { RecencyIndicationComponent } from './recency-indication/recency-indication.component';
import { NumberCitationsComponent } from './number-citations/number-citations.component';
import { NumberReferencesComponent } from './number-references/number-references.component';
import { PatentRiskComponent } from './patent-risk/patent-risk.component';
import {
  PatentValueOverRiskComponent
} from './patent-value-over-risk/patent-value-over-risk.component';

@NgModule({
  declarations: [
    MarketCoverageComponent,
    PatentBroadnessComponent,
    PatentValueComponent,
    PatentRiskComponent,
    PatentQualityIndicationComponent,
    NumberCitationsReferencesComponent,
    NumberCitationsComponent,
    NumberReferencesComponent,
    RecencyIndicationComponent,
    PatentValueOverRiskComponent
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    SharedModule
  ],
  exports: [
    MarketCoverageComponent,
    PatentBroadnessComponent,
    PatentValueComponent,
    PatentRiskComponent,
    PatentQualityIndicationComponent,
    NumberCitationsReferencesComponent,
    NumberCitationsComponent,
    NumberReferencesComponent,
    RecencyIndicationComponent,
    PatentValueOverRiskComponent
  ]
})
export class AnalyticsChartsModule {
}
