import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { InterpolateColorsPipe } from '@core/pipes';


@Component({
  selector: 'app-market-coverage',
  templateUrl: './market-coverage.component.html',
  styleUrls: ['./market-coverage.component.scss']
})
export class MarketCoverageComponent extends BaseChartComponent implements OnInit, OnDestroy {

  component = MarketCoverageComponent;
  chartName = 'analytics_patent_family';
  title = 'Number of authorities per patent family';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector,
    private interpolateColors: InterpolateColorsPipe,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const category = event.point.category;
    if (category === '>100') {
      filter.query = `(MARKET_COVERAGE>100)`;
    }

    const range = category.split('-');
    if (range.length > 1) {
      filter.query = `(MARKET_COVERAGE >= ${range[0]}) AND (MARKET_COVERAGE <= ${range[1]})`;
    } else {
      filter.query = `(MARKET_COVERAGE=${range[0]})`;
    }

    filter.value = category;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.xAxis && this.chart.series && this.datasource && this.datasource.xAxis && this.datasource.yAxis) {
      this.defineColor();
      this.datasource.xAxis = this.datasource.xAxis.map( v => v.replace(/^0+/, ''));
      this.chart.xAxis[0].setCategories(this.datasource.xAxis, true, true);
      this.chart.series[0].setData(this.datasource.yAxis, true, true, false);
      this.chart.data = [this.datasource.yAxis];
      this.reflowChart();
    }
  }
  private defineColor(){
    if(!this.datasource || !this.datasource.yAxis || this.datasource.yAxis.length <= 2 || isNaN(this.datasource.yAxis[0])){
      return;
    }
    const colors = this.interpolateColors.transform("rgb(140, 212, 200)", "rgb(124, 174, 213)", this.datasource.yAxis.length);
    this.datasource.yAxis = this.datasource.yAxis.map((v, i) => { return {y: v, color: colors[i]}})
  }
}
