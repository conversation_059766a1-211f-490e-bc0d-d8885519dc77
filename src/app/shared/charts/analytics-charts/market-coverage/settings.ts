import { chartsConfig } from './../../config';

export const chartSetting = {
  chart: {
    zoomType: 'xy',
    spacing: [10, 0, 10, 0],
    backgroundColor: chartsConfig.bgColor,
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {text: null},
  legend: {enabled: false},
  colors: ['#8CD4C8', '#7CAED5'],
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    }
  },
  series: [
    {
      color: '#8CD4C8',
      data: [],
      name: 'Number of patent families',
      type: 'column',
      yAxis: 0
    }
  ],
  xAxis: [{
    categories: [],
    crosshair: true,
    title: {
      text: 'Number of authorities',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  yAxis: [{
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return '<span>Number of authorities: <b>' + parseInt(this.x, 10) + '</b></span>' +
        '<br/><span>Number of patent families: <b>' + this.points[0].y + '</b></span>';
    },
    shared: true,
    useHTML: true
  },
};

export const tooltipText = `This graph illustrates the number of markets covered by the single patent family.<br>
The horizontal axis counts the number of authorities, and the vertical axis indicates the patent families that are present in that number of authorities.<br>
<b>Suggestion:</b><br>
Use this chart to assess the economic potential of patent families based on their market coverage. Patent families involving a larger number of markets may indicate broader applicability and commercial viability.`
