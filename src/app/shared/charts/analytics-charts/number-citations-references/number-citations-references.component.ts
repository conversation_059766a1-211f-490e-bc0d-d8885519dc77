import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-number-citations-references',
  templateUrl: './number-citations-references.component.html',
  styleUrls: ['./number-citations-references.component.scss']
})
export class NumberCitationsReferencesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NumberCitationsReferencesComponent;
  chartName = 'analytics_citations_references';
  title = 'Number of citations and references';
  chartOptions = settings.chartSetting;
  tooltipText = `<p>This graph displays how often a patent family has been referenced or cited.</p>
  <p>The <b>x-axis</b> shows the number of patent families.</p>
  <p>The <b>y-axis</b> shows how often the patent families have been referenced or cited.</p>`;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const name = event.point.series.name.toLowerCase();
    const category = event.point.category;
    const field = name === 'references' ? 'CITATION_BACKWARD_COUNT' : 'CITATION_FORWARD_COUNT';

    const range = category.split('-');

    if (range[0] === 0) {
      filter.query = `(${field}=null)`;
    } else if (category === '>100') {
      filter.query = `(${field}>100)`;
    } else if (range.length > 1) {
      filter.query = `(${field} >= ${range[0]}) AND (${field} <= ${range[1]})`;
    } else {
      filter.query = `(${field}=${range[0]})`;
    }
    filter.value = `Type: ${name}, value: ${category}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource.referenceCount, true, true, false);
      this.chart.series[1].setData(this.datasource.citationCount, true, true, false);
      this.chart.data = [this.datasource.referenceCount, this.datasource.citationCount];
      this.reflowChart();
    }
  }
}
