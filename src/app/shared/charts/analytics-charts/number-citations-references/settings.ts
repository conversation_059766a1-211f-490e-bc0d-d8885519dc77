import { chartsConfig } from './../../config'

const categories = ['0', '1', '2', '3', '4', '5', '6-10', '11-20', '21-30', '31-50', '51-100', '>100'];

export const chartSetting = {
  chart: {
    type: 'bar',
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  xAxis: [{
    categories: categories,
    reversed: false,
    labels: {
      step: 1,
      style: chartsConfig.textStyles
    }
  }, { // mirror axis on right side
    opposite: true,
    reversed: false,
    categories: categories,
    linkedTo: 0,
    labels: {
      step: 1,
      style: chartsConfig.textStyles
    }
  }],
  yAxis: {
    title: {
      text: null
    },
    labels: {
      formatter: function () {
        return this.value.toString().replace('-', '');
      },
      style: chartsConfig.textStyles
    }
  },

  legend: {
    itemStyle: chartsConfig.textStyles
  },

  plotOptions: {
    series: {
      stacking: 'normal'
    },
    bar: {
      allowPointSelect: false
    }
  },

  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `${this.point.category} ${this.series.name}(s):
                                <b>${this.point.y.toString().replace('-', '')}</b>`;
    }
  },

  series: [{
    name: 'References',
    data: [],
    color: '#7CAED5',
    style: chartsConfig.textStyles
  }, {
    name: 'Citations',
    data: [],
    color: '#00749F',
    labels: {
      style: chartsConfig.textStyles
    }
  }]
};
