<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="tooltipText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [urlTooltipArticle]="urlTooltipArticle.analytics" [isEmpty]="isEmpty"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
