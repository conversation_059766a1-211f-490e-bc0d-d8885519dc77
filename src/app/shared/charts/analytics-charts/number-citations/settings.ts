import { chartsConfig } from './../../config'
import pluralize from 'pluralize';

const categories = ['0', '1', '2', '3', '4', '5', '6-10', '11-20', '21-30', '31-50', '51-100', '>100'];

export const chartSetting = {
  chart: {
    type: 'column',
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: ["rgb(140, 212, 200)", "rgb(124, 174, 213)"],
  title: { text: null },
  legend: { enabled: false, },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
  },
  xAxis: [{
    title: {
      text: 'Number of citations',
      style: chartsConfig.textStyles
    },
    categories: categories,
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  yAxis: {
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const name = `Patent ${pluralize('family', this.point.y)}`;
      return `${name}: <b>${this.point.y}</b>`;
    }
  },
  series: [{
    name: 'Citation(s)',
    data: [],
    color: '#00749F',
    labels: {
      style: chartsConfig.textStyles
    }
  }]
};

export const tooltipText = `This graph provides insights into citations per patent family in the analyzed results.<br>
Citations are documents that mention a specific patent family after its various publications. The horizontal axis represents the citation count for each patent family, while the vertical axis displays the patent families.<br>
<b>Suggestion:</b><br>
Patent families with a higher number of citations are likely to be influential and have a significant impact within the relevant technology domain.`
