import { tooltipText } from './../../basic-charts/applicants/settings';
import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { InterpolateColorsPipe } from '@core/pipes';

@Component({
  selector: 'app-number-references',
  templateUrl: './number-references.component.html',
  styleUrls: ['./number-references.component.scss']
})
export class NumberReferencesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NumberReferencesComponent;
  chartName = 'analytics_references_count';
  title = 'Number of references';
  chartOptions = settings.chartSetting;
  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector,
    private interpolateColors: InterpolateColorsPipe,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const name = event.point.series.name.toLowerCase();
    const category = event.point.category;
    const field = 'CITATION_BACKWARD_COUNT';

    const range = category.split('-');

    if (range[0] === 0) {
      filter.query = `(${field}=null)`;
    } else if (category === '>100') {
      filter.query = `(${field}>100)`;
    } else if (range.length > 1) {
      filter.query = `(${field} >= ${range[0]}) AND (${field} <= ${range[1]})`;
    } else {
      filter.query = `(${field}=${range[0]})`;
    }
    filter.value = `Type: ${name}, value: ${category}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.defineColor();
      this.chart.series[0].setData(this.datasource.referenceCount, true, true, false);
      this.reflowChart();
    }
  }

  private defineColor(){
    if(this.datasource.referenceCount.length<=2 || isNaN(this.datasource.referenceCount[0])){
      return
    }
    const colors = this.interpolateColors.transform("rgb(140, 212, 200)", "rgb(124, 174, 213)", this.datasource.referenceCount.length);
    this.datasource.referenceCount = this.datasource.referenceCount.map((v, i) => { return {y: v, color: colors[i]}})
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.referenceCount.filter(x => x['y']).length < 1;
  }  
}
