import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-patent-broadness',
  templateUrl: './patent-broadness.component.html',
  styleUrls: ['./patent-broadness.component.scss']
})
export class PatentBroadnessComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = PatentBroadnessComponent;
  chartName = 'analytics_patent_broadness';
  title = 'Technology fields per patent families';
  chartOptions = settings.chartSetting;
  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        this.datasource = charts[this.chartName];
        if (!this.datasource) {
          return;
        }

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(TECHNOLOGY_BROADNESS=${event.point.category})`;
    filter.value = event.point.category;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.xAxis && this.datasource) {
      this.chart.xAxis[0].setCategories(this.datasource.frequency, true, true);
      this.chart.series[0].setData(this.datasource.values, true, true, false);
      this.reflowChart();
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }  
}
