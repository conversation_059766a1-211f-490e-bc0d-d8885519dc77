import { chartsConfig } from './../../config'

function tooltipBroadness(points) {
  const publicationQuantity = points[0].y;
  const areaQuantity = points[0].x;
  const indexAverage = Math.floor(points[0].y);
  let part1 = 'There are';
  let part2 = 'patent families';
  let part3 = areaQuantity === 1 ? 'field' : 'fields';

  if (publicationQuantity === 0) {
    return `There are no patent families with <b>${areaQuantity} technology ${part3}</b>.`;
  }

  if (publicationQuantity === 1) {
    part1 = 'There is';
    part2 = 'patent family';
  }

  return `${part1} <b>${publicationQuantity} ${part2}</b>
            with <b>${areaQuantity} technology ${part3}</b>`;
}

export const chartSetting = {
  chart: {
    type: 'column',
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  colors: ['#8CD4C8', '#84CCC9', '#7DC4CB', '#77BCCD', '#71B4CE', '#65A6D0', '#5E9FD1', '#5897D2', '#7CAED5'],
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    enabled: false,
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    }
  },
  series: [
    {
      // color: '#375484',
      colorByPoint: true,
      data: [],
      name: 'Number of patent families',
      type: 'column',
      yAxis: 0
    }
  ],
  title: {
    text: null
  },

  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const {points} = this;
      return tooltipBroadness(points);
    },
    shared: true,
    useHTML: true
  },
  xAxis: [{
    categories: [],
    crosshair: true,
    title: {
      text: 'Number of technology field',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  yAxis: [{
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }]
};

export const tooltipText = `This graph illustrates the number of <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">technology fields</a> relating to a single patent family.<br>
The horizontal axis displays the technology fields, and the vertical axis the number of corresponding patent families involved.<br>
<b>Suggestion:</b><br>
Use this chart to assess the technological scope of patent families. A larger number of technological fields covered may indicate broader applicability and commercial viability.`
