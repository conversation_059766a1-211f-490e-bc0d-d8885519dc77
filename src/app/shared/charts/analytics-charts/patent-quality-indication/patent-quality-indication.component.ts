import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-patent-quality-indication',
  templateUrl: './patent-quality-indication.component.html',
  styleUrls: ['./patent-quality-indication.component.scss']
})
export class PatentQualityIndicationComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = PatentQualityIndicationComponent;
  chartName = 'analytics_consistency_indication';
  title = 'Claims-description consistency';
  chartOptions = settings.chartSetting;
  tooltipText = `<p>This graph shows the similarity that exists between the description and the claims of a patent.</p>
  <p>A low similarity between them means that the claims have been formulated in very broad terms and the wording does not match the description. This is an indicator that the patent enjoys broader patent protection and consequently tends to be a valuable patent.</p>
  <p>A high similarity between them means that the wording between the claims and the description is very similar. This is an indicator that the patent has narrower patent protection.</p>`;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(CONSISTENCY<${1 - event.point.y} AND CONSISTENCY > 0)`; // Warning, consistency is inverted in the graph!
    filter.value = event.point.y;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }


  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.chart.data = [this.datasource];
      this.reflowChart();
    }
  }
}
