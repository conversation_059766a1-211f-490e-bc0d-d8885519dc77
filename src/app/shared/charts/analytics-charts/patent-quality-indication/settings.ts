import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  colors: ['#FF6700'],
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  xAxis: {
    title: {
      text: 'Consistency ranking',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    allowDecimals: false,
    title: {
      text: 'Consistency indication',
      style: chartsConfig.textStyles
    },
    min: 0,
    max: 1,
    plotLines: [{
      value: 0.25,
      label: {
        text: null
      },
      width: 1,
      color: '#d3d3d3'
    }, {
      value: 0.50,
      label: {
        text: null
      },
      width: 1,
      color: '#d3d3d3'
    }, {
      value: 0.75,
      label: {
        text: null
      },
      width: 1,
      color: '#d3d3d3'
    }],
    labels: {
      style: chartsConfig.textStyles
    }
  },
  legend: {
    enabled: false
  },
  plotOptions: {
    series: {
      allowPointSelect: false,
      cursor: 'pointer',
    }
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `Publication number: <b>${this.key}</b><br>Ranking: <b>${this.point.ranking}</b><br>Consistency indication: <b>${this.y}</b>`;
    }
  },
  series: [{
    name: 'Similarity',
    turboThreshold: 0, // disables 1000 items per serie limitation
    data: [],
    zones: [{
      color: chartsConfig.similarityColor
    }]
  }]
};
