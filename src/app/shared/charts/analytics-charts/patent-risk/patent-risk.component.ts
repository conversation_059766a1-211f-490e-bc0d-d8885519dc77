import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import addFunnel from 'highcharts/modules/funnel';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
addFunnel(Highcharts)

@Component({
  selector: 'app-patent-risk',
  templateUrl: './patent-risk.component.html',
  styleUrls: ['./patent-risk.component.scss']
})
export class PatentRiskComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = PatentRiskComponent;
  chartName = 'analytics_patent_risk';
  title = 'Patent risk';
  chartOptions = settings.chartSetting;
  labelMap = {
    0: {
      name: 'Low',
      color: 'rgb(140, 212, 200)'
    },
    1: {
      name: 'Medium',
      color: 'rgb(135,199,204)'
    },
    2: {
      name: 'High',
      color: 'rgb(129,187,209)'
    },
    3: {
      name: 'Very high',
      color: 'rgb(124, 174, 213)'
    }
  };

  tooltipText = settings.tooltipText;
  totalHits = 0;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get calculatingPatents(): number {
    return this.datasource ? this.datasource.map(item => item.y).reduce((a, b) => a + b, 0) : 0;
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.totalHits = chart['total_hits'];
        this.datasource = chart['values'];
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }
    filter.query = `(RISK=${event.point.x})`;
    filter.value = event.point.name;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.datasource.forEach(i => {
        i['name'] = this.labelMap[i['type']].name
        i['color'] = this.labelMap[i['type']].color
      });
      this.chart.series[0].options.labelMap = this.labelMap;
      this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.length < 1;
  }
}
