import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'pyramid',
    spacing: [10, 0, 30, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: { enabled: false },
  colors: ['#D3DBE7', '#92A1B7', '#506687', '#0F2C57'],
  title: { text: null },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<span>Patent value: <b>${this.key}</b></span><br>
              <span>Patent families: <b>${this.y}</b></span><br>` +
              `Percent:</span><span style="font-weight:bold">
                ${percent}%</span></div>`;
    },
    shared: true,
    useHTML: true
  },
  series: [{
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      distance: 10
    },
    data: []
  }],
  responsive: {
    rules: [{
      condition: { maxWidth: 600 },
      chartOptions: {
        series: [{
          dataLabels: {
            distance: 0
          },
        }]
      }
    }]
  },
};

export const tooltipText = `This diagram provides insights into the risk distribution of the patent families within your result list. Risk is computed using Octimine's proprietary algorithms and measures the likelihood of a patent or members of the patent family being involved in post-grant review, opposition, invalidation proceedings or litigation. The chart categorizes patent families into different risk classes based on their susceptibility to legal disputes. An "N/A" designation indicates no risk estimation available. Meanwhile, a family with a "very high" risk has a higher probability of triggering a legal dispute compared to a labeled "low" risk.<br>
<b>Suggestion:</b><br>
Scrutinize patent families that fall within the "very high" category for their vulnerability and economic value as they are likely attractive targets for competitors.`
