import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-patent-value-over-risk',
  templateUrl: './patent-value-over-risk.component.html',
  styleUrls: ['./patent-value-over-risk.component.scss']
})
export class PatentValueOverRiskComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = PatentValueOverRiskComponent;
  chartName = 'analytics_patent_value_over_risk';
  title = 'Patent value over risk';
  chartOptions = settings.chartSetting;
  colors = settings.colors;

  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart['values'];
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(RISK=${event.point.x}) AND (IMPACT=${event.point.y})`;
    filter.value = event.point.category;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const series = this.datasource.map(ds => {
        const serie = {
          cursor: 'pointer',
          data: [ds['data']],
          marker: {
            lineColor: 'rgba(0,125,100,0.1)',
            fillColor: this.colors[ds['type']],
            fillOpacity: .8
          }

        }
        return serie;
      })

      this.chart.update({series: series}, true, true);
      this.reflowChart();
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.length < 1;
  }  
}
