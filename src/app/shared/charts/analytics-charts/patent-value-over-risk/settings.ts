import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'bubble',
    spacing: [10, 0, 10, 0],
  },

  credits: chartsConfig.credits,
  exporting: {enabled: false},

  legend: {
    enabled: false
  },

  plotOptions: {
    bubble: {
      allowPointSelect: false,
      cursor: 'pointer',
      minSize: '4%',
      maxSize: '32%'
    }
  },

  title: {
    text: null
  },

  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const labelMap = {
        0: 'Low',
        1: 'Medium',
        2: 'High',
        3: 'Very high'
      };
      return `<span>Value: <b>${labelMap[this.point.y]}</b></span><br>
                                  <span>Risk: <b>${labelMap[this.point.x]}</b></span><br>
                                  <span>Patent families: <b>${this.point.z}</b></span>`;
    },
    shared: true,
    useHTML: true
  },

  xAxis: {
    maxPadding: 0,
    minPadding: 0,
    categories: ['Low', 'Medium', 'High', 'Very high'],
    gridLineWidth: 1,
    labels: {
      useHTML: true,
      formatter: function () {
        return `<div class="xAxisLabel">${this.value}</div>`;
      },
      enabled: true,
      style: {
        color: chartsConfig.labelColor
      }
    },
    minTickInterval: 1,
    title: {
      text: 'Risk',
      style: {
        color: chartsConfig.labelColor
      }
    }
  },

  yAxis: {
    min: 0,
    maxPadding: 0,
    minPadding: 0,
    endOnTick: false,
    startOnTick: false,
    categories: ['Low', 'Medium', 'High', 'Very high'],
    gridLineWidth: 1,
    labels: {
      useHTML: true,
      formatter: function () {
        return `<div class="yAxisLabel">${this.value}</div>`;
      },
      enabled: true,
      rotation: -45,
      style: {
        color: chartsConfig.labelColor
      }
    },
    lineWidth: 1,
    minTickInterval: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      enabled: true,
      text: 'Value',
      style: {
        color: chartsConfig.labelColor
      }
    }
  },
  series: [{data: []}],
};

export const colors = {
  '0,0': 'rgb(211, 219, 231, 0.7)',
  '0,1': 'rgb(168, 183, 206, 0.8)',
  '0,2': 'rgb(125, 148, 182, 0.9)',
  '0,3': 'rgb(39, 77, 133, 1)',
  '1,0': 'rgb(211, 219, 231, 0.7)',
  '1,1': 'rgb(168, 183, 206, 0.8)',
  '1,2': 'rgb(125, 148, 182, 0.9)',
  '1,3': 'rgb(39, 77, 133, 1)',
  '2,0': 'rgb(211, 219, 231, 0.7)',
  '2,1': 'rgb(168, 183, 206, 0.8)',
  '2,2': 'rgb(125, 148, 182, 0.9)',
  '2,3': 'rgb(39, 77, 133, 1)',
  '3,0': 'rgb(211, 219, 231, 0.7)',
  '3,1': 'rgb(168, 183, 206, 0.8)',
  '3,2': 'rgb(125, 148, 182, 0.9)',
  '3,3': 'rgb(39, 77, 133, 1)'
};

export const tooltipText = `The chart combines the value and risk of the analyzed results.<br>
Both parameters are computed using Octimine's proprietary algorithms. "Value" is an estimated monetary appraisal, while "risk" measures the likelihood of a patent or members of the patent family being involved in post-grant review, opposition, invalidation proceedings or litigation.<br>
<b>Suggestion:</b><br>
Use this grouping to identify the patent families most likely to stir up commercial and rival interest, focusing your attention on the "very high" categories.`
