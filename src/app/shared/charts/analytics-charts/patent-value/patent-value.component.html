<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="tooltipText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [urlTooltipArticle]="urlTooltipArticle.analytics" [isEmpty]="isEmpty"
                     [chartItemHeight]="chartItemHeight">
  <div class="h-100 d-flex flex-column align-items-center">
    <highcharts-chart
      (chartInstance)="onChartInstance($event)"
      (click)="selectPoint($event)"
      [Highcharts]='Highcharts'
      [options]='chartOptions'
      constructorType='chart'
      style="display: block;">
    </highcharts-chart>

    <div class="color-global-dennemeyer-subtle content-body-small">
      Value calculated for {{ calculatingPatents }} {{ 'patent family' | pluralize: calculatingPatents }} ({{totalHits}} total)
    </div>
  </div>
</app-base-card-chart>
