import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'pyramid',
    spacing: [10, 0, 30, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: { enabled: false },
  colors: ['#D3DBE7', '#92A1B7', '#506687', '#0F2C57'],
  title: { text: null },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<span>Patent value: <b>${this.key}</b></span><br>` +
              (this.series.options.showTooltipPatentFamilies ? `<span>Patent families: <b>${this.y}</b></span><br>` : '') +
              `Percent:</span><span style="font-weight:bold">
                ${percent}%</span></div>`;

    },
    shared: true,
    useHTML: true
  },
  series: [{
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      distance: 10,
    },
    data: []
  }],
  responsive: {
    rules: [{
      condition: { maxWidth: 600 },
      chartOptions: {
        series: [{
          dataLabels: {
            distance: 0
          },
        }]
      }
    }]
  },
};

export const secondaryChartSetting = {
  chart: {
    backgroundColor: chartsConfig.secondaryBgColor,
    type: 'pyramid',
    spacing: [10, 0, 30, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: { enabled: false },
  colors: ['#D3DBE7', '#92A1B7', '#506687', '#0F2C57'],
  title: { text: null },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<span>Patent value: <b>${this.key}</b></span><br>` +
              (this.series.options.showTooltipPatentFamilies ? `<span>Patent families: <b>${this.y}</b></span><br>` : '') +
              `Percent:</span><span style="font-weight:bold">
                ${percent}%</span></div>`;
    },
    shared: true,
    useHTML: true
  },
  series: [{
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      distance: 10,
    },
    data: []
  }],
  responsive: {
    rules: [{
      condition: { maxWidth: 600 },
      chartOptions: {
        series: [{
          dataLabels: {
            distance: 0
          },
        }]
      }
    }]
  },
};

export const tooltipText = `This diagram provides insights into the value distribution of the patent families within your result list.<br>
A patent family's monetary value is estimated using Octimine's proprietary algorithms, which are calibrated using a combination of data from inventor surveys, auctions, licensing agreements and renewals. The chart categorizes the patent families into different impact classes based on their likely economic significance.<br>
<b>Suggestion:</b><br>
Pay close attention to patent families that fall within the "very high" category as they are likely to be outstanding in their respective technology fields.`
