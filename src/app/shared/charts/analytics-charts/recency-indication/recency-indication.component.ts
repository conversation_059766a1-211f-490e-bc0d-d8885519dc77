import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { InterpolateColorsPipe } from '@core/pipes';

@Component({
  selector: 'app-recency-indication',
  templateUrl: './recency-indication.component.html',
  styleUrls: ['./recency-indication.component.scss']
})
export class RecencyIndicationComponent extends BaseChartComponent implements OnInit, OnDestroy {

  component = RecencyIndicationComponent;
  chartName = 'analytics_recency_indication';
  title = 'Patent recency';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;

  private categories: Array<string>;

  constructor(
    protected injector: Injector,
    private interpolateColors: InterpolateColorsPipe,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;
        this.categories = chart.categories;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    // Periodo 4-6 was arriving as 3-6 from microservices, just fixing the issue until to be solved in microservices
    const label = event.point.label === '3-6' ? '4-6' : event.point.label;

    const period = label.split('-').map((year) => parseInt(year, 10));

    filter.query = `(RECENCY_YEARS>=${period[0]}) AND (RECENCY_YEARS<=${period[1]})`;
    filter.value = event.point.category;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.xAxis && this.datasource) {
      this.defineColor();
      this.chart.xAxis[0].setCategories(this.categories);
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }

  private defineColor(){
    if(this.categories.length<=2){
      return
    }
    const colors = this.interpolateColors.transform("rgb(124, 174, 213)", "rgb(140, 212, 200)", this.categories.length);
    this.datasource.forEach((v, i)=> { v['color']= colors[i]; });
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.length < 1;
  }  
}
