import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'column',
    spacing: [10, 0, 10, 0],
  },
  colors: ["rgb(124, 174, 213)", "rgb(140, 212, 200)"],
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: { enabled: false },
  title: { text: null },
  xAxis: {
    categories: [],
    title: {
      text: 'Years between patent priority date and its references',
      style: chartsConfig.textStyles
    },
    labels: {
      enabled: true,
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    min: 0,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
    labels: {
      enabled: true,
      style: chartsConfig.textStyles
    }
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter() {
      return `
                <span>Patent families: </span>
                <span style="font-weight: bold;">${this.point.y}</span><br>
                <span>Recency Indicator: </span>
                <span style="font-weight: bold;">${this.point.category}</span>`;
    },

  },
  series: [{data: []}],
  plotOptions: {
    column: {
      cursor: 'pointer',
      allowPointSelect: false,
    },
    series: {
      pointPadding: 0,
      groupPadding: 0
    }
  }
};

export const tooltipText = `This chart visualizes the innovation rates in your analyzed results.<br>
It measures the median time between the filing dates of a patent and those of its references. A shorter median duration indicates patents building on one another within a short duration.<br>
<b>Suggestion:</b><br>
Patent families of greater recency may indicate fresh trends, innovations or improvements, potentially offering competitive advantages over older technologies.`
