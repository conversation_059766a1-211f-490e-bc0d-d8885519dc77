import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';

import { AuthoritiesMapComponent } from './authorities-map/authorities-map.component';
import { MainAuthoritiesComponent } from './main-authorities/main-authorities.component';

@NgModule({
  declarations: [
    AuthoritiesMapComponent,
    MainAuthoritiesComponent,
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    AuthoritiesMapComponent,
    MainAuthoritiesComponent,
  ]
})
export class AuthoritiesChartsModule {
}
