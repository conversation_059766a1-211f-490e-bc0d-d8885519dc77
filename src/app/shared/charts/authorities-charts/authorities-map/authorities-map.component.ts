import { Component, Injector, Input, OnDestroy, OnInit } from '@angular/core';
import * as settings from './settings';
import * as Highcharts from 'highcharts';

import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import addmap from 'highcharts/modules/map';
import { COUNTRY_FLAG, FlagSizeEnum, PO_LIST } from '@core/services/patent/utils/countryCode';
import { Tooltip } from 'bootstrap';

addmap(Highcharts);

@Component({
  selector: 'app-authorities-map',
  templateUrl: './authorities-map.component.html',
  styleUrls: ['./authorities-map.component.scss']
})
export class AuthoritiesMapComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = AuthoritiesMapComponent;
  chartName = 'authorities_authorities_map';
  @Input() title = 'Authorities map';
  @Input() chartOptions: any = settings.chartSetting;
  @Input() showTooltipPatentFamilies = true;
  @Input() showLegend = true;

  tooltipText = settings.tooltipText;
  totalItems = 0;
  updateFlag = false;

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = this.secondarySource ? this.secondarySource : charts[this.chartName];
        if (!chart) {
          return;
        }
        this.chartOptions.series[0]['data'] = chart.dataSource;
        this.chartOptions.series[0]['totalHits'] = chart.total;
        this.chartOptions.series[0]['showTooltipPatentFamilies'] = this.showTooltipPatentFamilies;
        this.chartOptions.legend.enabled = this.showLegend;

        this.totalItems = chart.total;
        if (!this.updateFlag) {
          setTimeout(() => {
            this.datasource = chart.dataSource;
            this.updateFlag = true;
          }, 100);
        }
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupFlagsTooltip();
    });
  }

  private setupFlagsTooltip() {
    if(this.datasource && this.chart?.renderer){
      Highcharts.AST.allowedAttributes.push('data-bs-toggle');
      Highcharts.AST.allowedAttributes.push('data-bs-html');
      Highcharts.AST.allowedAttributes.push('data-bs-title');
      Highcharts.AST.allowedAttributes.push('data-hs-point');
      const poList = this.datasource.filter(v => v[0] in PO_LIST);

      const flagGroup = this.chart.renderer.g('flag-group').add();
      if (!this.showLegend) {
        flagGroup.addClass('legend-invisible');
      }
      const flagBox = this.chart.renderer.g('flag-box').add(flagGroup);
      poList.forEach((v, i) => {
        const labelHtml = `<div class="tooltip-authorities-map-flag" data-bs-title='${this.getFlagTooltipText(v[0], v [1])}'><i data-hs-point="${v[0]}" class="${COUNTRY_FLAG(v[0], FlagSizeEnum.MD)}"></i></div>`;
        this.chart.renderer.label(labelHtml, (40*i), -35, null, null, null, true)
          .css({ fontSize: '18pt', cursor: 'pointer'})
          .add(flagBox);
      });

      Array.from(document.querySelectorAll(".tooltip-authorities-map-flag")).forEach(el => {
        if(!Tooltip.getInstance(el)){
          new Tooltip(el, {
            html: true,
            container: 'app-authorities-map',
            customClass: 'map-tooltip',
            title: function () {
              return _.unescape($(this).data('bs-title'));
            }
          });
        }
      })
    }
  }

  private getFlagTooltipText(code: string, count: number){
    var s = `<span style="font-size:16px; font-weight:bold"><i class="bg-white ${COUNTRY_FLAG(code, FlagSizeEnum.MD)}"></i> ${PO_LIST[code].name}</span><br/>`;
    if (this.showTooltipPatentFamilies) {
      s += `<span style="font-size:16px;">Patent families: </span><span style="font-size:16px; font-weight:bold">${count}</span><br/>`;
    }
    s += `<span style="font-size:16px;">Percent: </span><span style="font-size:16px; font-weight:bold">${((count / this.totalItems) * 100).toFixed(2)}%</span>`;
    return s;
  }

  selectPoint(event) {
    if (this.disableFilter) {
      return;
    }
    const value = event.target.getAttribute("data-hs-point") ? event.target.getAttribute("data-hs-point"): event?.point?.properties['iso-a2'];
    if (!value) { return; }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(AUTHORITIES=${value})`;
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }
    this.storeService.setFilters(filters);
  }
}
