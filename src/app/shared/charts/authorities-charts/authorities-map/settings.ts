import { chartsConfig } from './../../config';
import worldMap from "@highcharts/map-collection/custom/world.geo.json";
import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';

export const chartSetting = {
  chart: {
    map: worldMap,
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: { enabled: false },
  title: { text: null },
  mapNavigation: {
    enabled: true,
    buttonOptions: {
      align: 'bottom'
    }
  },
  legend: {
    symbolWidth: 360,
    title: {
      text: 'Number of patent families'
    }
  },
  colorAxis: {
      min: 0,
      minColor: chartsConfig.colorGradient.min,
      maxColor: chartsConfig.colorGradient.max,
      labels: {
        style: chartsConfig.textStyles
      }
  },
  tooltip: {
    useHTML: true,
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold"><i class="${COUNTRY_FLAG(this.point.properties['iso-a2'], FlagSizeEnum.MD)}"></i> ${this.point.name}</span><br/>` +
      (this.series.options.showTooltipPatentFamilies ?
        `<span>Patent families: </span><span style="font-weight:bold">${this.point.value}</span><br/>` : '') +
        `<span>Percent: </span><span style="font-weight:bold">
        ${((this.point.value / this.series.options.totalHits) * 100).toFixed(2)}%</span>`;
    }
  },
  series: [{
    type: "map",
    allAreas: true,
    name: "Number of patents:",
    keys: ['iso-a2', 'value'],
    cursor: 'pointer',
    joinBy: 'iso-a2',
    states: {
      select: {
        color: '#7CAED5'
      },
      hover: {
        color: '#7CAED5'
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function () {
        return '';//this.point.value ? `${this.point.name}` : null;
      }
    },
    data: [],
    totalHits: 0
  }]
}
export const secondaryChartSetting = {
  chart: {
    map: worldMap,
    backgroundColor: chartsConfig.secondaryBgColor,
    spacing: [0, 0, 0, 0],
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: { enabled: false },
  title: { text: null },
  mapNavigation: {
    enabled: true,
    buttonOptions: {
      align: 'bottom'
    }
  },
  legend: {
    symbolWidth: 360,
    title: {
      text: 'Number of patent families'
    }
  },
  colorAxis: {
      min: 0,
      minColor: chartsConfig.colorGradient.min,
      maxColor: chartsConfig.colorGradient.max,
      labels: {
        style: chartsConfig.textStyles
      }
  },
  tooltip: {
    useHTML: true,
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold"><i class="${COUNTRY_FLAG(this.point.properties['iso-a2'], FlagSizeEnum.MD)}"></i> ${this.point.name}</span><br/>` +
        (this.series.options.showTooltipPatentFamilies ?
          `<span>Patent families: </span><span style="font-weight:bold">${this.point.value}</span><br/>` : '') +
        `<span>Percent: </span><span style="font-weight:bold">
        ${((this.point.value / this.series.options.totalHits) * 100).toFixed(2)}%</span>`;
    }
  },
  series: [{
    type: "map",
    allAreas: true,
    name: "Number of patents:",
    keys: ['iso-a2', 'value'],
    cursor: 'pointer',
    joinBy: 'iso-a2',
    states: {
      select: {
        color: '#7CAED5'
      },
      hover: {
        color: '#7CAED5'
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function () {
        return '';//this.point.value ? `${this.point.name}` : null;
      }
    },
    data: [],
    totalHits: 0
  }]
}

export const tooltipText = `This map displays the number of patents for each authority.<br>
The color intensity is based on the number of patent families that cover that specific jurisdiction, represented in absolute values and as a percentage of all analyzed patents. Organizations not related to a specific country are shown in the bottom area.<br>
<b>Suggestion:</b><br>
Use this interactive chart to assess activity in each country with respect to the analyzed patents. A patent family's global market presence directly correlates with its economic impact.`
