import { tooltipText } from './../../basic-charts/applicants/settings';
import { Component, Injector, OnDestroy, OnInit } from '@angular/core';
import _ from 'lodash';
import * as settings from './settings';
import * as Highcharts from 'highcharts';
import addHeatmap from 'highcharts/modules/heatmap';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
addHeatmap(Highcharts);

@Component({
  selector: 'app-main-authorities',
  templateUrl: './main-authorities.component.html',
  styleUrls: ['./main-authorities.component.scss']
})
export class MainAuthoritiesComponent  extends BaseChartComponent implements OnInit, OnDestroy {
  component = MainAuthoritiesComponent;
  chartName = 'authorities_main_authorities';
  title = 'Main authorities';
  chartOptions = settings.chartSetting;
  tooltipText = settings.tooltipText;
  totalItems = 0;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;
        this.totalItems = chart.total;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.totalItems;
      let x = 0, y = 0;
      const values = this.datasource.map((e, i, arr) => {
        e['x'] = x
        e['y'] = y
        y += 1;
        if( y > 5){
          x += 1
          y = 0
        }
        return e;
      });
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  selectPoint(event) {
    if (!event.point || !event.point.name) { return; }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const value = event.point.name;

    filter.query = `(AUTHORITIES=${value})`;
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }
    this.storeService.setFilters(filters);
  }
}
