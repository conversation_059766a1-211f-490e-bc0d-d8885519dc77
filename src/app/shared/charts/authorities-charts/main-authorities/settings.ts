import { COUNTRY_FLAG, FlagSizeEnum, ISO_COUNTRIES } from '@core/services/patent/utils/countryCode';
import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360,
    itemStyle: chartsConfig.textStyles
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false,},
  title: {text: null},
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const title = this.point.name.toUpperCase() in ISO_COUNTRIES ? ISO_COUNTRIES[this.point.name.toUpperCase()] : this.point.name;
      return `<span style="font-weight:bold"><i class="${COUNTRY_FLAG(this.point.name, FlagSizeEnum.MD)}"></i> ${title}</span><br/>
        <span>Patent families: </span><span style="font-weight:bold">${this.point.value}</span><br/>
        <span>Percent: </span><span style="font-weight:bold">(${((this.point.value / this.point.total) * 100).toFixed(2)}%)</span>`;
    },
  },
  series: [{
    allowPointSelect: false,
    borderColor: '#AE6800',
    borderWidth: 0.1,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: '#7CAED5'
      },
      hover: {
        color: '#7CAED5'
      },
    },
    dataLabels: {
      enabled: true,
      useHTML: true,
      style: {
        textAlign: 'center',
        verticalAlign: 'middle',
        ...chartsConfig.dataLabelStylesHeatMap
      },
      formatter() {
        const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
        let name = this.point.name.toUpperCase() in ISO_COUNTRIES ? ISO_COUNTRIES[this.point.name.toUpperCase()] : this.point.name;
        if(name.length > 20){
          name = `${name.slice(0, 17)}...`;
        }
        return `<span>${name} <i class="${COUNTRY_FLAG(this.point.name, FlagSizeEnum.MD)}"></i></span><br/><span>${this.point.value}</span> <span>(${percent}%)</span>`;
      },
    },
    abbreviations: [],
    totalHits: 0,
  }],
  yAxis: {visible: false},
  xAxis: {visible: false},
  responsive: {
    rules: [{
        condition: { maxWidth: 750 },
        chartOptions: {
          series: [{
            dataLabels: {
              style: {...chartsConfig.dataLabelStylesHeatMap, fontSize: '10px'},
              formatter() {
                const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
                return `<span>${this.point.name} <i class="${COUNTRY_FLAG(this.point.name, FlagSizeEnum.MD)}"></i></span><br/><span>${this.point.value}</span> <span>(${percent}%)</span>`;
              },
            },
          }]
        }
    }]
  },
};

export const tooltipText = `This chart is a heatmap of the main patent authorities relevant to the analyzed patents, listed alphabetically.<br>
Each cell in the heatmap represents a specific patent authority. The color intensity is based on the number of patent families covered by that specific jurisdiction, represented in absolute values and as a percentage of all analyzed patents.<br>
<b>Suggestion:</b><br>
This chart reveals the main authorities appearing in your analyzed patents. Click on a specific cell to filter accordingly.`

