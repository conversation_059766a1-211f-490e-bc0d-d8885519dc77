import * as Highcharts from 'highcharts';
import { Component, Injector, Input, NgZone } from '@angular/core';
import { BaseStoreService } from '@core/store';
import { Subscription } from 'rxjs';
import { ApplicantsAliasesService, ChartsService } from '@core';
import { Chart } from '../types';
import _ from 'lodash';

@Component({
  selector: 'app-base-chart',
  templateUrl: './base-chart.component.html',
  styleUrls: ['./base-chart.component.scss']
})
export class BaseChartComponent {
  @Input() showFavoriteOption: boolean = true;
  @Input() showZoomOption: boolean = true;
  @Input() disableFilter: boolean;
  @Input() secondarySource: any;
  @Input() storeService: BaseStoreService;
  @Input() exportable = true;
  @Input() exportCssSelector = '.chart-content';
  @Input() chartItemHeight: string = null;
  @Input() chartInstanceHeight: string = null;

  component;
  Highcharts: typeof Highcharts = Highcharts;
  chartName: string;
  title: string;
  datasource: any;
  chart: any;
  chartOptions;
  colors;
  tooltipText: string;
  popupText: string;
  isCalculating = false;
  adjustLegend: boolean = false;
  chartRef: Chart;
  urlTooltipArticle = {
    applicants: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88341-applicants',
    authorities: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88347-authorities',
    technologies: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88342-technologies',
    classifications: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88343-classification',
    sustainabilities: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88344-sustainability',
    analytics: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88345-analytics',
    others: 'https://intercom-help.eu/dennemeyer-octimine/en/articles/88346-others',
  }

  protected ngZone: NgZone;
  protected chartsService: ChartsService;
  protected applicantsAliasesService: ApplicantsAliasesService;
 

  protected subscriptions = new Subscription();

  private reflowChartIntervalId = null;

  constructor(
    protected injector: Injector
  ) {
    this.ngZone = this.injector.get(NgZone);
    this.chartsService = this.injector.get(ChartsService);
    this.applicantsAliasesService = this.injector.get(ApplicantsAliasesService);
  }

  subscribeChartEvents(): void {
    const singleChartColumn$ = this.storeService.singleChartColumn$.subscribe({
      next: () => {
        this.reflowChart();
      }
    });
    this.subscriptions.add(singleChartColumn$);

    const patentListViewMode$ = this.storeService.patentListViewMode$.subscribe({
      next: () => {
        this.reflowChart();
      }
    });
    this.subscriptions.add(patentListViewMode$);
  }

  notifyGetAllApplicantAliases() {
    this.applicantsAliasesService.subscribeGetAllApplicantAliases();
    this.applicantsAliasesService.notifyGetAllApplicantAliases(false);
  }

  notifyGetAllOwnerAliases() {
    this.applicantsAliasesService.subscribeGetAllApplicantAliases();
    this.applicantsAliasesService.notifyGetAllApplicantAliases(false);
  }

  unsubscribe(): void {
    this.subscriptions.unsubscribe();
    this.clearReflowChartInterval();
  }

  reflowChart() {
    this.clearReflowChartInterval();

    const self = this;
    let countChartNotInitializedRetries = 0;
    let countChartNotDisplayedRetries = 0;
    this.reflowChartIntervalId = setInterval(() => {
      const chartEle = self.chart?.container?.id ? document.getElementById(self.chart.container.id) : null;
      if (chartEle) {
        const rect = self.chart.container.getBoundingClientRect();
        if (rect && rect.width > 0 && rect.height > 0) {
          self.clearReflowChartInterval();
          try {
            self.chart?.reflow();
            self.onAdjustLegend();
          } catch (e) {
            console.warn(e);
          }
        } else {
          if (countChartNotDisplayedRetries > 20) {
            self.clearReflowChartInterval();
          }
          countChartNotDisplayedRetries += 1;
        }
      } else {
        if (countChartNotInitializedRetries > 20) {
          self.clearReflowChartInterval();
        }

        countChartNotInitializedRetries += 1;
      }
    }, 50);
  }

  onAdjustLegend() {
    if (this.adjustLegend) {
      this.chart.legend.update({
        symbolHeight: this.chart.chartHeight - 50,
      })
    }
  }

  updateChartOptions() {
    if (!this.chartOptions) {
      this.chartOptions = {};
    }

    if (!this.chartOptions.chart) {
      this.chartOptions.chart = {};
    }

    if (this.chartInstanceHeight) {
      this.chartOptions.chart.height = this.chartInstanceHeight;
    }
  }

  protected setupLabelsTooltip() {
    Highcharts.AST.allowedAttributes.push('data-original-title');

    let hideTooltipTimeoutId = null;
    let showTooltipTimeoutId = null;
    const self = this;

    const clearTimeoutIds = () => {
      if (hideTooltipTimeoutId) {
        clearTimeout(hideTooltipTimeoutId);
        hideTooltipTimeoutId = null;
      }
      if (showTooltipTimeoutId) {
        clearTimeout(showTooltipTimeoutId);
        showTooltipTimeoutId = null;
      }
    }

    $(document).on('mouseenter', self.shownTooltipCssSelector(), () => {
      clearTimeoutIds();
    });
    $(document).on('mouseleave', self.shownTooltipCssSelector(), () => {
      clearTimeoutIds();
      $(self.tooltipToggleCssSelector()).tooltip('hide');
    });

    Array.from(document.querySelectorAll(self.tooltipToggleCssSelector())).forEach(el => {
      $(el).tooltip('dispose');
      $(el).tooltip({
        html: true,
        title: function () {
          return _.unescape(self.labelTooltipTitle($(this)));
        },
        template: self.labelTooltipTemplate(),
        trigger: 'manual'
      });
      $(el).on('mouseenter', (e) => {
        e.preventDefault();
        clearTimeoutIds();
        $(self.tooltipToggleCssSelector()).tooltip('hide');
        showTooltipTimeoutId = setTimeout(() => {
          $(el).tooltip('show');
          showTooltipTimeoutId = null;
        }, 100);
      });
      $(el).on('mouseleave', (e) => {
        e.preventDefault();
        clearTimeoutIds();
        hideTooltipTimeoutId = setTimeout(() => {
          $(el).tooltip('hide');
          hideTooltipTimeoutId = null;
        }, 3000);
      });
    });
  }

  protected tooltipToggleCssSelector(): string {
    return null;
  }

  protected shownTooltipCssSelector(): string {
    return '.tooltip.ipc-cpc-tooltip.show';
  }

  protected labelTooltipTemplate(): string {
    return '<div class="tooltip ipc-cpc-tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>';
  }

  protected labelTooltipTitle(ele): string {
    return ele.data('original-title');
  }

  private clearReflowChartInterval() {
    if (this.reflowChartIntervalId) {
      clearInterval(this.reflowChartIntervalId);
    }
  }

  get isEmpty(): boolean {
    return true;
  }
}
