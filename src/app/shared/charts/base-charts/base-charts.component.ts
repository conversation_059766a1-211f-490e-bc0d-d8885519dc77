import { Component, Injector, Input } from '@angular/core';
import { BaseStoreService } from '@core/store';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-base-charts',
  templateUrl: './base-charts.component.html',
  styleUrls: ['./base-charts.component.scss']
})
export class BaseChartsComponent {
  @Input() isColumnLayout: boolean;
  @Input() storeService: BaseStoreService;

  protected subscriptions = new Subscription();

  constructor(
    protected injector: Injector
  ) {
  }
}
