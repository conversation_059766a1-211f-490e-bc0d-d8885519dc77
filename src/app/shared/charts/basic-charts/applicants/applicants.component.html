<app-base-card-chart (sliderChanged)="onUserChangeEnd()" [(sliderValue)]="quantity" [chartName]="chartName"
                     [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [sliderOptions]="sliderOptions"
                     [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [chartItemHeight]="chartItemHeight"
                     [urlTooltipArticle]="urlTooltipArticle.applicants">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    [Highcharts]='Highcharts'
                    [hidden]="error || isCalculating"
                    [options]='chartOptions'
                    constructorType='chart'
                    style="display: block;">
  </highcharts-chart>
  <app-alert *ngIf="error" [hidden]="isCalculating" [message]="error" type="danger"></app-alert>
  <div *ngIf="isCalculating" class="d-flex justify-content-center align-items-center spinner" style="height: 100%;">
    <span class="spinner-wrapper"></span>
    <img alt="Please hold" src="assets/images/octimine_blue_spinner.gif">
  </div>
</app-base-card-chart>
