import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { take } from 'rxjs/operators';
import * as Highcharts from 'highcharts';
import { Options } from '@angular-slider/ngx-slider';
import * as settings from './settings';
import * as popupText from '../../popup-information';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { enquoteClauseTextValue, joinOr } from '@core/services/boolean-search/utils';

@Component({
  selector: 'app-applicants',
  templateUrl: './applicants.component.html',
  styleUrls: ['./applicants.component.scss']
})
export class ApplicantsComponent extends BaseChartComponent implements OnInit, OnDestroy {

  isCalculating = false;

  component = ApplicantsComponent;
  chartName = 'basic_top_applicants';
  title = 'Applicants distribution';
  dataSource = [];
  chartOptions = settings.chartSetting;

  quantity = 5;

  sliderOptions: Options = settings.sliderSetting;

  top5 = [];
  totalItems = 0;

  tooltipText = settings.tooltipText;
  popupText = popupText.basicApplicants;
  /**
   * Error container
   */
  public error: any;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {

    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        this.dataSource = [];
        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }
        if (chart.error) {
          this.error = chart.error;
          return;
        }
        this.error = undefined;

        this.dataSource = chart.datasource;
        this.top5 = chart.top;
        this.totalItems = chart.total;
        if (!this.dataSource) {
          return;
        }

        this.setOptionsSlider();

        this.updateChartData();

        this.isCalculating = false;
      }
    });
    this.subscriptions.add(charts$);

    const filterRemoved$ = this.storeService.filterRemoved$.subscribe({
      next: item => {
        if (item['type'] && item['type'] === 'chart' && item['chart'] === this.chartName) {
          this.removeChartFilter(item);
        }
      }
    });
    this.subscriptions.add(filterRemoved$);

    super.updateChartOptions();
    super.subscribeChartEvents();
    super.notifyGetAllApplicantAliases();
  }

  setOptionsSlider() {
    const lenDataSource = this.dataSource.filter(item => item.name !== 'Others' && item.name !== 'N/A').length;
    if (lenDataSource !== this.quantity) {
      this.quantity = lenDataSource;
    }

    if (this.storeService.filters.filter(item => item.chart === this.chartName).length) {
      this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: true});
    } else {
      this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: false});
    }
  }

  selectPoint(event) {
    if (!event.point || event.point.name === 'N/A') {
      return;
    }

    this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: true});

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = {...filters[index]};
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        othersLevel: this.quantity,
        othersNames: [],
        type: 'chart'
      };
    }

    if (event.point.name === 'Others') {
      if (filter.othersLevel >= 50) {
        return;
      }

      filter.value = `Others ${filter.othersLevel + 1} - ${filter.othersLevel + this.quantity}`;
      filter.othersLevel += this.quantity;

      filter.othersNames = [...filter.othersNames,
        ...(this.dataSource.filter(d => d.name !== 'N/A' && d.name !== 'Others').map(d => d.name))];
      const applicantsQuery = enquoteClauseTextValue(joinOr(this.applicantsAliasesService.getOriginalApplicants(filter.othersNames)));
      filter.query = 'NOT(APPLICANTS=(' + applicantsQuery + '))';
    } else {
      const applicantsQuery = enquoteClauseTextValue(joinOr(this.applicantsAliasesService.getOriginalApplicants([event.point.name])));
      filter.query = 'APPLICANTS=(' + applicantsQuery + ')';
      filter.value = event.point.name;
    }

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    const selectedValues = this.storeService.chartSelectedValues;
    selectedValues['top_applicants_selected_applicant'] = event.point.name;
    this.storeService.chartSelectedValues = selectedValues;

    this.storeService.setFilters(filters);
  }

  removeChartFilter(item: Object) {
    const filters = [...this.storeService.filters].map(o => o);
    const index = filters.findIndex((filter) => filter.chart === this.chartName);
    if (index === -1) {
      return;
    }
    this.storeService.filterRemoved = {};
    const selectedValues = this.storeService.chartSelectedValues;

    if (item['value'].indexOf('Others') === -1 || item['othersLevel'] - this.quantity <= this.quantity) {
      delete selectedValues['top_applicants_selected_applicant'];
      this.storeService.chartSelectedValues = selectedValues;

      filters.splice(index, 1);
      this.storeService.setFilters(filters);

      this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: false});

      return;
    }

    const level = item['othersLevel'] - (this.quantity * 2);
    const othersNames = item['othersNames'].slice(0, level);
    item['value'] = `Others ${level + 1} - ${level + this.quantity}`;
    item['othersLevel'] = level + this.quantity;
    item['othersNames'] = othersNames;

    item['query'] = 'NOT(APPLICANTS=(' + this.applicantsAliasesService.getOriginalApplicants(othersNames).join(' OR ') + '))';
    filters.splice(index, 1, item);

    this.storeService.chartSelectedValues = selectedValues;

    this.storeService.setFilters(filters);
  }

  // Slider
  onUserChangeEnd(): void {
    this.updateChart();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.dataSource) {
      this.dataSource.forEach(i => {
        if(i['name'] === 'Others'){
          i['color'] = '#bcc7d6';
        }
      })
      this.chart.series[0].options.totalHits = this.totalItems;
      this.chart.series[0].setData(this.dataSource, true, true, false);
      this.reflowChart();
    }
  }

  private updateChart(): void {
    this.isCalculating = true;
    this.storeService.chartQuantity = this.quantity;
    const payload = {
      charts: [
        'basic_top_applicants'
      ],
      search_filters: {}
    };

    const free_text_query = this.storeService.filters
      .filter(filter => filter.type === 'chart')
      .map(filter => filter.query)
      .join(' AND ');
    if (free_text_query) {
      payload.search_filters['free_text_query'] = free_text_query;
    }
    if (this.quantity > 0) {
      payload['parameters'] = {top_applicants_quantity: this.quantity};
    }
    const calculateToSingleChart$ = this.chartsService.calculateToSingleChart(payload, this.storeService.searchHash)
      .pipe(take(1))
      .subscribe({
        next: ({charts}) => {
          this.top5 = charts[this.chartName].top;
          const allCharts = {...this.chartsService.charts};
          allCharts['basic_top_applicants'] = charts[this.chartName];
          this.chartsService.setCharts(allCharts);
        },
        error: (err) => {
          console.log(err);
          this.isCalculating = false;
        }
      });
    this.subscriptions.add(calculateToSingleChart$);
  }
}
