import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import * as popupInformation from '../../popup-information';
import _ from 'lodash';
import { chartsConfig } from '@shared/charts/config';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-authorities',
  templateUrl: './authorities.component.html',
  styleUrls: ['./authorities.component.scss']
})
export class AuthoritiesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = AuthoritiesComponent;
  chartName = 'basic_authorities';
  title = 'Patent families per authority';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;
  popupText = popupInformation.basicAuthorities;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();

    const singleChartColumn$ = this.storeService.singleChartColumn$
      .pipe(debounceTime(500))
      .subscribe({
        next: () => {
          this.updateChartInstanceOptions();
        }
      });
    this.subscriptions.add(singleChartColumn$);

    const patentListViewMode$ = this.storeService.patentListViewMode$
      .pipe(debounceTime(500))
      .subscribe({
        next: () => {
          this.updateChartInstanceOptions();
        }
      });
    this.subscriptions.add(patentListViewMode$);
  }

  selectPoint(event) {
    if (!event.point || !event.point.name) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(AUTHORITIES=${event.point.name})`;
    filter.value = event.point.name;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);

  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.defineColors();
      const top20 = [...this.datasource].splice(0, 20); // < just the top 20
      this.chart.series[0].setData(top20, true, true, false);
      setTimeout(() => {
        this.updateChartInstanceOptions();
      }, 500);
      this.reflowChart();
    }
  }

  private defineColors() {
    const colors = [...chartsConfig.colorPalette];
    this.datasource.forEach((item, index) => {
      item['color'] = colors[index < colors.length ? index : index - colors.length - 1];
    });
  }

  private updateChartInstanceOptions() {
    if (this.chart && this.chart.series && this.datasource) {
      const xAxisLen = this.datasource.length;
      const shouldRotateLabel = xAxisLen > 10 && (this.storeService.isCombinedMode || !this.storeService.singleChartColumn);
      this.chart.xAxis[0].update({
        labels: {
          formatter: function () {
            const separator = '&nbsp;';
            if (shouldRotateLabel) {
              return `<div class="text-center d-flex column-flex align-items-center">
                        <i style="transform: rotate(180deg);" class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>
                        <div style="transform: rotate(180deg);">${this.value}${separator}</div>
                      </div>`;
            } else {
              return `<div class="text-center d-flex column-flex align-items-center" >
                      <i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>
                      <div>${separator}${this.value}</div>
                    </div>`;
            }
          },
          rotation: shouldRotateLabel ? 90 : 0
        }
      });
    }
  }
}
