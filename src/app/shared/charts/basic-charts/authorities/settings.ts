import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    spacing: [10, 0, 10, 0],
    backgroundColor: chartsConfig.bgColor,
    type: 'column',
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  legend: {
    enabled: false
  },
  plotOptions: {
    column: {
      cursor: 'pointer',
      allowPointSelect: false,
    },
    series: {
      pointPadding: 0,
      groupPadding: 0
    }
  },
  series: [{
    name: 'Patent families'
  }],
  xAxis: [{
    crosshair: true,
    title: {
      text: 'Authority',
      style: chartsConfig.textStyles
    },
    type: 'category',
    labels: {
      formatter: function () {
          return `<div class="text-center"><i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>&nbsp;${this.value}</div>`;
      },
      step: 1,
      style: chartsConfig.textStyles,
      useHTML: true,
    }
  }],
  yAxis: [{
    min: 0,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    headerFormat: '<span>Authority: <b>{point.key}</b></span><table>',
    pointFormat: '<tr><td><span>{series.name}: <b>{point.y:.0f}</b></span></td></tr>',
    footerFormat: '</table>',
    shared: true,
    useHTML: true
  },
};

export const tooltipText = `This graph represents the main authorities covering the analyzed patents.<br>
The horizontal axis lists the most frequently appearing authorities in descending order, and the vertical axis shows the number of patent families present.<br>
<b>Suggestion:</b><br>
Use this chart to quickly identify the main markets appearing in your analyzed results. Click on a specific bar to filter the results for those specific authorities.`
