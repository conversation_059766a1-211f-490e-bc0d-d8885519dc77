import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';

import { ApplicantsComponent } from './applicants/applicants.component';
import { OwnersComponent } from './owners/owners.component';
import { TechnologicalFieldsComponent } from './technological-fields/technological-fields.component';
import { TechnologyTimeLineComponent } from './technology-time-line/technology-time-line.component';
import { TechnologyHeatMapComponent } from './technology-heat-map/technology-heat-map.component';
import { AuthoritiesComponent } from './authorities/authorities.component';
import { SimilarityCurveComponent } from './similarity-curve/similarity-curve.component';

@NgModule({
  declarations: [
    ApplicantsComponent,
    OwnersComponent,
    TechnologicalFieldsComponent,
    TechnologyTimeLineComponent,
    TechnologyHeatMapComponent,
    AuthoritiesComponent,
    SimilarityCurveComponent,
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    ApplicantsComponent,
    OwnersComponent,
    TechnologyTimeLineComponent,
    SimilarityCurveComponent,
    AuthoritiesComponent,
    TechnologyHeatMapComponent,
    TechnologicalFieldsComponent
  ]
})
export class BasicChartsModule {
}
