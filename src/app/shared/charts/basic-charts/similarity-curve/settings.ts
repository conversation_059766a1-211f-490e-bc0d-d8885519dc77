import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  colors: [chartsConfig.similarityColor],
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  xAxis: {
    title: {
      text: 'Result rank',
      style: chartsConfig.textStyles
    },
    startOnTick: false,
    endOnTick: false,
    min: 1,
    labels: {
      style: chartsConfig.textStyles
    },
    crosshair: {
      enabled: true,
      width: 3,
      color: chartsConfig.similarityColor
    }
  },
  yAxis: {
    allowDecimals: false,
    title: {
      text: 'Result similarity',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    },
    plotLines: [{
      value: 0,
      width: 1,
      color: '#999999'
    }]
  },
  legend: {
    enabled: false
  },

  plotOptions: {
    series: {
      allowPointSelect: false,
      pointStart: 1,
      cursor: 'pointer',
      point: {
        events: {
          mouseOver: function (ev) {
            if (this.series?.chart?.series?.length) {
              const zones = this.series.chart.series[0].zones;
              zones[0] = {value: ev.target.y, color: 'gray'};
              this.series.chart.series[0].update({zones: zones}, true, true, false);
            }
          },
          mouseOut: function (ev) {
            if (document.getElementById('similarity-curve') && this.series?.chart?.series?.length) {
              this.series.chart.series[0].update({zones: []}, true, true, false);
            }
          }
        }
      },
    }
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `Publication number: <b>${this.key}</b><br>Rank: <b>${this.point.order}</b><br>Similarity: <b>${this.y}</b>`;
    }
  },
  series: [{
    allowPointSelect: false,
    borderColor: '#AE6800',
    borderWidth: 0.1,
    cursor: 'pointer',
    data: [],
    turboThreshold: 0, // disables 1000 items per serie limitation
    states: {
      name: 'Similarity',
      data: [],
      zones: [{
        color: chartsConfig.similarityColor
      }]
    }
  }]
};

export const tooltipText = `This curve displays how the similarity between your search and the analyzed results decreases from the highest-ranked results to the lowest.<br>
The vertical axis indicates how closely each patent family matches your search. Higher positions on the vertical axis correspond to higher similarity scores, signifying greater relevance. The horizontal axis presents the ranking of patent families in the result list, with the highest-similarity patents on the left and the lowest on the right.<br>
<b>Suggestion:</b><br>
By clicking on the curve, you can set a cut-off point, filtering out the less relevant ones. Use this graph in combination with others to better understand your immediate technology field.`
