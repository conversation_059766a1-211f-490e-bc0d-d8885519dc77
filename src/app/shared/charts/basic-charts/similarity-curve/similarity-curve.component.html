<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [urlTooltipArticle]="urlTooltipArticle.others"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    constructorType='chart'
                    id="similarity-curve"
                    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
