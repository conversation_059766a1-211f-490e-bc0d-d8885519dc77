import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import * as popupInformation from '../../popup-information';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-similarity-curve',
  templateUrl: './similarity-curve.component.html',
  styleUrls: ['./similarity-curve.component.scss']
})
export class SimilarityCurveComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = SimilarityCurveComponent;
  chartName = 'basic_similarity_curve';
  title = 'Result similarity';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;
  popupText = popupInformation.basicSimilarityCurve;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point || !event.point.y) {
      return;
    }

    const extraFilters = this.storeService.extraFilters;
    extraFilters.similarity_index = event.point.y;
    this.storeService.extraFilters = extraFilters;

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = '';
    filter.value = event.point.y;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }
}
