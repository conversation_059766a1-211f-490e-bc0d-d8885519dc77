import { debug } from 'console';
import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'pie',
    backgroundColor: chartsConfig.bgColor,
    spacing: [30, 30, 30, 30],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  colors: chartsConfig.colorPalette,
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const val = this.point.precise_y ? this.point.precise_y : this.point.y;
      return `<span style="font-weight:bold">${this.point.name}</span><br />` +
          (this.series.options.showTooltipPatentFamilies ? 
            `<span>Patent families: </span><span style="font-weight:bold">${val}</span><br/>` : '') +
          `<span>Percent: </span><span style="font-weight:bold">
          ${this.point.percent}%</span>`;
    }
  },
  plotOptions: {
    pie: {
      shadow: false,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: [{
    name: 'Technology areas',
    id: 'technology_areas',
    data: [],
    totalHits: 0
  }],
};

export const secondaryChartSetting = {
  chart: {
    type: 'pie',
    backgroundColor: chartsConfig.secondaryBgColor,
    spacing: [30, 30, 30, 30],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  colors: chartsConfig.colorPalette,
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const val = this.point.precise_y ? this.point.precise_y : this.point.y;
      return `<span style="font-weight:bold">${this.point.name}</span><br /> `+
        (this.series.options.showTooltipPatentFamilies ? 
          `<span>Patent families: </span><span style="font-weight:bold">${val}</span><br/>` : '') +
        `<span>Percent: </span><span style="font-weight:bold">
        ${this.point.percent}%</span>`;
    }
  },
  plotOptions: {
    pie: {
      shadow: false,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: [{
    name: 'Technology areas',
    id: 'technology_areas',
    data: [],
    totalHits: 0
  }],
};

export const tooltipText = `This chart provides a distribution of the analyzed results across the five main technical areas.<br>
The pie chart displays how many patent families a single technical area appears in, both in absolute values and as a percentage of the total number of patent families.<br>
<b>Suggestion:</b><br>
Observe how your analyzed results are distributed across the top technology areas to understand where they are focused.`;
