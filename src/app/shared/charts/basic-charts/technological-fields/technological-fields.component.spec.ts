import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { TechnologicalFieldsComponent } from './technological-fields.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';

describe('TechnologicalFieldsComponent', () => {
  let component: TechnologicalFieldsComponent;
  let fixture: ComponentFixture<TechnologicalFieldsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [TechnologicalFieldsComponent],
      imports: [
        HighchartsChartModule,
        NgxSliderModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TechnologicalFieldsComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
