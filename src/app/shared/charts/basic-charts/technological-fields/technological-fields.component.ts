import { colors } from './../../analytics-charts/patent-value-over-risk/settings';
import { tooltipText } from './../applicants/settings';
import { Component, Injector, Input, OnDestroy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import More from 'highcharts/highcharts-more';
import heatmap from 'highcharts/modules/heatmap';
import treemap from 'highcharts/modules/treemap';
import * as settings from './settings';
import * as popupInformation from '../../popup-information';
import _ from 'lodash';
import { chartsConfig } from '@shared/charts/config';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);
heatmap(Highcharts);
treemap(Highcharts);


@Component({
  selector: 'app-technological-fields',
  templateUrl: './technological-fields.component.html',
  styleUrls: ['./technological-fields.component.scss']
})
export class TechnologicalFieldsComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = TechnologicalFieldsComponent;
  chartName = 'basic_technological_fields';
  @Input() title = 'Technology areas';
  @Input() chartOptions: any = settings.chartSetting;
  @Input() showTooltipPatentFamilies = true;

  datasource = [];

  tooltipText = settings.tooltipText;
  popupText = popupInformation.basicTechFields;

  isExpanded = false;

  colors = [
    chartsConfig.colorPalette[5], chartsConfig.colorPalette[2], chartsConfig.colorPalette[3],
    chartsConfig.colorPalette[1], chartsConfig.colorPalette[0], chartsConfig.colorPalette[4]
  ];

  private techAreasColor = {
    'Electrical Engineering': this.colors[0],
    'Chemistry': this.colors[1],
    'Instruments': this.colors[2],
    'Mechanical Engineering': this.colors[3],
    'Other Fields': this.colors[4]
  };

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = this.secondarySource ? this.secondarySource : charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (this.disableFilter) {
      return;
    }
    if (!event.point || !event.point.name) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: null,
        type: 'chart',
        tech_area: event.point.options.area_name || event.point.options.name,
      };
    }

    const value = event.point.name;

    if (event.point.options.area_name) { // Filter by technological areas
      filter.query = `(TECH_AREAS=${event.point.options.area_name}) AND (TECH_FIELDS=${value})`;
    } else {  // Filter by technology fields
      filter.query = `(TECH_AREAS=${value})`;
    }
    filter.title = event.point.options.area_name ? 'Technology Fields' : 'Technology Areas';
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const filter = this.storeService.filters.find(f => !!f.tech_area);
      this.defineColors(!!filter ? 'tech_fields' : 'tech_areas');
      this.chart.series[0].options.showTooltipPatentFamilies = this.showTooltipPatentFamilies;
      if (filter) {
        this.chart.series[0].setData(this.datasource['tech_fields'].filter( tf => tf.area_name === filter.tech_area), true, true, false);
        this.chart.setTitle({text: filter.tech_area});
      } else {
        this.chart.series[0].setData(this.datasource['tech_areas'], true, true, false);
      }
      this.reflowChart();
    }
  }

  private defineColors(type: string) {
    const techList = this.datasource[type] || [];
    const total = techList.map(item => item['y']).reduce((a, b) => a + b, 0);
    techList.forEach((ta, index) => {
      ta['color'] = this.techAreasColor[ta.name] || this.colors[index < this.colors.length ? index : index - this.colors.length - 1];
      ta['percent'] = (ta['y'] / total * 100).toFixed(2);
    });
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource['tech_areas'] < 1;
  }
}
