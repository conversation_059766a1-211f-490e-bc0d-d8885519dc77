import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    align: 'right',
    layout: 'vertical',
    symbolHeight: 360,
    verticalAlign: 'top',
  },
  title: {
    text: null
  },
  plotOptions: {
    pie: {
      allowPointSelect: false,
      cursor: 'pointer',
      depth: 35,
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: {
          width: '128px',
          color: '#FFF'
        }
      }
    }
  },
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const label = this.series.options.abbreviations.find(abbr => abbr.abbr === this.series.xAxis.categories[this.point.x]);

      label.full = label.full.charAt(0).toUpperCase() + label.full.slice(1);
      const percent = this.series.options.totalHits > 0 ? ((this.point.value / this.series.options.totalHits) * 100).toFixed(2) : 0;

      return `
          <span style="font-weight: bold;">${label.full}</span><br>
          <span>Year: </span>
          <span style="font-weight: bold;">
          ${this.series.yAxis.categories[this.point.y]}</span><br>
          <span>Patent families: </span>
          <span style="font-weight: bold;">${this.point.value}</span><br>
          <span>Percent: </span>
          <span style="font-weight: bold;">${percent}%</span><br>`;
    },
  },
  series: [{
    allowPointSelect: false,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false
    },
    abbreviations: [],
    totalHits: 0,
  }],
  xAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    labels: {
      enabled: true,
      rotation: -45,
      style: chartsConfig.textStyles
    },
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLenght: 1,
    tickInterval: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: 'Technology field',
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLength: 1,
    rotation: 45,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 20;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
  name: 'Number of technology fields'
};

export const tooltipText = `This heatmap displays how the analyzed results developed over time with respect to
<a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">35 technology fields.</a><br>
The horizontal axis shows the priority year, while the vertical axis lists the technology fields. Each cell represents the number of patent families from the analyzed results for a specific priority year and technology field. The color intensity in the heatmap increases with a higher number of patent families, with darker colors indicating more significant activity.<br>
<b>Suggestion:</b><br>
Pay attention to dark-colored regions and how the analyzed results differ across technology fields over time. Tracking these variations can provide valuable information about emerging technologies and shifts in research and development.`
