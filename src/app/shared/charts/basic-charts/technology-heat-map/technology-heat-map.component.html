<app-base-card-chart (sliderChanged)="onUserChangeEnd()" [(sliderValue)]="quantity" [chartName]="chartName"
                     [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [sliderOptions]="sliderOptions"
                     [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [chartItemHeight]="chartItemHeight" [isEmpty]="isEmpty"
                     [urlTooltipArticle]="urlTooltipArticle.technologies">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
