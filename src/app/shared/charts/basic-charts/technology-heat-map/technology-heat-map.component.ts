import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import addHeatmap from 'highcharts/modules/heatmap';
import * as settings from './settings';
import * as popupInformation from '../../popup-information';
import { abbreviations } from '../../utils';
import { Subscription } from 'rxjs';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

addHeatmap(Highcharts);


@Component({
  selector: 'app-technology-heat-map',
  templateUrl: './technology-heat-map.component.html',
  styleUrls: ['./technology-heat-map.component.scss']
})
export class TechnologyHeatMapComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = TechnologyHeatMapComponent;
  chartName = 'basic_technology_trend';
  title = 'Technology fields over time';
  chartOptions = settings.chartSetting;
  sliderOptions: Options = settings.sliderSetting;
  quantity = settings.MAX_VALUE_SLIDER;
  tooltipText = settings.tooltipText;
  popupText = popupInformation.basicTechnologyTrend;
  private abbreviations: any;
  private valuesSorted = [];

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.abbreviations = chart.abbreviations;

        this.updateChartData();
        if (this.quantity !== settings.MAX_VALUE_SLIDER) {
          this.onUserChangeEnd();
        }
      }
    });
    this.subscriptions.add(charts$);

    const newSearchEvent$ = this.storeService.newSearchEvent.subscribe({
      next: value => {
        if (value) {
          this.quantity = settings.MAX_VALUE_SLIDER;
        }
      }
    });
    this.subscriptions.add(newSearchEvent$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      const values = this.sliceTop();
      if (values) {
        this.chart.series[0].setData(values, true, true, false);
        this.reflowChart();
      }
    }
  }

  sliceTop() {
    const values = [...this.datasource.values];
    const xAxis = [...this.datasource.xAxis];

    const techTop = this.valuesSorted.slice(0, this.quantity);

    const valuesNew = [];
    const xAxisNew = [];

    values.forEach(item => {
      if (techTop.findIndex(top => top.index === item[0]) > -1) {
        const itemNew = [...item];
        const elementX = xAxisNew.indexOf(xAxis[itemNew[0]]);
        if (elementX === -1) {
          xAxisNew.push(xAxis[itemNew[0]]);
          itemNew[0] = xAxisNew.length - 1;
        } else {
          itemNew[0] = elementX;
        }
        valuesNew.push(itemNew);
      }
    });
    if (values.length === valuesNew.length) {
      return null;
    }
    return valuesNew;

  }

  selectPoint(event) {
    if (!event.point || !event.point.series) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const abbr = abbreviations.find(ab => ab.abbr === event.point.series.xAxis.categories[event.point.x]);
    filter.query = `((${(this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=')}${event.point.series.yAxis.categories[event.point.y]}) AND ` +
      `(TECH_FIELDS=${abbr.full}))`;
    filter.value = `Field: ${abbr.full}, Year: ${event.point.series.yAxis.categories[event.point.y]}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.valuesSorted = [];

      this.datasource.values.reduce((result, item) => {
        if (!result[item[0]]) {
          result[item[0]] = {index: item[0], total: 0};
          this.valuesSorted.push(result[item[0]]);
        }
        result[item[0]].total += item[2];
        return result;
      }, {});
      this.valuesSorted = this.valuesSorted.sort((a, b) => b.total - a.total);

      this.chart.series[0].options.abbreviations = this.abbreviations;
      this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
      this.chart.xAxis[0].setCategories(this.datasource.xAxis);
      this.chart.yAxis[0].setCategories(this.datasource.yAxis);
      this.chart.series[0].setData(this.datasource.values, true, true, false);
      this.reflowChart();
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }  
}
