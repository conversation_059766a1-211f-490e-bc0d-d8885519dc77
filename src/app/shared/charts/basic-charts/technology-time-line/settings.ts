import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  legend: {
    align: 'center',
    verticalAlign: 'top',
    layout: 'horizontal',
    itemStyle: chartsConfig.textStyles,
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    },
    line: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    },
  },
  colors: chartsConfig.colorPalette,
  series: [{
    color: chartsConfig.similarityColor,
    data: [],
    name: 'Similarity',
    type: 'spline',
    zIndex: 2,
    events: {
      legendItemClick() {
        return false;
      }
    },
    showInLegend: true,
    custom: {type: 'similarity'}
  }, {
    color: '#7CAED5',
    id: 'bars',
    data: [],
    name: 'Number of patent families',
    type: 'column',
    events: {
      legendItemClick() {
        return false;
      }
    },
    yAxis: 1,
    showInLegend: false,
    custom: {type: 'families'}
  },
  {
    color: chartsConfig.trendlineColor,
    data: [],
    name: 'Trend line',
    type: 'line',
    zIndex: 2,
    yAxis: 1,
    events: {
      legendItemClick() {
        return false;
      }
    },
    showInLegend: true,
    custom: {type: 'trendline'}
  }],
  xAxis: [{
    categories: [],
    crosshair: true,
    minTickInterval: 5,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles,
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }],
  yAxis: [{
    allowDecimals: false,
    endOnTick: false,
    lineWidth: 1,
    opposite: true,
    visible: false,
    title: {
      text: 'Similarity average',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }, {
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles,
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const similarityPoint = this.points.find(p => p.point.series.options.custom.type === 'similarity');
      const familiesPoint = this.points.find(p => p.point.series.options.custom.type === 'families');
      const yAxisPercentage = familiesPoint.series.options.yAxisPercentage;
      const percent = !yAxisPercentage ? (familiesPoint.series.options.totalHits > 0 ? ((familiesPoint.y / familiesPoint.series.options.totalHits) * 100).toFixed(2) : '0') :
                                        familiesPoint.y.toFixed(2);
        return '<span>Year: <b>' + this.x + '</b></span>' +
        (familiesPoint.series.options.showTooltipPatentFamilies ? 
          '<br/><span>Patent families: <b>' + familiesPoint.y + '</b></span>' : '') +
        '<br/><span>Percent: <b>' + percent + '%</b></span>' +
          (similarityPoint ? '<br/><span>Similarity average: <b>' + similarityPoint.y + '</b></span>': '');
    },
    shared: true,
    useHTML: true
  },
};

export const secondaryChartSetting = {
  chart: {
    backgroundColor: chartsConfig.secondaryBgColor,
    spacing: [10, 0, 10, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  legend: {
    align: 'center',
    verticalAlign: 'top',
    layout: 'horizontal',
    itemStyle: chartsConfig.textStyles,
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    },
    line: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    },
  },
  colors: chartsConfig.colorPalette,
  series: [{
    color: chartsConfig.similarityColor,
    data: [],
    name: 'Similarity',
    type: 'spline',
    zIndex: 2,
    events: {
      legendItemClick() {
        return false;
      }
    },
    showInLegend: true,
    custom: {type: 'similarity'}
  }, {
    color: '#7CAED5',
    id: 'bars',
    data: [],
    name: 'Number of patent families',
    type: 'column',
    events: {
      legendItemClick() {
        return false;
      }
    },
    yAxis: 1,
    showInLegend: false,
    custom: {type: 'families'},
    totalHits: 0
  },
  {
    color: chartsConfig.trendlineColor,
    data: [],
    name: 'Trend line',
    type: 'line',
    zIndex: 2,
    yAxis: 1,
    events: {
      legendItemClick() {
        return false;
      }
    },
    showInLegend: true,
    custom: {type: 'trendline'},
  }],
  xAxis: [{
    categories: [],
    crosshair: true,
    minTickInterval: 5,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles,
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }],
  yAxis: [{
    allowDecimals: false,
    endOnTick: false,
    lineWidth: 1,
    opposite: true,
    visible: false,
    title: {
      text: 'Similarity average',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }, {
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles,
    },
    labels: {
      style: chartsConfig.textStyles,
    }
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const similarityPoint = this.points.find(p => p.point.series.options.custom.type === 'similarity');
      const familiesPoint = this.points.find(p => p.point.series.options.custom.type === 'families');
      const yAxisPercentage = familiesPoint.series.options.yAxisPercentage;
      const percent = !yAxisPercentage ? (familiesPoint.series.options.totalHits > 0 ? ((familiesPoint.y / familiesPoint.series.options.totalHits) * 100).toFixed(2) : '0') :
                                        familiesPoint.y.toFixed(2);
        return '<span>Year: <b>' + this.x + '</b></span>' + 
          (familiesPoint.series.options.showTooltipPatentFamilies ? 
            '<br/><span>Patent families: <b>' + familiesPoint.y + '</b></span>' : '') +
          '<br/><span>Percent: <b>' + percent + '%</b></span>' +
          (similarityPoint ? '<br/><span>Similarity average: <b>' + similarityPoint.y + '</b></span>': '');
    },
    shared: true,
    useHTML: true
  },
};

export const sliderSetting = {
  floor: 0,
  ceil: 50,
  showSelectionBar: true,
  noSwitching: true,
  name: 'Priority year',
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};

export const tooltipText = `This chart shows the distribution of patent families from the result list according to priority year.<br>
The horizontal axis features the priority year, while the vertical axis numbers patent families. The orange curve shows the average similarity of your search to the patent families from each year.<br>
<b>Suggestion:</b><br>
By investigating the patenting activity of that specific technology over time, you can gain valuable insights into the life cycle of a specific technology.`
