<app-base-card-chart (sliderChanged)="onUserChangeEnd($event)" [highValue]="hideSlider? null:maxValue" (highValueChange)="hideSlider ? null : maxValue = $event" [sliderValue]="hideSlider? null:minValue" (sliderValueChange)="hideSlider ? null : minValue = $event" [chartName]="chartName"
                     [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [sliderOptions]="hideSlider? null:sliderOptions"
                     [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [urlTooltipArticle]="urlTooltipArticle.others"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    constructorType='chart'
                    id="tl"
                    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
