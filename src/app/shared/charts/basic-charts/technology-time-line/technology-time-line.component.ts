import { Component, Injector, Input, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import * as popupInformation from '../../popup-information';
import { ChangeContext, Options } from '@angular-slider/ngx-slider';
import indicators from 'highcharts/indicators/indicators';
import trendLine from 'highcharts/indicators/trendline';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { BooleanSearchStoreService } from '@core/store/boolean-search-store/boolean-search-store.service';
import { LandscapeStoreService } from '@core/store/landscape-store/landscape-store.service';

indicators(Highcharts);
trendLine(Highcharts);

@Component({
  selector: 'app-technology-time-line',
  templateUrl: './technology-time-line.component.html',
  styleUrls: ['./technology-time-line.component.scss']
})
export class TechnologyTimeLineComponent extends BaseChartComponent implements OnI<PERSON>t, OnDestroy {
  component = TechnologyTimeLineComponent;
  @Input() hideSlider?: boolean;

  chartName = 'basic_technology_timeline';
  @Input() title = 'Patent families over time';
  @Input() chartOptions: any = settings.chartSetting;
  @Input() showTooltipPatentFamilies = true;
  @Input() yAxisPercentage = false;

  minValue = 1;
  maxValue = 50;
  sliderOptions: Options = settings.sliderSetting;

  tooltipText = settings.tooltipText;
  popupText = popupInformation.basicTechnologyTimeLine;
  totalItems = 0;

  private sliderFilter = false;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = this.secondarySource ? this.secondarySource : charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.totalItems = chart.total;
        if (!this.sliderFilter && !this.hideSlider) {
          this.minValue = this.datasource.xAxis[0];
          this.maxValue = this.datasource.xAxis[this.datasource.xAxis.length - 1];

          const newOptions: Options = Object.assign({}, this.sliderOptions);
          newOptions.floor = this.minValue;
          newOptions.ceil = this.maxValue;
          newOptions.stepsArray = this.datasource.xAxis.map((item) => ({value: item}));
          this.sliderOptions = newOptions;
        }
        this.sliderFilter = false;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    const filterRemoved$ = this.storeService.filterRemoved$.subscribe({
      next: item => {
        if (item['type'] && item['type'] === 'chart' && item['chart'] === this.chartName) {
          this.removeChartFilter();
        }
      }
    });
    this.subscriptions.add(filterRemoved$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (this.disableFilter) {
      return;
    }
    if (!event.point || !event.point.category) {
      return;
    }

    this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: true});
    this.sliderFilter = false;
    const queryFilter = (this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=') + event.point.category;
    this.onFilter(queryFilter, event.point.category);

  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  // Slider
  onUserChangeEnd(changeContext: ChangeContext): void {
    if(this.hideSlider){
      return;
    }
    this.sliderFilter = true;
    let dateField = this.storeService?.isPublications ? 'PUBLICATION_DATE' : 'PRIORITY_DATE';
    const queryFilter = `(${dateField}>=${changeContext.value}) AND (${dateField}<=${changeContext.highValue})`;
    const value = `${changeContext.value} - ${changeContext.highValue}`;
    this.onFilter(queryFilter, value);
  }

  removeChartFilter() {
    this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: false});
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private onFilter(queryFilter: string, value: string): void {
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }
    filter.query = queryFilter;
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  hasSimilarity(): boolean{
    if(this.storeService instanceof BooleanSearchStoreService && !this.storeService.sortBySimilarity){
      return false;
    }
    return this.datasource.yAxisAvg && this.datasource.yAxisAvg.some((i: number) => i !== 0);
  }

  hasTrendLine(): boolean {
    if (!(this.storeService instanceof LandscapeStoreService) || this.storeService.sortBySimilarity) {
      return false;
    }
    return this.datasource.yAxis && this.datasource.yAxis.filter((i: number) => i > 0).length > 5;
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const hasSimilarities = this.hasSimilarity();
      const hasTrendLine = this.hasTrendLine();

      this.chart.legend.update({ enabled: hasSimilarities || hasTrendLine });
      this.chart.series[0].update({showInLegend: hasSimilarities, visible: hasSimilarities});
      this.chart.xAxis[0].setCategories(this.datasource.xAxis, true, true);

      const yAxis = this.yAxisPercentage ? this.datasource.yAxisPercent : this.datasource.yAxis;
      this.chart.series[1].setData(yAxis , true, true, false);
      this.chart.series[1].options.totalHits = this.totalItems;
      this.chart.series[1].options.showTooltipPatentFamilies = this.showTooltipPatentFamilies;
      this.chart.series[1].options.yAxisPercentage = this.yAxisPercentage;

      if (this.yAxisPercentage) {
        this.chart.yAxis[1].setTitle({text: 'Percentage of patent families'});
        this.chart.yAxis[1].update({labels: {format: '{value}%'}});
      }

      if (hasSimilarities) {
        this.chart.series[0].setData(this.datasource.yAxisAvg, true, true, false);
      }

      this.chart.series[2].update({showInLegend: hasTrendLine, visible: hasTrendLine});
      if (hasTrendLine) {
        const trendLine = this.yAxisPercentage ? this.datasource.trendline_percent : this.datasource.trendline;
        this.chart.series[2].setData(trendLine, true, true, false);
      }
      if (this.datasource.xAxis.length <= 20) {
        this.chart.xAxis[0].options.minTickInterval = 1;
      }
      this.reflowChart();
    }
  }
}
