<div *ngIf="isEmptyDashboard" class="alert alert-info mt-3" role="alert">
  <h6 class="alert-heading open-sans-bold mb-2">Your dashboard is empty</h6>
  <div>
    <p class="p-0 m-0">Please select one or more charts to be added to your <span class="font-weight-bold">{{customChartCategory.name}}</span> dashboard</p>
    <p class="p-0 m-0"><i>Tip: You can add any chart by selecting "Add charts" option</i></p>
  </div>
</div>

<div *ngIf="!isColumnLayout && storeService.addChartInDashboard" class="dashboard-chart-card my-3">
  <div class="tools-bar chart-bar">
    <div class="d-flex justify-content-between dashboard-tabs tabs-container"
      data-intercom-target="dashboard-categories">
      <div class="d-flex flex-wrap align-items-center">
        <ng-container *ngFor="let item of defaultChartCategories">
          <div *ngIf="!item.excludeDashboard && storeService.hasFeature(item)" class="new-categories">
            <a (click)="changeTab(item)" [class.active]="!isSearchingChart && dashboardCardNav === item.category" [class.exclude]="item.excludeDashboard" *ngIf="!item.newCategory"
            [class]="'item-bar chart-icon chart-icon-' + item.category" [ngClass]="{'tab-dashboard': item.newCategory}" href="javascript:void(0)">
            {{item.title}} ({{totalChartsSelected(item.category)}}/{{totalChartsUnSelected(item)}})
            </a>
          </div>
        </ng-container>
      </div>
      <div class="align-self-center me-3">
        <input class="form-control" placeholder="Search charts" [(ngModel)]="searchChartTerm">
      </div>
    </div>
    <div class="d-flex">
      <div class="dashboard-card-container d-flex pb-5">
        <ng-container *ngFor="let card of defaultChartCategories">
          <ng-container *ngFor="let chart of card.charts">
            <div (click)="selectChart(chart)" [hidden]="!isChartVisible(chart) || (isSearchingChart ? isNotSearch(chart.name) : dashboardCardNav !== card.category) || card.excludeDashboard"
              class="col-md-2 d-flex flex-column justify-content-between chart-card cursor-pointer px-1">
              <div class="d-flex chart-card-title py-2">{{ chart.title }}</div>
              <div class="d-flex chart-card-image" [class.active]="findIndexSelectedChart(chart.name) > -1">
                <img class src="{{'assets/images/chart_thumbnails/' + chart.name + '.png'}}" />
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
    <div class="col-12 footer p-3">
      <button class="btn btn-primary mx-1" [class.disabled]="!selectedCharts.length" (click)="addCharts()">Add
        ({{selectedCharts.length}})</button>
      <button class="btn btn-primary-outline mx-1" [class.disabled]="!selectedCharts.length"
        (click)="selectedCharts.length = 0">Clear ({{selectedCharts.length}})</button>
    </div>
  </div>
</div>

<div class="mt-3" *ngIf="message">
  <app-alert [type]="messageType" [message]="message" [headline]="headLine"></app-alert>
</div>

<div class="row dashboard-chart-container" [class.arrange-dashboard]="arrangeDashBoard">
  <ng-container *ngFor="let chart of showingCharts; let i = index;">
    <div [draggable]="arrangeDashBoard" [class.dragging]="chartOrder(chart.name) === this.draggingIndex"
         (dragstart)="onDragStart(chartOrder(chart.name))" (dragenter)="onDragEnter(chartOrder(chart.name), $event)"
         (dragend)="onDragEnd()" class="dashboard-chart-item mb-4"
         [ngClass]="isColumnLayout || storeService.singleChartColumn ? 'col-md-12' : 'col-md-6'"
         [ngStyle]="{'order': i + 1}">
         <app-chart-factory [chart]="chart" [storeService]="storeService"></app-chart-factory>
    </div>
  </ng-container>
</div>
