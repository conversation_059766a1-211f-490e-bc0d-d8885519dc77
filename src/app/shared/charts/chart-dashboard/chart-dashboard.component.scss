@import 'scss/layout2021/variables';
@import 'scss/components/chart_tabs';

.dashboard-chart-item {
  transition: display 0.8s ease;
  display: inline-block;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
}

.dashboard-chart-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;

  &.arrange-dashboard {
    ::ng-deep {
      .dashboard-arrangement-layer { display: block; }

      .chart-item {
        border: 3px dashed $brand-green;
        border-radius: 5px;

        & > div:not([class*="chart-header"]) { opacity: .1; }
        .chart-header{
          & > div:not([class*="box-title"]), app-tooltip{
            opacity: 0;
          }
        }
        .box-title{ padding-left: 1rem; }
      }

      .dragging-over {
        .chart-item {
          border: 4px dashed $brand-green-pressed;
        }
      }
    }
  }
}

.dashboard-tabs,
.dashboard-card-container {
  border: 1px solid #CBDCE2;
  border-radius: 0px 0px 3px 3px;
  overflow: auto;
  min-height: 53px;
}

.dashboard-card-container {
  border-top: none;
}

.tab-dashboard {
  padding-left: 13px;
  padding-right: 16px;
}

.new-categories {
  margin-top: 8px;
  margin-bottom: 14px;
}
.chart-card-image {
  vertical-align: bottom;
  transition: box-shadow .2s ease;

  img {
    vertical-align: bottom;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    transition: opacity 150ms linear;
    user-select: none;
    max-width: 100%;
  }

  &.active {
    border: solid 3px #389A85;
  }

  &:hover {
    box-shadow: 0px 0px 16px 0px rgba(15, 44, 53, 0.75);
    -webkit-box-shadow: 0px 0px 16px 0px rgba(15, 44, 53, 0.75);
    -moz-box-shadow: 0px 0px 16px 0px rgba(15, 44, 53, 0.75);
  }
}

.chart-card-title {
  font-size: 1rem;
  color: #0F2C35;
  min-height: 2rem;
}

.chart-bar {
  .footer {
    background-color: #E8F0F3;
    border: 1px solid #CBDCE2;
  }
}

.font-weight-bold {
  font-weight: bold;
}
