import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { ChartDashboardComponent } from './chart-dashboard.component';
import { BasicChartsModule } from '../basic-charts/basic-charts.module';
import { CompetitionChartsModule } from '../competition-charts/competition-charts.module';
import { ClassificationChartsModule } from '../classification-charts/classification-charts.module';
import { AnalyticsChartsModule } from '../analytics-charts/analytics-charts.module';
import { TrendLandscapeChartsModule } from '../trend-landscape-charts/trend-landscape-charts.module';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';

describe('ChartDashboardComponent', () => {
  let component: ChartDashboardComponent;
  let fixture: ComponentFixture<ChartDashboardComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ChartDashboardComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
        BasicChartsModule,
        CompetitionChartsModule,
        ClassificationChartsModule,
        AnalyticsChartsModule,
        TrendLandscapeChartsModule
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ChartDashboardComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    component.customChartCategory = { index: 0, name: 'test', charts: [] };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
