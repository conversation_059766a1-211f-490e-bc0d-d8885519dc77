import { Component, EventEmitter, Injector, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UserService } from '@core/services';
import { BaseChartsComponent } from '@shared/charts/base-charts/base-charts.component';
import { UserProfile } from '@core/models';
import { ActionType } from './types';
import { Chart } from '../types';

@Component({
  selector: 'app-chart-dashboard',
  templateUrl: './chart-dashboard.component.html',
  styleUrls: ['./chart-dashboard.component.scss']
})
export class ChartDashboardComponent extends BaseChartsComponent implements OnInit, OnDestroy {
  @Input() arrangeDashBoard: boolean;
  @Input() customChartCategory: { index: number, name: string, charts: string[] } = null;
  @Output() showChartCardsChange: EventEmitter<boolean> = new EventEmitter();

  dashboardCardNav: string = 'applicant';
  draggingIndex: number;
  draggedIndex: number;
  selectedCharts: Chart[] = [];
  userProfile: UserProfile;
  message: string;
  headLine: string;
  messageType: string;
  searchChartTerm = '';

  constructor(
    protected injector: Injector,
    public userService: UserService
  ) {
    super(injector);
  }

  get defaultChartCategories() {
    return this.storeService.defaultChartCategories;
  }

  get isEmptyDashboard(): boolean {
    return this.showingChartNames.length === 0;
  }

  get isSearchingChart(): boolean {
    return this.searchChartTerm.length > 0;
  }

  get showingChartNames(): string[] {
    return this.customChartCategory?.charts || [];
  }

  get showingCharts(): Chart[] {
    const charts: Chart[] = [];
    for (const name of this.showingChartNames) {
      const c = this.storeService.getChartByName(name);
      if(c){
        charts.push(c);
      }
    }
    if(this.storeService.chartDashboardType === 'semantic'){
      return charts
    }
    return charts.filter(c => !c.mustHaveSimilarity || this.storeService.sortBySimilarity);
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: u => {
        this.userProfile = u.profile;
        if (u && u.profile) {
          this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
        }
      }
    });
    this.subscriptions.add(user$);

    const chartDashboardAction$ = this.storeService.chartDashboardAction$.subscribe({
      next: msg => {
        this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
        setTimeout(() => {
          this.message = msg.message;
          switch (msg.action) {
            case ActionType.Rename:
              this.headLine = 'Renamed dashboard';
              break;
            case ActionType.Empty:
              this.headLine = 'Dashboard has been emptied';
              break;
            case ActionType.Delete:
              this.headLine = 'Removed chart';
              break;
            default:
              this.headLine = 'Added chart';
              break;
          }
          this.messageType = 'success';
        }, 100);
      }
    });
    this.subscriptions.add(chartDashboardAction$);
  }

  isChartVisible(chart: Chart): boolean {
    return chart.features.indexOf(this.storeService.chartDashboardType) > -1 && this.showingChartNames.findIndex(chartName => chart.name === chartName) === -1;
  }

  chartOrder(chartName: string) {
    return this.showingChartNames.indexOf(chartName) + 1;
  }

  ngOnDestroy() {
    setTimeout(() => {
      this.storeService.arrangeDashboard = false;
    });
    this.subscriptions.unsubscribe();
    this.storeService.chartDashboardAction = {};
  }

  onDragStart(index: number): void {
    if (!this.arrangeDashBoard) {
      return
    }
    this.draggingIndex = index - 1;
  }

  onDragEnter(index: number, event): void {
    if (!this.arrangeDashBoard) {
      return
    }
    if (this.draggingIndex !== (index - 1) && this.draggedIndex !== (index - 1)) {
      this.unDraggingOver();
      event.currentTarget.classList.add('dragging-over');
      this.draggedIndex = index - 1;
    }
  }

  onDragEnd(): void {
    if (!this.arrangeDashBoard) {
      return
    }
    this._reorderItem(this.draggingIndex, this.draggedIndex);
    this.unDraggingOver();
    this.draggingIndex = undefined;
    this.draggedIndex = undefined;
  }

  findIndexSelectedChart(chartName: string): number {
    return this.selectedCharts.findIndex(chart => chart.name === chartName);
  }

  selectChart(chart: Chart) {
    const index = this.findIndexSelectedChart(chart.name);
    if (index > -1) {
      this.selectedCharts.splice(index, 1);
    } else {
      this.selectedCharts.push(chart);
    }
  }

  totalChartsUnSelected(item: any): number {
    return item.charts.map(chart => this.isChartVisible(chart)).filter(chart => !!chart).length;
  }

  totalChartsSelected(item: string): number {
    return this.selectedCharts.filter(chart => chart.name.startsWith(item)).length;
  }

  addCharts() {
    if (!this.userProfile || !this.customChartCategory) {
      return;
    }

    this.customChartCategory.charts.push(...this.selectedCharts.map(chart => chart.name));
    const customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
    const customChartCategoryId = this.storeService.generateCustomChartCategoryId(this.customChartCategory);
    const customChartCategory = customChartCategories.find(category => {
      return this.storeService.generateCustomChartCategoryId(category) === customChartCategoryId;
    });
    customChartCategory.charts = this.customChartCategory.charts;
    this.userProfile.ui_settings[this.storeService.chartDashboardType + "_dashboards"] = customChartCategories;

    const msg = `<span class="open-sans-semi-bold">${this.selectedCharts.map(chart => chart.title).join(', ')}</span>
                ${this.selectedCharts.length > 1 ? 'were' : 'was'} added to <b>${this.customChartCategory.name}</b>`;
    const updateProfile$ = this.userService.updateProfile(this.userProfile)
      .subscribe({
        next: () => {
          this.messageType = 'success';
          this.headLine = 'Charts successfully added';
          this.message = msg;
          this.storeService.addChartInDashboard = !this.storeService.addChartInDashboard;
          this.storeService.chartDashboardAction = {
            action: ActionType.Add,
            message: this.message
          };
          setTimeout(() => {
            this.message = null;
            this.messageType = null;
            this.headLine = null;
          }, 5000);
        },
        error: (err) => {
          console.log(err);
        }
      });
    this.subscriptions.add(updateProfile$);

    this.selectedCharts = [];
  }

  isNotSearch(chartName: string): boolean {
    return chartName.toLowerCase().indexOf(this.searchChartTerm.toLowerCase()) === -1;
  }

  changeTab(item) {
    this.dashboardCardNav = item.category
    this.searchChartTerm = '';
  }

  private unDraggingOver(): void {
    document.querySelectorAll('.dragging-over').forEach(el => el.classList.remove('dragging-over'));
  }

  private _reorderItem(fromIndex: number, toIndex: number): void {
    const itemToBeReordered = this.showingChartNames.splice(fromIndex, 1)[0];
    this.showingChartNames.splice(toIndex, 0, itemToBeReordered);
    this.draggingIndex = toIndex;
  }
}
