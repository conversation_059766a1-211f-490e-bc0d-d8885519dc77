import { SharedModule } from '@shared/shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ChartDashboardComponent } from './chart-dashboard.component';
import { BasicChartsModule } from '../basic-charts/basic-charts.module';
import { CompetitionChartsModule } from '../competition-charts/competition-charts.module';
import { AnalyticsChartsModule } from '../analytics-charts/analytics-charts.module';
import { ClassificationChartsModule } from '../classification-charts/classification-charts.module';
import { TrendLandscapeChartsModule } from '../trend-landscape-charts/trend-landscape-charts.module';
import { LandscapeChartsModule } from '../landscape-charts/landscape-charts.module';
import { ApplicantOwnerChartsModule } from '../applicant-owner-charts/applicant-owner-charts.module';
import { CitationChartsModule } from '../citation-charts/citation-charts.module';
import { AuthoritiesChartsModule } from '../authorities-charts/authorities-charts.module';
import { NPLChartsModule } from '../npl-charts/npl-charts.module';
import { MonitorChartsModule } from '../monitor-charts/monitor-charts.module';
import { TechnologyChartsModule } from '../technology-charts/technology-charts.module';
import { ChartFactoryComponent } from '../chart-factory';


@NgModule({
  declarations: [ ChartFactoryComponent, ChartDashboardComponent],
  imports: [
    CommonModule,
    AnalyticsChartsModule,
    BasicChartsModule,
    ClassificationChartsModule,
    CompetitionChartsModule,
    TrendLandscapeChartsModule,
    LandscapeChartsModule,
    ApplicantOwnerChartsModule,
    AuthoritiesChartsModule,
    CitationChartsModule,
    MonitorChartsModule,
    NPLChartsModule,
    TechnologyChartsModule,
    SharedModule,
  ],
  exports: [
    BasicChartsModule,
    CompetitionChartsModule,
    AnalyticsChartsModule,
    ClassificationChartsModule,
    CompetitionChartsModule,
    TrendLandscapeChartsModule,
    LandscapeChartsModule,
    ApplicantOwnerChartsModule,
    AuthoritiesChartsModule,
    CitationChartsModule,
    MonitorChartsModule,
    NPLChartsModule,
    TechnologyChartsModule,
    ChartFactoryComponent,
    ChartDashboardComponent
  ]
})
export class ChartDashboardModule {
}
