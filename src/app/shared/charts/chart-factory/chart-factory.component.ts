import { AfterViewInit, ChangeDetectorRef, Component, Input, ViewChild, ViewContainerRef } from '@angular/core';
import { BaseStoreService } from '@core';
import {
  ApplicantsComponent,
  OwnersComponent,
  AuthoritiesComponent,
  SimilarityCurveComponent,
  TechnologicalFieldsComponent,
  TechnologyHeatMapComponent,
  TechnologyTimeLineComponent
} from '../basic-charts';
import {
  CpcCodeHeatMapComponent,
  CpcCombinationComponent,
  IpcCodeHeatMapComponent,
  IpcCombinationComponent,
  MainCpcCodesComponent,
  MainIpcCodesComponent,
  TopCpcCodeComponent,
  TopIpcCodeComponent
} from '../classification-charts';
import {
  CompetitorsAuthoritiesProfileComponent,
  CompetitorsCpcProfileComponent,
  CompetitorsIpcProfileComponent,
  CompetitorsOverTimeComponent,
  CompetitorsTechProfileComponent,
  PortfolioAnalyticsProfileComponent
} from '../competition-charts';
import {
  MarketCoverageComponent,
  NumberCitationsComponent,
  NumberCitationsReferencesComponent,
  NumberReferencesComponent,
  PatentBroadnessComponent,
  PatentQualityIndicationComponent,
  PatentRiskComponent,
  PatentValueComponent,
  PatentValueOverRiskComponent,
  RecencyIndicationComponent
} from '../analytics-charts';
import { AnalyticRadarComponent, ApplicantCollaborationNetworkComponent } from '../trend-landscape-charts';
import {
  ApplicantLegalStatusDistributionComponent,
  AuthoritiesTimelineComponent,
  GreenCategoriesComponent,
  GreenFamiliesTimelineComponent,
  GreenRankingComponent,
  GreenTechnologiesLandscapeComponent,
  LegalStatusComponent,
  RichNeighborhoodComponent,
  TechnologyStackTimelineComponent,
  TopApplicantsAuthorityComponent
} from '../landscape-charts';
import { TechnologyFieldsComponent, } from '../technology-charts';
import { AuthoritiesMapComponent, MainAuthoritiesComponent } from '../authorities-charts';
import { Chart } from '../types';
import { MainApplicantsComponent, MainOwnersComponent, NumberOfApplicantsOverTimeComponent } from '../applicant-owner-charts';

import { VennComponent } from '../monitor-charts';
import {
  NplCitationsReferencesComponent,
  NplTechnologicalFieldsComponent,
  NplTechnologyTimeLineComponent,
  NplTopAuthorsComponent
} from '../npl-charts';
import {
  CitationPhasesComponent,
  CitationPlNplComponent,
  CitationsReferencesComponent,
  CitationTypesComponent
} from '../citation-charts';

@Component({
  selector: 'app-chart-factory',
  templateUrl: './chart-factory.component.html',
  styleUrls: ['./chart-factory.component.scss']
})
export class ChartFactoryComponent implements AfterViewInit {

  @Input() chart: Chart;
  @Input() storeService: BaseStoreService;


  @ViewChild('containerChart', { read: ViewContainerRef }) containerChart: ViewContainerRef;

  constructor(
    private changeDetectorRef: ChangeDetectorRef
  ) { }

  ngAfterViewInit() {
    const component = this.getChartComponent();

    if (component) {
      const componentRef = this.containerChart.createComponent<any>(component);
      componentRef.instance.storeService = this.storeService;
      componentRef.instance.chartRef = this.chart;
      if (this.chart?.height) {
        componentRef.instance.chartItemHeight = this.chart.height;
      }
      if (this.chart?.disableFilter) {
        componentRef.instance.disableFilter = this.chart.disableFilter;
      }
      this.changeDetectorRef.detectChanges();
    }
  }

  private getChartComponent(): any {
    const charts = {
      'npl_technology_timeline': NplTechnologyTimeLineComponent,
      'analytics_patent_family': MarketCoverageComponent,
      'analytics_citations_references': NumberCitationsReferencesComponent,
      'analytics_citations_count': NumberCitationsComponent,
      'analytics_references_count': NumberReferencesComponent,
      'analytics_patent_broadness': PatentBroadnessComponent,
      'analytics_consistency_indication': PatentQualityIndicationComponent,
      'analytics_patent_risk': PatentRiskComponent,
      'analytics_patent_value_over_risk': PatentValueOverRiskComponent,
      'analytics_patent_value': PatentValueComponent,
      'analytics_recency_indication': RecencyIndicationComponent,
      'applicants_main_applicants': MainApplicantsComponent,
      'owners_main_owners': MainOwnersComponent,
      'applicant_number_over_time': NumberOfApplicantsOverTimeComponent,
      'authorities_authorities_map': AuthoritiesMapComponent,
      'authorities_main_authorities': MainAuthoritiesComponent,
      'basic_top_applicants': ApplicantsComponent,
      'basic_top_owners': OwnersComponent,
      'basic_authorities': AuthoritiesComponent,
      'basic_similarity_curve': SimilarityCurveComponent,
      'basic_technological_fields': TechnologicalFieldsComponent,
      'basic_technology_trend': TechnologyHeatMapComponent,
      'basic_technology_timeline': TechnologyTimeLineComponent,
      'citation_phases': CitationPhasesComponent,
      'citation_pl_npl': CitationPlNplComponent,
      'citation_types': CitationTypesComponent,
      'citation_references': CitationsReferencesComponent,
      'classification_cpc_time': CpcCodeHeatMapComponent,
      'classification_top_cpc_combination': CpcCombinationComponent,
      'classification_ipc_time': IpcCodeHeatMapComponent,
      'classification_top_ipc_combination': IpcCombinationComponent,
      'classification_main_cpc_codes': MainCpcCodesComponent,
      'classification_main_ipc_codes': MainIpcCodesComponent,
      'classification_top_cpc': TopCpcCodeComponent,
      'classification_top_ipc': TopIpcCodeComponent,
      'competitors_authorities_profile': CompetitorsAuthoritiesProfileComponent,
      'competitors_cpc_profile': CompetitorsCpcProfileComponent,
      'competitors_ipc_profile': CompetitorsIpcProfileComponent,
      'competitors_over_time': CompetitorsOverTimeComponent,
      'competitors_tech_profile': CompetitorsTechProfileComponent,
      'competitors_portfolio_analytics_profile': PortfolioAnalyticsProfileComponent,
      'competitors_applicant_legal_status_distribution': ApplicantLegalStatusDistributionComponent,
      'basic_authorities_timeline': AuthoritiesTimelineComponent,
      'green_categories': GreenCategoriesComponent,
      'green_families_timeline': GreenFamiliesTimelineComponent,
      'green_ranking': GreenRankingComponent,
      'green_technologies_landscape': GreenTechnologiesLandscapeComponent,
      'analytics_legal_status': LegalStatusComponent,
      'rich_neighborhood': RichNeighborhoodComponent,
      'basic_technology_stack_timeline': TechnologyStackTimelineComponent,
      'basic_top_applicants_in_authority': TopApplicantsAuthorityComponent,
      'monitor_venn_diagram': VennComponent,
      'npl_citations_references': NplCitationsReferencesComponent,
      'npl_technological_fields': NplTechnologicalFieldsComponent,
      'npl_top_authors': NplTopAuthorsComponent,
      'technology_technology_fields': TechnologyFieldsComponent,
      'trend_analytic_radar': AnalyticRadarComponent,
      'trend_applicant_collaboration_network': ApplicantCollaborationNetworkComponent
    };
    return charts[this.chart?.name];
  }

}
