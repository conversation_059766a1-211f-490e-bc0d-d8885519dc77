import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ChartsContainerComponent } from './charts-container.component';
import { RouterModule } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { CollectionStoreService } from '@core/store/collection-store/collection-store.service';
import { LandscapeStoreService } from '@core/store/landscape-store/landscape-store.service';
import { provideMatomo } from 'ngx-matomo-client';

describe('ChartsContainerComponent', () => {
  let component: ChartsContainerComponent;
  let fixture: ComponentFixture<ChartsContainerComponent>;

  beforeEach(waitForAsync( () => {
    TestBed.configureTestingModule({
      declarations: [ ChartsContainerComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        CollectionStoreService,
        LandscapeStoreService,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ChartsContainerComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(CollectionStoreService);
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
