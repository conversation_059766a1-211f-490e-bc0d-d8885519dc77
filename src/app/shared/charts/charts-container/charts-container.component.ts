import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BaseStoreService,
  BooleanSearchStoreService,
  CollectionStoreService,
  LandscapeStoreService,
  MonitorLegalStoreService,
  MonitorStoreService,
  UserProfile,
  UserService } from '@core';
import { ViewModeTypeEnum } from '@search/patent/types';
import { Subscription } from 'rxjs';
import { Chart } from '../types';

@Component({
  selector: 'app-charts-container',
  templateUrl: './charts-container.component.html',
  styleUrls: ['./charts-container.component.scss']
})
export class ChartsContainerComponent implements OnInit, OnDestroy {
  @Input() isColumnLayout?: boolean;
  @Input() storeService: BaseStoreService;
  @Input() chartDisclaimer: string;
  @Input() fullWidth: boolean = false;

  tabsSaved = false;
  userProfile: UserProfile;
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService
  ) {
  }

  get activeChartCategory(): string {
    return this.storeService.activeChartCategory;
  }

  set activeChartCategory(value: string) {
    this.storeService.activeChartCategory = value;
  }

  get isCustomChartCategory(): boolean {
    return this.storeService.isCustomChartCategory();
  }

  get defaultChartCategories() {
    return this.storeService.defaultChartCategories;
  }

  get activeChartCategoryTitle(): string {
    return this.storeService.defaultChartCategories.find((ct) => ct.category === this.activeChartCategory)?.title;
  }

  get activeChartCategoryIcon(): string {
    return this.activeChartCategory;
  }

  get customChartCategories(): { name: string, charts: Array<string>, index: number }[] {
    return this.storeService.customChartCategories;
  }

  get activeDefaultCategoryCharts(): Chart[] {
    if(this.storeService.chartDashboardType === 'semantic'){
      return this.storeService.getActiveDefaultCategoryCharts()
    }
    return this.storeService.getActiveDefaultCategoryCharts()?.filter(c => !c.mustHaveSimilarity || this.storeService.sortBySimilarity);
  }

  get isCombinedMode (): boolean{
    return this.storeService.patentListViewMode === ViewModeTypeEnum.COMBINED;
  }

  ngOnInit() {
    this.setDashboardType()
    if (!this.activeChartCategory) {
      this.activeChartCategory = this.defaultChartCategories[0].category;
    }

    const user$ = this.userService.user.subscribe({
      next: u => {
        this.userProfile = u.profile;
        if (u && u.profile) {
          this.userProfile = u.profile;
          this.updateCustomerChartCategories();
        }
      }
    });
    this.subscriptions.add(user$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  setDashboardType(){
    switch(true){
      case this.storeService instanceof LandscapeStoreService:
        this.storeService.chartDashboardType = (this.storeService as LandscapeStoreService).isGreenReport ? 'greenReport' : 'landscape';
      break;
      case this.storeService instanceof CollectionStoreService:
        this.storeService.chartDashboardType = this.storeService.isPublications ? 'collectionPublication' : 'collection';
      break;
      case this.storeService instanceof MonitorStoreService:
        this.storeService.chartDashboardType = this.storeService.isPublications ? 'monitorPublication' : 'monitor';
      break;
      case this.storeService instanceof MonitorLegalStoreService:
        this.storeService.chartDashboardType = 'monitor_legal_status';
      break;
      case this.storeService instanceof BooleanSearchStoreService:
        this.storeService.chartDashboardType = this.storeService.isPublications ? 'booleanPublication' : 'boolean';
      break;
    }
  }

  toggle(tab: string): void {
    if (this.activeChartCategory !== tab) {
      this.activeChartCategory = tab;
      this.reloadCharts();
    }
  }

  setSingleChartColumn(value) {
    this.storeService.singleChartColumn = value;
  }

  addCustomChartCategory() {
    const addChartCategory$ = this.userService.addChartCategory(this.storeService.chartDashboardType).subscribe({
      next: (chartCategory) => {
        this.updateCustomerChartCategories();
        this.toggle(this.storeService.generateCustomChartCategoryId(chartCategory));
        this.storeService.addChartInDashboard = true;
      }
    });
    this.subscriptions.add(addChartCategory$);
  }

  isActiveChartCategory(tab: string) {
    return this.activeChartCategory === tab;
  }

  private updateCustomerChartCategories() {
    this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
  }

  private reloadCharts() {
    if (this.storeService.searchHash) {
      const isCustomCategory = this.isCustomChartCategory;
      if ((isCustomCategory && this.storeService.getActiveCustomChartCategory()?.charts?.length > 0) || !isCustomCategory) {
        this.storeService.searchHash = this.storeService.searchHash;
      }
    }
  }
}
