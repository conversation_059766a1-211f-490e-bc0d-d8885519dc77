import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '@shared/shared.module';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { HighchartsChartModule } from 'highcharts-angular';

import {
  AnalyticsChartsModule,
  BasicChartsModule,
  ClassificationChartsModule,
  CompetitionChartsModule,
  TrendLandscapeChartsModule,
  TechnologyChartsModule,
  ChartDashboardModule,
} from '@shared/charts';

import { ChartsContainerComponent } from './charts-container/charts-container.component';

@NgModule({
  declarations: [ChartsContainerComponent],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule,
    BasicChartsModule,
    AnalyticsChartsModule,
    ClassificationChartsModule,
    CompetitionChartsModule,
    TechnologyChartsModule,
    ChartDashboardModule,
    TrendLandscapeChartsModule
  ],
  exports: [
    BasicChartsModule,
    AnalyticsChartsModule,
    ClassificationChartsModule,
    CompetitionChartsModule,
    TrendLandscapeChartsModule,
    TechnologyChartsModule,
    ChartsContainerComponent,
    ChartDashboardModule
  ]
})

export class ChartsModule {}
