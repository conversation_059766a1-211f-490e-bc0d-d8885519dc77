import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { SharedModule } from '@shared/shared.module';

import { CitationsReferencesComponent } from './citations-references/citations-references.component';
import { CitationPhasesComponent } from './citation-phases/citation-phases.component';
import { CitationTypesComponent } from './citation-types/citation-types.component';
import { CitationPlNplComponent } from './citation-pl-npl/citation-pl-npl.component';

@NgModule({
  declarations: [
    CitationsReferencesComponent, CitationPhasesComponent, CitationTypesComponent, CitationPlNplComponent
  ],
  imports: [
    CommonModule, HighchartsChartModule, SharedModule
  ],
  exports: [
    CitationsReferencesComponent, CitationPhasesComponent, CitationTypesComponent, CitationPlNplComponent
  ]
})
export class CitationChartsModule {
}
