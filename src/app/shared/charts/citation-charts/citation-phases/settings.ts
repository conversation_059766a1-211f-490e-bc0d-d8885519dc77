import {chartsConfig} from '../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    plotBackgroundColor: null,
    plotBorderWidth: 0,
    plotShadow: false,
    options3d: {
      enabled: true,
      alpha: 50,
      beta: 0,
      frame: {
        bottom: {}
      }
    },
    spacing: [0, 0, 10, 0],
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: chartsConfig.colorPalette,
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold">${this.point.name}</span><br />
            <span>Patent families:</span><span style="font-weight:bold">
            ${this.point.y}</span><br /><span>
            Percent:</span><span style="font-weight:bold">
            ${this.point.percentage.toFixed(2)}%</span>`
    }
  },

  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      innerSize: '35%',
      depth: 60,
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles
      }
    }
  },
  series: [{
    type: 'pie',
    name: 'Percentual',
    data: []
  }]
};
