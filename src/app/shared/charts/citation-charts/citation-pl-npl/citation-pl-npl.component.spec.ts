import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CitationPlNplComponent } from './citation-pl-npl.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { HighchartsChartModule } from 'highcharts-angular';
import { provideMatomo } from 'ngx-matomo-client';

describe('CitationPlNplComponent', () => {
  let component: CitationPlNplComponent;
  let fixture: ComponentFixture<CitationPlNplComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CitationPlNplComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([]), HighchartsChartModule],
      providers: [
        SemanticSearchStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CitationPlNplComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
