import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import * as popupInformation from '@shared/charts/popup-information';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-citation-pl-npl',
  templateUrl: './citation-pl-npl.component.html',
  styleUrls: ['./citation-pl-npl.component.scss']
})
export class CitationPlNplComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = CitationPlNplComponent;
  chartName = 'citation_pl_npl';
  title = 'Patent and non-patent literature';
  chartOptions = settings.chartSetting;

  tooltipText = popupInformation.citationPlNpl;
  popupText = popupInformation.citationPlNpl;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const extraFilters = this.storeService.extraFilters;
    extraFilters.citation_pl_npl = event.point.value;
    this.storeService.extraFilters = extraFilters;

    filter.value = event.point.name;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }
    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }
}
