import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CitationTypesComponent } from './citation-types.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { HighchartsChartModule } from 'highcharts-angular';
import { provideMatomo } from 'ngx-matomo-client';

describe('CitationTypesComponent', () => {
  let component: CitationTypesComponent;
  let fixture: ComponentFixture<CitationTypesComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CitationTypesComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([]), HighchartsChartModule],
      providers: [
        SemanticSearchStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CitationTypesComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
