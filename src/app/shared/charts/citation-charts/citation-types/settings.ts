import { chartsConfig } from "@shared/charts/config";

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    plotBackgroundColor: null,
    plotBorderWidth: 0,
    plotShadow: false,
    spacing: [0, 0, 10, 0],
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: chartsConfig.colorPalette,
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      var title = this.point.name == this.point.title ? this.point.title : this.point.name + ': ' + this.point.title;
      return `<span style="font-weight:bold">${title}</span><br />
            <span>Patent families:</span><span style="font-weight:bold">
            ${this.point.y}</span><br /><span>
            Percent:</span><span style="font-weight:bold">
            ${this.point.percentage.toFixed(2)}%</span>`
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      },
      startAngle: -90,
      endAngle: 90,
      center: ['50%', '75%']
    }
  },
  series: [{
    type: 'pie',
    innerSize: '50%',
    data: []
  }]
};
