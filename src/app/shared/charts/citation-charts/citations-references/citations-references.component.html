<app-base-card-chart [chartName]="chartName" [chart]="chart" [popupText]="popupText" [showFavoriteOption]="false"
                     [title]="title" [tooltipText]="tooltipText" [storeService]="storeService" [component]="component"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
