import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    plotBackgroundColor: null,
    plotBorderWidth: 0,
    plotShadow: false,
    spacing: [0, 0, 10, 0],
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: [chartsConfig.colorPalette[0], chartsConfig.colorPalette[2], chartsConfig.colorPalette[4]],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold">${this.point.name}</span><br />
            <span>Patent families:</span>
            <span style="font-weight:bold">${this.point.families_amount}</span><br />
            <span>${this.point.name} amount:</span>
            <span style="font-weight:bold">${this.point.y}</span><br />
            <span>Percent:</span>
            <span style="font-weight:bold">${this.point.percentage.toFixed(2)}%</span>`
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        distance: -50,
        style: chartsConfig.dataLabelStyles,
      },
      startAngle: -90,
      endAngle: 90,
      center: ['50%', '75%']
    }
  },
  series: [{
    type: 'pie',
    innerSize: '50%',
    data: []
  }]
};
