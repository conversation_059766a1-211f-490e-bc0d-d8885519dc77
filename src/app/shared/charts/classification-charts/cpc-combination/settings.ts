import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    zoomType: 'xy',
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 30, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    align: 'center',
    verticalAlign: 'top',
    layout: 'horizontal',
    itemStyle: chartsConfig.textStyles
  },
  title: {
    text: null
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 2,
      marker: {
        enabled: false
      }
    }
  },
  series: [{
    color: chartsConfig.similarityColor,
    data: [],
    name: 'Similarity',
    type: 'spline',
    zIndex: 2,
    events: {
      legendItemClick() {
        return false;
      },
    }
  }, {
    data: [],
    events: {
      legendItemClick() {
        return false;
      },
    },
    name: 'CPC combinations',
    type: 'column',
    yAxis: 1,
    showInLegend: false,
  }],
  tooltip: {
    style: {...chartsConfig.tooltipStyles, pointerEvents: 'auto'},
    positioner: function (labelWidth, labelHeight, point) {
      let tooltipX = 0;
      if (labelWidth + point.plotX < 596) {
        tooltipX = point.plotX - 5;
      } else if (point.plotX > 596 - 70) {
        tooltipX = (point.plotX - labelWidth + 100);
      } else {
        tooltipX = 596 - labelWidth;
      }

      let tooltipY = 0;
      if (point.plotY < labelHeight) {
        tooltipY = 70;
      } else {
        tooltipY = point.plotY - labelHeight + 30;
      }

      return {
        x: tooltipX,
        y: tooltipY
      };
    },
    shared: true,
    useHTML: true
  },
  xAxis: [{
    categories: [],
    crosshair: true,
    rotation: -45,
    labels: {
      useHTML: true,
      style: chartsConfig.textStyles,
      formatter: function () {
        const firstPart = typeof(this.value) === 'string' && this.value?.includes('-') ? this.value.split('-')[0] + '...' : this.value;
        return `<div class="tooltip-cpc-combination-cpc-code" title="${this.value}">${firstPart}</div>`;
      },
    }
  }],
  yAxis: [{
    allowDecimals: false,
    endOnTick: false,
    lineWidth: 1,
    opposite: true,
    visible: false,
    title: {
      enabled: false,
      style: chartsConfig.textStyles
    },
    labels: {
      enabled: false,
      style: chartsConfig.textStyles
    }
  }, {
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }]
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 50;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};

export const tooltipText = `This chart indicates which combinations of two CPC codes might be most relevant to your patent portfolio. The bars are sorted by the number of patent families using the class combinations displayed on the horizontal axis. An orange line shows the average similarity score of your search to the patent families using these CPC code combinations.<br>
<b>Suggestion:</b><br>
While the number of times the CPC codes are used is more impactful for a portfolio on the whole, the peaks of the orange curve will reveal which class combinations are most similar to your input. This way, you can identify relevant CPC codes you may have omitted from your search.`
