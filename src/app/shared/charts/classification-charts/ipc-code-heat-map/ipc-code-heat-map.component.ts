import { tooltipText } from './../../basic-charts/applicants/settings';
import { Component, Injector, OnDestroy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';

import { PatentClassificationService } from '@core/services';
import _ from 'lodash';
import { generateCpcIpcDescription, openTooltipPopup } from '../../utils';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { Tooltip } from 'bootstrap';

declare var $: any;

@Component({
  selector: 'app-ipc-code-heat-map',
  templateUrl: './ipc-code-heat-map.component.html',
  styleUrls: ['./ipc-code-heat-map.component.scss']
})
export class IpcCodeHeatMapComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = IpcCodeHeatMapComponent;
  chartName = 'classification_ipc_time';
  title = 'IPC codes over time';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;

  private years: any;
  private labelsY: any;
  private letters: Array<string>;
  private currentPoint = null;

  constructor(
    protected injector: Injector,
    private patentClassificationService: PatentClassificationService
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;

        const {years, labelsY, letters} = chart;
        this.years = years;
        this.labelsY = labelsY;
        this.letters = letters;

        this.updateChartData();
        this.loadIpcDescriptions();
      }
    });
    this.subscriptions.add(charts$);

    const self = this;
    $(document).off('click', '.ipc-code-heatmap-tooltip');
    $(document).on('click', '.ipc-code-heatmap-tooltip', function (e) {
      openTooltipPopup('', self.tooltip(self.currentPoint));
    });

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point && !$(event.target).hasClass('tooltip-ipc-code-heatmap')) {
      return;
    }

    this.clearLabelsTooltip();

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    let ipcClass = '';
    let year = '';
    if (event.point) {
      ipcClass = event.point.class;
      year = event.point.year;
      filter.query = `((${(this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=')}${event.point.year}) AND (IPC=${event.point.class}*))`;
      filter.value = `Class: ${event.point.class}, Year: ${event.point.year}`;
    } else {
      ipcClass = event.target.textContent;
      filter.query = `(IPC=${event.target.textContent}*)`;
      filter.value = `Class: ${event.target.textContent}`;
    }

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    const selectedValues = this.storeService.chartSelectedValues;
    selectedValues[this.chartName] = {
      year_level: year,
      level: ipcClass
    };

    this.storeService.chartSelectedValues = selectedValues;

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupChartTooltip();
      this.updateChartData();
    });
  }

  private setupChartTooltip() {
    if (this.chart?.options?.tooltip) {
      const self = this;
      this.chart['options'].tooltip['formatter'] = (point) => {
        self.currentPoint = point.chart.hoverPoints[0];
        return self.tooltip(point.chart.hoverPoints[0]);
      };
    }

    this.setupLabelsTooltip();
  }

  protected tooltipToggleCssSelector(): string {
    return 'app-ipc-code-heat-map .tooltip-ipc-code-heatmap';
  }

  private tooltip(point) {
    if (!point || this.labelsY.length === 0 || this.labelsY.length <= point.y) {
      return false;
    }

    const yAxis = this.labelsY[point.y];
    const description = yAxis.text?.length > 0 ? `<div class="oc-text mb-0">IPC code description: ${yAxis.text}</div>` : '';
    return `<div class="ipc-code-heatmap-tooltip">
                    <p class="oc-text fw-bold mb-1">${yAxis.class}</b></p>
                    <p class="oc-text mb-1">Year: <span class="fw-bold">${this.years[point.x]}</span></p>
                    <p class="oc-text mb-1">Patent families: <span class="fw-bold">${point.value}</span></p>
                    ${description}
                </div>`;
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
      this.chart.xAxis[0].setCategories(this.years);
      this.chart.yAxis[0].setCategories(this.labelsY);
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }

  private loadIpcDescriptions() {
    if (this.letters && this.letters.length > 0) {
      const params = {classification_symbol: 'in:' + this.letters.join(','), page_size: 100};

      const getIpc$ = this.patentClassificationService.getIpc(params).subscribe({
        next: (classifications) => {
          if (this.chart && this.chart.yAxis && classifications.data.page.total_hits > 0) {
            for (const y of this.labelsY) {
              const item = classifications.data.results[y.class];
              y.text = item ? generateCpcIpcDescription(item['descriptions'], item['title']) : y.class;
            }
            this.updateChartData();
            this.setupLabelsTooltip();
          }
        }
      });
      this.subscriptions.add(getIpc$);
    }
  }

  private clearLabelsTooltip() {
    Array.from(document.querySelectorAll("app-ipc-code-heat-map .tooltip-ipc-code-heatmap")).forEach(el => {
      $(el).tooltip('dispose');
    });
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.length < 1;
  }  
}
