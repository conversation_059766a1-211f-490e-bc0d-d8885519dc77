<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [popupText]="tooltipText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [urlTooltipArticle]="urlTooltipArticle.classifications" [isEmpty]="isEmpty"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
