import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { generateCpcIpcDescription, openTooltipPopup } from '@shared/charts';
import { take } from 'rxjs/operators';
import { PatentClassificationService } from '@core';


@Component({
  selector: 'app-main-cpc-codes',
  templateUrl: './main-cpc-codes.component.html',
  styleUrls: ['./main-cpc-codes.component.scss']
})
export class MainCpcCodesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = MainCpcCodesComponent;
  chartName = 'classification_main_cpc_codes';
  title = 'Main CPC codes';
  chartOptions = settings.chartSetting;
  currentPoint = null;

  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector,
    private patentClassificationService: PatentClassificationService
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    const self = this;

    $(document).off('click', '.main-cpc-codes-tooltip');
    $(document).on('click', '.main-cpc-codes-tooltip', function (e) {
      openTooltipPopup('', self.tooltip(self.currentPoint));
    });

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart',
      };
    }

    const code = event.point.name;

    filter.query = `(CPC=${code})`;
    filter.value = `${code}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupChartTooltip();
      this.updateChartData();

      this.chart['tooltipName'] = this.chartName;
      if (!Highcharts.Tooltip.prototype['isTooltipWrapped']) {
        Highcharts.wrap(Highcharts.Tooltip.prototype, 'refresh', function (proceed) {
          var delay = this.chart.tooltipName ? 500 : 0;
          var chart = this.chart;

          clearTimeout(chart.tooltipTimeout);
          chart.tooltipTimeout = setTimeout(function () {
            proceed.apply(chart.tooltip, Array.prototype.slice.call(arguments, 1));
          }, delay);
        });

        Highcharts.wrap(Highcharts.Tooltip.prototype, 'hide', function (proceed) {
          clearTimeout(this.chart.tooltipTimeout);
          proceed.apply(this, Array.prototype.slice.call(arguments, 1));
        });
      }

      Highcharts.Tooltip.prototype['isTooltipWrapped'] = true;
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
      this.loadCPCDescriptions();
      this.setupChartTooltip();
    }
  }

  private setupChartTooltip() {
    if (this.chart?.options?.tooltip) {
      const self = this;
      this.chart.options.tooltip['formatter'] = (point) => {
        self.currentPoint = point.chart.hoverPoints[0];
        return self.tooltip(point.chart.hoverPoints[0]);
      };
    }
  }

  private tooltip(point) {
    const percent = ((point.value / point.total) * 100).toFixed(2);
    const description = point.text?.length ? `<p class="oc-text mb-0">CPC code description: ${point.text}</p>` : '';
    return `<div class="main-cpc-codes-tooltip">
              <p class="oc-text mb-1">CPC: <span class="fw-bold">${point.name}</span></p>
              <p class="oc-text mb-1">Patent families: <span class="fw-bold">${point.value}</span></p>
              <p class="oc-text mb-1">Percent: <span class="fw-bold">${percent}%</span></p>
              ${description}
            </div>`;
  }

  private loadCPCDescriptions() {
    if (this.chart && this.datasource?.length && this.chart.series?.length) {
      const cpcCodes = this.datasource.map((item) => item.name).join(',');
      const params = {
        classification_symbol: 'in:' + cpcCodes,
        page_size: 100
      };
      const getCpc$ = this.patentClassificationService.getCpc(params).pipe(take(1)).subscribe({
        next: (classifications) => {
          for (const item of this.datasource) {
            const cpc = classifications.data.results[item.name];
            item.text = cpc ? generateCpcIpcDescription(cpc['descriptions'], cpc['title']) : item.name;
          }
          this.chart.series[0].setData(this.datasource, true, true, false);
          this.reflowChart();
        }
      });
      this.subscriptions.add(getCpc$);
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.filter(x => x['value'] > 0).length == 0;
  }  
}
