import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false,},
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360
  },
  title: { text: null },
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles, pointerEvents: 'auto'},
  },
  series: [{
    allowPointSelect: false,
    borderWidth: 0,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: '#7CAED5'
      },
      hover: {
        color: '#7CAED5'
      },
    },
    dataLabels: {
      enabled: true,
      useHTML: true,
      style: {
        ...chartsConfig.dataLabelStylesHeatMap,
        textAlign: 'center',
        verticalAlign: 'middle'
      },
      formatter() {
        const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
        return `<span>${this.point.name}</span><br/><span>${this.point.value} (${percent}%)</span>`;
      },
    },
    events: {
      mouseOut: function () {
        this.chart.tooltip.hide();
      },
    }
  }],
  yAxis: {visible: false},
  xAxis: {visible: false},
};

export const tooltipText = `This heatmap provides an overview of the primary CPC codes used within the analyzed patents.<br>
Each box in the chart corresponds to a unique code, ordered by name, showing its patent-family count and the corresponding percentage of the total number of patent families. The color deepens with an increasing number of patent families.<br>
<b>Suggestion:</b><br>
Darker colors indicate areas of greater patenting activity and may signify promising technological areas.`
