import { Component, Injector, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import * as popupText from '../../popup-information';

import { PatentClassificationService } from '@core/services';
import { take } from 'rxjs/operators';
import _ from 'lodash';
import { generateCpcIpcDescription, openTooltipPopup } from '../../utils';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { Tooltip } from 'bootstrap';
import { BooleanSearchStoreService } from '@core/store/boolean-search-store/boolean-search-store.service';

declare var $: any;

@Component({
  selector: 'app-top-cpc-code',
  templateUrl: './top-cpc-code.component.html',
  styleUrls: ['./top-cpc-code.component.scss']
})
export class TopCpcCodeComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = TopCpcCodeComponent;
  chartName = 'classification_top_cpc';
  title = 'CPC codes and similarity';
  chartOptions = settings.chartSetting;
  currentPoint = null;
  sliderOptions: Options = settings.sliderSetting;
  quantity = settings.MAX_VALUE_SLIDER;
  tooltipText = settings.tooltipText;
  popupText = popupText.classificationTopCpcCode;
  private cpcClasses: any;

  constructor(
    protected injector: Injector,
    private patentClassificationService: PatentClassificationService
  ) {
    super(injector);
  }

  ngOnInit() {

    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.dataSource;
        this.cpcClasses = chart.cpcClasses;

        this.updateChartData();
        this.onUserChangeEnd();
      }
    });
    this.subscriptions.add(charts$);

    const newSearchEvent$ = this.storeService.newSearchEvent.subscribe({
      next: value => {
        if (value) {
          this.quantity = settings.MAX_VALUE_SLIDER;
        }
      }
    });
    this.subscriptions.add(newSearchEvent$);

    const self = this;
    $(document).off('click', '.top-cpc-oc-tooltip');
    $(document).on('click', '.top-cpc-oc-tooltip', function (e) {
      openTooltipPopup('', self.tooltip(self.currentPoint));
    });

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `(CPC=${event.point.category})`;
    filter.value = event.point.category;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);

  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource.average.slice(0, this.quantity));
      this.chart.series[1].setData(this.datasource.value.slice(0, this.quantity));
      this.chart.xAxis[0].setCategories(this.datasource.categories.slice(0, this.quantity));
      this.setupLabelsTooltip();
    }
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupChartTooltip();
      this.updateChartData();

      this.chart['tooltipName'] = this.chartName;
      if (!Highcharts.Tooltip.prototype['isTooltipWrapped']) {
        Highcharts.wrap(Highcharts.Tooltip.prototype, 'refresh', function (proceed) {
          var delay = this.chart.tooltipName ? 1000 : 0;
          var chart = this.chart;

          clearTimeout(chart.tooltipTimeout);
          chart.tooltipTimeout = setTimeout(function () {
            proceed.apply(chart.tooltip, Array.prototype.slice.call(arguments, 1));
          }, delay);
        });

        Highcharts.wrap(Highcharts.Tooltip.prototype, 'hide', function (proceed) {
          clearTimeout(this.chart.tooltipTimeout);
          proceed.apply(this, Array.prototype.slice.call(arguments, 1));
        });
      }

      Highcharts.Tooltip.prototype['isTooltipWrapped'] = true;
    });
  }

  private setupChartTooltip() {
    if (this.chart?.options?.tooltip) {
      const self = this;
      this.chart['options'].tooltip['formatter'] = (point) => {
        self.currentPoint = point.chart.hoverPoints;
        return self.tooltip(point.chart.hoverPoints);
      };
    }

    this.showLabelsTooltip();
  }

  private showLabelsTooltip() {
    if (this.chart['options'].xAxis?.length > 0) {
      const self = this;
      this.chart['options'].xAxis[0].labels['formatter'] = (point) => {
        const cpc = self.cpcClasses.find((o) => o.name === point.value);
        const text = cpc?.text ?
          `<div class="fw-bold text-start">${point.value}</div><div class="text-start">${cpc.text}</div>` : point.value;
        return `<div class="tooltip-top-cpc-code" data-original-title='${text}'>${point.value}</div>`;
      };
    }

    this.setupLabelsTooltip();
  }

  protected tooltipToggleCssSelector(): string {
    return 'app-top-cpc-code .tooltip-top-cpc-code';
  }

  private tooltip(points) {
    if (points && points.length > 1) {
      const description = points[1].text?.length > 0 ? `<p class="oc-text mb-0">CPC code description: ${points[1].text}</p>` : '';
      return `<div class="top-cpc-oc-tooltip">
                  <p class="oc-text mb-1">CPC: <span class="fw-bold">${points[0].category}</span></p>
                  <p class="oc-text mb-1">Average index: <span class="fw-bold">${points[0].y}</span></p>
                  <p class="oc-text mb-1">Patent families: <span class="fw-bold">${points[1].y}</span></p>
                  ${description}
              </div>`;
    } else {
      const description = points[0].text?.length > 0 ? `<p class="oc-text mb-0">CPC code description: ${points[0].text}</p>` : '';
      return `<div class="top-cpc-oc-tooltip">
                  <p class="oc-text mb-1">CPC: <span class="fw-bold">${points[0].category}</span></p>
                  <p class="oc-text mb-1">Patent families: <span class="fw-bold">${points[0].y}</span></p>
                  ${description}
              </div>`;
    }
  }

  hasSimilarity(): boolean{
    if(this.storeService instanceof BooleanSearchStoreService && !this.storeService.sortBySimilarity){
      return false;
    }
    return this.datasource.average && this.datasource.average.some((i: number) => i !== 0);
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const hasSimilarities = this.hasSimilarity();
      this.chart.series[0].update({showInLegend: hasSimilarities, visible: hasSimilarities});
      this.chart.legend.update({ enabled: hasSimilarities });
      this.defineColors();
      this.chart.xAxis[0].setCategories(this.datasource.categories);
      this.chart.series[1].setData(this.datasource.value, true, true, false);
      if (hasSimilarities) {
        this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
        this.chart.series[0].setData(this.datasource.average, true, true, false);
      }
      this.reflowChart();
    }

    if (this.datasource && this.datasource.categories.length > 0) {
      const params = {classification_symbol: 'in:' + this.datasource.categories, page_size: 100};
      const getCpc$ = this.patentClassificationService.getCpc(params).pipe(take(1)).subscribe({
        next: (classifications) => {
          if (this.chart && this.chart.series) {
            for (let i = 0; i < this.cpcClasses.length; i++) {
              const cpc = classifications.data.results[this.cpcClasses[i].name];
              if (cpc) {
                this.cpcClasses[i].text = generateCpcIpcDescription(cpc['descriptions'], cpc['title']);
              }
            }
            this.datasource.value = this.cpcClasses;
            this.chart.series[1].setData(this.datasource.value, true, true, false);
            this.chart.redraw(false);
            this.setupLabelsTooltip();
          }
        }
      });
      this.subscriptions.add(getCpc$);
    }
  }

  private defineColors() {
    this.cpcClasses.forEach(item => {
      item['color'] = this.chartsService.classificationCodesColor(item.name)
    });
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.value.length < 2;
  }  
}
