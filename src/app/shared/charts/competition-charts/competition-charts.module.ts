import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';

import {
  PortfolioAnalyticsProfileComponent
} from './portfolio-analytics-profile/portfolio-analytics-profile.component';
import { CompetitorsIpcProfileComponent } from './competitors-ipc-profile/competitors-ipc-profile.component';
import { CompetitorsCpcProfileComponent } from './competitors-cpc-profile/competitors-cpc-profile.component';
import { CompetitorsOverTimeComponent } from './competitors-over-time/competitors-over-time.component';
import { CompetitorsTechProfileComponent } from './competitors-tech-profile/competitors-tech-profile.component';
import {
  CompetitorsAuthoritiesProfileComponent
} from './competitors-authorities-profile/competitors-authorities-profile.component';
@NgModule({
  declarations: [
    PortfolioAnalyticsProfileComponent,
    CompetitorsIpcProfileComponent,
    CompetitorsCpcProfileComponent,
    CompetitorsOverTimeComponent,
    CompetitorsTechProfileComponent,
    CompetitorsAuthoritiesProfileComponent
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    PortfolioAnalyticsProfileComponent,
    CompetitorsIpcProfileComponent,
    CompetitorsCpcProfileComponent,
    CompetitorsOverTimeComponent,
    CompetitorsTechProfileComponent,
    CompetitorsAuthoritiesProfileComponent
  ]
})
export class CompetitionChartsModule {
}
