<app-base-card-chart (sliderChanged)="updateChartData()" [(sliderValue)]="quantity" [chartName]="chartName"
                     [chart]="chart" [component]="component" [popupText]="tooltipText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [sliderOptions]="sliderOptions"
                     [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
