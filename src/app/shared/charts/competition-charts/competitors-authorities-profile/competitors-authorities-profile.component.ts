import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-competitors-authorities-profile',
  templateUrl: './competitors-authorities-profile.component.html',
  styleUrls: ['./competitors-authorities-profile.component.scss']
})
export class CompetitorsAuthoritiesProfileComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = CompetitorsAuthoritiesProfileComponent;
  chartName = 'competitors_authorities_profile';
  title = 'Competitors authorities profile';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  currentPoint = null;
  sliderOptions: Options = settings.sliderSetting;
  quantity = settings.MAX_VALUE_SLIDER;
  tooltipText = `<p>This chart displays the docdb patent families of the results listed below distributed over the associated authority. It is based on the top 15 applicants, which can be enlarged by choosing: “<b>Enter the number of competitors to display in the charts</b>”.</p>`;
  private totalHits: any;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {

    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.totalHits = chart.total;
        this.mappedApplicants = charts['mappedApplicants'];

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    const newSearchEvent$ = this.storeService.newSearchEvent.subscribe({
      next: value => {
        if (value) {
          this.quantity = settings.MAX_VALUE_SLIDER;
        }
      }
    });
    this.subscriptions.add(newSearchEvent$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  sliceTop(arr) {
    const newArr = [];
    const auths = this.datasource.yAxis.slice(0, this.quantity);
    let qtd = 0;
    arr.forEach(item => {
      if (auths.indexOf(item[1]) === -1 && qtd < this.quantity) {
        auths.push(item[1]);
        qtd++;
      }
      if (item[1] < this.quantity) {
        newArr.push(item);
      }

    });

    return {values: newArr, authorities: auths};

  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const applicant = event.point.series.xAxis.categories[event.point.x];
    const auth = event.point.series.yAxis.categories[event.point.y];
    filter.query = `((APPLICANTS=(${this.applicantsAliasesService.getOriginalApplicants([applicant]).join(' OR ')})) AND
    (AUTHORITIES=${auth}))`;

    filter.value = `Applicant: ${applicant}, Authority: ${auth}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  // Slider
  updateChartData(): void {
    if (this.chart && this.chart.series && this.datasource) {
      const {values, authorities} = this.sliceTop(this.datasource.values);

      this.chart.series[0].options.totalHits = this.totalHits;

      this.chart.xAxis[0].setCategories(this.datasource.xAxis);
      this.chart.yAxis[0].setCategories(authorities);
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }
}
