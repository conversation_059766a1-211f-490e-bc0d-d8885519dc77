import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
    marginRight: 70,
  },
  colorAxis: {
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    align: 'right',
    layout: 'vertical',
    margin: 0,
    symbolHeight: 320,
    verticalAlign: 'top',
    itemMarginTop: 0,
    itemStyle: chartsConfig.textStyles
  },
  title: {
    text: null
  },
  series: [{
    allowPointSelect: false,
    borderColor: '#3e50b4',
    borderWidth: 0.1,
    cursor: 'pointer',
    data: [],
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false
    },
  }],
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const label = this.series.xAxis.categories[this.point.x];
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
                <span style="font-weight: bold;">${label}</span><br>
                <span>Auth: </span>
                <span style="font-weight: bold;">
                ${this.series.yAxis.categories[this.point.y]}</span><br>
                <span>Patent families: </span>
                <span style="font-weight: bold;">${this.point.value}</span><br>
                <span>Percent: </span>
                <span style="font-weight: bold;">${percent}%</span><br>`;
    },
  },
  xAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    labels: {
      enabled: true,
      rotation: -45,
      style: chartsConfig.textStyles,
    },
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLenght: 1,
    tickInterval: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: null
    }
  },
  yAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLength: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: 'Technologies',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles,
      formatter: function () {
        return `<div class="text-center"><i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.SM)}"></i><br>${this.value}</div>`;
      },
      useHTML: true,
    }
  }
};

export const MIN_VALUE_SLIDER = 2;
export const MAX_VALUE_SLIDER = 25;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};
