import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    symbolHeight: 360,
    verticalAlign: 'top',
  },
  title: {
    text: null
  },
  series: [{
    allowPointSelect: false,
    borderWidth: 0,
    cursor: 'pointer',
    data: [],
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false,
      useHTML: true,
    },
  }],
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles, pointerEvents: 'auto'},
    stickOnContact: true
  },
  xAxis: {
    categories: [],
    gridLineWidth: 1,
    labels: {
      enabled: true,
      rotation: -45,
      style: chartsConfig.textStyles
    },
    title: {
      text: 'Applicant',
      style: chartsConfig.textStyles
    },
  },
  yAxis: {
    categories: [],
    gridLineWidth: 1,
    rotation: 45,
    title: {
      text: 'CPC code',
      style: chartsConfig.textStyles
    },
    labels: {
      useHTML: true,
      style: chartsConfig.textStyles
    }
  }
};

export const MIN_VALUE_SLIDER = 5;
export const MAX_VALUE_SLIDER = 30;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  name: "Number of CPC codes",
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};

export const tooltipText = `This chart visualizes the CPC code distribution of the main applicants for the analyzed patents.<br>
The horizontal axis shows the most used CPC codes, while the vertical axis displays the main applicants. The color intensity of each data point on the chart is based on the number of patents where that specific code is present, represented in absolute values and as a percentage of all analyzed patents.<br>
<b>Suggestion:</b><br>
Use this chart to gain insights into top applicants' areas of technological interest. Adjust the displayed CPC codes to customize your analysis.`
