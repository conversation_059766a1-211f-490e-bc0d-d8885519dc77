import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import * as settings from './settings';

import { PatentClassificationService } from '@core/services';
import { generateCpcIpcDescription, openTooltipPopup } from '@shared/charts/utils';
import _ from 'lodash';
import { take } from 'rxjs/operators';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

declare var $: any;

@Component({
  selector: 'app-competitors-ipc-profile',
  templateUrl: './competitors-ipc-profile.component.html',
  styleUrls: ['./competitors-ipc-profile.component.scss']
})
export class CompetitorsIpcProfileComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = CompetitorsIpcProfileComponent;
  chartName = 'competitors_ipc_profile';
  title = 'Applicants per IPC code';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  currentPoint = null;
  sliderOptions: Options = settings.sliderSetting;
  tooltipText = settings.tooltipText;
  private DEFAULT_QUANTITY_SLIDER = 20;
  quantity = this.DEFAULT_QUANTITY_SLIDER;
  private totalHits: any;

  constructor(
    protected injector: Injector,
    private patentClassificationService: PatentClassificationService
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.totalHits = chart.total;
        this.mappedApplicants = charts['mappedApplicants'];

        this.onUserChangeEnd();
      }
    });
    this.subscriptions.add(charts$);

    const newSearchEvent$ = this.storeService.newSearchEvent.subscribe({
      next: value => {
        if (value) {
          this.quantity = this.DEFAULT_QUANTITY_SLIDER;
        }
      }
    });
    this.subscriptions.add(newSearchEvent$);

    const self = this;
    $(document).off('click', '.competitors-ipc-profile-tooltip');
    $(document).on('click', '.competitors-ipc-profile-tooltip', function (e) {
      openTooltipPopup('', self.tooltip(self.currentPoint));
    });

    super.updateChartOptions();
    super.subscribeChartEvents();
    super.notifyGetAllApplicantAliases();
  }

  sliceTop(arr, size) {
    const newArr = [];
    arr.forEach(item => {
      if (item[1] < this.quantity) {
        newArr.push(item);
      }
    });
    return newArr;
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const applicant = event.point.series.xAxis.categories[event.point.x];
    const ipcClass = event.point.series.yAxis.categories[event.point.y].class;
    filter.query = `((APPLICANTS=(${this.applicantsAliasesService.getOriginalApplicants([applicant]).join(' OR ')})) AND
    (IPC=${ipcClass}))`;

    filter.value = `Applicant: ${applicant}, Class: ${ipcClass}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      this.updateChartData();
      this.setupChartTooltip();
      this.loadIPCDescriptions();
    }
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupChartTooltip();
      this.onUserChangeEnd();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const top = this.sliceTop(this.datasource.values, this.quantity);
      this.chart.series[0].options.totalHits = this.totalHits;
      this.chart.xAxis[0].setCategories(this.datasource.xAxis);
      this.chart.yAxis[0].setCategories([...this.datasource.yAxis.slice(0, this.quantity), {class: ''}]);
      this.chart.series[0].setData(top, true, true, false);
      this.chart.data = [top];
      this.reflowChart();
    }
  }

  private setupChartTooltip() {
    if (this.chart?.options?.tooltip) {
      const self = this;
      this.chart.options.tooltip['formatter'] = (point) => {
        self.currentPoint = point.chart.hoverPoints[0];
        return self.tooltip(point.chart.hoverPoints[0]);
      };
    }

    this.showLabelsTooltip();
  }

  private showLabelsTooltip() {
    Highcharts.AST.allowedAttributes.push('data-original-title');

    if (this.chart['options'].yAxis?.length > 0) {
      this.chart['options'].yAxis[0].labels['formatter'] = (point) => {
        const text = point.value?.text ?
          `<div class="fw-bold text-start">${point.value?.class}</div><div class="text-start">${point.value.text}</div>` : point.value?.class;
        return `<div class="tooltip-competitors-ipc-profile" data-original-title='${text}'>${point.value?.class}</div>`;
      };
    }

    this.setupLabelsTooltip();
  }

  protected tooltipToggleCssSelector(): string {
    return 'app-competitors-ipc-profile .tooltip-competitors-ipc-profile';
  }

  private tooltip(point) {
    if (point.x > this.datasource?.xAxis?.length || point.y > this.datasource?.yAxis?.length) {
      return;
    }

    const label = this.datasource.xAxis[point.x];
    const yAxis = this.datasource.yAxis[point.y];
    const percent = ((point.value / this.chart.series[0].options.totalHits) * 100).toFixed(2);
    const description = yAxis.text?.length > 0 ? `<p class="oc-text mb-0">IPC code description: ${yAxis.text}</p>` : '';
    return `<div class="competitors-ipc-profile-tooltip">
              <p class="oc-text mb-1 fw-bold">${label}</p>
              <p class="oc-text mb-1">IPC: <span class="fw-bold">${yAxis.class}</span></p>
              <p class="oc-text mb-1">Patent families: <span class="fw-bold">${point.value}</span></p>
              <p class="oc-text mb-1">Percent: <span class="fw-bold">${percent}%</span></p>
              ${description}
            </div>`;
  }

  private loadIPCDescriptions() {
    if (this.chart?.yAxis && this.datasource?.yAxis) {
      const params = {
        classification_symbol: 'in:' + this.datasource.yAxis.map((n) => n.class).join(','),
        page_size: 100
      };
      const getIpc$ = this.patentClassificationService.getIpc(params).pipe(take(1)).subscribe({
        next: (classifications) => {
          if (this.chart?.yAxis && this.datasource?.yAxis) {
            for (const y of this.datasource.yAxis) {
              const item = classifications.data.results[y.class];
              y.text = item ? generateCpcIpcDescription(item['descriptions'], item['title']) : y.class;
            }
            this.updateChartData();
            this.showLabelsTooltip();
          }
        }
      });
      this.subscriptions.add(getIpc$);
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }
}
