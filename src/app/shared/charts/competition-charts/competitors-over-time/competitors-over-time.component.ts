import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { Options } from '@angular-slider/ngx-slider';

@Component({
  selector: 'app-competitors-over-time',
  templateUrl: './competitors-over-time.component.html',
  styleUrls: ['./competitors-over-time.component.scss']
})
export class CompetitorsOverTimeComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = CompetitorsOverTimeComponent;
  chartName = 'competitors_over_time';
  title = 'Applicants over time';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;

  sliderOptions: Options = settings.sliderSetting;
  quantity = settings.MAX_VALUE_SLIDER;
  currentPoint = null;

  tooltipText = settings.tooltipText;

  private totalHits: any;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.totalHits = chart.total;
        this.mappedApplicants = charts['mappedApplicants'];

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
    super.notifyGetAllApplicantAliases();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const applicant = event.point.series.xAxis.categories[event.point.x];
    const year = event.point.series.yAxis.categories[event.point.y];
    filter.query = `((APPLICANTS=(${this.applicantsAliasesService.getOriginalApplicants([applicant]).join(' OR ')})) AND
    (${(this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=')}${year}))`;

    filter.value = `Applicant: ${applicant}, Year: ${year}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      const xAxis = [...this.datasource.xAxis].slice(0, this.quantity);
      const values = this.datasource.values.filter(i => i[0]< this.quantity);
      this.chart.xAxis[0].setCategories(xAxis);
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.totalHits;
      this.chart.xAxis[0].setCategories(this.datasource.xAxis);
      this.chart.yAxis[0].setCategories(this.datasource.yAxis);
      this.chart.series[0].setData(this.datasource.values, true, true, false);
      this.reflowChart();
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }
}
