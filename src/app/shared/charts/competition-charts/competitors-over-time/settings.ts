import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [10, 0, 10, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360
  },
  title: {
    text: null
  },
  series: [{
    allowPointSelect: false,
    borderWidth: 0,
    cursor: 'pointer',
    data: [],
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false
    },
  }],
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    stickOnContact: true,
    formatter() {
      const label = this.series.xAxis.categories[this.point.x];
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
                <span style="font-weight: bold;">${label}</span><br>
                <span>Year: </span>
                <span style="font-weight: bold;">
                ${this.series.yAxis.categories[this.point.y]}</span><br>
                <span>Patent families: </span>
                <span style="font-weight: bold;">${this.point.value}</span><br>
                <span>Percent: </span>
                <span style="font-weight: bold;">${percent}%</span><br>`;
    },
  },
  xAxis: {
    categories: [],
    gridLineWidth: 1,
    labels: {
      enabled: true,
      rotation: -45,
      style: chartsConfig.textStyles
    },
    title: {
      text: 'Applicant',
      style: chartsConfig.textStyles
    },
  },
  yAxis: {
    categories: [],
    gridLineWidth: 1,
    rotation: 45,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 15;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
  name: 'Number of applicants'
};

export const tooltipText = `This chart presents the number of patent families in the selected field for the main applicants, arranged by priority year.<br>
The horizontal axis shows the priority year, while the vertical axis represents the main applicants. The color intensity of each data point is based on the number of patents, represented in absolute values and as a percentage relative to all analyzed patents.<br>
<b>Suggestion:</b><br>
Analyze how the activity of the main applicants developed over time. Adjust the number of displayed results using the 'Number of applicants' bar to explore the data further.`
