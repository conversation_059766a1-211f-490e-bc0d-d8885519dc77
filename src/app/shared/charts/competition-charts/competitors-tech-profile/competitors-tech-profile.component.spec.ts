import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CompetitorsTechProfileComponent } from './competitors-tech-profile.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

describe('CompetitorsTechProfileComponent', () => {
  let component: CompetitorsTechProfileComponent;
  let fixture: ComponentFixture<CompetitorsTechProfileComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CompetitorsTechProfileComponent],
      imports: [
        HighchartsChartModule,
        NgxSliderModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CompetitorsTechProfileComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
