import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';

import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { abbreviations } from '../../utils';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-competitors-tech-profile',
  templateUrl: './competitors-tech-profile.component.html',
  styleUrls: ['./competitors-tech-profile.component.scss']
})
export class CompetitorsTechProfileComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = CompetitorsTechProfileComponent;
  Highcharts: typeof Highcharts = Highcharts;
  chartName = 'competitors_tech_profile';
  title = 'Applicants per technology fields';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  currentPoint = null;
  sliderOptions: Options = settings.sliderSetting;
  quantity = settings.MAX_VALUE_SLIDER;
  tooltipText = settings.tooltipText;
  private totalHits: any;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.totalHits = chart.total;
        this.datasource = chart.datasource;
        this.mappedApplicants = charts['mappedApplicants'];

        this.onUserChangeEnd();
      }
    });
    this.subscriptions.add(charts$);

    const newSearchEvent$ = this.storeService.newSearchEvent.subscribe({
      next: value => {
        if (value) {
          this.quantity = settings.MAX_VALUE_SLIDER;
        }
      }
    });
    this.subscriptions.add(newSearchEvent$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  sliceTop(arr) {
    let newArr = [];
    const techs = [];
    let qtd = 0;
    arr.forEach(item => {
      if (techs.indexOf(item['y']) === -1 && qtd <= this.quantity) {
        techs.push(item['y']);
        qtd++;
      }
      if (qtd <= this.quantity) {
        newArr.push(item);
      }

    });
    newArr = newArr.map(item => {
      item.y = techs.indexOf(item.y);
      return item;
    });

    const techFields = techs.map(item => this.datasource.yAxis[item]);

    return {values: newArr, techFields: techFields};
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const applicant = event.point.series.xAxis.categories[event.point.x];
    // const area = event.point.series.yAxis.categories[event.point.y];
    const abbr = abbreviations.find(ab => ab.abbr === event.point.series.yAxis.categories[event.point.y]);

    filter.query = `((APPLICANTS=(${this.applicantsAliasesService.getOriginalApplicants([applicant]).join(' OR ')})) AND (TECH_FIELDS=${abbr.full}))`;

    filter.value = `Applicant: ${applicant}, Area: ${abbr.abbr}`;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.totalHits;
      const {values, techFields} = this.sliceTop(this.datasource.values);

      this.chart.xAxis[0].setCategories(this.datasource.xAxis);
      this.chart.yAxis[0].setCategories(techFields);
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.onUserChangeEnd();
    });
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }  
}
