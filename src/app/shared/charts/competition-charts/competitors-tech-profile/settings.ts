import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 10, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  legend: {
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360
  },
  title: {
    text: null
  },
  series: [{
    allowPointSelect: false,
    cursor: 'pointer',
    data: [],
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false
    },
  }],
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const label = this.series.xAxis.categories[this.point.x];
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
                <span style="font-weight: bold;">${label}</span><br>
                <span>Tech: </span>
                <span style="font-weight: bold;">
                ${this.series.yAxis.categories[this.point.y]}</span><br>
                <span>Patent families: </span>
                <span style="font-weight: bold;">${this.point.value}</span><br>
                <span>Percent: </span>
                <span style="font-weight: bold;">${percent}%</span><br>`;
    },
  },
  xAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    },
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLenght: 1,
    tickInterval: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: 'Applicant',
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    categories: [],
    endOnTick: false,
    gridLineWidth: 1,
    minorGridLineWidth: 1,
    minTickInterval: 1,
    minorTickInterval: 1,
    minorTickLenght: 1,
    tickLength: 1,
    tickWidth: 1,
    title: {
      text: 'Technology field',
      style: chartsConfig.textStyles
    },
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    }
  }
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 35;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
  name: 'Number of applicants'
};

export const tooltipText = `This heatmap divides the main applicants in your result list with respect to
<a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">35 technology fields.</a><br>
The horizontal axis lists the main technology fields present in your analyzed results, while the vertical axis lists the main applicants. Each cell represents the number of patent families for a specific applicant and technology field. The color intensity in the heatmap increases with a higher number of patent families, with darker colors indicating more significant activity.<br>
<b>Suggestion:</b><br>
Pay attention to dark-colored regions and how the analyzed results for a specific applicant spread across technology fields. Tracking this can provide valuable information about a company's technological focus.`
