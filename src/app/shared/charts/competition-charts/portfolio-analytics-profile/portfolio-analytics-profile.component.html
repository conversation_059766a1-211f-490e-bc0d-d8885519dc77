<app-base-card-chart (sliderChanged)="changeQuantityCompetition()" [(sliderValue)]="displayQuantity" [chartName]="chartName" [chart]="chart"
                     [component]="component"
                     [popupText]="popupText" [showFavoriteOption]="showFavoriteOption"
                     [showZoomOption]="showZoomOption" [isEmpty]="isEmpty"
                     [sliderOptions]="sliderOptions" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [chartItemHeight]="chartItemHeight"
                     [urlTooltipArticle]="urlTooltipArticle.applicants">
  <div class="h-100 d-flex flex-column align-items-center">

    <highcharts-chart
      (chartInstance)="onChartInstance($event)"
      (click)="selectPoint($event)"
      [Highcharts]='Highcharts'
      [options]='chartOptions'
      constructorType='chart'>
    </highcharts-chart>

    <div class="d-flex align-items-end my-1">
      <div class="me-2">
        <label class="form-label">Horizontal axis:</label>
        <div ngbDropdown>
          <div class="caret-off" ngbDropdownToggle>
            <input [value]="getIndicatorText(xAxis)" class="form-control" placeholder="Select an indicator" readonly/>
            <div class="dropdown-icon"></div>
          </div>
          <div ngbDropdownMenu>
            <div (click)="changeAxis(control, 'x')"
                 *ngFor="let control of indicatorOptions;" [ngClass]="{'active': xAxis === control.value}"
                 class="dropdown-item">{{control.text}}</div>
        </div>
        </div>
      </div>
      <div class="me-2" >
        <label class="form-label">Vertical axis:</label>
        <div ngbDropdown>
        <div class="caret-off" ngbDropdownToggle>
          <input [value]="getIndicatorText(yAxis)" class="form-control" placeholder="Select an indicator" readonly/>
          <div class="dropdown-icon"></div>
        </div>
        <div ngbDropdownMenu>
          <div (click)="changeAxis(control, 'y')"
               *ngFor="let control of indicatorOptions;" [ngClass]="{'active': yAxis === control.value}"
               class="dropdown-item">{{control.text}}</div>
        </div>
        </div>
      </div>
      <div class="ps-0 form-check">
        <label class="checkbox">
          <input (change)="changeLabelVisibility()" [(ngModel)]="displayCompetitors" class="form-check-input"
                 type="checkbox">
          <span class="colorLabel">Show competitors</span>
        </label>
      </div>
    </div>
  </div>
</app-base-card-chart>
