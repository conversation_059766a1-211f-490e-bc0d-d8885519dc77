import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PortfolioAnalyticsProfileComponent } from './portfolio-analytics-profile.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';
import { HighchartsChartModule } from 'highcharts-angular';

describe('PortfolioAnalyticsProfileComponent', () => {
  let component: PortfolioAnalyticsProfileComponent;
  let fixture: ComponentFixture<PortfolioAnalyticsProfileComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PortfolioAnalyticsProfileComponent],
      imports: [
        HighchartsChartModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PortfolioAnalyticsProfileComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
