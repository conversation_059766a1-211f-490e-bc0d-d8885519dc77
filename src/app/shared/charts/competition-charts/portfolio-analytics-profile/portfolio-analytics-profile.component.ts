import { Component, Injector, Input, OnDestroy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import add3d from 'highcharts/highcharts-3d';
import highcharts3D from 'highcharts/highcharts-3d';
import More from 'highcharts/highcharts-more';

import * as settings from './settings';
import * as popupText from '../../popup-information';
import { take } from 'rxjs/operators';
import _ from 'lodash';
import { Options } from '@angular-slider/ngx-slider';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);
highcharts3D(Highcharts);
add3d(Highcharts);

@Component({
  selector: 'app-portfolio-analytics-profile',
  templateUrl: './portfolio-analytics-profile.component.html',
  styleUrls: ['./portfolio-analytics-profile.component.scss']
})
export class PortfolioAnalyticsProfileComponent extends BaseChartComponent implements OnInit, OnDestroy {
  @Input() exportCssSelector = 'highcharts-chart';

  component = PortfolioAnalyticsProfileComponent;
  chartName = 'competitors_portfolio_analytics_profile';
  title = 'Applicants analytics';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  sliderOptions: Options = settings.sliderSetting;
  tooltipText = settings.tooltipText;
  popupText = popupText.competitorsPortfolioAnalyticsProfile;
  displayQuantity = 10;
  xAxis = 'impact_weighted';
  yAxis = 'risk_weighted';
  displayCompetitors = true;
  indicators = {
    x: {
      property: 'impact_weighted',
      label: 'Average value',
      axisText: null,
    },
    y: {
      property: 'risk_weighted',
      label: 'Average risk',
      axisText: null,
    }
  };

  indicatorOptions = [
    {value: 'impact_weighted', text: 'Average value', category: ['Low', 'Medium', 'High', 'Very high']},
    {value: 'recency_years', text: 'Average recency', axisText: 'Years between patent priority year and its references'},
    {value: 'risk_weighted', text: 'Average risk', category: ['Low', 'Medium', 'High', 'Very high']},
    {value: 'tech_fields', text: 'Technology broadness', axisText: 'Total number of technology fields covered'},
    {value: 'market_coverage', text: 'Market coverage', axisText: 'Average number of markets covered'},
    {value: 'citation_backward_count', text: 'Average of citations', axisText: 'Average number of citations'},
    {value: 'citation_forward_count', text: 'Average of references', axisText: 'Average number of references'},
    {value: 'environmental', text: 'Sustainability', axisText: 'Percentage of green patents'}
  ];

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    if(this.chartName in this.storeService.chartParameter){
      if(this.storeService.chartParameter[this.chartName]?.xAxis){
        const ind = this.indicatorOptions.find(o => o.value === this.storeService.chartParameter[this.chartName].xAxis)
        this.setAxisValue('x', ind.value, ind.text, ind.axisText)
      }
      if(this.storeService.chartParameter[this.chartName]?.yAxis){
        const ind = this.indicatorOptions.find(o => o.value === this.storeService.chartParameter[this.chartName].yAxis)
        this.setAxisValue('y', ind.value, ind.text, ind.axisText )
      }
    }

    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.mappedApplicants = charts['mappedApplicants'];
        this.setChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
    super.notifyGetAllApplicantAliases();
  }

  changeAxis(control: { value: string, text: string, axisText?: string }, axisName) {
    this.setAxisValue(axisName, control.value, control.text, control.axisText);
    this.calculateChart();
  }

  calculateChart() {
    const indicatorsToCalculate = [];
    Object.keys(this.indicators).forEach(key => {
      indicatorsToCalculate.push({axis: key, property: this.indicators[key]['property']});
    });
    const payload = {
      charts: ['competitors_portfolio_analytics_profile'],
      parameters: {
        portfolio_analytics_applicants_quantity: this.displayQuantity,
        portfolio_analytics_profile_indicators: indicatorsToCalculate
      }
    };

    const calculateToSingleChart$ = this.chartsService.calculateToSingleChart(payload, this.storeService.searchHash)
      .pipe(take(1))
      .subscribe({
        next: ({ charts }) => {
          this.datasource = charts['competitors_portfolio_analytics_profile']['datasource'];
          this.setChartData();
        },
        error: (err) => { console.log(err); }
      });
    this.subscriptions.add(calculateToSingleChart$);
  }

  changeLabelVisibility() {
    const options = this.chart.series[0].options;
    options.dataLabels.enabled = this.displayCompetitors;
    this.chart.series[0].update(options);
  }

  selectPoint(event) {
    if (!event.point || !event.point.competitor) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = 'APPLICANTS=(' + this.applicantsAliasesService.getOriginalApplicants([event.point.competitor]).join(' OR ') + ')';
    filter.value = event.point.competitor;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setChartData();
    });
  }

  getIndicatorText(val: string): string {
    const option = this.indicatorOptions.find(o => o.value === val);
    return option ? option.text : null;
  }

  changeQuantityCompetition() {
    this.calculateChart();
  }

  private setChartData() {
    if (this.chart && this.chart.series && this.datasource && this.chart.xAxis && this.chart.yAxis) {
      const xAxisMax = Math.ceil(Math.max(...this.datasource.map(o => o.x)));
      const yAxisMax = Math.ceil(Math.max(...this.datasource.map(o => o.y)));
      const xAxis = this.chart.xAxis[0];
      const yAxis = this.chart.yAxis[0];
      const xCategory = this.indicatorOptions.find(o => o.value === this.indicators.x.property)?.category
      const yCategory = this.indicatorOptions.find(o => o.value === this.indicators.y.property)?.category
      this.chart.series[0].options.indicators = this.indicators;
      this.chart.series[0].setData(this.datasource, true, true, false);
      const xAxisSetting = {
        max: xAxisMax,
        min: 0,
        labels: { format: undefined },
        tickInterval: undefined,
        categories: xCategory ? xCategory: [],
        title: { text: this.indicators.x.axisText? this.indicators.x.axisText:this.indicators.x.label }
      }, yAxisSetting = {
        max: yAxisMax,
        min: 0,
        labels: { format: undefined },
        tickInterval: undefined,
        categories: yCategory? yCategory: [],
        title: { text: this.indicators.y.axisText? this.indicators.y.axisText:this.indicators.y.label }
      }
      if(this.indicators.x.property === 'environmental'){
        xAxisSetting.max = 100;
        xAxisSetting.tickInterval = 10;
        xAxisSetting.labels.format = '{text}%';
      }
      if(this.indicators.y.property === 'environmental'){
        yAxisSetting.max = 100;
        yAxisSetting.tickInterval = 10;
        yAxisSetting.labels.format = '{text}%';
      }
      xAxis.update(xAxisSetting);
      yAxis.update(yAxisSetting);
      this.addChartRotation();
      this.reflowChart();
    }
  }

  private addChartRotation() {
    const chart = this.chart;
    const H = this.Highcharts;

    function dragStart(eStart) {
      eStart = chart.pointer.normalize(eStart);

      const posX = eStart.chartX,
        posY = eStart.chartY,
        alpha = chart.options.chart.options3d.alpha,
        beta = chart.options.chart.options3d.beta,
        sensitivity = 5, // lower is more sensitive
        handlers = [];

      function drag(e) {
        // Get e.chartX and e.chartY
        e = chart.pointer.normalize(e);

        chart.update(
          {
            chart: {
              options3d: {
                alpha: alpha + (e.chartY - posY) / sensitivity,
                beta: beta + (posX - e.chartX) / sensitivity
              }
            }
          },
          undefined,
          undefined,
          false
        );
      }

      function unbindAll() {
        handlers.forEach(function (unbind) {
          if (unbind) {
            unbind();
          }
        });
        handlers.length = 0;
      }

      handlers.push(H.addEvent(document, 'mousemove', drag));
      handlers.push(H.addEvent(document, 'touchmove', drag));

      handlers.push(H.addEvent(document, 'mouseup', unbindAll));
      handlers.push(H.addEvent(document, 'touchend', unbindAll));
    }

    H.addEvent(chart.container, 'mousedown', dragStart);
    H.addEvent(chart.container, 'touchstart', dragStart);
  }

  private setAxisValue(axis: string, value: string, text: string, axisText) {
    switch (true) {
      case axis === 'x':
        this.xAxis = value;
        this.indicators.x.property = value;
        this.indicators.x.label = text;
        this.indicators.x.axisText = axisText;
        break;
      case axis === 'y':
        this.yAxis = value;
        this.indicators.y.property = value;
        this.indicators.y.label = text;
        this.indicators.y.axisText = axisText;
        break;
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.length < 2;
  }
}
