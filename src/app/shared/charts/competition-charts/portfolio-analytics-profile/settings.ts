import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'scatter',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 10, 0],
  },
  title: {text: null},
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: chartsConfig.colorPalette,
  plotOptions: {
    scatter: {
      width: 10,
      height: 10,
      allowPointSelect: false,
    },
    series: {
      states: {
        hover: {
          enabled: false
        }
      }
    }
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      if (!this.series?.options?.indicators?.x || !this.series?.options?.indicators?.y) {
        return `<strong>${this.point.competitor}</strong><br>
            Patent families: <b>${this.point.publ_quantity}</b><br>`;
      }

      let xValue = '';
      let yValue = '';

      switch (this.series.options.indicators.x.property) {
        case 'impact_share':
        case 'risk_share':
        case 'recency_share':
        case 'environmental':
          xValue = `${this.point.x}%`;
          break;
        case 'tech_fields':
          xValue = `${this.point.x} tech areas`;
          break;
        case 'recency_years':
          xValue = `${this.point.x} years`;
          break;
        case 'impact_weighted':
        case 'risk_weighted':
        case 'consistency':
        case 'market_coverage':
        case 'citation_backward_count':
        case 'citation_forward_count':
          xValue = `${this.point.x}`;
          break;
      }

      switch (this.series.options.indicators.y.property) {
        case 'impact_share':
        case 'risk_share':
        case 'recency_share':
        case 'environmental':
          yValue = `${this.point.y}%`;
          break;
        case 'tech_fields':
          yValue = `${this.point.y} tech areas`;
          break;
        case 'recency_years':
          yValue = `${this.point.y} years`;
          break;
        case 'impact_weighted':
        case 'risk_weighted':
        case 'consistency':
        case 'market_coverage':
        case 'citation_backward_count':
        case 'citation_forward_count':
          yValue = `${this.point.y}`;
          break;
      }
      return `<strong>${this.point.competitor}</strong><br>
            Patent families: <b>${this.point.publ_quantity}</b><br>
            ${this.series.options.indicators.x.label}: <b>${xValue}</b><br>
            ${this.series.options.indicators.y.label}: <b>${yValue}</b>`
    },
  },
  yAxis: {
    title: {
      text: 'Average risk',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  },
  xAxis: {
    gridLineWidth: 1,
    title: {
      text: 'Average value',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  },
  legend: {
    enabled: false
  },
  series: [{
    name: 'Profile',
    colorByPoint: true,
    data: [],
    dataLabels: {
      verticalAlign: 'middle',
      align: 'center',
      style: chartsConfig.dataLabelStyles,
      enabled: true,
      // useHTML: true,
      formatter: function () {
        return this.point.competitor;
      }
    }
  }]
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 50;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  name: 'Number of applicants',
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};

export const tooltipText = `This chart allows for the evaluation of applicants with respect to a set of customizable parameters developed by Octimine.<br>
The chart consists of customizable axes, which can be manually modified by opening the dropdown menu and selecting the parameter of your choice.<br>
Within the chart, each applicant is represented by a circle whose size corresponds to the number of attributed patent families. The arrangement of these circles automatically adjusts according to the chosen axes.<br>
<b>Suggestion:</b><br>
Utilize this chart to perform a deeper analysis of the main applicants from various points of view. Locate and highlight insights relevant to your goals.`
