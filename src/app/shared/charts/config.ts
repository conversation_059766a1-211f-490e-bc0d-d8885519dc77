export const chartsConfig = {
  bgColor: '#F5F7FAFF',
  secondaryBgColor: '#FFF3EB',
  heightDefault: 568,
  heightOnSingleColumn: 768,
  heightWithSlider: 400,
  credits: {
    href: 'http://www.octimine.com',
    text: '<span style="font-size: 7px;">created with</span><br><span style="font-size: 10px;">Octimine</span>',
    style: {
      color: '#0F2C35',
      fontFamily: 'Open Sans Regular'
    },
    position: {
      y: -15
    }
  },
  colorPalette: ['#8CD4C8', '#6DA7A5', '#00729C', '#8296B9', '#7CAED5', '#375484', '#A37FBE', '#6A61A8', '#665191'],
  colorPaletteDark: ['#00729C', '#375484', '#6A61A8', '#665191', '#A37FBE'],
  colorPaletteLight: ['#8CD4C8', '#8296B9', '#7CAED5', '#6DA7A5'],
  colorGreenPalette: ['#26AF96', '#4BB9A5', '#71C4B5', '#8CD4C8', '#98CFC5', '#A3DCD3', '#BAE5DE', '#D1EEE9', '#E8F7F4'],
  labelColor: '#698A95',
  similarityColor: '#FF6700',
  trendlineColor: '#0D6EFD',
  colorGradient: {
    min: '#D3DBE7',
    max: '#0F2C57',
    hover: '#375484'
  },
  dataLabelStyles: {
    fontFamily: 'Open Sans Regular',
    color: '#0F2C35',
    lineHeight: '14px',
    fontSize: '12px',
  },
  dataLabelStylesHeatMap: {
    fontFamily: 'Open Sans Regular',
    lineHeight: '14px',
    fontSize: '12px',
    textOutline: 'none'
  },
  dataLabelOverflowStylesHeatMap: {
    textAlign: 'center',
    verticalAlign: 'middle',
    textOverflow: 'ellipsis',
    padding: '1px'
  },
  textStyles: {
    color: '#698A95',
    fontFamily: 'Open Sans Regular',
    fontSize: '12px',
    cursor: 'default'
  },
  tooltipStyles: {
    fontFamily: 'Open Sans Regular',
    fontSize: '14px',
    lineHeight: '18px',
  }
}
