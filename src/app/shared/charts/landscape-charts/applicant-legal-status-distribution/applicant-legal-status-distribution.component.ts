import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import { Subscription } from 'rxjs';
import _ from 'lodash';
import * as settings from './settings';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-applicant-legal-status-distribution',
  templateUrl: './applicant-legal-status-distribution.component.html',
  styleUrls: ['./applicant-legal-status-distribution.component.scss']
})
export class ApplicantLegalStatusDistributionComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = ApplicantLegalStatusDistributionComponent;
  chartName = 'competitors_applicant_legal_status_distribution';
  title = 'Applicant legal status distribution';
  chartSubscription: Subscription;
  chartOptions = settings.chartSetting;

  tooltipText = `The graph is aimed to provide an overview of the actual legal status of all the patent families in portfolio for top applicants.
  This can help the user analyze the active / inactive portfolio of an applicant.<br>
  The number of patent families are shown on x-axis and applicants are shown in y-axis. Each colour represents the legal status of the family.
  The user can further click on the graph to filter the portfolio for a particular applicant and all its patent families with a specific legal status.`;
  popupText: string;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }
        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point || !event.point.category || !event.point.series || !event.point.series.name) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex(x => x.title === this.title);
    if (index > -1) {
      filters.splice(index, 1);
    }

    const applicant = event.point.category;
    const legalStatus = event.point.series.name;
    let query = `(APPLICANTS=(${applicant}))`;
    if (legalStatus === 'Mixed') {
      query += ` AND (LEGAL_STATUS=invalid AND LEGAL_STATUS=valid)`;
    } else {
      query += ` AND (LEGAL_STATUS=${legalStatus.toLowerCase()})`;
    }

    filters.push({
      title: this.title,
      value: `Applicant: ${applicant}, Legal status: ${legalStatus}`,
      type: 'chart',
      chart: this.chartName,
      subType: 'highlight',
      query: `(${query})`
    });
    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const categories = _.cloneDeep(this.datasource.categories);
      const valid = _.cloneDeep(this.datasource.valid);
      const invalid = _.cloneDeep(this.datasource.invalid);
      const mix = _.cloneDeep(this.datasource.mix);
      const unknown = _.cloneDeep(this.datasource.unknown);

      const total = _.sum(valid) + _.sum(invalid) + _.sum(mix) + _.sum(unknown);
      this.chart.series.forEach(serie => serie.total = total);

      this.chart.xAxis[0].setCategories(categories);
      this.chart.series[0].setData(valid, true, true, false);
      this.chart.series[1].setData(invalid, true, true, false);
      this.chart.series[2].setData(mix, true, true, false);
      this.chart.series[3].setData(unknown, true, true, false);
      this.reflowChart();
    }
  }
}
