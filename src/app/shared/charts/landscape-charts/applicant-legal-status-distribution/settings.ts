import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'bar',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 70, 0, 0],
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  xAxis: {
    categories: [],
    labels: {
      style: chartsConfig.textStyles
    },
    title: {
      text: null
    }
  },
  yAxis: {
    min: 0,
    labels: {
      style: chartsConfig.textStyles
    },
    title: {
      text: null
    }
  },
  plotOptions: {
    bar: {
      dataLabels: {
        enabled: true,
        style: chartsConfig.dataLabelStyles,
        formatter: function () {
          if (this.y === 0) {
            return null;
          }
          return this.y;
        }
      }
    },

  },
  legend: {
    layout: 'vertical',
    align: 'right',
    verticalAlign: 'top',
    x: -5,
    y: 5,
    floating: true,
    borderWidth: 1,
    backgroundColor: chartsConfig.bgColor,
    shadow: true,
    itemStyle: chartsConfig.textStyles
  },
  series: [{
    name: 'Valid',
    data: [],
    borderWidth: 0,
    color: '#6DA7A5'
  }, {
    name: 'Invalid',
    data: [],
    borderWidth: 0,
    color: '#665191'
  }, {
    name: 'Mixed',
    data: [],
    borderWidth: 0,
    color: '#7CAED5'
  }, {
    name: 'Unknown',
    data: [],
    borderWidth: 0,
    color: '#8296B9'
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.point.y / this.series.total) * 100).toFixed(2);
      return `<span style="font-weight:bold">${this.point.category}</span><br />
            <span>Patent families:</span><span style="font-weight:bold">
            ${this.point.y}</span><br /><span>
            Percent:</span><span style="font-weight:bold">
            ${percent}%</span>`;
    }
  },
};
