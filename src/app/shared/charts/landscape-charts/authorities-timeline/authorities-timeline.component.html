<app-base-card-chart [chartClass]="chartClass" [chartName]="chartName" [chart]="chart" [component]="component"
                     (sliderChanged)="onUserChangeEnd()" [(sliderValue)]="topAuthorities" [sliderOptions]="sliderOptions"
                     [popupText]="popupText" [showFavoriteOption]="showFavoriteOption"
                     [showHeaderBar]="showHeaderBar" [spinner]="spinner" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [chartItemHeight]="chartItemHeight"
                     [urlTooltipArticle]="urlTooltipArticle.authorities">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    constructorType='chart'
                    id="tl"
                    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
