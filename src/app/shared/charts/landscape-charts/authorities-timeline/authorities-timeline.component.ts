import { Component, Injector, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { Options } from '@angular-slider/ngx-slider';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { HumanReadableNumberPipe } from '@core/pipes/human-readable-number/human-readable-number.pipe';

@Component({
  selector: 'app-authorities-timeline',
  templateUrl: './authorities-timeline.component.html',
  styleUrls: ['./authorities-timeline.component.scss']
})
export class AuthoritiesTimelineComponent extends BaseChartComponent implements OnInit, OnDestroy {
  @Input() showHeaderBar = true;
  @Input() spinner = 'assets/images/octimine_blue_spinner.gif';
  @Input() chartClass?: string;
  @Input() chartOptions: any = settings.chartSetting;
  @Input() topAuthorities = settings.MAX_VALUE_SLIDER;
  @Input() sliderOptions: Options = settings.sliderSetting;
  @Input() lastYears = 15;
  @Input() exportCssSelector = '.chart-content';

  component = AuthoritiesTimelineComponent;
  chartName = 'basic_authorities_timeline';
  title = 'Authorities over time';
  tooltipText = settings.tooltipText;
  popupText: string;

  constructor(
    protected injector: Injector,
    private humanReadableNumberPipe: HumanReadableNumberPipe
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const authorities = event.point.series.yAxis.categories[event.point.y];
    const year = event.point.series.xAxis.categories[event.point.x];
    if (year === 'Total' || authorities === 'Total') {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `((${(this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=')}${year}) AND (AUTHORITIES=${authorities}))`;
    filter.value = `Authority: ${authorities}, Year: ${year}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      let maxValue = 0;
      let total = 0;
      let values = _.cloneDeep(this.datasource.values.filter(value => value[2] > 0));
      let xAxis = _.cloneDeep(this.datasource.xAxis);
      let yAxis = _.cloneDeep(this.datasource.yAxis);

      if (this.topAuthorities < 10 || this.lastYears < 15) {
        [values, xAxis, yAxis] = this.updateTopAuthoritiesAndLastYears(values, xAxis, yAxis);
      }
      const self = this;

      values.forEach((value, i) => {
        if (value[0] === xAxis.length - 1 || value[1] === yAxis.length - 1) {
          values[i] = {
            x: value[0],
            y: value[1],
            value: value[2],
            color: '#cecece',
            dataLabels: {
              enabled: true,
              formatter: function () {
                return this.point.value > 999 ? self.humanReadableNumberPipe.transform(this.point.value, 1) : this.point.value;
              },
            }
          };
          if (value[0] === xAxis.length - 1 && value[1] === yAxis.length - 1) {
            total = value[2];
          }
        } else {
          maxValue = Math.max(maxValue, value[2]);
        }
      });

      this.chart.series[0]['options'].totalHits = total;

      this.chart.colorAxis[0].update({max: maxValue});
      this.chart.xAxis[0].setCategories(xAxis);
      this.chart.yAxis[0].setCategories(yAxis);
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  private updateTopAuthoritiesAndLastYears(values, xAxis, yAxis): any {

    const xYears = xAxis.slice(xAxis.length - this.lastYears - 1);
    const filteredValues = values.filter(value => value[0] >= xAxis.length - this.lastYears - 1 &&
      value[0] < xAxis.length - 1 && value[1] < yAxis.length - 1);

    const totalAuthorities = {};
    filteredValues.forEach(value => totalAuthorities[value[1]] = totalAuthorities[value[1]] ?
      totalAuthorities[value[1]] + value[2] : value[2]);
    const topAuthorities = Object.entries<number>(totalAuthorities).sort((a, b) => b[1] - a[1]).slice(0, this.topAuthorities);
    const yAuthorities = topAuthorities.map(auth => yAxis[auth[0]]);
    yAuthorities.push('Total');
    const topValues = filteredValues.filter(value => topAuthorities.find(auth => parseInt(auth[0], 10) === value[1]));

    const totalYearsByAuthorities = {};
    let total = 0;
    topValues.forEach(value => {
      const xIndex = value[0] - (xAxis.length - this.lastYears - 1);
      totalYearsByAuthorities[xIndex] = totalYearsByAuthorities[xIndex] ? totalYearsByAuthorities[xIndex] + value[2] : value[2];
      value[0] = xIndex;
      const yIndex = topAuthorities.findIndex(auth => parseInt(auth[0], 10) === value[1]);
      value[1] = yIndex;
      total += value[2];
    });
    topAuthorities.forEach((value, index) => value[0] = index.toString());

    let lastIndex = 0;
    Object.entries<number>(totalYearsByAuthorities).forEach((value, index) => {
      while (lastIndex !== parseInt(value[0], 10)) {
        topValues.push([lastIndex, yAuthorities.length - 1, 0]);
        lastIndex++;
      }
      topValues.push([parseInt(value[0], 10), yAuthorities.length - 1, value[1]]);
      lastIndex = parseInt(value[0], 10) + 1;
    });
    topAuthorities.forEach((value, index) => {
      topValues.push([xYears.length - 1, index, value[1]]);
    });
    topValues.push([xYears.length - 1, yAuthorities.length - 1, total]);
    return [topValues, xYears, yAuthorities];
  }
  onUserChangeEnd(){
    this.updateChartData();
  }
}
