import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { chartsConfig } from './../../config'

export const chartSettingHighlight = {
  chart: {
    spacing: [0, 0, 0, 0],
    backgroundColor: chartsConfig.bgColor,
    type: 'heatmap'
  },
  credits: {enabled: false},
  exporting: {enabled: false},
  title: {
    text: null
  },
  legend: {
    enabled: false
  },
  xAxis: {
    categories: [],
    opposite: true,
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    },
    title: {
      text: 'Year',
      style: chartsConfig.textStyles
    },
  },
  yAxis: {
    categories: [],
    title: {
      text: 'Authority',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles,
      formatter: function () {
        return `<div class="text-center"><i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i><br>${this.value}</div>`;
      },
      useHTML: true,
    },
    reversed: true,
  },
  series: [{
    allowPointSelect: false,
    borderColor: '#AE6800',
    borderWidth: 0.1,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false,
      color: '#000',
      style: chartsConfig.dataLabelStyles,
    },
  }],
  colorAxis: {
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  tooltip: {
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
          <span style="font-weight: bold;">${this.series.yAxis.categories[this.point.y]}</span><br>
          <span>Year: </span>
          <span style="font-weight: bold;">
          ${this.series.xAxis.categories[this.point.x]}</span><br>
          <span>Patents: </span>
          <span style="font-weight: bold;">${this.point.value}</span><br>
          <span>Percent: </span>
          <span style="font-weight: bold;">${percent}%</span><br>`;
    },
    useHTML: true,
  },
};

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'heatmap',
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360,
    itemStyle: chartsConfig.textStyles
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  xAxis: {
    categories: [],
    opposite: true,
    gridLineWidth: 1,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles
    },
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    categories: [],
    title: {
      text: 'Authority',
      style: chartsConfig.textStyles
    },
    reversed: true,
    gridLineWidth: 1,
    labels: {
      style: chartsConfig.textStyles,
      formatter: function () {
        return `<div class="text-center"> ${this.value} <i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i></div>`;
      },
      useHTML: true,
      enabled: true,
    },
  },
  series: [{
    allowPointSelect: false,
    borderWidth: 0,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false,
      color: '#000',
      style: {...chartsConfig.dataLabelStyles, textOutline: '0px'}
    },
  }],
  tooltip: {
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter() {
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
          <span style="font-weight: bold;"><i class="${COUNTRY_FLAG(this.series.yAxis.categories[this.point.y], FlagSizeEnum.MD)}"></i> ${this.series.yAxis.categories[this.point.y]}</span><br>
          <span>Year: </span><span style="font-weight: bold;">${this.series.xAxis.categories[this.point.x]}</span><br>
          <span>Patent families: </span><span style="font-weight: bold;">${this.point.value}</span><br>
          <span>Percent: </span><span style="font-weight: bold;">${percent}%</span>`;
    },
    useHTML: true
  },
  responsive: {
    rules: [{
        condition: { maxWidth: 750 },
        chartOptions: {
          yAxis: {
            labels: {
              style: chartsConfig.textStyles,
              formatter: function () {
                return `<div class="text-center"> ${this.value} <i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i></div>`;
              },
            }
          }
        }
    }]
  },
};
export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 10;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
  name: 'Number of authorities'
};

export const tooltipText = `This graph identifies the most active patent authorities in given priority years.<br>
The horizontal axis lists the priority years, and the vertical axis records the main patent authorities. The heatmap's color intensity represents the number of patent family publications, both in absolute value and as a percentage of all analyzed patents, with darker colors indicating higher numbers of patent families.<br>
<b>Suggestion:</b><br>
Quickly identify the most active patent authorities in specific priority years to get insights into development over time.`

