<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [urlTooltipArticle]="urlTooltipArticle.sustainabilities"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    *ngIf="hasPatents else noPatents"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    constructorType='chart'
                    style="display: block;">
  </highcharts-chart>

  <ng-template #noPatents>
    <div class="d-flex flex-column justify-content-center align-items-center h-100">
      <span>The analyzed results do not present any green patents, according to our classification</span>
    </div>
  </ng-template>
</app-base-card-chart>
