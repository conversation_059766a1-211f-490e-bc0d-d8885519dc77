import { Component, Injector, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import * as Highcharts from 'highcharts';
import * as settings from './settings';

@Component({
  selector: 'app-green-categories',
  templateUrl: './green-categories.component.html',
  styleUrls: ['./green-categories.component.scss']
})
export class GreenCategoriesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = GreenCategoriesComponent;
  chartName = 'green_categories';
  @Input() title = 'Green patents distribution';
  @Input() chartOptions: any = settings.chartSetting;
  @Input() showTooltipPatentFamilies = true;

  tooltipText = settings.tooltipText;
  popupText: string;
  total: 0;
  greenCodes: Array<string>;

  private readonly noGreenPatentColor = '#dbe1ec';

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get hasPatents() {
    return this.total > 0;
  }

  ngOnInit() {
    this.greenCodes = [];
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts) {
          return;
        }

        const chart = this.secondarySource ? this.secondarySource : charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.total = chart.total;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  selectPoint(event) {
    if (this.disableFilter) {
      return;
    }
    if (!event.point?.codes) {
      return;
    }

    const title = 'Green category';
    const filter = this.storeService.filters;
    const index = filter.findIndex(x => x.title === title);
    if (index > -1) {
      filter.splice(index, 1);
    }
    filter.push({
      title: title,
      value: event.point.name,
      type: 'chart',
      query: event.point.green ? `(CPC=(${event.point.codes.join(' OR ')}) OR IPC=(${event.point.codes.join(' OR ')}))` : `(NOT(CPC=(${this.greenCodes.join(' OR ')})) AND NOT(IPC=(${this.greenCodes.join(' OR ')})))`
    });
    this.storeService.setFilters(filter);
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.total;
      this.chart.series[0].options.showTooltipPatentFamilies = this.showTooltipPatentFamilies;

      this.datasource.forEach((st, index) => {
        st['y'] = st.value;
        st.name = st.name[0] + st.name.substring(1).toLowerCase();
        if (!st.green) {
          st.name = 'Non-green patents';
          st.color = this.noGreenPatentColor;
        } else {
          st.color = this.chartsService.greenCategoriesColor(st.name, index);
        }
      });
      this.greenCodes = this.datasource.filter(entry => entry.green).flatMap(entry => entry.codes);
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }
}
