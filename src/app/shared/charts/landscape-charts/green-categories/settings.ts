import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'pie',
    spacing: [10, 10, 10, 10],
    backgroundColor: chartsConfig.bgColor,
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  tooltip: {
    useHTML: true,
    padding: 0,
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.point.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<div style="padding: 8px"> <span style="font-weight:bold">${this.point.name}</span><br />` +
            (this.series.options.showTooltipPatentFamilies ?
              `<span>Patent families:</span><span style="font-weight:bold">${this.point.y}</span><br /><span>` : '') +
            `Percent:</span><span style="font-weight:bold">
              ${percent}%</span></div>`;
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: false,
      innerSize: '35%',
      depth: 35,
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: [{
    type: 'pie',
    data: [],
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      formatter: function () {
        return `<span style="text-outline: 0px">${this.point.name}</span>`;
      }
    }
  }]
};

export const secondaryChartSetting = {
  chart: {
    type: 'pie',
    spacing: [10, 10, 10, 10],
    backgroundColor: chartsConfig.secondaryBgColor,
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  tooltip: {
    useHTML: true,
    padding: 0,
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.point.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<div style="padding: 8px"> <span style="font-weight:bold">${this.point.name}</span><br /> `+
              (this.series.options.showTooltipPatentFamilies ?
                `<span>Patent families:</span><span style="font-weight:bold">${this.point.y}</span><br /><span>` : '') +
              `Percent:</span><span style="font-weight:bold">
                ${percent}%</span></div>`;
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: false,
      innerSize: '35%',
      depth: 35,
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: [{
    type: 'pie',
    data: [],
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      formatter: function () {
        return `<span style="text-outline: 0px">${this.point.name}</span>`;
      }
    }
  }]
};

export const tooltipText = `This graph represents the distribution of patents falling into various green categories, as defined by the
<a href="https://www.oecd.org/env/indicators-modelling-outlooks/green-patents.htm" target="_blank">OECD.</a>
Certain patents may have multiple green classifications, reflecting their relevance to different environmental technologies. By clicking on each category, you can filter the results and update all charts based on your selection.<br>
<b>Suggestion:</b><br>
Pay attention to those green categories where your portfolio has a substantial presence. You may wish to consider prioritizing patent protection and commercialization in these fields.`
