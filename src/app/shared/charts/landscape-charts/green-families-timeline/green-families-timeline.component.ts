import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { openTooltipPopup } from '@shared/charts';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

declare var $: any;

@Component({
  selector: 'app-green-families-timeline',
  templateUrl: './green-families-timeline.component.html',
  styleUrls: ['./green-families-timeline.component.scss']
})
export class GreenFamiliesTimelineComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = GreenFamiliesTimelineComponent;
  chartName = 'green_families_timeline';
  title = 'Green patents over time';
  greenClassificationCodesByYear = {};
  chartOptions = settings.chartSetting;
  currentPoint = null;

  tooltipText = settings.tooltipText;
  popupText: string;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get hasPatents() {
    return this.datasource?.xAxis.length > 0 && this.datasource?.yAxis.length > 0;
  }

  ngOnInit() {
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }
        this.datasource = chart.datasource;
        this.greenClassificationCodesByYear = chart['green_codes_by_year'];
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    const self = this;
    $(document).off('click', '.classification-tooltip');
    $(document).on('click', '.classification-tooltip', function (e) {
      openTooltipPopup('', self.tooltip(self.currentPoint));
    });

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.setupChartTooltip();
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point || !event.point.category) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const filterQuery = [`(${(this.storeService?.isPublications ? 'PUBLICATION_DATE=' : 'PRIORITY_DATE=')}${event.point.category})`]
    if (this.greenClassificationCodesByYear[event.point.category]) {
      const greenClassificationCodes = this.greenClassificationCodesByYear[event.point.category].join(' OR ');
      let greenCodesQuery = `(CPC=(${greenClassificationCodes}) OR IPC=(${greenClassificationCodes}))`;
      filterQuery.push(event.point.series.name === 'Green' ? greenCodesQuery : `(NOT${greenCodesQuery})`);
    }
    filter.query = filterQuery.join(' AND ');
    filter.value = `Classification: ${event.point.series.name}, Year: ${event.point.category}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private setupChartTooltip() {
    if (this.chart?.options?.tooltip) {
      const self = this;
      this.chart.options.tooltip['formatter'] = (point) => {
        self.currentPoint = point.chart.hoverPoints[0];
        return self.tooltip(point.chart.hoverPoints[0]);
      };
    }
  }

  private updateChartData() {
    if (this.chart && this.chart.xAxis && this.datasource) {
      this.chart.xAxis[0].setCategories(this.datasource.xAxis, true, true);
      const yAxis = Array(2);
      this.datasource.yAxis.forEach((item) => {
        if (item.name === 'Green') {
          item['color'] = '#26AF96';
          yAxis[1] = item;
        } else {
          item.name = 'Non-green';
          item['color'] = '#DBE1EC';
          yAxis[0] = item;
        }
      });
      this.chart.update({series: yAxis}, true, true);
      this.reflowChart();
    }
  }

  private tooltip(point) {
    return `<div class="classification-tooltip">
              <p class="oc-text mb-1"><span class="fw-bold">${point.series.name}</span></p>
              <p class="oc-text mb-1">Year: <span class="fw-bold">${point.category}</span></p>
              <p class="oc-text mb-1">Patent families: <span class="fw-bold">${point.y}</span></p>
              <p class="oc-text mb-1">Percent: <span class="fw-bold">${point.percentage.toFixed(0)}%</span></p>
            </div>`;
  }
}
