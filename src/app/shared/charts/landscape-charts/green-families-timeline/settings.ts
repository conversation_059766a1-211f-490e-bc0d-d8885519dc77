import { chartsConfig } from './../../config'
import { HumanReadableNumberPipe } from '@core/pipes/human-readable-number/human-readable-number.pipe';

const pipe = new HumanReadableNumberPipe;

export const chartSetting = {
  chart: {
    spacing: [10, 0, 10, 0],
    backgroundColor: chartsConfig.bgColor,
    type: 'column',
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: chartsConfig.colorPalette,
  title: {
    text: null
  },
  legend: {
    enabled: true,
    useHTML: true,
    itemStyle: chartsConfig.textStyles,
  },
  xAxis: {
    title: {
      text: 'Priority year'
    },
  },
  yAxis: {
    title: {
      text: 'Patent families'
    }
  },
  plotOptions: {
    column: {
      stacking: 'normal',
    },
    series: {
      dataLabels: {
        enabled: true,
        style: {...chartsConfig.dataLabelStyles, textOutline: 0},
        formatter: function () {
          return (this.y != 0) ? this.y? pipe.transform(this.y, 1): this.y : '';
        }
      },
      groupPadding: 0,
      pointPadding: 0,
    }
  },
  tooltip: {
    style: {...chartsConfig.tooltipStyles, pointerEvents: 'auto'},
    useHTML: true
  },
};

export const tooltipText = `This chart displays changes over time in the ratio of green to non-green patents, as defined by the <a href="https://www.oecd.org/env/indicators-modelling-outlooks/green-patents.htm" target="_blank">OECD</a>.<br>
The graph shows the priory year on the horizontal axis and the absolute total of patents families on the vertical axis. The grey portion of each bar represents non-green documents, while the green portion indicates sustainability-related documents.<br>
<b>Suggestion:</b><br>
Pay attention to significant changes in the green vs. non-green patent ratio, as this may indicate critical milestones or shifts in your innovation activities.`
