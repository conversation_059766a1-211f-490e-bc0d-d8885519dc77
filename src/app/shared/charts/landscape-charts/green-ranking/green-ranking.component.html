<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [urlTooltipArticle]="urlTooltipArticle.sustainabilities"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>

  <ng-template #noPatents>
    <div class="d-flex flex-column justify-content-center align-items-center h-100">
      <span>No patents in the current data set belonging to a green ranking</span>
    </div>
  </ng-template>
</app-base-card-chart>
