import { Component, Injector, OnDestroy, OnInit } from '@angular/core';
import More from 'highcharts/highcharts-more';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);

@Component({
  selector: 'app-green-ranking',
  templateUrl: './green-ranking.component.html',
  styleUrls: ['./green-ranking.component.scss']
})
export class GreenRankingComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = GreenRankingComponent;
  chartName = 'green_ranking';
  title = 'Green ranking';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;
  popupText: string;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  selectPoint(event) {
    if (this.disableFilter) {
      return;
    }
    if (!event.point?.category || !event.point?.series?.options?.stack ||
      event.point?.series?.options?.stack.toLowerCase() === 'average') {
      return;
    }

    const filter = this.storeService.filters;
    const index = filter.findIndex(x => x.title === this.title);
    if (index > -1) {
      filter.splice(index, 1);
    }
    const isOverall = event.point.category.toLowerCase() === 'overall';
    let greenCodes = isOverall ?
      Object.values(this.datasource.green_codes_by_tech_fields).flatMap((codes) => codes) :
      (this.datasource.green_codes_by_tech_fields[event.point.category] || []);

    if (greenCodes.length > 0) {
      greenCodes = greenCodes.join(' OR ');
      const isGreen = event.point.series.name.toLowerCase() === 'green';
      const techFieldsQuery = isOverall ? '' : `AND TECH_FIELDS=${event.point.category}`;
      const query = isGreen ? `((CPC=(${greenCodes}) OR IPC=(${greenCodes})) ${techFieldsQuery})` :
        `(NOT(CPC=(${greenCodes})) AND NOT(IPC=(${greenCodes})) ${techFieldsQuery})`;
      filter.push({
        title: this.title,
        value: `${event.point.series.name} patents of ${event.point.category}`,
        type: 'chart',
        query: query
      });
    }

    this.storeService.setFilters(filter);
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      const categories = this.datasource.categories.map(ct =>
        ct[0].toUpperCase() + ct.slice(1).toLowerCase()
      );
      const series = this.datasource.series.map(s => {
        return {...s};
      }).filter(d => {
        if (d.name === 'Green') {
          d.title = d.name;
          d.name = d.stack[0].toUpperCase() + d.stack.slice(1).toLowerCase();
          return true;
        }
        return false;
      }).reverse();
      const data = {series: series, xAxis: {categories: categories}};
      this.chart.update(data, true, true, false);
      this.reflowChart();
    }
  }

}
