import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'column',
    spacing: [10, 0, 10, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  colors: ['#8CD4C8', '#26AF96', '#DBE1EC', '#B5BECF'],
  title: {text: null},
  yAxis: {
    title: {
      text: 'Percentage of green patents'
    },
    min: 0
  },
  legend: {
    itemStyle: chartsConfig.textStyles,
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      pointPadding: 0,
    },
    series: {
      groupPadding: 0.1,
      dataLabels: {
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  tooltip: {
    style: {...chartsConfig.tooltipStyles, pointerEvents: 'auto'},
    formatter: function () {
      let thisPoint = this.point,
        stackName = this.series.userOptions.stack === 'Average' ? `Average ${this.point.category} portfolio` :
          'Your portfolio',
        text = '<b>' + this.x + '</b><br/><i>' + stackName + '</i><br/>';

      this.series.chart.series.forEach(function (ser) {
        if (ser.options.stack === thisPoint.series.options.stack) {
          text += thisPoint.series.options.title + ': <b>' + ser.points[thisPoint.index].y + '</b>%<br/>';
        }
      });

      return text;
    },
    useHTML: true
  },
};

export const tooltipText = `This chart ranks the proportion of green patents in the analyzed results, according to
<a href="https://www.oecd.org/env/indicators-modelling-outlooks/green-patents.htm" target="_blank">OECD</a> definitions, within
<a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">35 technology fields.</a>
The columns are in pairs, comparing the percentage of your portfolio that consists of green documents to an average portfolio in the same technology field.<br>
<b>Suggestion:</b><br>
Use this chart to benchmark the performance of your patent portfolio and identify areas where it excels or lags behind.`
