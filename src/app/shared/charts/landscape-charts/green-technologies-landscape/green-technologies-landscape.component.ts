import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import More from 'highcharts/highcharts-more';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);

@Component({
  selector: 'app-green-technologies-landscape',
  templateUrl: './green-technologies-landscape.component.html',
  styleUrls: ['./green-technologies-landscape.component.scss']
})
export class GreenTechnologiesLandscapeComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = GreenTechnologiesLandscapeComponent;
  chartName = 'green_technologies_landscape';
  title = 'Green patents categories';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;
  total: 0;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  get hasPatents() {
    return this.datasource?.length;
  }

  ngOnInit() {
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        // this.total = chart.total;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  selectPoint(event) {
    if (!event.point?.codes) {
      return;
    }

    const filter = this.storeService.filters;
    const index = filter.findIndex(x => x.title === this.title);
    if (index > -1) {
      filter.splice(index, 1);
    }
    filter.push({
      title: this.title,
      value: event.point.name,
      type: 'chart',
      query: `(CPC=(${event.point.codes.join(' OR ')}) OR IPC=(${event.point.codes.join(' OR ')}))`
    });
    this.storeService.setFilters(filter);
  }

  private updateChartData() {
    if (this.chart && this.datasource?.length) {
      this.datasource.forEach((ds, index) => {
        ds.name = ds.name[0].toUpperCase() + ds.name.slice(1).toLowerCase();
        const mainColor = this.chartsService.greenCategoriesColor(ds.name, index);
        ds.color = mainColor + 'FF';
        const subTotal = ds.data.map(d => d.value).reduce((acc, cur) => acc + cur, 0);
        ds.data.forEach(dt => {
          dt.name = dt.name[0].toUpperCase() + dt.name.slice(1).toLowerCase();
          const brightness = Math.min(Math.max(0.15, 1 - dt.value / subTotal), 0.9);
          dt.color = Highcharts.color(mainColor).brighten(brightness).get();
        });
      });
      this.chart.update({series: this.datasource}, true, true, false);
      this.reflowChart();
    }
  }
}
