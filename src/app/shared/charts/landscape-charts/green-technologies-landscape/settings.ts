import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'packedbubble',
    spacing: [0, 0, 30, 0],
    backgroundColor: chartsConfig.bgColor
  },
  title: {
    text: ''
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  tooltip: {
    useHTML: true,
    padding: 0,
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      let html = `<div style="padding: 8px"><span>Category:</span><span style="font-weight:bold">
                        ${this.point.series.name}</span><br />`;
      if (this.point.value) {
        html += `<span>Subcategory:</span>
                          <span style="font-weight:bold">
                            ${this.point.name}</span><br/>
                          <span>Patent families:</span><span style="font-weight:bold">
                            ${this.point.value}</span><br/>`;
      }
      html += `</div>`;
      return html;
    }
  },
  plotOptions: {
    packedbubble: {
      minSize: '20%',
      maxSize: '100%',
      zMin: 0,
      zMax: 1000,
      layoutAlgorithm: {
        enableSimulation: false,
        gravitationalConstant: 0.05,
        splitSeries: true,
        seriesInteraction: false,
        dragBetweenSeries: false,
        parentNodeLimit: true
      },
      dataLabels: {
        enabled: true,
        format: '{point.value}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: []
};

export const tooltipText = `This chart provides an overview of how the analyzed results considered green are distributed across the <a href="https://www.oecd.org/env/indicators-modelling-outlooks/green-patents.htm" target="_blank">OECD's</a> categories and subcategories.<br>
The chart represents categories with larger circles; within each, the corresponding subcategories are represented as smaller circles. The size of each circle corresponds to the number of patent families attributed to that specific classification.<br>
<b>Suggestion:</b><br>
Focus on the distribution of patent families within each category to understand those areas of sustainable innovation where your portfolio is strongest. Analyze subcategories to identify niche fields of expertise within broader sustainable technology disciplines.`
