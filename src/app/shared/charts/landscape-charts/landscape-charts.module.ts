import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';


import { TechnologyStackTimelineComponent } from './technology-stack-timeline/technology-stack.timeline.component';
import { AuthoritiesTimelineComponent } from './authorities-timeline/authorities-timeline.component';
import { TopApplicantsAuthorityComponent } from './top-applicants-authority/top-applicants-authority.component';
import { LegalStatusComponent } from './legal-status/legal-status.component';
import {
  ApplicantLegalStatusDistributionComponent
} from './applicant-legal-status-distribution/applicant-legal-status-distribution.component';
import { GreenCategoriesComponent } from './green-categories/green-categories.component';
import { GreenFamiliesTimelineComponent } from './green-families-timeline/green-families-timeline.component';
import { RichNeighborhoodComponent } from './rich-neighborhood/rich-neighborhood.component';
import {
  GreenTechnologiesLandscapeComponent
} from './green-technologies-landscape/green-technologies-landscape.component';
import { GreenRankingComponent } from '@shared/charts/landscape-charts/green-ranking/green-ranking.component';

@NgModule({
  declarations: [
    TechnologyStackTimelineComponent,
    AuthoritiesTimelineComponent,
    TopApplicantsAuthorityComponent,
    LegalStatusComponent,
    ApplicantLegalStatusDistributionComponent,
    GreenCategoriesComponent,
    GreenFamiliesTimelineComponent,
    RichNeighborhoodComponent,
    GreenTechnologiesLandscapeComponent,
    GreenRankingComponent
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    TechnologyStackTimelineComponent,
    AuthoritiesTimelineComponent,
    TopApplicantsAuthorityComponent,
    LegalStatusComponent,
    ApplicantLegalStatusDistributionComponent,
    GreenCategoriesComponent,
    GreenFamiliesTimelineComponent,
    RichNeighborhoodComponent,
    GreenTechnologiesLandscapeComponent,
    GreenRankingComponent
  ]
})
export class LandscapeChartsModule {
}
