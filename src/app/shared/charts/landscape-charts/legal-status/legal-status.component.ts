import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { LegalStatusService } from '@core';

@Component({
  selector: 'app-legal-status',
  templateUrl: './legal-status.component.html',
  styleUrls: ['./legal-status.component.scss']
})
export class LegalStatusComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = LegalStatusComponent;
  chartName = 'analytics_legal_status';
  title = 'Patent family legal status';
  total: 0;
  chartOptions = settings.chartSetting;
  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector,
    private legalStatusService: LegalStatusService
  ) {
    super(injector);
  }

  ngOnInit() {
    this.popupText = this.tooltipText;

    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.total = chart.total;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point || !event.point.name) {
      return;
    }

    const filter = this.storeService.filters;
    const index = filter.findIndex(x => x.title === this.title);
    if (index > -1) {
      filter.splice(index, 1);
    }
    filter.push({
      title: this.title,
      value: event.point.name,
      type: 'chart',
      subType: 'highlight',
      query: this.legalStatusService.getLegalStatusQuery(event.point.name)
    });
    this.storeService.setFilters(filter);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].options.totalHits = this.total;

      this.datasource.forEach((st) => {
        switch (st['value']) {
          case 'valid':
            st['name'] = 'Alive';
            st['color'] = '#8CD4C8';
            break;
          case 'invalid':
            st['name'] = 'Dead';
            st['color'] = '#0F2C57';
            break;
          case 'pending':
            st['name'] = 'Pending';
            st['color'] = '#0F2C57';
            break;
          case 'unknown':
            st['name'] = 'Unknown';
            st['color'] = '#bcc7d6';
            break;
          default:
            st['name'] = '';
            st['color'] = '#7CAED5';
            break;
        }
      });
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }
}
