import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    plotBackgroundColor: null,
    plotBorderWidth: 0,
    plotShadow: false,
    spacing: [0, 0, 0, 0],
  },
  title: { text: '' },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      const percent = ((this.point.y / this.series.options.totalHits) * 100).toFixed(2);
      return `<span style="font-weight:bold">${this.point.name}</span><br />
            <span>Patent families:</span><span style="font-weight:bold">
            ${this.point.y}</span><br /><span>
            Percent:</span><span style="font-weight:bold">
            ${percent}%</span>`;
    }
  },
  plotOptions: {
    pie: {
      allowPointSelect: true,
      cursor: 'pointer',
      dataLabels: {
        enabled: true,
        distance: -50,
      },
      startAngle: -90,
      endAngle: 90,
      center: ['50%', '75%']
    }
  },
  series: [{
    type: 'pie',
    innerSize: '50%',
    data: [],
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
      formatter: function () {
        return `<span style="text-outline: 0px">${this.point.name}</span>`;
      }
    }
  }]
};

export const tooltipText = `This graph shows the legal status of the underlying patent families.
A patent family is classified as "valid" if at least one of its members is currently active.
Conversely, a family is considered "invalid" if at least one of its members holds this status.
Additionally, if the status of any family member is unknown, the family is categorized as "unknown".
Please note that a family may be counted twice – as valid and invalid – if it contains members of mixed classifications.<br>
<b>Suggestion:</b><br>
Analyze patent families classified under both valid and invalid as they may have members of contrasting legal statuses. Understanding the reasons for this disparity can provide insights into potential challenges or opportunities within the portfolio.`
