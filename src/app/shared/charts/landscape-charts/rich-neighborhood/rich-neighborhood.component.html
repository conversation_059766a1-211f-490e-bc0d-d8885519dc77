<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component"
                     [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [title]="title" [tooltipText]="tooltipText"
                     [storeService]="storeService"
                     [exportable]="displayChart && !isCalculating && !errorMessage" [exportCssSelector]="exportCssSelector"
                     [showZoomOption]="showZoomOption" [chartItemHeight]="chartItemHeight">
  <div *ngIf="totalDocuments <= MAX_DOCUMENTS_TO_DISPLAY && displayChart && !errorMessage" [hidden]="isCalculating"
    class="rnb-chart-container">
    <highcharts-chart (chartInstance)="onChartInstance($event)" (click)="selectPoint($event)"
                          [Highcharts]='Highcharts'
                          [options]='chartOptions'
                          class="d-block w-100 h-100"
                          constructorType='chart'
                          id="rnb-tl">
        </highcharts-chart>
  </div>

  <div *ngIf="displayChart && isCalculating" class="d-flex flex-column justify-content-center align-items-center h-100">
    <img src="assets/images/octimine_blue_spinner.gif">
    <div class="text-center mt-2">
      Please wait while we compute rich neighborhood analysis. Note that this may take a while.
    </div>
  </div>

  <div *ngIf="displayChart && !isCalculating && errorMessage"
       class="d-flex flex-column justify-content-center align-items-center h-100">
    <div class="text-orange text-center mt-2">
      {{errorMessage}}
    </div>
  </div>

  <div *ngIf="totalDocuments > MAX_DOCUMENTS_TO_DISPLAY"
       class="d-flex justify-content-center align-items-center h-100 text-orange">
    This chart can not be processed because this profile has more than {{MAX_DOCUMENTS_TO_DISPLAY}} documents.
  </div>

  <div *ngIf="!displayChart && totalDocuments <= MAX_DOCUMENTS_TO_DISPLAY"
       class="d-flex flex-column justify-content-center align-items-center h-100">
    <div class="d-flex flex-column justify-content-center align-items-center h-75">
      <div>Do you want to compute rich neighborhood analysis for this portfolio?</div>
      <button (click)="onViewChartButtonClicked()" class="btn btn-primary-outline mt-2">Ok</button>
    </div>
    <div class="h-25 d-flex flex-column justify-content-end align-items-end text-center">
      Please note that the computation will run in the background.
    </div>
  </div>
</app-base-card-chart>
