import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RichNeighborhoodComponent } from './rich-neighborhood.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';

describe('RichNeighborhoodComponent', () => {
  let component: RichNeighborhoodComponent;
  let fixture: ComponentFixture<RichNeighborhoodComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [RichNeighborhoodComponent],
      imports: [
        HighchartsChartModule,
        SharedModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RichNeighborhoodComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
