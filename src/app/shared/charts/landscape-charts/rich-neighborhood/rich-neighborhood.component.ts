import { ChangeDetectorRef, Component, Injector, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as Highcharts from 'highcharts';
import {
  LandscapeRniResultData,
  LandscapeRniResultStatusEnum,
  LandscapeService,
  LandscapeStoreService,
  PatentTableService,
  RichNeighborhoodService
} from '@core';
import { chartSetting } from './settings';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-rich-neighborhood',
  templateUrl: './rich-neighborhood.component.html',
  styleUrls: ['./rich-neighborhood.component.scss']
})
export class RichNeighborhoodComponent extends BaseChartComponent implements OnInit, OnDestroy {
  @Input() exportCssSelector = '.chart-content';

  totalDocuments = 0;
  displayChart = false;
  component = RichNeighborhoodComponent;

  chartName = 'rich_neighborhood';
  title = 'Patents neighborhood value';
  tooltipText = `Rich neighborhood indicator is one of most advance and sophisticated indicators from Octimine suite. Rich Neighborhood technology takes the whole tech environment
  into account and delivers a more reliable patent valuation. The neighborhood of a patent is defined by identifying most similar patents using the Octimine semantic engine.
  Once the neighborhood for a patent has been established, the value of focal patent and all patents in the neighborhood is considered. Finally an aggregation is done to show the
  number of patents that have high/low impact for the focal patent along with its neighborhood.<br>
  This is a very useful technology and can be used for validating high/low impact patents in the portfolio. It can further be used for pruning portfolios or identifying active areas of high impact and technology development.
  The results are presented in the form of a grid. The user can view patent value on y-axis and neighborhood value on x-axis. The patent value represents the impact of the focal
  patent. The neighborhood value shows the impact of all the patents semantically similar to the focal patent. The numbers in the grid shows the number of patent families in current
  landscape with respective focal patent and neighborhood value. Depending on the landscape, the patents usually aggregate in the following categories. Please see below to interpret
  the results better.<br><br>
  <ul>
    <li>High focal patent and neighborhood impact shows: Both indicators agree - you have a patent right in a neighborhood with lots of nuggets in the ground</li>
    <li>High focal patent impact but low neighborhood impact shows: Your patent is valuable, but that there is little valuable in the neighborhood. It could mean one of the following
    i) you have found a way of finding value where few other players have done so. ii) the value indicator for your patent is plainly wrong</li>
    <li>Low focal patent impact but high neighborhood impact shows: You need to check. Chances are that the assessment for your patent may be wrong. The more reliable neighborhood
    estimate tells you that there is value creation around you</li>
    <li>Low focal patent and neighborhood impact shows: The indicators suggest that there is neither value in the neighborhood nor in your own patent. Tough luck. But this is the \
    fate of many patents</li>
    <li>A darker color represents a higher number of patent families.<br>
    The user can further click within the grid to filter the landscape and view the specifically selected patent families.</li>
  </ul><br>
  Please note that an explicit request by user is required for its computation as the calculation is very resource intensive.`;
  chartOptions: any = chartSetting;

  MAX_DOCUMENTS_TO_DISPLAY = 35000;

  errorMessage: string = null;

  private getRichNeighborhoodResultsInterval = null;
  private CHECK_TASK_STATUS_INTERVAL = 10000;
  private IMPACT_LOW_LEVEL = 0;
  private IMPACT_MEDIUM_LEVEL = 1;
  private IMPACT_HIGH_LEVEL = 2;
  private MATRIX_DIM = 3;
  private rniResultData: LandscapeRniResultData;

  constructor(
    protected injector: Injector,
    private richNeighborhoodService: RichNeighborhoodService,
    private landscapeStore: LandscapeStoreService,
    private landscapeService: LandscapeService,
    private patentTableService: PatentTableService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super(injector);
  }

  private get profile() {
    return this.landscapeStore.landscapeProfile;
  }

  ngOnInit() {
    this.popupText = this.tooltipText;
    this.totalDocuments = Number(this.profile?.matched_documents_count);
    this.checkRichNeighborhoodResults(this.profile?.id);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      if (!this.isCalculating) {
        this.updateChartData(this.rniResultData);
      }
      ;
    });
  }

  onViewChartButtonClicked() {
    this.displayChart = true;
    this.calculateRichNeighborhood(this.profile?.id);
  }

  selectPoint(event) {
    if (event.point?.options) {
      const {x, y, value} = event.point?.options;
      this.filterByImpactLevel(y, x, value);
    }
  }

  private calculateRichNeighborhood(profileId: number) {
    if (profileId) {
      this.isCalculating = true;
      const calculateRichNeighborhood$ = this.richNeighborhoodService.calculateRichNeighborhood(profileId)
        .subscribe({
          next: (res) => {
            this.checkRichNeighborhoodResults(profileId);
          },
          error: error => {
            console.error(error);
            this.errorMessage = 'Error when computing rich neighborhood analysis for this portfolio';
            this.isCalculating = false;
          }
        });
      this.subscriptions.add(calculateRichNeighborhood$);
    }
  }

  private checkRichNeighborhoodResults(profileId: number) {
    if (profileId) {
      this.ngZone.runOutsideAngular(() => {
        const self = this;

        this.getRichNeighborhoodResultsInterval = setInterval(function () {
          self.retrieveRichNeighborhoodResults(profileId);
        }, self.CHECK_TASK_STATUS_INTERVAL);

        this.retrieveRichNeighborhoodResults(profileId);
      });
    }
  }

  private retrieveRichNeighborhoodResults(profileId: number) {
    this.errorMessage = null;
    const getRichNeighborhoodResults$ = this.richNeighborhoodService.getRichNeighborhoodResults(profileId)
      .subscribe({
        next: ({data, status}) => {
          this.displayChart = true;
          switch (true) {
            case data.status === LandscapeRniResultStatusEnum.SUCCESS:
              this.afterGetRichNeighborhoodResultsCompleted(data.data);
              this.isCalculating = false;
              break;
            case data.status === LandscapeRniResultStatusEnum.FAILURE:
              this.afterGetRichNeighborhoodResultsCompleted(null);
              this.errorMessage = 'Error when computing rich neighborhood analysis for this portfolio';
              this.isCalculating = false;
              break;
            default:
              this.isCalculating = true;
          }
        },
        error: ({error})=> {
          console.error(error);
          this.isCalculating = false;
          this.afterGetRichNeighborhoodResultsCompleted(null);
          if (error.status !== 404) {
            this.errorMessage = 'Error when computing rich neighborhood analysis for this portfolio';
          }
        }
      });
    this.subscriptions.add(getRichNeighborhoodResults$);
  }

  private afterGetRichNeighborhoodResultsCompleted(data?: LandscapeRniResultData) {
    this.clearInterval();
    this.updateChartData(data);
    this.rniResultData = data;
    this.changeDetectorRef.detectChanges();
  }

  private clearInterval() {
    if (this.getRichNeighborhoodResultsInterval) {
      clearInterval(this.getRichNeighborhoodResultsInterval);
      this.getRichNeighborhoodResultsInterval = null;
    }
  }

  private getDefaultChartData(): { x: number, y: number, value: number }[] {
    let values = [];
    for (let y = 0; y < this.MATRIX_DIM; y++) {
      for (let x = 0; x < this.MATRIX_DIM; x++) {
        values[y * this.MATRIX_DIM + x] = {x, y, value: 0};
      }
    }
    return values;
  }

  private encodeNeighborImpactLevel(impactLevel: number, threshold: number[]): number {
    switch (true) {
      case threshold[0] <= impactLevel && impactLevel < threshold[1]:
        return this.IMPACT_LOW_LEVEL;
      case threshold[1] <= impactLevel && impactLevel < threshold[2]:
        return this.IMPACT_MEDIUM_LEVEL;
      default:
        return this.IMPACT_HIGH_LEVEL;
    }
  }

  /**
   * https://gitlab.com/octimine/analytics/-/blob/main/rich/utils_rich.py#L24
   *
   * @param impactLevel
   * @private
   */
  private encodeImpactLevel(impactLevel: number): number {
    switch (true) {
      case impactLevel === 2 || impactLevel === 3:
        return this.IMPACT_HIGH_LEVEL;
      case impactLevel === 1:
        return this.IMPACT_MEDIUM_LEVEL;
      default:
        return this.IMPACT_LOW_LEVEL;
    }
  }

  private decodeImpactLevel(impactLevel: number): string {
    switch (true) {
      case impactLevel === this.IMPACT_LOW_LEVEL:
        return 'Low';
      case impactLevel === this.IMPACT_MEDIUM_LEVEL:
        return 'Medium';
      case impactLevel === this.IMPACT_HIGH_LEVEL:
        return 'High';
      default:
        return null;
    }
  }

  private updateChartData(data: LandscapeRniResultData) {
    if (this.chart && this.chart.series) {
      const values = this.getDefaultChartData();
      if (data && data.source) {
        for (const src of data.source) {
          const impactLevelAsY = this.encodeImpactLevel(src[1]);
          const neighborImpactLevelAsX = this.encodeNeighborImpactLevel(src[2], data.threshold.simple_average);
          const loc = this.MATRIX_DIM * impactLevelAsY + neighborImpactLevelAsX;
          values[loc] = {
            x: neighborImpactLevelAsX,
            y: impactLevelAsY,
            value: values[loc].value + 1
          };
        }
      }
      this.chart.series[0].setData(values, true, true, false);
      this.reflowChart();
    }
  }

  private filterPatentNumbers(data: LandscapeRniResultData, impactLevel: number, neighborImpactLevel: number): string[] {
    const patentNumbers = [];
    if (data?.source) {
      for (const src of data.source) {
        const srcImpactLevel = this.encodeImpactLevel(src[1]);
        const srcNeighborImpactLevel = this.encodeNeighborImpactLevel(src[2], data.threshold.simple_average);
        if (srcImpactLevel === impactLevel && srcNeighborImpactLevel === neighborImpactLevel) {
          patentNumbers.push(src[0]);
        }
      }
    }
    return patentNumbers;
  }

  private filterByImpactLevel(impactLevel: number, neighborImpactLevel: number, value: number) {
    if (value > 0) {
      const filters = [...this.storeService.filters].filter((f) => f.chart !== this.chartName);
      const query = this.filterPatentNumbers(this.rniResultData, impactLevel, neighborImpactLevel)
        .map((p) => `ALSO_PUBLISHED_AS=${p}`)
        .join(' OR ');
      const title = `(Patent Value=${this.decodeImpactLevel(impactLevel)}) AND
      (Neighborhood Value=${this.decodeImpactLevel(neighborImpactLevel)})`;
      filters.push({
        chart: this.chartName,
        title: this.title,
        type: 'chart',
        query: query,
        value: title
      });
      this.storeService.setFilters(filters);
    }
  }
}
