import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'heatmap',
    spacing: [0, 0, 0, 0],
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {
    text: null
  },
  legend: {
    enabled: false
  },
  xAxis: {
    categories: ['Low', 'Medium', 'High'],
    title: {
      text: 'Neighborhood value',
      style: chartsConfig.textStyles
    },
  },
  yAxis: {
    categories: ['Low', 'Medium', 'High'],
    title: {
      text: 'Patent value',
      style: chartsConfig.textStyles
    }
  },
  series: [{
    allowPointSelect: false,
    borderColor: '#AE6800',
    borderWidth: 0.1,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: true,
      style: chartsConfig.dataLabelStyles,
    },
  }],
  tooltip: { enabled: false },
  colorAxis: {
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
  },
};
