import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    spacing: [10, 0, 0, 0],
    backgroundColor: chartsConfig.bgColor,
    type: 'bar'
  },
  credits: {
    enabled: false,
  },
  exporting: {enabled: false},
  colors: chartsConfig.colorPalette,
  title: {
    text: null
  },
  legend: {
    enabled: true
  },
  plotOptions: {
    series: {
      stacking: 'normal'
    }
  },
  xAxis: {
    title: {
      text: 'Year',
      style: chartsConfig.textStyles
    },
  },
  yAxis: {
    title: {
      text: 'Number of patent families',
      style: chartsConfig.textStyles
    },
  },
  tooltip: {
    formatter() {
      const percent = ((this.point.y / this.series.options.totalHits) * 100).toFixed(2);

      return `
          <span style="font-weight: bold;">${this.point.series.name}</span><br>
          <span>Year: </span>
          <span style="font-weight: bold;">
          ${this.point.category}</span><br>
          <span>Patent families: </span>
          <span style="font-weight: bold;">${this.point.y}</span><br>
          <span>Percent: </span>
          <span style="font-weight: bold;">${percent}%</span><br>`;
    },
    shared: false,
    shape: 'square',
    useHTML: true
  },
};
