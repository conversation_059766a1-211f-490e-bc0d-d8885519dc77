<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component"
                     [showHeaderBar]="false" [title]="title" chartClass="landscape-chart"
                     spinner="assets/images/octimine_blue_spinner.gif" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    (click)="selectPoint($event)"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    constructorType='chart'
                    id="tl"
                    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
