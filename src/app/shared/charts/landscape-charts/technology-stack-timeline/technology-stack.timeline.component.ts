import { AfterViewChecked, Component, Injector, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { abbreviations } from '@shared/charts';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-technology-stack-timeline',
  templateUrl: './technology-stack-timeline.component.html',
  styleUrls: ['./technology-stack-timeline.component.scss']
})
export class TechnologyStackTimelineComponent extends BaseChartComponent implements OnInit, OnDestroy, AfterViewChecked {
  @Input() exportCssSelector = '.chart-content';

  component = TechnologyStackTimelineComponent;
  chartName = 'basic_technology_stack_timeline';
  title = 'Top five technology fields';
  chartOptions = settings.chartSetting;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);
  }

  ngAfterViewChecked() {
    if (this.chart) {
      this.chart.reflow();
    }
  }

  selectPoint(event) {
    if (!event.point || !event.point.category) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const abbr = abbreviations.find(ab => ab.abbr === event.point.series.name);
    filter.query = `((PRIORITY_DATE=${event.point.category}) AND ` +
      `(TECH_FIELDS=${abbr.full}))`;
    filter.value = `Field: ${abbr.full}, Year: ${event.point.category}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.update({series: this.datasource.yAxis}, true, true);
      for (const serie of this.chart.series) {
        serie['options'].totalHits = this.storeService.state.total_hits;
      }
      this.chart.xAxis[0].setCategories(this.datasource.xAxis, true, true);
      this.reflowChart();
    }
  }
}
