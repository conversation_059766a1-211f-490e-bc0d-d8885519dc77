import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'heatmap',
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    verticalAlign: 'top',
    symbolHeight: 360,
    itemStyle: {
      color: chartsConfig.labelColor
    }
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: { text: null },
  xAxis: {
    title: {
      text: 'Authority',
      style: chartsConfig.textStyles
    },
    categories: [],
    labels: {
      formatter: function () {
        return `<div class="text-center"><i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>&nbsp;${this.value}</div>`;
      },
      useHTML: true,
      style: chartsConfig.textStyles
    }
  },
  yAxis: {
    title: {
      text: 'Applicant',
      style: chartsConfig.textStyles
    },
    categories: [],
    reversed: true,
    gridLineWidth: 1,
    labels: {
      enabled: true,
      rotation: -45,
      style: chartsConfig.textStyles
    },
  },
  series: [{
    allowPointSelect: false,
    borderColor: chartsConfig.labelColor,
    borderWidth: 0,
    cursor: 'pointer',
    data: [],
    turboThreshold: 0,
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    },
    dataLabels: {
      enabled: false,
      color: '#000',
      style: {...chartsConfig.dataLabelStyles, textOutline: '0px', fontSize: '10px'},
    },
  }],
  tooltip: {
    formatter() {
      const percent = ((this.point.value / this.series.options.totalHits) * 100).toFixed(2);
      return `
          <span style="font-weight: bold;">${this.series.yAxis.categories[this.point.y]}</span><br>
          <span>Authority: </span>
          <span style="font-weight: bold;">
          ${this.series.xAxis.categories[this.point.x]}</span><br>
          <span>Patent families: </span>
          <span style="font-weight: bold;">${this.point.value}</span><br>
          <span>Percent: </span>
          <span style="font-weight: bold;">${percent}%</span><br>`;
    },
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
  },
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 20;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
  name: 'Number of applicants'
};

export const tooltipText = `This graph illustrates applicant activity with respect to the main authorities.<br>
The vertical axis displays the top applicants based on the number of patent families, while the horizontal axis identifies the respective patent authorities. The heatmap's color intensity represents the number of patent family publications, with darker colors indicating a higher number for a given applicant at a specific authority.<br>
<b>Suggestion:</b><br>
Use this heatmap to compare the most active applicants at different patent authorities or to assess the freedom to operate with respect to the analyzed results. Click on the grid to explore the patent families associated with a specific applicant.`

