<app-base-card-chart (sliderChanged)="onUserChangeEnd()" [(sliderValue)]="quantity" [sliderOptions]="sliderOptions"
                     [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [title]="title" [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector" [showZoomOption]="showZoomOption"
                     [chartItemHeight]="chartItemHeight" [isEmpty]="isEmpty"
                     [urlTooltipArticle]="urlTooltipArticle.authorities">
  <highcharts-chart
    (chartInstance)="onChartInstance($event)"
    (click)="selectPoint($event)"
    [Highcharts]='Highcharts'
    [options]='chartOptions'
    [hidden]="isCalculating"
    constructorType='chart'
    style="display: block;">
  </highcharts-chart>
  <div *ngIf="isCalculating" class="d-flex justify-content-center align-items-center spinner" style="height: 100%;">
    <span class="spinner-wrapper"></span>
    <img alt="Please hold" src="assets/images/octimine_blue_spinner.gif">
  </div>
</app-base-card-chart>
