import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import { debounceTime, finalize, take } from 'rxjs/operators';
import * as Highcharts from 'highcharts';
import * as settings from './settings';
import { Options } from '@angular-slider/ngx-slider';
import _ from 'lodash';
import { HumanReadableNumberPipe } from '@core';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';


@Component({
  selector: 'app-top-applicants-authority',
  templateUrl: './top-applicants-authority.component.html',
  styleUrls: ['./top-applicants-authority.component.scss']
})
export class TopApplicantsAuthorityComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = TopApplicantsAuthorityComponent;
  chartName = 'basic_top_applicants_in_authority';
  title = 'Applicants per authority';
  chartOptions = settings.chartSetting;
  sliderOptions: Options = settings.sliderSetting;
  quantity: number = settings.MAX_VALUE_SLIDER;
  tooltipText = settings.tooltipText;

  constructor(
    protected injector: Injector,
    private humanReadableNumberPipe: HumanReadableNumberPipe
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    this.popupText = this.tooltipText;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();

    const singleChartColumn$ = this.storeService.singleChartColumn$
      .pipe(debounceTime(500))
      .subscribe({
        next: () => {
          this.updateChartInstanceOptions();
        }
      });
    this.subscriptions.add(singleChartColumn$);

    const patentListViewMode$ = this.storeService.patentListViewMode$
      .pipe(debounceTime(500))
      .subscribe({
        next: () => {
          this.updateChartInstanceOptions();
        }
      });
    this.subscriptions.add(patentListViewMode$);
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const applicants = event.point.series.yAxis.categories[event.point.y];
    const authorities = event.point.series.xAxis.categories[event.point.x];
    if (authorities === 'Total' || applicants === 'Total') {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    filter.query = `((AUTHORITIES=${authorities}) AND (APPLICANTS=${applicants}))`;
    filter.value = `Applicant: ${applicants}, Authority: ${authorities}`;
    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      let maxValue = 0;
      let total = 0;
      const values = _.cloneDeep(this.datasource.values.filter(value => value[2] > 0));
      const xAxis = _.cloneDeep(this.datasource.xAxis);
      const yAxis = _.cloneDeep(this.datasource.yAxis);
      const self = this;

      values.forEach((value, i) => {
        if (value[0] === xAxis.length - 1 || value[1] === yAxis.length - 1) {
          values[i] = {
            x: value[0],
            y: value[1],
            value: value[2],
            color: '#cecece',
            dataLabels: {
              enabled: true,
              formatter: function () {
                return this.point.value > 999 ? self.humanReadableNumberPipe.transform(this.point.value, 1) : this.point.value;
              },
            },
          };
          if (value[0] === xAxis.length - 1 && value[1] === yAxis.length - 1) {
            total = value[2];
          }
        } else {
          maxValue = Math.max(maxValue, value[2]);
        }
      });

      this.chart.series[0]['options'].totalHits = total;
      this.chart.colorAxis[0].update({max: maxValue});
      this.chart.xAxis[0].setCategories(xAxis);
      this.chart.yAxis[0].setCategories(yAxis);
      this.chart.series[0].setData(values, true, true, false);
      setTimeout(() => {
        this.updateChartInstanceOptions();
      }, 500);
      this.reflowChart();
    }
  }

  // Slider
  onUserChangeEnd(): void {
    if (this.chart && this.chart.series && this.datasource) {
      this.updateChart();
    }
  }
  private updateChart(): void {
    this.isCalculating = true;
    this.storeService.chartQuantity = this.quantity;
    const payload = {
      charts: [
        'basic_top_applicants_in_authority'
      ],
      search_filters: {}
    };

    const free_text_query = this.storeService.filters
      .filter(filter => filter.type === 'chart')
      .map(filter => filter.query)
      .join(' AND ');
    if (free_text_query) {
      payload.search_filters['free_text_query'] = free_text_query;
    }
    if (this.quantity > 0) {
      payload['parameters'] = {top_applicants_in_authority_quantity: this.quantity};
    }
    const calculateToSingleChart$ = this.chartsService.calculateToSingleChart(payload, this.storeService.searchHash)
      .pipe(
        take(1),
        finalize(() => this.isCalculating = false)
      )
      .subscribe({
        next: ({charts}) => {
          const allCharts = {...this.chartsService.charts};
          allCharts['basic_top_applicants_in_authority'] = charts[this.chartName];
          this.chartsService.setCharts(allCharts);
        },
        error: (err) => {
          console.log(err);
        }
      });
    this.subscriptions.add(calculateToSingleChart$);
  }

  private updateChartInstanceOptions() {
    if (this.chart && this.chart.series && this.datasource) {
      const xAxisLen = this.datasource.xAxis.length - 1;
      const shouldRotateLabel = xAxisLen > 8 && (this.storeService.isCombinedMode || !this.storeService.singleChartColumn);
      this.chart.xAxis[0].update({
        labels: {
          formatter: function () {
            const separator = '&nbsp;';
            if (shouldRotateLabel) {
              return `<div class="text-center d-flex column-flex align-items-center" >
                        <i style="transform: rotate(180deg);" class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>
                        <div style="transform: rotate(180deg);">${this.value}${separator}</div>
                      </div>`;
            } else {
              return `<div class="text-center d-flex column-flex align-items-center" >
                      <i class="${COUNTRY_FLAG(this.value, FlagSizeEnum.MD)}"></i>
                      <div>${separator}${this.value}</div>
                    </div>`;
            }
          },
          rotation: shouldRotateLabel ? 90 : 0
        }
      });
    }
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.values.length < 2;
  }
}
