import {chartsConfig} from './../../config'

export const chartSetting = {
  chart: {
    backgroundColor: chartsConfig.bgColor,
    type: 'venn',
    spacing: [0, 0, 0, 0],
  },
  series: [{
    type: 'venn',
    name: '',
    data: [],
    dataLabels: {
      style: chartsConfig.dataLabelStyles,
    },
  }],
  title: {
    text: null
  },
  tooltip: {
    headerFormat: '',
    footerFormat: '',
    useHTML: true,
    shared: false,
    style: chartsConfig.tooltipStyles,
    pointFormatter: function () {
      return `<div><i class="fa-solid fa-circle"  style="color: ${this.color}; margin-right: 5px;"></i> ${this.tooltiptitle}: <b>${this.value}</b></div>`
    }
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  plotOptions: {
    venn: {
      cursor: 'pointer',
      allowPointSelect: true,
      inactiveOtherPoints: true,
      opacity: 1,
      states: {
        select: {
          color: '#AE6800',
          borderColor: '#fff',
          borderWidth: 5,
          dashStyle: 'dot'
        }
      }
    }
  }
};
