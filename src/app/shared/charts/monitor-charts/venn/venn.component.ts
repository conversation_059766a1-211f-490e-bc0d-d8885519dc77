import { Component, EventEmitter, Injector, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import * as Highcharts from 'highcharts';
import addMore from 'highcharts/highcharts-more';
import vennInit from 'highcharts/modules/venn';
import * as popupText from '../../popup-information';
import { chartSetting } from './settings';
import { MonitorStoreService } from '@core/store';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import {chartsConfig} from './../../config'

addMore(Highcharts);
vennInit(Highcharts);

@Component({
  selector: 'app-venn',
  templateUrl: './venn.component.html',
  styleUrls: ['./venn.component.scss']
})
export class VennComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = VennComponent;
  @Output() reload: EventEmitter<Object> = new EventEmitter();
  chartName = 'monitor_venn_diagram';
  title = 'Results per search method';
  datasource = [];
  filterArea = '';
  chartOptions = chartSetting;
  tooltipText = `<p>The circles in this Venn diagram represent the relative sizes of the monitoring method involved in the selected monitoring interval.</p>`;
  popupText = popupText.monitorVenn;

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }
        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
        this.isCalculating = false;
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }
    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'venn-chart'
      };
    }

    let value = event.point.name;
    if (value.indexOf('DEEP LEARNING') > -1) {
      value = value.replace('DEEP LEARNING', 'MACHINE_LEARNING');  // The snapshot type still uses old name internally
    }

    filter.query = value;
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }
    this.storeService.setFilters(filters);
    this.reload.emit(true);
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      let i=0, j=0;
      this.datasource.forEach((ds, index) => {
        if(ds.sets?.length > 1){
          ds['color'] = chartsConfig.colorPaletteDark[i];
          i++;
        } else {
          ds['color'] = chartsConfig.colorPaletteLight[j];
          j++;
        }
      });
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }
}
