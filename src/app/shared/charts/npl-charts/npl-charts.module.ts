import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';
import { NplCitationsReferencesComponent } from './npl-citations-references/npl-citations-references.component';
import { NplTechnologicalFieldsComponent } from './npl-technological-fields/npl-technological-fields.component';
import { NplTechnologyTimeLineComponent } from './npl-technology-time-line/npl-technology-time-line.component';
import { NplTopAuthorsComponent } from './npl-top-authors/npl-top-authors.component';

@NgModule({
  declarations: [
    NplCitationsReferencesComponent,
    NplTechnologicalFieldsComponent,
    NplTechnologyTimeLineComponent,
    NplTopAuthorsComponent,
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    NplCitationsReferencesComponent,
    NplTechnologicalFieldsComponent,
    NplTechnologyTimeLineComponent,
    NplTopAuthorsComponent,
  ]
})
export class NPLChartsModule {
}
