import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-npl-citations-references',
  templateUrl: './npl-citations-references.component.html',
  styleUrls: ['./npl-citations-references.component.scss']
})
export class NplCitationsReferencesComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NplCitationsReferencesComponent;
  chartName = 'npl_citations_references';
  title = 'Number of references and citations';
  chartOptions = settings.chartSetting;
  tooltipText = `Number of Citations and References`;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource.referenceCount, true, true, false);
      this.chart.series[1].setData(this.datasource.citationCount, true, true, false);
      this.chart.data = [this.datasource.referenceCount, this.datasource.citationCount];
      this.reflowChart();
    }
  }

  selectPoint(event) { 
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const range = event.point.category.split('-');
    const field = event.point.total > 0 ? 'CITATION' : 'REFERENCE';
    if (range[0] === '>100') {
      filter.value = `${field}${range[0]}`;
    } else {
      filter.value = `${field}=${range[0]}` + (range.length > 1 ? `-${range[1]}` : '');
    }

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }
}
