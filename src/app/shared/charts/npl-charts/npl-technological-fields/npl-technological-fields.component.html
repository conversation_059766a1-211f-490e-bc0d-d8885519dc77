<app-base-card-chart [chartName]="chartName" [chart]="chart" [component]="component" [popupText]="popupText"
                     [showFavoriteOption]="showFavoriteOption" [showZoomOption]="showZoomOption" [title]="title"
                     [tooltipText]="tooltipText" [storeService]="storeService"
                     [exportable]="exportable" [exportCssSelector]="exportCssSelector"
                     [chartItemHeight]="chartItemHeight">
  <highcharts-chart (chartInstance)="onChartInstance($event)"
                    [Highcharts]='Highcharts'
                    [options]='chartOptions'
                    (click)="selectPoint($event)"
                    constructorType='chart'
                    id="tf"
                    style="display: block;">
  </highcharts-chart>
</app-base-card-chart>
