import { Component, Injector, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import More from 'highcharts/highcharts-more';
import heatmap from 'highcharts/modules/heatmap';
import treemap from 'highcharts/modules/treemap';
import * as settings from './settings';
import _ from 'lodash';
import { chartsConfig } from '@shared/charts/config';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);
heatmap(Highcharts);
treemap(Highcharts);

@Component({
  selector: 'app-npl-technological-fields',
  templateUrl: './npl-technological-fields.component.html',
  styleUrls: ['./npl-technological-fields.component.scss']
})
export class NplTechnologicalFieldsComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NplTechnologicalFieldsComponent;
  chartName = 'npl_technological_fields';
  title = 'Top scientific fields';
  datasource = [];
  filterArea = '';

  chartOptions = settings.chartSetting;

  tooltipText = `Top Technological Fields`;
  popupText = `Top Technological Fields`;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }


  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.defineColors();
      this.chart.series[0].options.totalHits = this.storeService.state.total_hits;
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }

  private defineColors() {
    const colors = chartsConfig.colorPalette;
    this.datasource.forEach((item, index) => {
      item['color'] = colors[index < colors.length ? index : index - colors.length - 1];
    });
  }

  selectPoint(event) { 
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const field = event.point.name;

    filter.query = `FIELD_OF_STUDY=${field}`;
    filter.value = field;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }
}
