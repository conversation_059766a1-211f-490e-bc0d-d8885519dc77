import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'pie',
    spacing: [10, 10, 10, 10],
    backgroundColor: chartsConfig.bgColor,
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: { text: null },
  colors: chartsConfig.colorPalette,
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold">${this.point.name}</span><br />
          <span>Publications: </span><span style="font-weight:bold">${this.point.y}</span><br/>
          <span>Percent: </span><span style="font-weight:bold"> ${this.point.percentage.toFixed(2)}%</span>`;
    }
  },
  plotOptions: {
    pie: {
      shadow: false,
      cursor: 'pointer',
      dataLabels: {
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  series: [{
    name: 'Technology fields',
    dataLabels: {
      formatter() {
        return `<span>${this.point.name}</span><br><span>${this.point.percentage.toFixed(2)}%</span>`
      }
    },
    id: 'technology_fields',
    data: [],
    totalHits: 0
  }]
};
