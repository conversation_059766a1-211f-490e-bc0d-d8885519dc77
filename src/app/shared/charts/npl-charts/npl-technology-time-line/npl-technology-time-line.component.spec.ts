import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core/store/semantic-search-store/semantic-search-store.service';
import { provideMatomo } from 'ngx-matomo-client';
import { HighchartsChartModule } from 'highcharts-angular';

import { NplTechnologyTimeLineComponent } from './npl-technology-time-line.component';

describe('NplTechnologyTimeLineComponent', () => {
  let component: NplTechnologyTimeLineComponent;
  let fixture: ComponentFixture<NplTechnologyTimeLineComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [NplTechnologyTimeLineComponent],
      imports: [
        HighchartsChartModule,
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NplTechnologyTimeLineComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
