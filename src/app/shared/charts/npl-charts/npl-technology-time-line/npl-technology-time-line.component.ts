import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';

import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-npl-technology-time-line',
  templateUrl: './npl-technology-time-line.component.html',
  styleUrls: ['./npl-technology-time-line.component.scss']
})
export class NplTechnologyTimeLineComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NplTechnologyTimeLineComponent;
  chartName = 'npl_technology_timeline';
  title = 'Scientific time line';
  chartOptions = settings.chartSetting;
  tooltipText = `Technology time line`;
  popupText = `Technology time line`;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[1].setData(this.datasource.yAxis, true, true, false);
      this.chart.xAxis[0].setCategories(this.datasource.xAxis, true, true);
      this.chart.series[0].update({showInLegend: false, visible: false});
      this.reflowChart();
    }
  }

  selectPoint(event) { 
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const year = event.point.category;

    filter.query = `YEAR=${year}`;
    filter.value = year;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }
}
