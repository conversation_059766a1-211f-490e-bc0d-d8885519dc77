import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    spacing: [10, 0, 10, 0],
    backgroundColor: chartsConfig.bgColor
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: {text: null},
  colors: chartsConfig.colorPalette,
  legend: {
    enabled: false,
    floating: true,
    verticalAlign: 'top',
    itemStyle: {...chartsConfig.textStyles, cursor: 'default'},
  },
  plotOptions: {
    column: {
      allowPointSelect: false,
      cursor: 'pointer',
      groupPadding: 0,
      pointPadding: 0,
      borderWidth: 1
    },
    spline: {
      lineWidth: 3,
      marker: {
        enabled: false
      }
    }
  },
  series: [{
    color: '#AE6800',
    data: [],
    type: 'spline',
    zIndex: 2,
    events: {
      legendItemClick() {
        return false;
      }
    },
    showInLegend: true,
  }, {
    color: '#389687',
    data: [],
    type: 'column',
    events: {
      legendItemClick() {
        return false;
      }
    },
    yAxis: 1,
    showInLegend: true,
  }],
  xAxis: [{
    categories: [],
    crosshair: true,
    minTickInterval: 5,
    title: {
      text: 'Priority year',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  yAxis: [{
    allowDecimals: false,
    endOnTick: false,
    lineWidth: 1,
    opposite: true,
    title: {
      text: 'Similarity average',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }, {
    allowDecimals: false,
    lineWidth: 1,
    title: {
      text: 'Articles',
      style: chartsConfig.textStyles
    },
    labels: {
      style: chartsConfig.textStyles
    }
  }],
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      if (!this.points[1]) {
        // if Similarity Average is hidden
        return '<span>Year: <b>' + this.x + '</b></span>' +
          '<br/><span>Articles: <b>' + this.points[0].y + '</b></span>' +
          '<br/><span>Percent: <b>' + ((this.points[0].y / 1000) * 100).toFixed(2) + '%</b></span>';
      }

    },
    shared: true,
    useHTML: true
  },
};

export const sliderSetting = {
  floor: 0,
  ceil: 50,
  showSelectionBar: true,
  noSwitching: true,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  }
};
