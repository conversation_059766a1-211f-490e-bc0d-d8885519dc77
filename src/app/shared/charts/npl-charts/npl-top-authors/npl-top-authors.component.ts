import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NplSearchService } from '@core';
import { Options } from '@angular-slider/ngx-slider';
import * as Highcharts from 'highcharts';
import { Subscription } from 'rxjs';
import * as settings from './settings';
import { finalize, take } from 'rxjs/operators';
import _ from 'lodash';
import { chartsConfig } from '@shared/charts/config';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

@Component({
  selector: 'app-npl-top-authors',
  templateUrl: './npl-top-authors.component.html',
  styleUrls: ['./npl-top-authors.component.scss']
})
export class NplTopAuthorsComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = NplTopAuthorsComponent;
  chartName = 'npl_top_authors';
  title = 'Top authors';
  dataSource = [];
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  quantity = 5;
  sliderOptions: Options = settings.sliderSetting;
  top5 = [];
  totalItems = 0;
  tooltipText = `Top authors`;
  popupText = `Top authors`;
  /**
   * Error container
   */
  public error: any;

  constructor(
    protected injector: Injector,
    private nplSearchService: NplSearchService,
  ) {
    super(injector);
  }

  ngOnInit() {
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        this.dataSource = [];
        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }
        if (chart.error) {
          this.error = chart.error;
          return;
        }
        this.error = undefined;

        this.dataSource = chart.datasource;
        this.top5 = chart.top;
        this.totalItems = chart.total;
        if (!this.dataSource) {
          return;
        }

        this.setOptionsSlider();

        this.updateChartData();

        this.isCalculating = false;
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  setColors(dataSource) {
    const othersColors = [];
    if (dataSource.length > 1) {
      if (dataSource[dataSource.length - 2]['name'] === 'Others' || dataSource[dataSource.length - 1]['name'] === 'Others') {
        othersColors.push('#344371');
      }
      if (dataSource[dataSource.length - 1]['name'] === 'N/A') {
        othersColors.push('#BBB');
      }
    }

    const newColors = chartsConfig.colorPalette.slice(0, dataSource.length - othersColors.length);
    othersColors.forEach(item => {
      newColors.push(item);
    });
    return newColors;
  }

  setOptionsSlider() {
    const lenDataSource = this.dataSource.filter(item => item.name !== 'Others' && item.name !== 'N/A').length;
    if (lenDataSource !== this.quantity) {
      this.quantity = lenDataSource;
    }

    if (this.storeService.filters.filter(item => item.chart === this.chartName).length) {
      this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: true});
    } else {
      this.sliderOptions = Object.assign({}, this.sliderOptions, {disabled: false});
    }
  }

  onUserChangeEnd(): void {
    this.updateChart();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.dataSource) {
      this.chart.series[0].options.totalHits = this.totalItems;
      this.chart.series[0].options.colors = this.setColors(this.dataSource);
      this.chart.series[0].setData(this.dataSource, true, true, false);
      this.reflowChart();
    }
  }

  private updateChart(): void {
    this.isCalculating = true;
    this.storeService.chartQuantity = this.quantity;
    const payload = {
      search_input: this.storeService.searchInput,
      charts: [
        'npl_top_authors'
      ],
      parameters: {npl_top_authors_quantity: this.quantity}
    };

    const calculateCharts$ = this.nplSearchService.calculateCharts(payload)
      .pipe(take(1), finalize(() => this.isCalculating = false))
      .subscribe({
        next: ({charts}) => {
          this.top5 = charts[this.chartName].top;
          this.dataSource = charts[this.chartName].datasource;
          this.updateChartData();
        },
        error: (err)=> {
          console.log(err);
          this.isCalculating = false;
        }
      });
    this.subscriptions.add(calculateCharts$);
  }

  selectPoint(event) { 
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const field = event.point.name;

    filter.query = `AUTHOR=${field}`;
    filter.value = field;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }

    this.storeService.setFilters(filters);
  }
}
