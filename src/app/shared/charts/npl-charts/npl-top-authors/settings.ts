import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'pie',
    spacing: [10, 10, 10, 10],
    backgroundColor: chartsConfig.bgColor,
  },
  credits: chartsConfig.credits,
  exporting: {enabled: false},
  title: { text: null },
  colors: ['#8CD4C8', '#6DA7A5', '#00729C', '#7CAED5', '#375484', '#A37FBE', '#6A61A8', '#665191'],
  zAxis: {
    categories: [],
    labels: {}
  },
  plotOptions: {
    pie: {
      allowPointSelect: false,
      cursor: 'pointer',
      innerSize: '35%',
      depth: 60,
      dataLabels: {
        enabled: true,
        padding: 0,
        format: '{point.name}',
        style: chartsConfig.dataLabelStyles,
      }
    }
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    formatter: function () {
      return `<span style="font-weight:bold">${this.point.name}</span><br />
        <span>Publications: </span><span style="font-weight:bold">
        ${this.point.absolute}</span><br />
        <span>Percent: </span><span style="font-weight:bold">
        ${((this.point.absolute / this.series.options.totalHits) * 100).toFixed(2)}%</span>`;
    }
  },
  series: [{
    type: 'pie',
    name: 'Percentual',
    data: {},
    totalHits: 0
  }]
};

export const MIN_VALUE_SLIDER = 5;
export const MAX_VALUE_SLIDER = 30;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  hidePointerLabels: false,
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },

  translate: (value: number): string => {
    return 'Top ' + value;
  }
};
