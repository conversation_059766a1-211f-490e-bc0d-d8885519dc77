export const basicApplicants = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
<p><b>Use case:</b> For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by <PERSON><PERSON><PERSON> et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
<p><b>Description of the graph</b></p>
<p>The slices of this pie chart represent the relative sizes of the patent family portfolios of the <b>top 5 applicant names</b> based on
the patent families in the octimine result list</p>
<p>Click on one slice to filter the result list to the slice’s applicant name. This will display only the patent families concerning that
applicant´s name. All other charts will be automatically readjusted to reflect this restriction to a single applicant name. This
readjustment of all charts permits tracking the patenting activity of a given applicant. For instance, selecting a specific applicant
(see figure 1) permits to analyse via the octimine IPC or CPC heat maps (see figure 2), in order to know in which technical fields its R/D
and patenting activity has been concentrated over time for the subject of your query. To reset the filter and return to the original list
and display, click again on the same name slice.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_applicants_1.jpg"></img><br><small><i>Figure 1: Top
Applicants (Bosch applicant) - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_applicants_2.jpg"></img><br><small><i>Figure 2: Top
IPC Codes (Bosch Applicant) - diesel use case</i></small></p>
<p>Note: The ranking of an applicant among the top 5 applicants (see figure 3) is likely to be underestimated in the pie chart if that
applicant files patents under different names. This may be because the name of an applicant has changed over time, been affected by
alliances and fusions, or that a multinational applicant uses slightly different names in different countries. Therefore, the size of the
slices in the “Applicants distribution” pie chart may be misleading because different pie slices with different names can correspond to the same
applicant. This situation is very frequent in the automotive industry.</p>
<p>For instance, for the diesel example, clicking on the “others” (see figure 4) pie chart reveals multiple different applicant names for
large multinational automotive companies:</p>
<p>•    General Motors Corporation is either listed as “Gen Motors” with 6 patent families or “GM global tech operations” with 17 patent
families, i.e. a total of 23.</p>
<p>•    Ford Motor Company is listed as “Ford Motor” with 13 patent families or “Ford Global” with 10 patent families, “Ford Werke” with 2
patent families or “Ford France” with 1, i.e. a total of 26;</p>
<p>•    Daimler is likewise variously listed as “Daimler Chrysler”, “Daimler Benz”, “Daimler”, or “Steyr Daimler Puch” with 10, 11, 5 and
2 patent families respectively, i.e. a total of 28.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_applicants_3.jpg"></img><br><small><i>Figure 3: Top
Applicants (Others) - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_applicants_3.jpg"></img><br><small><i>Figure 4: Top
Applicants (Others with names) - diesel use case</i></small></p>
<p>These multinational groups should have been listed among the top five in the “Applicants distribution” chart, but remained hidden from view
because of the different applicant names they used. Likewise, please note that Renault and Nissan are represented by different slices
(in total 35 patent families, see image Applicants distribution</p>
<p>1) but have been in a close alliance and have been technologically interdependent since 1999 (common powertrains and engine emissions
control).</p>
<p>To avoid the corruption of analytics, you should use the <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9097-top-applicants" target="_blank">“harmonize
applicants”</a> feature whenever you detect that the same applicant has filed patent under different names.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9097-top-applicants" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicOwners = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
<p><b>Use case:</b> For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
<p><b>Description of the graph</b></p>
<p>The slices of this pie chart represent the relative sizes of the patent family portfolios of the <b>top 5 owner names</b> based on
the patent families in the octimine result list</p>
<p>Click on one slice to filter the result list to the slice’s owner name. This will display only the patent families concerning that
owner´s name. All other charts will be automatically readjusted to reflect this restriction to a single owner name. This
readjustment of all charts permits tracking the patenting activity of a given owner. For instance, selecting a specific owner
(see figure 1) permits to analyse via the octimine IPC or CPC heat maps (see figure 2), in order to know in which technical fields its R/D
and patenting activity has been concentrated over time for the subject of your query. To reset the filter and return to the original list
and display, click again on the same name slice.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_owners_1.jpg"></img><br><small><i>Figure 1: Top
Owners (Bosch owner) - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_owners_2.jpg"></img><br><small><i>Figure 2: Top
IPC Codes (Bosch Owner) - diesel use case</i></small></p>
<p>Note: The ranking of an owner among the top 5 owners (see figure 3) is likely to be underestimated in the pie chart if that
owner files patents under different names. This may be because the name of an owner has changed over time, been affected by
alliances and fusions, or that a multinational owner uses slightly different names in different countries. Therefore, the size of the
slices in the “Owners distribution” pie chart may be misleading because different pie slices with different names can correspond to the same
owner. This situation is very frequent in the automotive industry.</p>
<p>For instance, for the diesel example, clicking on the “others” (see figure 4) pie chart reveals multiple different owner names for
large multinational automotive companies:</p>
<p>•    General Motors Corporation is either listed as “Gen Motors” with 6 patent families or “GM global tech operations” with 17 patent
families, i.e. a total of 23.</p>
<p>•    Ford Motor Company is listed as “Ford Motor” with 13 patent families or “Ford Global” with 10 patent families, “Ford Werke” with 2
patent families or “Ford France” with 1, i.e. a total of 26;</p>
<p>•    Daimler is likewise variously listed as “Daimler Chrysler”, “Daimler Benz”, “Daimler”, or “Steyr Daimler Puch” with 10, 11, 5 and
2 patent families respectively, i.e. a total of 28.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_owners_3.jpg"></img><br><small><i>Figure 3: Top
Owners (Others) - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_owners_3.jpg"></img><br><small><i>Figure 4: Top
Owners (Others with names) - diesel use case</i></small></p>
<p>These multinational groups should have been listed among the top five in the “Owners distribution” chart, but remained hidden from view
because of the different owner names they used. Likewise, please note that Renault and Nissan are represented by different slices
(in total 35 patent families, see image Owners distribution</p>
<p>1) but have been in a close alliance and have been technologically interdependent since 1999 (common powertrains and engine emissions
control).</p>
<p>To avoid the corruption of analytics, you should use the <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9097-top-owners" target="_blank">“harmonize
owners”</a> feature whenever you detect that the same owner has filed patent under different names.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9097-top-owners" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicTechFields = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
<p><b>Use case:</b> For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked." </p>
<p><b>Graph description</b></p><p>This chart shows the distribution of the patent families of the result
list over the 35 broad <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">technological
</a> fields (35TF)  defined by WIPO document IPC/CE/41/5 (“WIPO IPC-Technology Concordance Table”). This simplified technology
classification aims at aggregating different IPC-codes into just 35 concisely named classes. The main advantage here is that it aims to
group and display technological areas and instead of search codes. Click on a main category like “Chemistry” to display further 35TF
subcategories.</p>
<p>In the diesel emissions example, 611 patent families of the result list belong to the subcategory “environmental technology” of
chemistry (see figure 5). Clicking on that subcategory again, the result list will be limited to that particular subcategory and all other
charts will be automatically readjusted to reflect this restriction.</p>
<p>To reset the filter, click on the “back” arrow at the top right. This would permit clicking on the “mechanical engineering” main
category, which shows as much as 508 families in the “engines, pumps, turbines” subcategory (see figure 6).</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_tech_fields_5.jpg"></img><br><small><i>Figure 5: Top
Technological Fields - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_tech_fields_6.jpg"></img><br><small><i>Figure 6: Top
Technological Fields - diesel use case</i></small></p>
<p>Note: A patent sometimes gets several IPC codes which belong to several different 35TF broad technical fields, if for example it
improves or uses more than one technology. Therefore, the respective sizes of the different 35TF areas are based on the total number of
different patent to 35 TF technological field observations while the percentage value shows the percentage of patent families assigned to
that 35TF technological field.</p>
<p>Besides the 35TF fields are technologically very coarse, much coarser than the 70 000+ IPC codes or the several hundred thousand CPC
codes, which finely describe all fields of technology. This very coarse representation may be useful for general statistical purposes.
However, for R&D or more specific IP purposes, a more precise information about the technological nature of the individual inventions
contained in the results list can be obtained by clicking on “classification” to view the “top IPC codes” and “top CPC codes” as well as
the IPC and CPC heat maps. Doing so would show, for the diesel emissions example, a predominance of patent families in the F01N3 range,
which discusses exhaust gas treatment purification and control.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9098-top-technological-fields" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicTechnologyTimeLine = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u>
</p>
<p><b>Use case:</b>For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
<p><b>Graph description</b></p>
<p>This chart shows the patent families of the result list distributed over their priority year.  The chart can be interpreted as the
lifecycle of the technology defined by your query and displays the patenting activity of that queried technology over the years, past
peaks of activity and revealing trends (is the R&D effort dying or increasing? Is the queried technology outdated, e.g. like DVD or
      optical disk technology?).</p>
<p>The height of the <b>green bars</b> represents the number of patent families in the result list having the respective priority year. If
you entered a patent number into the search field, the light green bar will show its priority year.</p>
<p>The <b>highest green bars</b> (see figure 7) denote the years with the most numerous or more frequent advances in the result list for
the queried technology. Note that the “technology fields over time heat map” may give you a better overview of the technological fields
involved in the progress being made.</p>
<p>For instance, for the diesel example, the numbers of technological fields involved has significantly increased since 1990, visualizing
a technological shift not readily apparent in the time line, which is mainly due to the appearance and increasing role of computer control
in engine management. </p>
<p>The <b>orange curve</b> (see figure 8) shows the similarity average of the patent families assigned to a particular priority year.
Peaks in the yellow curve signal the priority years of documents which, at least on average, are likely to be most similar to the
technology of your query, i.e. the year when the technology advances had most of the features described by the query</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_technology_timelie_7.jpg"></img><br><small><i>Figure
7: Technology time line - diesel use case</i></small></p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_technology_timelie_8.jpg"></img><br><small><i>Figure
8: Technology time line (highest similarity) - diesel use case</i></small></p>
<p>If you are looking for a recent technology and the orange curve is lower for recent years, then probably your query in fact best
matches older technology and you may need to reformulate it to obtain a more up to date list of results.</p>
<p>In such situations, please also double check the peaks of the black line in the top IPC chart (if you have clicked on “classification”)
to identify whether the technical fields octimine considers most relevant to your query really are those which you intended or expected
(see figure 9). If not, a reformulation of your octimine query may be needed. For instance, you may try removing outdated details, e.g.
mentions of CD, DVD or videocassettes.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_technology_timelie_9.jpg"></img><br><small><i>Figure
9: Top IPC Codes - diesel use case</i></small></p>
<p>For the diesel exhaust example, the level of the yellow curve is substantially constant around 350 for the past 20 years, and climbs
slightly in recent years, suggesting the query does a relatively good job of capturing at least the evolutions of this technology over
the past two decades.</p>
<p>Click on one of the green bars to restrict the result list to patent families with that priority year. Then, the clicked bar becomes
grey and only the patent families assigned to that priority year will appear in the result list. All other charts will be automatically
readjusted to reflect this restriction to a priority year. To reset the filter, click again on the same bar. </p>
<p>The hover mouse display provides quantitative information about specific points of the orange curve or specific green bars.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9099-technology-timeline" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicTechnologyTrend = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
<p><b>Use case:</b>For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
<p><b>Graph description</b></p>
<p>This chart displays the distribution of the patenting activity according to the result list in different technological fields  over
time. Each cell of this chart represents the number of patent families in the result list for a given priority year and technological
field. The 35TF technological fields of this representation were defined by WIPO document IPC/CE/41/5 (“WIPO IPC-Technology Concordance
Table”).</p><p>The chart provides an overview of the developments in all known <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields"
target="_blank">technology fields</a> over time by showing
the number of patent families applied for, in a given broad technology field, in a given year. Consequently, it provides valuable
information about technological trends, hot spots of research and the evolution of the technological landscape related to your query.</p>
<p>The chart with no filters being applied will give you a first overview of how the technology has evolved. (see figure 10). By filtering
your query to a specific competitor (to do so go to the “top 5 applicants” pie chart and click on the desired competitor or click on
      “competition”), you will get an insight of what this competitor has been up to, fields and intensity of its R&D efforts and
      patenting strategies over time across all known technologies. This may be invaluable when preparing a cross-licensing deal
      (to get qualitatively and quantitatively representative information, please make sure you have correctly identified under which
            names patents were applied for: see remarks under “top 5 applicants”, please make sure that your query is representative of
            current technology: see discussion under “time line”, and please make sure you have properly truncated the similarity index
            curve to keep only the most relevant results).</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_technology_trend_10.jpg"></img><br><small><i>Figure
10: Technology over Time Heat Map (no filters) - diesel use case</i></small></p>
<p>The mouse-over display over any cell of this chart shows the technological field, the priority year and the number of patent families
in the result list which were assigned to that individual cell. The number of patent families is color-coded: the deeper the color, the
higher the number of patents represented by that cell. Due to the lack of space, sometimes not all 35 technological fields are indicated
by name to the left of the matrix, so the mouse-over display is an important way to confirm the nature of the technological field. For
instance, for the diesel example, the important technological field “engines, pumps, turbines” is not explicitly listed between “machine
tools” and “textile, paper machines” but would appear in the mouse over display (see figure 11)</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_technology_trend_11.jpg"></img><br><small><i>Figure
11: Technology over Time Heat Map (engines, pumps, turbines) - diesel use case</i></small></p>
<p>Click on one of the colored cells to restrict the results list to the documents corresponding to that particular cell. Hence, only the
patent families assigned to that cell will appear in the result list. All other charts will be automatically readjusted to that limitation
of the result list. To reset the filter, click again on the same cell.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9100-technology-over-time-heat-map" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicAuthorities = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
 <p><b>Use case:</b> For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
 exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
 automotive exhaust sensor test”.</p>
 <p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
 <p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
 Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
 <p><b>Graph description</b></p>
 <p>This chart shows the regional distribution of the patent families of the result list by showing how many patents were filed in the
 different patent authorities. </p>
 <p>Each bar represents a patent authority. The higher the bar, the more patent families that have been applied for in that patent
 authority. </p>
 <p>If you have previously restricted the “top applicant graph” to all the applicant names used by a single competitor, the “Patent by
 Authorities” chart may be interpreted to anticipate or suggest the competitors market ambitions, and may reveal where in the world an
 applicant has plans of growth. </p>
 <p>For instance, the diesel exhaust example if we click in the top applicant “GM Tech Operations” (see figure 15) it reveals that GM Tech
 Operation has substantial patenting of this technology in China (on the same scale as in Germany) whereas other western manufacturers
 have practically no patents in China, not even Renault Nissan, which only has patents in their home markets (F, JP) and the US, but very
 few elsewhere (in Germany, for instance). This seems consistent with the market share of GM in China, and its 12 joint ventures,
 including the largest, SAIC-GM, producing about 2 million vehicles per year.</p>
 <p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_authorities_15.jpg"></img><br><small><i>Figure 15:
 Top Authorities (applicant filter) - diesel use case</i></small></p>
 <p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_authorities_16.jpg"></img><br><small><i>Figure 16:
 Top Authorities (CN and DE) - diesel use case</i></small></p>
 <p>Click on one bar to limit the result list to that particular patent authority. All other charts will be automatically readjusted to
 this limitation. To reset the filter, click again on the same bar.</p>
 <p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9101-patents-by-authorities" target="_blank">Help center</a>.</p>
 <p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const basicSimilarityCurve = `<p><u>In order understand this graph in detail, please have a quick look at the use case below.</u></p>
<p><b>Use case:</b> For a better explanation of the competition graph, we will use the example related to the automotive diesel engine
exhaust emission controls. For this purpose, we will query the search field with the following text: “diesel program emissions engine
automotive exhaust sensor test”.</p>
<p>Unsurprisingly, two of the top three applicants in the results list are Volkswagen (22 patent families) and Bosch (44 patent families
      related to diesel injection systems equipment used by most European car manufacturers).</p>
<p>Similar octimine results are obtained with various technical texts on this theme, e.g. by pasting into the search field the New-York
Times article by Guilbert Gates et al. dated March 16, 2017: "How Volkswagen’s ‘Defeat Devices’ Worked."</p>
<p><b>Graph description</b></p>
<p>The similarity curve shows the similarity of the patent families in the result list to your octimine query in decreasing order, i.e.
the patent with the highest similarity value will be ranked first, the one with the lowest will be ranked last.</p>
<p>The <b>horizontal axis</b> shows the ranking of the patent families in the result list. </p>
<p>The <b>vertical axis</b> indicates how similar these patent families are to your query. The higher the similarity index, the higher the
similarity between your query and the patents ranked in the graph, as revealed by a mouse-over over any point of the curve. </p>
<p>The leftmost top part of the curve (see figure 12), in general exhibiting a relatively steep descending slope above any elbow on the
curve, normally represents the documents which exhibit high similarity index values and which therefore are most relevant and most similar
to your query.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_similarity_curve_12.jpg"></img><br><small><i>Figure 12:
Similarity Curve (high relevance) - diesel use case</i></small></p>
<p>A sharp drop in the curve usually reveals that only a few patent families are relevant to your octimine query. Normally, the documents
represented in the flatter part of the descending slope are considered to be of low relevance (see figure 13).</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_similarity_curve_13.jpg"></img><br><small><i>Figure 13:
Similarity Curve (low relevance) - diesel use case</i></small></p>
<p>Note that if you are only interested in a small minority of very relevant documents, keeping too many irrelevant documents with a low
similarity index value in your result list may corrupt the other analytic graphs. For example, the percentages in the “top applicant graph”.
Therefore, it is highly recommended to systematically cut off this very long tail of mostly irrelevant results, preferably near the elbow
in the similarity curve, in order to obtain more relevant analytics graphs and to correctly interpret them.</p>
<p>To properly truncate the curve, click anywhere in the curve near its elbow to cut off this tail of less relevant results. The discarded
low-relevance documents will be represented in blue on the similarity density curve, and the retained results in orange (see figure 14).
All other charts will be automatically readjusted and will become more statistically meaningful. The list of results will thus be shortened
accordingly, keeping only the most similar and the most relevant documents.</p>
<p style="text-align: center; color: #44546a"><img src="/assets/images/charts/basic_similarity_curve_14.jpg"></img><br><small><i>Figure 14:
Similarity Curve (truncation) - diesel use case</i></small></p>
<p>For instance, in the diesel exhaust example shown below, the slope of the curve varies rapidly for similarity index values above 420,
and more slowly for lower values. A look at the relevance of the abstracts in the list of results confirms that documents with a similarity
index below 420 are less likely to be very relevant than above that threshold (see again figure 14). Hence, the curve truncation should
happen here. This would leave a truncated list of more than 100 very relevant documents, and the analytics on that reduced list would be
far more relevant.</p>
<p>For further information visit our <a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/9102-similarity-density-curve" target="_blank">Help center</a>.</p>
<p>Any questions still open? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>`;

export const classificationTopIpcCode = `<p>This chart shows the bar graph distribution over the 50 best represented full digit IPC patent
classification codes of the patent documents in the result list.</p>
<p>The height of the <b>vertical bars</b> reflects the number of documents representing the patent families in the result list which are
assigned to each displayed <a href="http://www.wipo.int/classifications/ipc/en/">IPC code</a>. The different bar colors represent the
different IPC sections.  IPC codes featuring particularly high bars are not necessarily relevant, as the height of the bar does not measure
the relevance of the documents to the query, just their number. Relevance is indicated by the yellow curve only.</p>
<p>The <b>yellow curve</b> shows for each represented IPC code the average of the similarity indices of the documents referencing that code.
This curve should be <b>carefully examined</b>, since it enables you to quickly identify highly relevant IPC classes.</p>
<p>The higher a peak of the yellow curve, the higher the <a href="https://intercom-help.eu/dennemeyer-octimine/en/collections/2498-interactive-graphs-explained" target="_blank">“average
similarity”</a> and the most likely it is that the respective IPC code includes very relevant documents. Hence, an IPC restriction to the
IPC of that peak should be considered to focus on more relevant results. Other high peaks in the yellow curve may either correspond to
other relevant IPC groups or may highlight very relevant patent documents which may have been misclassified by patent authorities.</p>
<p>If the query was properly understood, the yellow curve should exhibit only one or very few well defined high peaks, and the highest
peaks of the yellow curve normally should correspond to the most relevant IPC classes for your query text or patent number because they
correspond to the highest-ranked documents of the list, i.e. those which have the highest similarity index because they are most similar to
your query.</p>
<p>In cases where no high peaks but just a multitude of low peaks (typically with indices below 350) of the yellow curve are observed,
octimine likely misunderstood your query and needs to be helped along by an appropriate restriction to a suitable technological field or IPC
code. Hence, in such cases an IPC should be considered to help octimine focus on more relevant results. High peaks in the yellow curve
occurring for unexpected or apparently irrelevant IPC codes may also reveal important patent documents which have been misclassified. To
form an opinion about the relevance of each IPC code within its respective hierarchy, please consult the WIPO IPC web page, which includes
a link to the current version of the IPC: http://www.wipo.int/classifications/ipc/en/</p>
<p>Each document of the result list is represented by its publication number and is meant to represent a different “Simple Patent Family”.
The IPC patent classification codes/symbols associated with that document and printed on its front page at the time of publication were
intended to precisely and narrowly describe the technological field of the invention and have been attributed by patent officers.</p>
<p>Since all the patent documents in any given family normally are technically equivalent to one another, the IPC codes of the referenced
document are assumed (!) to correctly represent all the IPC codes and the whole technical contents of all the other members of that
particular family. In some cases, however, particularly before the introduction of CPC in 2012, different codes may have been attributed
to different members of the same family, not least due to differences of opinion or classification practice between authorities.</p>
<p>Note that the patent classifications of published patent documents are regularly updated and improved, e.g. when the characterization
of the invention changes during examination due to existing prior art and amendments, due to improvements in the classification scheme, or
due to agreements between authorities.</p>
<p>octimine therefore uses the latest set of IPC and CPC patent classification symbols, which are also directly available through Espacenet,
because the IPC and CPC codes printed on the front page of the patent document may have become insufficient or obsolete for any of the
above reasons.</p>
<p>Click on any bar to filter the list of results to the corresponding IPC code. Hence, only the patent families having an English language
patent document representative actually referencing that particular IPC code will appear in the result list. All other charts will also be
automatically readjusted to reflect this restriction to a IPC Code. Of course, it is best to click on a bar with a very high peak in the
yellow curve to focus on the most relevant documents.</p>
<p>Note: if more than 50 different IPC codes are available in the result list, only the top 50 IPC codes cited by the highest number of
documents in the result list will be shown in the graph.</p>
<p>If the query was properly understood, the yellow curve should exhibit only one or very few well defined high peaks, and the highest
peaks of the yellow curve normally should correspond to the most relevant IPC classes for your query text or document, because they
correspond to the highest-ranked documents of the list, i.e. those which have the highest similarity index because they are most similar
to your query.</p>`;

export const classificationTopCpcCode = `<p>This chart shows the bar graph distribution over the 50 best represented full digit CPC patent
classification codes of the patent documents in the result list corresponding to your query.</p>
<p>The height of the <b>vertical bars</b> reflects the number of documents representing patent families in the results list which are
assigned to each displayed CPC code. The different bar colors represent the different CPC sections.  CPC codes featuring particularly high
bars are not necessarily relevant, as the height of the bar does not measure the relevance of the documents to the query, just their
number. Relevance is indicated by the yellow curve only.</p>
<p>The <b>yellow curve</b> shows for each represented CPC code the average of the similarity indices of the documents referencing that
code. This curve should be carefully examined, since it enables you to quickly identify highly relevant CPC classes as well as to
appreciate whether your query was interpreted by octimine in the manner which you expected.</p>
<p>The higher a peak of the yellow curve, the higher the <a href="https://intercom-help.eu/dennemeyer-octimine/en/collections/2498-interactive-graphs-explained" target="_blank">“average
similarity”</a> and the most likely it is that the respective CPC code includes very relevant documents. Hence, a CPC restriction to the CPC
of that peak should be considered to focus on more relevant results. Other high peaks in the yellow curve may either correspond to other
relevant CPC groups or may highlight very relevant patent documents which may have been misclassified by patent authorities.</p>
<p>If the query was properly understood, the yellow curve should exhibit only one or very few well defined high peaks, and the highest
peaks of the yellow curve normally should correspond to the most relevant CPC classes for your query text or document, because they
correspond to the highest-ranked documents of the list, i.e. those which have the highest similarity index because they are most similar
to your query.</p>
<p>In cases where no high peaks but just a multitude of low peaks (typically with indices below 350) of the yellow curve are observed,
octimine likely misunderstood your query and needs to be helped along by an appropriate restriction to a suitable technological field or CPC
code. Hence, in such cases a CPC restriction should be considered to help octimine focus on more relevant results. High peaks in the yellow
curve occurring for unexpected or apparently irrelevant CPC codes may also reveal important patent documents which have been misclassified.
To form an opinion about the relevance of each CPC code within its respective hierarchy, please consult the CPC web page, which includes a
link to the current version of the CPC as well as great CPC tutorials: http://www.cooperativepatentclassification.org/index.html</p>
<p>Each document of the results list is represented by its publication number in the third column of the result list and is meant to
represent a different “Simple Patent Family”. Unlike the IPC information, the more detailed CPC patent classification codes/symbols
associated with that document often has not been printed on its front page at the time of publication, and may only be available
electronically via Espacenet, for instance. Moreover, CPC codes may not be available for some categories of patent documents, such as
Japanese patent documents. Finally, CPC codes are attributed to the whole patent family by officers of the search authority of origin of
the initial patent document, the other authorities accepting this attribution without discussing or double-checking it. Note that despite
rigid, well documented classification rules, the practice of attributing CPC codes may slightly differ between officers of different
authorities, and it is not yet clear which consequences this may have in the long run.  However, CPC is mostly IPC-compatible. More
information about CPC, its relationship to IPC and the meaning of the CPC codes can be found here:</p>
<p>http://www.cooperativepatentclassification.org/index.html</p>
<p>Note that the patent classifications of published patent documents are regularly updated and improved, e.g. when the characterization of
the invention changes during examination due to existing prior art and amendments, due to improvements in the classification scheme, or due
to agreements between authorities.</p>
<p>octimine therefore uses the latest set of electronically available CPC patent classification symbols for each document. Please kindly
note that although IPC codes are available for every patent document, CPC information may not be available for some patent
documents/families of the result list.</p>
<p>Therefore, the resulting CPC graph, although it should be technologically more precise and better adapted to modern technology than the
corresponding IPC graph, may nevertheless appear to be more sparsely populated and may omit important documents which do not have a CPC
classification. Moreover, the top CPC chart may omit important, very relevant but sparsely populated CPC classes, because the number of CPC
groups which should be displayed vastly exceeds the maximum of 50 displayable groups.</p>
<p>Click on any bar to filter the list of results to the corresponding CPC code. Hence, only the patent families having an English language
patent document representative actually referencing that particular CPC code will appear in the result list. All other charts will also be
automatically readjusted to reflect this restriction to a CPC Code. Of course, it is best to click on a bar with a very high peak in the
yellow curve to focus on the most relevant documents.</p>
<p>Note: in the rather likely case of more than 50 different CPC codes appearing in the result list, only the top 50 CPC codes cited by the
highest number of documents in the result list will be shown in the graph.</p>
<p>You can click on one bar of the chart to filter the results to that particular CPC code. All other charts will be automatically
readjusted. To reset the filter, click again on the same bar.</p>`;

export const competitorsPortfolioAnalyticsProfile = `<p>
This chart is 3 dimensional and provides the option to click and drag the plot to the desired angle for a better overview.
It consist of 3 axes (x-horizontal, y-vertical, z-depth), which have preselected indicators (Average Value, Average Risk,
Portfolio Technological Broadness). These can be manually changed by selecting the dropdown menu on the right and clicking
on the indicator of your choice. Each bubble represents an applicant and the size of it stands in relation to the number
of patent families found that the specific applicant holds.</p>
<p>To see the names of the applicants, click the box “Show Competitors” and the applicant’s names will appear above each bubble.</p>

<p><b>Indicators:</b></p>
<p>You have a selection of 11 indicators to assign to the axis of your choice.</p>

<p><b>Average Value:</b></p>
Average Value measures the economic value of the focal patent and its patent family. It is computed using octimine proprietary algorithms,
which have been calibrated based on auction pricing, licensing, renewal and other data.

<p><b>Average Recency:</b></p>
Average Recency shows the average time span/year in which the patent families, by a certain applicant were filed in.

<p><b>Average Risk:</b></p>
Average Risk is computed using proprietary algorithms and measures the legal risk incurred by a patent or by members of the patent family
of being involved in post-grant review, opposition, annulment proceedings or litigation.

<p><b>Share of top 10% Value:</b></p>
Share of top 10% Value shows only patent families which rank within the top 10% economic value, therefore might have the highest value.

<p><b>Share of top 10% Recency:</b></p>
Share of top 10% Recency shows the patent families which only appear within the most recent 10%, meaning a timely restriction in order to
 eliminate longer time spans which can refine your analytics.

<p><b>Share of top 10% Risk:</b></p>
Share of top 10% Risk shows only patent families which rank within the top 10% of legal risk, therefore might being involved in post-grant
review, opposition, annulment proceedings or litigation.

<p><b>Average Portfolio Consistency:</b></p>
Average Portfolio Consistency has the index of 0 – 1. Where 0 stands for the lowest level of portfolio quality and 1 for the highest level
of portfolio consistency.

<p><b>Portfolio Technological Broadness:</b></p>
Portfolio Technological Broadness indicates the number of technological areas in which the adjusted patent families were found in.

<p><b>Average Market Coverage:</b></p>
Average Market Coverage shows in how many markets a patent/patent family was found in. As a rule of thumb, the more markets a patent/patent
family can be found in, the higher the relevance might be.

<p><b>Average Citations:</b></p>
Average Citations indicates the average number of citations the found patent families have. (e.g. When the mouse-over field shows Patent
Families: 8 and Average Citations: 40, then the average number of citations to one patent family of this applicant is 40)

<p><b>Average References:</b></p>
Average References indicates the average number of references the found patent families have. (e.g. When the mouse-over field shows Patent
 Families: 8 and Average References: 40, then the average number of references to one patent family of this applicant is 40)

<p><b>Important:</b> Patent value and risk estimations are computed using patent and publication data. Indicators are based on cutting edge
 research but it is recommended to use them carefully as they are just an approximation of the true values.</p>`;

export const citationReferences = `
<p>This graph displays the proportional relationship between the citations & references in the result list below.</p>
`;
export const citationPhases = `
<p>This chart shows in which phases of the patents lifetime references were presented.</p>
<p>115 - citations introduced according to Art 115 EPC</p>
<p>APP - citations introduced by the applicant</p><p>CH2 - citations introduced during the Chapter 2 phase of the PCT</p>
<p>EXA - citations introduced during examination</p><p>ISR - citations from the International Search Report</p>
<p>PRS - Pre grant/ Pre search</p><p>OPP - citations introduced during opposition</p><p>SEA - citations introduced during search</p>
<p>SUP - citations from the Supplementary Search Report</p>
`;
export const citationTypes = `<p>This chart shows the different citation types found in the patents in the result list below.</p>
<p>A - Documents defining the general state of the art (but not belonging to X or Y).</p>
<p>D - Documents cited in the application i.e. already mentioned in the description of the patent application.</p>
<p>E - Potentially conflicting documents – Any patent document bearing a filing or priority date earlier than the filing date of the
application searched but published later than that date, and the content of which would constitute prior art.</p>
<p>L - Documents cited for other reasons (e.g. a document that may throw doubt on a priority claim).</p>
<p>O - Documents which refer to non-written disclosure.</p>
<p>P - Intermediate documents - documents published between the date of filing of the application being examined and the date
of priority claimed.</p>
<p>T - Documents relating to the theory or principle underlying the invention (documents which were published after the filing
date and are not in conflict with the application, but were cited for a better understanding of the invention).</p>
<p>X Particularly relevant documents when taken alone (a claimed invention cannot be considered novel or cannot be considered
      to involve an inventive step).</p>
<p>Y - Particularly relevant documents if combined with one or more other documents of the same category, such a combination
being obvious to a person skilled in the art.</p>
`;
export const citationPlNpl = `
<p>This graph displays the proportional relationship between patent literature & and non-patent literature.</p>
`;

export const monitorVenn = `<p>This graph displays the monitor Method ration in selected monitor interval.</p>`;

export const technologyFields = `<p>Technology Field chart popup.</p>`;
