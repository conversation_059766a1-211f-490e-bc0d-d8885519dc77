import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';
import { TechnologyFieldsComponent } from './technology-fields/technology-fields.component';

@NgModule({
  declarations: [
    TechnologyFieldsComponent,
  ],
  imports: [
    CommonModule,
    HighchartsChartModule,
    NgxSliderModule,
    SharedModule
  ],
  exports: [
    TechnologyFieldsComponent,
  ]
})
export class TechnologyChartsModule {
}
