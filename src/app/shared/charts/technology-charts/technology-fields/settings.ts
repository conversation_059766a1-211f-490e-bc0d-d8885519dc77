import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    inverted: true,
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0]
  },
  colorAxis: {
    reversed: false,
    min: 0,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false,},
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    symbolHeight: 360,
    verticalAlign: 'top',
    style: chartsConfig.textStyles
  },
  title: {
    text: null
  },
  tooltip: {
    style: chartsConfig.tooltipStyles,
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    formatter() {
      const label = this.point.name.charAt(0).toUpperCase() + this.point.name.slice(1);
      const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
      return `<span>Patent families: </span><span style="font-weight: bold;">${this.point.value}</span><br>
        <span>Technological field: </span><span style="font-weight: bold;">${label}</span><br>
        <span>Percent: </span><span style="font-weight: bold;">${percent}%</span>`;
    },
  },
  series: [{
    allowPointSelect: false,
    cursor: 'pointer',
    data: [],
    turboThreshold: 2000,
    states: {
      select: {
        color: '#7CAED5'
      },
      hover: {
        color: '#7CAED5'
      },
    },
    dataLabels: {
      enabled: true,
      useHTML: true,
      style: {
        ...chartsConfig.dataLabelStylesHeatMap,
        ...chartsConfig.dataLabelOverflowStylesHeatMap,
        width: 100
      },
      formatter() {
        const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
        return `<span class="datalabel-name">${this.point.abbr}</span><br/><span>${this.point.value} (${percent}%)</span>`;
      },
    },
    abbreviations: [],
    totalHits: 0,
  }],
  responsive: {
    rules: [
      {
        condition: {
          maxWidth: 600
        },
        chartOptions: {
          series: [{
            dataLabels: {
              enabled: true,
              useHTML: true,
              style: {
                ...chartsConfig.dataLabelStylesHeatMap,
                ...chartsConfig.dataLabelOverflowStylesHeatMap,
                width: 80
              },
              formatter() {
                const label = this.point.abbr.length > 9 ? `${this.point.abbr.slice(0, 6)}...` : this.point.abbr;
                const percent = ((this.point.value / this.point.total) * 100).toFixed(2);
                return `<span>${label}</span><br/><span>${this.point.value}<br/>(${percent}%)</span>`;
              },
            },
          }]
        }
      }
    ]
  },
  yAxis: {reversed: false, visible: false},
  xAxis: {reversed: true, visible: false},
};

export const tooltipText = `This chart provides a distribution of the analyzed results across
<a href="https://intercom-help.eu/dennemeyer-octimine/en/articles/10015-main-technical-fields" target="_blank">35 technology fields</a>.
Each technology field is ordered alphabetically and displays the number of patent families it contains - also indicated by color intensity.<br>
<b>Suggestion:</b><br>
The darkest colors show the technology fields with the most patent families. These categories signify areas where your analyzed results are more focused.`
