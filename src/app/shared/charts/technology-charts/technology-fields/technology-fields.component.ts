import { tooltipText } from './../../basic-charts/applicants/settings';
import { Component, Injector, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import * as settings from './settings';
import * as popupText from '../../popup-information';
import * as Highcharts from 'highcharts';
import addHeatmap from 'highcharts/modules/heatmap';
import _ from 'lodash';
addHeatmap(Highcharts);


@Component({
  selector: 'app-technology-fields',
  templateUrl: './technology-fields.component.html',
  styleUrls: ['./technology-fields.component.scss']
})
export class TechnologyFieldsComponent extends BaseChartComponent implements OnInit, OnDestroy  {

  component = TechnologyFieldsComponent;
  chartName = 'technology_technology_fields';
  title = 'Technology fields';
  chartOptions = settings.chartSetting;

  tooltipText = settings.tooltipText;
  popupText = popupText.technologyFields;

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;

        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  private updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.series[0].setData(this.datasource, true, true, false);
      this.reflowChart();
    }
  }

  selectPoint(event) {
    if (!event.point || !event.point.name) { return; }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }

    const value = event.point.original_name;

    filter.query = `(TECH_FIELDS=${value})`;
    filter.value = value;

    if (index > -1) {
      filters.splice(index, 1, filter);
    } else {
      filters.push(filter);
    }
    this.storeService.setFilters(filters);
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.filter(x => x.value > 0).length == 0;
  }  
}
