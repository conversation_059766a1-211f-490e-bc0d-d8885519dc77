import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, Injector, Input, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import _ from 'lodash';

import ParallelCoordinates from 'highcharts/modules/parallel-coordinates';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

ParallelCoordinates(Highcharts);

declare var $: any;

@Component({
  selector: 'app-analytic-radar',
  templateUrl: './analytic-radar.component.html',
  styleUrls: ['./analytic-radar.component.scss']
})
export class AnalyticRadarComponent extends BaseChartComponent implements OnInit, OnDestroy {
  @ViewChild('chartEle', {read: ElementRef, static: true}) chartEle: ElementRef<HTMLDivElement>;
  @Input() showHeaderBar: boolean = true;
  @Input() autoHeight: boolean;
  @Input() chartClass: string = 'chart-item';
  @Input() customizeOptions = {};
  @Input() spinner = 'assets/images/octimine_blue_spinner.gif';

  component = AnalyticRadarComponent;

  chartName = 'trend_analytic_radar';
  title = 'Octimine analytic radar';
  chartOptions = {};

  tooltipText = `<p>This chart shows how the technology average and the patent universe average behave according to 8 different indicators:</p>
  <ul>
  <li><u>Family size</u> shows the number of patents within one patent family</li>
  <li><u>Value indicator</u> shows the economic impact of the focal patent and its patent family. It is computed using Octimine proprietary algorithms, which have been calibrated based on auction pricing, licensing, renewal, and other data.</li>
  <li><u>Risk indicator</u> shows the legal risk incurred by a patent or by members of the patent family of being involved in the post-grant review, opposition, annulment proceedings, or litigation.</li>
  <li><u>The recency indicator</u> shows the average time span/year in which the patent families, by a certain applicant, were filed.</li>
  <li><u>The claims quality</u> indicator shows the quality of the claims with an index of 0 - 1, where 0 stands for the lowest level of quality and 1 stand for the highest level of quality.</li>
  <li><u>The technological Broadness indicator</u> shows the number of technological areas in which the adjusted patent families were found.</li>
  <li><u>The number of citations</u> shows how many citations were found to the focal patent.</li>
  <li><u>The number of references</u> shows how many references were found to the focal patent.</li>
  </ul>`;

  chartId: string;

  constructor(
    protected injector: Injector,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    super(injector);
    this.chartOptions = _.merge(_.cloneDeep(settings.chartSetting), this.customizeOptions);
  }

  ngOnInit() {
    this.chartOptions = _.merge(_.cloneDeep(settings.chartSetting), this.customizeOptions);
    this.chartId = 'chart-' + (new Date().getTime());
    this.changeDetectorRef.detectChanges();
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        this.datasource = chart.datasource;
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
  }

  ngOnDestroy() {
    this.destroyChart();
    super.unsubscribe();
  }

  private updateChartData() {
    if (this.datasource) {
      this.chartOptions['series'] = [
        {
          id: 'technology',
          name: 'Portfolio average',
          type: 'line',
          color: '#8CD4C8',
          data: this.datasource.technology_average
        },
        {
          id: 'universe',
          name: 'Patent universe average',
          type: 'line',
          color: '#A37FBE',
          data: this.datasource.universe_average
        }
      ];
      this.showChart();
      this.reflowChart();
    }
  }

  private showChart() {
    if (this.chartId && this.chartEle && this.datasource) {
      const self = this;
      if (this.autoHeight && this.chartOptions['chart'].height) {
        delete this.chartOptions['chart'].height
      }
      this.chart = Highcharts.chart(this.chartId, this.chartOptions);
    }
  }

  private destroyChart() {
    if (this.chart) {
      try {
        this.chart.destroy();
        this.chart = null;
      } catch (e) {
        console.warn(e);
      }
    }

    $(`#${this.chartId}`).remove();
  }
}
