import { Component, Injector, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';

import * as Highcharts from 'highcharts';
import sankey from 'highcharts/modules/sankey';
import arcdiagram from 'highcharts/modules/arc-diagram';
import * as settings from './settings';
import { Options } from '@angular-slider/ngx-slider';
import _ from 'lodash';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';
import { finalize } from 'rxjs/operators';

sankey(Highcharts);
arcdiagram(Highcharts);

@Component({
  selector: 'app-applicant-collaboration-network',
  templateUrl: './applicant-collaboration-network.component.html',
  styleUrls: ['./applicant-collaboration-network.component.scss']
})
export class ApplicantCollaborationNetworkComponent extends BaseChartComponent implements OnInit, OnDestroy {
  component = ApplicantCollaborationNetworkComponent;
  chartName = 'trend_applicant_collaboration_network';
  title = 'Applicant collaboration';
  mappedApplicants = null;
  chartOptions = settings.chartSetting;
  quantity = 15;
  sliderOptions: Options = settings.sliderSetting;
  currentPoint = null;
  tooltipText = settings.tooltipText;

  public error: any;
  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    this.adjustLegend = true;
    const charts$ = this.chartsService.charts$.subscribe({
      next: charts => {
        if (!charts || _.isEmpty(charts)) {
          return;
        }

        const chart = charts[this.chartName];
        if (!chart) {
          return;
        }

        if (chart.error) {
          this.error = chart.error;
          return;
        }
        this.error = undefined;

        this.datasource = chart.datasource;
        if (this.datasource.value.length === 0) {
          this.sliderOptions = undefined;
        }
        this.mappedApplicants = charts['mappedApplicants'];
        this.updateChartData();
      }
    });
    this.subscriptions.add(charts$);

    super.updateChartOptions();
    super.subscribeChartEvents();
    super.notifyGetAllApplicantAliases();
  }

  updateChartData() {
    if (this.chart && this.chart.series && this.datasource) {
      this.chart.xAxis[0].setCategories(this.datasource.categories);
      this.chart.yAxis[0].setCategories(this.datasource.categories);
      const cats = this.datasource.categories.slice(0, this.quantity);
      const value = this.datasource.value.filter(o => cats.includes(o.xApplicant) && cats.includes(o.yApplicant));
      this.chart.series[0].setData(value, true, true, false);
      this.reflowChart();
    }
  }

  ngOnDestroy() {
    super.unsubscribe();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => {
      this.chart = event;
      this.updateChartData();
    });
  }

  selectPoint(event) {
    if (!event.point) {
      return;
    }

    const filters = [...this.storeService.filters];
    const index = filters.findIndex((item) => item.chart === this.chartName);
    let filter: any;

    if (index > -1) {
      filter = filters[index];
    } else {
      filter = {
        chart: this.chartName,
        title: this.title,
        type: 'chart'
      };
    }
    const apps = [
      ...this.applicantsAliasesService.getOriginalApplicants([event.point.xApplicant]),
      ...this.applicantsAliasesService.getOriginalApplicants([event.point.yApplicant])
    ];

    if (event.point) {
      filter.query = `(APPLICANTS=(${apps.join(' AND ')}))`;
      filter.value = `${event.point.xApplicant} AND ${event.point.yApplicant}`;
      if (index > -1) {
        filters.splice(index, 1, filter);
      } else {
        filters.push(filter);
      }
    }
    this.storeService.setFilters(filters);
  }

  // Slider
  onUserChangeEnd(): void {
    this.updateChart();
  }

  private updateChart(): void {
    this.isCalculating = true;
    const payload = {
      charts: [this.chartName],
      search_filters: {}
    };

    const free_text_query = this.storeService.filters
      .filter(filter => filter.type === 'chart')
      .map(filter => filter.query)
      .join(' AND ');
    if (free_text_query) {
      payload.search_filters['free_text_query'] = free_text_query;
    }
    if (this.quantity > 0) {
      payload['parameters'] = { trend_applicant_collaboration_network_quantity: this.quantity };
    }
    const calculateToSingleChart$ = this.chartsService.calculateToSingleChart(payload, this.storeService.searchHash)
      .pipe(
        finalize(() => this.isCalculating = false)
      )
      .subscribe({
        next: ({ charts }) => {
          this.datasource = charts[this.chartName].datasource;
          this.updateChartData();
        },
        error: (err) => { console.log(err); }
      });
    this.subscriptions.add(calculateToSingleChart$);
  }

  get isEmpty() {
    return !this.isCalculating && this.datasource && this.datasource.value.length < 2;
  }  
}
