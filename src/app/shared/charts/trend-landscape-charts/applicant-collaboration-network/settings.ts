import { chartsConfig } from './../../config'

export const chartSetting = {
  chart: {
    type: 'heatmap',
    backgroundColor: chartsConfig.bgColor,
    spacing: [0, 0, 0, 0],
  },
  colorAxis: {
    reversed: false,
    minColor: chartsConfig.colorGradient.min,
    maxColor: chartsConfig.colorGradient.max,
    labels: {
      style: chartsConfig.textStyles
    }
  },
  colors: chartsConfig.colorPalette,
  credits: chartsConfig.credits,
  exporting: {enabled: false,},
  legend: {
    reversed: true,
    align: 'right',
    layout: 'vertical',
    symbolHeight: 360,
    verticalAlign: 'top',
    style: chartsConfig.textStyles
  },
  title: { text: null },
  series: [{
    name: null,
    borderWidth: 0,
    drilldown: true,
    cursor: 'pointer',
    dataLabels: {
      enabled: false,
      style: chartsConfig.dataLabelStyles
    },
    data: [],
    states: {
      select: {
        color: chartsConfig.colorGradient.hover
      },
      hover: {
        color: chartsConfig.colorGradient.hover
      },
    }
  }],
  tooltip: {
    useHTML: true,
    backgroundColor: chartsConfig.bgColor,
    padding: 1,
    style: {...chartsConfig.tooltipStyles},
    formatter: function () {
      return `<div>Applicants: <strong> ${this.point.xApplicant}, ${this.point.yApplicant}</strong><br/>Collaborations:<strong> ${this.point.value}</strong></div>`;
    }
  },
  xAxis: {
    categories: [],
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    },
    title: {text: null,},
  },
  yAxis: {
    categories: [],
    title: {text: null,},
    labels: {
      rotation: -45,
      style: chartsConfig.textStyles
    }
  }
};

export const MIN_VALUE_SLIDER = 1;
export const MAX_VALUE_SLIDER = 50;

export const sliderSetting = {
  floor: MIN_VALUE_SLIDER,
  ceil: MAX_VALUE_SLIDER,
  showSelectionBar: true,
  name: 'Number of  applicants',
  getSelectionBarColor: (value: number): string => {
    return '#389C87';
  },
};

export const tooltipText = `This chart displays ongoing collaborations between applicants with respect to the analyzed patents.<br>
Both axes list the main applicants. The box at the intersection of two applicants represents the number of collaborations, defined as the number of patents where both companies are present as applicants. The color deepens with an increasing number of collaborations.<br>
<b>Suggestion:</b><br>
Utilize this chart to gain insights into current collaborations and identify potential partners for mutual ventures.`
