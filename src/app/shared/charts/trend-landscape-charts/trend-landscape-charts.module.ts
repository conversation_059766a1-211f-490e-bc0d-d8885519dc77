import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

import { SharedModule } from '@shared/shared.module';

import { AnalyticRadarComponent } from './analytic-radar/analytic-radar.component';
import {
  ApplicantCollaborationNetworkComponent
} from './applicant-collaboration-network/applicant-collaboration-network.component';

@NgModule({
  declarations: [
    AnalyticRadarComponent,
    ApplicantCollaborationNetworkComponent
  ],
    imports: [
        CommonModule,
        HighchartsChartModule,
        NgxSliderModule,
    SharedModule
    ],
  exports: [
    AnalyticRadarComponent,
    ApplicantCollaborationNetworkComponent,
  ]
})
export class TrendLandscapeChartsModule {
}
