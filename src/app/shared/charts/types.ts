import { featureType } from "@core/services/user/types";

export interface Category {
  category: string;
  title: string;
  newCategory?: boolean;
  excludeDashboard?: boolean;
  charts: Array<Chart>;
}

export interface Chart {
  name: string;
  title: string;
  size?: 'sm' | 'lg';
  height?: string;
  mustHaveSimilarity?: boolean;
  hide?: boolean;
  features?: featureType[];
  disableFilter?: boolean;
}
