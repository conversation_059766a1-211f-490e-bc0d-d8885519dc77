import _ from 'lodash';
import { TextUtil } from '@core/utils';
import { Category } from './types';


export const abbreviations = [
  {
    full: 'Electrical Machinery, Apparatus, Energy',
    abbr: 'Electr. Mach.'
  },
  {
    full: 'Audio-Visual Technology',
    abbr: 'Audio-visual Tech.'
  },
  {
    full: 'Telecommunications',
    abbr: 'Telecomm.'
  },
  {
    full: 'Digital Communication',
    abbr: 'Digital Communic.'
  },
  {
    full: 'Basic Communication Processes',
    abbr: 'Basic Communic.'
  },
  {
    full: 'Computer Technology',
    abbr: 'Comp. Tech.'
  },
  {
    full: 'IT Methods for Management',
    abbr: 'IT Methods Managem.'
  },
  {
    full: 'Semiconductors',
    abbr: 'Semiconductor'
  },
  {
    full: 'Optics',
    abbr: 'Optics'
  },
  {
    full: 'Measurement',
    abbr: 'Measurement'
  },
  {
    full: 'Analysis of Biological Materials',
    abbr: 'Anal. Biol. Mat.'
  },
  {
    full: 'Control',
    abbr: 'Control'
  },
  {
    full: 'Medical Technology',
    abbr: 'Med. Tech.'
  },
  {
    full: 'Organic Fine Chemistry',
    abbr: 'Org. Fine Chem.'
  },
  {
    full: 'Biotechnology',
    abbr: 'Biotech'
  },
  {
    full: 'Pharmaceuticals',
    abbr: 'Pharmaceut.'
  },
  {
    full: 'Macromolecular Chemistry, Polymers',
    abbr: 'Macromolecular. Chem.'
  },
  {
    full: 'Food Chemistry',
    abbr: 'Food Chem.'
  },
  {
    full: 'Basic Materials Chemistry',
    abbr: 'Basic Mat. Chem.'
  },
  {
    full: 'Materials, Metallurgy',
    abbr: 'Mat., Metallurgy'
  },
  {
    full: 'Surface Technology, Coating',
    abbr: 'Surface Tech., Coat.'
  },
  {
    full: 'Micro-Structural and Nano-Technology',
    abbr: 'Micro, Nanotech.'
  },
  {
    full: 'Chemical Engineering',
    abbr: 'Chem. Engin.'
  },
  {
    full: 'Environmental Technology',
    abbr: 'Environ. Tech.'
  },
  {
    full: 'Handling',
    abbr: 'Handling'
  },
  {
    full: 'Machine Tools',
    abbr: 'Machine Tools'
  },
  {
    full: 'Engines, Pumps, Turbines',
    abbr: 'Engine, Pumps...'
  },
  {
    full: 'Textile and Paper Machines',
    abbr: 'Textile, Paper Mach.'
  },
  {
    full: 'Other Special Machines',
    abbr: 'Other Spec. Machines'
  },
  {
    full: 'Thermal Processes and Apparatus',
    abbr: 'Thermal'
  },
  {
    full: 'Mechanical Elements',
    abbr: 'Mech. Elem.'
  },
  {
    full: 'Transport',
    abbr: 'Transport'
  },
  {
    full: 'Furniture, Games',
    abbr: 'Furniture, Games'
  },
  {
    full: 'Other Consumer Goods',
    abbr: 'Other Cons. Goods'
  },
  {
    full: 'Civil Engineering',
    abbr: 'Civil Engin.'
  },
];

export function generateCpcIpcDescription(descriptions: string[], defaultValue: string = null,
                                          defaultWidth: number = 350): string {
  if (!descriptions?.length) {
    return defaultValue;
  }

  if (descriptions.length === 1) {
    return descriptions[0];
  }

  const fontName = '"Lucida Grande", "Lucida Sans Unicode", Arial, Helvetica, sans-serif';
  const fontSize = '12px/24px';
  const fontWeight = 400;

  const width = Math.min(Math.max(
    ...descriptions.map((o) => TextUtil.calculateTextWidth(o, fontName, fontSize, fontWeight))
  ), defaultWidth);
  return _.clone(descriptions).reverse().reduce((p, c, i) => {
    const padding = i === descriptions.length - 1 ? 20 : 10;
    const widthStyle = i === descriptions.length - 1 ? `width: ${width}px;` : '';
    const style = `padding-inline-start: ${padding}px; list-style-type: square !important;
      white-space: normal !important; word-break: normal !important; ${widthStyle}`;
    c = c.replaceAll("'", "&apos;");
    const splitC = c.split(' - ');
    const code = splitC[0];
    const description = splitC[1];

    return `<ul style="${style}"><li style="white-space: normal !important; word-break: normal !important;"><span>${code}</span> - ${description}</li>${p}</ul>`;
  }, '');
}

export function openTooltipPopup(title, content) {
  const left = (screen.width / 2) - (600 / 2);
  const top = (screen.height / 2) - (600 / 2);
  const windowTip = window.open('', '', `scrollbars=yes,resizable=yes,top=${top},left=${left},width=600,height=600`);
  const markup = `
      <div style="font-family: Arial; padding: 20px;">
        <h3>${title}</h3>
        <div style="font-size: 1.1em; background-color: #FFFBE8; text-align: justify;">
          ${content}
        </div>
      </div>`;
  windowTip.document.write(markup);
  windowTip.document.title = title;
}

export const categories: Array<Category> = [
  {
    category: 'basic',
    title: 'Basic',
    newCategory: false,
    excludeDashboard: true,
    charts: [{
      name: 'monitor_venn_diagram',
      title: 'Results per search method',
      features: ['monitor', 'monitorPublication'],
      size: 'sm'
    }, {
      name: 'basic_top_applicants',
      title: 'Applicants distribution',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_technological_fields',
      title: 'Technology areas and fields',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_authorities',
      title: 'Patent families per authority',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'authorities_authorities_map',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      title: 'Authorities map',
      size: 'sm'
    }, {
      name: 'basic_technology_timeline',
      title: 'Patent families over time',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_similarity_curve',
      title: 'Result similarity',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm',
      mustHaveSimilarity: true
    }]
  }, {
    category: 'applicant',
    title: 'Applicants & Owners',
    newCategory: false,
    charts: [{
      name: 'basic_top_applicants',
      title: 'Applicants distribution',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    },
    {
      name: 'basic_top_owners',
      title: 'Owners distribution',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    },
    {
      name: 'applicants_main_applicants',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      title: 'Main applicants',
      size: 'sm'
    }, 
    {
      name: 'owners_main_owners',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      title: 'Main owners',
      size: 'sm'
    },
    {
      name: 'applicant_number_over_time',
      title: 'Number of applicants over time',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'competitors_over_time',
      title: 'Applicants over time',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'competitors_ipc_profile',
      title: 'Applicants per IPC code',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'competitors_cpc_profile',
      title: 'Applicants per CPC code',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'competitors_portfolio_analytics_profile',
      title: 'Applicants analytics',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'trend_applicant_collaboration_network',
      title: 'Applicant collaboration',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }],
  }, {
    category: 'authority',
    title: 'Authorities',
    newCategory: false,
    charts: [{
      name: 'authorities_authorities_map',
      title: 'Authorities map',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_authorities',
      title: 'Patent families per authority',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'authorities_main_authorities',
      title: 'Main authorities',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_top_applicants_in_authority',
      title: 'Applicants per authority',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'basic_authorities_timeline',
      title: 'Authorities over time',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
      size: 'sm'
    }, {
      name: 'analytics_patent_family',
      title: 'Number of authorities per patent family',
      features: ['semantic', 'collection', 'landscape', 'monitor', 'monitor_legal_status', 'boolean'],
      size: 'sm'
    }],
  }, {
    category: 'technology',
    title: 'Technologies',
    newCategory: false,
    charts: [
      {
        name: 'technology_technology_fields',
        title: 'Technology fields',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'basic_technological_fields',
        title: 'Technology areas and fields',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'basic_technology_trend',
        title: 'Technology fields over time',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'competitors_tech_profile',
        title: 'Applicants per technology fields',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'analytics_patent_broadness',
        title: 'Technology fields per patent families',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
    ],
  }, {
    category: 'classification',
    title: 'Classification',
    newCategory: false,
    charts: [
      {
        name: 'classification_main_ipc_codes',
        title: 'Main IPC codes',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_main_cpc_codes',
        title: 'Main CPC codes',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_ipc_time',
        title: 'IPC codes over time',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_cpc_time',
        title: 'CPC codes over time',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_top_ipc',
        title: 'IPC codes and similarity',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_top_cpc',
        title: 'CPC codes and similarity',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_top_ipc_combination',
        title: 'Top IPC combinations',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
      {
        name: 'classification_top_cpc_combination',
        title: 'Top CPC combinations',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm'
      },
    ]
  }, {
    category: 'sustainability',
    title: 'Sustainability',
    newCategory: false,
    charts: [{
      name: 'green_categories',
      title: 'Green patents distribution',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication', 'greenReport'],
      size: 'sm'
    }, {
      name: 'green_technologies_landscape',
      title: 'Green patents categories',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication', 'greenReport'],
      size: 'sm'
    }, {
      name: 'green_families_timeline',
      title: 'Green patents over time',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication', 'greenReport'],
      size: 'sm'
    }, {
      name: 'green_ranking',
      title: 'Green ranking',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication', 'greenReport'],
      size: 'sm',
      disableFilter: true
    },],
  }, {
    category: 'analytics',
    title: 'Analytics',
    newCategory: false,
    charts: [{
      name: 'analytics_citations_count',
      title: 'Number of citations',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean'],
      size: 'sm'
    }, {
      name: 'analytics_references_count',
      title: 'Number of references',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean'],
      size: 'sm'
    }, {
      name: 'analytics_patent_value',
      title: 'Patent value',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean'],
      size: 'sm'
    }, {
      name: 'analytics_patent_risk',
      title: 'Patent risk',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean'],
      size: 'sm'
    }, {
      name: 'analytics_patent_value_over_risk',
      title: 'Patent value over risk',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean'],
      size: 'sm'
    }, {
      name: 'analytics_recency_indication',
      title: 'Patent recency',
      features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'monitor_legal_status', 'boolean'],
      size: 'sm'
    }, {
      name: 'rich_neighborhood',
      title: 'Patents neighborhood value',
      features: ['landscape'],
      size: 'sm'
    }],
  }, {
    category: 'others',
    title: 'Others',
    newCategory: false,
    charts: [{
        name: 'competitors_applicant_legal_status_distribution',
        title: 'Applicant legal status distribution',
        features: ['landscape'],
        size: 'lg',
        height: '100%'
      }, {
        name: 'monitor_venn_diagram',
        title: 'Results per search method',
        features: ['monitor', 'monitorPublication'],
        size: 'sm'
      }, {
        name: 'basic_technology_timeline',
        title: 'Patent families over time',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
        size: 'sm'
      }, {
        name: 'analytics_legal_status',
        title: 'Patent family legal status',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'boolean', 'booleanPublication'],
        size: 'sm'
      }, {
        name: 'basic_similarity_curve',
        title: 'Result similarity',
        features: ['semantic', 'collection', 'collectionPublication', 'landscape', 'monitor', 'monitorPublication', 'boolean', 'booleanPublication'],
        size: 'sm',
        mustHaveSimilarity: true
      }],
  }
];

export const manualCalculationCharts = ['rich_neighborhood'];
