<div class="modal-header" *ngIf="showHeader">
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Close')"></button>
</div>

<div class="modal-body p-0">
  <div class="w-100 pt-0 pe-4 pb-4 ps-4 m-0 atc-container d-flex flex-column justify-content-start align-items-stretch">
    <app-alert type="success" [message]="saveSearchSuccessMsg" *ngIf="saveSearchSuccessMsg"></app-alert>
    <app-alert type="danger" [message]="saveSearchErrors" *ngIf="saveSearchErrors"></app-alert>

    <div class="atc-title w-100" *ngIf="!saveSearchSuccessMsg">
      {{customTitle ? customTitle: title}}
    </div>

    <div class="d-flex justify-content-between align-items-center w-100">
      <div class="input-group d-flex justify-content-between align-items-center col-6 p-0" *ngIf="canCreateList">
        <input type="text" class="form-control flex-fill atc-collection-name rounded"
               placeholder="Enter a name for a new list"
               [(ngModel)]="collectionName" (keyup.enter)="saveCollection()" [hidden]="isSaving">
        <img *ngIf="isSaving" class="clt-loading" src="assets/images/octimine_blue_spinner.gif" />
        <a class="ctl-ss-close ms-3 atc-save-collection" href="javascript:void(0)" (click)="saveCollection()" ngbTooltip="Save the list" [hidden]="isSaving">
          <i class="fas fa-save"></i>
        </a>
      </div>
      <div class="input-group d-flex col-4 p-0 align-items-center" [ngClass]="canCreateList ? 'justify-content-end' : 'justify-content-start'">
        <div class="d-flex justify-content-between align-items-stretch">
          <input type="text" class="search-input form-control border-end-0 flex-fill" placeholder="Search for existing list"
                 [(ngModel)]="filter" (ngModelChange)="onFilterChanged()"
                 *ngIf="collections.length > 0 || folders.length > 0 || filter"/>
          <button ngbTooltip="Search for a term" tooltipClass="white-tooltip" container="body" class="btn border-start-0"
                  (click)="onFilterChanged()" *ngIf="collections.length > 0 || folders.length > 0 || filter">
            <i class="fa fa-search icon"></i>
          </button>
        </div>
        <img *ngIf="isLoadingFolders || isLoadingCollections" class="clt-loading ms-2" src="assets/images/octimine_blue_spinner.gif" />
      </div>
    </div>

    <a href="javascript:void(0)" (click)="onNewFolderClicked()" ngbTooltip="Add a new folder" class="w-25 ctl-ss-close atc-add-folder" *ngIf="canCreateFolder">
      <i class="fas fa-folder-plus"></i> Create new folder
    </a>

    <div class="mt-3 atc-collections w-100" >
      <app-alert type="danger" *ngIf="!(isLoadingFolders || isLoadingCollections) && collections.length === 0 && folders.length === 0 && sharedCollections?.length === 0 && sharedFolders?.length === 0"
                 [message]="noListMessage()"></app-alert>

      <table class="table table-condensed" *ngIf="sharedCollections?.length > 0 || sharedFolders?.length > 0 || collections.length > 0 || folders.length > 0 || userService.canUseWorkflowFeature()">
        <ng-container *ngIf="collections.length > 0 || folders.length > 0">
          <thead >
            <th class="content-heading-h6">My collections</th>
          </thead>
          <tbody #collectionsEle class="collection-container" (scroll)="onCollectionsScrolled($event, collectionsEle)">
            <ng-container [ngTemplateOutlet]="folderRowsTemplate"
                          [ngTemplateOutletContext]="{rowFolders: folders, level: 0}">
            </ng-container>
            <ng-container [ngTemplateOutlet]="collectionRowsTemplate"
                          [ngTemplateOutletContext]="{rowCollections: collections, folder: null, level: 0}">
            </ng-container>

            <tr *ngIf="canLoadMore && !isLoadingCollections">
              <td class="clt-collection-row border-0 p-0 d-flex justify-content-center align-items-center">
                <button class="btn btn-link btn-link mt-3" (click)="onLoadMoreClicked()">Load more collections</button>
              </td>
            </tr>
          </tbody>
        </ng-container>
        <ng-container *ngIf="sharedCollections?.length > 0 || sharedFolders?.length > 0">
          <thead >
            <th class="content-heading-h6" [class.p-t-spacing-big]="collections.length > 0 || folders.length > 0">Shared with me</th>
          </thead>
          <tbody #sharedCollectionsEle class="collection-container" (scroll)="onSharedCollectionsScrolled($event, sharedCollectionsEle)">
            
            <ng-container *ngIf="sharedFolders" [ngTemplateOutlet]="folderRowsTemplate"
                          [ngTemplateOutletContext]="{rowFolders: sharedFolders, isShared: true, level: 0}">
            </ng-container>

            <ng-container *ngIf="sharedCollections" [ngTemplateOutlet]="collectionRowsTemplate"
                          [ngTemplateOutletContext]="{rowCollections: sharedCollections, folder: null, level: 0}">
            </ng-container>

            <tr *ngIf="canLoadMoreShared && !isLoadingCollections">
              <td class="clt-collection-row border-0 p-0 d-flex justify-content-center align-items-center">
                <button class="btn btn-link btn-link mt-3" (click)="onLoadMoreSharedClicked()">Load more shared collections</button>
              </td>
            </tr>
          </tbody>

        </ng-container>
      </table>
    </div>
  </div>
</div>

<ng-template #collectionRowsTemplate let-rowCollections="rowCollections" let-folder="folder" let-level="level">
  <tr *ngFor="let c of rowCollections" [class.disabled]="collectionsStoreService?.collection?.id === c.id">
    <td class="clt-collection-row border-0 p-0 d-flex justify-content-between align-items-start level-{{level}}"
        (click)="saveSearchToCollection(c, folder)"
        [ngClass]="{'disabled': collectionsStoreService?.collection?.id === c.id, 'ctl-folder-collections': level, 'ctl-selected-list': selectedCollection?.id === c.id}" [attr.colspan]="folder ? 2 : 1">
      <div class="me-2">
        <img *ngIf="currentSavingCollectionId === c.id" src="assets/images/octimine_blue_spinner.gif" class="loading-spinner"/>
        <i class="far fa-fw fa-copy" *ngIf="currentSavingCollectionId !== c.id"></i>
      </div>
      <div class="flex-fill m-0 p-0">
        <span [ngbTooltip]="customTitle">
          {{c.name}} ({{c.results_count + (c.results_count === 1 ? ' patent' : ' patents')}})
        </span>
      </div>
    </td>
  </tr>
</ng-template>
<ng-template #folderRowsTemplate let-rowFolders="rowFolders" let-isShared="isShared" let-level="level">
  <ng-container  *ngFor="let f of rowFolders">
    <tr>
      <td class="clt-folder-row border-0 p-0 d-flex justify-content-between align-items-start level-{{level}}"
          (click)="onFolderClicked(f, isShared)"
          [ngClass]="{active: isFolderActive(f)}">
        <div class="me-2">
          <img *ngIf="loadingFolders[f.id]" src="assets/images/octimine_blue_spinner.gif" class="loading-spinner"/>
          <i *ngIf="!loadingFolders[f.id]" [ngClass]="openFolders[f.id] ? 'fa-folder-open' : 'fa-folder'"
            class="fas"></i>
        </div>
        <div class="flex-fill m-0 p-0">
          <span [ngbTooltip]="customTitle">
            {{f.name}} <span  *ngIf="!isShared">({{f.collections_count + (f.collections_count === 1 ? ' list' : ' lists')}})</span>
          </span>
        </div>
      </td>
    </tr>

    <ng-container *ngIf="openFolders[f.id]">
      <ng-container *ngIf="openFolders[f.id].folders.length > 0" [ngTemplateOutlet]="folderRowsTemplate"
                    [ngTemplateOutletContext]="{rowFolders: openFolders[f.id].folders, isShared: isShared, level: level+1}"></ng-container>
      <ng-container *ngIf="openFolders[f.id].collections.length > 0" [ngTemplateOutlet]="collectionRowsTemplate"
                    [ngTemplateOutletContext]="{rowCollections: openFolders[f.id].collections, folder: f, level: level+1}">
      </ng-container>
      <ng-container *ngIf="openFolders[f.id].pagination && openFolders[f.id].pagination.current_page < openFolders[f.id].pagination.last_page"
                    [ngTemplateOutlet]="loadMoreTemplate" [ngTemplateOutletContext]="{folderID: f.id, isShared: isShared, level: level+1}">
      </ng-container>
    </ng-container>
  </ng-container>
</ng-template>
<ng-template #loadMoreTemplate let-folderID="folderID" let-isShared="isShared" let-level="level">
  <tr *ngIf="folderID">
    <td class="clt-collection-row border-0 p-0 d-flex justify-content-start align-items-center level-{{level}}">
      <a class="button-main-tertiary button-small" href="javascript:void(0)" (click)="onNestedLoadMoreClicked(isShared, folderID)">{{isShared? 'Load more shared collections':'Load more collections'}}</a>
    </td>
  </tr>
</ng-template>
