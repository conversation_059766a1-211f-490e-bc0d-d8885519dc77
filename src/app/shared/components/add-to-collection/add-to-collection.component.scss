@use 'scss/layout2021/variables' as variables;

.atc-container {
  max-height: 75vh;
  overflow: auto;

  .atc-title {
    color: variables.$color-text-03;
    font-family: variables.$font-open-sans-bold;
    font-size: 1rem;
    margin: 0 0 20px 0;
  }

  .clt-collection-row {
    cursor: pointer;
    color: variables.$brand-green;
    height: auto;
    padding-top: 6px;
    font-size: 0.875rem;
    line-height: 1.5rem;

    .loading-spinner {
      height: 17.5px;
      width: 17.5px;
    }

    &:hover {
      color: variables.$brand-green-pressed !important;
    }
    &.ctl-selected-list{
      color: variables.$brand-green-pressed !important;
      background-color: variables.$brand-gray;
    }
    &.disabled{
      cursor: not-allowed;
      pointer-events: none !important;
      opacity: .7;
      color: variables.$brand-gray;
    }
  }

  .clt-folder-row {
    cursor: pointer;
    color: variables.$brand-green;
    height: auto;
    padding-top: 6px;
    font-size: 0.875rem;
    line-height: 1.5rem;

    &:hover,
    &.active {
      color: variables.$brand-green-pressed !important;

      .fa-folder-open {
        color: variables.$brand-green-pressed !important;
      }
    }

    .loading-spinner {
      height: 17.5px;
      width: 17.5px;
    }
  }

  @for $i from 1 through 20{
    .level-#{$i}{
      padding-left: 12px * $i !important;
    }
  }

  .atc-collections {
    overflow: auto;
    max-height: 50vh;

    > table {
      tr {
        cursor: pointer;
        &.disabled{
          cursor: not-allowed;
        }
      }
    }
  }
  .collection-container{
    overflow: auto;
    max-height: 20vh;
    display: block;
  }

  .clt-loading {
    height: 30px;
    width: 30px;
  }

  @import 'scss/components/input-group';

  .input-group {
    .form-control.search-input {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
  }
}

a.disable{
  pointer-events: none;
  cursor: progress;
}
.atc-save-collection{
  font-size: 2rem;
}
