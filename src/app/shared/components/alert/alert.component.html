<div *ngIf="version == 'current' else figma">
  <div class="alert alert-{{type}} {{classes}}" role="alert" *ngIf="headline || message && message.length > 0">
    <a href="javascript:void(0)" class="close" (click)="onCloseClicked()" [hidden]="hideCloseBtn">
      <span aria-hidden="true">&times;</span>
    </a>
    <h4 class="alert-heading" *ngIf="mainTitle">{{mainTitle}}</h4>
    {{headline}} <a class="alert-title-link" *ngIf="hideText" [class.expandable-btn]="expandable" href="javascript:void(0)" (click)="hideText = false">[show]</a>
    <div *ngIf="!hideText || expandable" [class.expandable-text]="expandable && hideText">
      <p *ngIf="!isMessageArray else alertArray" [innerHTML]="message" class="p-0 m-0"></p>
      <ng-template #alertArray>
        <ul>
          <li *ngFor="let msg of message" [innerHTML]="msg"></li>
        </ul>
      </ng-template>
    </div>
  </div>
</div>

<ng-template #figma>
  <div class="alert-{{type}}-2024 mt-2" *ngIf="headline || message">
    <div class="d-flex gap-spacing-sm">
      <span class="icon">
        <i class="fa-solid fa-circle-info" *ngIf="type == 'info' || type == 'brand'"></i>
        <i class="fa-solid fa-triangle-exclamation" *ngIf="type == 'warning' || type == 'danger'"></i>
      </span>
      <div class="w-100 d-flex flex-column justify-content-center">
        <span class="content-heading-h6 m-b-spacing-x-s" *ngIf="headline">{{headline}}</span>
        <span class="content-body-small content-color-secondary">{{message}}</span>
      </div>
      <div [class.align-self-center]="!headline">
        <span class="close" (click)="onCloseClicked()" [hidden]="hideCloseBtn"><i class="fa-regular fa-xmark"></i></span>
      </div>
    </div>
  </div>
</ng-template>