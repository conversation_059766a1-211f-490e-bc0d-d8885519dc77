
@import 'scss/layout2021/variables';
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";
.alert-title-link {
  font-weight: normal !important;
  color: inherit;
}
.expandable{
  &-text{
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    -webkit-line-clamp: 1; /* start showing ellipsis when 3rd line is reached */
    white-space: pre-wrap;
    width: calc(100% - 75px);    
  }
  &-btn {
    right: 35px;
    margin-top: -3px;
    position: absolute;
  }
}
