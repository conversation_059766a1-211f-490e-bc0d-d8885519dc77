import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-alert',
  templateUrl: './alert.component.html',
  styleUrls: ['./alert.component.scss']
})
export class AlertComponent implements OnInit, OnChanges {

  @Input() mainTitle: string;
  @Input() headline: string;
  @Input() message: string | Array<string>;
  @Input() type: 'info' | 'success' | 'danger' | 'warning';
  @Input() version: 'figma' | 'current' = 'current';
  @Input() hideCloseBtn: boolean;
  @Input() expandable: boolean;
  @Input() hideText: boolean;
  @Input() classes: string = '';
  @Output() closed = new EventEmitter<void>();

  isMessageArray: boolean;

  constructor(
    public activeModal: NgbActiveModal,) {
  }

  ngOnInit(): void {
    this.setValues(this.message);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.message?.currentValue) {
      this.setValues(changes.message?.currentValue);
    }
  }

  onCloseClicked() {
    this.headline = null;
    this.message = null;
    this.closed.emit();
    this.activeModal.close();
  }

  private setValues(msg: string | string[]) {
    this.isMessageArray = Array.isArray(msg) && msg.length > 1;
    if (msg.length === 1) {
      this.message = msg[0];
    }
  }
}
