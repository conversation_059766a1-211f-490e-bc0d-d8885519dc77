<ng-container *ngIf="!hasStatus else authoritiesStatus">
  <div class="d-flex justify-content-center badge-{{name | lowercase}} {{ cssClass }}"
        [class.badge-small]="size == 'sm'" [ngbTooltip]="tooltip" tooltipClass="white-tooltip" container="body">
      {{ name | titlecase }}
  </div>
</ng-container>

<ng-template #authoritiesStatus>
  <div class="d-flex justify-content-center badge-{{name | lowercase}} {{ cssClass }}"

       [class.badge-small]="size == 'sm'" appSelectableTooltip [selectableTooltipPopover]="legalStatus"
       [parentPopover]="parentPopover" classPopover="authority-legal-status-popover"
       #legalStatus="ngbPopover" [ngbPopover]="tooltipAuthorities" triggers="manual" container="body"
       popoverClass="white-popover authority-legal-status-popover" [autoClose]="'outside'" (hidden)="onPopoverHidden()">
    {{ name  | titlecase }} {{ name.toLowerCase() == 'granted' && inForce.length ? '(' + inForce.length + ')' : '' }}
  </div>
</ng-template>

<ng-template #tooltipAuthorities>
    <ng-container [ngTemplateOutlet]="generateAuthorityList"
        [ngTemplateOutletContext]="{ context: {title: 'In force', authorities: inForce, showAll: showAllInForce, badgeClass: 'badge-granted', borderBottom: notInForce.length > 0, paddingTop: false}}"></ng-container>
    <ng-container [ngTemplateOutlet]="generateAuthorityList"
        [ngTemplateOutletContext]="{ context: {title: 'Not in force', authorities: notInForce, showAll: showAllNotInForce, badgeClass: 'badge-dead', borderBottom: false, paddingTop: inForce.length > 0}}"></ng-container>
</ng-template>

<ng-template #generateAuthorityList let-context="context">
    <div class="text-left p-spacing-md" [class.border-bottom]="context.borderBottom" [class.p-t-spacing-md]="context.paddingTop" *ngIf="context.authorities?.length">
        <div class="{{context.badgeClass}} title content-label-small text-capitalize">
            {{context.title}} ({{context.authorities.length}})
        </div>
        <span class="content-body-xsmall content-color-tertiary m-t-spacing-sm d-block">The patent is {{context.title | lowercase}} in the following authorities</span>
        <div class="d-flex flex-wrap m-t-spacing-big overflow-hidden">
            <span *ngFor="let authority of context.authorities; let i = index"
            class="authority content-label-small m-r-spacing-md m-b-spacing-xx-s" [class.d-none]="!context.showAll && i >= 8">
                <span [ngClass]="getFlagIcon(authority.authority)" class="m-r-spacing-xx-s"></span>{{authority.authority}}
            </span>
        </div>
        <span class="button-main-link content-label-small" (click)="toggleSeeAll(context.title)"
            *ngIf="context.authorities.length > 8">{{ context.showAll ? 'See less' : 'See all'}}</span>
    </div>
</ng-template>

