@import 'scss/figma2023/variables';

.badge- {
    &granted, &dead {
        display: inline-block;
    }
    &granted.title:hover {
        background-color: $colours-background-bg-success !important;
    }
    &dead.title:hover {
        background-color: $colours-background-bg-danger !important;
    }
}

.border-bottom {
    border-bottom: 1px solid $colours-border-subtle;
}

.authority {
    width: 66px;
    border-radius: $radius-sm;
    padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-sm;
    background-color: $colours-background-bg-secondary;
    cursor: default;
}

::ng-deep .authority-legal-status-popover {
    max-width: 336px; 
}