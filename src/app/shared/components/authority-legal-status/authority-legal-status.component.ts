import { Component, Input } from '@angular/core';
import { COUNTRY_FLAG, FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-authority-legal-status',
  templateUrl: './authority-legal-status.component.html',
  styleUrls: ['./authority-legal-status.component.scss']
})
export class AuthorityLegalStatusComponent {

  @Input() tooltip: string;
  @Input() name: string;
  @Input() authorities: any[];
  @Input() size: 'sm' | 'md' = 'md';
  @Input() parentPopover: NgbPopover;
  @Input() cssClass: string = '';

  showAllInForce = false;
  showAllNotInForce = false;

  get hasStatus(): boolean {
    return this.authorities?.filter( auth => auth.legal_status === 'in_force' ||
                                             auth.legal_status === 'not_in_force').length > 0;
  }

  get inForce(): any[] {
    return this.authorities?.filter( auth => auth.legal_status === 'in_force');
  }

  get notInForce(): any[] {
    return this.authorities?.filter( auth => auth.legal_status === 'not_in_force');
  }

  getFlagIcon(authority: string) {
    if (authority === 'ep') {
      return `flag-icon-border fi fi-upc fi-flag-md`;
    }

    return COUNTRY_FLAG(authority, FlagSizeEnum.MD);
  }

  onPopoverHidden() {
    this.showAllInForce = false;
    this.showAllNotInForce = false;
  }

  toggleSeeAll(title: string) {
    if (title === 'In force') {
      this.showAllInForce = !this.showAllInForce;
    } else {
      this.showAllNotInForce = !this.showAllNotInForce;
    }
  }
}
