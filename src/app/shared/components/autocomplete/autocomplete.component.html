<div class="autocomplete" *ngIf="!tags else selectTag">
  <input #input class="form-control" type="text"
         [ngClass]="classCss"
         [(ngModel)]="initialValue"
         [ngbTypeahead]="search"
         (ngModelChange)="onModelChanged()"
         (selectItem)="selectedItem($event)"
         [placeholder]="placeholder">

  <span class="spinner-border spinner-border-sm autocomplete-spinner" role="status" aria-hidden="true"
        *ngIf="searching"></span>
</div>

<ng-template #selectTag>
  <div class="autocomplete" [ngClass]="classCss" >
    <ng-select [items]="items$ | async"
               [(ngModel)]="selectedValues"
               [placeholder]="placeholder"
               [typeahead]="itemsInput$"
               [multiple]="true"
               [bindValue]="autocompleteFunc ? 'value' : null"
               [bindLabel]="autocompleteFunc ? 'label' : null"
               typeToSearchText=""
               notFoundText="No items found"
               [openOnEnter]="false"
               [loading]="searching"
               [isOpen]="false"
               [hideSelected]="true"
               [selectOnTab]="true"
               [clearable]="clearable"
               (change)="selectedItem($event)"
               (blur)="onBlur($event)"
               (focus)="onFocus($event)"
               (paste)="onPaste($event)"
               (keydown.tab)="onKeydown($event)"
               #select>
    </ng-select>
    <div class="dropdown-icon" (click)="onToggle()" *ngIf="hasDropdownIcon"></div>
  </div>
</ng-template>
