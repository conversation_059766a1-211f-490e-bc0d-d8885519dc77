@import 'scss/layout2021/variables';
@import 'scss/components/autocomplete';

::ng-deep {

  .ng-value {
    .ng-value-icon {
      padding-right: 10px !important;
      background-color: transparent !important;
      border: none !important;
      position: relative;
      z-index: 10;  
    }
    .ng-value-label {
      white-space: break-spaces;
      padding-left: 15px !important;
    }
  }

  .ng-value-container .ng-value {
    margin: 0 5px;
    font-family: Open Sans Regular;
  }
}

.dropdown-icon {
  display: inline;
  right: 0px;
  top: 6px;
  bottom: auto;
  text-align: center;
  z-index: 99999;
  cursor: pointer;
}

.ng-select-opened {
  ~.dropdown-icon::after {
    content: '\f077';
  }
}