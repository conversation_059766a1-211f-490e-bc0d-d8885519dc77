import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostListener, Injector,
  Input,
  OnDestroy,
  OnInit, Optional,
  Output,
  ViewChild
} from '@angular/core';
import { catchError, debounceTime, distinctUntilChanged, filter, map, switchMap, tap } from 'rxjs/operators';
import { concat, fromEvent, iif, Observable, of, Subject, Subscription } from 'rxjs';

import { NgSelectComponent } from '@ng-select/ng-select';
import { SearchTermService } from '@core/services';
import { ControlContainer, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ControlValueAccessorComponent } from '../control-value-accessor/control-value-accessor.component';

@Component({
  selector: 'app-autocomplete',
  templateUrl: './autocomplete.component.html',
  styleUrls: ['./autocomplete.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: AutocompleteComponent
    }
  ]
})
export class AutocompleteComponent extends ControlValueAccessorComponent implements AfterViewInit, OnDestroy, OnInit {
  @Input() field: string;
  @Input() id: string;
  @Input() classCss: string;
  @Input() classError: string;
  @Input() placeholder = '';
  @Input() initialValue?: string | Array<string> | Array<{ value: string, label: string }> = '';
  @Input() minLength = 3;
  @Input() maxLength = 30;
  @Input() multipleEntries = true;
  @Input() tags = false;
  @Input() tagSeparator = ',';
  @Input() tagVertical = true;
  @Input() pasteSeparator?: RegExp = /[\n\t]/;
  @Input() autocompleteFunc: (term: string) => Array<{ value: string, label: string }>;
  @Input() autoCompleteObservable: (term: string) => Observable<any>;
  @Input() autocompleteTermOnFocus: string = null;
  @Input() closeWhenItemSelected = true;
  @Input() closeWhenOnBlur = false;
  @Input() isEnteredValueSelectable = false;
  @Input() hasDropdownIcon = false;
  @Input() clearable = true;
  @Input() canAddTag = true;

  @Output() valueChanged: EventEmitter<Object> = new EventEmitter();

  @ViewChild('input', {static: true}) input: ElementRef;
  @ViewChild('select', {static: false}) select: NgSelectComponent;

  options: Array<string> = [];
  searching = false;

  items$: Observable<any>;
  itemsInput$ = new Subject<string>();
  selectedValues: string | Array<string>;

  isDropdownOpened = false;
  private subscriptions = new Subscription();

  constructor(
    @Optional() protected controlContainer: ControlContainer,
    private searchService: SearchTermService
  ) {
    super(controlContainer);
  }

  ngOnInit() {
    if (this.tags) {
      if (this.initialValue) {
        if (typeof (this.initialValue) === 'string') {
          this.selectedValues = (<string>this.initialValue).split(this.tagSeparator);
        } else {
          if (typeof (this.initialValue[0]) === 'string') {
            this.selectedValues = <Array<string>>this.initialValue;
          } else {
            this.selectedValues = (<Array<{ value: string, label: string }>>this.initialValue).map<string>(o => o.value);
          }
        }
      }
      this.initializeDropdownItems();
    }
  }

  ngAfterViewInit(): void {
    this.subscribeKeyup();
    if (this.tags) {
      $(`.ng-select-container input`).addClass('form-control');
      if (this.tagVertical) {
        $(`.ng-value-container`).addClass('flex-column');
        $(`.ng-value-container`).addClass('align-items-start');
      }
    }
  }

  notifyValueChanged(value): void {
    this.valueChanged.emit(value);
    this.setControlValue(value, true);
  }

  detachValue(value: string): string {
    if (!this.multipleEntries) {
      return value;
    }
    const arrayOfValue = value.split(this.tagSeparator);
    return arrayOfValue.pop().trim();
  }

  search = (term$: Observable<string>) =>
    term$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      tap(() => this.searching = true),
      switchMap(value => iif(() => this.detachValue(value).length <= this.minLength,
        of([]).pipe(tap(() => this.searching = false)),
        this.searchService.search({field: this.field, value: this.detachValue(value)})
          .pipe(
            tap(() => this.searching = false),
            map(({data}) => this.postProcessResults(data.results.sort(), value)),
            catchError(() => {
              this.searching = false;
              return of(this.isEnteredValueSelectable ? [value] : []);
            })
          )
      ))
    )

  initializeDropdownItems() {
    if (this.autocompleteFunc) {
      this.items$ = concat(
        of(this.initialValue || []), // default items
        this.itemsInput$.pipe(
          tap(() => this.searching = true),
          map((val) => this.autocompleteFunc(val)),
          tap(() => this.searching = false),
          tap(() => this.openDropdown())
        )
      );
      return;
    }

    if (this.autoCompleteObservable) {
      this.items$ = concat(
        of([]), // default items
        this.itemsInput$.pipe(
          distinctUntilChanged(),
          debounceTime(800),
          tap(() => this.searching = true),
          switchMap(value => iif(() => !value || value.length <= this.minLength,
            of([]).pipe(tap(() => this.searching = false)),
            this.autoCompleteObservable(value)
              .pipe(
                tap(() => this.searching = false),
                tap(() => this.openDropdown()),
                map((results) => this.postProcessResults(results, value)),
                catchError(() => {
                  this.searching = false;
                  return of(this.isEnteredValueSelectable ? [value] : []);
                })
              )
          ))
        )
      );
      return;
    }

    this.items$ = concat(
        of([]), // default items
        this.itemsInput$.pipe(
          distinctUntilChanged(),
          debounceTime(800),
          tap(() => this.searching = true),
          switchMap(value => iif(() => !value || value.length <= this.minLength,
            of([]).pipe(tap(() => this.searching = false)),
            this.searchService.search({field: this.field, value})
              .pipe(
                tap(() => this.searching = false),
                tap(() => this.openDropdown()),
                map(({data}) => this.postProcessResults(data.results.sort(), value)),
                catchError(() => {
                  this.searching = false;
                  return of(this.isEnteredValueSelectable ? [value] : []);
                })
              )
          ))
        )
      );
  }

  selectedItem(event) {
    if (!this.multipleEntries) {
      return event.item;
    }
    let updatedValue: string;
    if (this.tags) {
      updatedValue = this.autocompleteFunc ? event.map(o => o.value).join(this.tagSeparator) : event.join(this.tagSeparator);
      if (this.closeWhenItemSelected) {
        this.closeSelectDropdown();
      }
    } else {
      const arrayOfValue = (<string>this.initialValue).split(this.tagSeparator);
      arrayOfValue[arrayOfValue.length - 1] = event.item;
      updatedValue = arrayOfValue.join(this.tagSeparator);
    }
    this.notifyValueChanged(updatedValue);
  }

  onBlur(event) {
    if (!this.select.searchTerm) {
      this.closeSelectDropdown();
      return;
    }
    this.addTag();
    const selectedItems = [...this.selectedValues];
    this.notifyValueChanged(selectedItems.join(this.tagSeparator));
    if (this.closeWhenOnBlur) {
      this.closeSelectDropdown();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onFocus(event) {
    if (this.autocompleteTermOnFocus != null) {
      this.itemsInput$.next(this.autocompleteTermOnFocus);
    }
  }

  onModelChanged() {
    this.notifyValueChanged(this.initialValue);
  }

  onPaste($event) {
    $event.stopPropagation();
    $event.preventDefault();
    let items = $event.clipboardData.getData('Text').split(this.pasteSeparator).map((v) => v.trim());
    items = items.filter((v)  => {
      return v.length > 0;
    });

    if (this.tags) {
      items = items.flatMap(o => o.split(this.tagSeparator));
    }

    if (items.length > 0) {
      const selectedItems = !this.selectedValues ? [] : [...this.selectedValues];
      selectedItems.push(...items);
      this.closeSelectDropdown();

      if (this.autocompleteFunc) {
        this.initialValue = this.autocompleteFunc(selectedItems.join(this.tagSeparator));
        this.selectedValues = (<Array<{ value: string, label: string }>>this.initialValue).map<string>(o => o.value);
        this.initializeDropdownItems();
      } else {
        this.selectedValues = selectedItems;
      }

      this.notifyValueChanged(selectedItems.join(this.tagSeparator));
    }
  }

  reset() {
    this.ngOnInit();
    this.selectedValues = null;
    this.clearControlValue();
  }

  private subscribeKeyup(): void {
    const element = this.input ? this.input.nativeElement : <HTMLElement>this.select?.element.getElementsByTagName('input')[0];
    if (!element) {
      return;
    }
    const keyup$ = fromEvent(element, 'keyup')
      .pipe(
        filter((e: KeyboardEvent) => e.key !== 'Enter' && !e.ctrlKey && e.key !== 'Control'),
        map((event: any) => event.target.value.trim()),
        distinctUntilChanged()
      )
      .subscribe({
        next: value => {
          if (this.tags) {
            const selectedItems = !this.selectedValues ? [] : [...this.selectedValues];
            if (value) {
              const trimmedValue = typeof (value) === 'string' ? value.trim() : value;
              selectedItems.push(trimmedValue);
            }
            this.notifyValueChanged(selectedItems.join(this.tagSeparator));
          } else {
            this.notifyValueChanged(value);
          }
        }
      });
    this.subscriptions.add(keyup$);
  }

  private postProcessResults(results: string[], searchTerm: string): string[] {
    if (searchTerm) {
      searchTerm = searchTerm.trim().toLowerCase();
      if (this.isEnteredValueSelectable && !results.find((r) => r.trim().toLowerCase() === searchTerm)) {
        results.unshift(searchTerm.toUpperCase());
      }
    }

    return results;
  }

  onToggle() {
    if (this.isDropdownOpened) {
      this.closeSelectDropdown();
    } else {
      this.openDropdown();
    }
  }

  onKeydown(event: KeyboardEvent) {
    if (event.key === 'Tab' && !this.select.isOpen && this.select.searchTerm) {
      event.preventDefault();
      this.addTag();
    }
  }

  private addTag() {
    if (this.canAddTag) {
      if (!this.selectedValues) {
        this.selectedValues = [this.select.searchTerm.toUpperCase()];
      } else {
        if (!this.selectedValues.includes(this.select.searchTerm.toUpperCase())) {
          this.selectedValues = [...this.selectedValues, this.select.searchTerm.toUpperCase()];
        }
      }
      this.select.searchTerm = '';
    }
  }

  private closeSelectDropdown() {
    this.select.isOpen = false;
    this.select.blur();
    this.isDropdownOpened = false;
  }

  private openDropdown() {
    this.select.isOpen = true;
    this.select.focus();
    this.isDropdownOpened = true;
  }
}
