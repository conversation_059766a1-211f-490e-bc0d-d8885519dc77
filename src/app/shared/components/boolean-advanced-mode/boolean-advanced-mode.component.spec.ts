import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { BooleanAdvancedModeComponent } from './boolean-advanced-mode.component';
import { SharedModule } from '@shared/shared.module';
import { BooleanSearchService, BooleanSearchStoreService } from '@core';

describe('BooleanAdvancedModeComponent', () => {
  let component: BooleanAdvancedModeComponent;
  let fixture: ComponentFixture<BooleanAdvancedModeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BooleanAdvancedModeComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BooleanAdvancedModeComponent);
    component = fixture.componentInstance;
    component.booleanSearchService = TestBed.inject(BooleanSearchService);
    component.storeService = TestBed.inject(BooleanSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
