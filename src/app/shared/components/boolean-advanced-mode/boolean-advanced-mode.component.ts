import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import ace from 'ace-builds/src-min-noconflict/ace';

import 'ace-builds/src-noconflict/mode-sql';
import './theme-github';
import { Subscription } from 'rxjs';
import {
  BooleanSearchService,
  EXCLUDED_BOOLEAN_FIELDS_FOR_PUBLICATION_SEARCH,
  EXCLUDED_FIELDS_MESSAGE,
  PatentListScopeEnum,
  TagService
} from '@core/services';
import { BaseStoreService } from '@core/store';

declare var $: any;

@Component({
  selector: 'app-boolean-advanced-mode',
  templateUrl: './boolean-advanced-mode.component.html',
  styleUrls: ['./boolean-advanced-mode.component.scss']
})
export class BooleanAdvancedModeComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {

  @Input() booleanSearchService: BooleanSearchService;
  @Input() storeService?: BaseStoreService;
  @Output() doSubmit: EventEmitter <boolean> = new EventEmitter();
  @Output() changeQuery: EventEmitter <boolean> = new EventEmitter();
  markers = [];

  @ViewChild('editor') private editor: ElementRef<HTMLElement>;

  private Mode;
  private aceEditor: ace.Ace.Editor;
  private debounce: any;
  private subscriptions = new Subscription();

  constructor(private tagService: TagService) {
  }

  get isPublicationQuery(): boolean{
    return this.storeService?.isPublications;
  }

  get queryKeywords(): string {
    if(this.isPublicationQuery){
      return `CPC|IPC|CPC4|IPC4|APPLICANTS|INVENTORS|OWNERS
        |TITLE|ABSTRACT|TECH_FIELDS|AUTHORITIES
        |RAW_PUBLICATION_NUMBER|PUBLICATION_DATE|APPLICATION_DATE|PUBLICATION_AUTHORITY|ALSO_PUBLISHED_AS
        |CLAIMS|DESCRIPTION|TEXT|TAC|PUBLICATION_KIND|APPLICATION_NUMBERS|MARKET_COVERAGE|TECHNOLOGY_BROADNESS
        |RECENCY|RECENCY_YEARS|CONSISTENCY|LEGAL_STATUS|TAG
        |OWNER_IDS`;
    }
    return `CPC|IPC|CPC4|IPC4|APPLICANTS|INVENTORS
        |TITLE|ABSTRACT|TECH_FIELDS|AUTHORITIES|OWNERS
        |RAW_PUBLICATION_NUMBER|PUBLICATION_DATE|PRIORITY_DATE|PUBLICATION_AUTHORITY|ALSO_PUBLISHED_AS
        |CLAIMS|DESCRIPTION|TEXT|TAC|PUBLICATION_KIND|APPLICATION_NUMBERS|RISK|IMPACT|MARKET_COVERAGE|TECHNOLOGY_BROADNESS
        |RECENCY|RECENCY_YEARS|CONSISTENCY|CITATION_BACKWARD_COUNT|CITATION_FORWARD_COUNT|NUMBER_OF_AUTHORITIES|LEGAL_STATUS|TAG
        |OWNER_IDS`;
  }

  ngOnInit(): void {
    const resetEvent$ = this.booleanSearchService.resetEvent.subscribe({
      next: val => {
        this.aceEditor.setValue('');
        this.booleanSearchService.search_input = '';
      }
    });
    this.subscriptions.add(resetEvent$);

    if (this.tagService.tags.length === 0) {
      const getTags$ = this.tagService.getTags().subscribe();
      this.subscriptions.add(getTags$);
    }
    const isPublications$ = this.storeService.isPublications$.subscribe({
      next: val => {
        setTimeout(() => {
          this.setRule();
          this.aceEditor.session.setMode(new this.Mode);
          this.updateText();
        }, 100);
      }
    });
    this.subscriptions.add(isPublications$);
  }

  ngAfterViewInit(): void {
    this.setRule();
    ace.config.set('fontSize', '14px');

    this.aceEditor = ace.edit(this.editor.nativeElement);
    this.aceEditor.setTheme('ace/theme/github');
    this.aceEditor.session.setMode(new this.Mode);
    this.aceEditor.setOptions({
      enableBasicAutocompletion: true,
      enableLiveAutocompletion: true,
      maxLines: Infinity,
    });

    if (this.booleanSearchService.search_input) {
      this.aceEditor.setValue(this.booleanSearchService.search_input, 1);
      this.updateText();
    }
    this.aceEditor.setShowPrintMargin(false);
    this.aceEditor.session.setUseWrapMode(true);
    this.aceEditor.on('change', () => {
      if (this.debounce) {
        clearTimeout(this.debounce);
      }
      this.updateText();
    });

    this.aceEditor.container.addEventListener('mousedown', (e) => {
      this.validateHighlighError();
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onKeyDown(event) {
    if (!event.ctrlKey || event.keyCode !== 13) {
      return;
    }
    if (!this.booleanSearchService.search_input || this.booleanSearchService.disableAdvancedModeSearch) {
      return;
    }
    if(this.isPublicationQuery && this.hasExcludeFields() ){
      return;
    }
    this.doSubmit.emit(true);
  }

  private hasExcludeFields(): boolean{
    const excludedFields = EXCLUDED_BOOLEAN_FIELDS_FOR_PUBLICATION_SEARCH.join("|");
    if (new RegExp(excludedFields, 'g').test(this.booleanSearchService.search_input)) {
      const queryText = this.aceEditor.getValue();
      const lines = queryText.split('\n');
      lines.forEach((line, lineNumber) => {
        let matches = [...line.matchAll(new RegExp(excludedFields, 'g'))];
        if (matches.length> 0){
          const errorMessages: Array<string> = [];
          matches.forEach((match) => {
            ///const range: any = this.createSemanticOrLexerRange(lineNumber+1, match.index, match[0]);
            const Range = ace.require('ace/range').Range;

            const endPosition = line.toUpperCase().indexOf(match[0], match.index) + match[0].length;

            const range = new Range(lineNumber, match.index, lineNumber, endPosition);
            if (range !== null) {
              this.markers.push(this.aceEditor.getSession().addMarker(range, 'highlightError', 'text'));
              errorMessages.push(EXCLUDED_FIELDS_MESSAGE);
            }
          });

          this.tooltipError(errorMessages);
        }
      });
      this.booleanSearchService.disableAdvancedModeSearch = true;
      return true;
    }
    return false;
  }

  private updateText() {
    for (const marker of this.markers) {
      this.aceEditor.getSession().removeMarker(marker);
    }
    const text = this.aceEditor.getValue();
    this.removeTooltips();
    this.booleanSearchService.disableAdvancedModeSearch = true;
    this.debounce = setTimeout(() => {
      this.booleanSearchService.validateMessage = null;
      if (text) {
        this.setCursorWait(true);
        const invalidFields = this.isPublicationQuery && this.hasExcludeFields();
        const searchType = this.isPublicationQuery ? PatentListScopeEnum.PUBLICATION : PatentListScopeEnum.FAMILY;
        const parseAdvanced$ = this.booleanSearchService.parseAdvanced(text, searchType).subscribe({
          next: resp => {
            if(!invalidFields){
              this.booleanSearchService.validateMessage = null;
              this.booleanSearchService.search_input = text;
              this.booleanSearchService.disableAdvancedModeSearch = false;
            }
            this.setCursorWait(false);
          },
          error: ({error})=> {
            this.setCursorWait(false);
            this.booleanSearchService.disableAdvancedModeSearch = true;
            this.parseErrors(error);
          }
        });
        this.subscriptions.add(parseAdvanced$);
      } else {
        this.booleanSearchService.search_input = '';
      }
      this.changeQuery.emit(true);
    }, 500);
  }

  setCursorWait(value: boolean) {
    const element = document.getElementsByClassName('ace_content') as HTMLCollectionOf<HTMLElement>;
    if (element?.length) {
      const parent = element[0].parentElement;
      if (value) {
        parent.classList.add('cursor-wait');
      } else {
        parent.classList.remove('cursor-wait');
      }
    }
  }

  private setRule() {
    const oop = ace.require('ace/lib/oop');
    const textMode = ace.require('ace/mode/sql').Mode;
    const textHighlightRules = ace.require('ace/mode/sql_highlight_rules').SqlHighlightRules;
    const self = this;

    const customHighlightRules = function () {

      const keywords = ( self.queryKeywords);

      const builtinConstants = (
        'true|false|AND|OR'
      );

      const keywordMapper = this.createKeywordMapper({
        'keyword': keywords,
        'constant.language': builtinConstants,
      }, 'identifier', true);

      this.$rules = {
        'start': [
          {
            token: 'comment',
            regex: '--.*$'
          },
          {
            token: keywordMapper,
            regex: '[a-zA-Z_$][a-zA-Z0-9_$]*\\b'
          }
        ]
      };
    };

    oop.inherits(customHighlightRules, textHighlightRules);

    this.Mode = function () {
      this.HighlightRules = customHighlightRules;
    };

    oop.inherits(this.Mode, textMode);

    (function () {
      this.$id = 'ace/mode/custom';
    }).call(this.Mode.prototype);
  }

  private parseErrors(error) {
    const errorMessages: Array<string> = [];

    if (error.parser_error) {
      const range: any = this.createParserRange(error.parser_error.line_number - 1, error.parser_error.line_position,
        error.parser_error.value);
      if (range !== null) {
        this.markers.push(this.aceEditor.getSession().addMarker(range, 'highlightError', 'text'));

        this.tooltipError([error.parser_error.message]);
      }
      return;
    }

    if (error.lexer_error) {
      const range: any = this.createSemanticOrLexerRange(error.lexer_error.line_number - 1, error.lexer_error.line_position,
        error.lexer_error.value);
      if (range !== null) {
        this.markers.push(this.aceEditor.getSession().addMarker(range, 'highlightError', 'text'));
        this.tooltipError([error.lexer_error.message]);
      }
      return;
    }

    if (error.semantic_errors) {
      for (const semantic_error of error.semantic_errors) {
        const range: any = this.createSemanticOrLexerRange(semantic_error.line_number - 1, semantic_error.line_position,
          semantic_error.value);
        if (range !== null) {
          this.markers.push(this.aceEditor.getSession().addMarker(range, 'highlightError', 'text'));
          errorMessages.push(semantic_error.reason);
        }
      }
      this.tooltipError(errorMessages);
    }
  }

  private createParserRange(lineNumber: number, linePosition: number, value: string): Range {
    const text = this.aceEditor.getValue();
    const lines = text.split('\n');
    const line = lines[lineNumber];
    let startPosition = 0;
    let endPosition = 0;
    if (value === '(' || value === ')') {
      startPosition = line.indexOf(value, linePosition);
      endPosition = startPosition + value.length;
    } else {
      startPosition = line.substring(linePosition - 1, 0).trim().lastIndexOf(' ') + 1;
      endPosition = line.substring(linePosition + 1, 0).trim().length;
    }

    const Range = ace.require('ace/range').Range;

    return new Range(lineNumber, startPosition, lineNumber, endPosition);
  }

  private createSemanticOrLexerRange(lineNumber: number, linePosition: number, value: string): Range {
    const text = this.aceEditor.getValue();
    const lines = text.split('\n');
    const line = lines[lineNumber];
    const startPosition = linePosition;
    const endPosition = line.toUpperCase().indexOf(value, startPosition) + value.length;
    const Range = ace.require('ace/range').Range;

    return new Range(lineNumber, startPosition, lineNumber, endPosition);
  }

  private tooltipError(messages: Array<string>): void {
    // Add tooltip
    setTimeout(() => {
      for (let i = 0; i < messages.length; i++) {
        const errorHighlight = $('.highlightError')[i];
        $(errorHighlight).attr('data-toggle', 'tooltip');
        $(errorHighlight).attr('data-placement', 'bottom');
        $(errorHighlight).attr('title', messages[i]);
        $('[data-toggle="tooltip"]').tooltip({
          template: '<div class="tooltip tooltip-custom"><div class="tooltip-inner"></div></div>'
        });
      }
    }, 100);
  }

  private validateHighlighError() {
    setTimeout(() => {
      if (!$('.highlightError[data-toggle="tooltip"]').length) {
        this.updateText();
      }
    }, 100);
  }

  private removeTooltips(): void {
    $('.tooltip-inner').remove();
  }
}
