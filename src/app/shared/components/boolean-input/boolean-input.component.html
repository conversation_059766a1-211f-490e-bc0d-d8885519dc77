<div class="alert alert-info overflow-hidden" *ngIf="booleanSearchService.buildBooleanQuery() && showBooleanQuery">
  <a (click)="toggleShowBooleanQuery()" class="close" >&times;</a>
  <span class="headline-6">Your query:</span> <span class="paragraph-3"> {{ booleanSearchService.buildBooleanQuery(false) }}</span>
</div>

<button (click)="toggleShowBooleanQuery()" *ngIf="!showBooleanQuery" class="btn btn-primary-outline btn-sm mb-3">Show boolean query</button>

<div class="mb-2 bls-input" *ngFor="let clause of clauses; let i = index;">
  <div class="row m-0 position-relative field-row">
    <div class="col-{{getIndent(i, false)}} position-relative p-0">
      <div class="btn-group btn-group-toggle inter-field-operator" data-toggle="buttons" *ngIf="i > 0">
        <label  *ngFor="let conjunction of getConjunctions();" class="btn btn-sm conjunction-{{ conjunction }} "
                [ngClass]="clause.conjunction == conjunction ? 'active btn-primary' : 'btn-primary-outline'"
                (click)="clause.conjunction = conjunction; changeQuery.emit(true)">
          <input type="radio" name="options" hidden>{{ conjunction }}
        </label>
      </div>
      <div class="bracket open_bracket">
        <a (click)="booleanSearchService.incLevel(i, 1); changeQuery.emit(true)" [class.disabled]="clause.openBrackets === 0 ? true : null" ngbTooltip="Open bracket">
          <span *ngFor="let i of openBrackets(clause)">(</span>
        </a>
      </div>
    </div>

    <div *ngIf="getIndent(i, true)-getIndent(i, false) > 0" class="col-{{getIndent(i, true)-getIndent(i, false)}} position-relative p-0">
    </div>

    <div class="col-5 d-flex m-0 p-0 justify-content-between">
      <div class="p-0 m-0 flex-fill me-2 position-relative bls-input-field-dropdown" ngbDropdown (openChange)="onFieldSelector($event)">
        <div class="big-input caret-off" ngbDropdownToggle tabindex="2">
          <input [value]="getFieldTitle(clause)" placeholder="Select a field" readonly
            [ngClass]="getFieldErrorClass(clause.field)" class="form-control"/>
          <div class="dropdown-icon">&#xf107;</div>
        </div>
        <div ngbDropdownMenu class="field-selector">
          <ng-container *ngFor="let field of fields">
            <a ngbDropdownItem *ngIf="shouldDisplayField(clause, field)" [ngClass]="field.equals(clause.field) ? 'active' : ''"
              [ngStyle]="getTitleStyle(field)" (click)="onFieldSelected(clause, field, inputValueContainer)">{{ field.title }}</a>
          </ng-container>
        </div>
      </div>

      <div class="p-0 m-0 me-2 position-relative bls-input-operator-dropdown" ngbDropdown>
        <div class="big-input caret-off" ngbDropdownToggle tabindex="3">
          <input [value]="clause.operator" readonly [ngClass]="getOperatorErrorClass(clause.operator)"
                 class="form-control"/>
          <div class="dropdown-icon">&#xf107;</div>
        </div>
        <div ngbDropdownMenu>
          <a ngbDropdownItem *ngFor="let operator of clause.getOperators()"
            [ngClass]="{ 'active' : clause.operator === operator, 'd-flex justify-content-around': clause.field?.type === fieldTypeEnum.DATE}"
            (click)="clause.operator = operator; changeQuery.emit(true)"><span>{{ operator }}</span> <b>{{getOpDescription(operator,clause)}}</b></a>
        </div>
      </div>
    </div>

    <div class="col d-flex m-0 p-0 justify-content-between">
      <div class="flex-fill p-0 m-0">
        <div #inputValueContainer class="dropdown me-0 bls-input-value" tabindex="4" (click)="onFocus($event)"
             [ngClass]="{'dropdown-with-select':
             booleanFieldEnum.IPC === clause.field?.name ||
             booleanFieldEnum.CPC === clause.field?.name
             }">
          <ng-container [ngSwitch]="true">
            <ng-container *ngSwitchCase="clause.field?.type === fieldTypeEnum.DATE">
              <div class="big-input">
                <input [(ngModel)]="clause.value" name="{{'value' + i }}" ngbDatepicker #dp="ngbDatepicker" appMaskDate
                       [ngClass]="getInputErrorClass(clause, clause.value, true)" type="text"
                       (keydown.control.enter)="onSubmit()" [maxDate]="today" [minDate]="minday"
                       class="form-control" (ngModelChange)="validateClause(clause)"/>
              </div>
              <div class="dropdown-icon more-icon" (click)="dp.toggle()"><i class="fas fa-calendar-alt"></i></div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.type === fieldTypeEnum.NUMBER">
              <div class="big-input">
                <input [(ngModel)]="clause.value" name="{{'value' + i }}" mask="0*"
                       [ngClass]="getInputErrorClass(clause, clause.value, true)" type="text"
                       placeholder="Enter a number to search" (keydown.control.enter)="onSubmit()"
                       class="form-control" (ngModelChange)="validateClause(clause)"/>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.CPC">
              <div class="big-input">
                <app-classification-filter-browser classification="cpc" [selectedItems]="clause.valueArr"
                                                   (keydown)="onKeyDown($event, clause)" (focusout)="onKeyDown($event, clause, true)" (paste)="onPaste($event, clause)"
                                                   (changed)="updateTagInputValue($event, clause)" (search)="onTypedCpcIpcText($event, clause)"
                                                   [chkValidTagFunction]="invalidTag" [validTagParam]='clause' [errorClass]="getInputErrorClass(clause, clause.value, true)"
                                                   placeholder="Click plus icon to select a CPC code">
                </app-classification-filter-browser>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.IPC">
              <div class="big-input">
                <app-classification-filter-browser classification="ipc" [selectedItems]="clause.valueArr"
                                                   (keydown)="onKeyDown($event, clause)" (focusout)="onKeyDown($event, clause, true)" (paste)="onPaste($event, clause)"
                                                   (changed)="updateTagInputValue($event, clause)" (search)="onTypedCpcIpcText($event, clause)"
                                                   [chkValidTagFunction]="invalidTag" [validTagParam]='clause' [errorClass]="getInputErrorClass(clause, clause.value, true)"
                                                   placeholder="Click plus icon to select a IPC code">
                </app-classification-filter-browser>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.TECH_FIELDS">
              <div class="big-input">
                <app-technology-field-browser [selectedItems]="clause.valueArr" (changed)="updateTechFieldInputValue($event, clause)">
                </app-technology-field-browser>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.APPLICANTS">
              <div class="big-input">
                <app-autocomplete [field]="'applicants'" [id]="'applicants_boolean'"
                                  [tags]="true" [tagVertical]="false" tagSeparator=" OR " [minLength]="2"
                                  [classCss]="getInputClass(clause, clause.value, true)"
                                  [classError]="getInputErrorClass(clause, clause.value, true)"
                                  (keydown.control.enter)="onSubmit()"
                                  [initialValue]="clause.value" (valueChanged)="updateClauseValue($event, clause, true)"
                                  [isEnteredValueSelectable]="true" [closeWhenOnBlur]="true"
                                  placeholder="Type to select an applicant">
                </app-autocomplete>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.INVENTORS">
              <div class="big-input">
                <app-autocomplete [field]="'inventors'" [id]="'inventors_boolean'"
                                  [tags]="true" [tagVertical]="false" tagSeparator=" OR " [minLength]="2"
                                  [classCss]="getInputClass(clause, clause.value, true)"
                                  [classError]="getInputErrorClass(clause, clause.value, true)"
                                  (keydown.control.enter)="onSubmit()"
                                  [initialValue]="clause.value" (valueChanged)="updateClauseValue($event, clause, true)"
                                  [closeWhenOnBlur]="true"
                                  placeholder="Type to select an inventor">
                </app-autocomplete>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.OWNERS">
              <div class="big-input">
                <app-autocomplete [field]="'assignees'" [id]="'assignees_boolean'"
                                  [tags]="true" [tagVertical]="false" tagSeparator=" OR " [minLength]="2"
                                  [classCss]="getInputClass(clause, clause.value, true)"
                                  [classError]="getInputErrorClass(clause, clause.value, true)"
                                  (keydown.control.enter)="onSubmit()"
                                  [initialValue]="clause.value" (valueChanged)="updateClauseValue($event, clause, true)"
                                  [closeWhenOnBlur]="true"
                                  placeholder="Type to select an owner">
                </app-autocomplete>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.OWNER_IDS">
              <div class="big-input">
                <app-corporate-entities-browser [id]="'owner_ids_boolean'"
                                                (changed)="updateCorporateEntityInputValue($event, clause, true)"
                                                [chkValidTagFunction]="invalidTag" [validTagParam]='clause'
                                                [errorClass]="getInputErrorClass(clause, clause.value, true)"
                                                [selectedItems]="ownersPerClauses[clause.value]"
                                                placeholder="Click plus icon to select owners">
                </app-corporate-entities-browser>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.LEGAL_STATUS">
              <div ngbDropdown>
                <div class="form-control py-0" ngbDropdownToggle>
                  <p class="my-1 legal-status-field" *ngIf="!clause.valueArr?.length">Click here to select a legal status</p>
                  <div class="my-1 legal-status-field d-flex align-items-center" *ngIf="clause.valueArr?.length">
                    <div class="badge-selected-{{status.value | lowercase}}" *ngFor="let status of getSelectedLegalStatus(clause)">
                      <span  class="badge-{{status.value | lowercase}} content-label-small legal-status-selection">{{ status.text | titlecase}}</span>
                      <div class="remove-status {{status.value | lowercase}}" (click)="clause.setUnSelectLegalStatus(status, $event)">
                        <i class="fa fa-close px-1 cursor-pointer"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div ngbDropdownMenu [ngClass]="{'d-none': !getUnselectedLegalStatus(clause).length}">
                  <div class="d-flex flex-column gap-spacing-sm">
                    <span *ngFor="let status of getUnselectedLegalStatus(clause)" class="badge-{{status.value | lowercase}} m-x-spacing-md content-label-small legal-status-selection"
                    (click)="clause.setValueByLegalStatus(status)">{{ status.text | titlecase}}</span>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.isValuablePercentageField(clause.field)">
              <div ngbDropdown>
                <div class="big-input caret-off" ngbDropdownToggle>
                  <input [value]="getSelectedImpact(clause)?.text" readonly
                         [ngClass]="getInputErrorClass(clause, clause.value, true)" type="text"
                         placeholder="Click here to select an option" (keydown.control.enter)="onSubmit()"
                         class="form-control"/>
                  <div class="dropdown-icon">&#xf107;</div>
                </div>
                <div ngbDropdownMenu>
                  <a *ngFor="let item of PERCENTAGE_FIELD_OPTIONS[clause.field?.name]" class="dropdown-item"
                    (click)="setClauseValue(clause, item.value, true)"
                    [ngClass]="clause.value === item.value ? 'active' : ''">{{ item.text }}</a>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.isAuthorityField()">
              <div ngbDropdown>
                <div class="big-input caret-off" ngbDropdownToggle>
                  <input [(ngModel)]="clause.value" name="{{'value' + i }}"
                         [ngClass]="getInputErrorClass(clause, clause.value, true)" type="text"
                         placeholder="Enter a value to search authorities" (keydown.control.enter)="onSubmit()"
                         class="form-control" (ngModelChange)="validateClause(clause)"/>
                  <div class="dropdown-icon">&#xf107;</div>
                </div>
                <div ngbDropdownMenu>
                  <a *ngFor="let authority of filterAuthoritiesFunc(clause.value)" class="dropdown-item"
                    (click)="onAuthorityItemClicked(clause, authority)">
                    {{ authority.id + '-' + authority.name }}
                  </a>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.PUBLICATION_KIND">
              <div class="big-input">
                <input [(ngModel)]="clause.value" name="{{'value' + i }}"
                       [ngClass]="getInputErrorClass(clause, clause.value, true)"
                       (keydown.control.enter)="onSubmit()" maxlength="2"
                       placeholder="Enter a value to search for publication kind"
                       class="form-control" (ngModelChange)="validateClause(clause)"/>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="clause.field?.name === booleanFieldEnum.TAG">
              <div #containerTags>
                <app-tags-display [container]="containerTags"
                                  [tags]="getSelectedTags(clause)"
                                  [canManageTags]="false"
                                  [showIcon]="false"
                                  [updateTagsOnResize]="false"
                                  [onlySelectTags]="true"
                                  [openAfterSelect]="false"
                                  (tagsChange)="onTagsChanged(clause, $event, true)"
                                  [updateTagsDelay]="10"
                                  searchInputPlaceholder="Select a tag"
                                  noTagsMessage="No tags found"
                                  tagsDisplayCssClass="form-control tag-boolean-field p-y-spacing-none p-x-spacing-md">
                  <div selectorPlaceholder class="content-body-sm content-color-secondary p-y-spacing-xxx-s w-100">
                    Select a tag
                  </div>
                </app-tags-display>
              </div>
            </ng-container>
            <ng-container *ngSwitchDefault>
              <div class="big-input" data-intercom-target="text-input">
                <textarea [(ngModel)]="clause.value" name="{{'value' + i }}" appTextareaAutoresize
                [ngClass]="getInputErrorClass(clause, clause.value, true)"
                placeholder="Enter a value to search" (keydown.control.enter)="onSubmit()"
                class="form-control" (ngModelChange)="validateClause(clause)" [disabled]="!clause.field"></textarea>
              </div>
            </ng-container>
          </ng-container>
        </div>
      </div>
      <div class="m-0 p-0 ps-2 d-flex justify-content-between align-items-center">
        <div class="bracket close_bracket" [ngClass]="{'me-0': i === 0 && clauses.length == 1}">
          <a (click)="booleanSearchService.incLevel(i, -1); changeQuery.emit(true)" [class.disabled]="clause.closeBrackets === 0 ? true : null" ngbTooltip="Close bracket">
            <span *ngFor="let i of closeBrackets(clause)">)</span>
          </a>
        </div>

        <a (click)="onRemoveClause(i)" class="btn btn-sm btn-secondary-outline btn-icon"
          ngbTooltip="Remove this condition" [ngClass]="i === 0 && clauses.length == 1 ? 'd-none' : 'visible'">
          <i class="fa fa-times"></i>
        </a>
      </div>
    </div>

    <div class="col-input-extra d-flex ms-1 p-0 justify-content-start align-items-center" [hidden]="!clause?.field?.title">
      <div class="bls-tooltip d-flex align-items-center">
        <i class="fa-regular fa-circle-info tooltip-icon" (click)="openBooleanTooltip(clause)"
           [ngbTooltip]="'Click to see the description of ' + clause?.field?.title" tooltipClass="white-tooltip"></i>
      </div>

      <app-tooltip *ngIf="clause.errorMsg" [id]="'input-error-tooltip-'+i" [tooltipText]="clause.errorMsg"
                   [showIcon]="false" class="bls-input-error">
        <i class="fa fa-exclamation-circle"></i>
      </app-tooltip>

      <app-tooltip *ngIf="clause.warningMsg" [id]="'input-error-tooltip-'+i" [tooltipText]="clause.warningMsg"
                   [showIcon]="false" class="bls-input-warning">
        <i class="fa fa-exclamation-circle"></i>
      </app-tooltip>
    </div>
  </div>
</div>

<div class="d-flex justify-content-center mt-3" *ngIf="!booleanSearchService.isClauseLimited()">
  <a (click)="onAddClause()" class="btn btn-sm btn-primary btn-icon btn-add" ngbTooltip="Add another condition"
    data-intercom-target="add-condition">
    <i class="fa fa-plus"></i>
  </a>
</div>
