@import 'scss/layout2021/layout2021';
@import 'scss/layout2021/mixins';
@import 'scss/figma2023/index';

:host::ng-deep {
  @media (min-width: 1000px) {
    .container {
      max-width: 750px !important;
    }
  }

  @media (min-width: 1100px) {
    .container {
      max-width: 900px !important;
    }
  }

  @media (min-width: 1200px) {
    .container {
      max-width: 1000px !important;
    }
  }

  @media (min-width: 1300px) {
    .container {
      max-width: 1140px !important;
    }
  }
}

.big-input {
  cursor: pointer;
  position: relative;

  label {
    display: block;
    color: #999;
    cursor: pointer;
  }

  input {
    width: 100%;
    cursor: pointer;

    @include placeholder {
      color: #999999 !important;
      font-weight: normal;
    }
  }

  .error {
    border: 1px solid #d8000c;
    &:focus,
    &:active,
    &:focus-within {
      border-color: transparent;
      box-shadow: 0 0 5px #d8000c;
    }

    .ng-select-container {
      border: none;
    }
  }

  .ng-arrow-wrapper {
    display: none;
  }
}

.dropdown-with-select {
  .ng-select .ng-select-container {
    border: none !important;
  }
}

.col-input-extra {
  gap: 3px;
  width: 54px !important;
  min-width: 54px !important;
  max-width: 54px !important;
  position: absolute;
  right: -60px;
  top: 7px;

  .bls-input-error {
    color: #d8000c;
    font-size: 16px;
  }

  .bls-input-warning {
    color: orange;
    font-size: 16px;
  }

  .bls-tooltip {
    height: 24px;
    cursor: pointer;
    @include add-properties(map-get(map-get($typography, 'color'), 'tertiary'), false);

    &:hover {
      @include add-properties(map-get(map-get($typography, 'color'), 'primary'), false);
    }
  }
}

.bls-input {
  .dropdown-with-select {
    border: 1px solid #ced4da;
    @include border-radius(0.25rem);

    &:focus, &:focus-within {
      border-color: #389A85 !important;
    }
  }

  .dropdown-icon {
    font-weight: 900;
    font-family: "Font Awesome 6 Pro";
    color: #C5C5C5;
    transition: all .2s;
    position: absolute;
    width: 30px;
    background-color: transparent;
    right: 2px;
    top: 2px;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 4px 4px 0;
    border: 0;

    &:active {
      background-color: transparent;
    }

    &.more-icon {
      color: #00A083;
      cursor: pointer;
    }

    &::after {
      content: '' !important;
    }
  }

  .inter-field-operator {
    right: 34px;
    margin-top: 3px;
    position: absolute;
    .btn {
      width: 48px !important;
      min-width: 48px !important;

      &.conjunction-AND {
        padding-left: 10px;
        padding-right: 10px;
      }
    }
  }

  .bracket {
    line-height: 36px;

    a {
      font-size: 14px;
      width: 24px;
      text-align: center;
      border: 1px solid #00A083;
      color: #00A083;
      border-radius: 3px;
      white-space: nowrap;
      display: block;
      cursor: pointer;
    }

    a.disabled {
      border: 1px solid #ccc;
      color: #ccc;
    }
  }

  .close_bracket {
    margin-left: -3px;
    margin-right: 5px;
  }

  .open_bracket {
    position: absolute;
    right: 0;
    margin-right: 5px;
  }
  .field-row > *:not(.col-0){
    width: unset;
  }
  .col-0{
    width: 0;
    padding: 0;
  }
}
tag-input.form-control{
  min-height: 38px;
  height: auto;
}
a.dropdown-item > b{
  float: right;
}
textarea.form-control {
  resize: none;
}

.btn-secondary-outline .fa-times {
  line-height: 1rem;
}

.legal-status-field {
  color: $colour-grey-500;
  cursor: pointer;
  height: 28px;
}
.legal-status-selection {
  width: fit-content;
  display: block;
  cursor: pointer;
}

.badge-selected- {
  &valid, &pending, &invalid, &unknown {
    display: flex;
    // margin-bottom: $spacing-system-spacing-sm;
    margin-right: $spacing-system-spacing-x-s;
    width: fit-content;
    font-size: $font-size-sm;

    .remove-status {
      display: none;
      border-top-right-radius: $spacing-system-spacing-xx-s;
      border-bottom-right-radius: $spacing-system-spacing-xx-s;
      height: $spacing-system-spacing-xx-big;

      &.valid {
        color: $colours-content-content-success;
      }
      &.pending {
        color: $colours-content-content-warning;
      }
      &.invalid {
        color: $colours-content-content-danger;
      }
      &.unknown {
        color: $colours-content-content-secondary;
      }
    };

    &:hover {
      span {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      .remove-status {
        display: flex;
        align-items: center;
        &.valid {
          background-color: $colours-border-success;
        }
        &.pending {
          background-color: $colours-border-warning;
        }
        &.invalid {
          background-color: $colours-border-danger;
        }
        &.unknown {
          background-color: $colours-border-bold;
        }
      }
    }
  }
}

::ng-deep {
  .container-tags {
    max-height: none !important;

    .custom-tags-container {
      max-height: none !important;
    }
  }
}
