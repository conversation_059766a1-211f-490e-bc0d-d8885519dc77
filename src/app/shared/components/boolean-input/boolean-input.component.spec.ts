import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { BooleanInputComponent } from './boolean-input.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { BooleanSearchService, BooleanSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('BooleanInputComponent', () => {
  let component: BooleanInputComponent;
  let fixture: ComponentFixture<BooleanInputComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
        BrowserAnimationsModule,
        NgbModule,
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BooleanInputComponent);
    component = fixture.componentInstance;
    component.booleanSearchService = TestBed.inject(BooleanSearchService);
    component.storeService = TestBed.inject(BooleanSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
