import {
  AfterViewInit,
  Component,
  <PERSON><PERSON>he<PERSON>,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  ViewChildren
} from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { AUTHORITY_CODE_NODES, TECH_AREAS, techFieldsToNodes } from '@core/models';
import {
  AuthorityService,
  BooleanFieldEnum,
  BooleanSearchService,
  TreeSelectionItem,
  Clause,
  ClauseConjunctionEnum,
  ClauseOperatorEnum,
  Field,
  FieldTypeEnum
} from '@core/services';
import { TreeSelectionDialogComponent } from '../tree-selection-dialog/tree-selection-dialog.component';
import { BaseStoreService, BooleanSearchStoreService, TagService, UserService } from '@core';
import { TagModel } from '@core/models/tag.model';
import { TagLabelComponent } from '../tag-label/tag-label.component';
import { Subscription } from 'rxjs';

export enum TypeCpcIpc {
  CPC = 'cpc',
  IPC = 'ipc'
}

/**
 * Boolean input box component
 *
 * This component contains the boolean input box section of boolean module for reusability
 * @copyright Dennemeyer Octimine GmbH
 */
@Component({
  selector: 'app-boolean-input',
  templateUrl: './boolean-input.component.html',
  styleUrls: ['./boolean-input.component.scss'],
})
export class BooleanInputComponent implements OnInit, DoCheck, OnDestroy, AfterViewInit {

  @Input() booleanSearchService: BooleanSearchService;
  @Input() clearEmptyClause: boolean;
  @Input() storeService: BaseStoreService;

  @Output() doSubmit: EventEmitter <boolean> = new EventEmitter();
  @Output() changeQuery: EventEmitter <boolean> = new EventEmitter();

  fieldTypeEnum = FieldTypeEnum;
  showBooleanQuery: boolean = true;

  IMPACT_OPTIONS = [
    {value: '3', text: 'Top 1% most valuable patents in their technological fields'},
    {value: '2', text: 'Top 10% most valuable patents in their technological fields'},
    {value: '1', text: 'Top 25% most valuable patents in their technological fields'},
    {value: '0', text: 'Bottom 75% least valuable patents in their technological fields'}
  ];
  RISK_OPTIONS = [
    {value: '3', text: 'Top 1% most at-risk patents in their technological fields'},
    {value: '2', text: 'Top 10% most at-risk patents in their technological fields'},
    {value: '1', text: 'Top 25% most at-risk patents in their technological fields'},
    {value: '0', text: 'Bottom 75% least at-risk patents in their technological fields'}
  ];
  LEGAL_STATUS = [
    {value: 'valid', text: 'Alive'},
    {value: 'pending', text: 'Pending'},
    {value: 'invalid', text: 'Dead'},
    {value: 'unknown', text: 'Unknown'},
  ];

  /**
   * reference for ngbdatepicker
   */
  today = {year: new Date().getFullYear(), month: new Date().getMonth() + 1, day: new Date().getDate()};
  minday = {year: 1900, month: 1, day: 1};

  PERCENTAGE_FIELD_OPTIONS = {IMPACT: this.IMPACT_OPTIONS, RISK: this.RISK_OPTIONS};

  filterAuthoritiesFunc: (string) => Array<{ id: string, name: string }> = AuthorityService.filterAuthorities;

  booleanFieldEnum = BooleanFieldEnum;
  ownersPerClauses: Record<string, TreeSelectionItem[]> = {};

  private titleTree = '';
  private modalRef: NgbModalRef;

  private tagDebounce;

  private subscriptions = new Subscription();

  constructor(
    private modalService: NgbModal,
    public userService: UserService,
    private tagService: TagService
  ) {
  }

  get fields(): Array<Field> {
    return this.booleanSearchService.fields;
  }

  get clauses(): Array<Clause> {
    return this.booleanSearchService.clauses;
  }

  get messageAlert(): string | Array<string> {
    return this.booleanSearchService.messageAlert;
  }

  set messageAlert(value: string | Array<string>) {
    this.booleanSearchService.messageAlert = value;
  }

  get validateMessage(): string | Array<string> {
    return this.booleanSearchService.validateMessage;
  }

  set validateMessage(value: string | Array<string>) {
    this.booleanSearchService.validateMessage = value;
  }

  ngOnInit() {
    this.booleanSearchService.booleanInit(this.storeService.isPublications);
    const recoveredClausesFromStorage$ = this.booleanSearchService.recoveredClausesFromStorage$.subscribe({
      next: (values) => {
        this.recoverOwnersFromStorage();
      }
    });
    this.subscriptions.add(recoveredClausesFromStorage$);
    const recoveredClausesFromHistory$ = this.booleanSearchService.recoveredClausesFromHistory$.subscribe({
      next: ([clauses, savedHistory]) => {
        if (savedHistory?.additional_params?.owners) {
          this.recoverOwnersFromHistory(savedHistory.additional_params.owners as TreeSelectionItem[]);
        }
      }
    });
    this.subscriptions.add(recoveredClausesFromHistory$);
    const isPublication$ = this.storeService.isPublications$.subscribe({
      next: (isPublications) => {
        this.LEGAL_STATUS.find(st => st.value === 'valid').text = isPublications ? 'Granted' : 'Alive';
      }
    });
    this.subscriptions.add(isPublication$);
  }

  ngAfterViewInit(): void {
    const getTags$ = this.tagService.getTags({page_size: 250}).subscribe({
      next: _ => this.customTagsInit()
    });
    this.subscriptions.add(getTags$);
  }

  private customTagsInit() {
    setTimeout(() => {
      this.clauses
      .filter(clause => clause.field?.name === BooleanFieldEnum.TAG)
      .forEach((clause, i) => {
        if (clause.value) {
          clause.valueArr = this.tagService.tags.map(t => {
            return {value: t.name, id: t.id};
          });
        }
      });
    });
  }

  ngOnDestroy(): void {
    this.booleanSearchService.reset();
  }

  ngDoCheck() {
    if (this.booleanSearchService.isInitialClauses() || this.booleanSearchService.areClausesValid(this.storeService.isPublications)) {
      this.validateMessage = null;
    }
  }

  /**
   * Event listener for add boolean fragment button
   */
  onAddClause() {
    this.booleanSearchService.addNewClause(this.storeService.isPublications);
  }

  /**
   * Event listener for remove boolean fragment button
   * @param i index of fragment in clauses array
   */
  onRemoveClause(i: number) {
    this.booleanSearchService.removeClause(i);
    this.changeQuery.emit(true);
  }

  /**
   * getInputErrorClass
   *
   * get error class of single boolean clause
   * @param clause single clause object
   * @param value value string
   * @param checkErrorMessage whether to check error message
   * @returns class string
   */
  getInputErrorClass(clause: Clause, value: string, checkErrorMessage = false): string {
    if (this.isInputValueInvalid(clause, value, checkErrorMessage)) {
      return 'error';
    }

    return '';
  }

  getFieldErrorClass(field: Field): string {
    if (this.validateMessage && !field) {
      return 'error';
    }

    return '';
  }

  getOperatorErrorClass(operator: ClauseOperatorEnum): string {
    if (this.validateMessage && !operator) {
      return 'error';
    }

    return '';
  }

  getFieldTitle(clause: Clause) {
    return clause.field && clause.field.title ? clause.field.title : null;
  }

  onAuthorityItemClicked(clause: Clause, authority: { name: string; id: string }) {
    clause.setValueByAuthority(authority);
    this.validateClause(clause);
  }

  getConjunctions(): Array<ClauseConjunctionEnum> {
    return [ClauseConjunctionEnum.AND, ClauseConjunctionEnum.OR];
  }

  onFieldSelected(clause: Clause, field: Field, inputValueContainer: HTMLDivElement) {
    this.changeQuery.emit(true);

    if (field.isTypeGroup()) {
      return false;
    }

    if (!clause.field || !clause.field.equals(field)) {
      clause.setField(field);
      clause.isFieldValid(this.storeService.isPublications);
    }

    setTimeout(() => {
      inputValueContainer.click();
    }, 100);
  }

  getTitleStyle(field: Field): Object {
    if (field.isTypeGroup()) {
      return {'font-weight': 600, 'padding-left': '0.5rem', 'cursor': 'default'};
    }
  }

  getSelectedImpact(clause: Clause) {
    return this.IMPACT_OPTIONS.find(o => o.value === clause.value);
  }

  getSelectedLegalStatus(clause) {
    return this.LEGAL_STATUS.filter(st => clause.valueArr?.includes(st.value));
  }

  getUnselectedLegalStatus(clause: Clause) {
    return this.LEGAL_STATUS.filter(st => !clause.valueArr?.includes(st.value) &&
      (st.value !== 'pending' || (st.value === 'pending' && this.storeService.isPublications) ));
  }

  getInputClass(clause: Clause, value: string, checkErrorMessage = false): string {
    if (this.isInputValueInvalid(clause, value, checkErrorMessage)) {
      return 'big-input error';
    }

    return 'big-input';
  }

  updateClauseValue(event, clause: Clause, needToValidate: boolean) {
    clause.value = this.cleanClauseValue(event.toString());
    if (needToValidate) {
      this.validateClause(clause);
    } else {
      this.changeQuery.emit(true);
    }
  }

  openTechFieldModal(event: MouseEvent, clause: Clause) {
    this.titleTree = 'Select a tech field';
    this.openTreeModal(event, TreeSelectionDialogComponent, clause, techFieldsToNodes(), false);
  }

  onFocus(event: Event) {
    const element = event.target as HTMLElement;
    const inputs = element.parentElement?.querySelectorAll('input, textarea');
    if (inputs?.length > 0) {
      (inputs[0] as HTMLElement).focus();
    }
  }

  getIndent(n: number, withOpenBracket: boolean = true): number {
    let level = this.booleanSearchService.numberOfOpenBracketsAtPosition(n);
    if (!withOpenBracket) {
      level -= this.clauses[n].openBrackets;
    }
    return level;
  }

  openBrackets(clause: Clause): Array<any> {
    return [].constructor(Math.max(1, clause.openBrackets));
  }

  closeBrackets(clause: Clause): Array<any> {
    return [].constructor(Math.max(1, clause.closeBrackets));
  }

  onSubmit() {
    if(this.clearEmptyClause && !this.booleanSearchService.areClausesEmpty()){
      this.booleanSearchService.cleanBooleanClauses();
    }
    if (!this.booleanSearchService.areClausesValid(this.storeService.isPublications)) {
      this.validateMessage = `<p>Please correct the errors below.</p>`;
      return;
    }
    this.validateMessage = null;
    this.messageAlert = null;
    this.doSubmit.emit(true);
  }

  /**
   * refreshTagInput - refresh and validate the clause value on change in tag input
   */
  public refreshTagInput(clause: Clause) {
    if (this.tagDebounce) {
      clearTimeout(this.tagDebounce);
    }
    if (clause.valueArr) {
      clause.value = clause.valueArr.join(' OR ');
    }
    this.validateClause(clause);
  }

  toggleShowBooleanQuery() {
    this.showBooleanQuery = !this.showBooleanQuery;
  }

  validateClause(clause: Clause) {
    if (!clause) {
      return;
    }
    this.booleanSearchService.addValidatorForClause(clause, this.storeService.isPublications);
    this.changeQuery.emit(true);
  }

  setClauseValue(clause: Clause, val: any, needToValidate: boolean) {
    clause.value = val;
    if (needToValidate) {
      this.validateClause(clause);
    } else {
      this.changeQuery.emit(true);
    }
  }

  onPaste($event, clause: Clause) {
    $event.stopPropagation();
    $event.preventDefault();
    let items = $event.clipboardData.getData('Text').split(/[^A-Za-z0-9\/]/);
    items = items.filter(function (el) {
      if ((clause.field.name === BooleanFieldEnum.CPC || clause.field.name === BooleanFieldEnum.IPC) && el.trim() === 'OR') {
        return false;
      }
      return el.trim().length > 0;
    });
    if (clause.valueArr) {
      clause.valueArr = [...clause.valueArr, ...items];
    } else {
      clause.valueArr = items;
    }

    clause.value = clause.valueArr.join(' OR ');

    this.validateClause(clause);
  }

  onTypedCpcIpcText(item, clause: Clause) {
    if (this.tagDebounce) {
      clearTimeout(this.tagDebounce);
    }
    this.tagDebounce = setTimeout(() => {
      const term = item.isNewTerm ? item.value.trim().replace(/,/g, ' ') : '';
      if (term.length === 0 && item.isNewTerm) {
        return;
      }
      let inputText = '(';
      if (clause.valueArr && clause.valueArr.length > 0) {
        inputText += (clause.valueArr.join('* OR ') + '*').toUpperCase();
        inputText += item.isNewTerm ? ' OR ' : '';
      }
      inputText += term.toUpperCase() + (item.isNewTerm ?  '*)' : ')');

      const query = clause.field.name + clause.operatorForJsonQuery() + inputText ;
      const parse$ = this.booleanSearchService.parse(query, clause, this.storeService.isPublications).subscribe();
      this.subscriptions.add(parse$);
    }, 500);
  }

  /**
   * on keydown in Tag input, detect and add text as tag
   */
  onKeyDown(event, clause: Clause, focusOut: boolean = false) {
    const text = event.target.value.trim().replace(',', '');
    const keySeparator = event.code === 'Space' || event.code === 'Comma';
    const keySubmit = event.ctrlKey && event.keyCode === 13;
    if ((focusOut || keySeparator) && text.length > 0) {
      event.stopPropagation();
      event.preventDefault();
      event.target.value = '';
      clause.valueArr = [...clause.valueArr ? clause.valueArr : [], text];
      clause.value = clause.valueArr.join(' OR ');
      this.validateClause(clause);
    }
    if (keySubmit) {
      this.onSubmit();
    }
  }

  getOpDescription(operator, clause: Clause) {
    if (clause.field && clause.field.type === this.fieldTypeEnum.DATE) {
      switch (operator) {
        case ClauseOperatorEnum.EQUAL:
          return 'Equals';
        case ClauseOperatorEnum.LESS_THAN:
          return 'Before';
        case ClauseOperatorEnum.GREATER_THAN:
          return 'After';
        case ClauseOperatorEnum.NOT_EQUAL:
          return 'Not equals';
      }
    }
    return '';
  }

  invalidTag = (item: string, clause: Clause) => {
    if (clause.errorValue) {
      return clause.errorValue.find(o => o.value.replace(/\*\s*$/, '') === item.toUpperCase())?.reason;
    }
    return null;
  }

  private isInputValueInvalid(clause: Clause, value: string, checkErrorMessage = false): boolean {
    let extraResult = !value;

    if (clause.isAuthorityField() && value) {
      const valueUpper = value.toUpperCase();
      extraResult = AUTHORITY_CODE_NODES.findIndex(o => o.id.toUpperCase() === valueUpper) === -1;
    }

    return !!((this.validateMessage && extraResult)
      || (this.messageAlert && checkErrorMessage && clause.errorMsg)
      || (this.booleanSearchService.clausesContainErrors && clause.errorMsg));
  }


  private openTreeModal(event: Event, component: any, clause: Clause, treeNodes: any, canSelectRootNodes: boolean) {
    event.stopPropagation();

    const modalOptions = {
      scrollable: true,
      windowClass: 'right-side-modal',
    };

    this.modalRef = this.modalService.open(component, modalOptions);

    if (this.modalRef.componentInstance) {
      this.modalRef.componentInstance.title = this.titleTree;
      this.modalRef.componentInstance.treeNodes = treeNodes;
      this.modalRef.componentInstance.rootNodesSelectable = canSelectRootNodes;
    }

    this.modalRef.result.then(
      (result) => {
        clause.value = result;
        this.validateClause(clause);
      }, () => {
      });

  }

  updateTagInputValue(event, clause: Clause) {
    clause.valueArr = event;
    this.refreshTagInput(clause);
    this.changeQuery.emit(true);
  }

  updateTechFieldInputValue(values: TreeSelectionItem[], clause: Clause) {
    clause.valueArr = values.map((val) => val.id.toString());
    this.refreshTagInput(clause);
    this.changeQuery.emit(true);
  }

  updateCorporateEntityInputValue(values: TreeSelectionItem[], clause: Clause, needToValidate: boolean) {
    clause.valueArr = values.map((val) => val.id.toString());
    this.refreshTagInput(clause);
    this.saveOwnersIntoStorage(values);
    this.recoverOwnersFromStorage();
    if (needToValidate) {
      this.validateClause(clause);
    } else {
      this.changeQuery.emit(true);
    }
  }

  getSelectedTags(clause: Clause,) {
    if (!clause.value) {
      return [];
    }
    return this.tagService.tags.filter(t => t.id.toString() === clause.value.toString());
  }

  shouldDisplayField(clause: Clause, field: Field): boolean {
    if (!clause.isInitial() && clause.field.equals(field)) {
      return true;
    }

    if (field.isGroupOf(clause.field)) {
      return true;
    }

    if (this.storeService.isPublications){
      return !(field.isExcludedFieldForPublicationSearch())

    } else {
      return !(field.isExcludedFieldForFamilySearch())
    }


    
  }

  onTagsChanged(clause: Clause, tags: TagModel[], needToValidate: boolean) {
    if (tags?.length) {
      const oldTagId = clause.value?.toString();
      const addedTag = tags.find(t => t.id.toString() !== oldTagId);
      clause.value = addedTag?.id?.toString();
      clause.valueArr = this.tagService.tags.map(t => {
        return {value: t.id.toString(), id: t.id};
      });
    } else {
      clause.value = '';
    }

    if (needToValidate) {
      this.validateClause(clause);
    } else {
      this.changeQuery.emit(true);
    }
  }

  private saveOwnersIntoStorage(values: TreeSelectionItem[]) {
    const storedItems = this.getStoredOwnersFromStorage();

    for (let newItem of values) {
      const index = storedItems.findIndex(o => o.id === newItem.id);
      if (index > -1) {
        storedItems.splice(index, 1);
      }
      storedItems.push(newItem);
    }

    this.cleanOwnersInStorage(storedItems);

    window.localStorage.setItem('selected_owners_boolean_search', JSON.stringify(storedItems));
  }

  private cleanOwnersInStorage(storedItems: TreeSelectionItem[]) {
    const ownerIds = this.booleanSearchService.ownerIdsOfClauses;
    for (let value of storedItems) {
      if (!ownerIds.includes(value.id)) {
        const index = storedItems.findIndex(o => o.id === value.id);
        storedItems.splice(index, 1);
      }
    }
  }

  private getStoredOwnersFromStorage(): TreeSelectionItem[] {
    const storedValue = window.localStorage.getItem('selected_owners_boolean_search');
    return storedValue ? JSON.parse(storedValue) as TreeSelectionItem[] : [];
  }

  private recoverOwnersFromStorage() {
    const storedOwners = this.getStoredOwnersFromStorage();
    this.recoverOwners(storedOwners);
    this.updateSelectedOwnersOfClauses();
  }

  private recoverOwnersFromHistory(owners: TreeSelectionItem[]) {
    this.recoverOwners(owners);
    this.updateSelectedOwnersOfClauses();
  }

  private recoverOwners(owners: TreeSelectionItem[]) {
    const ownerClauses = this.clauses.filter(clause => clause.field?.name === BooleanFieldEnum.OWNER_IDS);

    for (let clause of ownerClauses) {
      const clauseValue = clause.value?.toString() || '';
      const values = clauseValue.split(' OR ').map(o => o.trim());
      this.ownersPerClauses[clauseValue] = owners.filter(o => values.includes(o.id.toString()));
    }
  }

  private updateSelectedOwnersOfClauses() {
    if (this.storeService.isBooleanSearchStore()) {
      const booleanStoreServices = this.storeService as BooleanSearchStoreService;
      const ownerIds = this.booleanSearchService.ownerIdsOfClauses;
      booleanStoreServices.selectedOwnersOfClauses = this.getStoredOwnersFromStorage().filter(o => ownerIds.includes(o.id));
    }
  }

  private cleanClauseValue(value: string) {
    return value
      .replace(/\s+/g, ' ')
      .trim();
  }

  openBooleanTooltip(clause: Clause) {
    if (clause) {
      const url = '/boolean-tooltip?field=' + clause.field.name.toString().toLowerCase();
      this.openCenteredWindow(url, 'Boolean tips', 800, 900);
    }
  }

  private openCenteredWindow(url: string, windowName: string, desiredWidth: number, desiredHeight: number) {
    const screenWidth = window.screen.availWidth;
    const screenHeight = window.screen.availHeight;

    const maxWidth = Math.floor(screenWidth * 0.8);
    const maxHeight = Math.floor(screenHeight * 0.8);

    const width = Math.min(desiredWidth, maxWidth);
    const height = Math.min(desiredHeight, maxHeight);

    const left = Math.round((screenWidth - width) / 2);
    const top = Math.round((screenHeight - height) / 2);

    const features = [
      `width=${width}`,
      `height=${height}`,
      `left=${left}`,
      `top=${top}`,
      'resizable=yes',
      'scrollbars=yes',
      'status=yes'
    ].join(',');

    const newWindow = window.open(url, '', features);

    if (newWindow) {
      newWindow.document.title = windowName;
      newWindow.onload = () => {
        newWindow.document.title = windowName;
      };
      setTimeout(() => {
        newWindow.moveTo(left, top);
        newWindow.document.title = windowName;
      }, 1000);
    }

    return newWindow;
  }
  onFieldSelector(event){
    setTimeout(() => {
      const el = document.querySelector('.field-selector.show .dropdown-item.active');
      if(event && el){
        const scrollTop = $('.field-selector.show').find('.dropdown-item.active').position().top;
        $('.field-selector.show').scrollTop(scrollTop<32? scrollTop : scrollTop-32);
      }
    });
  }
}
