import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { Router } from '@angular/router';
import { BooleanSearchService, BooleanSearchStoreService, BooleanTemplateService } from '@core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BooleanSearchLogicComponent } from '../boolean-search-logic/boolean-search-logic.component';
import { Subscription } from 'rxjs';
import { SaveTemplateDialogComponent } from '../save-template-dialog/save-template-dialog.component';
import { RecallTemplateDialogComponent } from '../recall-template-dialog/recall-template-dialog.component';

@Component({
  selector: 'app-boolean-query-options',
  templateUrl: './boolean-query-options.component.html',
  styleUrls: ['./boolean-query-options.component.scss']
})
export class BooleanQueryOptionsComponent implements OnD<PERSON>roy {

  @Input() advancedMode = false;
  @Input() isBooleanSearch: boolean = true;
  @Output() advancedModeChange = new EventEmitter<boolean>();

  private subscriptions = new Subscription();

  constructor(
    public booleanSearchService: BooleanSearchService,
    public booleanSearchStoreService: BooleanSearchStoreService,
    private booleanTemplateService: BooleanTemplateService,
    private modalService: NgbModal,
    private router: Router,
  ) { }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onSearchLogic(event) {
    const modal = this.modalService.open(BooleanSearchLogicComponent, {size: 'md'});
    modal.result.then(response => { }, reason => {
      console.log(reason);
    });
  }

  openSaveTemplateDialog(): void {
    if (this.disableAdvancedSearchButton() && this.disableSearchButton()) {
      return;
    }
    const modalRef = this.modalService.open(SaveTemplateDialogComponent, { size: 'md' });
    this.booleanTemplateService.savedTemplate.content = this.advancedMode ?
                                  this.booleanSearchService.search_input :
                                  JSON.stringify(this.booleanSearchService.buildPayload(this.booleanSearchStoreService.isPublications).search_fields);
    this.booleanTemplateService.savedTemplate.type = this.advancedMode ? 'ADVANCED' : 'STANDARD';
  }

  recallTemplateDialog(): void {
    const modalRef = this.modalService.open(RecallTemplateDialogComponent, { size: 'lg' });
    const onLoad$ = modalRef.componentInstance.onLoad.subscribe({
      next: (template) => {
        if (template.type === 'STANDARD') {
          this.booleanSearchService.filterClauses.shift();
          const clausesTmp = BooleanSearchService.makeClausesFromSearchFields(JSON.parse(template.content));
          if (clausesTmp.length > 0) {
            if(this.isBooleanSearch){
              this.router.navigateByUrl('/', {skipLocationChange: false}).then(() => {
                this.router.navigate(['boolean']);
                this.booleanSearchService.setClauses(clausesTmp);
              });
            } else {
              this.advancedModeChange.emit(false);
              this.booleanSearchService.setClauses(clausesTmp);
            }
          }
        } else {
          this.advancedModeChange.emit(true);
          this.booleanSearchService.search_input = template.content;
          if(this.isBooleanSearch){
            this.router.navigateByUrl('/', {skipLocationChange: true}).then(() => {
              this.router.navigate(['boolean/advancedMode']);
            });
          }
        }
        modalRef.close();
      }
    });
    this.subscriptions.add(onLoad$);
  }

  disableAdvancedSearchButton() {
    return !this.advancedMode || (!this.booleanSearchService.search_input || this.booleanSearchService.disableAdvancedModeSearch);
  }

  disableSearchButton() {
    return this.advancedMode || !this.booleanSearchService.areClausesValid(this.booleanSearchStoreService.isPublications);
  }
}
