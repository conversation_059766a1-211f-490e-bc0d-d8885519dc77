<div class="modal-header">
    <div class="modal-title">
        Search logic
        <div class="sub-title">Please select a logic for search:</div>
    </div>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
    <div class="d-flex flex-row justify-content-center" *ngIf="!isPrecessing">
        <label class="radio-container" [ngClass]="{checked: selectedOption === booleanSearchLogicEnum.DEFAULT}"
               (click)="setBooleanSearchLogic(booleanSearchLogicEnum.DEFAULT)">
            <img src="assets/images/search-logic-default.png" />
            <span class="radio-label">
                A or B and C
            </span>
        </label>

        <label class="radio-container" [ngClass]="{checked: !selectedOption || selectedOption === booleanSearchLogicEnum.MATHEMATICAL}"
               (click)="setBooleanSearchLogic(booleanSearchLogicEnum.MATHEMATICAL)">
            <img src="assets/images/search-logic-second.png" />
            <span class="radio-label">
                A or (B and C)<br>
                Default
            </span>
        </label>
    </div>

    <div class="d-flex justify-content-center mt-3" *ngIf="isPrecessing">
        <img src="assets/images/octimine_blue_spinner.gif">
    </div>
</div>
<div class="modal-footer">
    <div class="d-flex justify-content-end">
        <button class="btn btn-primary-outline btn-lg" [disabled]="isPrecessing" (click)="activeModal.dismiss()">Cancel</button>
        <button class="btn btn-primary btn-lg ms-2" [disabled]="selectedOption === booleanSearchLogic || isPrecessing" (click)="onSave()">Save</button>
    </div>
</div>
