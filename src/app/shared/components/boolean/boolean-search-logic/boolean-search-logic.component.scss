@import 'scss/layout2021/variables';

.sub-title{
    font-family: $font-open-sans-regular;
    font-size: .875rem;
    margin-top: 10px;
}
.radio-container {
    margin: 1rem;
    position: relative;
    font-family: $font-open-sans-semi-bold;
    width: 150px;
    width: 156px;
    box-shadow: 0px 3px 6px #00000029;
    border: 1px solid $brand-green;
    color: $brand-green;
    border-radius: 3px;
    opacity: 1;
    text-align: center;
    padding-top: 15px;
    padding-bottom: 15px;
    cursor: pointer;
    transition: all ease-in-out .2s;

    &:hover, &.checked {
        background: $brand-green-pressed 0% 0% no-repeat padding-box;
        box-shadow: 0px 3px 6px #00000029;
        border: 1px solid $brand-green-pressed;
        color: $color-text-01;
    }
    > img{
        display: block;
        margin: 0 auto;
    }
}