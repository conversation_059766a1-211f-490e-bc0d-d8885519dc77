import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideMatomo } from 'ngx-matomo-client';
import { BooleanSearchLogicComponent } from './boolean-search-logic.component';

describe('BooleanSearchLogicComponent', () => {
  let component: BooleanSearchLogicComponent;
  let fixture: ComponentFixture<BooleanSearchLogicComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BooleanSearchLogicComponent ],
      imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
      providers: [ NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BooleanSearchLogicComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
