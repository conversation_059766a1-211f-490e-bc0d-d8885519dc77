import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { BooleanSearchLogicEnum, UserService } from '@core/services';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-boolean-search-logic',
  templateUrl: './boolean-search-logic.component.html',
  styleUrls: ['./boolean-search-logic.component.scss']
})
export class BooleanSearchLogicComponent implements OnInit, OnDestroy {
  isPrecessing: boolean;
  selectedOption: BooleanSearchLogicEnum;

  booleanSearchLogicEnum = BooleanSearchLogicEnum;

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private userService: UserService,) {
  }

  get booleanSearchLogic() {
    return this.userService.getUISetting('booleanSearchLogic', BooleanSearchLogicEnum.MATHEMATICAL);
  }

  ngOnInit(): void {
    this.selectedOption = this.booleanSearchLogic;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  setBooleanSearchLogic(option: BooleanSearchLogicEnum) {
    this.selectedOption = option;
  }

  onSave() {
    this.isPrecessing = true;
    const updateUISettings$ = this.userService.updateUISettings({booleanSearchLogic: this.selectedOption}).subscribe({
      next: (data) => {
        this.isPrecessing = false;
        this.activeModal.close();
      },
      error: (error)=> {
        this.isPrecessing = false;
        console.error(error);
      }
    });
    this.subscriptions.add(updateUISettings$);
  }
}
