<div class="modal-header">
  <div class="modal-title">
    <span>Recall queries</span>
    <span class="caption">Please select a query to load</span>
  </div>

  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body px-0">
  <div *ngIf="!loading">
    <div class="col-12" *ngIf="queries?.length == 0">
      <app-alert type="info" message="No saved queries yet" ></app-alert>
    </div>
    <div class="box-query py-4 d-flex align-items-center" *ngFor="let query of queries;" >
      <div class="data-query" (click)="loadQuery(query)">
        <div class="d-flex justify-content-between">
          <span>{{ query.title }}</span>
          <span class="label-date">{{ query.created_at | dateFormat: 'ShortDate'}}</span>
        </div>
        <div class="alert alert-info overflow-hidden p-1 m-0">
          <span>{{ query.type === 'STANDARD' ? query.standardContent : query.content }}</span>
        </div>
      </div>
      <div class="btn-remove ms-3 mt-4" (click)="removeQuery(query.id)"><i class="fas fa-window-close"></i></div>
    </div>
  </div>
  <div class="d-flex justify-content-center" *ngIf="loading">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</div>
<div class="modal-footer">
  <button (click)="activeModal.dismiss('Cancel')" class="btn btn-primary-outline btn-md" type="button">Back</button>
</div>
