@import 'scss/layout2021/variables';

.label-text {
  color: $label-color;
}

.modal-title {
  span {
    display: block;
  }
  .caption {
    font-size: 0.9rem;
    font-family: 'Open Sans Regular';
  }
}

.modal-body {
  max-height: 600px;
  overflow: auto;
  .box-query {
    padding: 5px 10px;
    .label-date {
      color: $label-color;
    }
    span {
      font-family: Open Sans Semi Bold !important;
      font-size: 0.9rem;
      color: $color-text-04;
    }
    .data-query {
      width: 94%;
    }
    .btn-remove {
      display: none;
      font-size: 1.2rem;
      color: $red;
    }
    &:hover {
      cursor: pointer;
      background: #F5F7FA;
      .btn-remove {
        display: block;
      }
    }
  }
  .overflow-hidden {
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

