import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';
import { RecallTemplateDialogComponent } from './recall-template-dialog.component';

describe('RecoverQueryDialogComponent', () => {
  let component: RecallTemplateDialogComponent;
  let fixture: ComponentFixture<RecallTemplateDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [SharedModule, NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ],
      imports: [ HttpClientTestingModule, RouterModule.forRoot([]) ],
      declarations: [RecallTemplateDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RecallTemplateDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
