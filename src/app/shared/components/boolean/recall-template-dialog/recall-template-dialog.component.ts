import { Component, OnInit, EventEmitter, OnD<PERSON>roy } from '@angular/core';
import { BooleanSearchService, BooleanTemplateService } from '@core';
import { BooleanTemplate } from '@core/services/boolean-template/type';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-recall-template-dialog',
  templateUrl: './recall-template-dialog.component.html',
  styleUrls: ['./recall-template-dialog.component.scss']
})
export class RecallTemplateDialogComponent implements OnInit, OnDestroy {

  queries: BooleanTemplate[];
  onLoad = new EventEmitter(null);
  loading: boolean;

  private subscriptions = new Subscription();

  constructor(public activeModal: NgbActiveModal,
    private booleanTemplateService: BooleanTemplateService,
    private booleanSearchService: BooleanSearchService
  ) { }

  public ngOnInit(): void {
    this.loading = true;
    const listTemplate$ = this.booleanTemplateService.listTemplate()
      .pipe(
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: ({data}) => {
          this.queries = data.boolean_templates;
          this.queries.forEach(query => {
            if (query.type === 'STANDARD') {
              const clauses = BooleanSearchService.makeClausesFromSearchFields(JSON.parse(query.content));
              query['standardContent'] = BooleanSearchService.buildBooleanQueryFromClauses(clauses);
            }
          });
        }
      });
    this.subscriptions.add(listTemplate$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  loadQuery(template: BooleanTemplate): void {
    this.onLoad.emit(template);
  }

  removeQuery(idTemplate: number): void {
    this.loading = true;
    const removeTemplate$ = this.booleanTemplateService.removeTemplate(idTemplate)
      .pipe(
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: () => {
          this.queries = this.queries.filter(qry => qry.id !== idTemplate);
          if (this.booleanTemplateService.savedTemplate.id === idTemplate) {
            this.booleanTemplateService.resetTemplate();
          }
        }
      });
    this.subscriptions.add(removeTemplate$);
  }
}
