<div class="modal-header">
  <div class="modal-title">
    <span>Save query</span>
    <span class="caption">Please name your query</span>
  </div>

  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
  <!-- <app-alert type="danger" [message]="errorMessage" *ngIf="errorMessage"></app-alert> -->

  <div class="d-flex flex-column justify-content-between">
      <label class="label-text form-label">Query name <sup style="color: #FF4D55">*</sup></label>
      <input class="form-control mb-2" placeholder="Query name" [(ngModel)]="booleanTemplateService.savedTemplate.title" maxlength="255"/>
  </div>

</div>
<div class="modal-footer">
  <button (click)="activeModal.dismiss('Cancel')" class="btn btn-primary-outline btn-md" type="button">Back</button>
  <button (click)="saveQuery()" class="btn btn-primary btn-md" type="button" [disabled]="!booleanTemplateService.savedTemplate.title">Save</button>
</div>
