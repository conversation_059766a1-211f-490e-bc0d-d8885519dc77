import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';

import { SaveTemplateDialogComponent } from './save-template-dialog.component';

describe('SaveQueryDialogComponent', () => {
  let component: SaveTemplateDialogComponent;
  let fixture: ComponentFixture<SaveTemplateDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [SharedModule, NgbActiveModal],
      imports: [HttpClientTestingModule, RouterModule.forRoot([])],
      declarations: [ SaveTemplateDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SaveTemplateDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
