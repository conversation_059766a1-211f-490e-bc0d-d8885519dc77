import { Component, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { BooleanTemplateService, ToastService, ToastTypeEnum } from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-save-template-dialog',
  templateUrl: './save-template-dialog.component.html',
  styleUrls: ['./save-template-dialog.component.scss']
})
export class SaveTemplateDialogComponent implements OnDestroy {

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    public booleanTemplateService: BooleanTemplateService,
    private toastService: ToastService
  ) {
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  saveQuery(): void {
    const {id, created_at, updated_at, ...payload} = this.booleanTemplateService.savedTemplate;
    const save$ = this.booleanTemplateService.save(payload, id)
      .subscribe({
        next: ({data}) => {
          this.booleanTemplateService.savedTemplate.title = '';
          this.activeModal.dismiss();
          this.toastService.show({
            type: ToastTypeEnum.SUCCESS,
            header: 'The boolean query template saved',
            body: 'The boolean query template has been saved successfully',
            delay: 5000
          });
        },
        error: () => {
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Error in saving the boolean query template',
            body: 'An error occurred while saving the boolean query template',
            delay: 5000
          });
        }
      });
    this.subscriptions.add(save$);
  }
}
