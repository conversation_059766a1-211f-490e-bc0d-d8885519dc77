<div class="boost-search d-flex justify-content-between mb-3">
    <div class="factors boosted-factors">
        <div class="d-flex justify-content-between w-100 mb-1">
            <label>Keywords</label>
            <label class="clear-all cursor-pointer" (click)="cleanFactor('boost_factors.keywords', 'keywords')" 
                *ngIf="hasFactors('boost_factors.keywords')">Clean all</label>
        </div>
        <ng-select [addTag]="true" [multiple]="true" [selectOnTab]="true" [isOpen]="false" [clearable]="false" id="keywords"
            (keydown.space)="updateTags($event, 'boost_factors.keywords', 'keywords')"
            (focusout)="updateTags($event, 'boost_factors.keywords', 'keywords')"
            [items]="form?.get('boost_factors.keywords').value" 
            [(ngModel)]="keywords"
            (change)="updateControl({
                control: 'boost_factors.keywords',
                value: $event
            })" 
            placeholder="Type your keywords to enhance your search."
            class="boosted-fields row-direction">
        </ng-select>
    </div>
    <div class="factors">
        <div class="d-flex justify-content-between w-100 mb-1">
            <label>IPC codes</label>
            <label class="clear-all cursor-pointer" (click)="cleanFactor('boost_factors.ipc_codes', 'ipcCodes')"
                *ngIf="hasFactors('boost_factors.ipc_codes')">Clean all</label>
        </div>
        <app-classification-filter-browser  id="ipc_codes" classification="ipc" [selectedItems]="ipcCodes" id="ipcCodes"
            (keydown.space)="updateTags($event, 'boost_factors.ipc_codes', 'ipcCodes')"
            (focusout)="updateTags($event, 'boost_factors.ipc_codes', 'ipcCodes')"
            [chkValidTagFunction]="invalidClassificationTag" (search)="onSearchClassificationFilter($event, 'ipc')"
            classCss="boosted-factors row-direction boosted-fields"
            validTagParam='ipc' [isPlusButtonVisible]="true"
            (changed)="updateControl({
                control: 'boost_factors.ipc_codes',
                value: $event })">
        </app-classification-filter-browser>
    </div>
    <div class="factors">
        <div class="d-flex justify-content-between w-100 mb-1">
            <label>CPC codes</label>
            <label class="clear-all cursor-pointer" (click)="cleanFactor('boost_factors.cpc_codes', 'cpcCodes')"
                *ngIf="hasFactors('boost_factors.cpc_codes')">Clean all</label>
        </div>        
        <app-classification-filter-browser  id="cpc_codes" classification="cpc" [selectedItems]="cpcCodes" id="cpcCodes"
            (keydown.space)="updateTags($event, 'boost_factors.cpc_codes', 'cpcCodes')"
            (focusout)="updateTags($event, 'boost_factors.cpc_codes', 'cpcCodes')"
            [chkValidTagFunction]="invalidClassificationTag" (search)="onSearchClassificationFilter($event, 'cpc')"
            classCss="boosted-factors row-direction boosted-fields"
            validTagParam='cpc' [isPlusButtonVisible]="true"
            (changed)="updateControl({
                control: 'boost_factors.cpc_codes',
            value: $event })">
        </app-classification-filter-browser>
    </div>
</div>