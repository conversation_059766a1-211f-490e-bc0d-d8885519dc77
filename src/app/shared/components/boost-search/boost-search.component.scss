@import 'scss/layout2021/variables';

.boost-search {
    .factors {
        width: 32%;
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 20px;
        label {
            color: $color-text-03;
            &.clear-all {
                color: $color-text-02;
            }
        }

        &::ng-deep {
            .boosted-fields {
                border-radius: 0.5rem;
                background-color: $input-background-color;
                padding: 0.5rem 0.5rem 0.5rem 0.85rem;
                border: 1px solid transparent;
                .ng-select-container {
                    background-color: $input-background-color;
                    border: 1px solid transparent;
                    box-shadow: none;
                }
                .ng-placeholder {
                    padding-top: 0.3rem;
                }
                &:active, &:focus-within {
                    border: 1px solid $input-color;
                }
            }

            .row-direction {
                .ng-value-container {
                    flex-direction: row !important;
                }
            }
        }
    }
}