import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { BOOST_VALUE } from '@core';

@Component({
  selector: 'app-boost-search',
  templateUrl: './boost-search.component.html',
  styleUrls: ['./boost-search.component.scss']
})
export class BoostSearchComponent implements OnInit {

  @Input() form: UntypedFormGroup;

  keywords: Array<String> = [];
  ipcCodes: Array<String> = [];
  cpcCodes: Array<String> = [];
  private errorClassificationFilter = [];
  
  ngOnInit() {
    if (!this.form) {
      return;
    }
    this.keywords = this.form.get('boost_factors.keywords').value.map(vl => vl.term);
    this.ipcCodes = this.form.get('boost_factors.ipc_codes').value.map(vl => vl.term);
    this.cpcCodes = this.form.get('boost_factors.cpc_codes').value.map(vl => vl.term);
  }

  cleanFactor(factor: string, factorList: string) {
    this.form.get(factor).setValue([]);
    this[factorList] = [];
  }

  updateTags(event, factor: string, selectedItems: string) {
    const text = event.target.value.trim();
    if (text.length > 0) {
      event.preventDefault();
      this.form.get(factor).value.push({boost: BOOST_VALUE, term: text});
      this[selectedItems] = [...this[selectedItems], text];
      event.target.value = '';
    }
  }

  updateControl(event) {
    const {control, value} = event;
    this.form.get(control).setValue(value.map(vl => ({ boost: BOOST_VALUE, term: (vl instanceof Object ? vl.label : vl) })));
    if (this.isIpcCpcControl(control)) {
      this[control.includes('ipc') ? 'ipcCodes' : 'cpcCodes'] = value;
    }
  }

  isIpcCpcControl(control) {
    return control.includes('ipc') || control.includes('cpc');
  }

  hasFactors(factor) {
    return this.form?.get(factor)?.value.length > 0;
  }

  onSearchClassificationFilter(input, field: string) {
    const valueArray = input.value.split(',');
    valueArray.filter(item => !input.results.includes(item.toUpperCase())).forEach(item => {
      this.errorClassificationFilter.push({field, value: item, message: 'Invalid classification code'});
    });
  }

  invalidClassificationTag = (item: string, field: string) => {
    if (this.errorClassificationFilter.length) {
      const error = this.errorClassificationFilter.find(o => o.field === field && o.value === item);
      return error?.message ?? error?.value;
    }
    return null;
  }
}
