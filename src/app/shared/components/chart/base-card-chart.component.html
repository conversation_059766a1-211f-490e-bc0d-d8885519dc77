<div class="dashboard-arrangement-layer text-end">
  <a class="btn btn-outline-danger" (click)="removeFromDashboard()" href="javascript:void(0)"><i class="fa-solid fa-xmark me-2"></i> Remove</a>
  <div class="dashboard-arrangement-icon">
    <span class="icon-up">&#8593;</span>
    <span class="icon-sides">&#8592; &#8594;</span>
    <span class="icon-down">&#8595;</span>
  </div>
</div>
<div class="chart-item d-flex flex-column justify-content-between" [class]="chartClass" [ngClass]="{'exportable': exportable, 'chart-single-column': storeService.singleChartColumn}"
     [attr.data-export-title]="title" [attr.data-export-css-selector]="exportCssSelector"
     [ngStyle]="chartItemStyle">
    <div class="d-flex justify-content-between align-items-center chart-header" *ngIf="showHeaderBar">
        <div class="box-title">{{ title }} <app-tooltip [tooltipText]="tooltipText" [tooltipTitle]="title" [popupText]="getPopupContent()" [urlNewTab]="urlTooltipArticle"  labelClass="labelTooltip" tooltipIconSize="sm"></app-tooltip></div>
        <div class="d-flex justify-content-end align-items-center" *ngIf="showTitleOption">
          <div class="chart-header-option position-relative" ngbDropdown [autoClose]="true">
            <span class="caret-off">
              <i class="fa-solid fa-ellipsis-vertical chart-header-option-icon"></i>
            </span>
            <div class="dropdown-menu-end" ngbDropdownMenu>
              <a class="dropdown-item px-3" href="javascript:void(0)" (click)="openZoom()" *ngIf="showZoomOption"><i class="fa-solid fa-maximize"></i> Expand Chart</a>
              <a class="dropdown-item px-3" href="javascript:void(0)" (click)="removeFromDashboard()" *ngIf="showFavoriteOption && isCustomChartCategory" placement="left" container="body"
                 ngbTooltip="By clicking the button, you are deselecting this chart for your individual chart dashboard. Note: the order of clicking determines the order in your personal chart dashboard. To edit your personal chart dashboard and the order of the chart click the chart dashboard icon">
                <i class="fa-solid fa-gauge-high"></i> Remove from my dashboard</a>
              <div class="dropdown-divider" *ngIf="showZoomOption || showFavoriteOption"></div>
              <a class="dropdown-item disabled px-3" href="javascript:void(0)">Download as:</a>
              <div class="dropdown-item px-3 d-flex justify-content-between" >
                <div class="d-flex justify-content-start align-items-center" *ngFor="let type of ['JPEG', 'PNG', 'PDF', 'SVG']; let last = last;" [ngClass]="{'me-1': !last}">
                  <ng-container [ngTemplateOutlet]="downloadingSpinnerTemplate" *ngIf="isDownloading(type)"></ng-container>
                  <a href="javascript:void(0)" (click)="onDownloadChart(type)" [ngClass]="{'ms-1': isDownloading(type)}">{{type}}</a>
                </div>
              </div>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item px-3 d-flex justify-content-start align-items-center" href="javascript:void(0)" (click)="onDownloadChart('PRINT')">
                <i class="fa-solid fa-print" *ngIf="!isDownloading('PRINT')"></i>
                <ng-container [ngTemplateOutlet]="downloadingSpinnerTemplate" *ngIf="isDownloading('PRINT')"></ng-container>
                <div class="ms-2">Print chart</div>
              </a>
            </div>
          </div>
        </div>
    </div>
    <div class="graph-wrap d-flex justify-content-center align-items-center flex-fill" data-chart-area="similarities"
         [ngStyle]="{'height': 'calc( 100% - ' + (sliderValue && sliderOptions ? '130px' : '55px') + ' )'}">
      <div [id]="chartName" [hidden]="isCalculating || isEmpty" class="chart-content w-100 h-100 p-2 position-relative" [class.has-footer]="sliderValue && sliderOptions">
        <ng-content></ng-content>
      </div>

      <div *ngIf="isEmpty && !isCalculating" class="d-flex flex-column justify-content-center align-items-center h-100">
        <div class="text-center mt-2">
          Not enough data to display this chart
        </div>
      </div>

      <div class="spinner" [hidden]="!isCalculating" [class.has-footer]="sliderValue && sliderOptions">
        <span class="spinner-wrapper"></span>
        <img [ngSrc]="spinner" alt="Please hold" width="160" height="160">
      </div>
    </div>
    <div class="d-flex justify-content-between align-items-center chart-footer py-0" *ngIf="sliderValue && sliderOptions">
      <div class="chart-slider w-100 text-center">
          <ngx-slider [(value)]="sliderValue" [options]="sliderOptions" [(highValue)]="highValue" (userChangeEnd)="onUserChangeEnd($event)"></ngx-slider>
          <div class="slider-name text-center" *ngIf="sliderOptions.name">{{ sliderOptions.name }}</div>
      </div>
    </div>
</div>

<ng-template #downloadingSpinnerTemplate>
  <img [ngSrc]="spinner" alt="Download charts" ngbTooltip="Downloading charts" container="body" width="20" height="20" />
</ng-template>
