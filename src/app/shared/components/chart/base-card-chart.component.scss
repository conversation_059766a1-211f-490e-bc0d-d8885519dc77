@import 'scss/layout2021/variables';

.chart-item {
  height: 100%;
  width: 100%;
  position: relative;
  border: 1px solid #dad8d6;
  overflow: hidden;
  border-radius: .5rem;

  .chart-header,
  .chart-footer {
    color: $brand-dark;
    padding: 12px;
  }

  .chart-header {
    position: relative;
    font-size: 1rem;
    font-family: 'Open Sans Semi Bold';
    color: #0F2C35;
  }
  .chart-header-option{
    font-size: 14px;
    a{font-size: 14px;}
    .dropdown-menu{
      font-size: 14px;
      top: 97%;
      min-width: 240px;
      margin-top: 0;
      &.dropdown-menu-end{
        right: 0;
        left: unset;
      }
    }
    &-icon{
      border-radius: 3px;
      line-height: 1.5rem;
      padding: 0 10px;
    }
    &:hover {
      .chart-header-option-icon{
        background-color: #D4E4E9;
      }
      .dropdown-menu{
        display: block;
      }
    }
  }

  .graph-wrap {
    border-top: 1px solid $scrollbar-background;
    background-color: $dropdown-item-background;
  }

  .chart-footer {
    font-size: .750rem;
    background-color: $scrollbar-background;
    border-top: 1px solid $scrollbar-background;
  }

  highcharts-chart {
    width: 100% !important;
    height: 100% !important;
  }
  .spinner{
    height: 458px;
    &.has-footer{
      height: 400px;
    }
    &-wrapper{
      display: inline-block;
      height: 100%;
      vertical-align: middle;
    }
  }

  .chart-slider {
    .slider-name {
      margin-top: -10px;
      margin-bottom: 10px;
    }
  }
}
.chart-content {
  min-width: 100%;
}
.dashboard-arrangement-layer{
  display: none;
  position: absolute;
  padding: 12px;
  border-radius: 5px;
  width: calc(100% - 30px);
  height: 100%;
  cursor: move;
  z-index: 2;
  .dashboard-arrangement-icon{
    max-width: 110px;
    vertical-align: middle;
    margin: calc(40% - 126px) auto 0;
    .icon-up, .icon-down, .icon-sides{
      display: block;
      font-size: 3rem;
      line-height: 2rem;
      color: $brand-green;
      text-align: center;
    }
  }
}

.dashboard-chart-item.col-md-12 {
  .dashboard-arrangement-icon {
    margin: calc(20% - 126px) auto 0 !important;
  }
}
