import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  Type,
  ViewEncapsulation,
} from '@angular/core';
import { BaseStoreService } from '@core/store';
import { ExportChartService, UserService } from '@core/services';
import { from, Subscription } from 'rxjs';
import { chartsConfig } from '@shared/charts/config';

declare var $: any;

@Component({
  selector: 'app-base-card-chart',
  templateUrl: './base-card-chart.component.html',
  styleUrls: ['./base-card-chart.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class BaseCardChartComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() storeService: BaseStoreService;
  @Input() title: string;
  @Input() chartName: string;
  @Input() tooltipText: string;
  @Input() popupText: string;
  @Input() chart?: any;
  @Input() component: Type<any>;
  @Input() chartClass: string = 'chart-item';
  @Input() showTitleOption: boolean = true;
  @Input() showFavoriteOption: boolean = true;
  @Input() showZoomOption: boolean = true;
  @Input() showHeaderBar: boolean = true;
  @Input() dashboardName: string;
  @Input() chartItemHeight: string = null;
  @Input() urlTooltipArticle: string;
  @Input() isEmpty = false;

  /**
   * whether to show footer bar TODO: add chart slider hear
   */
  @Input() sliderOptions?: any;
  @Input() sliderValue?: number;
  @Output() sliderValueChange: EventEmitter<number> = new EventEmitter();
  @Input() highValue?: number;
  @Output() highValueChange: EventEmitter<number> = new EventEmitter();
  @Output() sliderChanged: EventEmitter<any> = new EventEmitter();
  @Input() spinner = 'assets/images/octimine_blue_spinner.gif';
  @Input() exportable = true;
  @Input() exportCssSelector = '.chart-content';
  @Output() chartExport: EventEmitter<string> = new EventEmitter();

  addingFavorite: boolean = false;
  dashboardCharts = [];
  chartsConfig = chartsConfig;
  chartItemStyle = {};

  private subscriptions = new Subscription();

  constructor(
    private userService: UserService,
    private exportChartService: ExportChartService,
    private elementRef: ElementRef
  ) {
  }

  get isCalculating(): boolean {
    return this.storeService.isCalculatingCharts;
  }

  get isCustomChartCategory(): boolean {
    return this.storeService.isCustomChartCategory();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.chart && changes.chart.currentValue) {
      this.chart = changes.chart.currentValue;
    }
    if (changes.sliderOptions && changes.sliderOptions.currentValue) {
      this.sliderOptions = changes.sliderOptions.currentValue;
    }
    if (changes.sliderValue && changes.sliderValue.currentValue) {
      this.sliderValue = changes.sliderValue.currentValue;
    }
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: u => {
        if (u && u.profile) {
          this.dashboardCharts = this.userService.getChartCategories(this.storeService.chartDashboardType);
        }
      }
    });
    this.subscriptions.add(user$);

    const singleChartColumn$ = this.storeService.singleChartColumn$.subscribe({
      next: () => {
        this.chartItemStyle = this.getChartItemStyle();
      }
    });
    this.subscriptions.add(singleChartColumn$);

    const patentListViewMode$ = this.storeService.patentListViewMode$.subscribe({
      next: () => {
        this.chartItemStyle = this.getChartItemStyle();
      }
    });
    this.subscriptions.add(patentListViewMode$);

    this.chartItemStyle = this.getChartItemStyle();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngAfterViewInit() {
    const chartFilterDisabled$ = this.storeService.chartFilterDisabled$.subscribe({
      next: disabled => {
        $('[data-chart-area="similarities"]').css('pointer-events', disabled ? 'none' : 'auto');
      }
    });
    this.subscriptions.add(chartFilterDisabled$);
  }

  getPopupContent(): string {
    return `<h3>${this.title}</h3><div>${this.popupText}</div>`;
  }

  openZoom() {
    if (this.component) {
      this.storeService.expandingChartComponent = this.component;
    }
  }

  removeFromDashboard() {
    this.addingFavorite = true;
    this.storeService.removeChart = {name: this.chartName, title: this.title};
    setTimeout(() => this.addingFavorite = false, 1000);
  }

  async onDownloadChart(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT') {
    const exportingEle = this.elementRef.nativeElement.querySelector(this.exportCssSelector);
    await this.exportChartService.exportChart(exportingEle, type, 2, this.getDownloadChartName(), true,
      () => {
        document.documentElement.style.cursor = 'wait';
      }, () => {
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      }, () => {
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      });
  }

  onUserChangeEnd(event) {
    this.sliderValueChange.emit(this.sliderValue);
    if (this.highValue) {
      this.highValueChange.emit(this.highValue)
    }
    this.sliderChanged.emit(event);
  }

  isDownloading(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT'): boolean {
    return this.exportChartService.isExporting(this.getDownloadChartName(), type, true);
  }

  private getChartItemStyle() {
    let height;

    switch (true) {
      case !!this.chartItemHeight:
        height = this.chartItemHeight;
        break;
      case (!!this.storeService.singleChartColumn && !this.storeService.isCombinedMode):
        height = this.chartsConfig.heightOnSingleColumn + 'px';
        break;
      default:
        height = this.chartsConfig.heightDefault + 'px';
    }

    return height ? {'height': height} : {};
  }

  private getDownloadChartName(): string {
    return this.storeService.getCapitalizedStoreName() + ' - ' + this.title;
  }
}
