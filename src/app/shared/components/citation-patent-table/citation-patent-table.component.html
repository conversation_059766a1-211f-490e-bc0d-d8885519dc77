<div class="overflow-auto">
  <table *ngIf="!searchingTable else loaderTable"
         id="table-results" class="table table-condensed publication-table w-100 table-hover">
    <thead class="w-100">
    <tr>
      <th *ngIf="!isPatentViewerSource" width="30">
        <label class="checkbox m-0 p-0">
          <input type="checkbox" (change)="selectAllPatent($event)" [checked]="selectedPublications.length === patents?.length">
          <span class="no-text">&nbsp;</span>
        </label>
      </th>
      <th (click)="onHeadClick(null)" *ngIf="!isPatentViewerSource" width="30">
        <a>#</a>
      </th>
      <th>
        <a>Type</a>
      </th>
      <th>
        <a>Title</a>
      </th>
      <th (click)="onHeadClick('raw_publication_number')" width="100">
        <a>Publ. No.</a>
      </th>
      <th (click)="onHeadClick('raw_publication_number')" width="100" *ngIf="!isPatentViewerSource">
        <a>Focal</a>
      </th>
      <th (click)="onHeadClick('raw_publication_number')" width="50" *ngIf="!isPatentViewerSource">
        <a>Level</a>
      </th>
      <th (click)="onHeadClick('ipc4')" *ngIf="storeService.showColumn('ipc4') && !isPatentViewerSource" width="100">
        <a>IPC 4</a>
      </th>
      <th (click)="onHeadClick('applicants')" *ngIf="storeService.showColumn('applicants')" width="200">
        <a>Applicants</a>
      </th>
      <!-- <th (click)="onHeadClick('priority_date')" *ngIf="storeService.showColumn('priority_date')" class="text-center" width="130">
        <a>Priority date</a>
      </th> -->
      <th (click)="onHeadClick('impact')" *ngIf="storeService.showColumn('impact') && !isPatentViewerSource" class="text-center" width="80">
        <a>Value</a>
      </th>
      <th width="105" *ngIf="userService.isNotExternalUser()">
        <div (click)="openAll()" class="m-0 p-0 cursor-pointer text-center">
          <span *ngIf="openedPatent.length !== patents?.length" class="caret-down"><i class="fa fa-angle-down"></i></span>
          <span *ngIf="openedPatent.length === patents?.length" class="caret-up"><i class="fa fa-angle-up"></i></span>
        </div>
      </th>
    </tr>
    </thead>
    <tbody *ngFor="let patent of patents | paginate:
    { itemsPerPage: pagination?.page_size,
      id: 'citations-pagination',
      currentPage: pagination?.current_page,
      totalItems: pagination?.total_hits
    }; index as indexi">
    <tr [ngClass]="{'detailed':openedPatent.indexOf(patent.general.docdb_family_id)>-1, 'obfuscated':patent.general?.obfuscated }">
      <td *ngIf="!isPatentViewerSource">
        <label class="checkbox m-0 p-0">
          <input type="checkbox" (change)="selectPatent($event, patent)" *ngIf="!patent.general?.obfuscated"
                 [checked]="selectedPublications.indexOf(patentTableService.getPublicationNumber(patent)) > -1">
          <span class="no-text">&nbsp;</span>
        </label>
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)" class="number-col" nowrap="nowrap" *ngIf="!isPatentViewerSource">
        {{ patent?.general?.rank ? patent?.general?.rank : ( pagination | countSerial:indexi) }}
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)">
        {{patent?.citations[0].direction}}
      </td>
      <td class="text-break" (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)">{{ patent?.bibliographic?.title }}</td>
      <td class="publication-number-col" nowrap="nowrap">
        <a [href]="espacenetService.getWorldwideLinkByPatent(patent)" target="_blank" (click)="userService.isNotExternalUser()">
          <i [ngClass]="getFlagIcon(patentTableService.getPublicationNumber(patent))"></i>
          {{ patentTableService.getPublicationNumber(patent) }}
        </a>
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)" *ngIf="!isPatentViewerSource">
        {{ patentTableService.getFocal(patent) }}
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)" *ngIf="!isPatentViewerSource">
        {{ patentTableService.getLevel(patent) }}
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)"
          *ngIf="storeService.showColumn('ipc4') && !isPatentViewerSource" class="ip4-col">
        {{ patentTableService.getIPC4(patent) }}
      </td>
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)"
          *ngIf="storeService.showColumn('applicants')">{{ patentTableService.getApplicants(patent) }}</td>
      <!-- <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)"
          *ngIf="storeService.showColumn('priority_date')"
          class="text-center">{{
        patent.bibliographic.priority_date | dateFormat: 'ShortDate'
        }}</td> -->
      <td (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)"
          *ngIf="storeService.showColumn('impact') && !isPatentViewerSource"
          class="text-center">
        {{ patentTableService.getAnalyticsValue(patent) }}
      </td>
      <td *ngIf="userService.isNotExternalUser()">
        <div class="d-flex align-items-center justify-content-center gap-spacing-sm">
          <a *ngIf="!patent.general?.obfuscated"
             [routerLink]="['/patent/view', patent.general.docdb_family_id]"
             [state]="{data: linkData}"
             [target]="isPatentViewerSource ? '_blank' : '_self'"
             class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square">
            <span>
              <i class="fa-regular fa-arrow-up-right-from-square"></i>
            </span>
          </a>
          <a href="javascript:void(0)" data-intercom-target="Expand patent"
             (click)="openDetail(patent.general.docdb_family_id, patent.general?.obfuscated)" *ngIf="!patent.general?.obfuscated"
             class="patent-icon preview-patent-icon button-main-secondary-grey button-medium button-square"
             [ngClass]="{'preview-close': openedPatent.indexOf(patent.general.docdb_family_id) > -1}">
            <span *ngIf="openedPatent.indexOf(patent.general.docdb_family_id) === -1"><i class="fa-regular fa-angle-down"></i></span>
            <span *ngIf="openedPatent.indexOf(patent.general.docdb_family_id) > -1"><i class="fa-regular fa-times"></i></span>
          </a>
        </div>
      </td>
    </tr>
    <app-patent-detail *ngIf="openedPatent.indexOf(patent.general.docdb_family_id)>-1"
                       [colspan]="countColumnsShowed()"
                       [ngClass]="'patent-detail-'+indexi" [patent]="patent"
                       class="d-table-row patent-detail-container"
                       [backButtonTitle]="backButtonTitle"
                       [linkData]="linkData" [storeService]="storeService">
    </app-patent-detail>
    </tbody>
  </table>

  <ng-template #loaderTable>
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
  </ng-template>
</div>
