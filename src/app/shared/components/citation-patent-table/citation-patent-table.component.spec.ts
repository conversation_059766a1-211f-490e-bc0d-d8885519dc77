import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CitationPatentTableComponent } from './citation-patent-table.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('ResultsTableComponent', () => {
  let component: CitationPatentTableComponent;
  let fixture: ComponentFixture<CitationPatentTableComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [],
      imports: [SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CitationPatentTableComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
