import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';

import {
  BaseStoreService,
  CitationSearchService,
  Patent,
  PatentService,
  PatentTableService,
  UserService
} from 'app/core';
import { PaginationMetadata } from 'app/core/services/semantic-search/types';
import { Subscription } from 'rxjs';
import { EspacenetService } from '@core/services';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';

@Component({
  selector: 'app-citation-patent-table',
  templateUrl: './citation-patent-table.component.html',
  styleUrls: ['./citation-patent-table.component.scss']
})
export class CitationPatentTableComponent implements AfterViewInit, OnDestroy {
  @Input() patents: Array<Patent>;
  @Input() pagination: PaginationMetadata;
  @Input() isPatentViewerSource: boolean;
  @Input() backButtonTitle: string;
  @Input() storeService: BaseStoreService;
  @Output() sort: EventEmitter<string> = new EventEmitter();
  openedPatent = [];
  searchingTable = false;
  selectedPublications = [];
  private subscriptions = new Subscription();

  constructor(
    public citationService: CitationSearchService,
    public patentTableService: PatentTableService,
    public espacenetService: EspacenetService,
    public userService: UserService,
    public patentService: PatentService,
  ) {
  }

  get linkData() {
    return this.citationService.linkData;
  }

  ngAfterViewInit() {
    const searchingEvent$ = this.storeService.searchingEvent.subscribe({
      next: value => {
        this.searchingTable = value;
      }
    });
    this.subscriptions.add(searchingEvent$);
  }

  onHeadClick(field: string) {
    this.sort.emit(field);
  }

  selectAllPatent(event) {
    this.selectedPublications = this.patentTableService.selectVisiblePatent(event, this.patents, this.storeService);
  }

  selectPatent(event, patent) {
    this.selectedPublications = this.patentTableService.selectPatent(event, patent, this.storeService);
  }

  openDetail(docdb_family_id: number, obfuscated: boolean) {
    this.openedPatent = this.patentTableService.openDetail(docdb_family_id, obfuscated);
  }

  openAll() {
    this.openedPatent = this.patentTableService.openAll(this.patents);
  }

  countColumnsShowed() {
    if (this.storeService.selectedColumnsToShow.length) {
      return this.storeService.selectedColumnsToShow.length + (this.isPatentViewerSource ? 3 : 9);
    }

    if (this.isPatentViewerSource) {
      return 5;
    }

    return 9;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getFlagIcon(publicationNumber: string) {
    return this.patentService.getFlagCssByPublication(publicationNumber, FlagSizeEnum.MD).toLowerCase();
  }
}
