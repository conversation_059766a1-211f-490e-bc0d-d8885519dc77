<div class="flex-fill d-flex classification-select" [ngClass]="classCss">
    <a (click)="openCpcIpcModal($event)" class="button-main-primary button-small button-square align-self-center" *ngIf="isPlusButtonVisible else dropdown">
      <i class="fa-regular fa-plus"></i>
    </a>
    <ng-select [addTag]="true" [multiple]="true" [selectOnTab]="true" [isOpen]="false" [clearable]="false"
      [items]="selectedItems" [(ngModel)]="selectedItems" (change)="clearSelected()" (search)="onSearch($event)" [ngClass]="errorClass"
      [placeholder]="placeholder" class="w-100">
      <ng-template ng-label-tmp let-item="item" let-clear="clear" >
        <div class="ng-tag-value" [ngClass]="{'invalid' : chkValidTagFunction && chkValidTagFunction(getValue(item), validTagParam)}"
          [ngbTooltip]="classificationTooltip"
          tooltipClass="filter-tooltip"
          container="body">
          <span class="ng-value-icon left" (click)="clear(item)"  aria-hidden="true">×</span>
          <span class="ng-value-label" (click)="onSelectTag($event, getValue(item))">{{getValue(item)}}</span>
        </div>
        <ng-template #classificationTooltip>
          <div [innerHTML]="chkValidTagFunction && chkValidTagFunction(getValue(item), validTagParam) ? chkValidTagFunction(getValue(item), validTagParam) : getDescriptionClassification(getValue(item))" class="text-start"></div>
        </ng-template>
      </ng-template>
    </ng-select>
    <ng-content></ng-content>
    <ng-template #dropdown>
      <div class="dropdown-icon more-icon" (click)="openCpcIpcModal($event)"><i class="fa-solid fa-circle-plus"></i></div>
    </ng-template>
</div>
