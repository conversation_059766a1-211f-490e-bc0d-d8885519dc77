import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';

import { ClassificationFilterBrowserComponent } from './classification-filter-browser.component';

describe('ClassificationFilterBrowserComponent', () => {
  let component: ClassificationFilterBrowserComponent;
  let fixture: ComponentFixture<ClassificationFilterBrowserComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ClassificationFilterBrowserComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ClassificationFilterBrowserComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
