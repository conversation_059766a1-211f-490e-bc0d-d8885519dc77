<div class="col-selector-container position-relative" ngbDropdown>
  <a href="javascript:void(0)" ngbDropdownToggle
    *ngIf="icon" class="item-bar icon-column-selector caret-off">Customize columns
  </a>

  <div class="caret-off d-flex justify-content-start align-items-start"
  ngbDropdownToggle  *ngIf="!icon">
    <div class="caption-1 mb-0 me-1">{{ selectedToString() }}</div>
    <i class="fa fa-sort-down"></i>
  </div>

  <div ngbDropdownMenu>
    <div class="dropdown-item" (click)="toggleSelection($event, !selectAllCheckbox.checked)">
      <label class="checkbox mb-0">
        <input #selectAllCheckbox type="checkbox" (change)="toggleSelection($event, $event.target.checked)" [checked]="selected.length === columns?.length"/>
        <span *ngIf="selected.length < columns?.length">Select all</span>
        <span *ngIf="selected.length === columns?.length">Unselect all</span>
      </label>
    </div>

    <div *ngFor="let column of columns" class="dropdown-item" (click)="toggleColumn(column, $event)">
      <label class="checkbox mb-0">
        <input type="checkbox" (change)="toggleColumn(column,$event)" [checked]="isChecked(column)"/>
        <span>{{ column.label }}</span>
      </label>
    </div>
  </div>
</div>
