@import 'scss/layout2021/variables';

.col-selector-container {
  line-height: .875rem;
  .dropdown-menu{
    line-height: 1.5rem;
    &.show {
      min-width: 200px;
      > .dropdown-item {
        min-width: 180px;
        display: block;
      }
    }
  }
}

.item-bar {
  color: #0F2C35;
  text-decoration: none;
  &:hover {
    color: $brand-green;
  }
}
.icon-column-selector{
  padding-right: 20px;
  padding-left: 25px;
  background-position: 0;
  background-position-y: 7px;
  background-size: 15px;
  background-repeat: no-repeat;
  transition: all 0.2s ease;
  background-image:  url('/assets/images/layout2022/icon-columns.svg');
  &:hover,&.active{
    background-image:  url('/assets/images/layout2022/icon-columns-hover.svg');
  }
}
