import { AfterViewInit, Component, EventEmitter, Input, Output } from '@angular/core';
import * as $ from 'jquery';

@Component({
  selector: 'app-col-selector',
  templateUrl: './col-selector.component.html',
  styleUrls: ['./col-selector.component.scss']
})
export class ColSelectorComponent implements AfterViewInit {

  @Input() icon: boolean = true;
  @Input() iconColor: string = '#00a083';
  @Input() columns: any;
  @Input() selected = [];

  @Output() selectColumns: EventEmitter<Array<string>> = new EventEmitter();

  constructor() {
  }

  ngAfterViewInit() {
    $('.dropdown-item').on('click', e => {
      e.stopPropagation();
    });
  }

  isChecked(column) {
    return this.selected.findIndex(item => item.property === column.property) > -1;
  }

  toggleSelection(event: Event, isChecked: boolean) {
    event.preventDefault();
    event.stopPropagation();

    if (isChecked) {
      this.selected = this.columns.map((c) => c);
    } else {
      this.selected = [];
    }
    this.selectColumns.emit(this.selected);
  }

  toggleColumn(column, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const index = this.selected.findIndex(item => item.property === column.property);

    if (index > -1) {
      this.selected.splice(index, 1);
    } else {
      this.selected.unshift(column);
    }

    this.selectColumns.emit(this.selected);
  }

  selectedToString() {
    if (!this.selected || this.selected.length === 0) {
      return 'None selected';
    } else {
      if (this.selected.length <= 4) {
        return this.selected.map(c => c.label).join(', ');
      } else if (this.selected.length === this.columns.length) {
        return `All selected (${this.selected.length})`;
      } else {
        return `${this.selected.length} selected`;
      }
    }
  }
}
