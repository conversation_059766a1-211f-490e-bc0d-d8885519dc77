<div class="modal-header">
    <div class="modal-title" [innerHTML]="title"></div>

    <button type="button" class="close" aria-label="Close" (click)="dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
    <div *ngIf="isMessageArray" >
        <p *ngFor="let msg of message" [innerHTML]="msg"></p>
    </div>
    <p *ngIf="!isMessageArray" [innerHTML]="message"></p>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-md btn-primary-outline" [hidden]="btnCancelText===''" (click)="decline()" tabindex="-1">{{ btnCancelText }}</button>
  <button type="button" class="btn btn-md btn-primary" (click)="accept()" data-intercom-target="confirm-selection-button">{{ btnOkText }}</button>
</div>
