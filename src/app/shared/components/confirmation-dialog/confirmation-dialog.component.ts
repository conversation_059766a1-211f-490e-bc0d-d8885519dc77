import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

/**
 * Component for custom Confirm and Alert Element in Application
 */
@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})

export class ConfirmationDialogComponent implements OnInit {

  /**
   * Title for the dialog box
   */
  @Input() title: string;

  /**
   * Array or string of message to be displayed in dialog body
   */
  @Input() message: string | Array<string>;

  /**
   * Text for Ok Button in dialog
   */
  @Input() btnOkText: string;

  /**
   * Text for cancel button in dialog, Empty text will hide the cancel button from dialog
   */
  @Input() btnCancelText: string;

  /**
   * Reference for message as if array or string
   */
  public isMessageArray: boolean;

  constructor(private activeModal: NgbActiveModal) {
  }

  ngOnInit() {
    if (Array.isArray(this.message)) {
      this.isMessageArray = true;
    } else {
      this.isMessageArray = false;
    }
  }

  /**
   * Event listener for cancel button
   */
  public decline() {
    this.activeModal.close(false);
  }

  /**
   * Event listener for Ok button
   */
  public accept() {
    this.activeModal.close(true);
  }

  /**
   * Event listener for Dismissing the dialog box
   */
  public dismiss() {
    try {
      this.activeModal.close();
    } catch (e) {
      console.warn(e);
    }
  }
}
