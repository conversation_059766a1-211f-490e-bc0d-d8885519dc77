import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ContactUsBannerComponent } from './contact-us-banner.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ContactUsFooterComponent', () => {
  let component: ContactUsBannerComponent;
  let fixture: ComponentFixture<ContactUsBannerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ContactUsBannerComponent],
      imports: [
        HttpClientTestingModule,
        BrowserAnimationsModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ContactUsBannerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
