import { Component } from '@angular/core';
import { SupportComponent } from '@shared/components';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DelegationSourceEnum, UserService } from '@core';

@Component({
  selector: 'app-contact-us-banner',
  templateUrl: './contact-us-banner.component.html',
  styleUrls: ['./contact-us-banner.component.scss']
})
export class ContactUsBannerComponent {

  constructor(
    private modalService: NgbModal,
    private userService: UserService
  ) {
  }

  get isVisible(): boolean {
    return this.userService.isIpLounge();
  }

  openHubSpotForm(): void {
    const supportModalRef = this.modalService.open(SupportComponent, {centered: true, windowClass: 'modal-support'});
    supportModalRef.componentInstance.title = 'Contact us';
    supportModalRef.componentInstance.fillUserFields = false;
    supportModalRef.result.then(() => {
    }, () => {
    });
  }
}
