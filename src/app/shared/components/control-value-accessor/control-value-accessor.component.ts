import { Component, Injector, Input, Optional, ViewChild } from '@angular/core';
import { Control<PERSON><PERSON>r, ControlValueAccessor, UntypedFormControl, FormControlDirective } from '@angular/forms';

@Component({
  selector: 'app-control-value-accessor',
  templateUrl: './control-value-accessor.component.html',
  styleUrls: ['./control-value-accessor.component.scss']
})
export class ControlValueAccessorComponent implements ControlValueAccessor {
  @ViewChild(FormControlDirective, {static: true}) formControlDirective: FormControlDirective;

  @Input() formControl: UntypedFormControl;
  @Input() formControlName: string;
  @Input() isInvalid = false;
  @Input() isDisabled = false;

  constructor(
    @Optional() protected controlContainer: ControlContainer
  ) {
  }

  get control(): UntypedFormControl {
    if (this.formControl) {
      return this.formControl;
    }

    if (this.controlContainer?.control) {
      return this.controlContainer.control.get(this.formControlName) as UntypedFormControl;
    }

    return null;
  }

  get controlValue() {
    return this.control?.value;
  }

  get hasFormControl(): boolean {
    return !!this.control;
  }

  get controlDisabled(): boolean {
    return this.control?.disabled;
  }

  get controlEnabled(): boolean {
    return this.control?.enabled;
  }

  registerOnTouched(fn: any): void {
    this.formControlDirective?.valueAccessor?.registerOnTouched(fn);
  }

  registerOnChange(fn: any): void {
    this.formControlDirective?.valueAccessor?.registerOnChange(fn);
  }

  writeValue(obj: any): void {
    this.formControlDirective?.valueAccessor?.writeValue(obj);
  }

  setDisabledState(isDisabled: boolean): void {
    this.formControlDirective?.valueAccessor?.setDisabledState(isDisabled);
  }

  disableControl(canDisable: boolean = true) {
    if (canDisable) {
      this.control?.disable();
    }
  }

  enableControl(canEnable: boolean = true) {
    if (canEnable) {
      this.control?.enable();
    }
  }

  protected setControlValue(val: any, emitEvent: boolean = true, markAsDirty: boolean = true) {
    if (this.control) {
      this.control.setValue(val, {emitEvent});

      if (markAsDirty) {
        this.control.markAllAsTouched();
        this.control.markAsDirty();
      }
    }
  }

  protected setControlErrors(errors: any, emitEvent: boolean = true) {
    if (this.control) {
      this.control.setErrors(errors, {emitEvent});
      this.control.markAllAsTouched();
      this.control.markAsDirty();
    }
  }

  protected clearControlValue() {
    if (this.control && this.control.value) {
      this.control.setValue(null);
    }
  }
}
