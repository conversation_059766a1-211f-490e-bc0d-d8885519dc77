import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SettingsService } from '@core';

@Component({
  selector: 'app-cookie-banner',
  templateUrl: './cookie-banner.component.html'
})
export class CookieBannerComponent implements OnInit, OnDestroy {

  @Input()
  siteID: string = null;

  @Input()
  hjID: string = null;

  e: HTMLScriptElement;

  constructor(private settingService: SettingsService) {
  }

  ngOnInit() {
    if (!this.settingService.settings.cookieBanner || document.URL.indexOf('privacy-policy') > -1) {
      return;  // No HJ integration for automated tests
    }
    this.e = document.createElement('script');
    this.e.async = true;
    this.e.src = 'https://cdn-cookieyes.com/client_data/133b0520c0bd87f621b64f10/script.js';
    const t = document.getElementsByTagName('script')[0];
    t.parentNode.insertBefore(this.e, t);
  }

  ngOnDestroy() {
    if (this.e) {
      this.e.remove();
    }
  }
}
