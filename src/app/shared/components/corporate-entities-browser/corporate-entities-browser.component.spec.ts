import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CorporateEntitiesBrowserComponent } from './corporate-entities-browser.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';

describe('OwnerSelectorComponent', () => {
  let component: CorporateEntitiesBrowserComponent;
  let fixture: ComponentFixture<CorporateEntitiesBrowserComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CorporateEntitiesBrowserComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CorporateEntitiesBrowserComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
