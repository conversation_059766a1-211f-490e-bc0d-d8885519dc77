<div class="similar-search-top d-flex justify-content-between align-items-center">
  <div class="d-flex justify-content-start align-items-center tools-bar">
    <div class="group group-separator-right">
      <a href="javascript:void(0)" (click)="addCharts()"
        class="item-bar icon-plus" [ngClass]="{'disabled': !isCustomChartCategory || storeService.arrangeDashboard}">
        Add charts
      </a>
      <a href="javascript:void(0)" (click)="arrangeDashboard()" *ngIf="!storeService.arrangeDashboard"
        class="item-bar icon-arrange" [ngClass]="{'disabled': !isCustomChartCategory || isCustomChartCategoryEmpty}">
        Arrange charts
      </a>
      <a href="javascript:void(0)" (click)="save()" *ngIf="storeService.arrangeDashboard"
        class="item-bar no-icon" [ngClass]="{'disabled': !isCustomChartCategory}">
        Save
      </a>
      <a href="javascript:void(0)" (click)="cancel()" *ngIf="storeService.arrangeDashboard"
        class="item-bar no-icon" [ngClass]="{'disabled': !isCustomChartCategory}">
        Cancel
      </a>
    </div>

    <div class="group group-separator-right">
      <a href="javascript:void(0)" (click)="openConfirmModal(confirmModal, ActionType.Rename)"
        class="item-bar icon-rename" [ngClass]="{'disabled': !isCustomChartCategory || storeService.arrangeDashboard}">
        Rename dashboard
      </a>

      <a href="javascript:void(0)" (click)="openConfirmModal(confirmModal, ActionType.Empty)"
        class="item-bar icon-empty" [ngClass]="{'disabled': !isCustomChartCategory || isCustomChartCategoryEmpty || storeService.arrangeDashboard}">
        Empty dashboard
      </a>

      <a href="javascript:void(0)" (click)="openConfirmModal(confirmModal, ActionType.Delete)"
        class="item-bar icon-delete" [ngClass]="{'disabled': !isCustomChartCategory || storeService.arrangeDashboard}">
        Delete dashboard
      </a>
    </div>

    <div class="download position-relative" ngbDropdown [autoClose]="true">
      <a href="javascript:void(0)" ngbDropdownToggle data-intercom-target="download-dashboard"
        class="item-bar icon-download" [ngClass]="{'disabled': isCustomChartCategory && storeService.arrangeDashboard}">
        Download dashboard
      </a>
      <div class="dropdown-menu dropdown-menu-end" ngbDropdownMenu>
        <a class="dropdown-item disabled px-2" href="javascript:void(0)">Download as:</a>
        <div class="dropdown-item px-2 d-flex justify-content-between" >
          <div class="d-flex justify-content-start align-items-center" *ngFor="let type of ['JPEG', 'PNG', 'PDF', 'SVG']; let last = last;" [ngClass]="{'me-1': !last}">
            <ng-container [ngTemplateOutlet]="downloadingSpinnerTemplate" *ngIf="isDownloading(type)"></ng-container>
            <a href="javascript:void(0)" (click)="onDownloadCharts(type)" [ngClass]="{'ms-1': isDownloading(type)}">{{type}}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #confirmModal let-modal>
  <div class="modal-header px-5 pt-5">
    <div class="d-flex justify-content-center w-100">
      <img [ngSrc]="modalImg" width="60" height="60" alt="">
    </div>
  </div>
  <div class="modal-body px-5 pb-5">
    <div class="d-flex px-5">
      <div class="text-center" *ngIf="modalAction !== 'rename'">Are you sure you want to <b>{{this.modalAction}} {{storeService.getActiveChartCategoryName()}}?</b></div>
      <div class="w-100 text-center" *ngIf="modalAction === 'rename'">
        <p>Rename your dashboard</p>
        <input class="form-control" type="text" [(ngModel)]="newChartCategoryName" placeholder="Name your dashboard" [ngClass]="{'is-invalid': isNameInvalid && !isProcessing}">
        <div *ngIf="isNameInvalid && !isProcessing" class="invalid-feedback text-start">
          Name already used by another dashboard.
        </div>
      </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
      <button class="btn btn-primary me-3" *ngIf="modalAction !== 'rename'" (click)="deleteEmptyDashboard();">Delete</button>
      <button class="btn btn-primary me-3" *ngIf="modalAction === 'rename'" (click)="renameDashboard();" [class.disabled]="isNameInvalid">Save</button>
      <button class="btn btn-ghost" (click)="modal.dismiss()" >Cancel</button>
    </div>
  </div>
</ng-template>

<ng-template #downloadingSpinnerTemplate>
  <img [ngSrc]="'assets/images/octimine_blue_spinner.gif'" ngbTooltip="Downloading charts" container="body" width="20" height="20" />
</ng-template>
