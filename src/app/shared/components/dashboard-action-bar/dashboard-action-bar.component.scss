.icon {
  &-plus,
  &-arrange,
  &-rename,
  &-empty,
  &-delete,
  &-download {
    padding-right: 20px;
    padding-left: 25px;
    background-position: 0;
    background-size: 15px;
    background-repeat: no-repeat;
    transition: all 0.2s ease;
  }

  &-plus {
    background-image: url("/assets/images/layout2022/icon-plus.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-plus-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-plus-disabled.svg');
    }
  }

  &-arrange {
    background-image: url("/assets/images/layout2022/icon-arrange.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-arrange-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-arrange-disabled.svg');
    }
  }

  &-rename {
    background-image: url("/assets/images/layout2022/icon-rename.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-rename-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-rename-disabled.svg');
    }
  }

  &-empty {
    background-image: url("/assets/images/layout2022/icon-empty.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-empty-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-empty-disabled.svg');
    }
  }

  &-delete {
    background-image: url("/assets/images/layout2022/icon-delete.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-delete-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-delete-disabled.svg');
    }
  }

  &-download {
    background-image: url("/assets/images/layout2022/icon-export.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-export-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-export-disabled.svg');
    }
  }
}

.no-icon {
  padding-right: 20px;
}

.download {
  &:hover {
    .dropdown-menu {
      display: block;
    }
  }

  .dropdown-menu {
    font-size: 14px;
    top: 97%;
    min-width: 200px;
    margin-top: 0;
    &.dropdown-menu-end {
      right: 0;
      left: unset;
    }
    .dropdown-item {
      :not(.disabled) {
        text-decoration: none;
        color: #000;
      }
      :hover {
        color: #00A083;
      }
    }
  }
}

.group {
  &.group-separator-right {
    border-right: 1px solid #d5d5d5;
    border-image: linear-gradient(0deg, white, #d5d5d5, white) 1;
    margin-right: 20px;
  }
  .group-name {
    color: #e3e3e3;
    font-size: 0.8rem;
  }
}
