import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core';
import { SharedModule } from '@shared/shared.module';

import { DashboardActionBarComponent } from './dashboard-action-bar.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('DashboardActionBarComponent', () => {
  let component: DashboardActionBarComponent;
  let fixture: ComponentFixture<DashboardActionBarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DashboardActionBarComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([]),
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardActionBarComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
