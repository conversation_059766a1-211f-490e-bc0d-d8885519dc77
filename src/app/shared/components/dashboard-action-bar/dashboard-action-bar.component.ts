import { Component, Input, OnD<PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { BaseStoreService, ExportChartService, UserProfile, UserService } from '@core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs/internal/Subscription';
import { ActionType } from './../../charts/chart-dashboard/types';
import { Chart } from '@shared/charts';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-dashboard-action-bar',
  templateUrl: './dashboard-action-bar.component.html',
  styleUrls: ['./dashboard-action-bar.component.scss']
})
export class DashboardActionBarComponent implements OnInit, OnDestroy {

  @Input() storeService: BaseStoreService;
  @Input() exportCssSelector: string = '.charts-container app-base-card-chart';
  @Input() exportDownloadName: string = null;

  userProfile: UserProfile;
  modal: NgbActiveModal = null;
  modalImg: string;
  modalAction: string;
  newChartCategoryName = '';
  ActionType = ActionType;
  isProcessing: boolean = false;

  private subscriptions = new Subscription();
  private sourceCharts: string[];

  constructor(
    private userService: UserService,
    private modalService: NgbModal,
    private exportChartService: ExportChartService
  ) {
  }

  get isCustomChartCategoryEmpty(): boolean {
    const categoryName = this.storeService.getActiveChartCategoryName();
    const chartCategory = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, categoryName);
    return !chartCategory || chartCategory.charts.length === 0;
  }

  get isNameInvalid(): boolean {
    if (!this.newChartCategoryName) {
      return false;
    }

    const newName = this.newChartCategoryName.trim().toLowerCase();

    if (newName.length === 0) {
      return true;
    }

    const chartNames = [
      ...this.userService.getChartCategories(this.storeService.chartDashboardType).map(ds => ds.name),
      ...this.defaultChartCategoryNames
    ];

    if (chartNames.length > 0) {
      return chartNames.some(name => name.toLowerCase() === newName);
    }

    return false;
  }

  get defaultChartCategoryNames(): string[] {
    return this.storeService.defaultChartCategories.map(ds => ds.category);
  }

  get isCustomChartCategory(): boolean {
    return this.storeService.isCustomChartCategory();
  }

  ngOnInit(): void {
    const user$ = this.userService.user.subscribe({
      next: u => {
        if (u && u.profile) {
          this.userProfile = u.profile;
        }
      }
    });
    this.subscriptions.add(user$);

    const removeChart$ = this.storeService.removeChart$.subscribe({
      next: c => {
        this.removeChartFromCustomChartCategory(c);
      }
    });
    this.subscriptions.add(removeChart$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.storeService.arrangeDashboard = false;
  }

  addCharts() {
    this.storeService.addChartInDashboard = !this.storeService.addChartInDashboard;
  }

  arrangeDashboard() {
    if (!this.isCustomChartCategory) {
      return;
    }
    const charts = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, this.storeService.getActiveChartCategoryName()).charts;
    this.sourceCharts = [...charts];
    this.storeService.arrangeDashboard = true;
  }

  cancel() {
    this.restoreCharts();
    this.storeService.arrangeDashboard = false;
  }

  save() {
    const updateProfile$ = this.userService.updateProfile(this.userService.getUser().profile)
      .subscribe({
        next: () => {
        }, error: (err) => {
          console.log(err);
        }
      });
    this.subscriptions.add(updateProfile$);
    this.storeService.arrangeDashboard = false;
  }

  async onDownloadCharts(type: "JPEG" | "PNG" | "PDF" | "SVG" | "PRINT") {
    await this.exportChartService.exportCharts(this.exportCssSelector, type, 2, this.getChartsDownloadName(type),
      () => {
        document.documentElement.style.cursor = 'wait';
      }, () => {
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      }, (error: string) => {
        console.warn(error);
        if (this.exportChartService.noExportingTasks()) {
          document.documentElement.style.cursor = 'default';
        }
      });
  }

  emptyCustomChartCategory() {
    const chartCategory = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, this.storeService.getActiveChartCategoryName());
    if (!chartCategory) {
      return;
    }
    chartCategory.charts.length = 0;

    this.modal.close();
    const updateProfile$ = this.userService.updateProfile(this.userProfile)
      .subscribe({next: () => {
        this.storeService.chartDashboardAction = {
          action: ActionType.Empty,
          message: `<b>${this.storeService.getActiveChartCategoryName()}</b> was cleared`
        };
        this.storeService.customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
      }, error: (err) => { console.log(err); }});
    this.subscriptions.add(updateProfile$);
  }

  deleteCustomChartCategory() {
    const customChartCategories = this.userService.getChartCategories(this.storeService.chartDashboardType);
    const foundCategoryIndex = customChartCategories.findIndex((d) => {
      return this.storeService.activeChartCategory === this.storeService.generateCustomChartCategoryId(d);
    });
    if (foundCategoryIndex > -1) {
      customChartCategories.splice(foundCategoryIndex, 1);
    }

    this.modal.close();
    const updateProfile$ = this.userService.updateProfile(this.userProfile)
      .subscribe({next: () => {
        if (customChartCategories.length > 0) {
          const newActiveCategoryIndex = foundCategoryIndex + 1 <= customChartCategories.length ? foundCategoryIndex : foundCategoryIndex - 1;
          const newActiveCategory = customChartCategories[newActiveCategoryIndex];
          this.storeService.activeChartCategory = this.storeService.generateCustomChartCategoryId(newActiveCategory);
        } else {
          this.storeService.activeChartCategory = this.defaultChartCategoryNames[this.defaultChartCategoryNames.length - 1];
        }
        this.storeService.addChartInDashboard = false;
      }, error: (err) => { console.log(err); }});
    this.subscriptions.add(updateProfile$);
  }

  removeChartFromCustomChartCategory(chart: Chart) {
    if (!chart) {
      return;
    }
    const activeChartCategory = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, this.storeService.getActiveChartCategoryName());
    if (!activeChartCategory) {
      return;
    }
    const chartIndex = activeChartCategory.charts.indexOf(chart.name);
    if (chartIndex > -1) {
      activeChartCategory.charts.splice(chartIndex, 1);
    }

    const updateProfile$ = this.userService.updateProfile(this.userProfile)
      .subscribe({next: () => {
        this.storeService.chartDashboardAction = {
          action: ActionType.Delete,
          message: `Chart <b>${chart.title}</b> was removed from <b>${this.storeService.getActiveChartCategoryName()}</b>`
        };
      }, error: (err) => { console.log(err); }});
    this.subscriptions.add(updateProfile$);
  }

  openConfirmModal(modal: TemplateRef<any>, action: ActionType) {
    if (!this.isCustomChartCategory || (action === ActionType.Empty && this.isCustomChartCategoryEmpty)) {
      return;
    }

    switch (action) {
      case ActionType.Empty:
        this.modalImg = 'assets/images/layout2022/icon-empty.svg';
        break;
      case ActionType.Delete:
        this.modalImg = 'assets/images/layout2022/icon-delete.svg';
        break;
      default:
        this.newChartCategoryName = '';
        this.modalImg = 'assets/images/layout2022/icon-rename.svg';
        break;
    }

    this.modalAction = action;
    this.modal = this.modalService.open(modal, {centered: true});
  }

  deleteEmptyDashboard() {
    if (this.modalAction === ActionType.Empty) {
      this.emptyCustomChartCategory();
    } else if (this.modalAction === ActionType.Delete) {
      this.deleteCustomChartCategory();
    }
  }

  renameDashboard() {
    if (!this.newChartCategoryName || this.isNameInvalid) {
      return;
    }
    this.isProcessing = true;
    const activeName = this.storeService.getActiveChartCategoryName();
    const activeChartCategory = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, activeName);
    const oldDashboardName = activeChartCategory.name;
    activeChartCategory.name = this.newChartCategoryName;

    this.modal.close();
    const updateProfile$ = this.userService.updateProfile(this.userProfile)
      .pipe(
        finalize(() => this.isProcessing = false)
      )
      .subscribe({
        next: () => {
          this.storeService.activeChartCategory = this.storeService.generateCustomChartCategoryId(activeChartCategory);
          this.storeService.chartDashboardAction = {
            action: ActionType.Rename,
            message: `Dashboard <b>${oldDashboardName}</b> was renamed to <b>${this.newChartCategoryName}</b>`
          };
        },
        error: (err) => { console.log(err); }
      });
    this.subscriptions.add(updateProfile$);
  }

  isDownloading(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT'): boolean {
    return this.exportChartService.isExporting(this.getChartsDownloadName(type), type, true);
  }

  private restoreCharts() {
    const dashboard = this.userService.getActiveChartCategory(this.storeService.chartDashboardType, this.storeService.getActiveChartCategoryName());
    dashboard.charts = this.sourceCharts;
  }

  private getChartsDownloadName(type: 'JPEG' | 'PNG' | 'PDF' | 'SVG' | 'PRINT'): string {
    return this.storeService.getDownloadChartsName(type, this.exportDownloadName);
  }
}
