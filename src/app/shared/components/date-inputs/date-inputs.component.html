<div class="date-inputs" [formGroup]="form">
  <input class="form-control mb-0"
    [ngClass]="{'is-invalid': form.dirty && form.get('day').errors}"
    formControlName="day"
    type="text"
    min="1"
    max="31"
    inputmode="numeric"
    pattern="\d*"
    maxlength="2"
    id="{{ id + '_day' }}"
    autocomplete="off"
    (click)="showDatepicker = true"
    (blur)="onBlur($event)">
  <input class="form-control mb-0"
    [ngClass]="{'is-invalid': form.dirty && form.get('month').errors}"
    formControlName="month"
    type="text"
    min="1"
    max="12"
    inputmode="numeric"
    pattern="\d*"
    maxlength="2"
    id="{{ id + '_month' }}"
    autocomplete="off"
    (click)="showDatepicker = true"
    (blur)="onBlur($event)">
  <input class="form-control mb-0"
    [ngClass]="{'is-invalid': form.dirty && form.get('year').errors}"
    formControlName="year"
    type="text"
    inputmode="numeric"
    pattern="\d*"
    maxlength="4"
    id="{{ id + '_year' }}"
    autocomplete="off"
    (click)="showDatepicker = true"
    (blur)="onBlur($event)">
</div>
<ngb-datepicker #d (dateSelect)="onDateSelect($event, d)" *ngIf="showDatepicker" style="position: absolute"></ngb-datepicker>
