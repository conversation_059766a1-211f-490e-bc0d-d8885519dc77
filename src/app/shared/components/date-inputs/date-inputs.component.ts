import { filter, get, split } from 'lodash';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges
} from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-date-inputs',
  templateUrl: './date-inputs.component.html',
  styleUrls: ['./date-inputs.component.scss']
})
export class DateInputsComponent implements OnInit, OnDestroy, OnChanges {
  @Input() id: string;
  @Input() date?: string = '';
  @Output() valueChanged: EventEmitter<string> = new EventEmitter();

  form: UntypedFormGroup;

  reseting = false;
  showDatepicker = false;

  private subscriptions = new Subscription();

  @HostListener('document:keydown.escape', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.showDatepicker = false;
  }

  ngOnInit() {
    this.buildForm();
    this.fillForm();

    const valueChanges$ = this.form.valueChanges.subscribe({
      next: date => {
        this.form.markAllAsTouched();
        if (!this.reseting) {
          const formatted = filter([date.year, date.month, date.day]).join('-');
          this.valueChanged.emit(formatted);
        } else {
          this.reseting = false;
        }
      }
    });
    this.subscriptions.add(valueChanges$);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['date']) {
      if (!changes['date'].currentValue && this.form) {
        this.reseting = true;
        this.form.setValue({
          day: '',
          month: '',
          year: '',
        });
      }
    }
  }

  private buildForm() {
    this.form = new UntypedFormGroup({
      day: new UntypedFormControl('', [Validators.required , Validators.pattern('^(0?[1-9]|[12][0-9]|3[01])$')]),
      month: new UntypedFormControl('', [Validators.required, Validators.pattern('^(0?[1-9]|1[012])')]),
      year: new UntypedFormControl('', [Validators.required, Validators.pattern('^\\d{4}$')])
    });
  }

  private fillForm() {
    if (!this.date) {
      return;
    }

    const dateAsArray = split(this.date, '-');

    this.form.setValue({
      day: get(dateAsArray, 2),
      month: get(dateAsArray, 1),
      year: get(dateAsArray, 0),
    });
  }

  onDateSelect(event, target) {
    this.form.setValue({
      day: event.day,
      month: event.month,
      year: event.year,
    });
    this.date = event.year + '-' + event.month + '-' + event.day;
    this.showDatepicker = false;
  }

  onBlur(event: FocusEvent) {
    if (!event.relatedTarget ||
      (event.relatedTarget['className'].indexOf('ngb-dp-day') === -1 && !event.relatedTarget['closest']('ngb-datepicker'))) {
      this.showDatepicker = false;
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
