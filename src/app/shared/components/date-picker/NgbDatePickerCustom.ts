import { Injectable } from '@angular/core';
import { UserService } from '@core';
import { NgbDateAdapter, NgbDateParserFormatter, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';

/**
 * This Service handles how the date is represented in scripts i.e. ngModel.
 */
@Injectable()
export class NgbDateLocaleAdapter extends NgbDateAdapter<any> {
  constructor(private userService: UserService) {
    super();
  }

  fromModel(value): NgbDateStruct | null {
    if (typeof value !== 'string') {
      return value;
    }
    return getValue(value, this.userService);
  }

  toModel(date) {
    return (date && date.year && date.month && date.day) ?
        { year: date.year, month: date.month, day: date.day } :
        null;
  }
}

@Injectable()
export class NgbDateLocaleParserFormatter extends NgbDateParserFormatter {

  constructor(private userService: UserService) {
    super();
  }
  parse(value: string) {
    return getValue(value, this.userService);
  }

  format(date: NgbDateStruct | null) {
    if (!date) {
      return '';
    }
    let formattedDate = '';
    if (this.userService.getLocale().dateFormatting === 'dd/MM/yyyy') {
      formattedDate = date.day.toString().padStart(2, '0') + '/' +
                      date.month.toString().padStart(2, '0') + '/' +
                      date.year;
    } else if (this.userService.getLocale().dateFormatting === 'MM/dd/yyyy') {
      formattedDate = date.month.toString().padStart(2, '0') + '/' +
                      date.day.toString().padStart(2, '0') + '/' +
                      date.year;
    } else if (this.userService.getLocale().dateFormatting === 'dd.MM.yyyy') {
      formattedDate = date.day.toString().padStart(2, '0') + '.' +
                      date.month.toString().padStart(2, '0') + '.' +
                      date.year;
    }
    return formattedDate;
  }
}

export function getValue(value: string, userService: UserService): NgbDateStruct | null {
    if (!value) {
      return null;
    }

    if (userService.getLocale().dateFormatting === 'dd/MM/yyyy') {
      const date = value.split('/');
      return {
        day : parseInt(date[0], 10),
        month : parseInt(date[1], 10),
        year : parseInt(date[2], 10)
      };
    } else if (userService.getLocale().dateFormatting === 'MM/dd/yyyy') {
      const date = value.split('/');
      return {
        day : parseInt(date[1], 10),
        month : parseInt(date[0], 10),
        year : parseInt(date[2], 10)
      };
    } else if (userService.getLocale().dateFormatting === 'dd.MM.yyyy') {
      const date = value.split('.');
      return {
        day : parseInt(date[0], 10),
        month : parseInt(date[1], 10),
        year : parseInt(date[2], 10)
      };
    }
    return null;
}
