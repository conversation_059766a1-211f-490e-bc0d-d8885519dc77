<div class="modal-header">
  <div class="modal-title">
    It seems the number you have entered is linked to two different patent families. Please choose the one you want to use for your search below:
  </div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>

<div class="modal-body">
  <div class="overflow-auto publication-table">
    <table class="table table-condensed publication-table w-100 table-hover mb-0 mt-0">
      <thead class="w-100">
      <tr>
        <th width="30">
          <label class="checkbox m-0 p-0">
            <input type="checkbox" (change)="selectAllPatents()" [checked]="isAllChecked()">
            <span class="no-text">&nbsp;</span>
          </label>
        </th>
        <th width="30">
          <span>#</span>
        </th>
        <th>
          <span>Title</span>
        </th>
        <th style="min-width: 100px;" >
          <span>Publ. No.</span>
        </th>
        <th style="min-width: 130px;">
          <span>Applicants</span>
        </th>
      </tr>
      </thead>
      <tbody *ngFor="let patent of patents; index as indexi" >
        <tr>
          <td>
            <label class="checkbox m-0 p-0">
              <input type="checkbox" (change)="selectPatent(patent)"
                     [checked]="isPatentSelected(patent)" *ngIf="!patent.general?.obfuscated">
              <span class="no-text">&nbsp;</span>
            </label>
          </td>
          <td class="number-col" nowrap="nowrap">
            {{ indexi + 1 }}
          </td>
          <td class="text-break" >
            <div>{{ patent?.bibliographic?.title }}</div>
          </td>
          <td class="publication-number-col" nowrap="nowrap" [ngStyle]="{'font-style': patent.last_read ? 'italic' : 'normal'}">
            <a href="javascript:void()">
               <span>{{ patentTableService.getQueriedPublicationNumber(patent) }}</span>
            </a>
          </td>
          <td>
            {{ patentTableService.getApplicants(patent) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>

</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between align-items-center w-100">
    <div class="modal-hint flex-fill">

    </div>

    <button class="btn btn-md btn-ghost me-2" (click)="activeModal.close([])">Cancel</button>

    <button class="btn btn-md btn-primary" ngbTooltip="Search with selection" (click)="activeModal.close(selectedIds)"
            [disabled]="selectedIds.length === 0">
      Search with selection
    </button>
  </div>
</div>
