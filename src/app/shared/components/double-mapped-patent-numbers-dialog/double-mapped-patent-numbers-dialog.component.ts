import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Patent, PatentTableService } from '@core';

@Component({
  selector: 'app-double-mapped-patent-numbers-dialog',
  templateUrl: './double-mapped-patent-numbers-dialog.component.html',
  styleUrls: ['./double-mapped-patent-numbers-dialog.component.scss']
})
export class DoubleMappedPatentNumbersDialogComponent {
  @Input() patents: Patent[] = [];
  selectedIds: number[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    public patentTableService: PatentTableService
  ) {
  }

  selectAllPatents() {
    const patentNumbers = this.patents.map(patent => Number(patent.general.docdb_family_id));
    if (this.selectedIds.length === patentNumbers.length) {
      this.selectedIds = [];
    } else {
      this.selectedIds = patentNumbers;
    }
  }

  isAllChecked(): boolean {
    return this.selectedIds.length === this.patents.length;
  }

  selectPatent(patent: Patent) {
    const docId = Number(patent.general.docdb_family_id);
    const index = this.selectedIds.indexOf(docId);
    if (index === -1) {
      this.selectedIds.push(docId);
    } else {
      this.selectedIds.splice(index, 1);
    }
  }

  isPatentSelected(patent: Patent): boolean {
    return this.selectedIds.indexOf(Number(patent.general.docdb_family_id)) !== -1;
  }
}
