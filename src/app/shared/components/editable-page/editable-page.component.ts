import {
  Component, EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewEncapsulation
} from '@angular/core';
import { CockpitService } from '@core/services';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { catchError, filter, switchMap, tap } from 'rxjs/operators';
import { BehaviorSubject, of, Subscription } from 'rxjs';

/**
 * Component for static pages which loads page body from content management system.
 */
@Component({
  selector: 'app-editable-page',
  templateUrl: './editable-page.component.html',
  encapsulation: ViewEncapsulation.None
})
export class EditablePageComponent implements OnInit, OnDestroy, OnChanges {

  @Input() pageName: string;
  @Input() showLoadingIndicator = true;
  @Output() loadingEvent = new EventEmitter<boolean>();

  isLoading = false;

  pageText: string | SafeHtml = '';

  private getSingletonSubject = new BehaviorSubject<string>(null);
  private readonly getSingleton$ = this.getSingletonSubject.asObservable();

  private subscriptions = new Subscription();

  constructor(
    private cockpitService: CockpitService,
    private sanitizer: DomSanitizer,
    private router: Router
  ) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.pageName) {
      this.getSingletonSubject.next(changes.pageName.currentValue);
    }
  }

  ngOnInit(): void {
    const getSingleton$ = this.getSingleton$
      .pipe(
        filter(pageName => !!pageName),
        tap(() => {
          this.setLoadingIndicator(true);
          this.pageText = '';
        }),
        switchMap(pageName => {
          return this.cockpitService.getSingleton(pageName)
            .pipe(
              catchError(() => of(null))
            )
        }),
        tap(t => {
          this.pageText = this.sanitizer.bypassSecurityTrustHtml(t);
          this.scrollToAnchor(this.router.url);
          this.setLoadingIndicator(false);
        })
      )
      .subscribe();
    this.subscriptions.add(getSingleton$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  scrollToAnchor(url) {
    const urlParts = url.split('#');
    if (urlParts.length > 1) {
      setTimeout(() => {
        const anchor = decodeURI(urlParts[urlParts.length - 1]);
        const el = document.querySelector('[name="' + anchor + '"]') ||
          document.querySelector('[id="' + anchor + '"]');
        if (el) {
          el.scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
        }
      }, 500);
    }
  }

  private setLoadingIndicator(isLoading: boolean) {
    this.isLoading = isLoading;
    this.loadingEvent.emit(isLoading);
  }
}
