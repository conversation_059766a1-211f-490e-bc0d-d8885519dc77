import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ErrorService } from '@core/services';
import { Subscription } from 'rxjs';

declare var $: any;

@Component({
  selector: 'app-errors-dialog',
  templateUrl: './errors-dialog.component.html',
  styleUrls: ['./errors-dialog.component.scss']
})
export class ErrorsDialogComponent implements OnInit, OnDestroy {
  showExtraInfo = false;

  private subscriptions = new Subscription();

  constructor(private errorService: ErrorService) {
  }

  ngOnInit() {
    const error$ = this.errorService.error$.subscribe({
      next: error => {
        if (error) {
          if (error instanceof Error) {
            this.showExtraInfo = true;
            $('#errorDialog .errorText').text(error.message);
          } else {
            this.showExtraInfo = false;
            $('#errorDialog .errorText').text(error);
          }
          $('#errorDialog').modal('show');
        }
      }
    });
    this.subscriptions.add(error$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
