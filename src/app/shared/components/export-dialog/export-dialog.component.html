<div class="modal-header">
  <div class="modal-title">Export results</div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body px-5 py-3">
  <div class="headline-6 mt-0 mb-2">Export format</div>
  <div class="d-flex justify-content-between">
    <label class="radio-button d-flex justify-content-center p-3">
      <input type="radio" name="exportButton" [(ngModel)]="typeFormat" value="pdf" (change)="selectFormat($event)" />
      <span>
                <div class="text-center">
                    <i class="far fa-file-pdf mb-2"></i>
                    <label>PDF</label>
                </div>
            </span>
    </label>
    <label class="radio-button d-flex justify-content-center p-3">
      <input type="radio" name="exportButton" [(ngModel)]="typeFormat" value="xlsx" (change)="selectFormat($event)"/>
      <span>
                <div class="text-center">
                    <i class="far fa-file-excel mb-2"></i>
                    <label>Excel</label>
                </div>
            </span>
    </label>
    <label class="radio-button d-flex justify-content-center p-3">
      <input type="radio" name="exportButton" [(ngModel)]="typeFormat" value="csv" (change)="selectFormat($event)"/>
      <span>
                <div class="text-center">
                    <i class="far fa-file-alt mb-2"></i>
                    <label>CSV</label>
                </div>
            </span>
    </label>
  </div>

  <div class="headline-6 mt-4 mb-2">Fields to include in the export</div>
  <div class="d-flex justify-content-between">
    <div class="box-selection">
      <p class="box-selection-header mb-2">All available fields</p>
      <div class="box-selection-list">
        <div *ngFor="let field of fields; let first = first; let last = last;" >
          <a  *ngIf="!field.selected && allowExportField(field)"
              (click)="field.name ? field.checked = !field.checked : false"
              (dblclick)="onSelectField(field)"
              class="box-selection-item" [ngClass]="{'first': first, 'last': last, 'title': !field.name, 'checked': field.checked}">{{ field.title }}</a>
        </div>
      </div>
    </div>
    <div class="box-button mt-4 d-flex flex-column align-items-center justify-content-center">
      <div class="button-selection p-3 text text-center mt-2" (click)="onUnSelectFieldsMarked()">
        <i class="fas fa-long-arrow-alt-left"></i>
      </div>
      <div class="button-selection p-3 text text-center mt-2" (click)="onSelectFieldsMarked()">
        <i class="fas fa-long-arrow-alt-right"></i>
      </div>
    </div>
    <div class="box-selection">
      <p class="box-selection-header mb-2">Selected fields</p>

      <div class="box-selection-list" cdkDropList (cdkDropListDropped)="drop($event)">
        <div class="example-custom-placeholder" *cdkDragPlaceholder></div>
        <a cdkDrag *ngFor="let field of selectedFields; let i = index; let first = first; let last = last;"  [ngbTooltip]="field.required ? 'Required' : ''"
           [cdkDragDisabled]="i < 2"
           (click)="field.name && !field.required ? field.checked = !field.checked : false"
           (dblclick)="onUnSelectField(field)"
           class="box-selection-item"
           [ngClass]="{'first': first, 'last': last, 'title': !field.name, 'checked': field.checked, 'required': field.required}">{{ field.title }}</a>
      </div>
    </div>
  </div>

  <div class="mt-4 mb-2 d-flex flex-column align-items-start gap-spacing-sm">
    <div class="d-flex justify-content-start align-items-center">
      <div class="headline-6 m-0">
        <label class="checkbox">
          <input [(ngModel)]="exportChartsChecked" class="form-check-input" type="checkbox" (change)="onExportChartsChecked($event)" [disabled]="!typeFormat || typeFormat === 'csv'">
          <span class="form-check-label">Export the charts</span>
        </label>
      </div>

      <div ngbDropdown class="ms-3 tabs-container">
        <div class="caret-off text-capitalize cursor-pointer" ngbDropdownToggle id="chartCategoryDropdown" style="min-width: 212px;">
          <span class="form-control text-green" *ngIf="selectedDefaultChartCategory">
            <img alt="" [src]="'assets/images/layout2022/icon-' + selectedDefaultChartCategory.category + '-hover.svg'"
                 style="width: 15px;"/> {{ selectedDefaultChartCategory.title }}
          </span>

          <span class="form-control text-green" *ngIf="selectedCustomChartCategory">{{selectedCustomChartCategory.name}}</span>
          <input class="form-control cursor-pointer" type="text" placeholder="Select a chart category"
                 [disabled]="!exportChartsChecked"
                 [readOnly]="exportChartsChecked && !selectedCustomChartCategory && !selectedDefaultChartCategory"
                 *ngIf="!exportChartsChecked || (!selectedCustomChartCategory && !selectedDefaultChartCategory)"/>
          <div class="dropdown-icon" (click)="onChartCategoryClicked($event, exportChartsChecked)"></div>
        </div>

        <div ngbDropdownMenu aria-labelledby="chartCategoryDropdown">
          <span *ngFor="let item of defaultChartCategories;">
            <a *ngIf="storeService.hasFeature(item)" ngbDropdownItem
               [class]="'chart-icon chart-icon-' + item.category + ' item-bar dropdown-item'"
               [ngClass]="{'active': selectedDefaultChartCategory?.category === item.category}"
               (click)="updateSelectedChartCategory(item, null);">
              {{ item.title }}
            </a>
          </span>
          <a *ngFor="let item of customChartCategories;" ngbDropdownItem
             [ngClass]="{'active': storeService.generateCustomChartCategoryId(selectedCustomChartCategory) === storeService.generateCustomChartCategoryId(item)}"
             (click)="updateSelectedChartCategory(null, item);">
            {{ item.name }}
          </a>
        </div>
      </div>

      <app-tooltip *ngIf="exportChartsChecked && !isChartCategorySelected && showChartCategorySelectionError" class="ms-1"
                   tooltipText="Please select a chart category" [showIcon]="false">
        <i class="fa fa-exclamation-circle"></i>
      </app-tooltip>
    </div>

    <label *ngIf="typeFormat === 'pdf' || typeFormat === 'xlsx'" class="checkbox" [ngClass]="{'disabled': isExporting}">
      <input class="form-check-input" type="checkbox" [(ngModel)]="includeImages" [disabled]="isExporting">
      <span class="headline-6 mb-0">Export patent images</span>
    </label>
  </div>

  <div class="spinner" [hidden]="!isExporting">
    <div style="width: 100%; height: 100%;" class="d-flex flex-column justify-content-center align-items-center">
      <h4>Please wait, the export file is being created</h4>
      <img src="assets/images/octimine_blue_spinner.gif">

    </div>
  </div>
</div>

<div class="modal-footer justify-content-between">
  <label class="checkbox" [ngClass]="{'disabled': isExporting}">
    <input class="form-check-input" type="checkbox" [(ngModel)]="saveFields" [disabled]="isExporting">
    <span class="headline-6 mb-0">Remember selection for the next time</span>
  </label>
  <div>
    <button class="btn btn-lg btn-ghost me-2" (click)="activeModal.dismiss()" appAutofocus>Cancel</button>
    <button class="btn btn-lg btn-primary" (click)="loadChartsAndExport()"
            [disabled]="!typeFormat || isExporting || (exportChartsChecked && !isChartCategorySelected)">
      Export
    </button>
  </div>
</div>
