@import 'scss/layout2021/variables';
@import 'scss/components/chart_tabs';

.modal-body {
    position: relative;
}
.radio-button {
    width: 230px;
    border-radius: 10px;

    &.checked {
        border: 2px solid #3398b2;
        background: $dropdown-item-background;
    }

    border: 2px dashed #3398b2;

    ::before {
        top: 30px !important;
        left: -40px;
    }
    ::after {
        top: 34px !important;
        left: -36px !important;
    }
    label {
        font-weight: bold;
        color: #0484a9;
    }
    span {
        padding: 0;
    }
}

.far {
    font-size: 3rem;
    display: block;
}

.fa-file-pdf {
    color: #bd4a2a;
}

.fa-file-excel {
    color: #017e6b;
}

.fa-file-alt {
    color: #3f97af;
}

.box-selection {
    width: 45%;
    &-header {
        color: #cfcfcf !important;
        font-size: 1rem;
        font-weight: bold;
    }

    &-list {
        border: 1px solid #cdd4da;
        border-radius: 7px;
        height: 300px;
        overflow: auto;
    }

    &-item {
        display: block;
        width: 100%;
        padding: 0.25rem 1.5rem;
        clear: both;
        font-weight: 400;
        color: #212529;
        text-align: inherit;
        white-space: nowrap;
        background-color: transparent;
        border: 0;
        cursor: pointer;

        &:hover {
            background: $dropdown-item-background;
            color: $brand-green-hover;
            text-decoration: none;
        }

        &.title {
            font-weight: 600;
            padding-left: 0.5rem;
            cursor: default;
        }
        &.first {
            border-top-left-radius: 7px;
            border-top-right-radius: 7px;
        }
        &.last {
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }
        &.checked {
            background: #d6d8db7b;
            font-style: italic;
        }
        &.required {
            color: #ACADAE;
            cursor: default;
        }
    }
}

.box-button {
    width: 10%;
    height: 300px;

    .button-selection {
        display: block;
        cursor: pointer;
        border: 1px solid #00A084;
        border-radius: 7px;
        width: 62px;
        height: 62px;
        i {
            color: #00A084;
            font-size: 2rem;
        }
    }
}

.spinner {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    background: #FFF;
    opacity: 0.8;
}
.cdk {
  &-drag {
    &-placeholder {
        background-color: rgba(51, 152, 178, 0.3);
    }
  }
}

:host {
    .fa-exclamation-circle {
        color: #d8000c;
    }
}
