import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import {
  BaseStoreService,
  BooleanSearchService,
  CitationSearchService,
  CollectionService,
  ExportChartService,
  LandscapeService,
  MonitorService,
  PatentListScopeEnum,
  SemanticSearchService,
  UserService
} from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { debounceTime, filter, take } from 'rxjs/operators';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Category } from '@shared/charts';
import { ViewModeTypeEnum } from '@search/patent/types';
import { BehaviorSubject, Subscription } from 'rxjs';

declare var $;

export interface Field {
  name: string;
  title: string;
  checked: boolean;
  selected: boolean;
  required?: boolean;
  default?: boolean;
  onlyTo?: {
    semanticSearch?: boolean;
    citationSearch?: boolean;
    patentListScope?: PatentListScopeEnum;
  }
}

@Component({
  selector: 'app-export-dialog',
  templateUrl: './export-dialog.component.html',
  styleUrls: ['./export-dialog.component.scss']
})
export class ExportDialogComponent implements OnInit, OnDestroy {

  @Input() searchService: SemanticSearchService | CitationSearchService | BooleanSearchService | CollectionService | MonitorService | LandscapeService;

  @Input() additionalParams: Object;
  @Input() storeService: BaseStoreService;

  typeFormat: 'pdf' | 'xlsx' | 'csv';
  fields: Array<Field> = [
    {name: 'rank', title: 'Rank', checked: false, selected: false, required: true},
    {name: 'publication_number', title: 'Publication number', checked: false, selected: false, required: true},

    {name: '', title: 'Bibliographic information:', checked: false, selected: false},
    {name: 'title', title: 'Title', checked: false, selected: false, default: true},
    {name: 'abstract', title: 'Abstract', checked: false, selected: false, default: true},
    {name: 'applicants', title: 'Applicants', checked: false, selected: false, default: true},
    {name: 'applicants_original', title: 'Applicants (original)', checked: false, selected: false, default: true},
    {name: 'assignees', title: 'Current assignees', checked: false, selected: false, default: true},
    {name: 'assignees_original', title: 'Current assignees (original)', checked: false, selected: false, default: true},
    {name: 'inventors', title: 'Inventors', checked: false, selected: false, default: true},
    {name: 'inventors_original', title: 'Inventors (original)', checked: false, selected: false, default: true},
    {name: 'also_published_as', title: 'Patent family', checked: false, selected: false},
    {name: 'application_numbers', title: 'Application numbers', checked: false, selected: false},
    {
      name: 'authorities',
      title: 'Authorities',
      checked: false,
      selected: false,
      onlyTo: {
        patentListScope: PatentListScopeEnum.FAMILY
      }
    },
    {name: 'cpc', title: 'CPC class', checked: false, selected: false},
    {name: 'ipc', title: 'IPC class', checked: false, selected: false, default: true},
    {
      name: 'priority_date',
      title: 'Priority date',
      checked: false,
      selected: false,
      default: true,
      onlyTo: {
        patentListScope: PatentListScopeEnum.FAMILY
      }
    },
    {name: 'publication_date', title: 'Publication date', checked: false, selected: false, default: true},
    {name: 'tech_areas', title: 'Main technology areas', checked: false, selected: false, default: true},
    {name: 'tech_fields', title: 'Technology fields', checked: false, selected: false, default: true},
    {name: 'legal_status', title: 'Legal status', checked: false, selected: false, default: false},
    {name: 'docdb_family_id', title: 'DOCDB family ID', checked: false, selected: false},
    {
      name: 'similarity_index',
      title: 'Similarity',
      checked: false,
      selected: false,
      default: true,
      onlyTo: {semanticSearch: true}
    },
    {name: 'owners', title: 'Owners', checked: false, selected: false, default: false},
    {name: 'ultimate_owners', title: 'Ultimate owners', checked: false, selected: false, default: false},

    {name: '', title: 'Analytics information:', checked: false, selected: false},
    {
      name: 'risk',
      title: 'Legal risk',
      checked: false,
      selected: false,
      default: true,
      onlyTo: {
        patentListScope: PatentListScopeEnum.FAMILY
      }
    },
    {
      name: 'impact',
      title: 'Patent value',
      checked: false,
      selected: false,
      default: true,
      onlyTo: {
        patentListScope: PatentListScopeEnum.FAMILY
      }
    },
    {name: 'citation_backward_unique_count', title: 'No. of family references', checked: false, selected: false},
    {name: 'citation_forward_count_unique', title: 'No. of family citations', checked: false, selected: false},
    {name: 'green_categories', title: 'Green category', checked: false, selected: false},

    {name: '', title: 'Citation information:', checked: false, selected: false, onlyTo: {citationSearch: true}},
    {name: 'category', title: 'Category', checked: false, selected: false, onlyTo: {citationSearch: true}},
    {name: 'parent', title: 'Focal document', checked: false, selected: false, onlyTo: {citationSearch: true}},
    {name: 'level', title: 'Level', checked: false, selected: false, onlyTo: {citationSearch: true}},
    {name: 'cited_phase', title: 'Cited phase', checked: false, selected: false, onlyTo: {citationSearch: true}},
    {name: 'type', title: 'Type', checked: false, selected: false, onlyTo: {citationSearch: true}},

    {name: '', title: 'Other information:', checked: false, selected: false},
    {name: 'custom_tags', title: 'Custom tags', checked: false, selected: false},
  ];
  selectedFields: Array<Field> = [];
  isExporting = false;
  isLoadingChartsAndExporting = false;
  saveFields = false;
  includeImages = true;
  exportChartsChecked = false;
  selectedDefaultChartCategory: Category;
  selectedCustomChartCategory: { name: string, charts: Array<string>, index: number };
  showChartCategorySelectionError = false;
  patentListScope: PatentListScopeEnum = PatentListScopeEnum.FAMILY;

  private doExportSubject = new BehaviorSubject<boolean>(false);
  private generatingChartsSubject = new BehaviorSubject<{
    countCharts: number,
    searchParams: {},
    generatedCharts: { key: string, title: string, img_base64: string }[]
  }>({countCharts: 0, searchParams: {}, generatedCharts: []});
  private previousActiveChartCategory: string = null;
  private previousPatentListViewMode: ViewModeTypeEnum = null;
  private previousSingleChartColumn: boolean = null;

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private userService: UserService,
    private exportChartService: ExportChartService
  ) {
  }

  get isSemanticSearch(): boolean {
    return this.searchService instanceof SemanticSearchService;
  }

  get isCitationSearch(): boolean {
    return this.searchService instanceof CitationSearchService;
  }

  get defaultChartCategories() {
    return this.storeService.defaultChartCategories;
  }

  get customChartCategories(): { name: string, charts: Array<string>, index: number }[] {
    return this.storeService.customChartCategories;
  }

  get isChartCategorySelected(): boolean {
    return !!(this.selectedCustomChartCategory) || !!(this.selectedDefaultChartCategory);
  }

  get canTypeExportWithCharts(): boolean {
    return this.typeFormat && ['pdf', 'xlsx'].includes(this.typeFormat);
  }

  ngOnInit(): void {
    this.loadRequiredFields();
    this.loadSavedFields();

    const isCalculatingCharts$ = this.storeService.isCalculatingCharts$
      .pipe(
        debounceTime(2000)
      )
      .subscribe({
        next: (isCalculating) => {
          if (!isCalculating && this.isLoadingChartsAndExporting) {
            this.isLoadingChartsAndExporting = false;
            this.doExportSubject.next(true);
          }
        }
      });
    this.subscriptions.add(isCalculatingCharts$);

    const doExport$ = this.doExportSubject.asObservable()
      .subscribe({
        next: async (shouldExport) => {
          if (shouldExport) {
            await this.export();
          }
        }
      });
    this.subscriptions.add(doExport$);

    const generatingCharts$ = this.generatingChartsSubject.asObservable()
      .pipe(
        filter(({countCharts, generatedCharts}) => countCharts > 0 && countCharts === generatedCharts.length)
      )
      .subscribe({
      next: ({countCharts, searchParams, generatedCharts}) => {
        searchParams['charts'] = generatedCharts;
        this.doExport(searchParams);
      }
    });
    this.subscriptions.add(generatingCharts$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.restoreChartsView();
  }

  selectFormat(event): void {
    if ($('.checked').length) {
      $('.checked')[0].classList.remove('checked');
    }
    $(event.currentTarget.closest('.radio-button')).addClass('checked');

    if (!this.canTypeExportWithCharts) {
      this.exportChartsChecked = false;
      this.selectedCustomChartCategory = null;
      this.selectedDefaultChartCategory = null;
    }
  }

  onSelectField(field: Field): void {
    if (!field.name) return;

    field.selected = true;
    field.checked = false;
    this.selectedFields.push(field);
  }

  onUnSelectField(field: Field): void {
    if (!field.name || field.required) return;

    this.selectedFields = this.selectedFields.filter(fd => fd.name != field.name);
    field.selected = false;
  }

  onSelectFieldsMarked(): void {
    this.fields.forEach(fd => {
      if (fd.checked) {
        fd.checked = false;
        fd.selected = true;
        this.selectedFields.push(fd);
      }
    });
  }

  onUnSelectFieldsMarked(): void {
    this.selectedFields = this.selectedFields.map(fd => {
      if (fd.checked) {
        fd.selected = false;
        fd.checked = false;
        return null;
      }
      return fd;
    }).filter(fd => fd != null);
  }

  loadChartsAndExport() {
    this.isExporting = true;
    if (this.isChartCategorySelected) {
      this.isLoadingChartsAndExporting = true;
      this.showChartsView();
      this.reloadCharts();
    } else {
      this.doExportSubject.next(true);
    }
  }

  async export() {
    const params = {'date_format': this.userService.getLocale().dateFormatting, include_images: this.includeImages};

    switch (true) {
      case (this.storeService.selectedPatentIds?.length > 0) || (this.storeService.selectedPublications?.length > 0):
        if (this.patentListScope === PatentListScopeEnum.FAMILY) {
          params['patent_documents_ids'] = this.storeService.selectedPatentIds;
        } else {
          params['publication_numbers'] = this.storeService.selectedPublications;
        }
        break;
      case this.storeService.search['params']['filters']:
        params['search_filters'] = {free_text_query: this.storeService.search['params']['filters']};
        break;
      default:
        const searchFilters = this.storeService.search['payload']?.search_filters || {};
        params['search_filters'] = {
          ...searchFilters,
          ...this.storeService.extraFilters
        };

        const freeTextQuery = this.storeService.getAppliedFiltersQuery();

        if (freeTextQuery) {
          params['search_filters']['free_text_query'] = freeTextQuery;
        }
        break;
    }

    if (this.selectedFields.length) {
      params['custom_fields'] = this.selectedFields.map(fd => fd.name);
    }

    if (this.canTypeExportWithCharts && this.isChartCategorySelected) {
      await this.doExportWithCharts(params);
    } else {
      this.doExport(params);
    }
  }

  downloadFile(blob: Blob, name: string = null) {
    let type = '';
    switch (blob.type) {
      case 'application/pdf':
        type = 'pdf';
        break;
      case 'text/csv':
        type = 'csv';
        break;

      default:
        type = 'xlsx';
        break;
    }
    if (!name) {
      name = this.getFileName() + "_" + this.formatDateToString();
    }

    const ua = navigator.userAgent;

    const msie = ua.indexOf('MSIE');
    const trident = ua.indexOf('Trident/');
    const edge = ua.indexOf('Edge/');

    if (msie > 0 || trident > 0 || edge > 0) {
      (navigator as any).msSaveOrOpenBlob(blob, name + '.' + type);
      this.isExporting = false;
      return;
    }

    if (ua.toLowerCase().indexOf('mozilla') > -1) {
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      if (blob.type === 'application/pdf') {
        link.setAttribute('target', '_blank');
      }
      link.download = name + '.' + type;
      document.body.appendChild(link);
      link.click();
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      }, 1000);
      this.isExporting = false;
      return;
    }

    if (blob.type === 'application/pdf') {
      const link = document.createElement('a');
      link.setAttribute('target', '_blank');
      link.href = URL.createObjectURL(blob);
      link.click();
    }

    this.isExporting = false;
  }

  saveSelectedFields(): void {
    if (!this.saveFields) return;
    const fields = this.userService.getUISetting('export_custom_fields', {});
    let fieldName = 'others';
    switch (true) {
      case this.isSemanticSearch:
        fieldName = 'semantic_search';
        break;
      case this.isCitationSearch:
        fieldName = 'citation_search';
        break;
    }

    fields[fieldName] = this.selectedFields.map(fd => fd.name);

    const updateUISettings$ = this.userService.updateUISettings({export_custom_fields: fields}).subscribe({
      next: (data) => {
      },
      error: (error) => {
        console.error(error);
      }
    });
    this.subscriptions.add(updateUISettings$);
  }

  allowExportField(field: Field): boolean {
    if (!field) return false;

    if (!field.onlyTo) return true;

    if (field.onlyTo.semanticSearch && this.isSemanticSearch) return true;

    if (field.onlyTo.citationSearch && this.isCitationSearch) return true;

    if (field.onlyTo.patentListScope && field.onlyTo.patentListScope === this.patentListScope) return true;

    return false;
  }

  drop(event: CdkDragDrop<Field[]>) {
    const currentIndex = event.currentIndex;

    if (currentIndex < 2) {
      return;
    }

    moveItemInArray(this.selectedFields, event.previousIndex, event.currentIndex);
  }

  updateSelectedChartCategory(defaultChartCategory: Category, customChartCategory: {
    name: string,
    charts: Array<string>,
    index: number
  }) {
    this.selectedDefaultChartCategory = defaultChartCategory;
    this.selectedCustomChartCategory = customChartCategory;
  }

  onExportChartsChecked(event: Event) {
    if (!(<HTMLInputElement>event.target).checked) {
      this.selectedCustomChartCategory = null;
      this.selectedDefaultChartCategory = null;
    }

    this.showChartCategorySelectionError = false;

    setTimeout(() => {
      this.showChartCategorySelectionError = this.canTypeExportWithCharts && !this.isChartCategorySelected;
    }, 3000);
  }

  onChartCategoryClicked(event: MouseEvent, checked: boolean) {
    if (!checked) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  restoreChartsView() {
    if (this.previousPatentListViewMode && this.previousActiveChartCategory) {
      this.storeService.activeChartCategory = this.previousActiveChartCategory;
      this.storeService.patentListViewMode = this.previousPatentListViewMode;
      this.storeService.singleChartColumn = this.previousSingleChartColumn;

      this.previousActiveChartCategory = null;
      this.previousPatentListViewMode = null;
      this.previousSingleChartColumn = null;

      this.reloadCharts();
    }
  }

  private doExport(params: any) {
    Object.assign(params, this.additionalParams);

    let queryParams = {format: this.typeFormat};
    if (this.storeService.patentTableSort) {
      let sorting = this.storeService.patentTableSort;
      queryParams = {
        ...queryParams,
        ...sorting.field && {sort_by: sorting.field},
        ...sorting.order && {sort_order: sorting.order}
      };
    }

    this.saveSelectedFields();

    const export$ = this.searchService.export(this.storeService.searchHash, params, queryParams)
      .pipe(take(1))
      .subscribe({
        next: (file) => {
          this.restoreChartsView();
          const blob = new Blob([file], {type: file.type});
          this.downloadFile(blob);
          this.activeModal.dismiss();
        },
        error: (error) => {
          this.isExporting = false;
          this.restoreChartsView();
          throw error;
        }
      });
    this.subscriptions.add(export$);
  }

  private async doExportWithCharts(params: {}) {
    await (async () => {
      let countGenerateImagesRetries = 0;

      const generateImagesIntervalId = setInterval(async () => {
        countGenerateImagesRetries += 1;

        if (countGenerateImagesRetries > 1200) {
          clearInterval(generateImagesIntervalId);
        }

        let exportableChartElements = Array.from(document.querySelectorAll('.chart-item.exportable'));

        if (exportableChartElements.length > 0) {
          clearInterval(generateImagesIntervalId);

          const timeoutId = setTimeout(async() => {
            clearTimeout(timeoutId);

            exportableChartElements = Array.from(document.querySelectorAll('.chart-item.exportable'));

            const preparedData: {title: string, element: HTMLElement}[] = [];

            for (const ele of exportableChartElements) {
              const chartItemEle = ele as HTMLElement;
              const exportingTitle = chartItemEle.dataset.exportTitle;
              const exportingCssSelector = chartItemEle.dataset.exportCssSelector || '.chart-content';
              const exportingEle = chartItemEle.querySelector(exportingCssSelector) as HTMLElement;

              if (exportingEle) {
                preparedData.push({title: exportingTitle, element: exportingEle});
              }
            }

            const generatedCharts = [];

            for (const {title, element} of preparedData) {
              await this.exportChartService.exportChart(element, 'PNG', 2, title, false,
                () => {
                }, (dataUrl) => {
                  if (dataUrl) {
                    const content = this.exportChartService.getBase64OfImageUri(dataUrl);
                    if (content) {
                      generatedCharts.push({key: title, title: title, img_base64: content});
                      this.generatingChartsSubject.next({
                        countCharts: preparedData.length,
                        searchParams: params,
                        generatedCharts: [...generatedCharts]
                      });
                    }
                  }
                }, (error) => {
                  console.warn(error);
                });
            }
          }, 5000);
        }
      }, 100);
    })();
  }

  private formatDateToString(): string {
    const dt = new Date();
    return dt.toLocaleDateString('en-US', {
        day: ('2-digit'),
        month: ('2-digit'),
        year: ('numeric')
      }).replace(/\//g, '') +
      dt.toLocaleTimeString('en-US', {hourCycle: 'h23'}).replace(/\:/g, '');
  }

  private getFileName(): string {
    const fileName = 'octimine_';
    if (this.searchService instanceof SemanticSearchService) {
      return fileName + 'semantic_results';
    }
    if (this.searchService instanceof BooleanSearchService) {
      return fileName + 'boolean_results';
    }
    if (this.searchService instanceof CitationSearchService) {
      return fileName + 'citation_results';
    }
    if (this.searchService instanceof MonitorService) {
      return fileName + 'monitor_results';
    }
    if (this.searchService instanceof LandscapeService) {
      return fileName + 'landscape_results';
    }
    if (this.searchService instanceof CollectionService) {
      return fileName + 'collection_results';
    }

    return fileName + 'search_results';
  }

  private loadRequiredFields(): void {
    this.selectedFields = this.fields.filter(fd => fd.required)
      .map(fd => {
        fd.selected = true;
        return fd
      });
  }

  private loadDefaultFields(): void {
    this.selectedFields = [
      ...this.selectedFields,
      ...this.fields.filter(fd => fd.default)
        .map(fd => {
          fd.selected = true;
          return fd;
        })
    ];
  }

  private loadSavedFields(): void {
    const fields = this.userService.getUISetting('export_custom_fields', {});
    const typeSearch = this.isSemanticSearch ? 'semantic_search' : this.isCitationSearch ? 'citation_search' : 'others';
    if (!fields || !fields[typeSearch] || !fields[typeSearch].length) {
      this.loadDefaultFields();
      return;
    }

    fields[typeSearch].forEach(savedField => {
      if (this.selectedFields.findIndex(fd => fd.name === savedField) === -1) {
        const field = this.fields.find(fd => fd.name === savedField);
        if (this.allowExportField(field)) {
          field.selected = true;
          this.selectedFields.push(field);
        }
      } else {
        const fromIndex = this.selectedFields.findIndex(fd => fd.name === savedField);
        const field = this.selectedFields[fromIndex];
        this.selectedFields.splice(fromIndex, 1);
        this.selectedFields.push(field);
      }
    });
  }

  private reloadCharts() {
    if (this.storeService.searchHash) {
      if (!this.storeService.isCustomChartCategory() || this.storeService.getActiveCustomChartCategory()?.charts?.length > 0) {
        this.storeService.searchHash = this.storeService.searchHash;
      }
    }
  }

  private showChartsView() {
    if (this.isChartCategorySelected) {
      this.previousActiveChartCategory = this.storeService.activeChartCategory;
      this.previousPatentListViewMode = this.storeService.patentListViewMode;
      this.previousSingleChartColumn = this.storeService.singleChartColumn;

      this.storeService.activeChartCategory = this.storeService.generateCustomChartCategoryId(this.selectedCustomChartCategory) || this.selectedDefaultChartCategory.category;
      this.storeService.patentListViewMode = ViewModeTypeEnum.ANALYSIS;
      this.storeService.singleChartColumn = false;
    }
  }
}


