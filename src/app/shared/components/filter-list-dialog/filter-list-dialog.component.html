<div class="modal-header">
  <div class="modal-title" id="quick-filter-title">{{filterTitle}}</div>
  <button (click)="closeDialog('Cross click')" aria-label="Close" class="close" tabindex="-1"
    type="button"></button>
</div>
<div class="modal-body" *ngIf="!isAdvancedMode"
  [style.padding-left]="filterService.clauses.length > 1 ? '150px' : '65px'">
  <app-boolean-input [booleanSearchService]="filterService"
                     [storeService]="storeService"
                     (doSubmit)="onFilterSimpleModeSubmitted($event)">
  </app-boolean-input>
</div>
<div class="modal-body" *ngIf="isAdvancedMode">
  <app-boolean-advanced-mode [booleanSearchService]="filterService" [storeService]="storeService" (doSubmit)="onFilterSimpleModeSubmitted($event)">
  </app-boolean-advanced-mode>
</div>
<div class="modal-footer d-flex align-items-center justify-content-end">
  <div class="d-flex justify-content-end align-items-center">
    <button type="button" class="btn btn-md btn-primary-outline me-2" (click)="closeDialog('Cancel click')"
      tabindex="-1">Cancel</button>

    <ng-container *ngIf="isAdvancedMode">
      <button type="button" class="btn btn-md btn-primary-outline me-2" (click)="clearAdvancedFilter()" tabindex="-1"
        [disabled]="disableAdvancedSearchButton()">Clear</button>
      <button type="button" class="btn btn-md btn-primary" [disabled]="disableAdvancedSearchButton()"
        (click)="performAdvancedFilterAdvancedMode()">Search</button>
    </ng-container>

    <ng-container *ngIf="!isAdvancedMode">
      <button type="button" class="btn btn-md btn-primary-outline me-2" (click)="clearAdvancedFilter()" tabindex="-1"
        [disabled]="filterService.isInputEmpty()">Clear</button>
      <button type="button" class="btn btn-md btn-primary" [disabled]="!filterService.isFilterValid()"
        (click)="performAdvancedFilterSimpleMode()">Search</button>
    </ng-container>
  </div>
</div>
