import { AfterViewInit, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AdvancedFilterService, Clause, UserService } from '@core/services';
import { BaseStoreService } from '@core/store';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-filter-list-dialog',
  templateUrl: './filter-list-dialog.component.html',
  styleUrls: ['./filter-list-dialog.component.scss']
})
export class FilterListDialogComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() filterTitle?: string;
  @Input() storeService: BaseStoreService;
  isAdvancedMode = false;

  initialFilterValues: {
    isAdvancedMode: boolean,
    advancedFilterAppliedAdvancedQuery: string,
    advancedFilterAppliedBooleanClauses: Clause[],
    advancedFilterAppliedQuery: string
  };

  constructor(
    public filterService: AdvancedFilterService,
    public activeModal: NgbActiveModal,
    public userService: UserService
  ) {
  }

  get appliedBooleanClauses(): Clause[] {
    return this.storeService.advancedFilterAppliedBooleanClauses;
  }

  get appliedAdvancedQuery(): string {
    return this.storeService.advancedFilterAppliedAdvancedQuery;
  }

  get wasFiltered(): boolean {
    return this.storeService.advancedFilterAppliedQuery?.length > 0;
  }

  get advancedFilterAppliedQuery(): string {
    return this.storeService.advancedFilterAppliedQuery;
  }

  ngOnInit(): void {
    this.resetInitialFilterValues();
    this.isAdvancedMode = this.storeService.isAdvancedFilterAdvancedMode;
    this.restoreAdvancedFilterState();
  }

  ngAfterViewInit() {
    this.initialFilterValues = {
      isAdvancedMode: this.storeService.isAdvancedFilterAdvancedMode,
      advancedFilterAppliedAdvancedQuery: this.appliedAdvancedQuery,
      advancedFilterAppliedBooleanClauses: [...this.appliedBooleanClauses],
      advancedFilterAppliedQuery: this.advancedFilterAppliedQuery
    };
  }

  ngOnDestroy() {
    this.resetInitialFilterValues();
  }

  onFilterSimpleModeSubmitted(val: boolean) {
    if (val) {
      this.performAdvancedFilterSimpleMode();
    }
  }

  clearAdvancedFilter() {
    const shouldEmit = this.wasFiltered;
    this.isAdvancedMode = false;
    this.resetFilterStore();
    if (shouldEmit) {
      this.emitAdvancedFilterAndCloseDialog();
    }
  }

  onRemoveFilterSimpleModeClicked(item: Clause) {
    const clauses = this.storeService.advancedFilterBooleanClauses;
    this.storeService.advancedFilterBooleanClauses = clauses.filter((c) => c.id !== item.id);
    this.restoreAdvancedFilterClauses();
    this.emitAdvancedFilterAndCloseDialog();
  }

  clearAllBooleanClauses() {
    this.resetFilterStore();
    this.emitAdvancedFilterAndCloseDialog();
  }

  disableAdvancedSearchButton() {
    return !this.filterService.search_input || this.filterService.disableAdvancedModeSearch;
  }

  performAdvancedFilterSimpleMode() {
    if (this.filterService.areClausesValid(false)) {
      this.storeAdvancedFilterClauses();
      this.emitAdvancedFilterAndCloseDialog();
    }
  }

  performAdvancedFilterAdvancedMode() {
    this.storeService.advancedFilterBooleanClauses = [];
    this.storeAdvancedFilterAdvancedQuery();
    this.emitAdvancedFilterAndCloseDialog();
  }

  onAdvancedModeQueryChanged(val: boolean) {
    this.storeAdvancedFilterAdvancedQuery();
  }

  private resetInitialFilterValues() {
    this.initialFilterValues = {
      isAdvancedMode: false,
      advancedFilterAppliedAdvancedQuery: '',
      advancedFilterAppliedBooleanClauses: [],
      advancedFilterAppliedQuery: ''
    };
  }

  private restoreInitialFilterValues() {
    if (this.isAdvancedMode) {
      this.storeService.advancedFilterAdvancedQuery = this.initialFilterValues.advancedFilterAppliedAdvancedQuery;
      this.storeService.advancedFilterAppliedBooleanClauses = [];
      this.storeService.advancedFilterAppliedQuery = this.initialFilterValues.advancedFilterAppliedQuery;

      this.restoreAdvancedFilterAdvancedQuery();
    } else {
      this.storeService.advancedFilterAdvancedQuery = '';
      this.storeService.advancedFilterBooleanClauses = this.initialFilterValues.advancedFilterAppliedBooleanClauses;
      this.storeService.advancedFilterAppliedQuery = this.initialFilterValues.advancedFilterAppliedQuery;

      this.restoreAdvancedFilterClauses();
    }
  }

  private storeAdvancedFilterClauses() {
    this.storeService.advancedFilterBooleanClauses = this.filterService.clauses.map((c) => c.clone());
  }

  private restoreAdvancedFilterClauses() {
    this.filterService.setClauses(this.storeService.advancedFilterBooleanClauses.map((c) => c.clone()));
  }

  private restoreAdvancedFilterAdvancedQuery() {
    this.filterService.search_input = this.storeService.advancedFilterAdvancedQuery;
    this.storeService.searchInput = this.storeService.advancedFilterAdvancedQuery;
  }

  private storeAdvancedFilterAdvancedQuery() {
    this.storeService.advancedFilterAdvancedQuery = this.filterService.search_input;
  }


  private emitAdvancedFilterAndCloseDialog() {
    this.storeAdvancedFilterAppliedValues();
    this.storeService.isAdvancedFilterAdvancedMode = this.isAdvancedMode;
    this.activeModal.close(true);
  }


  private resetFilterStore() {
    this.storeService.advancedFilterAdvancedQuery = '';
    this.storeService.advancedFilterBooleanClauses = [];
    this.storeService.advancedFilterAppliedQuery = '';
    this.storeService.searchInput = '';

    this.filterService.reset(true);
  }

  private storeAdvancedFilterAppliedValues() {
    if (this.isAdvancedMode) {
      this.storeService.advancedFilterAppliedAdvancedQuery = this.filterService.search_input;
      this.storeService.advancedFilterAppliedBooleanClauses = [];
      this.storeService.advancedFilterAppliedQuery = this.filterService.search_input;
    } else {
      this.storeService.advancedFilterAppliedAdvancedQuery = '';
      const clauses = this.filterService.clauses.map((c) => c.clone()).filter(c => c.field);
      this.storeService.advancedFilterAppliedBooleanClauses = [...clauses];
      this.storeService.advancedFilterAppliedQuery = this.filterService.buildBooleanQuery();
    }
  }

  private restoreAdvancedFilterState() {
    this.restoreAdvancedFilterClauses();
    this.restoreAdvancedFilterAdvancedQuery();
  }

  closeDialog(event: string) {
    this.activeModal.dismiss(event);
  }
}
