import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FilterTermInputComponent } from './filter-term-input.component';

describe('FilterTermInputComponent', () => {
  let component: FilterTermInputComponent;
  let fixture: ComponentFixture<FilterTermInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ FilterTermInputComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FilterTermInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
