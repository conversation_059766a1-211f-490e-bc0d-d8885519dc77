import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-filter-term-input',
  templateUrl: './filter-term-input.component.html',
  styleUrls: ['./filter-term-input.component.scss']
})
export class FilterTermInputComponent {

  @Input() placeHolder = '';
  @Output() termChanged: EventEmitter<string> = new EventEmitter();

  filterTerm = '';

  constructor() { }

  searchTerm() {
    this.termChanged.emit(this.filterTerm);
  }
}
