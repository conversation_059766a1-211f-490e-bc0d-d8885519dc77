<div #filterContainer class="filters-bar" [class.always-be-sticky]="alwaysBeSticky" [hidden]="!isFiltered">
  <div class="filters-container d-flex justify-content-between" [class.sticky]="isFilterOutsideWindows()" [style.bottom]="absoluteDistanceFromBottom + 'px'">
    <div class="d-flex flex-wrap">
      <div class="mt-2 filter-item" *ngIf="displayConjugation">
        <span class="title">Combine filters with: </span>
        <button ngbTooltip="Combine filters with AND operator" container="body" (click)="onOperatorCheck('AND')" [ngClass]="{'inactive': filtersOperator !== 'AND'}">AND</button>
        <button ngbTooltip="Combine filters with OR operator" container="body" (click)="onOperatorCheck('OR')" [ngClass]="{'inactive': filtersOperator !== 'OR'}">OR</button>
      </div>
      <div *ngIf="advancedFilterQuery.length > 0" class="mt-2 filter-item">
        <span class="title">Applied filter: </span>
        <span class="value">{{ advancedFilterQuery }}</span>
        <button ngbTooltip="Click to remove this filter" container="body" (click)="resetFilterList()" class="remove-filter-button">
          <i class="fa-light fa-xmark"></i>
        </button>
      </div>
      <ng-container *ngFor="let item of getSelectedMainTechnologyAreas();">
        <div class="mt-2 filter-item">
          <span class="title">Technology Areas: </span>
          <span class="value">{{ item.label }}</span>
          <button ngbTooltip="Click to remove this filter" container="body" (click)="removeMainTechnologyAreaFilter(item.value)"
                  class="remove-filter-button">
            <i class="fa-light fa-xmark"></i>
          </button>
        </div>
      </ng-container>
      <ng-container *ngFor="let item of filters; let key = index; let last = last;">
        <div *ngIf="!isMainTechnologyArea(item.filterName)" class="mt-2 filter-item">
          <span class="title">{{ item.title }}: </span>
          <span class="value">{{ formatValue(item) }}</span>
          <button ngbTooltip="Click to remove this filter" container="body" (click)="removeChartFilter(item)" class="remove-filter-button">
            <i class="fa-light fa-xmark"></i>
          </button>
        </div>
      </ng-container>
    </div>
    <div class="btn-clear mt-2 pt-1">
      <button class="btn btn-ghost" (click)="onClearAll()" >Clear all filters</button>
    </div>
  </div>
</div>
