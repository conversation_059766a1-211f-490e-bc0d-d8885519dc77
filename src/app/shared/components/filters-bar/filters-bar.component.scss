@import 'scss/components/filters-bar';

.filters-container {
  &.sticky {
    background-color: #FFF !important;
    border-radius: 8px 8px 0 0;
    padding: 16px 72px;
    box-shadow: 0px -2px 15px 2px rgba(15, 44, 53, 0.08);
  }

  .filter-item {
    border-radius: 4px;
    padding: 6px 15px;
    background: rgba(0, 160, 131, 0.20);
    margin-right: 12px;
    display: inline;

    .title {
      padding-left: 0;
    }

    .title, .value {
      background-color: transparent;
      font-size: 0.875rem;
      line-height: 20px;
      color: $color-text-04;
    }

    button {
      background-color: transparent;
      font-size: 0.75rem;
      color: $color-text-04;
    }
  }

  .btn-clear {
    min-width: 10%;

    button {
      border-radius: 20px !important;
      padding: 6px 8px !important;
      font-size: 0.875rem !important;
      line-height: 20px !important;
      color: $color-text-03;
      background-color: transparent !important;
      text-align: right !important;

      &:hover {
        color: $brand-green;
      }
    }
  }
}
