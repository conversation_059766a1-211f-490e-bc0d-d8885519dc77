import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

import { FiltersBarComponent } from './filters-bar.component';
import { SemanticSearchStoreService } from '@core';
import { SharedModule } from '@shared/shared.module';

describe('FiltersBarComponent', () => {
  let component: FiltersBarComponent;
  let fixture: ComponentFixture<FiltersBarComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [FiltersBarComponent],
      imports: [
        HttpClientTestingModule, RouterModule.forRoot([]), SharedModule
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FiltersBarComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
