import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { PatentTableService } from '@core/services';
import { BaseStoreService } from '@core/store';
import { Subscription } from 'rxjs';
import * as $ from 'jquery';
import { DateFormatPipe, MAIN_TECHNOLOGY_AREA_KEYS, MAIN_TECHNOLOGY_AREAS, MonitorLegalStoreService } from '@core';

@Component({
  selector: 'app-filters-bar',
  templateUrl: './filters-bar.component.html',
  styleUrls: ['./filters-bar.component.scss']
})
export class FiltersBarComponent implements OnInit, OnDestroy {

  @Input() alwaysBeSticky = false;
  @Input() showOperator = false;
  @Input() distanceFromBottom: number = 0;
  @Input() storeService: BaseStoreService;
  @Input() cssClass: string;

  /**
   * reference for filter remove event emitter to parent component
   */
  @Output() filterRemoved: EventEmitter<Object> = new EventEmitter();
  @Output() clearAll: EventEmitter<Object> = new EventEmitter();

  @ViewChild('filterContainer') filterContainer: ElementRef;

  filters = [];

  distanceToTop = 0;
  absoluteDistanceFromBottom = 0;

  private subscriptions = new Subscription();

  constructor(
    private patentTableService: PatentTableService,
    private changeDetectorRef: ChangeDetectorRef,
    private dateFormatPipe: DateFormatPipe
  ) {
  }

  get advancedFilterQuery(): string {
    return this.storeService.advancedFilterAppliedQuery;
  }

  get isFiltered(): boolean {
    return this.filters?.length > 0 || this.advancedFilterQuery?.length > 0;
  }

  get displayConjugation(): boolean {
    return (this.showOperator && ((this.filters ? this.filters.length : 0) + (this.advancedFilterQuery?.length > 0 ? 1 : 0)) > 1);
  }

  get filtersOperator() {
    return this.storeService.filtersOperator;
  }

  @HostListener('window:scroll', ['$event'])
  @HostListener('window:resize', ['$event'])
  onWindowScroll($event: any) {
    this.calculateDistanceFromBottom();
  }

  ngOnInit() {
    this.filters = this.storeService.filters;
    const filters$ = this.storeService.filters$.subscribe({
      next: filters => {
        if (!(this.storeService instanceof MonitorLegalStoreService)) {
          this.filters = filters;
          this.changeDetectorRef.detectChanges();
        }
      }
    });
    this.subscriptions.add(filters$);

    this.calculateDistanceToTop();
    this.calculateDistanceFromBottom();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  isFilterOutsideWindows() {
    if (this.alwaysBeSticky) {
      return true;
    }

    return this.distanceToTop < 10 || this.distanceToTop >= window.innerHeight;
  }

  removeChartFilter(item) {
    this.filterRemoved.emit(item);
    if (item['type'] === 'result-table') {
      this.patentTableService.removeResultTableFilter(item, this.storeService);
      return;
    }
    if (item['type'] !== 'chart') {
      this.storeService.filterRemoved = item;
      return;
    }

    if (item['chart'] !== 'basic_top_applicants') {
      if (item['chart'] === 'basic_top_owners') {
        this.storeService.filterRemoved = item;
      }
      if (item['chart'] === 'basic_technology_timeline') {
        this.storeService.filterRemoved = item;
      }
      if (item['chart'] === 'basic_similarity_curve') {
        const extraFilters = this.storeService.extraFilters;
        extraFilters.similarity_index = null;
        this.storeService.extraFilters = extraFilters;
      }
      if (item['chart'] === 'citation_references') {
        const extraFilters = this.storeService.extraFilters;
        extraFilters.citation_direction = null;
        this.storeService.extraFilters = extraFilters;
      }
      if (item['chart'] === 'citation_pl_npl') {
        const extraFilters = this.storeService.extraFilters;
        extraFilters.citation_pl_npl = null;
        this.storeService.extraFilters = extraFilters;
      }
      if (item['chart'] === 'citation_phases') {
        const extraFilters = this.storeService.extraFilters;
        extraFilters.citation_phase = null;
        this.storeService.extraFilters = extraFilters;
      }
      if (item['chart'] === 'citation_types') {
        const extraFilters = this.storeService.extraFilters;
        extraFilters.citation_category = null;
        this.storeService.extraFilters = extraFilters;
      }

      const selectedValues = this.storeService.chartSelectedValues;

      if (selectedValues[item['chart']]) {
        delete selectedValues[item['chart']];
        this.storeService.chartSelectedValues = selectedValues;
      }

      const filters = [...this.storeService.filters].map(o => o);
      const index = filters.findIndex((chart) => chart.chart === item.chart);

      filters.splice(index, 1);

      this.storeService.setFilters(filters);

      return;
    }
    if (document.querySelector('app-applicants')) {
      this.storeService.filterRemoved = item;
    } else {
      this.removeTopApplicantFilter(item);
    }
  }

  resetFilterList() {
    this.storeService.resetAdvancedFilter();
    this.storeService.setFilters(this.filters);
  }

  onOperatorCheck(operator: 'AND' | 'OR') {
    this.storeService.filtersOperator = operator;
    this.storeService.setFilters(this.storeService.filters);
  }

  private calculateDistanceToTop() {
    try {
      if (!this.alwaysBeSticky) {
        this.distanceToTop = this.filterContainer.nativeElement.getBoundingClientRect().top;
      }
    } catch (e) {
    }
  }

  private calculateDistanceFromBottom() {
    try {
      this.absoluteDistanceFromBottom = this.distanceFromBottom;
      $('.sticky-bottom').each((index, ele) => {
        const ele$ = $(ele);
        if (ele$.find('.filters-container').length === 0) {
          this.absoluteDistanceFromBottom += ele$.height();
        }
      });
    } catch (e) {
    }
  }

  private removeTopApplicantFilter(item) {
    const filters = this.storeService.filters.filter(filter => filter.chart !== 'basic_top_applicants');
    this.storeService.filterRemoved = {};
    const selectedValues = this.storeService.chartSelectedValues;
    delete selectedValues['top_applicants_selected_applicant'];
    this.storeService.chartSelectedValues = selectedValues;
    this.storeService.setFilters(filters);
  }

  formatValue(item) {
    if (item.type !== 'search-filter' || item.filterName.indexOf('date') === -1) {
      return item.labelValue ?? item.value;
    }

    return this.dateFormatPipe.transform(item.value, 'ShortDate');
  }

  onClearAll() {
    this.clearPatentTableFilters();
    this.clearAll.emit();
  }

  private clearPatentTableFilters() {
    this.patentTableService.selectedLegalStatus = [];
  }

  isMainTechnologyArea(filterName: string) {
    return MAIN_TECHNOLOGY_AREA_KEYS.includes(filterName);
  }

  getSelectedMainTechnologyAreas() {
    const mtaFilterNames = this.filters.filter(filter => this.isMainTechnologyArea(filter.filterName)).map(filter => filter.filterName);
    const results = MAIN_TECHNOLOGY_AREAS.filter(mta => !mtaFilterNames.includes(mta.value));
    if (results.length === MAIN_TECHNOLOGY_AREAS.length) {
      return [];
    }
    return results;
  }

  removeMainTechnologyAreaFilter(mtaName: string) {
    let filter = this.filters.find(filter => filter.filterName === mtaName);
    if (filter) {
      filter.value = false;
    } else {
      const mta = MAIN_TECHNOLOGY_AREAS.find(mta => mta.value === mtaName);
      filter = {
        filterName: mtaName,
        title: mta.label,
        value: false,
        type: 'search-filter'
      };
    }
    this.removeChartFilter(filter);
  }
}
