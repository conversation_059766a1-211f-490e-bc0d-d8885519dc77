<div class="overflow-auto">
    <table width="100%" class="table table-condensed publication-table w-100 table-hover mb-0">
      <thead>
        <tr>
          <th (click)="onHeadClick(null)" width="10">
            <a>#</a>
          </th>
          <th>
            <a>Title</a>
          </th>
          <th (click)="onHeadClick('raw_publication_number')" [width]="200">
            <a>Publ. No.</a>
          </th>
          <th (click)="onHeadClick('ipc4')" width="200" *ngIf="showColumn('ipc4')">
            <a>IPC 4</a>
          </th>
          <th (click)="onHeadClick('applicants')" width="200" *ngIf="showColumn('applicants')">
            <a>Applicants</a>
          </th>
          <th (click)="onHeadClick('priority_date')" width="130" class="text-center" *ngIf="showColumn('priority_date')">
            <a>Priority date</a>
          </th>
          <th (click)="onHeadClick('publication_date')" width="130" class="text-center" *ngIf="showColumn('publication_date')">
            <a>Publication date</a>
          </th>
          <th (click)="onHeadClick('risk')" width="100" class="text-center" *ngIf="showColumn('risk')">
            <a>Risk</a>
          </th>
          <th (click)="onHeadClick('impact')" width="100" class="text-center" *ngIf="showColumn('impact')">
            <a>Value</a>
          </th>
          <th width="105"></th>
        </tr>
      </thead>
      <tbody *ngFor="let patent of patents; index as indexi"
        [ngClass]="{'preview-border': openedPatent.indexOf(indexi) > -1}">
        <tr [ngClass]="{'detailed':openedPatent.indexOf(indexi) > -1, 'obfuscated':patent.general?.obfuscated }">
          <td class="number-col cursor-default" nowrap="nowrap" (click)="openDetail(indexi)">
            {{ indexi + 1 }}
          </td>
          <td class="patent-title tag-selector-hover" (click)="openDetail(indexi)">
            <div class="cursor-default d-block">{{ patent?.bibliographic?.title || 'N/A' }}</div>
            <div #containerTags class="w-100 d-flex align-items-center" *ngIf="userService.hasTagFeature()">
              <app-tags-display [patent]="patent" [container]="containerTags"
                                [canManageTags]="userService.isNotExternalUser()"
                                [showIcon]="false" [updateTagsOnResize]="true"
                                [storeService]="storeService"
                                [collapsedDisplayRows]="1"
                                [expandedDisplayRows]="3"
                                (tagsChange)="onPatentTagsChanged($event, patent)"
                                (click)="$event.stopPropagation()">
              </app-tags-display>
            </div>
          </td>
          <td nowrap="nowrap" class="publication-number-col" [ngStyle]="{'font-style': patent.last_read ? 'italic' : 'normal'}" (click)="openDetail(indexi)">
            <a *ngIf="!patent.general?.obfuscated" class="cursor-pointer"
               (click)="onClickPatentViewer($event, patent, 'openSameTab')">
              <i [ngClass]="getFlagIcon(getPublicationNumber(patent))"></i>
              {{ getPublicationNumber(patent) }}
            </a>
          </td>
          <td *ngIf="showColumn('ipc4')" (click)="openDetail(indexi)" class="ip4-col">
            <span *ngFor="let ipc4 of patent?.bibliographic?.ipc4; index as indexIpc" class="d-block cursor-default"
                  [ngbTooltip]="ipcDescriptionTooltip" container="body" tooltipClass="ipc-description-tooltip">
              <ng-template #ipcDescriptionTooltip>
                <div [innerHTML]="patentTableService.getIpcDescription(ipc4)" class="text-start"></div>
              </ng-template>
              {{ ipc4 }}
            </span>
          </td>
          <td class="cursor-default" *ngIf="showColumn('applicants')" (click)="openDetail(indexi)">{{ getApplicants(patent) }}</td>
          <td class="text-center cursor-default" *ngIf="showColumn('priority_date')" (click)="openDetail(indexi)">{{
            patent.bibliographic.priority_date | dateFormat: 'ShortDate'
          }}</td>
          <td class="text-center cursor-default" *ngIf="showColumn('publication_date')" (click)="openDetail(indexi)">{{
            patent.bibliographic.publication_date | dateFormat: 'ShortDate'
          }}</td>
          <td class="text-center cursor-default" *ngIf="showColumn('risk')" (click)="openDetail(indexi)">{{ getAnalyticsRisk(patent) }}</td>
          <td class="text-center cursor-default" *ngIf="showColumn('impact')" (click)="openDetail(indexi)">{{ getAnalyticsValue(patent) }}</td>
          <td>
            <div *ngIf="!patent.general?.obfuscated"
                 class="d-flex align-items-center justify-content-center gap-spacing-sm">
              <a class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square d-flex justify-content-center align-items-center "
                 ngbTooltip="Open patent" tooltipClass="white-tooltip"
                 (click)="onClickPatentViewer($event, patent, 'openSameTab')">
                <i class="fa-regular fa-file-magnifying-glass"></i>
              </a>
              <a class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square d-flex justify-content-center align-items-center"
                 (click)="onClickPatentViewer($event, patent, 'openNewTab')"
                 ngbTooltip="Open in a new tab" tooltipClass="white-tooltip">
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
              </a>
            </div>
          </td>
        </tr>
        <app-patent-detail *ngIf="openedPatent.indexOf(indexi)>-1"
                           [patent]="patent" [colspan]="countColumnsToShow() + 5"
                           [ngClass]="'patent-detail-'+indexi"
                           hasLinksToBooleanSearch="true"
                           [isFocalPatent]="true"
                           [linkData]="linkData"
                           [storeService]="storeService"
                           backButtonTitle="Back to search" class="d-table-row patent-detail-container">
        </app-patent-detail>
      </tbody>
    </table>
  </div>
