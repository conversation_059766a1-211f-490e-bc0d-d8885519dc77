import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { FocalTableComponent } from './focal-table.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('FocalTableComponent', () => {
  let component: FocalTableComponent;
  let fixture: ComponentFixture<FocalTableComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([]),
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FocalTableComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
