import { AfterContentChecked, ChangeDetectorRef, Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { get } from 'lodash';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { MatomoService, PatentService, PatentTableService, UserService } from '@core/services';
import { Patent } from '@core/models';
import { BaseStoreService } from '@core/store';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { TagModel } from '@core/models/tag.model';

@Component({
  selector: 'app-focal-table',
  templateUrl: './focal-table.component.html',
  styleUrls: ['./focal-table.component.scss']
})
export class FocalTableComponent implements OnInit, OnDestroy, AfterContentChecked {
  @Input() columnsToShow: any[] = [];
  @Input() linkData: Object;
  @Input() pathUrl: string;
  @Input() storeService: BaseStoreService;
  selectedColumnsToShow = [];
  openedPatent = [];

  private subscriptions = new Subscription();

  constructor(
    private router: Router,
    private matomoService: MatomoService,
    private patentService: PatentService,
    public userService: UserService,
    public patentTableService: PatentTableService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
  }

  private _patents: Array<Patent>;

  get patents(): Array<Patent> {
    return this._patents;
  }

  @Input() set patents(value: Array<Patent>) {
    this._patents = value;
    const loadIpc4Description$ = this.patentTableService.loadIpc4Description(value);
    this.subscriptions.add(loadIpc4Description$);
  }

  ngOnInit() {
    if (!this.pathUrl) {
      this.pathUrl = this.router.url.split('?')[0];
    }
    this.selectedColumnsToShow = this.columnsToShow.filter(column => column.default).map(item => item);
  }

  ngAfterContentChecked() {
    this.changeDetectorRef.detectChanges();
  }

  onHeadClick(field: string) {
  }

  getIPC4(patent: Patent): string {
    if (!patent.bibliographic.ipc4) {
      return '';
    }
    return get(patent, 'bibliographic.ipc4', []).join(', ');
  }

  getApplicants(patent: Patent): string {
    if (!patent.bibliographic.applicants) {
      return 'N/A';
    }
    return get(patent, 'bibliographic.applicants', []).join(', ');
  }

  getPublicationNumber(patent: Patent): string {
    let patentNumber = 'general.raw_publication_number';
    if (patent.general.original_number_normalized) {
      patentNumber = 'general.original_number_normalized';
    }
    return get(patent, patentNumber, '').replace(/-|-/gi, '');
  }

  getAnalyticsValue(patent: Patent): string {
    const analyticsValues = [
      'Bottom 75%', 'Top 25%', 'Top 10%', 'Top 1%'
    ];
    return analyticsValues[get(patent, 'analytics.impact')] || 'N/A';
  }

  getAnalyticsRisk(patent: Patent): string {
    const analyticsValues = [
      'Bottom 75%', 'Top 25%', 'Top 10%', 'Top 1%'
    ];
    return analyticsValues[get(patent, 'analytics.risk')] || 'N/A';
  }

  openDetail(patentIndex: number) {
    const elIndex = this.openedPatent.indexOf(patentIndex);
    if (elIndex === -1) {
      this.openedPatent.push(patentIndex);
    } else {
      this.openedPatent.splice(elIndex, 1);
    }
  }

  showColumn(column): boolean {
    return this.selectedColumnsToShow.findIndex(col => col.property === column) > -1;
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  countColumnsToShow(): number {
    return ['ipc4', 'applicants', 'priority_date', 'risk', 'impact'].filter(o => this.showColumn(o)).length;
  }

  getFlagIcon(publication_number: string) {
    return this.patentService.getFlagCssByPublication(publication_number, FlagSizeEnum.MD).toLowerCase();
  }

  onClickPatentViewer(event: MouseEvent, patent: Patent, openMode: 'openNewTab' | 'openSameTab') {
    event.stopPropagation();

    const previousUrl = this.router.parseUrl(this.router.url);
    delete previousUrl.queryParams['search'];

    const patentViewUrl = ['/patent/view', patent.general.docdb_family_id];
    const storeCurrentStateId = new Date().getTime().toString(10);
    const backData = {
      storeCurrentStateId,
      ...this.linkData
    };
    const queryParams = {
      focalPatent: true,
      previousUrl: previousUrl.toString(),
      backButtonTitle: 'Back to search',
      search_hash: this.storeService.searchHash,
      back_data: JSON.stringify(backData),
    };

    this.storeService.storeCurrentState(storeCurrentStateId);
    this.storeService.storeSearchData(this.patents, this.storeService.pagination);
    this.matomoService.resultListPatentViewerButton();

    if (openMode === 'openNewTab') {
      const url = this.router.serializeUrl(this.router.createUrlTree(patentViewUrl, {queryParams: queryParams}));
      window.open(url, '_blank');
      return;
    }

    this.router.navigate(patentViewUrl, {state: {data: backData}, queryParams: queryParams});
  }

  hasTags(patent): boolean {
    return patent?.tags?.green  ||
           patent?.tags?.sep ||
           patent?.custom_tags?.length > 0;
  }

  onPatentTagsChanged(tags: TagModel[], patent: Patent) {
    if (!patent.custom_tags) {
      patent.custom_tags = [];
    }

    patent.custom_tags = tags;
  }
}
