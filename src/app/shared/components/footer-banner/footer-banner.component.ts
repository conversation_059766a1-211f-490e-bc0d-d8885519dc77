import { Component } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SupportComponent } from '../support/support.component';
import { UserService } from '@core/services';

@Component({
  selector: 'app-footer-banner',
  templateUrl: './footer-banner.component.html',
  styleUrls: ['./footer-banner.component.scss']
})
export class FooterBannerComponent {
  hideBanner: boolean;

  constructor(
    private modalService: NgbModal,
    private userService: UserService
  ) {
  }
  get isVisible(): boolean {
    return this.userService.isIpLounge();
  }

  openHubSpotForm(): void {
    const supportModalRef = this.modalService.open(SupportComponent, {centered: true, windowClass: 'modal-support'});
    supportModalRef.componentInstance.title = 'Contact us';
    supportModalRef.componentInstance.fillUserFields = false;
    supportModalRef.result.then(() => {
    }, () => {
    });
  }
}
