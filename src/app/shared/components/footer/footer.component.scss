@import 'scss/layout2021/variables';

footer {
  background-color: $brand-dark;
  padding: 30px 0px;
}
.footer{
  &-block{
    .footer-title,a{ display: block;}
    .footer-title{
      font-size: 1.125rem;
      position: relative;
      font-family: $font-open-sans-semi-bold;
      color: $color-text-01;
      border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      padding-bottom: 5px;
      margin-bottom: 15px;
    }
    a{
      color: $color-text-01;
      font-size: .875rem;
      margin-bottom: 5px;
      transition: all ease-in-out .2s;
      text-decoration: none;
      opacity: 80%;
      &:hover{
        opacity: 100%;
      }
    }
    .footer-title-border{
      border-bottom: 2px solid $light-green-color;
      width: 40px;
      position: absolute;
      bottom: -2px;
      left: 0;
    }
  }
  &-logo{
    img{max-width: 140px;}
   }
}
.footer-item {
  flex: 3;
  flex-basis: 30%;
  margin: 0 5%;

  p {
    margin-bottom: 0;
  }
}
.footer-item-small {
  flex: 1;
  font-size: .7rem;
  color: $brand-gray;
}
