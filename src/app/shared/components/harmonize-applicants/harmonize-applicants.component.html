<div class="modal-header">
  <div class="modal-title">
    <span>Applicant aliases</span>
    <p class="caption-1 mb-0">You can manually select the applicants present in your result list and group them together under new aliases. This allows you to display them as you prefer. All charts and future results will be updated accordingly. You can access this function at any time from your Workspace menu, under Applicant aliases. </p>
  </div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Close')"></button>
</div>

<div class="modal-body">

  <div class="harmonize-container d-flex flex-column justify-content-start align-items-end ps-2 pe-2 pb-2"
       *ngIf="!showSpinner else loader">
    <app-alert type="success" [message]="messageSuccess" class="w-100" *ngIf="messageSuccess"></app-alert>
    <app-alert type="danger" [message]="messageError" class="w-100" *ngIf="messageError"></app-alert>
    <div class="mb-2 w-100">
      <input type="text" class="form-control" placeholder="Filter the list" [(ngModel)]="filter"
                 (ngModelChange)="doFilter()">
    </div>
    <table class="table table-condensed w-100 m-0">
      <thead>
      <tr>
        <th width="60%">
          <label class="mb-0">
            <span>Original</span>
          </label>
        </th>
        <th width="40%">Alias</th>
      </tr>
      </thead>
    </table>

    <div class="harmonize-list w-100">
      <table class="table table-condensed">
        <tbody>
        <tr *ngFor="let applicant of normalizedApplicantAliases;">
          <td width="60%" class="m-0 pt-2 pb-2">
            <label class="checkbox mb-0">
              <input type="checkbox" [checked]="applicant.selected" (click)="selectApplicant(applicant)">
              <span>{{ applicant.original }}</span>
            </label>
          </td>
          <td width="40%" class="m-0 pt-2 pb-2">
            <div class="d-flex justify-content-start align-items-center" *ngIf="applicant.alias">
              <img class="harmonize-remove me-1" src="assets/images/times-sm.png" (click)="removeAlias(applicant)"/>
              <span>{{ applicant.alias }}</span>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="pt-3 d-flex justify-content-end align-items-start w-100 harmonize-filter-form">
      <div class="mb-3 mb-0">
        <input #inputAlias type="text" class="form-control me-2 harmonize-filter-alias"
               [readOnly]="selected.length === 0" [formControl]="alias"
               [ngClass]="{'is-invalid': isAliasInvalid()}"
               placeholder="Enter an alias"/>
        <div class="invalid-feedback">
          Please enter an alias.
        </div>
      </div>
      <button class="btn btn-primary btn-md harmonize-filter-btn-save" (click)="save()">Save</button>
    </div>
  </div>
</div>

<ng-template #loader>
  <div class="d-flex justify-content-center">
    <img src="assets/images/octimine_blue_spinner.gif">
  </div>
</ng-template>
