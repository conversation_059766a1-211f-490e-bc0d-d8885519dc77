@import 'scss/layout2021/layout2021';

.harmonize-container {
  border-color: $border-default;
  max-height: 75vh;

  .harmonize-list {
    overflow: auto;

    .harmonize-remove {
      color: #e40303;
      cursor: pointer;
    }

    td {
      height: auto !important;
    }
  }

  .harmonize-filter-form {
    .harmonize-filter-alias {
      width: 300px;
    }

    .harmonize-filter-btn-save {
      height: 38px !important;
    }
  }
}
