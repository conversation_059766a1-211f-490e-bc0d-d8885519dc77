import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { HarmonizeApplicantsComponent } from './harmonize-applicants.component';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideMatomo } from 'ngx-matomo-client';
import { SemanticSearchStoreService } from '@core';

describe('HarmonizeApplicantsComponent', () => {
  let component: HarmonizeApplicantsComponent;
  let fixture: ComponentFixture<HarmonizeApplicantsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [HarmonizeApplicantsComponent],
      providers: [NgbActiveModal, SemanticSearchStoreService, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      })],
      imports: [FormsModule, BrowserAnimationsModule, HttpClientTestingModule, RouterModule.forRoot([]) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HarmonizeApplicantsComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
