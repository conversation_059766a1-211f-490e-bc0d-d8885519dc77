import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges, OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { ApplicantAlias, ApplicantsAliasesService } from '@core/services';
import { finalize, take } from 'rxjs/operators';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormControl } from '@angular/forms';
import { BaseStoreService } from '@core/store';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-harmonize-applicants',
  templateUrl: './harmonize-applicants.component.html',
  styleUrls: ['./harmonize-applicants.component.scss']
})
export class HarmonizeApplicantsComponent implements OnInit, OnChanges, OnDestroy {
  @Input() applicants: Array<string>;
  @Input() storeService: BaseStoreService;
  @Output() hasBeenChanged: EventEmitter<boolean>  = new EventEmitter();
  selected = [];
  allSelected = false;
  showSpinner = false;
  messageError = '';
  messageSuccess = '';
  filter = '';
  normalizedApplicantAliases = [];

  private subscriptions = new Subscription();

  alias = new UntypedFormControl('');

  @ViewChild('inputAlias') inputAlias: ElementRef;

  private localList = [];

  constructor(
    private applicantsAliasesService: ApplicantsAliasesService,
    public activeModal: NgbActiveModal
  ) {
  }

  ngOnInit() {
    this.showSpinner = true;
    const getAll$ = this.applicantsAliasesService.getAllApplicantAliases()
      .pipe(
        finalize(() => this.showSpinner = false),
      )
      .subscribe({
        next: (val) => {
          this.setApplicantsAliases();
        },
        error: (error) => {
          console.error(error);
        }
      });
    this.subscriptions.add(getAll$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['applicants']) {
      this.setApplicantsAliases();
    }
  }

  doFilter() {
    this.normalizedApplicantAliases = this.localList.filter(item => {
      return item.original.toLowerCase().indexOf(this.filter.toLowerCase()) > -1 || item.alias.toLowerCase().indexOf(this.filter.toLowerCase()) > -1;
    });
  }

  selectApplicant(appl) {
    const index = this.selected.indexOf(appl.original);
    if (index > -1) {
      this.selected.splice(index, 1);
    } else {
      this.selected.push(appl.original);
    }
    appl.selected = !appl.selected;

    this.inputAlias.nativeElement.focus();
  }

  selectAllApplicants() {
    this.allSelected = !this.allSelected;
    this.normalizedApplicantAliases.forEach((item) => {
      item.selected = this.allSelected;
    });
    this.selected = this.allSelected ? this.normalizedApplicantAliases.map((val, i) => i) : [];
    this.inputAlias.nativeElement.focus();
  }

  resetMessages() {
    this.messageError = '';
    this.messageSuccess = '';
  }

  save() {
    if (this.selected.length > 0) {
      this.alias.markAsDirty();
    } else {
      this.alias.markAsPristine();
    }

    if (this.alias.value === '' || this.selected.length === 0) {
      this.inputAlias.nativeElement.focus();
      return;
    }

    this.showSpinner = true;
    this.resetMessages();
    const saveApplicants = [];
    this.localList.forEach((item) => {
      if (item.selected) {
        item.alias = this.alias.value.toUpperCase();
        item.selected = false;
        saveApplicants.push({applicant: item.original, alias: item.alias});
      }
    });

    if (saveApplicants.length > 0) {
      const save$ = this.applicantsAliasesService.save(saveApplicants).pipe(take(1)).subscribe({
        next: saved => {
          saved.data.forEach(appl => {
            const applicant = this.localList.find(item => item.original === appl.applicant);
            applicant.id = appl.id;
          });
          this.messageSuccess = `The alias *${this.alias.value}* has been saved successfully.`;
          this.resetHarmonizer();
          this.hasBeenChanged.emit(true);
          this.reloadAllApplicantAliases();
        },
        error: error => {
          this.messageError = `Error when saving the alias *${this.alias.value}*. Please try again!`;
          console.error(error);
        }
      });
      this.subscriptions.add(save$);
    }
  }

  removeAlias(applicant) {
    this.resetMessages();
    const delete$ = this.applicantsAliasesService.delete(applicant.id)
      .pipe(take(1))
      .subscribe({
        next: saved => {
          this.messageSuccess = `The alias *${applicant.alias}* of the applicant *${applicant.original}* has been deleted successfully.`;
          applicant.alias = '';
          applicant.id = null;
          this.hasBeenChanged.emit(true);
          this.reloadAllApplicantAliases();
        },
        error: error => {
          this.messageError = `Error when deleting the alias *${applicant.alias}* of the applicant *${applicant.original}*. Please try again!`;
          console.error(error);
        }
      });
    this.subscriptions.add(delete$);
  }

  private resetHarmonizer() {
    this.alias.setValue('');
    this.alias.markAsPristine();
    this.selected = [];
    this.showSpinner = false;
  }

  private setApplicantsAliases() {
    this.normalizedApplicantAliases = this.applicants
      .map(applicant => {
        if (!applicant || applicant.trim() === '') {
          return null;
        }
        const newAlias = {
          original: applicant,
          alias: '',
          selected: false,
          id: 0
        };

        const alias = this.applicantsAliasesService.allApplicantAliases.find(al => al.applicant === applicant);
        if (alias) {
          newAlias.alias = alias.alias;
          newAlias.id = alias.id;
        }

        return newAlias;

      })
      .filter(alias => alias !== null)
      .sort((a, b) => {
        if (a.original > b.original) {
          return 1;
        } else if (a.original < b.original) {
          return -1;
        }
        return 0;
      });
    this.localList = [...this.normalizedApplicantAliases];
  }

  isAliasInvalid(): boolean {
    const aliasVal = this.alias.value ? this.alias.value.trim() : '';
    return this.selected.length > 0 && aliasVal.length === 0 && this.alias.dirty;
  }

  private reloadAllApplicantAliases() {
    const obs$ = this.applicantsAliasesService.getAllApplicantAliases(true).subscribe();
    this.subscriptions.add(obs$);
  }
}
