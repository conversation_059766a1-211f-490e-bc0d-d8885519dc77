<nav class="navbar navbar-expand-lg">
  <div class="navbar-brand ms-4 py-0 my-0 check" (click)="onLogoClick()">
    <img ngSrc="assets/images/logo/Logo-stander-curves-over-dark-bg.svg" alt="Octimine logo" height="150" width="55" class="cursor-pointer">
  </div>

  <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#header-navbar"
          aria-controls="header-navbar" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>

  <div class="menu collapse navbar-collapse p-0 m-0 d-flex justify-content-between align-items-center" id="header-navbar">
    <div class="navbar-nav me-auto flex-fill d-flex justify-content-center" data-intercom-target="main-menu" *ngIf="user">
      <ng-container [ngTemplateOutlet]="iploungeNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="searchNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="monitorNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="landscapeNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="collectionNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="workspaceNavItemTemplate"></ng-container>
    </div>

    <div class="navbar-nav navbar-extras me-4 d-flex justify-content-end align-items-center" *ngIf="user">
      <ng-container [ngTemplateOutlet]="notificationsNavItemTemplate"></ng-container>
      <ng-container [ngTemplateOutlet]="settingsNavItemTemplate"></ng-container>
    </div>

    <div *ngIf="subscription && subscription.type !== 'ENTERPRISE' && userService.isNotExternalUser() && !isIPLoungeUser() && !userService.isCollaboratorUser()">
      <a id="btnUpgrade" (click)="openHubSpotForm()" class="btn btn-secondary">Upgrade</a>
    </div>
  </div>
</nav>

<div>
  <div class="modal fade" id="tutorial">
    <div class="modal-dialog" style="width: 926px; max-width: 926px;">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">Tutorial</div>
          <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close" tabindex="-1"></button>
        </div>
        <div class="modal-body">
          <iframe width="896" height="504" frameborder="0" allowfullscreen></iframe>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #iploungeNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #iploungeDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="iploungeDropdown.close()"
       *ngIf="isIPLoungeUser()">
    <div id="dropdownDennemeyer" class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(iploungeDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/logo-dennemeyer.svg" width="24" height="24" alt="Dennemeyer Apps">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
         [ngClass]="{'active': iploungeDropdown.isOpen() }">
        <div>Dennemeyer Apps</div>
        <i class="fa fa-angle-down ms-2"></i>
      </div>
    </div>

    <div ngbDropdownMenu>
      <div ngbDropdownItem class="dropdown-toggle-overlap d-flex justify-content-start align-items-center">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/logo-dennemeyer.svg" width="24" height="24" alt="Dennemeyer Apps">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <div>Dennemeyer Apps</div>
          <i class="fa fa-angle-up ms-2"></i>
        </div>
      </div>

      <div class="dropdown-toggle-overlap" ngbDropdownItem aria-labelledby="dropdownDennemeyer">Switch to</div>

      <div class="dropdown-items-container" aria-labelledby="dropdownDennemeyer">
        <div *ngFor="let item of ipLoungeServices" routerLinkActive [routerLink]="getBaseUrl(item.targetUrl)" #rla="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions"
             (click)="navigateToUrl(item.url)" ngbDropdownItem
             class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center justify-content-center">
            <div class="ip-lounge-icon me-3 p-1">
              <div [ngStyle]="{'background-image': 'url(' + getIpLoungeIcon(item) + ')'}"></div>
            </div>
            <div class="text-wrap">{{ item.serviceName }}</div>
          </div>
          <i class="fas fa-check ms-auto ip-lounge-check ps-3" *ngIf="isMenuItemActive(rla.isActive, item)"></i>
        </div>
      </div>

    </div>
  </div>
</ng-template>

<ng-template #searchNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #searchDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="searchDropdown.close()">
    <div id="navbarDropdownSearch" class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(searchDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/nav/nav-search.svg" width="24" height="24" alt="Search">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
         [ngClass]="{'active': semanticRouterLink.isActive || booleanRouterLink.isActive || citationRouterLink.isActive || nplRouterLink?.isActive || historyRouterLink.isActive || searchDropdown.isOpen() }">
        <div>Search</div>
        <i class="fa fa-angle-down ms-2"></i>
      </div>
    </div>

    <div ngbDropdownMenu aria-labelledby="navbarDropdownSearch">
      <div ngbDropdownItem class="dropdown-toggle-overlap d-flex justify-content-start align-items-center"
           (click)="goRouterLink('/search/patent')">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/nav/nav-search-hover.svg" width="24" height="24" alt="Search">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <a class="ms-0" routerLink="/search/patent">Search</a>
          <i class="fa fa-angle-up ms-2"></i>
        </div>
      </div>

      <a class="ms-0" routerLinkActive="active" routerLink="/search/patent" (click)="goRouterLink('/search/patent')" #semanticRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Semantic</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/boolean" (click)="goRouterLink('/boolean')" #booleanRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Boolean</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/citation" (click)="goRouterLink('/citation')" #citationRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Citations</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/npl-search" (click)="goRouterLink('/npl-search')" #nplRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" appBeta ngbDropdownItem>Non-patent literature</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/history-saves/history" (click)="goRouterLink('/history-saves/history')" #historyRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Search history</a>
    </div>
  </div>
</ng-template>

<ng-template #monitorNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #monitorDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="monitorDropdown.close()"
       *ngIf="userService.hasMonitor">
    <div class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(monitorDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/nav/nav-monitor.svg" width="24" height="24" alt="Monitor">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
         [ngClass]="{'active': monitorRouterLink.isActive || legalStatusRouterLink.isActive || monitorDropdown.isOpen() }">
        <div>Monitor</div>
        <i class="fa fa-angle-down ms-2"></i>
        <i *ngIf="hasNewSharedMonitorProfiles()" class="fas fa-circle header-notifications-dot"></i>
      </div>
    </div>

    <div ngbDropdownMenu>
      <div ngbDropdownItem class="dropdown-toggle-overlap d-flex justify-content-start align-items-center"
           (click)="goRouterLink('/monitor')">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/nav/nav-monitor-hover.svg" width="24" height="24" alt="Monitor">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <a class="ms-0" routerLink="/monitor">Monitor</a>
          <i class="fa fa-angle-up ms-2"></i>
          <i *ngIf="hasNewSharedMonitorProfiles()" class="fas fa-circle header-notifications-dot"></i>
        </div>
      </div>
      <a class="ms-0" routerLinkActive="active" routerLink="/monitor" (click)="goRouterLink('/monitor')"  #monitorRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>New publications</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/monitor/legalStatus" (click)="goRouterLink('/monitor/legalStatus')" #legalStatusRouterLink="routerLinkActive"  [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Legal status changes</a>
    </div>
  </div>
</ng-template>

<ng-template #landscapeNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #landscapeDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="landscapeDropdown.close()"
       *ngIf="userService.hasFeature('landscape')">
    <div class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(landscapeDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/nav/nav-landscape.svg" width="24" height="24" alt="Landscape">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
           [ngClass]="{'active': landscapeRouterLink.isActive || landscapeDropdown.isOpen() }">
        <div>Landscape</div>
        <i *ngIf="hasNewSharedLandscapeProfiles()" class="fas fa-circle header-notifications-dot"></i>
      </div>
    </div>

    <div ngbDropdownMenu>
      <div ngbDropdownItem class="dropdown-toggle-as-item d-flex justify-content-start align-items-center"
           (click)="goRouterLink('/landscape')">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/nav/nav-landscape-hover.svg" width="24" height="24" alt="Landscape">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <a class="ms-0" routerLink="/landscape" routerLinkActive="active" #landscapeRouterLink="routerLinkActive"
             [routerLinkActiveOptions]="routerLinkActiveOptions">Landscape</a>
          <i *ngIf="hasNewSharedLandscapeProfiles()" class="fas fa-circle header-notifications-dot"></i>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #collectionNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #collectionDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="collectionDropdown.close()">
    <div class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(collectionDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/nav/nav-patent-collection.svg" width="24" height="24" alt="Patent collection">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
         [ngClass]="{'active': savedDocumentsRouterLink.isActive ||  annotatedDocumentsRouterLink.isActive ||  openedDocumentsRouterLink.isActive || collectionDropdown.isOpen() }">
        <div>Patent collection</div>
        <i class="fa fa-angle-down ms-2"></i>
        <i *ngIf="hasNewSharedCollections()" class="fas fa-circle header-notifications-dot"></i>
      </div>
    </div>

    <div ngbDropdownMenu>
      <div ngbDropdownItem class="dropdown-toggle-overlap d-flex justify-content-start align-items-center"
           (click)="goRouterLink('/collections')">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/nav/nav-patent-collection-hover.svg" width="24" height="24" alt="Patent collection">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <a class="ms-0" routerLink="/collections" >Patent collection</a>
          <i class="fa fa-angle-up ms-2"></i>

          <i *ngIf="hasNewSharedCollections()" class="fas fa-circle header-notifications-dot"></i>
        </div>
      </div>
      <a class="ms-0" routerLinkActive="active" routerLink="/collections" (click)="goRouterLink('/collections')" #savedDocumentsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>My collections </a>
      <a class="ms-0" routerLinkActive="active" routerLink="/collections/tagged" (click)="goRouterLink('/collections/tagged')" #openedDocumentsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Tags</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/collections/documents/annotated" (click)="goRouterLink('/collections/documents/annotated')" #annotatedDocumentsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Comments & highlights</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/collections/documents/opened" (click)="goRouterLink('/collections/documents/opened')" #openedDocumentsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Recently opened</a>
    </div>
  </div>
</ng-template>

<ng-template #workspaceNavItemTemplate>
  <div class="main-menu-item dropdown-menu-overlap" ngbDropdown #workspaceDropdown="ngbDropdown" placement="bottom-start" display="dynamic" (mouseleave)="workspaceDropdown.close()">
    <div class="d-flex justify-content-start align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(workspaceDropdown)">
      <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
        <img ngSrc="/assets/images/nav/nav-workspace.svg" width="24" height="24" alt="Workspace">
      </div>

      <div class="dropdown-toggle-text d-flex align-items-center justify-content-center"
         [ngClass]="{'active': myTeamRouterLink.isActive ||  applicantAliasesRouterLink.isActive || ratingsRouterLink.isActive || workspaceDropdown.isOpen() }">
        <div>Workspace</div>
        <i class="fa fa-angle-down ms-2"></i>
      </div>
    </div>

    <div ngbDropdownMenu>
      <div ngbDropdownItem class="dropdown-toggle-overlap d-flex justify-content-start align-items-center"
           (click)="goRouterLink(workspaceMenuItemUrl())">
        <div class="dropdown-toggle-icon d-flex align-items-center justify-content-center">
          <img ngSrc="/assets/images/nav/nav-workspace-hover.svg" width="24" height="24" alt="Workspace">
        </div>

        <div class="dropdown-toggle-text d-flex align-items-center justify-content-center">
          <a class="ms-0" [routerLink]="workspaceMenuItemUrl()">Workspace</a>
          <i class="fa fa-angle-up ms-2"></i>
        </div>
      </div>

      <a class="ms-0" routerLinkActive="active" routerLink="/users" (click)="goRouterLink('/users')" *ngIf="userService.getUser()?.profile?.company_id"  ngbDropdownItem>My team</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/applicant-aliases" (click)="goRouterLink('/applicant-aliases')" #applicantAliasesRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Applicant aliases</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/tags" (click)="goRouterLink('/tags')" *ngIf="userService.hasTagFeature()" #labelsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Tags manager</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/ratings" (click)="goRouterLink('/ratings')" *ngIf="userService.hasTaskFeature()" ngbDropdownItem>Ratings</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/ratings" (click)="goRouterLink('/ratings')" #ratingsRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" class="d-none" hidden>Ratings</a>
      <a class="ms-0" routerLinkActive="active" routerLink="/users" (click)="goRouterLink('/users')" #myTeamRouterLink="routerLinkActive" [routerLinkActiveOptions]="routerLinkActiveOptions" class="d-none" hidden>My team</a>
    </div>
  </div>
</ng-template>

<ng-template #notificationsNavItemTemplate>
  <div id="dropdownNotification" *ngIf="userService.canUseWorkflowFeature()" class="dropdown-notifications" ngbDropdown #notificationsDropdown="ngbDropdown" placement="bottom-end" display="dynamic">
    <div class="cursor-pointer d-flex justify-content-center align-items-center" ngbDropdownAnchor (mouseenter)="openDropdown(notificationsDropdown)">
      <i class="fas fa-bell"></i>
      <i *ngIf="hasNewNotifications()" class="fas fa-circle header-notifications-dot"></i>
    </div>

    <div class="p-0 dropdown-notifications-content" ngbDropdownMenu (mouseleave)="notificationsDropdown.close()" aria-labelledby="dropdownNotification">
      <app-notifications headline="Notification feed" subtitle="Most recent" [loadNotificationsOnScrolling]="true">
      </app-notifications>
    </div>
  </div>
</ng-template>

<ng-template #settingsNavItemTemplate>
  <div class="main-menu-item dropdown-menu-classic dropdown-settings ms-2" ngbDropdown #settingsDropdown="ngbDropdown" container="app-header" placement="bottom-end" display="dynamic">
    <div id="dropdownProfile" class="cursor-pointer d-flex justify-content-center align-items-center" ngbDropdownAnchor data-intercom-target="settings" (mouseenter)="openDropdown(settingsDropdown)">
      <i class="fa fa-ellipsis-v"></i>
    </div>

    <div ngbDropdownMenu (mouseleave)="settingsDropdown.close()" aria-labelledby="dropdownProfile">
      <a class="ms-0" routerLink="/profile" routerLinkActive="active" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Settings</a>
      <a class="ms-0" routerLink="/data-coverage" routerLinkActive="active" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>Data coverage</a>
      <a class="ms-0" routerLink="/api" routerLinkActive="active" [routerLinkActiveOptions]="routerLinkActiveOptions" ngbDropdownItem>API</a>
      <a class="ms-0 btn-sign-out" href="javascript:void(0)" (click)="signout()" ngbDropdownItem>Signout</a>
    </div>
  </div>
</ng-template>
