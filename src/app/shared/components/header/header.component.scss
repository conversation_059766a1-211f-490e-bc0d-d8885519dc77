@import 'scss/layout2021/variables';
@import 'scss/layout2021/mixins';

.navbar {
  background: transparent linear-gradient(180deg, #457281 0%, #355E6B 100%) 0 0 no-repeat padding-box;
  @include box-shadow(0 15px 30px #0F2C350D);
  height: 79px;

  .navbar-brand {
    img {
      width: 150px;
      height: 55px;
    }
  }

  .navbar-extras {
    width: 150px;
  }
}

.ip-lounge-icon {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  min-width: 40px;
  box-shadow: 0 2px 8px #00000029;

  div {
    width: 100%;
    height: 100%;
  }
}

.menu-item {
  display: block;
  margin-left: 0px !important;
  padding: 5px 20px 5px 40px;
}

.header-notifications-dot {
  font-size: 8px;
  color: #FF6700;
  background: #FF6700;
  border: 2px solid #FFFFFF;
  position: absolute !important;
  @include border-radius(10px);
}

.dropdown-notifications {
  .dropdown-toggle {
    width: 32px !important;
    height: 32px !important;
    background: #FF6700 0 0 no-repeat padding-box;
    border: 1px solid #FF6700;
    @include border-radius(3px);
    color: #FFFFFF;

    &:hover {
      color: white !important;
      background: #FF5122;
    }

    .header-notifications-dot {
      top: -5px;
      right: -5px;
    }
  }

  .dropdown-notifications-content {
    max-height: none !important;
    background-color: transparent !important;
    min-width: 442px !important;
    z-index: 1021;
  }
}

@include keyframes(rotate-dropdown-icon) {
  from {
    transform-origin: center;
    transform: rotate(-0.5turn);
  }
  to {
    transform-origin: center;
    transform: rotate(0turn);
  }
}

.main-menu-item {
  .dropdown-toggle {
    padding: 10px 15px 10px 0;
    display: block;
    height: 43px;
    outline: none;
    font-family: $font-open-sans-semi-bold;
    font-size: 16px;
    line-height: 24px;
    color: #FFFFFF;
  }

  .dropdown-toggle-icon {
    width: 46px;
    height: 46px;
    border-bottom: 2px solid rgba(56, 154, 133, .7);
  }

  .dropdown-toggle-text {
    height: 46px;
    border-bottom: 2px solid rgba(255, 255, 255, .1);
    padding-right: 10px;
    position: relative;
    color: #FFFFFF;
    font-family: $font-open-sans-semi-bold;
    font-size: 16px;
    line-height: 24px;

    &.active {
      border-bottom: 2px solid rgba(56, 154, 133, .7);
    }

    .fa-angle-up {
      @include animate(rotate-dropdown-icon, 0.5s, linear, 1);
    }
  }

  .header-notifications-dot {
    top: 5px;
    left: -15px;
  }

  .dropdown-menu {
    min-width: max-content !important;
    @include border-radius(3px);
    background: #FFFFFF 0 0 no-repeat padding-box;
    box-shadow: 0 5px 10px #0F2C3529;
    padding: 0 !important;
    outline: none;
    border: none;
    z-index: 99999;
    @include animate-fade-in(0.5s);

    .dropdown-item {
      min-width: 150px !important;
      max-width: 350px !important;
      outline: none;
    }

    > .dropdown-item, .dropdown-item > a {
      color: #0F2C35 !important;
    }

    .dropdown-items-container {
      max-height: 40vh !important;
      overflow-y: auto !important;
    }
  }

  &.dropdown-menu-classic {
    .dropdown-toggle {
      background: #FFFFFF 0% 0% no-repeat padding-box;
      box-shadow: 0 5px 10px #0F2C3529;
      border: 1px solid #FFFFFF;
      border-radius: 3px;
    }

    .dropdown-menu {
      > .dropdown-item {
        font-family: $font-open-sans-semi-bold;
        font-size: 14px;
        line-height: 28px;
        padding: 10px 15px;

        &:hover, &:active, &:focus, &.active {
          color: #01526F !important;
          background: #CBDCE2 0 0 no-repeat padding-box !important;
        }
      }
    }
  }

  &.dropdown-menu-overlap {
    .dropdown-menu {
      top: -46px !important;

      > .dropdown-item {
        &:first-child {
          font-family: $font-open-sans-semi-bold;
          font-size: 16px;
          line-height: 24px;
          border-bottom: 2px solid #389A85;
          padding: 0 15px 0 0;
          height: 45px;

          .dropdown-toggle-icon {
            border-bottom: none;
          }

          .dropdown-toggle-text, .dropdown-toggle-text > a {
            color: #0F2C35;
          }
        }

        &:not(:first-child) {
          font-family: $font-open-sans-semi-bold;
          font-size: 14px;
          line-height: 28px;
          padding: 10px 15px;
        }

        &.dropdown-toggle-overlap {
          cursor: pointer;
          color: #0F2C35;

          &:hover, &:active, &:focus {
            color: inherit !important;
            background: inherit !important;
          }
        }

        &.dropdown-toggle-as-item {
          cursor: pointer;
        }

        &:not(.dropdown-toggle-overlap):not(.dropdown-toggle-as-item) {
          &:hover, &:active, &:focus, &.active {
            color: #01526F !important;
            background: #CBDCE2 0 0 no-repeat padding-box !important;
          }
        }
      }
    }
  }

  &.dropdown-settings {
    .dropdown-toggle {
      width: 32px;
      height: 32px;
      background: transparent;
      border: none;
      padding: unset;
      @include border-radius(3px);

      &:hover, &:active, &:focus, &.active {
        background: #F9F9F9 0 0 no-repeat padding-box;
        border: 1px solid #BCCACE;
        color: #01526F;
      }
    }

    .dropdown-menu {
      border: 1px solid #BCCACE;
    }
  }
}

::ng-deep {
  .dropdown-menu .notification-container {
    background: #F9F9F9 padding-box !important;
    box-shadow: 0px 0px 16px #00000014 !important;
    border: 1px solid #BCCACE !important;
    border-radius: 5px;
    font-family: $font-open-sans-regular;
    margin: 0;
    max-width: 440px !important;
    max-height: 493px !important;

    a {
      color: #002832;
    }

    .notification-title {
      border-bottom: 1px solid #CBDCE2 !important;
      font-size: 1rem;
      padding-bottom: 10px !important;
      margin-bottom: 15px !important;
    }

    .notification-sub-title {
      font-size: 12px;
      color: #00A083;
      cursor: pointer;

      .disabled {
        color: #BCCACE;
        cursor: auto;
      }

      margin-top: 0.8rem;
    }

    .notification {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
      padding-bottom: 1rem !important;
      border-bottom: none !important;
      position: relative;

      a {
        margin: 0;
        background: white padding-box !important;
        width: 396px !important;
        min-height: 84px;
        box-shadow: 0 10px 15px #0000000D;

        .highlight {
          color: #002832 !important;
        }
      }
    }

    .notification-new {
      right: 10px !important;
      top: -5px !important;
    }
  }
}
