import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QueryList, ViewChildren } from '@angular/core';
import { IsActiveMatchOptions, Router } from '@angular/router';

import { of, Subscription } from 'rxjs';
import { catchError } from 'rxjs/operators';
import {
  IpLoungeService,
  Notifications,
  NotificationsService,
  RoutingHistoryService,
  UserService
} from '@core/services';
import { UserProfile, UserSubscription } from '@core/models';
import { IpLounge } from '@core/services/ip-lounge/types';
import { NgbDropdown, NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { SupportComponent } from '@shared/components';
import { StoreDataManagerService } from '@core/store/store-data-manager/store-data-manager.service';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements On<PERSON><PERSON>t, On<PERSON><PERSON><PERSON> {
  @ViewChildren(NgbDropdown) dropdowns: QueryList<NgbDropdown>;

  user: UserProfile;
  ipLoungeServices: Array<IpLounge> = [];
  isShowingIpLoungeServices = false;
  routerLinkActiveOptions = {
    matrixParams: 'ignored',
    queryParams: 'ignored',
    paths: 'exact',
    fragment: 'exact'
  } as IsActiveMatchOptions;
  subscription: UserSubscription;

  private checkNewNotificationsInterval = null;
  private CHECK_NEWS_NOTIFICATIONS_INTERVAL = 60000;
  private supportModalRef: NgbModalRef = null;
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    private router: Router,
    private zone: NgZone,
    private routingHistoryService: RoutingHistoryService,
    public notificationsService: NotificationsService,
    private ipLoungeService: IpLoungeService,
    private modalService: NgbModal,
    private storeDataManagerService: StoreDataManagerService,
  ) {
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: u => {
        if (u) {
          this.user = u.profile;
          this.subscription = u.subscription;
          if (this.user && this.user.ui_settings && 'first_login' in this.user.ui_settings) {
            delete this.user.ui_settings['first_login'];
            const updateProfile$ = this.userService.updateProfile(this.user).subscribe();
            this.subscriptions.add(updateProfile$);
          }
        }
      }
    });
    this.subscriptions.add(user$);

    if (this.userService.canUseWorkflowFeature()) {
      this.zone.runOutsideAngular(() => {
        const self = this;
        this.checkNewNotificationsInterval = setInterval(function () {
          self.checkNewNotifications();
        }, this.CHECK_NEWS_NOTIFICATIONS_INTERVAL);
      });
    }

    if (this.ipLoungeService.hasIPLoungeAccess()) {
      this.ipLoungeServices = this.ipLoungeService.getIPLoungeServices();
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    if (this.checkNewNotificationsInterval) {
      clearInterval(this.checkNewNotificationsInterval);
    }
  }

  signout() {
    const signout$ = this.userService.signout().subscribe({
      next: () => {
        this.routingHistoryService.purgePreviousUrls();
        this.storeDataManagerService.clearAllStoredData();
        this.router.navigate(['auth/login']);
      }
    });
    this.subscriptions.add(signout$);
  }

  getUserName() {
    return this.userService.formatName(this.user);
  }

  hasNewNotifications(): boolean {
    return this.notificationsService.notifications?.findIndex(o => this.isNotificationUnread(o)) > -1;
  }

  hasNewSharedMonitorProfiles(): boolean {
    return this.notificationsService.notifications?.findIndex(o =>
      this.isNotificationUnread(o) && this.isCopiedMonitorType(o)) > -1;
  }

  hasNewSharedLandscapeProfiles(): boolean {
    return this.notificationsService.notifications?.findIndex(o => this.isNotificationUnread(o) && this.isLandscapeType(o)) > -1;
  }


  hasNewSharedCollections(): boolean {
    return this.notificationsService.notifications?.findIndex(o => this.isNotificationUnread(o) &&
      (this.isCollectionType(o) || this.isPatentType(o))) > -1;
  }

  isNotificationUnread(n: Notifications): boolean {
    return this.notificationsService.isNotificationUnread(n);
  }

  isCollectionType(n: Notifications): boolean {
    return this.notificationsService.isCollectionType(n);
  }

  isPatentType(n: Notifications): boolean {
    return this.notificationsService.isPatentType(n);
  }

  isCopiedMonitorType(n: Notifications): boolean {
    return this.notificationsService.isCopiedMonitorType(n);
  }

  isLandscapeType(n: Notifications): boolean {
    return this.notificationsService.isLandscapeType(n);
  }

  isIPLoungeUser(): boolean {
    return this.user && this.ipLoungeService.hasIPLoungeAccess();
  }

  getIpLoungeIcon(item: IpLounge) {
    if (item.serviceIcon) {
      return 'data:image/svg+xml;base64,' + item.serviceIcon;
    }
    return '/assets/images/ip-lounge-default.svg';
  }

  getBaseUrl(targetUrl: string): string {
    const parts = targetUrl.split('/').filter((v) => v.length > 0);
    return parts.length > 0 ? '/' + parts[0] : targetUrl;
  }

  isMenuItemActive(isRouterLinkActive: boolean, ipLounge: IpLounge): boolean {
    if (this.ipLoungeService.isOctimineService(ipLounge.serviceCode)) {
      if (isRouterLinkActive) {
        return true;
      }

      const hasActiveLink = this.ipLoungeServices.find((v) => {
        return this.router.isActive(this.getBaseUrl(v.targetUrl), false);
      });

      if (!hasActiveLink) {
        return ipLounge.serviceCode === 'octimine';
      }
    }
    return false;
  }

  private checkNewNotifications() {
    const loadNotifications$ = this.notificationsService.loadNotifications({})
      .pipe(
        catchError((err) => {
          console.error(err);
          return of(null);
        })
      )
      .subscribe({
        next: (res) => {
          if (res) {
            for (const n of res.notifications) {
              const existed = this.notificationsService.notifications.find(o => o.id === n.id);
              if (existed) {
                existed.status = n.status;
              } else {
                const getTeamUsers$ = this.userService.getTeamUsers({id: 'eq:' + n.from_user_id})
                  .subscribe({
                    next: ({users}) => {
                      const userList = Object.fromEntries(users.map(o => [o.id, o]));
                      n.from_user = userList[n.from_user_id];
                      const notifications = [...this.notificationsService.notifications];
                      notifications.unshift(n);
                      this.notificationsService.notifications = notifications;
                    }
                  });
                this.subscriptions.add(getTeamUsers$);
              }
            }
          }
        }
      });
    this.subscriptions.add(loadNotifications$);
  }

  goRouterLink(link) {
    if (link === this.router.url) {
      this.router.navigate(['/'], {skipLocationChange: true}).then( () => {
        this.router.navigate([link]);
      });
    } else {
      this.router.navigate([link]);
    }
  }

  openHubSpotForm(): void {
    const self = this;
    this.supportModalRef = this.modalService.open(SupportComponent, {centered: true, windowClass: 'modal-support'});
    this.supportModalRef.componentInstance.title = 'Contact us';
    this.supportModalRef.componentInstance.fillUserFields = true;
    this.supportModalRef.result.then(() => {
      self.supportModalRef = null;
    }, () => {
      self.supportModalRef = null;
    });
  }

  navigateToUrl(url: string) {
    window.location.href = url;
  }

  openDropdown(dropdown: NgbDropdown) {
    if (this.dropdowns) {
      this.dropdowns.toArray().forEach(el => {
        el.close();
      });
    }

    if (dropdown) {
      dropdown.open();
    }
  }

  workspaceMenuItemUrl(): string {
    return this.userService.getUser()?.profile?.company_id ? '/users' : '/applicant-aliases';
  }
  onLogoClick(){
    if(!this.userService.isNotExternalUser()){
      this.routingHistoryService.purgePreviousUrls();
      this.storeDataManagerService.clearAllStoredData();
    }
    this.router.navigate(['launchpad']);
  }
}
