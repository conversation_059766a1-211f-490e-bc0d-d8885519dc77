import { Component, Input, OnDestroy, OnInit } from '@angular/core';

declare function createInlineManualPlayer(o: any): any;

declare var $: any;

@Component({
  selector: 'app-inline-manual',
  templateUrl: './inline-manual.component.html'
})
export class InlineManualComponent implements OnInit, OnDestroy {

  @Input()
  manualID: string = null;

  e: HTMLScriptElement;
  manual: any;

  constructor() {
  }

  ngOnInit() {
    const self = this;
    if (window['inline_manual']) {
      if (window['inline_manual_id'] === this.manualID) {
        window['inline_manual'].update();
        return;
      } else {
        delete window['inline_manual_id'];
        delete window['inline_manual'];
      }
    }
    this.e = document.createElement('script');
    const t = document.getElementsByTagName('script')[0];
    this.e.async = true;
    this.e.src = 'https://inlinemanual.com/embed/player.' + this.manualID + '.js';
    t.parentNode.insertBefore(this.e, t);
    this.e.addEventListener('load', function () {
      window['inline_manual'] = createInlineManualPlayer(window['inlineManualPlayerData']);
      window['inline_manual_id'] = self.manualID;
    });
  }

  ngOnDestroy() {
    if (this.e) {
      this.e.remove();
      $('.inmplayer-general').remove();
    }
  }
}
