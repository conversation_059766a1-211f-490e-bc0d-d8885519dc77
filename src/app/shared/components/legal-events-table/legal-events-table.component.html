<div class="legal-events-title d-flex justify-content-between align-items-center">
  <div class="le-title">Legal events</div>

  <div class="d-flex justify-content-end align-items-center">
    <div class="me-3">Show:</div>

    <button type="button" class="btn btn-md btn-link me-2 d-flex justify-content-between align-items-center"
            *ngFor="let impact of impacts; let last = last;" (click)="onImpactClicked(impact.impact)"
            [ngClass]="{'me-3': last, 'active': isImpactActive(impact.impact)}">
      <div class="d-flex justify-content-start align-items-center me-2">
        <i class="{{ getImpactIcon(impact.impact) }}"></i>
        <div class="ms-2">{{impact.title}}</div>
      </div>

      <i [ngClass]="{'invisible': !isImpactActive(impact.impact)}" class="fa fa-times"
         (click)="removeCurrentImpact($event)"></i>
    </button>

    <div ngbDropdown *ngIf="!isFigma2023 else figma2023Dropdown" container="body">
      <button class="btn btn-md btn-link btn-outline-info" type="button" id="dropdownSelectPatent"
      ngbDropdownToggle aria-haspopup="true" aria-expanded="false">
        <i *ngIf="documentInfo?.length && selectedPatentNumber" class="dot {{getFamilyMemberLegalStatus(selectedPatentNumber).icon}}"></i>
        {{ selectedPatentNumber ? selectedPatentNumber : 'Select patent'}}
      </button>
      <div ngbDropdownMenu aria-labelledby="dropdownSelectPatent">
        <a (click)="removeSelectedPatent($event)" class="dropdown-item" href="javascript:void(0)">--- Show all ---</a>
        <a *ngFor="let publication of patent.bibliographic?.also_published_as;"
           class="dropdown-item" href="javascript:void(0)" (click)="onPatentSelected(publication)">
           <i *ngIf="documentInfo?.length" class="dot {{getFamilyMemberLegalStatus(publication).icon}}"></i>
           <span class="ms-1" [ngClass]="getFlagIcon(publication)"></span>
           {{publication}}
        </a>
      </div>
    </div>
    <ng-template #figma2023Dropdown>
      <div ngbTooltip="Show by" tooltipClass="white-tooltip" class="figma-dropdown">
        <button [ngbPopover]="popoverFilterTemplate" [autoClose]="true"
                popoverClass="context-menu-popper" container="body" placement="bottom-right"
                class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small">
          <span class="p-x-spacing-xx-s menu-text">{{ selectedFilter }}</span>
          <i class="fa-regular fa-lg fa-bars-filter ms-2"></i>
        </button>
      </div>
    </ng-template>
  </div>
</div>

<div *ngIf="legalEvents?.length > 0 else noLegalEvents" class="overflow-hidden">
  <table *ngIf="!loading else loader" id="legal-events-table"
         class="table table-condensed publication-table w-100 table-hover">
    <thead class="w-100">
    <tr>
      <th class="text-center">
        <a>Impact</a>
      </th>
      <th>
        <a>Document</a>
      </th>
      <th nowrap="nowrap">
        <a>Event indicator</a>
      </th>
      <th>
        <a>Category</a>
      </th>
      <th>
        <a>Event description</a>
      </th>
      <th nowrap="nowrap">
        <a>Event date</a>
      </th>
      <th nowrap="nowrap">
        <a>Effective date</a>
      </th>
      <th>
      </th>
    </tr>
    </thead>
    <tbody *ngFor="let le of legalEvents | paginate:
    { itemsPerPage: page?.page_size,
      id: 'legal-events-pagination',
      currentPage: page?.current_page,
      totalItems: page?.total_hits
    }; let index = index;" class="le-event-row">
    <tr [ngClass]="{'cursor-pointer': le.event_attributes}">
      <td (click)="openDetail(index)" class="text-center" nowrap="nowrap">
        <i class="{{getImpactIcon(le.impact)}}"></i>
      </td>
      <td (click)="openDetail(index)">
        {{le.publication_number}}
      </td>
      <td (click)="openDetail(index)">
        {{le.event_code}}
      </td>
      <td (click)="openDetail(index)">
        {{getCategory(le)}}
      </td>
      <td (click)="openDetail(index)" class="le-col-description">
        {{le.event_code_description}}
      </td>
      <td (click)="openDetail(index)" nowrap="nowrap">
        {{le.event_date | dateFormat: 'ShortDate'}}
      </td>
      <td (click)="openDetail(index)" nowrap="nowrap">
        {{le.effective_date | dateFormat: 'ShortDate'}}
      </td>
      <td (click)="openDetail(index)" class="text-end">
        <ng-container *ngIf="le.event_attributes">
          <div class="button-main-secondary-grey button-medium button-square">
            <span *ngIf="!openedLegalEvents[index]" class="caret-down"><i class="fa-regular fa-angle-down"></i></span>
            <span *ngIf="openedLegalEvents[index]" class="caret-up"><i class="fa-regular fa-angle-up"></i></span>
          </div>
        </ng-container>
      </td>
    </tr>
    <ng-container *ngIf="openedLegalEvents[index] && le.event_attributes;">
      <ng-container *ngFor="let eventObj of le.event_attributes; let last = last;">
        <tr class="le-detail" *ngFor="let attKey of getObjectKeys(eventObj);">
          <td colspan="2" class="border-0"></td>
          <td colspan="2" class="border-0 text-end le-detail-title">{{attKey}}</td>
          <td colspan="3" class="border-0">{{eventObj[attKey]}}</td>
        </tr>
        <tr *ngIf="!last" class="le-detail le-detail-separator">
          <td colspan="2" class="border-0"></td>
          <td colspan="5"></td>
        </tr>
      </ng-container>
    </ng-container>
    </tbody>
  </table>

  <div class="d-flex justify-content-between mb-2">
      <div class="col-5 reference-text">
        * See the extent ouf our legal data coverage
        <a routerLink="/data-coverage" fragment="data-coverage"> here</a>
      </div>
      <div class="col-6">
        <pagination-controls *ngIf="!loading" (pageChange)="onPageChange($event)" id="legal-events-pagination" autoHide="true"
                            class="d-flex justify-content-end" directionLinks="false" maxSize="10">
        </pagination-controls>
      </div>
  </div>

  <ng-template #loader>
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
  </ng-template>
</div>

<ng-template #noLegalEvents>
  <div class="full-text" *ngIf="!loading">
    <p>No legal events are available for this patent</p>
    <br><br>
  </div>
</ng-template>

<ng-template #popoverFilterTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Show</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium p-r-spacing-s"
       *ngFor="let filter of filterOptions"
       (click)="sortList(filter)"
       [class.active]="filter === selectedFilter">
    <span class="p-r-spacing-xxx-big">{{ filter }} </span>
  </div>
</ng-template>
