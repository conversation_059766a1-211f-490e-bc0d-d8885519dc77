@import 'scss/layout2021/variables';

.legal-events-title {
  border-bottom: 1px solid #BCCACE;
  padding-bottom: 1.25rem;
  margin-bottom: 1.875rem;
  margin-top: 1.875rem;

  .le-title {
    font-size: 36px;
  }

  .btn-link {
    text-decoration: none;
    color: #4B5D66;
    font: $font-open-sans-regular;
    font-size: 16px;
    padding: 0 10px !important;
    margin: 0 10px;

    &.active {
      color: #FFFFFF !important;
      background-color: #00A083;
    }
  }

  .dropdown {
    .dropdown-toggle {
      border-color: #698A95;
      background-color: #FFFFFF;

      &:hover {
        background-color: #FFFFFF;
      }
    }

    .dropdown-item {
      width: 100%;
      padding: 2px 10px;
    }
  }
}

.le-event-row {
  .le-col-description {
    color: #005470;
  }

  .le-detail {
    &:hover {
      background-color: white;
    }

    .le-detail-title {
      color: $color-text-04;
      font-size: 18px;
      font-family: $font-open-sans-bold;
      white-space: nowrap;
      text-align: right;
    }

    &.le-detail-separator {
      td {
        height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
      }
    }
  }
}
