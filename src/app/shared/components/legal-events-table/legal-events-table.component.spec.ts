import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LegalEventsTableComponent } from './legal-events-table.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { Patent } from '@core/models';
import { provideMatomo } from 'ngx-matomo-client';

describe('LegalEventsTableComponent', () => {
  let component: LegalEventsTableComponent;
  let fixture: ComponentFixture<LegalEventsTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LegalEventsTableComponent],
      imports: [SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LegalEventsTableComponent);
    component = fixture.componentInstance;
    const patent = {general: {}} as Patent;
    patent.general['docdb_family_id'] = '123';
    component.patent = patent;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
