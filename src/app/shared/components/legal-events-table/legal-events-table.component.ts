import { Component, Input, OnDestroy } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { LegalEvent, LegalEventImpactEnum, Patent } from '@core/models';
import { LegalStatusService, PaginationMetadata, PatentService } from '@core/services';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { Subscription } from 'rxjs';
import { FilterTableOptionEnum } from '@patent/types';

@Component({
  selector: 'app-legal-events-table',
  templateUrl: './legal-events-table.component.html',
  styleUrls: ['./legal-events-table.component.scss']
})
export class LegalEventsTableComponent implements OnDestroy {

  private _patent: Patent;
  private subscriptions = new Subscription();

  @Input() set patent(value: Patent) {
    this._patent = value;
    this.selectedPatentNumber = this.patent?.general.raw_publication_number;
    this.currentImpact = null;
    this.page = null;
    this.selectedFilter = FilterTableOptionEnum.CURRENT_DOCUMENT;
    this.loadLegalEvents();
  }

  get patent(): Patent {
    return this._patent;
  }

  @Input()
  documentInfo: Array<any>;
  @Input()
  isFigma2023: boolean;

  loading = false;

  legalEvents: Array<LegalEvent> = [];
  openedLegalEvents = {};
  page: PaginationMetadata;

  impacts = [
    {impact: LegalEventImpactEnum.POSITIVE, title: 'Positive'},
    {impact: LegalEventImpactEnum.NEGATIVE, title: 'Negative'},
    {impact: LegalEventImpactEnum.NEUTRAL, title: 'Neutral'},
  ];

  selectedPatentNumber: string;

  filterOptions: FilterTableOptionEnum[] = [FilterTableOptionEnum.CURRENT_DOCUMENT, FilterTableOptionEnum.ALL_FAMILY];
  selectedFilter = FilterTableOptionEnum.CURRENT_DOCUMENT;

  private currentImpact: LegalEventImpactEnum;

  constructor(
    private patentService: PatentService,
    private legalStatusService: LegalStatusService
  ) {
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  openDetail(index: number) {
    this.openedLegalEvents[index] = !this.openedLegalEvents[index];
  }

  getCategory(le: LegalEvent): string {
    return [le.event_class, le.event_class_description].filter(v => v).join(': ');
  }

  getObjectKeys(obj: {}): Array<string> {
    return Object.keys(obj);
  }

  isImpactActive(impact: LegalEventImpactEnum): boolean {
    return this.currentImpact === impact;
  }

  getImpactIcon(impact: LegalEventImpactEnum): string {
    switch (impact) {
      case LegalEventImpactEnum.POSITIVE:
        return 'fa fa-plus-circle';
      case LegalEventImpactEnum.NEGATIVE:
        return 'fa fa-minus-circle';
      default:
        return 'far fa-circle';
    }
  }

  onImpactClicked(impact: LegalEventImpactEnum) {
    this.currentImpact = impact;
    this.page = null;
    this.loadLegalEvents();
  }

  removeCurrentImpact(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.page = null;
    this.currentImpact = null;
    this.loadLegalEvents();
  }

  onPageChange($event: number) {
    this.page.current_page = $event;
    this.loadLegalEvents();
  }

  onPatentSelected(familyId: string) {
    this.page = null;
    this.selectedPatentNumber = familyId;
    this.loadLegalEvents();
  }

  removeSelectedPatent(event: MouseEvent) {
    this.page = null;
    this.selectedPatentNumber = null;
    this.loadLegalEvents();
  }

  private buildPayload() {
    const payload = {
      page: this.page?.current_page ? this.page.current_page : 1
    };

    if (this.isFigma2023) {
      if (this.selectedFilter !== FilterTableOptionEnum.ALL_FAMILY) {
        payload['publication_number'] = this.selectedPatentNumber;
      }
    } else {
      if (this.selectedPatentNumber) {
        payload['publication_number'] = this.selectedPatentNumber;
      }
    }

    if (this.currentImpact) {
      payload['impact'] = this.currentImpact;
    }

    return payload;
  }

  private loadLegalEvents() {
    this.loading = true;

    const getLegalEvents$ = this.legalStatusService.getLegalEvents(this.patent.general.docdb_family_id, this.buildPayload())
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (val) => {
          this.page = val.page;
          this.legalEvents = val.legal_events;
        }
      });
    this.subscriptions.add(getLegalEvents$);
  }

  getFamilyMemberLegalStatus(publication: string) {
    const activePublication = this.documentInfo.find(doc => doc.publication_number === publication);
    return this.patentService.getPatentLegalStatusIcon(activePublication);
  }

  getFlagIcon(publication_number: string) {
    return this.patentService.getFlagCssByPublication(publication_number, FlagSizeEnum.MD).toLowerCase();
  }

  sortList(sort) {
    this.page = null;
    this.selectedFilter = sort;
    this.loadLegalEvents();
  }
}
