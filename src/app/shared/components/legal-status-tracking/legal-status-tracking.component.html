<button type="button" class="btn btn-primary{{!isAnyLegalStatusTracked() ? '-outline' : ''}} mt-4 mb-2 {{ ready ? '' : 'disabled'  }}"
        (click)="openLegalStatusTrackingModal(legalStatusTrackingModalContent)">
        <i class="fas fa-bell"></i>  {{isAnyLegalStatusTracked() ? "Tracking legal status" : "Track legal status"}}
</button>

<ng-template #legalStatusTrackingModalContent let-modal>
  <div class="modal-header">
    <div class="modal-title">Legal Status Tracking</div>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')" tabindex="-1"></button>
  </div>
  <div class="modal-body p-0 m-0">
    <div class="m-2">
      <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>
    </div>
    <div class="overflow-auto publication-table" style="max-height: 500px">
      <app-spinner *ngIf="!ready"></app-spinner>
      <table class="table table-condensed publication-table w-100 table-hover mb-0 mt-0" *ngIf="ready">
        <thead class="w-100">
          <tr>
            <th width="30">
              <label class="checkbox m-0 p-0" *ngIf="trackablePublications.length > 0">
                <input type="checkbox" (change)="checkAllPublications()" [checked]="areAllPublicationsChecked()">
                <span class="no-text">&nbsp;</span>
              </label>
            </th>
            <th width="30">
              <span>#</span>
            </th>
            <th style="min-width: 100px;" >
              <span>Publ. No.</span>
            </th>
            <th style="min-width: 130px;">
              <span>Status</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let publication of trackablePublications; index as i">
            <td>
              <label class="checkbox m-0 p-0">
                <input type="checkbox" (change)="checkPublication(publication)"
                      [checked]="isPublicationChecked(publication)">
                <span class="no-text">&nbsp;</span>
              </label>
            </td>
            <td class="number-col" nowrap="nowrap">
              {{ i + 1 }}
            </td>
            <td class="publication-number-col" nowrap="nowrap" [ngStyle]="{'font-style': 'normal'}">
              <span>{{ publication }}</span>
            </td>
            <td class="text-break" >
              Not tracked by any monitoring profile yet
            </td>
          </tr>
          <tr *ngFor="let entry of alreadyTrackedPublications | keyvalue; index as i">
            <td>
            </td>
            <td class="number-col" nowrap="nowrap">
              {{ i + 1 + trackablePublications.length }}
            </td>
            <td class="publication-number-col" nowrap="nowrap" [ngStyle]="{'font-style': 'normal'}">
              <span>{{ entry.key }}</span>
            </td>
            <td class="text-break" >
              <i class="fas fa-check-circle"></i>&nbsp;&nbsp;Already being tracked by:
              <ul>
                <li *ngFor="let profile of entry.value">
                  <a href="/monitor/profile/{{ profile.profile_id }}/result">{{ profile.profile_name }}</a>
                </li>
              </ul>
            </td>
          </tr>
          <tr *ngFor="let entry of untrackablePublications | keyvalue; index as i">
            <td>
            </td>
            <td class="number-col" nowrap="nowrap">
              {{ i + 1 + trackablePublications.length + (alreadyTrackedPublications | keyvalue).length }}
            </td>
            <td class="publication-number-col" nowrap="nowrap" [ngStyle]="{'font-style': 'normal'}">
              <span>{{ entry.key }}</span>
            </td>
            <td class="text-break">
              <i class="fas fa-exclamation-triangle"></i>&nbsp;&nbsp;{{ entry.value }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="modal-footer">
    <div class="d-flex justify-content-between align-items-center w-100">
      <div class="modal-hint flex-fill">
      </div>

      <button class="btn btn-md btn-ghost me-2" (click)="modal.close([])">Cancel</button>

      <button class="btn btn-md btn-primary" ngbTooltip="Track legal status" (click)="trackLegalStatus()"
              [disabled]="checkedPublications.length === 0">
        Track legal status
      </button>
    </div>
  </div>
</ng-template>
