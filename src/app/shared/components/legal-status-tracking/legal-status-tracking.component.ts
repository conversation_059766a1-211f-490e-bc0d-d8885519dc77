import { Component, Input, OnDestroy, TemplateRef } from '@angular/core';
import { MatomoService, MonitorService } from '@core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-legal-status-tracking',
  templateUrl: './legal-status-tracking.component.html',
  styleUrls: ['./legal-status-tracking.component.scss']
})
export class LegalStatusTrackingComponent implements OnDestroy {
  private _publications: Array<string>;
  private subscriptions = new Subscription();

  @Input() public set publications(value: Array<string>) {
    if (value) {
      this._publications = value;
      this.refreshState();
    }
  }

  public get publications(): Array<string> {
    return this._publications;
  }

  trackablePublications: Array<string> = []
  checkedPublications: Array<string> = [];
  untrackablePublications: Object = {};
  alreadyTrackedPublications: Object = {};
  successMessage: string;
  ready: boolean = false;

  modal: NgbActiveModal = null;

  constructor(
    private modalService: NgbModal,
    private monitorService: MonitorService,
    private matomoService: MatomoService
  ) { }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private refreshState() {
    this.ready = false;
    const isLegalStatusTrackable$ = this.monitorService.isLegalStatusTrackable(this.publications).subscribe({
      next: response => {
        this.untrackablePublications = response.data['not_trackable'];
        const getLegalStatusTracking$ = this.monitorService.getLegalStatusTracking(this.publications).subscribe({
          next: response => {
            this.alreadyTrackedPublications = response.data;
            this.trackablePublications = this.publications.filter(p => !(p in this.alreadyTrackedPublications) && !(p in this.untrackablePublications));
            this.checkedPublications = [];
            this.ready = true;
          }
        });
        this.subscriptions.add(getLegalStatusTracking$);
      }
    });
    this.subscriptions.add(isLegalStatusTrackable$);
  }

  openLegalStatusTrackingModal(modalContent: TemplateRef<any>) {
    if (!this.isAnyLegalStatusTracked()) {
      this.matomoService.monitorLegalStatusTrackButton();
    }
    this.modal = this.modalService.open(modalContent, {size: 'lg'});
  }

  checkAllPublications() {
    this.areAllPublicationsChecked() ? this.checkedPublications = [] : this.checkedPublications = [...this.trackablePublications];
  }

  checkPublication(publication: string) {
    const index = this.checkedPublications.indexOf(publication);
    if (index == -1) {
      this.checkedPublications.push(publication);
    } else {
      this.checkedPublications.splice(index, 1);
    }
  }

  isPublicationChecked(publication: string) {
    return this.checkedPublications.indexOf(publication) != -1;
  }

  areAllPublicationsChecked() {
    return this.checkedPublications.length == this.trackablePublications.length;
  }

  trackLegalStatus() {
    this.successMessage = undefined;
    const trackLegalStatus$ = this.monitorService.trackLegalStatus(this.checkedPublications).subscribe({
      next: response => {
        this.successMessage = response.data.message;
        this.refreshState();
      }
    });
    this.subscriptions.add(trackLegalStatus$);
  }

  isAnyLegalStatusTracked() {
    return Object.keys(this.alreadyTrackedPublications).length > 0;
  }

  isLegalStatusTracked(publication: string) {
    return (publication in this.alreadyTrackedPublications);
  }
}
