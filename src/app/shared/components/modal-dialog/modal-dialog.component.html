<div class="dialog">
  <div class="dialog-header p-b-spacing-big">
    <div class="d-flex align-items-center justify-content-between">
      <div class="content-heading-h3 content-color-primary">{{options.title}}</div>
      <div class="dialog-close">
        <span (click)="activeModal.dismiss()">
          <i class="fa-light fa-xmark cursor-pointer p-spacing-s d-flex justify-content-center align-items-center"></i>
        </span>
      </div>
    </div>
    <div class="content-body-small content-color-tertiary m-t-spacing-sm" *ngIf="options.description" [innerHTML]="options.description"></div>
  </div>
  <div class="content-body-medium content-color-primary" [innerHTML]="options.question"></div>
  <div class="dialog-footer d-flex justify-content-end m-t-spacing-xxx-big">
    <button class="button-main-secondary-grey" (click)="activeModal.dismiss()">{{options.cancelButton}}</button>
    <button [ngClass]="options.buttonClass || 'button-destructive-primary'" (click)="activeModal.close(true)">{{options.confirmButton}}</button>
  </div>
</div>
