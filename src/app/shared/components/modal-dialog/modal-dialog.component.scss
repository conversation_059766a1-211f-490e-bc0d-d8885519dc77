@import 'scss/figma2023/variables';
@import 'scss/figma2023/mixins';
@import 'scss/figma2023/typography';

.dialog {
  padding: $spacing-system-spacing-xx-big;
  gap: $spacing-system-spacing-xxx-big;
  border-radius: 8px;
  background: $colours-background-bg-primary;
  box-shadow: 0px 6px 15px -3px rgba(40, 40, 40, 0.20);

  &-header {
    border-bottom: 1px solid $colours-border-subtle;
    margin-bottom: $spacing-system-spacing-md;
  }
  &-close {
    padding: $spacing-system-spacing-x-s;
    border-radius: $radius-sm;
    &:hover {
      background-color: $colours-buttons-main-tertiary-grey-bg-hover;
    }

    span {
      border-radius: $radius-sm;
      text-align: center;
      i {
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
  &-footer {
    gap: $spacing-system-spacing-md;
  }
}

::ng-deep {
  .modal-backdrop.show {
    opacity: 0.3;
  }
}
