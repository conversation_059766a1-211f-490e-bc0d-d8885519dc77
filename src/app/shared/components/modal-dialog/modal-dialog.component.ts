import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-modal-dialog',
  templateUrl: './modal-dialog.component.html',
  styleUrls: ['./modal-dialog.component.scss']
})
export class ModalDialogComponent {

  @Input() options: {
    title?: string,
    description?: string,
    question?: string,
    confirmButton?: string,
    cancelButton?: string,
    buttonClass?: string } = {};

  constructor(public activeModal: NgbActiveModal) {}

}
