<div class="modal-header">
  <div class="modal-title">Set up monitor</div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
  <app-alert type="danger" [message]="error" *ngIf="error"></app-alert>
  <div class="disable-layer" *ngIf="isSavingShare"></div>
  <form [formGroup]="form" (ngSubmit)="onSubmit()">

    <div class="mb-3">
      <label class="open-sans-bold mb-3 form-label">
        New profile
      </label>
      <input type="text" formControlName="name" class="form-control profile-name" placeholder="Name of your profile *"
        maxlength="15" />
      <small id="emailHelp" class="form-text text-muted">e.g. Chemical (max. 15 characters)</small>
    </div>

    <label class="open-sans-bold mb-3">Report delivery</label>
    <div class="mb-3">
      <label class="color-2">
        Interval of alerts
        <sup>*</sup>
      </label>
      <div class="checkbox-wrap custom-radio">
        <label class="radio-button occurrence-weekly d-inline-block me-2">
          <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="Weekly" />
          <span>Weekly</span>
        </label>
        <label class="radio-button occurrence-biweekly d-inline-block me-2">
          <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="BiWeekly" />
          <span>Every two weeks</span>
        </label>
        <label class="radio-button occurrence-monthly d-inline-block me-2">
          <input type="radio" formControlName="delivery_frequency" name="delivery_frequency" value="Monthly" />
          <span>Monthly</span>
        </label>
      </div>
    </div>
    <div class="mb-3">
      <label class="color-2">
        Email attachment
        <sup>*</sup>
      </label>
      <div class="checkbox-wrap form-check">
        <label *ngFor="let item of methods.controls; let i=index;" [class]="'checkbox  me-2 delivery-'+dMethod[i].name">
          <input type="checkbox" [formControl]="item" (change)="onMethodChange($event, i)">
          <span>{{dMethod[i].name}}</span>
        </label>
      </div>
    </div>
  </form>

</div>
<div class="modal-footer">
  <div class="d-flex justify-content-end align-items-center w-100">

    <img src="/assets/images/octimine_blue_spinner.gif" style="width: 35px;" class="me-2" *ngIf="isSavingShare">

    <button class="btn btn-md btn-primary" [disabled]="!this.form.valid || isSavingShare"
      (click)="onSubmit()">Save</button>
  </div>
</div>
