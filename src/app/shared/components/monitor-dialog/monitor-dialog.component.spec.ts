import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MonitorDialogComponent } from './monitor-dialog.component';
import { NgControl } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorDialogComponent', () => {
  let component: MonitorDialogComponent;
  let fixture: ComponentFixture<MonitorDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MonitorDialogComponent ],
      providers: [NgbActiveModal, NgControl, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
