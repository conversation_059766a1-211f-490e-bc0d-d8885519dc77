import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { UntypedFormGroup, UntypedFormControl, UntypedFormArray, Validators } from '@angular/forms';
import { MonitorService, ConfirmationDialogService } from '@core';
import { map } from 'lodash';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-monitor-dialog',
  templateUrl: './monitor-dialog.component.html',
  styleUrls: ['./monitor-dialog.component.scss']
})
export class MonitorDialogComponent implements OnDestroy {

  /**
   * form for profile setting
   */
  form: UntypedFormGroup;
  dMethod: any = [
    { name: 'PDF', value: 'PDF', selected: true },
    { name: 'Excel list', value: 'Excel', selected: false },
    { name: 'CSV', value: 'CSV', selected: false }
  ];

  canCreateProfile: boolean;

  semantic_query;
  boolean_query;
  error: string;
  isPublications?: boolean;

  isSavingShare: boolean;

  private subscriptions = new Subscription();

  createDMethod() {
    const arr = this.dMethod.map(item => {
      return new UntypedFormControl(item.selected || false);
    });
    return new UntypedFormArray(arr);
  }

  get methods(): UntypedFormArray {
    return this.form.get('delivery_method') as UntypedFormArray;
  }
  constructor(
    public activeModal: NgbActiveModal,
    private confirmationDialogService: ConfirmationDialogService,
    public monitorService: MonitorService) {
    this.form = new UntypedFormGroup({
      name: new UntypedFormControl('', [Validators.required]),
      delivery_method: this.createDMethod(),
      delivery_frequency: new UntypedFormControl('Weekly', [Validators.required])
    }
    );
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getCheckedMethod() {
    return map(this.form.controls.delivery_method['controls'], (item, i) => {
      return item.value && this.dMethod[i].value;
    });
  }

  onMethodChange(event, index: number) {
    if (event.target.checked) {
      this.dMethod.forEach((method, i) => {
        if (i !== index) {
          this.dMethod[i].selected = false;
        } else {
          this.dMethod[i].selected = true;
        }
      });
      this.form.setControl('delivery_method', this.createDMethod());
    }
  }

  onSubmit() {
    if (!this.form.valid) {
      alert('Please fill out the form');
      return;
    }
    const payload = this.form.value, dme = [];
    this.getCheckedMethod().forEach(element => {
      if (element) {
        dme.push(element);
      }
    });
    payload.delivery_method = dme.length > 0 ? dme.join(', ') : 'Text';

    if (this.semantic_query) {
      payload.semantic_query_active = true;
      payload.semantic_query = this.semantic_query;
    }

    if (this.boolean_query) {
      payload.boolean_query_active = true;
      payload.boolean_query = this.boolean_query;
    }
    if(this.isPublications){
      payload.scope = "Publications";
    }
    this.isSavingShare = true;
    const newProfile$ = this.monitorService.newProfile( payload).subscribe({
      next: profile=>{
        const initRuns$ = this.monitorService.initMonitorRun(profile['id']).subscribe({
          next: (res) => {
            this.isSavingShare = false;
            this.activeModal.close();
            this.confirmationDialogService.alert('Monitor alert',
              `Monitor profile has been created. <a href="/monitor/profile/${profile['id']}/result"  target="_blank">Click here</a> to view the profile.`);
          }, error: err => {
          this.isSavingShare = false;
          this.error = `Error while triggering result sets for newly <a target="_blank" href="/monitor/profile/${profile['id']}/setting">created profile</a>`;
        }});
        this.subscriptions.add(initRuns$)
      },
      error: error=>{
        console.log(error);
        this.isSavingShare = false;
        this.error = 'Error while creating new monitoring profile.';
      }
    });
    this.subscriptions.add(newProfile$)
  }
}
