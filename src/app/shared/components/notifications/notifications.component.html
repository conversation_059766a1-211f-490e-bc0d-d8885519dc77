<ul #notificationsEle class="position-relative notification-container p-1 m-0" (scroll)="onNotificationsScrolled(notificationsEle)">
  <a class="position-absolute notification-sub-title me-3" (click)="markAllNotificationsAsRead()" *ngIf="notifications.length">
    <i class="fa fa-angle-right" aria-hidden="true"></i> Mark all as read
  </a>
  <li class="notification-title mb-0 pt-2 ps-3 pb-4" *ngIf="headline?.length">
      <div class="d-flex justify-content-between">{{headline}}</div>
    </li>
    <li class="d-flex justify-content-between notification-sub-title ps-3 pe-3 pb-2" *ngIf="subtitle?.length">
      <span class="disabled">{{subtitle}}</span>
    </li>

    <li *ngFor="let n of notifications;" class="notification notification-icon">
        <a class="d-flex align-items-center justify-content-center notification-item" (click)="onNotificationClicked(n)">
            <i *ngIf="isNotificationUnread(n)" class="fas fa-circle notification-new"></i>
          <app-user-avatar [user]="getUser(n)" [hasSubTitle]="false" class="notification-avatar mb-2" size="medium" [showTooltip]="false">
            <div class="circle-share d-flex align-items-center justify-content-center" [style.backgroundColor]="getNotificationIconColor(n)">
              <img [src]="getNotificationIcon(n)" width="60%">
            </div>
          </app-user-avatar>
          <div class="w-100 notification-text">
            <span [innerHTML]="getNotificationTitle(n)"></span><span>&nbsp;{{n.created_at | timeReadable}}</span>
          </div>
        </a>
    </li>
    <li class="text-end p-2 caption-1 m-0"
        *ngIf="notifications.length && !isLoadingNotifications && !loadNotificationsOnScrolling &&
        pagination?.current_page < pagination?.last_page">
        <a href="javascript:void(0)" (click)="loadNextNotifications()">Load more</a>
    </li>

    <li *ngIf="!notifications.length && !isLoadingNotifications">
        <div class="notification-title no-notification widget-no-content">
        No notifications currently
        </div>
    </li>
    <li class="d-flex justify-content-center align-items-center" *ngIf="isLoadingNotifications">
        <img src="assets/images/octimine_blue_spinner.gif" class="loading-spinner">
    </li>
</ul>
