@import 'scss/layout2021/variables';

.notification-container {
  max-height: 500px;
  overflow: auto;
  background-color: #F8F8F8;

  .notification {
    border-bottom: 1px solid #dee2e6 !important;

    ::ng-deep {
      .highlight {
        font-weight: bold !important;
        color: #00A083 !important;
      }
    }

    a {
      color: #002832;
    }

    a:hover {
      color: #FF6700 !important;

      ::ng-deep {
        .highlight {
          color: #FF6700 !important;
        }
      }
    }
  }
}

.notification-title {
  color: #0F2C35;
  font-size: 1.5rem;
  font-weight: bold;
}

.notification-sub-title {
  font-size: 12px;
  color: #00A083;
  cursor: pointer;

  .disabled {
    color: #BCCACE;
    cursor: auto;
  }

  right: 0;
  z-index: 12;
  margin-top: 1.4rem;
}

.notification-item {
  padding: 5px 10px;
  cursor: pointer;
  text-decoration: none;

}

.notification-avatar {
  height: 40px;
  margin-right: 10px;
  position: relative;
}

.notification-text {
  font-family: $font-open-sans-regular;
  font-size: 14px;
  padding-right: 5px;
}

.notification-new {
  font-size: 8px;
  color: #FF6700;
  position: absolute;
  right: 5px;
  top: 10px;
}

.notification-icon {
  position: relative;
}

.loading-spinner {
  height: 50px;
}

.circle-share {
  background: #FF6700 0% 0% no-repeat padding-box;
  width: 27px;
  height: 27px;
  position: absolute;
  right: -5px;
  top: 23px;
  border-radius: 15px;
  font-size: 14px;
}

.notification-date {
  color: #698A95;
}
