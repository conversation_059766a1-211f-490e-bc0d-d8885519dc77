import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CollaborationStatusEnum, Notifications, NotificationsService, TeamUser } from '@core';
import { BehaviorSubject, of, Subscription } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss']
})
export class NotificationsComponent implements OnInit, OnDestroy {

  @Input() headline: string;
  @Input() subtitle: string;
  @Input() loadNotificationsOnScrolling = false;
  @Input() isLoadingNotifications = false;

  private subscriptions = new Subscription();
  private loadNextNotificationsSubject = new BehaviorSubject<boolean>(false);

  constructor(
    public router: Router,
    public notificationsService: NotificationsService
  ) { }

  get notifications() {
    return this.notificationsService.notifications;
  }
  get pagination() {
    return this.notificationsService.pagination;
  }

  ngOnInit(): void {
    if (!this.notifications) {
      this.notificationsService.notifications = [];
      this.loadNotifications();
    }
    if (this.loadNotificationsOnScrolling) {
      this.loadNextNotificationsOnScrolling();
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onNotificationsScrolled(notificationsEle: HTMLUListElement) {
    const shouldScroll = notificationsEle.scrollHeight - notificationsEle.scrollTop - 3 <= notificationsEle.clientHeight;
    this.loadNextNotificationsSubject.next(shouldScroll);
  }

  onNotificationClicked(n: Notifications) {
    const markAsRead$ = this.notificationsService.markAsRead(n.id).subscribe({
      next: val => {
        this.markNotificationAsRead(n);
        this.router.routeReuseStrategy.shouldReuseRoute = () => {
          return false;
        };
        this.router.navigateByUrl(n.url);
      }
    });
    this.subscriptions.add(markAsRead$);
  }

  public loadNextNotifications() {
    if (!this.isLoadingNotifications &&
      this.pagination &&
      this.pagination.current_page < this.pagination.last_page) {
      this.pagination.current_page += 1;
      this.loadNotifications();
    }
  }

  public isNotificationUnread(n: Notifications) {
    return n.status === CollaborationStatusEnum.NEW;
  }
  /**
   * getNotificationTitle
   */
  public getNotificationTitle(n: Notifications) {
    let title = n.body;
    if (n.params) {
      for (const [key, value] of Object.entries(n.params)) {
        title = title.replace(new RegExp(`\{${key}\}`, 'g'), `<span class="highlight">${value}</span>`);
      }
    }
    return title;
  }

  private markNotificationAsRead(n: Notifications) {
    const similarNotifications = this.notifications.filter(o => o.id === n.id);
    for (const col of similarNotifications) {
      col.status = CollaborationStatusEnum.READ;
    }
  }

  public markAllNotificationsAsRead() {
      const markAllAsRead$ = this.notificationsService.markAllAsRead().subscribe();
      this.subscriptions.add(markAllAsRead$);
  }

  private loadNextNotificationsOnScrolling() {
    const loadNextNotifications$ = this.loadNextNotificationsSubject.asObservable()
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe({
        next: val => {
          if (val) {
            this.loadNextNotifications();
          }
        }
      });
    this.subscriptions.add(loadNextNotifications$);
  }

  private loadNotifications() {
    this.isLoadingNotifications = true;
    const payload = {page: this.pagination ? this.pagination.current_page : 1};
    const loadNotifications$ = this.notificationsService.loadNotifications(payload)
    .pipe(
      catchError((err) => {
        console.error(err);
        return of(null);
      }),
      finalize(() => this.isLoadingNotifications = false)
    )
    .subscribe({
      next: (res) => {
        if (res) {
          this.notificationsService.pagination = res.page;
          this.notificationsService.notifications = [...this.notifications, ...res.notifications];
        }
      }
    });
    this.subscriptions.add(loadNotifications$);
  }

  getUser(n: Notifications) {
    const user = {} as TeamUser;
    user.id = n.from_user_id;
    user['name'] = n.params['from_name'];
    user.email = n.params['from_name'];
    return user;
  }

  getNotificationIcon(n: Notifications): string {
    const path = 'assets/images/';
    if (this.isLandscape(n)) {
      return path + 'landscape-icon.svg';
    }
    if (this.isMonitor(n)) {
      return path + 'monitoring-icon.svg';
    }
    if (this.isCollection(n)) {
      return path + 'patent-collections-icon.svg';
    }
    if (this.isSinglePatent(n)) {
      return path + 'single-patent-icon.svg';
    }

    return path + 'search-icon.svg';
  }

  getNotificationIconColor(n: Notifications): string {
    if (this.isLandscape(n)) {
      return '#d3cdd7';
    }
    if (this.isMonitor(n)) {
      return '#e2d4ba';
    }
    if (this.isCollection(n)) {
      return '#c1d1d7';
    }
    if (this.isSinglePatent(n)) {
      return '#cbdce2';
    }
    return '#bedace';
  }
  isLandscape(n: Notifications): boolean {
    return n.url.indexOf('/landscape') > -1;
  }
  isMonitor(n: Notifications): boolean {
    return n.url.indexOf('/monitor') > -1;
  }
  isCollection(n: Notifications): boolean {
    return n.url.indexOf('/collections') > -1;
  }
  isSinglePatent(n: Notifications): boolean {
    return n.url.indexOf('/patent/view') > -1;
  }

  isPatent(n: Notifications): boolean {
    return n.url.indexOf('/patent') > -1;
  }
}
