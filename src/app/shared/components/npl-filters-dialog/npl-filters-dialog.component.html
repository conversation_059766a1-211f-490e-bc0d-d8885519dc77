<div class="modal-header flex-column">
    <div class="modal-close w-100">
        <a href="javascript:void(0)" (click)="onCancel()"><i class="fa-light fa-circle-xmark cursor-pointer"></i></a>
    </div>
    <div class="modal-title d-flex justify-content-between align-items-center px-4 py-2">
        <span>Filters</span>
        <a href="javascript:void(0)" (click)="clearFilters()" *ngIf="hasFilters()">Clean all</a>
    </div>
</div>

<div id="search-filters" class="modal-body p-0 semantic-filters" [formGroup]="form" *ngIf="form">
    <div class="section">
        <div class="d-flex align-items-center">
            <label class="caption-1">Author</label>
            <app-tooltip id='author-tooltip' class="ms-2" tooltipTitle='Author'
                        tooltipText='Only match articles from a specific author'></app-tooltip>
        </div>
        <div class="mb-2 mt-1">
            <input class="form-control mt-1 mb-2 me-2" formControlName="author">
        </div>
        <div class="d-flex align-items-center">
            <label class="caption-1">Venue</label>
            <app-tooltip id='venue-tooltip' class="ms-2" tooltipTitle='Venue'
                        tooltipText='Only match articules containing this venue'></app-tooltip>
        </div>
        <div class="mb-2 mt-1">
            <input class="form-control mt-1 mb-2 me-2" formControlName="venue">
        </div>
        <div class="d-flex align-items-center">
            <label class="caption-1">Field of study</label>
            <app-tooltip id='field-of-study-tooltip' class="ms-2" tooltipTitle='Field of study'
                        tooltipText='Only match articles from this field of study'></app-tooltip>
        </div>
        <div class="mb-2 mt-1">
            <input class="form-control mt-1 mb-2 me-2" formControlName="study">
        </div>
        <div class="d-flex gap-spacing-md">
            <div class="d-flex flex-column w-50">
                <div class="d-flex align-items-center">
                    <label class="caption-1">Min year</label>
                    <app-tooltip id='min-year-tooltip' class="ms-2" tooltipTitle='Min year'
                                tooltipText='Only match articles published from this year onwards'></app-tooltip>
                </div>
                <div class="mb-2 mt-1">
                    <input class="form-control mt-1 me-2" formControlName="minYear" maxlength="4">
                    <div class="invalid-feedback d-block" *ngIf="!form.get('minYear').valid">Please use a number between 1900 and {{currentYear}}</div>
                </div>
            </div>            
            <div class="d-flex flex-column w-50">
                <div class="d-flex align-items-center">
                    <label class="caption-1">Max year</label>
                    <app-tooltip id='max-year-tooltip' class="ms-2" tooltipTitle='Max year'
                                tooltipText='Only match articles published up to this year'></app-tooltip>
                </div>
                <div class="mb-2 mt-1">
                    <input class="form-control mt-1 me-2" formControlName="maxYear" maxlength="4">
                    <div class="invalid-feedback d-block" *ngIf="!form.get('maxYear').valid">Please use a number between 1900 and {{currentYear}}</div>
                </div>
            </div>            
        </div>
        <div class="d-flex gap-spacing-md mt-2">
            <div class="w-50">
                <label class="checkbox">
                    <input class="form-check-input" type="checkbox" formControlName="openAccess">
                    <span class="caption-1">Only open access publications</span>
                </label>
            </div>
            <div class="w-50">
                <label class="checkbox">
                    <input class="form-check-input" type="checkbox" formControlName="influential">
                    <span class="caption-1">Only highly influential publications</span>
                </label>
            </div>
        </div>              

    </div>
</div>

<div class="modal-footer d-flex justify-content-end">
    <button type="button" class="btn btn-ghost"
            [disabled]="isSavingFilters || (!hasFilters() && !isFromModified()) || form.invalid"
            (click)="onSaveFilterToProfile()">
        <i class="fa-light fa-floppy-disk me-2"></i> Save filters
    </button>
    <button type="button" class="btn btn-primary-outline" (click)="onCancel()">Cancel</button>
    <button id="confirmFilters" type="button" class="btn btn-primary" (click)="commitFilter()"
            [disabled]="(!hasFilters() && !isFromModified()) || form.invalid">Select</button>
</div>
