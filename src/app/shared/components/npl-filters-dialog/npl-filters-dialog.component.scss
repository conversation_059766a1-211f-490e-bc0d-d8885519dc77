@import 'scss/layout2021/variables';
@import 'scss/layout2021/typography';

::ng-deep {
    .modal-dialog.modal-dialog-scrollable {
        .modal-content {
            border-radius: 8px 0px 0px 8px !important;
        }
        width: 100vw !important;
        max-width: 100vw !important;
        height: 100vh !important;
        max-height: 100vh !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .modal.right-side-modal {
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
    }
    .modal.right-side-modal.show {
        transform: translateX(0);
    }
    .modal.right-side-modal.hide {
        transform: translateX(100%);
    }

    .semantic-filters {
        .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
            padding-top: 1px;
        }
    }

    .row-direction {
        .ng-value-container {
            flex-direction: row !important;
            align-self: flex-start;
        }
    }
}

.modal-header {
    .modal-title {
        text-align: left;
        letter-spacing: 0.24px;
        line-height: 36px;
        color: $color-text-03;

        a {
            font-family: 'Open Sans Regular';
            font-size: 0.875rem;
            line-height: 26px;            
        }
    }
    .modal-close {
        padding: 20px 12px 8px 12px;
        text-align: right;
        i {
            font-size: 1.5rem;
        }
        a {
            color: $color-text-03;
        }
    }
}

.modal-body {
    overflow-y: auto !important;
}

.modal-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    .btn {
        font-size: 0.875rem;
        line-height: 20px;
        padding: 6px 15px;
        border-radius: 4px;
    }
}
.section {
    padding: 12px 24px;

    .section-title {
        font-size: 1rem;
        color: $color-text-04;
        font-weight: 700;
        letter-spacing: 0.16px;
        line-height: normal;
    }

    .caption-1 {
        margin: 0px;
        color: $color-text-03;
        line-height: 20px;
    }
}

::ng-deep {
    .semantic-filters {
        .dropdown {
            .dropdown-icon {
                font-weight: 900;
                font-family: "Font Awesome 6 Pro";
                color: #C5C5C5;
                transition: all .2s;
                position: absolute;
                width: 30px;
                background-color: transparent;
                right: 2px;
                top: 2px;
                bottom: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 0 4px 4px 0;
                border: 0;

                &:active {
                    background-color: transparent;
                }

                &.date-icon {
                    color: $color-text-03;
                    cursor: pointer;
                }

                &::after {
                    content: '' !important;
                }
            }
        }
    }
}