import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NplFiltersDialogComponent } from './npl-filters-dialog.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideMatomo } from 'ngx-matomo-client';

describe('NplFiltersDialogComponent', () => {
  let component: NplFiltersDialogComponent;
  let fixture: ComponentFixture<NplFiltersDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [NplFiltersDialogComponent],
      providers: [ NgbActiveModal, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(NplFiltersDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
