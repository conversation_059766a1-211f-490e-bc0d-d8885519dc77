import { Component, OnInit } from '@angular/core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, ValidationErrors, Validators } from '@angular/forms';
import { UserService } from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { filterFields, getFieldNameByFilterName } from '../../../npl-search/npl/npl-filters';
@Component({
  selector: 'app-npl-filters-dialog',
  standalone: false,
  templateUrl: './npl-filters-dialog.component.html',
  styleUrl: './npl-filters-dialog.component.scss'
})
export class NplFiltersDialogComponent implements OnInit {
  form: UntypedFormGroup;
  isSavingFilters = false;
  isFiltersFormGroupInvalid = false;
  currentYear = new Date().getFullYear();

  // filterFields = filterFields;

  private _filters = [];
  private initialValues: any;

  get filters() {
    return this._filters;
  }
  set filters(value) {
    this._filters = value;
  }

  constructor(
    public userService: UserService,
    public activeModal: NgbActiveModal,
  ) {}

  ngOnInit() {
    this.buildForm();
    this.populateFormWithFilters();
    setTimeout(() => { this.initialValues = {...this.form.value}; }, 200);
  }

  private buildForm(): void {
    this.form = new UntypedFormGroup({
      author: new UntypedFormControl(''),
      venue: new UntypedFormControl(''),
      study: new UntypedFormControl(''),
      minYear: new UntypedFormControl('',
        [Validators.pattern(/^\d{4}$/), this.yearValidator(1900, this.currentYear)]),
      maxYear: new UntypedFormControl('',
        [Validators.pattern(/^\d{4}$/), this.yearValidator(1900, this.currentYear)]),
      openAccess: new UntypedFormControl(false),
      influential: new UntypedFormControl(false),
    });
  }

  private yearValidator(min: number, max: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      const value = parseInt(control.value, 10);
      if (isNaN(value) || value < min || value > max) {
        return { yearInvalid: true };
      }
      return null;
    };
  }

  private populateFormWithFilters(): void {
    if (this.filters.length > 0) {
      this.filters.forEach(filter => {
        const fieldName = getFieldNameByFilterName(filter.filterName);
        if (fieldName && this.form.get(fieldName)) {
          this.form.get(fieldName).setValue(filter.value);
        }
      });
    }
  }

  onCancel = () => {
    document.querySelector('.modal.show')?.classList.add('hide');
    setTimeout(() => {
      this.activeModal.dismiss();
    }, 50);
  }

  hasFilters(): boolean {
    if (!this.form) {
      return;
    }
    if (Object.keys(this.form['controls']).some(key => {
      if (!!this.form.get(key)?.value) {
        return true;
      }
    })) {
      return true;
    }
    return false;
  }

  onSaveFilterToProfile() {
    this.isSavingFilters = true;
    const npl_search_filters = this.form.value;
    this.userService.updateUISettings({npl_search_filters}).subscribe({
      next: (data) => {
        this.isSavingFilters = false;
        this.commitFilter();
      }, error: error => {
        this.isSavingFilters = false;
        console.error(error);
      }
    });
  }

  commitFilter() {
    this.filters = [];
    Object.keys(this.form['controls']).forEach(key => {
      if (!!this.form.get(key)?.value) {
        this.filters.push({...filterFields[key], value: this.form.get(key).value });
      }
    });
    document.querySelector('.modal.show')?.classList.add('hide');
      setTimeout(() => {
        this.activeModal.close(this.filters);
      }, 50);
  }

  clearFilters() {
    this.form.reset();
  }

  isFromModified(){
    return this.initialValues !== this.form.value;
  }
}
