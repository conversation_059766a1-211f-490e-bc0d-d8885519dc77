<div class="octi-panel-wrapper {{wrapperClass}}"
[ngClass]="{'chat-by-patent': isAskByPatent,'chat-by-list': !isAskByPatent, 'large-size': Resize, 'normal-size': !isAskByPatent && !Resize, 'loading-history': isLoadingHistory}">
  <ng-container *ngIf="showHeader">
    <div class="p-spacing-md d-flex justify-content-between align-items-center" [ngClass]="{'octi-panel-header': hasVerticalScrollbar}">
      <div class="d-flex justify-content-start">
        <div class="content-heading-h4 content-color-primary">
          Octi AI
        </div>
        <span class="d-flex justify-content-center align-items-center tooltip-title-section tooltip-icon">
          <ng-template #octiTooltip>
            <div class="p-spacing-sm text-start">
              Octi AI is an AI chatbot and offers general information about patents or lists of patents. <br />
              <br />
              Its purpose is to simplify and improve patent searches within Octimine by extracting patent information and it is designed not to access personal data. <br />
              Due to technical requirements Oct AI is forced to use Microsoft Azure's globally distributed hosting service. <br />
              This means, that while OctiAI itself will not store or use input data for training purposes, input data may be processed outside of the European Union. <br />
              We strive for accuracy, however, the information provided should not be considered legal advice or a substitute for consulting a human representative for further assistance. <br />
              Users are responsible for their interactions with Octi AI and any reliance on its responses. <br />
              Octimine disclaims liability for errors or omissions in the chatbot's information and for any consequences, including direct or indirect damages, arising from its use.<br />
              <br />
              Given the description above, we consider that Octi AI complies with data protection requirements and regulations. <br />
              Nevertheless, we follow legal developments in the field of AI and data privacy and if required, we will implement the required adaptions. <br />
              In any case, we recommend removing personal data and confidential information of any kind from your inputs.
            </div>
          </ng-template>
          <i class="fa-regular fa-circle-info"
             [ngbTooltip]="octiTooltip"
             tooltipClass="tooltip-large text-start"
             placement="bottom"
             container="body"></i>
        </span>
      </div>
      <div class="d-flex justify-content-end align-items-center">
        <span class="button-main-tertiary-grey button-small content-label-small" (click)="onNewChatClicked()"
          *ngIf="chatHistory?.length>0" ngbTooltip="Opening a new chat will erase your current conversation." tooltipClass="text-start">New chat</span>
        <span class="button-main-tertiary-grey button-square button-small content-label-small" (click)="onToggleSize($event)"
          triggers="hover focus" [ngbTooltip]="size? 'Minimize':'Expand'">
          <i *ngIf="Resize" class="fa-regular fa-arrow-down-left-and-arrow-up-right-to-center"></i>
          <i *ngIf="!Resize" class="fa-regular fa-arrow-up-right-and-arrow-down-left-from-center"></i>
        </span>
        <span ngbTooltip="Close" (click)="onCloseClicked($event)"
          class="button-main-tertiary-grey button-square button-small d-flex justify-content-center align-items-center ">
          <i class="fa-regular fa-xmark cursor-pointer p-spacing-s fa-fw fa-lg color-grey-900"></i>
        </span>
      </div>
    </div>
  </ng-container>
    <div class="chat-container w-100">
      <div class="chat-body" #chatBody>
        <div class="chat-headline">
          <div class=" d-flex gap-spacing-sm  p-b-spacing-xxx-big" [hidden]="chatHistory?.length>1 || isProcessing">
            <ng-template #octiAITooltip>
              <div class="d-flex flex-column text-start">
                <div class="content-heading-h6 content-color-secondary">Octi!</div>
                <div class="content-body-small content-color-secondary">Your AI assistant</div>
              </div>        
            </ng-template>
            <div><i class="octi-icon-v2" [ngbTooltip]="octiAITooltip" tooltipClass="white-tooltip" container="body"></i></div>
            <div *ngIf="!isPatentComparisonLimitExceeded"><b>Hi {{getUsername()}}!</b> {{octiBannerMessage}}</div>
            <div *ngIf="isPatentComparisonLimitExceeded"><p class="m-b-spacing-none"><b>Select 2 patents.</b></p>I can compare only two patents at a time. Please make sure that you have selected only two patents.</div>
          </div>
        </div>
        <div class="chat-history-rows" *ngFor="let r of chatHistory">
          <div class="chat-timestamp content-body-small content-color-quartary" *ngIf="shouldShowTimestamp(r)">
            {{ r.timestamp | dateFormat: 'ShortDateTime' }}
          </div>
          <div class="chat-group content-body-small content-color-quartary" 
            *ngIf="r.internal && (r?.data?.event_type === 'compare_event' || r?.data?.event_type === 'range_event')">
            <ng-container *ngIf="r?.data?.event_type === 'compare_event' else text">
              <span class="d-block">Comparing 
                <span class="content-color-active cursor-pointer hover-underline" [ngbPopover]="familyIdsPopover" triggers="manual" container="body" #pFmilyIds="ngbPopover"
                [autoClose]="false" popoverClass="white-popover popover-publication" appSelectableTooltip [selectableTooltipPopover]="pFmilyIds">2 patents</span>
              </span> 

              <ng-template #familyIdsPopover>
                <div class="publication align-items-center" *ngFor="let p of r.data.event_data.patents">
                  <span class="d-flex align-items-center gap-spacing-xx-s">
                    <span class="cursor-pointer" [ngClass]="getFlagIcon(p.publication_number)" (click)="openViewer(p.publication_number)"></span>
                    <span class="content-heading-h5 cursor-pointer" (click)="openViewer(p.publication_number)">{{p.publication_number}}</span>
                    <span class="button-main-tertiary-grey button-square button-xsmall m-l-spacing-x-s open-family-new-tab"
                      (click)="openViewer(p.publication_number)">
                      <i class="fa-regular fa-arrow-up-right-from-square fa-1x content-color-secondary"></i>
                    </span>
                  </span>
                </div>
              </ng-template>              
            </ng-container>
            <ng-template #text>
              {{ r?.data?.event_data?.text }}
            </ng-template>
          </div>
          <div class="chat-record" *ngIf="!r.internal">
            <div class="chat-icon" *ngIf="r.is_reply">
              <i class="octi-icon"></i>
            </div>
            <ng-template #tooltipReply>
              <div class="octi-ai-popover-actions">
                <div class="button-main-tertiary-grey content-label-small" (click)="onCopyReply(r.text)">{{!isTextCopied ? 'Copy' : 'Copied'}}</div>
              </div>
            </ng-template>
            <div class="chat-box answer-row" [innerHTML]="getResultText(r)" [ngbPopover]="tooltipReply" triggers="manual"
              #p="ngbPopover" container="body" [autoClose]="'outside'" popoverClass="white-popover octi-ai-popover"
              appSelectableTooltip [selectableTooltipPopover]="p" triggerPopover="click" (hidden)="isTextCopied = false" *ngIf="r.is_reply"></div>
  
            <ng-container *ngIf="!r.is_reply" [ngSwitch]="r.prompt_id">
              <div class="chat-box question-row" *ngSwitchCase="PromptType.SUMMARY">
                <strong><i class="fa-regular fa-sparkles m-r-spacing-x-s"></i>Summarize this patent</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.EXPLAIN">
                <strong><i class="fa-regular fa-book-sparkles m-r-spacing-x-s"></i>Explain this part</strong><span
                  [innerHTML]="r.text.replace('Explain this part', '')"></span>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.IMPORTANT">
                <strong><i class="fa-regular fa-messages-question m-r-spacing-x-s"></i>Why is this patent
                  important?</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.USAGE">
                <strong><i class="fa-regular fa-messages-question m-r-spacing-x-s"></i>How can this technology be
                  used?</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.PROBLEMS">
                <strong><i class="fa-regular fa-messages-question m-r-spacing-x-s"></i>What are the main problems addressed by these patents?</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.FEATURES">
                <strong><i class="fa-regular fa-messages-question m-r-spacing-x-s"></i>What are the main technical features discussed in these patents?</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.COMPANIES">
                <strong><i class="fa-regular fa-messages-question m-r-spacing-x-s"></i>How are the top applicants on this list solving their problems?</strong>
              </div>
              <div class="chat-box question-row" *ngSwitchCase="PromptType.COMPARE">
                <strong class="d-flex align-items-start">
                  <i class="fa-regular fa-code-compare m-r-spacing-x-s m-t-spacing-xx-s"></i>
                  <span [innerHTML]="getTextCompare(r)"></span>
                </strong>
              </div>
              <div class="chat-box question-row" *ngSwitchDefault>
                <span *ngIf="r.text" [innerHTML]="r.text || invalidQuestionReply"></span>
              </div>
            </ng-container>
          </div>
        </div>
        <div class="chat-history-rows" *ngIf="!noHistory && isPatentComparisonLimitExceeded">
          <div class="chat-record">
            <div class="chat-icon">
              <i class="octi-icon"></i>
            </div>
            <div class="chat-box answer-row">
              <strong>Do you want to compare other patents in this chat?</strong>
              <div class="chat-box m-t-spacing-xx-s">
                Make sure you have selected 2 patents or if you want to talk about the entire list please start a new chat.
              </div>
            </div>
          </div>
        </div>
        <div class="chat-suggestion" *ngIf="isNewComparison && !isProcessing">
          <div class="cursor-pointer content-color-primary content-body-small p-spacing-sm figma-dropdown-item-hover 
            radius-sm transition-all d-flex align-items-center justify-content-between" (click)="onChatSubmit('Compare patents', PromptType.COMPARE)">
            <span class="d-flex align-items-center">
              <i class="fa-regular fa-code-compare"></i>
              <span class="p-l-spacing-x-s">Do you want to compare these patents?</span>
            </span>
          </div>
        </div>
        <div class="chat-suggestion" *ngIf="noHistory">
          <div class="content-color-tertiary content-heading-h6 p-x-spacing-md p-y-spacing-sm">Suggested</div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="isAskByPatent">
            <span class="cursor-pointer" (click)="onChatSubmit('Summarize this patent', PromptType.SUMMARY)"><i
                class="fa-regular fa-sparkles"></i> <span class="p-l-spacing-x-s">Summarize this patent</span></span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="isAskByPatent">
            <span class="cursor-pointer" (click)="onChatSubmit('Why is this patent important?', PromptType.IMPORTANT)"><i
                class="fa-regular fa-messages-question"></i> <span class="p-l-spacing-x-s">Why is this patent
                important?</span></span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="isAskByPatent">
            <span class="cursor-pointer"
              (click)="onChatSubmit('What is the meaning of the first claim?', PromptType.CLAIM)"><i
                class="fa-regular fa-messages-question"></i> <span class="p-l-spacing-x-s">What is the meaning of the
                first claim?</span></span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="isAskByPatent">
            <span class="cursor-pointer" (click)="onChatSubmit('How can this technology be used?', PromptType.USAGE)"><i
                class="fa-regular fa-messages-question"></i> <span class="p-l-spacing-x-s">How can this technology be
                used?</span></span>
          </div>
          <div class="cursor-pointer content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover 
          radius-sm transition-all d-flex align-items-center justify-content-between" (click)="onChatSubmit('Compare patents', PromptType.COMPARE)"
          [class.comparison-disabled]="!isComparisonEnabled"
          [ngbTooltip]="!isComparisonEnabled ? 'Select two patents from your result list to compare them.' : ''"  tooltipClass="white-tooltip  tooltip-text-start"
          *ngIf="!isAskByPatent">
            <span class="d-flex align-items-center"><i class="fa-regular fa-code-compare"></i><span class="p-l-spacing-x-s">Compare patents</span></span>
            <span [ngbTooltip]="isComparisonEnabled ? 'Select two patents from your result list to compare them.' : ''"  tooltipClass="white-tooltip tooltip-text-start">
              <i class="fa-regular fa-circle-info content-color-quartary"></i>
            </span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="!isAskByPatent">
            <span class="cursor-pointer d-flex" (click)="onChatSubmit('What are the main problems addressed by these patents?', PromptType.PROBLEMS)"><i
                class="fa-regular fa-messages-question m-t-spacing-sm"></i> <span class="p-l-spacing-x-s">What are the main problems addressed by these patents?</span></span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="!isAskByPatent">
            <span class="cursor-pointer d-flex" (click)="onChatSubmit('What are the main technical features discussed in these patents?', PromptType.FEATURES)"><i
                class="fa-regular fa-messages-question m-t-spacing-sm"></i> <span class="p-l-spacing-x-s">What are the main technical features discussed in these patents?</span></span>
          </div>
          <div class="content-color-primary content-body-small p-x-spacing-sm p-y-spacing-x-s figma-dropdown-item-hover radius-sm transition-all" *ngIf="false && !isAskByPatent">
            <span class="cursor-pointer d-flex" (click)="onChatSubmit('How are the top applicants on this list solving their problems?', PromptType.COMPANIES)"><i
                class="fa-regular fa-messages-question m-t-spacing-sm"></i> <span class="p-l-spacing-x-s">How are the top applicants on this list solving their problems?</span></span>
          </div>
        </div>
        <div class="chat-record" *ngIf="isProcessing">
          <div class="chat-icon">
            <i class="octi-icon"></i>
          </div>
          <div class="chat-box processing-row">Thinking...</div>
        </div>
      </div>
      <div class="chat-footer">
        <div class="input-field content-body-medium">
          <div [contentEditable]="!isProcessing && !isPatentComparisonLimitExceeded" class="input-field-text input-field-text-icon-right octi-input"
            [class.is-processing]="isProcessing || isPatentComparisonLimitExceeded" #octiEditor [attr.data-placeholder]="getEditorPlaceholderText()"
            (paste)="onPaste($event)"
            (keydown.enter)="!isProcessing && !isEditorEmpty && onChatSubmit(); $event.preventDefault()"></div>
          <i class="fa-regular fa-paper-plane chat-submit-btn button-main-primary button-square button-xsmall"
            [class.disabled]="isEditorEmpty || isProcessing" (click)="onChatSubmit()"></i>
        </div>
      </div>
    </div>
</div>