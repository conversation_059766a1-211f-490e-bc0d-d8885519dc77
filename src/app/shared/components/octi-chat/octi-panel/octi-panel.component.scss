@import 'scss/figma2023/variables';
@import 'scss/figma2023/mixins';

.figma-side-section-content {
  position: relative;
  display: flex;
  height: 100%;
  width: 100%;
}
.chat {
  &-container{
    display: flex;
    flex-direction: column;
    align-self: flex-end;
    max-height: 100%;
    overflow: hidden;
  }
  &-body {
    display: flex;
    align-self: stretch;
    flex-direction: column;
    padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm $spacing-system-spacing-x-s $spacing-system-spacing-sm;
    max-height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }

  &-footer {
    display: flex;
    margin: $spacing-system-spacing-md $spacing-system-spacing-big $spacing-system-spacing-big;
  }
  &-submit-btn{
    bottom: .4rem;
    right: .5rem;
    position: absolute;
  }
  &-headline{
    display: flex;
    gap: $spacing-system-spacing-sm;

    .octi-icon-v2{
      width: 40px;
      height: 40px;
      border: 1px solid $colours-content-content-active;
      border-radius: 200px;
      padding: 4px;
    }
  }
  &-timestamp {
    display: flex;
    justify-content: center;
    padding: $spacing-system-spacing-xx-s 0;
  }
  &-group {
    display: flex;
    justify-content: center;
    margin-top: $spacing-system-spacing-md;
    padding-bottom: $spacing-system-spacing-xx-s;
  }
  &-record {
    display: flex;
    margin-top: $spacing-system-spacing-md;
    @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
    .question-row, .answer-row{
      padding: $spacing-system-spacing-md;
      position: relative;
      margin-left: $spacing-system-spacing-xxx-big;
    }
    .question-row{
      background-color: $colours-background-bg-brand;
      color: $colours-content-content-primary;
      border-radius: $radius-big $radius-big 0 $radius-big;
      &::before{
        content: '';
        position: absolute;
        width: 12px;
        height: 12px;
        bottom: 0;
        border-bottom-left-radius: 50%;
        right: -12px;
        box-shadow: -6px 0 0 0 $colours-background-bg-brand;
      }
    }
    .processing-row{
      padding: 0 $spacing-system-spacing-sm;
      margin-left: $spacing-system-spacing-xx-big;
      display: flex;
      align-items: center;
    }
    .answer-row{
      color: $colours-content-content-secondary;  
      border-radius: $radius-big;
      padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
      &:hover{
        background-color: $colours-background-bg-secondary;
      }
    }
    .chat-icon{
      position: relative;
      min-height: 30px;
      .octi-icon{
        position: absolute;
        top: 2px;
        left: 0;
        border: 1px solid $colours-content-content-active;
        border-radius: 200px;
        padding: 4px;
        width: 26px;
        height: 26px;
        margin-right: 2px;
      }
    }
    .chat-box{
      flex: 1 1 auto;
    }
  }
  &-suggestion{
    padding-bottom: $spacing-system-spacing-sm;

    .comparison-disabled{
      cursor: default !important;
      color: $colours-content-content-disabled !important;
    }
    &>:not(.comparison-disabled){
      .fa-circle-info:hover{
        color: $colours-content-content-secondary !important;
      }
    }
  }
}
.input-field-text{
  &.is-processing{
    background-color: $colours-background-bg-secondary;
    min-height: 44px;
  }
  max-height: 150px;
  overflow-y: auto;
}
.octi-ai-popover-actions .button-main-tertiary-grey{
  padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-sm;
}

.octi-panel-header{
  box-shadow: 0px 1px 3px 0px rgba(40, 40, 40, 0.15);
}

.octi-panel-wrapper{
  &.normal-size{
    max-width: 423px;
  }
  &.large-size{
    max-width: 665px;
  }
  &.chat-by-list {
    .chat-body{
      height: 542px;
      max-height: 61vh;
      padding: 0 $spacing-system-spacing-big;
    }
    .chat-footer{
      margin: $spacing-system-spacing-md $spacing-system-spacing-big $spacing-system-spacing-big;
    }
    .chat-headline{
      margin-top: auto;
    }
  }
}
.tooltip-icon{
  height: 1rem;
  width: 1rem;
  font-size: .75rem;
}
[contenteditable=true]{
  @include add-properties(map-get(map-get($typography, 'body'), 'small'), true);
  &:empty:not(:focus):before{
    content:attr(data-placeholder);
    color:$colours-content-content-disabled;
  }
}

::ng-deep {
  .popover-publication {
    .popover-body {
      .publication {
        display: flex;
        justify-content: space-between;
        padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
        margin-bottom: $spacing-system-spacing-xx-s;
        border-radius: $radius-sm;
  
        .open-family-new-tab {
          visibility: hidden;
        }
        &:hover {
          background-color: $colours-background-bg-secondary;
          .open-family-new-tab {
            visibility: visible;
          }
        }
      }
    }
  }
  .tooltip-text-start{
    .tooltip-inner{
      text-align: start;
    }
  }

  .replay-title{
    font-weight: bold;
    line-height: 24px;
  }

  .hover-underline {
    &:hover {
      text-decoration: underline !important;
    }
  }
}