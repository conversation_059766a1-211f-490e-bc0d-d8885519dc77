import { ComponentFixture, TestBed } from '@angular/core/testing';

import { OctiPanelComponent } from './octi-panel.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { BaseStoreService } from '@core/store';
import { BehaviorSubject } from 'rxjs';

describe('OctiPanelComponent', () => {
  let component: OctiPanelComponent;
  let fixture: ComponentFixture<OctiPanelComponent>;
  let mockStoreService: jasmine.SpyObj<BaseStoreService>;

  beforeEach(async () => {
    mockStoreService = jasmine.createSpyObj('BaseStoreService', [], {
      selectedPublications$: new BehaviorSubject<number[]>([]),
      selectedPublications: []
    });

    await TestBed.configureTestingModule({
      declarations: [OctiPanelComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [provideMatomo({ siteId: '', trackerUrl: '', disabled: true })]
    })
      .compileComponents();

    fixture = TestBed.createComponent(OctiPanelComponent);
    component = fixture.componentInstance;
    
    component.storeService = mockStoreService;
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
