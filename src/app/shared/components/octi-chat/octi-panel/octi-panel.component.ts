import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild, AfterViewChecked } from '@angular/core';
import { OctiAiService, PatentData, PatentService, PatentViewService, PromptType, SingleChat, ToastService, ToastTypeEnum, UserService } from '@core';
import { BaseStoreService } from '@core/store';
import { BehaviorSubject, catchError, filter, finalize, map, Observable, of, scan, Subscription, switchMap, tap } from 'rxjs';
import { isEqual, findLast } from 'lodash';
import { Router } from '@angular/router';
import { regExpPublications } from '@core/services/semantic-search/utils/freetext/freetext';

@Component({
  selector: 'app-octi-panel',
  templateUrl: './octi-panel.component.html',
  styleUrl: './octi-panel.component.scss'
})
export class OctiPanelComponent implements OnInit, OnDestroy, OnChanges, AfterViewChecked {
  @Input() storeService: BaseStoreService;
  @Input() chatID: string;
  @Input() isAskByPatent: boolean = true;
  @Input() range?: string;
  @Input() showHeader?: boolean;
  @Input() wrapperClass?: string = '';

  // for patent viewer
  @Input() familyId?: number;
  @Input() publicationNumber?: string;

  //for patent list
  @Input() documents?: any[];
  @Input() Resize?: boolean;

  @Output() closePanel: EventEmitter<boolean> = new EventEmitter<boolean>();
  
  private askOctiSubject = new BehaviorSubject<SingleChat>(null);
  private readonly askOcti$ = this.askOctiSubject.asObservable();

  private familyIds: number[];
  private publicationNumbers: string[];
  private patents: PatentData[];

  isProcessing: boolean;
  isLoading: boolean;
  PromptType = PromptType;
  isTextCopied = false;
  isComparison = false;
  comparisonPatents: string[] = [];

  private lastInteractionTime: string = '';
  private subscriptions = new Subscription();
  private popperSubscription: Subscription;

  @ViewChild('octiEditor') octiEditor: ElementRef;
  @ViewChild('chatBody') chatBody: ElementRef;

  invalidQuestionReply = `Unfortunately, I cannot find an answer to your question based on the content provided in this patent. <br>Try to rephrase your question so that I can assist you better. `;

  hasVerticalScrollbar: boolean = false;

  constructor(
    private userService: UserService,
    private toastService: ToastService,
    private octiAiService: OctiAiService,
    private patentViewerService: PatentViewService,
    private patentService: PatentService,
    private router: Router
  ) {}

  get octiInputValue(): string {
    return this.octiEditor?.nativeElement?.innerHTML?.trim();
  }
  get isEditorEmpty(): boolean{
    if(this.octiInputValue?.length > 0){
      return false
    }
    return true
  }

  get chatHistory(){
    return this.octiAiService.octiAIHistory[this.chatID];
  }

  get noHistory(): boolean{
    return this.chatHistory && this.chatHistory.length === 0;
  }

  get isLoadingHistory(): boolean{
    return this.isLoading;
  }

  get octiBannerMessage(): string{
    if(this.isAskByPatent){
      return 'I\'m Octi, and I\'m here to help you understand patents better.';
    }
    return this.noHistory? 'I am happy to assist you with any questions you have about these documents.': 'Let\'s dive together on this result list.'
  }

  get isComparisonEnabled(): boolean{
    return this.storeService.selectedPublications.length == 2;
  }
  
  get isPatentComparisonLimitExceeded(): boolean{
    return this.isComparison && this.storeService.selectedPublications.length > 2;
  }

  get isPublicationSearch(): boolean{
    return this.storeService.isPublications;
  }

  get isNewComparison(): boolean{
    return this.isComparisonEnabled && !this.noHistory && !isEqual(this.comparisonPatents, this.storeService.selectedPublications.map(p => p.replace(/[-]/g, '')) );
  }

  shouldShowTimestamp(record: any): boolean {
    const index = this.chatHistory.indexOf(record);
    if (index === -1) return false;

    if (record.is_reply || !record.timestamp) return false;

    if (index === 0) {
      return true;
    }

    let previousIndex = index - 1;
    while (previousIndex >= 0) {
      if (this.chatHistory[previousIndex].timestamp) {
        break;
      }
      previousIndex--;
    }

    if (previousIndex < 0) {
      return true;
    }

    const currentTimestamp = new Date(record.timestamp);
    const previousTimestamp = new Date(this.chatHistory[previousIndex].timestamp);
    
    const timeDiff = currentTimestamp.getTime() - previousTimestamp.getTime();
    const oneMinute = 15 * 60 * 1000;

    return timeDiff > oneMinute;
  }

  ngOnInit() {
    this.processDocuments();
    this.subscribeOcti();
    const subscription = this.octiAiService.octiAINewChat$.subscribe({
      next: (newChat) => { if (newChat && this.chatHistory.length>0) { this.clearOctiHistory(); } }
    });
    this.subscriptions.add(subscription);
    this.subscriptions.add(this.octiAiService.octiAIHistoryRefresh$.subscribe({
      next: () => { this.initializeOcti(); }
    }));

    const selectedPatentIdsSubscription = this.storeService.selectedPublications$.subscribe({
      next: () => {
        if (!this.noHistory && (this.isPatentComparisonLimitExceeded || this.isNewComparison)){
          this.scrollDown();
        }
      }
    });
    this.subscriptions.add(selectedPatentIdsSubscription);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.chatID && !changes.chatID.firstChange) {
      this.initializeOcti();
    }
  }

  initializeOcti(){
    const now = new Date();
    this.lastInteractionTime = now.toISOString().split('.')[0];
    this.getOctiHistory();
  }

  ngAfterViewChecked() {
    this.setupPatentClickHandlers();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private processDocuments() {
    if (!this.isAskByPatent && this.documents?.length > 0) {
      this.patents = [];
      this.documents.slice(0, 25).forEach((doc, index) => {
        if (this.isPublicationSearch) {
          this.publicationNumbers ? this.publicationNumbers.push(doc['general'].publication_number) : this.publicationNumbers = [doc['general'].publication_number];
        } else {
          this.familyIds ? this.familyIds.push(doc['general'].docdb_family_id) : this.familyIds = [doc['general'].docdb_family_id];
        }
        this.patents.push({
          doc_family_id: doc['general'].docdb_family_id, 
          publication_number: doc['general'].raw_publication_number
        });
      });
    }
  }

  getUsername() {
    if (!this.userService.getUser()) {
      return '';
    } else if (this.userService.getUser().profile?.first_name || this.userService.getUser().profile?.last_name) {
      return this.userService.getUser().profile.first_name.trim() || this.userService.getUser().profile.last_name.trim();
    } else {
      return this.userService.getUser().profile?.email;
    }
  }

  onChatSubmit(defaultText: string = null, prompt_id: PromptType = PromptType.DEFAULT){
    if(prompt_id === PromptType.COMPARE && (!this.isComparisonEnabled || this.isPatentComparisonLimitExceeded)){
      return;
    }
    this.isComparison = this.isComparison || prompt_id === PromptType.COMPARE;
    const text = defaultText || this.octiInputValue;
    this.isProcessing = true;

    this.addHistory(text, prompt_id).subscribe(singleChat => {
      this.askOctiSubject.next(singleChat);
    });
  }

  private addHistory(text: string, prompt_id: PromptType): Observable<SingleChat> {
    const now = new Date();
    this.lastInteractionTime = now.toISOString().split('.')[0];

    const o = {
      text: text,
      prompt_id: prompt_id,
      timestamp: this.lastInteractionTime
    }

    if(!this.isAskByPatent && (!this.isComparison && this.range && this.getLastRange() !== this.range) || 
      (this.isComparison && !isEqual(this.comparisonPatents, this.storeService.selectedPublications))){
      let event = 'range_event';
      if (this.isComparison){
        this.comparisonPatents = this.storeService.selectedPublications.map(p => p.replace(/[-]/g, ''));
        text = `Compare patents ${this.comparisonPatents[0]} with ${this.comparisonPatents[1]}`;
        event = 'compare_event';
        o.text = text;
      }
      const rangeData = !this.isComparison ? {'range': this.range, text: `Conversation about patent list from ${this.range}`, patents: this.patents} : 
        {text: `Comparing 2 patents`, patents: this.buildComparisonPatents()};
      
        return this.octiAiService.saveChatEvent(this.chatID, rangeData, event, this.lastInteractionTime).pipe(
          tap(() => {
            this.chatHistory.push({internal: true, data: {event_data: rangeData, event_type: event}});
          }),
          map(() => {
            this.chatHistory.push(o);
            this.scrollDown();
            this.octiEditor.nativeElement.innerHTML = '';
            return o;
          })
        );
    } else {
      this.chatHistory.push(o);
      this.scrollDown();
      this.octiEditor.nativeElement.innerHTML = '';
      return of(o);
    }
  }

  private buildComparisonPatents(): PatentData[] {
    this.patents = [];
    this.comparisonPatents.forEach((patent, index) => {
      this.patents.push({
        doc_family_id: this.storeService.selectedPatentIds[index], 
        publication_number: patent.replace(/[-]/g, '')
      });
    });
    return this.patents;
  }

  private getFamilyId(publicationNumber: string): number {
    const familyId = this.findPatent(publicationNumber)?.doc_family_id || 0;
    return familyId;
  }

  private getPublicationNumber(publicationNumber: string): string {
    return this.findPatent(publicationNumber)?.publication_number || publicationNumber;
  }

  private findPatent(publicationNumber: string): PatentData {

    if (!publicationNumber?.trim()) {
      return null;
    }
  
    const normalizedNumber = publicationNumber.replace(/[-]/g, '').trim();

    const internalHistory = this.chatHistory.find(h => h.internal && 
      h.data.event_data.patents.find(p => p.publication_number.includes(normalizedNumber)));
    
    if (!internalHistory) {
      return null;
    }

    const patents = internalHistory.data.event_data.patents;
    
    if (!patents) {
      return;
    }
    return patents.find(p => p.publication_number.includes(normalizedNumber));
  }

  onPaste(event: ClipboardEvent){
    event.preventDefault();
    event.stopPropagation();

    const clipboardText = event.clipboardData.getData('text/plain');
    const selection = window.getSelection();

    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    range.deleteContents();

    const textNode = document.createTextNode(clipboardText);
    range.insertNode(textNode);

    const newRange = document.createRange();
    newRange.setStartAfter(textNode);
    newRange.setEndAfter(textNode);

    selection.removeAllRanges();
    selection.addRange(newRange);
    setTimeout(() => { this.octiEditor.nativeElement.scrollTo({ left: 0, top: this.chatBody.nativeElement.scrollHeight, behavior: "smooth" }); this.focusEditor() }, 50);
  }

  scrollDown(){
    setTimeout(() => { 
      this.chatBody.nativeElement.scrollTo({ left: 0, top: this.chatBody.nativeElement.scrollHeight, behavior: "smooth" }); 
      this.checkScrollbarVisibility();
    }, 50);
  }

  /**
   * subscription for ask octi stream
   */
  subscribeOcti() {
    const explain$ = this.askOcti$
      .pipe(
        filter(text => !!text),
        switchMap((questionChat) => {
          let askOcti: Observable<any>;
          if(this.isAskByPatent){
            askOcti = this.octiAiService.askOctiAboutPatentStream(this.chatID, this.familyId, questionChat.text, this.publicationNumber || null, this.lastInteractionTime, questionChat.prompt_id);
          } else {
            askOcti = this.octiAiService.askOctiAboutListStream(this.chatID, this.isComparison && !this.isPublicationSearch ? this.storeService.selectedPatentIds : this.familyIds, 
              questionChat.text, this.isPublicationSearch ? 
                !this.isComparison ? this.publicationNumbers : this.storeService.selectedPublications : null, this.lastInteractionTime, questionChat.prompt_id);
          }
          return askOcti.pipe(
              catchError((err) => {
                this.handleError(err, 'Failed to ask octi', 'There was an error while asking octi.');
                return of(null);
              }),
              filter(chunk => !!chunk),
              scan((acc, chunk) => {
                if (!acc.started) {
                  this.chatHistory.push({is_reply: true, text: chunk || ''});
                  this.scrollDown();
                  this.isProcessing = false;
                  return {started: true, lastIndex: this.chatHistory.length - 1, text: chunk || ''};
                } else {
                  const lastIndex = acc.lastIndex;
                  this.chatHistory[lastIndex].text = chunk || '';
                  this.scrollDown();
                  return {...acc, text: chunk || '' };
                }
              }, { started: false, lastIndex: -1, text: '' }),
              finalize(() => {
                this.focusEditor();
              }),
            )
        }),
        finalize(() => this.isProcessing = false)
      )
      .subscribe();
    this.subscriptions.add(explain$);
  }

  private getOctiHistory(){
    this.isLoading = true;
    const octiHistory$ = this.octiAiService.getOctiHistory(this.chatID).subscribe({
      next: (res)=>{
        this.octiAiService.octiAIHistory[this.chatID] = res;
        this.scrollDown();
        if(this.isAskByPatent){
          this.subscribeToOctiAIPopper();
        } else {
          this.isComparison = res.some(r => r.prompt_id === PromptType.COMPARE);
          if (this.isComparison) {
            const internalRecord = findLast(res, {data: {event_type: 'compare_event'}});
            if(internalRecord){
              this.comparisonPatents = internalRecord.data.event_data.patents.map(p => p.publication_number); 
            }
          }
        }
        this.focusEditor();
        this.isLoading = false;
      },
      error: (err)=>{
        this.isLoading = false;
        this.toastService.show({
          type: ToastTypeEnum.ERROR,
          header: 'Failed to fetch octi history',
          body: `There was an error while fetching the octi history.<br/>${err.error.message}`,
          delay: 10000
        });
      }
    });
    this.subscriptions.add(octiHistory$);
  }

  clearOctiHistory() {
    const octiHistory$ = this.octiAiService.deleteOctiHistory(this.chatID).subscribe({
      next: () => {
        this.octiAiService.removeHistoryByKey(this.chatID);
        this.octiAiService.setNewChat(false);
        this.isComparison = false;
        this.comparisonPatents = [];
        this.checkScrollbarVisibility();
      },
      error: (err) => {
        this.toastService.show({
          type: ToastTypeEnum.ERROR,
          header: 'Failed to delete octi history',
          body: `There was an error while deleting the octi history.<br/>${err.error.message}`,
          delay: 10000
        });
      }
    });
    this.subscriptions.add(octiHistory$);
  }

  onCopyReply(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      this.isTextCopied = true;
    });
  }

  onExplainSection(text: string) {
    this.onChatSubmit(`Explain this part<br/> ${text.substring(0, 1000)}`, PromptType.EXPLAIN);
  }

  private handleChatReply(reply: string) {
    this.chatHistory.push({ 
      is_reply: true, 
      text: reply || null
    });
    this.scrollDown();
    this.focusEditor();
    this.checkScrollbarVisibility();
  }

  private handleError(err: any, header: string, message: string) {
    this.toastService.show({
      type: ToastTypeEnum.ERROR,
      header: header,
      body: `${message}<br/>${err.error.message}`,
      delay: 10000
    });
  }
  
  private setupPatentClickHandlers() {
    if (!this.chatBody) return;
    
    const patentSpans = this.chatBody.nativeElement.querySelectorAll('span.patent-number-bold.content-color-active.fw-bold.cursor-pointer');
    
    patentSpans.forEach(span => {
      if (span.dataset.processed === 'true') return;
      span.dataset.processed = 'true';
      span.addEventListener('click', () => {
        this.openViewer(span.textContent);
      });
    });
  }

  getResultText(r: any) {
    if (!r.text) {
      return this.invalidQuestionReply;
    }
    
    let text = r.text.replace(/\n/g, '<br/>');
    if (!this.isAskByPatent ) {
      text = text.replace(regExpPublications(), match => {
        return `<span class="patent-number-bold content-color-active fw-bold cursor-pointer hover-underline">${this.getPublicationNumber(match)}</span>`;
      });
      // format the title of the replay
      text = text.replace(/(\d+\.\s+)\*\*(.*?)\*\*(:)/g, '$1<span class="replay-title">$2$3</span>');
    }

    // format the bold text
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    return text;
  }

  private subscribeToOctiAIPopper() {
    if(this.popperSubscription){ return }
    this.popperSubscription = this.patentViewerService.octiAIPopper$.subscribe({
      next: (content) => {
        if (content) {
          switch (content.promptType) {
            case PromptType.EXPLAIN:
              this.onExplainSection(content.text);
              break;
            case PromptType.SUMMARY:
              this.onChatSubmit('Summarize this patent', PromptType.SUMMARY);
              break;
            case PromptType.DEFAULT:
              this.onChatSubmit(content.text);
              break;
          }
          this.patentViewerService.setOctiAIPopper(null);
        }
      }
    });
    this.subscriptions.add(this.popperSubscription);
  }

  onNewChatClicked(){
    this.clearOctiHistory();
  }

  focusEditor(){
    this.octiEditor?.nativeElement.focus();
  }

  onCloseClicked(){
    this.closePanel.emit(true);
  }

  onToggleSize(event: any){
    this.Resize = !this.Resize;
    event.currentTarget.blur();
    setTimeout(() => this.checkScrollbarVisibility(), 100);
  }

  getEditorPlaceholderText(): string{
    if (this.isAskByPatent){
      return 'Ask anything about this patent...';
    }
    return this.isComparison ? 'Ask anything about these 2 patents...' : 
      `Ask anything about the ${this.familyIds?.length || this.publicationNumbers?.length} visible results...`;
  }

  getLastRange(): string{
    const rangeEvents = this.chatHistory?.filter(r => r.internal && r?.data?.event_type === 'range_event');
    if(rangeEvents.length>0){
      return rangeEvents[rangeEvents.length-1].data.event_data.range;
    }
    return null
  }

  getTextCompare(r: any): string{
    let text = r.text;
    text = text.replace(regExpPublications(), match => `<span class="content-color-active">${match}</span>`);
    return text;
  }

  getFlagIcon(publicationNumber: string) {
    return this.patentService.getFlagCssByPublication(publicationNumber).toLowerCase();
  }

  getViewPath(familyId): Array<string> {
    const baseViewPath = this.userService.isExternalUser() ? `patent/view/shared` : `patent/view`;
    const viewPath = [baseViewPath, familyId];

    return viewPath;
  }

  openViewer(patentNumber: string): void {
    const familyId = this.getFamilyId(patentNumber);
    let url = this.router.serializeUrl(
      this.router.createUrlTree(this.getViewPath(familyId))
    );
    
    if (this.isPublicationSearch) {
      url = url + `/publication/${patentNumber}`;
    }
    window.open(url, '_blank');
  }

  private checkScrollbarVisibility() {
    if (this.chatBody?.nativeElement) {
      const element = this.chatBody.nativeElement;
      setTimeout(() => this.hasVerticalScrollbar = element.scrollHeight > element.clientHeight);
    }
  }
} 
