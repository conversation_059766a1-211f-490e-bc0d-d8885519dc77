<div class="d-flex align-items-center justify-content-between page-size">
  Show
  <div class="position-relative" ngbDropdown>
    <div ngbDropdownToggle class="caret-off">
      <input class="form-control" readonly [placeholder]="pageSize"/>
      <div class="dropdown-icon"></div>
    </div>

    <div ngbDropdownMenu>
      <a href="javascript:void(0)" href="javascript:void(0)" class="dropdown-item"
         *ngFor="let option of pageOptions" (click)="onChangeSize(option)">
        {{ option }}
      </a>
    </div>
  </div>
  per page
</div>

