import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-page-size',
  templateUrl: './page-size.component.html',
  styleUrls: ['./page-size.component.scss']
})
export class PageSizeComponent {

  @Input() pageSize = 25;
  @Input() pageOptions = [25, 50, 100];
  @Output() changeSize: EventEmitter<number> = new EventEmitter();

  constructor() {
  }

  onChangeSize(val: number): void {
    this.pageSize = val;
    this.changeSize.emit(this.pageSize);
  }
}
