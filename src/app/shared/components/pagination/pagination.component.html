<div class="pagination-wrapper">
  <nav *ngIf="pagination && pagination.total_hits > 0">
    <ul class="pagination">
      <li *ngIf="showPrev">
        <a href="#" (click)="navigatePrev($event)">
          <span aria-hidden="true">
            <i class="fa fa-angle-left"></i>
          </span>
        </a>
      </li>
      <li [ngClass]="{ 'current': number == pagination.current_page }" *ngFor="let number of pageNumbers" >
        <span *ngIf="number == pagination.current_page">{{number}}</span>
        <a *ngIf="number != pagination.current_page" (click)="navigate($event, number)"><span>{{number}}</span></a>
      </li>
      <li *ngIf="showNext">
        <a href="#" aria-label="Next" (click)="navigateNext($event)">
          <span aria-hidden="true">
            <i class="fa fa-angle-right"></i>
          </span>
        </a>
      </li>
    </ul>
  </nav>
</div>
