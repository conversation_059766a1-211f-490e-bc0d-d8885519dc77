import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PaginationMetadata } from '@core/services';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent implements OnInit {
  /**
   * pagination object containing the page data
   */
  @Input() pagination: PaginationMetadata;
  /**
   * limit pagination to specific number
   */
  @Input() maxPage?: number;
  @Output() navigatePage: EventEmitter<Object> = new EventEmitter();

  constructor() {
  }

  ngOnInit() {
    if (this.maxPage && (this.pagination.total_hits > (Number(this.maxPage) * Number(this.pagination.page_size)))) {
      this.pagination.total_hits = Number(this.maxPage) * Number(this.pagination.page_size);
    }
  }

  get showNext() {
    return this.pagination.current_page !== (this.maxPage ? this.maxPage : this.pagination.last_page)
      ? true
      : false;
  }

  get showPrev() {
    return this.pagination.current_page > 1 ? true : false;
  }

  /**
   * get the array for pages to be displayed accoringto the input data
   */
  get pageNumbers() {
    return this.generatePagesArray(this.pagination.current_page, this.pagination.total_hits, this.pagination.page_size, 10);
  }

  /**
   * generate the array for pages to be displayed
   */
  generatePagesArray(
    currentPage,
    collectionLength,
    rowsPerPage,
    paginationRange
  ) {
    const pages = [];
    const totalPages = Math.ceil(collectionLength / rowsPerPage);
    const halfWay = Math.ceil(paginationRange / 2);
    let position;

    if (currentPage <= halfWay) {
      position = 'start';
    } else if (totalPages - halfWay < currentPage) {
      position = 'end';
    } else {
      position = 'middle';
    }

    const ellipsesNeeded = paginationRange < totalPages;
    let i = 1;
    while (i <= totalPages && i <= paginationRange) {
      const pageNumber = this.calculatePageNumber(
        i,
        currentPage,
        paginationRange,
        totalPages
      );
      const openingEllipsesNeeded =
        i === 2 && (position === 'middle' || position === 'end');
      const closingEllipsesNeeded =
        i === paginationRange - 1 &&
        (position === 'middle' || position === 'start');
      if (
        ellipsesNeeded &&
        (openingEllipsesNeeded || closingEllipsesNeeded)
      ) {
        pages.push('...');
      } else {
        pages.push(pageNumber);
      }
      i++;
    }
    return pages;
  }

  calculatePageNumber(i, currentPage, paginationRange, totalPages) {
    const halfWay = Math.ceil(paginationRange / 2);
    if (i === paginationRange) {
      return totalPages;
    } else if (i === 1) {
      return i;
    } else if (paginationRange < totalPages) {
      if (totalPages - halfWay < currentPage) {
        return totalPages - paginationRange + i;
      } else if (halfWay < currentPage) {
        return currentPage - halfWay + i;
      } else {
        return i;
      }
    } else {
      return i;
    }
  }

  navigateNext(event) {
    event.preventDefault();
    if (
      this.pagination.current_page > 0 &&
      this.pagination.current_page < this.pagination.total_hits
    ) {
      this.navigatePage.emit(Number(this.pagination.current_page) + 1);
    }
  }

  navigatePrev(event) {
    event.preventDefault();
    if (
      this.pagination.current_page > 1 &&
      this.pagination.current_page <= this.pagination.total_hits
    ) {
      this.navigatePage.emit(Number(this.pagination.current_page) - 1);
    }
  }

  navigate(event, pagenum) {
    event.preventDefault();
    if (!isNaN(parseInt(pagenum, 10))) {
      if (
        pagenum > 0 &&
        pagenum < this.pagination.total_hits &&
        this.pagination.current_page !== pagenum
      ) {
        this.navigatePage.emit(pagenum);
      }
    }
  }
}
