<div *ngIf="document && document.labels && document.labels.length" class="w-100">
  <div class="dd-title p-1 d-flex align-items-center"><i class="fas fa-bookmark pe-2"></i> Labels</div>
  <table class="dd-table w-100">
    <thead>
    <tr>
      <td>Color</td>
      <td>Name and shortcut</td>
      <td>Field</td>
      <td>Selected text</td>
      <td>Time</td>
      <td>Owner</td>
      <td></td>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let lb of document.labels">
      <td width="10px" align="center">
        <div class="dd-box-color" [style.background-color]="'#'+lb.label.color"></div>
      </td>
      <td width="150px">{{lb.label.name}} {{lb.label.shortcut ? '(' + lb.label.shortcut + ')' : '' }} </td>
      <td width="60px">{{lb.field | titlecase}}</td>
      <td><div class="dd-long-text">{{lb.text | truncate: 100: true}}</div></td>
      <td width="110px">{{lb.created_at | date}}</td>
      <td  width="120px">
        <p *ngIf="lb.user">{{lb.user | userTitle}}</p>
      </td>
      <td width="10px">
        <a ngbTooltip="Delete this label" (click)="onDeleteLabel($event, lb)"  *ngIf="canDeleteAnnotation(lb.user_id)">
          <i class="fa fa-fw fa-trash-alt fa-green"></i>
        </a>
      </td>
    </tr>
    </tbody>
  </table>
</div>

<div *ngIf="document && document.comments && document.comments.length" class="w-100">
  <div class="dd-title p-1 d-flex align-items-center"><i class="fas fa-comment-alt pe-2"></i> Comments</div>
  <table class="table w-100">
    <thead>
    <tr>
      <td>Color</td>
      <td>Field</td>
      <td>Selected text</td>
      <td>Comment</td>
      <td>Time</td>
      <td>Owner</td>
      <td></td>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let cm of document.comments">
      <td width="10px" align="center">
        <div class="dd-box-color" [style.background-color]="'#'+cm.color"></div>
      </td>
      <td width="60px">{{cm.field | titlecase}}</td>
      <td><div class="dd-long-text">{{cm.text | truncate: 50: true}}</div></td>
      <td>
        <div class="dd-long-text" [innerHTML]="cm.comment | safeHtml: null: null: {allowedTags: []} | tagParser: '16.6px/22px' | bypassSecurity: 'html'">
        </div>
      </td>
      <td width="110px">{{cm.created_at | date}}</td>
      <td  width="120px">
        <p *ngIf="cm.user">{{cm.user | userTitle}}</p>
      </td>
      <td width="10px">
        <a *ngIf="canDeleteAnnotation(cm.user_id)" ngbTooltip="Delete this comment" (click)="onDeleteComment($event, cm)">
          <i class="fa fa-fw fa-trash-alt fa-green"></i>
        </a>
      </td>

    </tr>
    </tbody>
  </table>
</div>
