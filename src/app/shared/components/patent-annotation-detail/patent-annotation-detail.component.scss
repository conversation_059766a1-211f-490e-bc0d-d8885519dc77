
@import 'scss/layout2021/variables';

.dd-title {
    color: #389A85;
  font-size: 18px;
  border-bottom: 2px solid #389A85;
  font-weight: bold;
}

.dd-box-color {
  width: 25px;
  height: 25px;
  display: inline-block;
  text-align: center;
  margin: 0 auto;
  border: 1px solid #A0A0A0;
  border-radius: 3px;
}

.dd-long-text {
  word-wrap: break-word;
  word-break: break-all;
}

.fa-blue {
  color: #337ab7;
}

.fa-green {
  color: #389A85;
}

.dd-table {
  thead td {
    border-top: none;
  }

  tr td {
        font-size: 17px;
    color: #333;
    white-space: initial;
  }

  tr th {
    font-size: 16px;
  }
}

a {
  cursor: pointer;
}

::ng-deep {
  .dd-long-text {
    .lcd-tagged-user, .lcd-tagged-group {
      margin: 1px;
      border: none;
      border-radius: 3px;
      background: $brand-green;
      color: white;
      padding: 0 2px;
    }
  }
}
