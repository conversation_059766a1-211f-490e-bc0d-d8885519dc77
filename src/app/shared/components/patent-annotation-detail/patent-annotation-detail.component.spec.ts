import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PatentAnnotationDetailComponent } from './patent-annotation-detail.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { TruncatePipe } from '@core/pipes';
import { provideMatomo } from 'ngx-matomo-client';

describe('DocumentDetailComponent', () => {
  let component: PatentAnnotationDetailComponent;
  let fixture: ComponentFixture<PatentAnnotationDetailComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PatentAnnotationDetailComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        TruncatePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentAnnotationDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
