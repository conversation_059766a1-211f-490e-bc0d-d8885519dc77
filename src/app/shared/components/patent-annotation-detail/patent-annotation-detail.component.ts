import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import {
  AnnotationService,
  ConfirmationDialogService,
  DocumentAnnotation,
  DocumentLabel,
  FullDocumentAnnotationResponse,
  PatentTableService,
  UserService
} from '@core/services';
import { Subscription } from 'rxjs';
import { TruncatePipe } from '@core/pipes';

@Component({
  selector: 'app-patent-annotation-detail',
  templateUrl: './patent-annotation-detail.component.html',
  styleUrls: ['./patent-annotation-detail.component.scss']
})
export class PatentAnnotationDetailComponent implements OnInit, OnDestroy {
  @Input()
  document: FullDocumentAnnotationResponse;
  private subscriptions = new Subscription();

  constructor(
    public patentTableService: PatentTableService,
    private annotationService: AnnotationService,
    private userService: UserService,
    private confirmationDialogService: ConfirmationDialogService,
    private truncatePipe: TruncatePipe
  ) {
  }

  ngOnInit() {
    if (!this.document) return;
    this.loadUsers();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onDeleteLabel($event: MouseEvent, label: DocumentLabel) {
    const doc = this.document.document;
    const title = `Delete label`;
    const text = this.truncatePipe.transform(label.text, 100, true);
    const message = `Do you want to delete the label <code>${label.label.name}</code> which were assigned to patent
    <code>${this.patentTableService.getPublicationNumber(doc)}</code> for the text <code>${text}</code>?`;

    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');

    modalRef.then(val => {
      if (val) {
        this.deleteLabel(Number(this.document.document.general.docdb_family_id), label.id);
      }
    }, rejected => {
    });
  }

  onDeleteComment($event: MouseEvent, comment: DocumentAnnotation) {
    const doc = this.document.document;
    const title = `Delete comment`;
    const text = this.truncatePipe.transform(comment.text, 50, true);
    const message = `Do you want to delete the comment <code>${comment.comment}</code> which were assigned to patent
    <code>${this.patentTableService.getPublicationNumber(doc)}</code> for the text <code>${text}</code>?`;

    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');

    modalRef.then(val => {
      if (val) {
        this.deleteComment(Number(this.document.document.general.docdb_family_id), comment.id);
      }
    }, rejected => {
    });
  }

  private deleteLabel(documentId: number, documentLabelId: number) {
    const deleteDocumentLabel$ = this.annotationService.deleteDocumentLabel(documentId, documentLabelId)
      .subscribe({
        next: val => {
          this.document.labels = this.document.labels.filter(o => o.id !== documentLabelId);
        },
        error: error => {
          console.error(error);
        }
      });
    this.subscriptions.add(deleteDocumentLabel$);
  }

  private deleteComment(documentId: number, documentCommentId: number) {
    const deleteDocumentComment$ = this.annotationService.deleteDocumentComment(documentId, documentCommentId)
      .subscribe({
        next: val => {
          this.document.comments = this.document.comments.filter(o => o.id !== documentCommentId);
        },
        error: error => {
          console.error(error);
        }
      });
    this.subscriptions.add(deleteDocumentComment$);
  }

  canDeleteAnnotation(user_id: number) {
    return user_id == this.userService.getUser().profile.id || this.userService.isManager();
  }

  private loadUsers() {
    let userIds: number[] = [];
    if (this.document.comments) userIds = [...this.document.comments.map(o => o.user_id), ...userIds];
    if (this.document.labels) userIds = [...this.document.labels?.map(o => o.user_id), ...userIds];
    if (userIds.length < 1) return;
    const payload = { id: 'in:' + userIds.join(','), 'load_all': 1 };
    const getTeamUsers$ = this.userService.getTeamUsers(payload)
      .subscribe({
        next: ({users}) => {
          const userList = Object.fromEntries(users.map(o => [o.id, o]))
          userList[this.userService.getUser().profile.id] = this.userService.getUser().profile
          if (this.document.comments) {
            for (const cm of this.document.comments) {
              cm.user = userList[cm.user_id];
            }
          }
          if (this.document.labels) {
            for (const lb of this.document.labels) {
              lb.user = userList[lb.user_id];
            }
          }
        }
      });
    this.subscriptions.add(getTeamUsers$);
  }
}
