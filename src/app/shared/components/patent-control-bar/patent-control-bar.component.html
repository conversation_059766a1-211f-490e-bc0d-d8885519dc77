<div class="similar-search-top d-flex justify-content-start align-items-center tools-bar">

  <div class="d-flex justify-content-start align-items-center">
    <div class="group group-separator-right"
      *ngIf="canAddNumbersToSearch() || canAddTask() || hasAddPatent || canAddTag()  || canUseOctiAI()">
      <div class="d-flex justify-content-start align-items-center">
        <a href="javascript:void(0)" *ngIf="canAddNumbersToSearch()" (click)="enableToAdd() && addNumbersToSearch()"
          class="item-bar icon-add-to-search" [ngClass]="{'disabled': !enableToAdd()}" [disabled]="!enableToAdd()"
          ngbTooltip="Refine your search with the selected results" container="body">
          Add to search
        </a>

        <a href="javascript:void(0)" [ngbTooltip]="(isPatentSelected ? 'Remove' : 'Add') + ' patents from the list'" container="body"
          *ngIf="hasAddPatent" (click)="onAddPatent()" class="item-bar " [class]="isPatentSelected ? 'icon-minus' : 'icon-plus'">
          {{ isPatentSelected ? 'Remove patents' : 'Add patents' }}
        </a>
        <ng-template #octiAITooltip>
          <div class="d-flex">
            <div class="d-flex flex-column text-start">
              <div class="content-heading-h6 content-color-secondary">Hi, I'm Octi!</div>
              <div class="content-body-small content-color-secondary">If you need help going through these documents, I'm here to support you.</div>
            </div>
          </div>
        </ng-template>

        <a href="javascript:void(0)" [ngbTooltip]="octiAITooltip"  *ngIf="canUseOctiAI()" (click)="onOctiAIButtonClicked($event)" class="item-bar icon-octi-ai">
          Octi AI
        </a>

        <span [ngbTooltip]="!hasSelectedPatents() ? 'Please select at least one patent in order to request ratings' : null"
              tooltipClass="white-tooltip"
              container="body">
          <a href="javascript:void(0)" *ngIf="canAddTask()" (click)="onRatingButtonClicked($event)"
            class="item-bar icon-fontawesome" [class.disabled]="!hasSelectedPatents()" [disabled]="!hasSelectedPatents()">
            <i class="fa-regular fa-circle-star"></i>Request ratings
          </a>
        </span>

        <span *ngIf="canAddTag()"  tooltipClass="white-tooltip" container="body" [ngbTooltip]="addTagTooltip">
          <a href="javascript:void(0)" (click)="onAddTagButtonClicked($event)"
            class="item-bar icon-add-tag" [class.disabled]="!hasSelectedPatents()">
            Add tag
          </a>
        </span>
      </div>
    </div>

    <div *ngIf="hasFilterListControl || canSortBySimilarity || hasShowColumnControl || canHarmonize() || hasMonitor || hasLandscape"
         class="group group-separator-right">
      <div class="d-flex justify-content-start align-items-center">
        <app-col-selector ngbTooltip="Hide/show columns in the patents list" container="body"
                          *ngIf="hasShowColumnControl"
                          [columns]="columnsToShow"
                          (selectColumns)="selectedColumnsToShowEvent($event)"
                          [selected]="selectedColumnsToShow"></app-col-selector>

        <a href="javascript:void(0)" ngbTooltip="Group applicants under custom aliases" *ngIf="canHarmonize()" (click)="onHarmonize()" container="body"
           class="item-bar icon-harmonize-applicant"> Group applicants</a>

        <a *ngIf="canSortBySimilarity" href="javascript:void(0)" ngbTooltip="Reorder patents based on semantic similarity" (click)="onSortBySimilarity($event)" container="body"
          class="item-bar icon-sort-by-similarity" [ngClass]="{'btn btn-primary': !!sortBySimilarityValue, 'ms-3': isFilteredList && !!sortBySimilarityValue}">
          Sort by similarity
        </a>

        <a *ngIf="hasFilterListControl" href="javascript:void(0)" ngbTooltip="Filter patents list" container="body" (click)="onFilterList($event)"
           class="item-bar icon-filter-list" [ngClass]="{'btn btn-primary': isFilteredList}">
          Filter
        </a>

        <a href="javascript:void(0)" ngbTooltip="Analyze patents with Landscape" container="body"
           *ngIf="hasLandscape" (click)="onLandscape()" class="item-bar icon-landscape"> Landscape
        </a>

        <a href="javascript:void(0)" ngbTooltip="Create a Monitor profile using this collection" container="body"
           *ngIf="hasMonitor" (click)="onMonitor()" class="item-bar icon-monitor"> Monitor
        </a>
      </div>
    </div>

    <div class="group" *ngIf="canSaveToCollection() || canExportPatents() || canSaveAndSharePatents()">
      <div class="d-flex justify-content-start align-items-center">
        <a href="javascript:void(0)" ngbTooltip="Save selected patents in your patent collection" container="body"
          *ngIf="canSaveToCollection()" (click)="onSaveToCollectionClicked($event)" class="item-bar icon-save-list">
          Save
        </a>

        <a href="javascript:void(0)" ngbTooltip="Export selected patents" container="body"
           *ngIf="canExportPatents()" (click)="openExport()" class="item-bar icon-export-list">
          Export
        </a>

        <a href="javascript:void(0)" [ngbTooltip]="shareButtonTooltip" container="body" *ngIf="canSaveAndSharePatents()" (click)="openSharePatents($event, isShared)" class="item-bar item-secondary icon-share">
          {{isShared ? 'Shared' : 'Share'}}
        </a>
      </div>
    </div>
  </div>
</div>

<ng-template #shareButtonTooltip>
  <span [innerHTML]="getShareButtonTooltip()"></span>
</ng-template>

<app-popper #ratingPopper
            placement="bottom" [showArrow]="false" [resizePopperWidth]="false"
            customClass="button-control-bar-popper p-spacing-none"
            [allowedClickClasses]="['popper-inner', 'ngb-dp-body']">
  <div class="button-rating-form-popper">
    <app-patent-rating-form *ngIf="canAddTask() && ratingPopper.isOpen" [storeService]="storeService"
                            [showCloseButton]="false"
                            [task]="emptyTask"
                            [resourceId]="taskResourceId"
                            [resourceType]="taskResourceType"
                            (hideRatingFormEvent)="closeRatingPopper($event)">
    </app-patent-rating-form>
  </div>
</app-popper>

<div class="octi-ai-bottom-wrapper" *ngIf="canUseOctiAI()" [ngClass]="{'ip-lounge-user': isIpLounge}">
  <ng-template #octiAILauncherTooltip>
    <div class="d-flex">
      <div class="content-color-active m-r-spacing-xxx-s">
        <i class="fa-solid fa-sparkles"></i>
      </div>
      <div class="d-flex flex-column text-start">
        <div class="content-heading-h6 content-color-secondary">Hi I'm Octi!</div>
        <div class="content-body-small content-color-secondary">If you need help going through these documents, I'm here to support you.</div>
      </div>
    </div>
  </ng-template>
  <i class="octi-ai-bottom-launcher" *ngIf="!octiAIPopper.isOpen" tooltipClass="tooltip-sm" [ngbTooltip]="octiAILauncherTooltip" (click)="onOctiAIButtonClicked($event)"></i>
</div>

<app-popper #octiAIPopper placement="top-end" [showArrow]="false" [resizePopperWidth]="false"
  customClass="octi-ai-popper p-spacing-none super-z-index" [allowedClickClasses]="['popper-inner', 'ngb-dp-body', 'publication-table']">
  <app-octi-panel *ngIf="octiAIPopper.isOpen && canUseOctiAI()" [storeService]="storeService"
    [isAskByPatent]="false" [showHeader]="true" [chatID]="searchHash" wrapperClass="scrollbar-2024"
    [range]="octiListChatRange" [documents]="getDocuments()"
    (closePanel)="closeOctiPopper()">
  </app-octi-panel>
</app-popper>

<app-popper #addTagPopper placement="bottom" [showArrow]="false" [resizePopperWidth]="false"
            customClass="p-spacing-sm tags-select-patent-control-bar super-z-index"
            [allowedClickClasses]="['tags-select-patent-control-bar']">
  <app-tags-select *ngIf="addTagPopper.isOpen && canAddTag()" [canAddTag]="true"
                   [storeService]="storeService" [multipleTaggingDocumentIds]="storeService.selectedPatentIds"
                   (tagsSelectAddedTag)="addTagPopper.hide()"
                   (tagsSelectCanceled)="addTagPopper.hide()">
  </app-tags-select>
</app-popper>
