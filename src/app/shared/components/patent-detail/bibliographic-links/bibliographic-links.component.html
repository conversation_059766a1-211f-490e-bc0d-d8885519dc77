<ng-container *ngIf="linkEnabled() else showWithoutLink">
    <span *ngFor="let item of getListItems(); let i = index">
        <ng-template #tooltipTemp>
          <div [innerHTML]="getTitle(false, i)" class="text-start"></div>
        </ng-template>
        <a routerLink="/boolean" target="_blank"
           [queryParams]="getItemParam(i)"
           [ngbTooltip]="tooltipTemp" container="body"
           tooltipClass="bibliographic-tooltip" class="bibliographic-items"
           [innerHtml]="item"></a><!--
      -->{{i < getListItems().length - 1 ? ',' : ''}}
    </span>
  <a routerLink="/boolean" target="_blank"
     [queryParams]="getAllParams()"
     [ngbTooltip]="getTitle(true)">
    <i class="fa fa-external-link-alt open-document-link"></i>
  </a>
</ng-container>
<ng-template #showWithoutLink>
  <div class="bibliographic-items" [innerHtml]="getAllItemsValues(patent)"></div>
</ng-template>
