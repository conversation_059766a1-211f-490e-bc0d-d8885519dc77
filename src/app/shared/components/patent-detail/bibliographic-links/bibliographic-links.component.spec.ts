import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

import { BibliographicLinksComponent } from './bibliographic-links.component';

describe('BibliographicLinksComponent', () => {
  let component: BibliographicLinksComponent;
  let fixture: ComponentFixture<BibliographicLinksComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [BibliographicLinksComponent],
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])   
      ], providers: [ provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BibliographicLinksComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
