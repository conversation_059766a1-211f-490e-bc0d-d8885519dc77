import { Component, Input, OnInit } from '@angular/core';
import { UserProfile } from '@core/models';
import { PatentTableService, UserService } from '@core/services';

enum fields { IPC = 'ipc', CPC = 'cpc', INVENTORS = 'inventors', APPLICANTS = 'applicants', ASSIGNEES = 'assignees',
  INVENTORS_ORIGINAL = 'inventors_original', APPLICANTS_ORIGINAL = 'applicants_original', ASSIGNEES_ORIGINAL = 'assignees_original',
  OWNERS = 'owners', ULTIMATE_OWNERS = 'ultimate_owners'
};

@Component({
  selector: 'app-bibliographic-links',
  templateUrl: './bibliographic-links.component.html',
  styleUrls: ['./bibliographic-links.component.scss']
})
export class BibliographicLinksComponent implements OnInit {

  @Input() field: fields;
  @Input() patent: any;
  @Input() patentParameterValue: any;
  @Input() hasLinksToBooleanSearch: boolean;
  @Input() fieldBooleanSearch: string;

  profile: UserProfile;

  constructor(public patentTableService: PatentTableService, public userService: UserService) {
  }

  ngOnInit() {
    if (this.userService.getUser()) {
      this.profile = this.userService.getUser().profile;
    }

  }

  linkEnabled() {
    return this.patentTableService.showBooleanSearchLinks(this.patent, this.field, this.hasLinksToBooleanSearch);
  }

  getListItems() {
    if (this.field === 'owners' || this.field === 'ultimate_owners') {
      return this.patent.bibliographic[this.field]?.map(ow => ow.name.toUpperCase()) ?? null;
    }
    return this.patent.bibliographic[this.field] ?? null;
  }

  getItemValue(index: number) {
    if (this.field === 'owners' || this.field === 'ultimate_owners') {
      return this.patent.bibliographic[this.field][index].id ?? this.patent.bibliographic.owner_ids[index];
    }

    if (!this.patentParameterValue) {
      this.patentParameterValue = this.patent;
    }
    return this.patentParameterValue.bibliographic[this.field][index];

  }

  getAllItemsValues(patenValue) {
    if (!patenValue) {
      patenValue = this.patent;
    }

    switch (this.field) {
      case fields.IPC:
        return this.patentTableService.getIPC(patenValue);
      case fields.CPC:
        return this.patentTableService.getCPC(patenValue);
      case fields.INVENTORS:
        return this.patentTableService.getInventors(patenValue);
      case fields.INVENTORS_ORIGINAL:
        return this.patentTableService.getInventorsOriginal(patenValue);
      case fields.APPLICANTS:
        return this.patentTableService.getApplicants(patenValue);
      case fields.APPLICANTS_ORIGINAL:
        return this.patentTableService.getApplicantsOriginal(patenValue);
      case fields.ASSIGNEES:
        return this.patentTableService.getAssignees(patenValue);
      case fields.ASSIGNEES_ORIGINAL:
        return this.patentTableService.getAssigneesOriginal(patenValue);
      case fields.OWNERS:
        return this.patentTableService.getOwnersIds(patenValue);
      case fields.ULTIMATE_OWNERS:
        return this.patentTableService.getUltimateOwnersIds(patenValue);
      default:
        return null;
    }
  }

  getTitle(allItems: boolean, index?: number) {
    let description = '';
    let singular = '';
    let plural = '';

    switch (this.field) {
      case fields.IPC:
        description = this.patentTableService.getIpcDescription(this.getItemValue(index));
        singular = 'IPC code';
        plural = 'IPC codes';
        break;
      case fields.CPC:
        description = this.patentTableService.getCpcDescription(this.getItemValue(index));
        singular = 'CPC code';
        plural = 'CPC codes';
        break;
      case fields.INVENTORS:
      case fields.INVENTORS_ORIGINAL:
        singular = 'inventor';
        plural = 'inventors';
        break;
      case fields.APPLICANTS:
      case fields.APPLICANTS_ORIGINAL:
        singular = 'applicant';
        plural = 'applicants';
        break;
      case fields.ASSIGNEES:
      case fields.ASSIGNEES_ORIGINAL:
        singular = 'assignee';
        plural = 'assignees';
        break;
      case fields.OWNERS:
        singular = 'owner';
        plural = 'owners';
        break;
      case fields.ULTIMATE_OWNERS:
        singular = 'ultimate owner';
        plural = 'ultimate owners';
        break;
      default:
        return null;
    }

    if (allItems) {
      return `Show more documents from these ${plural}`;
    }

    if (!description) {
      return `Show more documents from this ${singular}`;
    }

    return description;
  }

  getItemParam(index: number) {
    const params = {field: this.fieldBooleanSearch?.toUpperCase() ?? this.field.toUpperCase(), value: this.getItemValue(index)};
    if (this.field === 'owners' || this.field === 'ultimate_owners') {
      params['label'] =  this.patent.bibliographic[this.field][index].name;
    }
    if (this.field === 'assignees' && this.patent.bibliographic['owner_ids']?.length) {
      params['value'] =  this.patent.bibliographic['owner_ids'][0];
      params['label'] =  this.patent.bibliographic[this.field][index];

    }
    return params;
  }

  getAllParams() {
    const params = {field: this.fieldBooleanSearch?.toUpperCase() ?? this.field.toUpperCase(), value: this.getAllItemsValues(this.patentParameterValue)}
    if (this.field === 'owners' || this.field === 'ultimate_owners') {
      params['label'] = this.patent.bibliographic[this.field].map(ow => ow.name.toUpperCase()).join(', ');
    }
    if (this.field === 'assignees' && this.patent.bibliographic['owner_ids']?.length) {
      params['value'] =  this.patent.bibliographic['owner_ids'][0];
      params['label'] = this.patent.bibliographic[this.field].map(ow => ow.toUpperCase()).join(', ');
    }
    return params;
  }
}
