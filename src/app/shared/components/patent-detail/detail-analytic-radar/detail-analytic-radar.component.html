<div *ngIf="!isCalculating">
    <highcharts-chart
        [Highcharts]='Highcharts'
        constructorType='chart'
        [options]='chartOptions'
        (chartInstance)="onChartInstance($event)"
        style="display: block;">
    </highcharts-chart>
</div>

<!-- <ng-template #spinner *ngIf="isCalculating"> -->
    <div style="text-align: center; padding: 30px;" *ngIf="isCalculating | async">
        <img src="assets/images/octimine_blue_spinner.gif" alt="Please hold">
    </div>
<!-- </ng-template> -->
