import { Component, HostListener, Injector, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

import * as Highcharts from 'highcharts';
import * as settings from './settings';
import More from 'highcharts/highcharts-more';
import { BehaviorSubject } from 'rxjs';
import { BaseChartComponent } from '@shared/charts/base-chart/base-chart.component';

More(Highcharts);

declare var $: any;

@Component({
  selector: 'app-detail-analytic-radar',
  templateUrl: './detail-analytic-radar.component.html',
  styleUrls: ['./detail-analytic-radar.component.scss']
})
export class DetailAnalyticRadarComponent extends BaseChartComponent implements OnInit, OnChanges {

  @Input() dataSourceExternal: any;
  @Input() id: string;
  @Input() showTechnologyAverage = true;

  component = DetailAnalyticRadarComponent;
  Highcharts: typeof Highcharts = Highcharts;

  chartOptions = settings.chartSetting;

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
  }

  ngOnInit() {
    if (this.id === 'viewer' && this.chartOptions.series.length > 24) {
      this.chartOptions.series.splice(24, 8);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dataSourceExternal'] && changes['dataSourceExternal'].currentValue) {
      this.datasource = changes['dataSourceExternal'].currentValue;
      this.isCalculating = true;
      $('.' + this.id).remove();
      this.updateChartData();
    }
  }

  @HostListener('window:resize')
  onResizeBrowser() {
    setTimeout(() => {
      if (this.chart && this.chart.series && this.datasource) {
        $('.' + this.id).remove();
        this.addLines();
      }
    }, 200);
  }

  private addSeries(seriesObj): void {
    for (let i = 0; i < 8; i++) {

      const serie = Object.assign({}, seriesObj);
      serie['yAxis'] = i;

      if (i === 0) {
        serie['start'] = true;
        delete serie.linkedTo;
      }

      if (i === 7) {
        serie['end'] = true;
      }

      this.chartOptions.series.push(serie);
    }
  }

  private updateChartData(): void {
    if (this.chart && this.chart.series && this.datasource) {
      /**
       * Sets the data in the series
       */
      let startPosition = 0;

      this.chart['data'] = [];
      if (this.chart.yAxis) {
        this.chart.yAxis.forEach((axis, i) => {
          axis.setExtremes(0, this.datasource.max_average[i], false, false);
        });

        this.setDataSeries(this.datasource.focal_average, startPosition);
        startPosition += 8;
        if (this.showTechnologyAverage) {
          this.setDataSeries(this.datasource.technology_average, startPosition);
          startPosition += 8;
        } else {
          this.removeTechnologyAverage();
        }
        this.setDataSeries(this.datasource.universe_average, startPosition);
        startPosition += 8;

        this.addLines();
      }
      this.reflowChart();
    }

    this.isCalculating = false;
  }

  private setDataSeries(series: Array<number>, initialPosition = 0) {
    series.forEach((v, i) => {
      this.chart.series[i + initialPosition].setData([[i, v]], true, false, true);
      this.chart.data.push([[i, v]]);
    });
  }

  private removeTechnologyAverage() {
    for (let i = this.chart.series.length; i--;) {
      const item = this.chart.series[i];
      if (item.name === 'Technology Average') {
        item.remove();
      }
    }
  }

  addLines() {
    const chart = this.chart;
    let point, focalPath = [], technologyPath = [], universePath = [], similarPath = [], pathStart = null;

    chart.series.forEach(series => {

      point = series.data[0];
      if (point) {
        if (series.options.start) {
          pathStart = {
            plotX: point.plotX + chart.plotLeft,
            plotY: point.plotY + chart.plotTop
          };
        }

        switch (series.options.seriesName) {
          case 'focal':
            focalPath = [
              ...focalPath,
              ...this.getPaths(chart, pathStart, series, point)
            ];
            break;
          case 'technology':
            technologyPath = [
              ...technologyPath,
              ...this.getPaths(chart, pathStart, series, point)
            ];
            break;
          case 'universe':
            universePath = [
              ...universePath,
              ...this.getPaths(chart, pathStart, series, point)
            ];
            break;
          case 'similar':
            similarPath = [
              ...similarPath,
              ...this.getPaths(chart, pathStart, series, point)
            ];
            break;
        }
      }

    });

    this.renderPath(chart, similarPath, '#a953b6', 4);

    this.renderPath(chart, focalPath, '#BA4C20', 3);

    this.renderPath(chart, technologyPath, '#69B9D5', 2);

    this.renderPath(chart, universePath, '#F5A669', 1);

  }

  getPaths(chart, pathStart, series, point) {
    let path = [];
    if (series.options.start) {
      path = ['M', pathStart.plotX, pathStart.plotY];
    }

    path.push('L');

    path.push(point.plotX + chart.plotLeft);
    path.push(point.plotY + chart.plotTop);

    if (series.options.end) {
      path.push('L');
      path.push(pathStart.plotX);
      path.push(pathStart.plotY);
    }

    return path;
  }

  renderPath(chart, path, color, z) {
    chart.renderer.path(path).attr({
      'stroke-width': 2,
      stroke: color,
      zIndex: z
    }).addClass('cLine1').addClass(this.id).add();
  }

  onChartInstance(event: Highcharts.Chart) {
    setTimeout(() => { this.chart = event; this.updateChartData(); });
  }
}
