import { chartsConfig } from './../../../charts/config'

export const chartSetting = {
  chart: {
    polar: true,
    type: 'line',
    backgroundColor: chartsConfig.bgColor,
    marginTop: 30
  },
  legend: {
    align: 'center',
    itemMarginTop: 50,
    itemStyle: {
      color: 'black'
    }
  },
  credits: chartsConfig.credits,
  exporting: { enabled: false },
  title: {
    text: null
  },
  pane: {
    size: '100%'
  },
  xAxis: {
    categories: ['Value indicator', 'Risk indicator', 'Recency indicator', 'Claims description consistency',
      'Technological broadness indicator', 'No. of citations', 'No. of references', 'No. of family size'],
    labels: {
      y: 12,
      style: {
        color: 'black'
      }
    }
  },
  yAxis: setYAxis(32),
  tooltip: {
    formatter: function () {
      if (this.points && this.points[1]) {
        let tip = `<span style="font-size:16px; font-weight:bold">${this.x}</span><br/><br/>
          <span style="font-size:15px;">Focal publication:<span style="font-weight:bold">${this.y}</span></span><br/>
          <span style="font-size:15px;">Portfolio average:<span style="font-weight:bold">${this.points[1].y}</span></span><br/>`;
        if (this.points.length > 2) {
          tip +=  `<span style="font-size:15px;">Patent universe average:<span style="font-weight:bold">${this.points[2].y}</span></span><br/>`;
        }
        if (this.points.length > 3) {
          tip += `<span style="font-size:15px;">Similar publication:<span style="font-weight:bold">${this.points[3].y}</span></span><br/>`;
        }
        tip += `<br/><span style="font-size:14px;">${getInformation(this.x)}</span>`;
        return tip;
      }
      return '';
    },
    shared: true,
    useHTML: true,
  },
  plotOptions: {
    series: {
      pointPlacement: 'on',
      marker: {
        symbol: 'circle'
      }
    }
  },
  series: setSeries({
    quantity: 8,
    series: [
      {
        name: 'Focal publication',
        id: 'focal',
        color: '#BA4C20'
      }, {
        name: 'Technology average',
        id: 'technology',
        color: '#69B9D5'
      }, {
        name: 'Patent universe average',
        id: 'universe',
        color: '#F5A669'
      }
    ]
  })
};

function getInformation(indicator) {
  if (indicator === 'Value indicator') {
    return `<span>Takes into account over 30 variables and inventor survey information to classify patents for their valuation.</span><br/><br/>
      <span>Scale: 0 - 3</span><br/>
      <span>3 = Top 1% most valued</span><br/>
      <span>2 = Top 10%</span><br/>
      <span>1 = Top 25%</span><br/>
      <span>0 = Bottom 75%</span><br/>`;
  }
  if (indicator === 'Risk indicator') {
    return `<span>AI based prediction on possible involvements into litigation events like oppositions.</span><br/><br/>
      <span>Scale: 0 - 3</span><br/>
      <span>3 = Top 1% most risky</span><br/>
      <span>2 = Top 10%</span><br/>
      <span>1 = Top 25%</span><br/>
      <span>0 = Bottom 75%</span><br/>`;
  }
  if (indicator === 'Recency indicator') {
    return `<span>Measures the time lag between the focal patent and its backward citations</span><br/><br/>
      <span>Scale: 0 - 3</span><br/>
      <span>3 = Top 1% most recent</span><br/>
      <span>2 = Top 10%</span><br/>
      <span>1 = Top 25%</span><br/>
      <span>0 = Bottom 75%</span><br/>`;
  }
  if (indicator === 'Claims description consistency') {
    return `<span>A measure of similarity between the text of claims and description.</span><br/>
      <span>Scale: 0 (lowest)  - 1 (highest)</span><br/>`;
  }
  if (indicator === 'Technological broadness indicator') {
    return `<span>Number of technical fields covered.</span><br/>
      <span>Scale: 1 (min) - 35 (max possible WIPO technical fields)</span><br/>`;
  }
  if (indicator === 'No. of citations') {
    return `<span>Calculated by counting the number of citations received by each focal patent from other patents after being published.</span><br/>`;
  }
  if (indicator === 'No. of references') {
    return `<span>Calculated by counting the number of patents each focal patent is citing back in time.</span><br/>`;
  }
  if (indicator === 'No. of family size') {
    return `<span>Number of patents in a patent family.</span><br/>`;
  }
}

/**
 * We need one yAxis for each value of each indicator
 * So for instance if we have 8 indicators and 3 different
 * analysis, then we need 24 axis in order to accommodate the values.
 */
function setYAxis(quantity) {
  let i = 0;
  const yAxis = [];
  for (i; i < quantity + 1; i++) {
    yAxis.push({
      min: 0,
      visible: false
    });
  }
  return yAxis;
}


/**
 * in order to have different scales for each axis
 * we need one series for each indicator. this method
 * provides those series.
 */
function setSeries(config: any) {
  const series = [];

  config.series.forEach(data => {
    for (let i = 0; i < config.quantity; i++) {
      const seriesObj = {
        data: [],
        name: data.name,
        yAxis: i,
        linkedTo: data.id,
        seriesName: data.id,
        color: data.color
      };

      if (i === 0) {
        seriesObj['start'] = true;
        seriesObj['id'] = data.id;
        delete seriesObj.linkedTo;
      }

      if (i === config.quantity - 1) {
        seriesObj['end'] = true;
      }

      series.push(seriesObj);
    }
  });
  return series;
}
