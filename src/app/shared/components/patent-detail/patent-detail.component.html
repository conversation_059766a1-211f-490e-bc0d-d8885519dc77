<td [attr.colspan]="colspan" class="patent-detail">
  <div class="row {{sectionClass}}">
    <div class="col-5">
      <div class="col-12 p-2 text-center">
        <app-patent-image [publication]="storeService.isPublications ? publication_number : null"
                          [patent]="patent" [loadImageList]="false">
        </app-patent-image>
      </div>
      <div class="d-flex align-content-start justify-content-center flex-wrap gap-2 mb-2 mt-3">
        <div (click)="downloadPdf(publication_number, $event)"
             *ngIf="userService.isNotExternalUser()">
          <a class="btn btn-sm btn-ghost btn-outline">View PDF</a>
          <img src="/assets/images/octimine_blue_spinner.gif" style="width: 25px;"
            *ngIf="isDownloadingPdfs[publication_number]">
        </div>

        <a class="btn btn-sm btn-ghost btn-outline"
           *ngIf="userService.isNotExternalUser() && publication_number != 'N/A'"
           (click)="openPublication('/search/?publication='+publication_number)">Similar publications</a>

        <a class="btn btn-sm btn-ghost btn-outline"
           *ngIf="userService.isNotExternalUser() && publication_number != 'N/A'"
           (click)="openPublication('/citation/?publication='+publication_number)">Citation search</a>

        <div (click)="addNumbersToSearch()" *ngIf="canAddNumbersToSearch()">
          <a class="btn btn-sm btn-ghost btn-outline" ngbTooltip="Use the selected results to refine your search">
            Add to search
          </a>
        </div>

        <a class="btn btn-sm btn-secondary-ghost btn-outline"
           *ngIf="userService.isNotExternalUser() && hasWorkflowFeature()"
           (click)="onSharePatentClicked(patent)">
          Share
          <i class="fas fa-share-alt icon-right"></i>
        </a>

      </div>
    </div>
    <div class="col-7">
      <div class="row mb-3">
        <div class="col-12">
          <div class="row">
            <div class="col-3">
              <div class="detail-title">Abstract</div>
            </div>
            <div class="col-9">
              <div *ngIf="patentAbstract" class="detail-content paragraph-2 mb-0" [innerHtml]="patentAbstract"></div>
              <div *ngIf="!patentAbstract" class="detail-content paragraph-2 mb-0">
                <i class="fa-regular fa-circle-info"></i> Text not available. This section of the document was not yet made available by the patent office.
              </div>
            </div>
          </div>
          <div class="row" *ngIf="patent?.bibliographic.abstract_publication_number">
            <div class="col-3"></div>
            <div class="col-9">
              <div class="abstract-source">
                Abstract from {{patent?.bibliographic.abstract_publication_number}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">IPC class</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content ipc-content" field='ipc' [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" ></app-bibliographic-links>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">CPC class</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content cpc-content" field='cpc' [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" ></app-bibliographic-links>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">Publ. date</div>
        </div>
        <div class="col-9">
          <div class="detail-content paragraph-2 mb-0">{{patent?.bibliographic.publication_date | dateFormat: 'ShortDate'}}</div>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">Inventors</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content inventors-content" field='inventors' [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" ></app-bibliographic-links>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">Applicants</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content inventors-content" field='applicants' [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" ></app-bibliographic-links>
        </div>
      </div>

      <div class="row mb-3">
        <div class="col-3">
          <div class="detail-title">Owners</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content inventors-content"
            [field]="patent?.bibliographic?.owners?.length || !patent?.bibliographic?.owner_ids?.length ? 'owners' : 'assignees'"
            [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" fieldBooleanSearch="owner_ids" [patent]="patent"></app-bibliographic-links>
        </div>
      </div>
      <div class="row mb-3" *ngIf="patentService.showSubsidiary(patent)">
        <div class="col-3">
          <div class="detail-title">{{ 'Subsidiaries '| pluralize: patent.bibliographic.owners.length }} of</div>
        </div>
        <div class="col-9">
          <app-bibliographic-links class="detail-content inventors-content" field='ultimate_owners' fieldBooleanSearch="owner_ids" [patent]="patent" [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" ></app-bibliographic-links>
        </div>
      </div>

      <div class="row" *ngIf="patent?.bibliographic.also_published_as">
        <div class="col-3">
          <div class="detail-title">Family</div>
        </div>
        <div class="col-9">
          <div class="detail-content family-content pe-2">
            <div *ngFor="let publication of patent?.bibliographic.also_published_as; let last=last;"
                 class="d-flex align-items-center justify-content-between" [ngClass]="{'mb-2': !last}">
              <div class="d-flex align-items-center justify-content-start">
                <span class="ms-1" [ngClass]="getFlagIcon(publication)"></span>
                <a class="family-link ms-1" (click)="showInfo(publication)">{{ publication }}</a>
              </div>
              <div class="flex-grow-1 d-flex align-items-end justify-content-end">
                <app-authority-legal-status
                  [name]="getFamilyMemberLegalStatusIcon(publication).name"
                  [tooltip]="getFamilyMemberLegalStatusIcon(publication).tooltip"
                  [authorities]="getFamilyMember(publication)?.authority_legal_status"
                  size="sm" class="align-self-end">
                </app-authority-legal-status>
              </div>
              <span *ngIf="!documentInfo?.length"></span>
            </div>
          </div>
        </div>
      </div>

      <div class="row mb-3" *ngIf="patent?.bibliographic.also_published_as">
        <div class="col-3"></div>
        <div class="col-9">
          <app-legal-status-tracking *ngIf="userService.hasMonitor"
                                     [publications]="patent?.bibliographic.also_published_as">
          </app-legal-status-tracking>
        </div>
      </div>
    </div>
  </div>
</td>
