import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DetailAnalyticRadarComponent } from './detail-analytic-radar/detail-analytic-radar.component';
import { HighchartsChartModule } from 'highcharts-angular';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { PatentDetailComponent, PatentImageComponent } from '..';
import { BibliographicLinksComponent } from './bibliographic-links/bibliographic-links.component';
import { SharedModule } from '@shared/shared.module';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentDetailComponent', () => {
  let component: PatentDetailComponent;
  let fixture: ComponentFixture<PatentDetailComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        PatentDetailComponent,
        PatentImageComponent,
        DetailAnalyticRadarComponent,
        BibliographicLinksComponent
      ],
      imports: [
        SharedModule,
        HighchartsChartModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentDetailComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
