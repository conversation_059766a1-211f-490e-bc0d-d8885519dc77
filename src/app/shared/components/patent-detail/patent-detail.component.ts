import { Component, Injector, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  ChartsService,
  DocumentService,
  MatomoService,
  PatentNumberService,
  PatentService,
  PatentTableService,
  PatentViewService,
  ReadDocumentsService,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core/services';
import { Router } from '@angular/router';
import { BaseStoreService } from '@core/store';
import { catchError, debounceTime, filter, finalize, map, take, tap } from 'rxjs/operators';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BehaviorSubject, mergeMap, Observable, of, Subscription } from 'rxjs';
import { CollaborationResourceTypeEnum, Patent, UserProfile } from '@core/models';
import { PdfNotFoundDialogComponent } from '../pdf-not-found-dialog/pdf-not-found-dialog.component';
import { PdfOtherErrorsDialogComponent } from '../pdf-other-errors-dialog/pdf-other-errors-dialog.component';
import { ShareDialogComponent, ShareDialogShareTypeEnum } from '../share-dialog';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';

@Component({
  selector: 'app-patent-detail',
  templateUrl: './patent-detail.component.html',
  styleUrls: ['./patent-detail.component.scss']
})
export class PatentDetailComponent implements OnInit, OnDestroy {
  /**
   * reference for 'colspan' of detail section. Useful in detail section width for different patent list width
   */
  @Input() colspan?: number = 7;
  /**
   * optional class for detail section
   */
  @Input() sectionClass?: string = '';
  /**
   * reference for searchHash. Useful in analytic radar chart in detail section
   */
  @Input() searchHash?: string;
  /**
   * reference for patent view mode. Useful in opening patent view component in different mode (i.e. content, annotate.)
   */
  @Input() patentViewMode?: string = PatentSideBarViewModeEnum.MODE_CONTENT;
  /**
   * present/hide the analytics button
   */
  @Input() showAnalytics = true;
  @Input() hasLinksToBooleanSearch = false;
  @Input() openAnnotationNewTab = false;
  @Input() backButtonTitle: string;
  @Input() isFocalPatent: boolean;
  @Input() linkData: Object;
  @Input() showAddToSearchButton = false;
  @Input() storeService: BaseStoreService;
  @Input() showHighlight = false;
  @Input() showHighlightSearchHash: string = null;
  @Input() showQueriedPublicationNumber = false;

  showAnalyticChart = false;
  dataSourceChart = null;
  isDownloadingPdfs = {};
  documentInfo;
  profile: UserProfile;

  private subscriptions = new Subscription();
  private loadHighlightSubject = new BehaviorSubject<Patent>(null);

  private patentViewService: PatentViewService;

  constructor(
    protected inject: Injector,
    public patentService: PatentService,
    private patentNumberService: PatentNumberService,
    private chartService: ChartsService,
    private toastService: ToastService,
    public userService: UserService,
    private router: Router,
    public patentTableService: PatentTableService,
    private ngbModal: NgbModal,
    private readDocumentsService: ReadDocumentsService,
    private matomoService: MatomoService,
    private documentService: DocumentService
  ) {
    this.patentViewService = inject.get(PatentViewService);
  }

  /**
   * patent object to be displayed in detail section
   */
  private _patent: Patent;

  get patent(): Patent {
    return this._patent;
  }

  get publication_number(){
    if (this.storeService.isPublications && this.patent?.general?.publication_number){
      return this.patent.general.publication_number.toString().replace(/-|-/gi, '');
    }
    return this.showQueriedPublicationNumber ? this.patentTableService.getQueriedPublicationNumber(this.patent) : this.patentTableService.getPublicationNumber(this.patent)
  }

  get isMonitorViewInput(): boolean {
    return !!this.storeService['monitorProfile'] && !!this.patent?.general['original_number'];
  }

  @Input() set patent(value: Patent) {
    this._patent = value;
    const loadIpcClassifications$ = this.patentTableService.loadIpcClassifications(value);
    this.subscriptions.add(loadIpcClassifications$);
    const loadCpcClassifications$ = this.patentTableService.loadCpcClassifications(value);
    this.subscriptions.add(loadCpcClassifications$);
    this.markAsRead();
    this.loadHighlightSubject.next(value);
  }

  get patentAbstract(): string {
    return this.patentTableService.getPatentAbstract(this.patent, this.showHighlight);
  }

  ngOnInit() {
    const user$ = this.userService.user.subscribe({
      next: data => {
        this.profile = data.profile;
      }
    });
    this.subscriptions.add(user$);

    this.loadFamilyLegalStatus();

    const loadHighlight$ = this.loadHighlightSubject.asObservable()
      .pipe(
        filter((val) => !!val),
        debounceTime(500),
        mergeMap((val) => this.loadHighlight(val))
      )
      .subscribe();
    this.subscriptions.add(loadHighlight$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  downloadPdf(patentNumber, event = null) {
    let isDownloading = false;
    for (const key of Object.keys(this.isDownloadingPdfs)) {
      if (this.isDownloadingPdfs[key]) {
        isDownloading = true;
        break;
      }
    }

    if (this.userService.isExternalUser() || isDownloading) {
      return;
    }

    this.isDownloadingPdfs[patentNumber] = true;
    this.matomoService.resultListViewPdfButton();

    const getPdf$ = this.patentService.getPdf(this.buildAttachmentPayload())
      .pipe(
        take(1),
        finalize(() => this.isDownloadingPdfs[patentNumber] = false)
      ).subscribe({
        next: (file) => {
          const blob = new Blob([file], {type: file.type});
          if (blob.type === 'application/pdf') {
            this.patentService.downloadFile(blob, patentNumber, this.patent);
          }
        },
        error: (err) => {
          console.log(err);
          if (err.status === 404) {
            this.showPdfNotFoundError(patentNumber, this.patent);
          } else {
            this.showPdfOtherErrors(patentNumber, this.patent);
          }
        }
      });
    this.subscriptions.add(getPdf$);
  }

  openPublication(publication: string) {
    if (this.userService.isExternalUser()) {
      return;
    }
    window.open(publication);
  }

  loadAnalyticRadar(patentNumber) {
    return;
    this.showAnalyticChart = !this.showAnalyticChart;

    if (this.showAnalyticChart) {
      const getPatents$ = this.patentNumberService.getPatents({patent_numbers: [patentNumber]}).subscribe({
        next: patent => {
          const payload = {
            charts: ['trend_analytic_radar']
          };

          let dataFocal;
          let dataSearch;
          const calculate$ = this.chartService.calculate(payload, patent.data.search_info.search_hash).subscribe({
            next: data => {
              dataFocal = data.charts.trend_analytic_radar.datasource;
              this.setAnalyticRadarData(dataFocal, dataSearch);
            },
            error: err => {
              console.log(err);
            }
          });
          this.subscriptions.add(calculate$);

          const searchHash = this.searchHash ? this.searchHash : this.storeService.searchHash;
          if (searchHash) {
            const calculate2$ = this.chartService.calculate(payload, searchHash).subscribe({
              next: data => {
                dataSearch = data.charts.trend_analytic_radar.datasource;
                this.setAnalyticRadarData(dataFocal, dataSearch);
              },
              error: err => {
                console.log(err);
              }
            });
            this.subscriptions.add(calculate2$);
          }
        }
      });
      this.subscriptions.add(getPatents$);
    }
  }

  getQueryParamsForCompareLink() {
    return this.buildQueryParams({
      mode: this.patentViewMode,
      focalPatent: this.isFocalPatent
    });
  }

  getFamilyMember(publication: string)  {
    return this.documentInfo?.find(obj => obj.publication_number?.replace(/-|-/gi, '') === publication);
  }

  getFamilyMemberLegalStatusIcon(publication: string) {
    const docInfoFiltered = this.getFamilyMember(publication);
    return this.patentService.getPatentLegalStatusIcon(docInfoFiltered);
  }

  canAddNumbersToSearch(): boolean {
    return this.showAddToSearchButton && this.userService.isNotExternalUser() && this.enableToAdd();
  }

  enableToAdd(): boolean {
    if (this.storeService.typedPublications.indexOf(this.patent?.general.raw_publication_number.toString()) > -1) {
      return false;
    }
    return this.storeService.typedPublications.length + 1 <= 5;
  }

  addNumbersToSearch(): void {
    this.storeService.addNumberToSearch = [this.patent.general.raw_publication_number.toString()];
  }

  hasWorkflowFeature(): boolean {
    return this.userService.canUseWorkflowFeature();
  }

  onSharePatentClicked(patent: Patent) {
      if(!this.userService.canUseSaveAndShareFeature('share')){
        return;
      }
    const title = `${patent.general.raw_publication_number}-${patent.bibliographic.title}`;
    const modal = this.ngbModal.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.hasSharedLink = true;
    modal.componentInstance.headline = `Share patent document ${title}`;
    modal.componentInstance.hideAgreementCheck = true;
    modal.componentInstance.resourceId = patent.general.docdb_family_id;
    modal.componentInstance.resourceType = CollaborationResourceTypeEnum.PATENT;
    modal.componentInstance.shareType = ShareDialogShareTypeEnum.SHARE_WITH_TEAM;
    modal.componentInstance.canSwitchShareType = true;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.shareUrl = '/patent/view/';
  }

  showInfo(publication: string): void {
    if (this.userService.isExternalUser() || this.patent.general.obfuscated) {
      return;
    }
    const documentId = Number(this.patent.general.docdb_family_id);
    window.open(this.patentViewService.getPatentViewerBaseUrl(documentId, publication), '_blank');
  }

  getFlagIcon(publication_number: string) {
    return this.patentService.getFlagCssByPublication(publication_number).toLowerCase();
  }

  private setAnalyticRadarData(focal, search) {
    if (!search || !focal) {
      return;
    }
    const max_average = focal.max_average.map((value, index) => Math.max(value, search.max_average[index]));
    const datasource = {
      focal_average: focal.technology_average,
      technology_average: search.technology_average,
      universe_average: focal.universe_average,
      max_average: max_average
    };
    this.dataSourceChart = datasource;
  }

  private buildQueryParams(initialParams) {
    if (this.backButtonTitle) {
      initialParams['previousUrl'] = this.router.url;
      initialParams['backButtonTitle'] = this.backButtonTitle;
    }
    return initialParams;
  }

  private showPdfNotFoundError(patentNumber, patent) {
    const modalRef = this.ngbModal.open(PdfNotFoundDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = patentNumber;
    modalRef.componentInstance.patent = patent;
  }

  private showPdfOtherErrors(patentNumber, patent) {
    const modalRef = this.ngbModal.open(PdfOtherErrorsDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = patentNumber;
    modalRef.componentInstance.patent = patent;
  }

  private loadFamilyLegalStatus() {
    if (!this.patent) {
      return;
    }
    const getDocumentInfo$ = this.patentService.getDocumentInfo(this.patent.general.docdb_family_id)
      .subscribe({next:({data}) => {
        this.documentInfo = data['document_info'];
      }, error: (err) => { console.error(err); }});
    this.subscriptions.add(getDocumentInfo$);
  }

  private markAsRead(): void {
    const markAsRead$ = this.readDocumentsService.markAsRead(parseInt(this.patent.general.docdb_family_id.toString(), 10)).subscribe({
      next: () => { }, error: (err) => console.log(err)
    });
    this.subscriptions.add(markAsRead$);
  }

  private loadHighlight(patent: Patent): Observable<Patent> {
    const searchHash = this.showHighlightSearchHash || this.storeService.searchHash;
    const filtersQuery = this.storeService.getAppliedFiltersQuery();
    const shouldShowHighlight = this.showHighlight && searchHash && patent?.general?.docdb_family_id &&
      (this.storeService.isBooleanSearchStore() || filtersQuery?.length > 0);

    if (!shouldShowHighlight) {
      return of(patent);
    }

    const payload = {
      search_hash: searchHash,
      document_id: patent.general.docdb_family_id
    };

    if (!this.storeService.isBooleanSearchStore()) {
      payload['boolean_query'] = filtersQuery;
    }

    return this.documentService.getHighlight(payload)
      .pipe(
        tap((res) => {
          patent.highlight = res;
        }),
        map(() => patent),
        catchError((error) => {
          console.error(error);
          return of(patent);
        })
      );
  }

  private buildAttachmentPayload() {
    return this.storeService.isPublications ? {publication_number: this.publication_number} : {document_id: this.patent?.general?.docdb_family_id};
  }
}
