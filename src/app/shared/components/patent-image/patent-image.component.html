<div class="patent-image-container">
  <div *ngIf="isLoadingImage" class="image loading-spinner">
    <img src="assets/images/octimine_blue_spinner.gif" alt="Please hold">
  </div>

  <div *ngIf="!isLoadingImage && image else noImage" class="image">
    <div class="new-tab-opener" *ngIf="patent"><a class="button-main-secondary-grey button-square button-small" (click)="openNewWindow()" href="javascript:void(0)" ><i class="fa-regular fa-arrow-up-right-from-square" [class.fa-2xs]="isSmallIcon"></i></a></div>
    <a (click)="showGallery()">
      <div [ngStyle]="{'background-image': 'url(' + image + ')'}" class="patent-image"></div>
    </a>
    <div class="image-source" *ngIf="showImageSource">{{ getSourceImageLabel() }}</div>
  </div>

  <ng-template #noImage>
    <div class="no-image text-center" *ngIf="!isLoadingImage && !image">Image not available</div>
  </ng-template>
</div>
<lightgallery [settings]="settings" [onInit]="onInit" [onBeforeSlide]="onBeforeSlide" [onBeforeClose]="onBeforeClose" [onAfterOpen]="onAfterOpen"> </lightgallery>
