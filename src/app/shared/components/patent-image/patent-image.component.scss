@use 'scss/figma2023/variables' as variable;
.patent-image-container {
  .image {
    text-align: center;
    display: block;
    overflow: hidden;
    background-color: variable.$colour-base-white;
    position: relative;

    &.loading-spinner {
      padding: 93px 0;

      img {
        width: 140px;
      }
    }

    &:hover .new-tab-opener{
      display: flex;
    }
  }
  
  .new-tab-opener{
    display: none;
    position: absolute;
    top: variable.$spacing-system-spacing-sm;
    right: variable.$spacing-system-spacing-sm;
    transition: all ease-in .2s;
  }

  .image-source {
    color: #BCCACE;
    font-size: 0.875rem;
    padding-top: 5px;
  }

  .no-image {
    border: 1px solid #ECECEC;
    border-radius: 3px;
    padding: 151.5px 20px;
    color: #485D66;
    font-size: 0.875rem;
  }

  .patent-image {
    cursor: pointer;
    height: 300px;
    width: 100%;
    background-size: contain;
    background-color: transparent;
    background-position: top;
    background-repeat: no-repeat;
    &:hover{
      background-color: variable.$colour-transparency-8blue-brand;
    }
  }
}
