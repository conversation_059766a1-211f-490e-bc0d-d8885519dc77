import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PatentImageComponent } from './patent-image.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';

describe('PatentImageComponent', () => {
  let component: PatentImageComponent;
  let fixture: ComponentFixture<PatentImageComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PatentImageComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentImageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
