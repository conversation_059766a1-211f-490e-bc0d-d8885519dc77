<div class="modal-header">
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Close')"></button>
</div>
<div class="modal-body mb-3" *ngIf="!isLoading">
    <h4 class="text-center">{{publicationNumber}}</h4>
    <h6 class="text-center" *ngIf="!documentInfo">No information</h6>
    <div *ngIf="documentInfo">
        <div class="d-flex info p-3" *ngIf="documentInfo.application_date">
          <div class="col-6">Application date</div>
          <div class="col-6 ps-0">{{documentInfo.application_date | dateFormat: 'ShortDate'}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.publication_date">
          <div class="col-6">Publication date</div>
          <div class="col-6 ps-0">{{documentInfo.publication_date | dateFormat: 'ShortDate'}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.application_number">
          <div class="col-6">Application number</div>
          <div class="col-6 ps-0">{{getApplicationNumber()}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.cpc?.length">
          <div class="col-6">CPC class</div>
          <app-bibliographic-links field='cpc' [patent]="patent" [patentParameterValue]="patent"
            [hasLinksToBooleanSearch]="hasLinksToBooleanSearch"></app-bibliographic-links>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.ipc?.length">
          <div class="col-6">IPC class</div>
          <app-bibliographic-links field='ipc' [patent]="patent" [patentParameterValue]="patent"
            [hasLinksToBooleanSearch]="hasLinksToBooleanSearch"></app-bibliographic-links>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.inventors_cleaned?.length">
          <div class="col-6">Inventors</div>
          <app-bibliographic-links field='inventors' [patent]="patent" [patentParameterValue]="patent"
            [hasLinksToBooleanSearch]="hasLinksToBooleanSearch"></app-bibliographic-links>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.applicants_cleaned?.length">
          <div class="col-6">Applicants</div>
          <app-bibliographic-links field='applicants' [patent]="patent" [patentParameterValue]="patent"
            [hasLinksToBooleanSearch]="hasLinksToBooleanSearch"></app-bibliographic-links>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.assignees_cleaned?.length">
          <div class="col-6">Current patent assignee</div>
          <div class="col-6 ps-0">{{documentInfo.assignees_cleaned.join(', ')}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.tech_areas?.length">
          <div class="col-6">Technology areas</div>
          <div class="col-6 ps-0">{{documentInfo.tech_areas.join(', ')}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.tech_fields?.length">
          <div class="col-6">Technology fields</div>
          <div class="col-6 ps-0">{{documentInfo.tech_fields.join(', ')}}</div>
        </div>
        <div class="d-flex info p-3" *ngIf="documentInfo.priority_claims">
          <div class="col-6">Priority claims</div>
          <div class="col-6 ps-0">{{getPriorityClaims()}}</div>
        </div>
        <div class="d-flex info pt-3 pe-3 ps-3 pb-0">
            <div class="col-6">Legal status</div>
            <div class="col-6 ps-0">
                <i class="dot {{getLegalStatusIcon()}} align-self-center"></i>
                {{getFamilyMemberLegalStatus()}}
            </div>
        </div>
        <div class="d-flex info">
            <div class="col-6"></div>
            <div class="col-6 reference-text ps-0">
                * See the extent ouf our legal data coverage
                <a (click)="activeModal.dismiss('Close')" routerLink="/data-coverage" fragment="data-coverage"> here</a>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer d-flex" *ngIf="!isLoading">
    <button class="btn btn-primary-outline btn-md" type="button" (click)="downloadPdf()">View PDF</button>
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isDownloading">
    <button class="btn btn-primary-outline btn-md" type="button" (click)="openEspacenet()">View on Espacenet</button>
    <button class="btn btn-primary-outline btn-md ms-auto" type="button" (click)="activeModal.dismiss('Close')">Close</button>
</div>


<div class="modal-body mb-3" *ngIf="isLoading">
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
</div>
