import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';

import { PatentInfoComponent } from './patent-info.component';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentInfoComponent', () => {
  let component: PatentInfoComponent;
  let fixture: ComponentFixture<PatentInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentInfoComponent ],
      imports: [
        SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
