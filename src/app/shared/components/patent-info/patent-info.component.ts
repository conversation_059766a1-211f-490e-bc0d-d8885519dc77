import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { DateFormatPipe, EspacenetService, Patent, PatentNumberService, PatentService, UserService } from '@core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PdfNotFoundDialogComponent } from '@shared/components/pdf-not-found-dialog/pdf-not-found-dialog.component';
import {
  PdfOtherErrorsDialogComponent
} from '@shared/components/pdf-other-errors-dialog/pdf-other-errors-dialog.component';
import { finalize, take } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-patent-info',
  templateUrl: './patent-info.component.html',
  styleUrls: ['./patent-info.component.scss']
})
export class PatentInfoComponent implements OnInit, OnDestroy {

  @Input() publicationNumber: string ;
  @Input() patent: Patent;
  @Input() hasLinksToBooleanSearch = false;

  documentInfo: any;
  legalStatus: any;

  isLoading = true;
  isDownloading = false;
  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private ngbModal: NgbModal,
    private patentNumberService: PatentNumberService,
    private espacenetService: EspacenetService,
    private patentService: PatentService,
    private userService: UserService,
    private dateFormatPipe: DateFormatPipe
  ) { }

  ngOnInit(): void {
    if (!this.patent) {
      return;
    }
    const getDocumentInfo$ = this.patentNumberService.getDocumentInfo(this.patent.general.docdb_family_id)
    .pipe(
      take(1),
      finalize(() => this.isLoading = false)
    )
    .subscribe({
      next: ({data}) => {
        this.documentInfo = data.document_info.find(doc => doc.publication_number === this.publicationNumber);
        this.legalStatus = this.patentService.getPatentLegalStatusIcon(this.documentInfo);
      },
      error: (err)=> {
        console.error(err);
      }
    });

    this.subscriptions.add(getDocumentInfo$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  public getLegalStatusIcon() {
    return this.legalStatus['icon'];
  }

  openEspacenet() {
    window.open(this.espacenetService.getWorldwideLinkByPublicationNumber(this.publicationNumber, this.patent.general.docdb_family_id), '_blank').focus();
  }

  downloadPdf() {
    if (this.isDownloading) {
      return;
    }

    this.isDownloading = true;
    const getPdf$ = this.patentService.getPdf({patent_number: this.publicationNumber.replace(/-/g, '')})
      .pipe(
        take(1),
        finalize(() => this.isDownloading = false)
      )
      .subscribe({
        next:(file) => {
          const blob = new Blob([file], {type: file.type});
          if (blob.type === 'application/pdf') {
            this.patentService.downloadFile(blob, this.publicationNumber, this.patent);
          }
        },
        error: (err)=> {
          console.error(err);
          if (err.status === 404) {
            this.showPdfNotFoundError();
          } else {
            this.showPdfOtherErrors();
          }
        }
      });
    this.subscriptions.add(getPdf$);
  }

  private showPdfNotFoundError() {
    const modalRef = this.ngbModal.open(PdfNotFoundDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = this.publicationNumber;
    modalRef.componentInstance.patent = this.patent;
  }

  private showPdfOtherErrors() {
    const modalRef = this.ngbModal.open(PdfOtherErrorsDialogComponent, {size: 'lg'});

    modalRef.componentInstance.family = this.publicationNumber;
    modalRef.componentInstance.patent = this.patent;
  }

  public getApplicationNumber(): string {
    return this.documentInfo.application_number.replace(/-/g, '');
  }

  public getFamilyMemberLegalStatus(): string {
    return `${this.legalStatus.description.toUpperCase()} - ${this.legalStatus.tooltip}`;
  }

  public getPriorityClaims(): string {
    const priorityClaims = this.documentInfo.priority_claims.map(claim => {
      return `${claim.priority_number.replace(/-/g, '')} (${this.dateFormatPipe.transform(claim.priority_date, 'ShortDate')})`;
    });
    return priorityClaims.join(', ');
  }
}
