import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentLabelsComponent } from './patent-labels.component';

describe('PatentLabelsComponent', () => {
  let component: PatentLabelsComponent;
  let fixture: ComponentFixture<PatentLabelsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentLabelsComponent ],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentLabelsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});