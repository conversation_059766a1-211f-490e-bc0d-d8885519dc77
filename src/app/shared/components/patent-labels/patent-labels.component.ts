import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-patent-labels',
  templateUrl: './patent-labels.component.html',
  styleUrls: ['./patent-labels.component.scss']
})
export class PatentLabelsComponent implements OnInit {

  @Input() patent;
  greenTooltip = ''

  constructor() {
  }

  get standardText(): string {
    let text = ''
    if (this.patent.tags.sep.length === 1) {
      text = 'standard ' + this.patent.tags.sep[0];
    } else {
      text = 'standards ' + this.patent.tags.sep.join(', ').replace(/,([^,]*)$/, ' and $1');
    }

    return 'This is an essential patent for ETSI ' + text;
  }

  ngOnInit(): void {
    if (this.patent?.general?.green_codes?.length > 0) {
      this.greenTooltip = this.groupSameCodes(this.patent.general.green_codes)
        .map(c => this.generateCategoryHtml(c))
        .reduce((a, b) => a + b, '');
    }
  }

  private generateCategoryHtml(item: { codes: string[], description: string, type: string, titleKey: string }): string {
    return `
      <div class="categories-text">
          <div class="open-sans-bold">
              <div>${item.type}</div>
              <div class="text-nowrap ms-3">${item.codes.join('<br/>')}</div>
          </div>
          <div class="ps-4">${item.description}</div>
      </div>`;
  }

  private getCodeType(code: string): string {
    return this.patent.bibliographic.ipc4?.find(c => code.startsWith(c.toString())) ? 'IPC' : 'CPC';
  }

  private generateDescription(data: { root_title: string, parent_title: string, title: string }): string {
    return `
      <ul>
          <li>
              ${data.root_title}
              <ul>
                  <li>
                      ${data.parent_title}
                      <ul>
                          <li>${data.title}</li>
                      </ul>
                  </li>
              </ul>
          </li>
    </ul>`;
  }

  private generateTitleIdentifier(codeType: string, data: {
    root_title: string,
    parent_title: string,
    title: string
  }): string {
    return (codeType + data.root_title.trim() + data.parent_title.trim() + data.title.trim()).toLowerCase();
  }

  private groupSameCodes(greenCodes: {}[]): { codes: string[], description: string, type: string, titleKey: string }[] {
    const groupedCodes: { codes: string[], description: string, type: string, titleKey: string }[] = [];

    greenCodes.forEach((item) => {
      const actualCode = Object.keys(item)[0];
      const codeType = this.getCodeType(actualCode);
      const titleKey = this.generateTitleIdentifier(codeType, item[actualCode]);
      const foundSameItem = groupedCodes.find(c => c.titleKey === titleKey);

      if (foundSameItem) {
        foundSameItem.codes.push(actualCode);
      } else {
        groupedCodes.push({
          type: codeType,
          codes: [actualCode],
          description: this.generateDescription(item[actualCode]),
          titleKey: titleKey
        });
      }
    });

    return groupedCodes;
  }
}
