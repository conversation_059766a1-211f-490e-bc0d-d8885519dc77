<div class="patent-list-box" data-intercom-target="Deep learning input">
  <div class="text-and-patent">
    <div class="text-patent-mix" [class.disable-form]="isProcessing">
      <div class="faded-cover"></div>
      <textarea class="form-control patent-numbers" id="patent_numbers" [(ngModel)]="patent_numbers" #textarea data-intercom-target="patent-numbers-input"
        (keydown.control.enter)="onSubmit()"
        placeholder="Type a list of patent numbers, retrieve it from your collection or upload external files (e.g. EP2049363, US8697359, WO2018158104)" value=""
        *ngIf="!collectionDialogOpened" (ngModelChange)="onChange($event)"></textarea>
      <app-add-to-collection *ngIf="collectionDialogOpened"
                             [getCollectionPublicationNumbers]="true" [canCreateList]="false" [canCreateFolder]="false"
                             (listSelection)="onListSelection($event)" [showHeader]="false" [patentListScope]="null"
                             [customTitle]="collectionsTitle" [storeService]="storeService" [permission]="null">
      </app-add-to-collection>
      <div class="mt-2">
        <a href="javascript:void(0)" class="btn btn-blue-outline me-3" (click)="browseCollections()"
          [class.disabled]="(collectionDialogOpened && !selectedList) || selectedList?.results_count === 0">
          <span *ngIf="!collectionDialogOpened">Browse collections list</span>
          <span *ngIf="collectionDialogOpened && !selectedList">Select a list</span>
          <span *ngIf="collectionDialogOpened && selectedList?.results_count > 0">Use this list</span>
          <span *ngIf="collectionDialogOpened && selectedList?.results_count === 0">Selected list is empty</span></a>
        <a href="javascript:void(0)" class="btn btn-blue-outline me-3" (click)="openUploadDialog()"
          [hidden]="collectionDialogOpened">Upload file</a>
        <a href="javascript:void(0)" class="btn btn-gray-outline me-3" (click)="onCancelList()"
          [hidden]="!collectionDialogOpened">Cancel</a>
      </div>
    </div>
  </div>
</div>
<div class="align-self-center d-flex flex-wrap justify-content-between col-6 ps-0 mb-3" *ngIf="patentNumbersType">
  <label class="radio-button mb-1" id="patent-input-type">
    <input type="radio" name="patentNumbersType" class="me-1" [(ngModel)]="patentNumbersType" (change)="onChange($event)" value="publication_number">
    <span>Publication numbers</span>
  </label>

  <label class="radio-button" id="patent-input-type">
    <input type="radio" name="patentNumbersType" class="me-1" [(ngModel)]="patentNumbersType" (change)="onChange($event)" value="application_number">
    <span>Application numbers</span>
  </label>

  <label class="radio-button" id="patent-input-type">
    <input type="radio" name="patentNumbersType" class="me-1" [(ngModel)]="patentNumbersType" (change)="onChange($event)" value="mixed">
    <span>All</span>
  </label>
</div>