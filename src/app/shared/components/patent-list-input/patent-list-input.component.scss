@import 'scss/layout2021/variables';

:host {
  .patent-list-box {
    margin-bottom: 25px;
    border: 1px solid #d5d5d5;

    .text-and-patent {
      display: -ms-flex;
      display: flex;
      justify-content: space-between;
    }

    .text-patent-mix {
      max-width: 100%;
      width: 100%;
      padding: 10px;
      overflow: hidden;
      height: auto;
    }

    textarea {
      font-family: $font-open-sans-regular;
      max-width: 100%;
      min-height: 150px;
      border-color: transparent;
      height: auto;
    }

    .disable-form {
      position: relative;

      .faded-cover {
        position: absolute;
        cursor: wait;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 44;
        opacity: .5;
        background-color: #f7f7f7;
      }
    }
  }

  #patent-input-type {
    font-family: $font-open-sans-bold;
  }

  ::ng-deep {
    .atc-collections {
      max-height: 30vh;
    }
  }
}
