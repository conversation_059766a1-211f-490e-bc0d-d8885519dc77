import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentListInputComponent } from './patent-list-input.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentListInputComponent', () => {
  let component: PatentListInputComponent;
  let fixture: ComponentFixture<PatentListInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentListInputComponent ],
      imports: [
        SharedModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentListInputComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
