import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnDestroy, Output, ViewChild } from '@angular/core';
import { UploadDialogComponent } from '../upload-dialog/upload-dialog.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseStoreService, Collection, PatentNumberService } from '@core';
import { UntypedFormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-patent-list-input',
  templateUrl: './patent-list-input.component.html',
  styleUrls: ['./patent-list-input.component.scss']
})
export class PatentListInputComponent implements AfterViewInit, OnDestroy {
  @Input() patent_numbers: string = '';
  @Input() form: UntypedFormGroup;
  @Input() collectionsTitle: string = 'Get publication number from patent collection list';
  @Input() columnLabel: string = 'Select the column that contains publication numbers: ';
  @Input() storeService: BaseStoreService;
  @Input() patentNumbersType?: string;
  @Output() update = new EventEmitter<{patent_numbers : string, patentNumbersType: string}>(null);
  @Output() save = new EventEmitter();
  collectionDialogOpened: boolean = false;
  isProcessing: boolean = false;
  selectedList: Collection;

  @ViewChild('textarea') filterContainer: ElementRef;
  private subscriptions = new Subscription();

  constructor(
    private patentNumberService: PatentNumberService,
    private modalService: NgbModal
  ) { }

  ngAfterViewInit(): void {
    setTimeout(() => {
      if (this.filterContainer?.nativeElement) {
        this.filterContainer.nativeElement.scrollTop = this.filterContainer.nativeElement.scrollHeight;
      }
    }, );
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  openUploadDialog() {
    const modal = this.modalService.open(UploadDialogComponent, { size: 'lg' });
    modal.componentInstance.columnLabel = this.columnLabel;
    modal.result.then(({ data }) => {
      const patents = data.valid.join('\n');
      if (this.form) {
        this.form.get('patent_numbers').setValue(patents);
      }
      this.updateValue(patents, this.patentNumbersType);
    }, reason => {
      console.log(reason);
    });
  }

  browseCollections() {
    if (this.collectionDialogOpened && this.selectedList) {
      this.isProcessing = true;
      const extractPatentList$ = this.patentNumberService.extractPatentList({collection_id: this.selectedList.id})
        .pipe(
          finalize(() => this.isProcessing = false)
        )
        .subscribe({next: response => {
          const patents = (response.patent_list || []).join('\n');
          this.updateValue(patents, this.patentNumbersType);
          this.collectionDialogOpened = false;
        },error: error => {
          console.error(error);
        }});
      this.subscriptions.add(extractPatentList$);
    } else {
      this.collectionDialogOpened = true;
    }
  }
  updateValue(patents: string, patentNumbersType?: string){
    this.patent_numbers = this.patent_numbers + '\n' + patents ;
    if(this.patentNumbersType){
      this.patentNumbersType = patentNumbersType;
    }
    this.update.emit({patent_numbers : this.patent_numbers, patentNumbersType: this.patentNumbersType});
  }

  onListSelection(selectedList: Collection){
    this.selectedList = selectedList;
    if(selectedList.results_count > 0){
      this.browseCollections();
    }
  }
  onCancelList(){
    this.selectedList = null;
    this.collectionDialogOpened = false;
  }
  onChange(event:any){
    this.update.emit({patent_numbers : this.patent_numbers, patentNumbersType: this.patentNumbersType});
  }

  onSubmit() {
    this.update.emit({patent_numbers : this.patent_numbers, patentNumbersType: this.patentNumbersType});
    this.save.emit();
  }
}
