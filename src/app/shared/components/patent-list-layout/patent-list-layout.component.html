<ng-container *ngIf="!isLoading else loader">
  <div *ngIf="documents?.length || alwaysShowDocumentsControlBar" class="search-result-bar">
    <div class="d-flex container-fluid justify-content-between p-0">
      <div>
        <ng-content select="[customTabs]"></ng-content>
      </div>

      <div>
        <ul *ngIf="showMainTabs" ngbNav class="nav-tabs" [(activeId)]="viewMode" (navChange)="onViewModeChanged($event.nextId)" data-intercom-target="mode-tabs">
          <ng-container *ngFor="let item of menuItems">
            <li [ngbNavItem]="item.id" class="p-0" *ngIf="item.show">
              <a ngbNavLink [class]="item.icon"> {{item.title}}</a>
            </li>
          </ng-container>
        </ul>
      </div>
    </div>
  </div>

  <ng-container *ngIf="documents?.length">
    <div class="bg-white" *ngIf="showVisual && !isCombinedMode && showDashboardActionBar">
      <div class="container-fluid">
        <app-dashboard-action-bar [storeService]="storeService"></app-dashboard-action-bar>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="showList && (alwaysShowDocumentsControlBar || documents?.length)">
    <ng-content select="[documentsControlBar]"></ng-content>
  </ng-container>

  <ng-container *ngIf="documents?.length">
    <div *ngIf="isCombinedMode" appCombinedLayout></div>

    <div class="tab-content" data-intercom-target="result-container">
      <div [ngClass]="{'combined-mode-layout': isCombinedMode}">
        <div *ngIf="showList" [ngClass]="{'combined-list-section': isCombinedMode}">
          <ng-content select="[documentsTable]"></ng-content>
        </div>
        <div *ngIf="showVisual" [ngClass]="{'combined-chart-section': isCombinedMode}">
          <ng-content select="[documentsVisual]"></ng-content>
        </div>
        <ng-content select="[additionalTabsContent]"></ng-content>
      </div>
    </div>
  </ng-container>

  <ng-content select="[alertMessages]"></ng-content>
</ng-container>

<ng-template #loader>
  <app-spinner class="py-5 d-block"></app-spinner>
</ng-template>
