import { of } from 'rxjs';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PatentListLayoutComponent } from './patent-list-layout.component';
import { SemanticSearchStoreService } from '@core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { ActivatedRoute, convertToParamMap } from '@angular/router';

describe('PatentListLayoutComponent', () => {
  let component: PatentListLayoutComponent;
  let fixture: ComponentFixture<PatentListLayoutComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PatentListLayoutComponent],
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(PatentListLayoutComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
