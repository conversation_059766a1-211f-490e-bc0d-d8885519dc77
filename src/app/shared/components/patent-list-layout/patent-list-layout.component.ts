import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseStoreService, MatomoService } from '@core';
import { ViewModeTypeEnum } from '@search/patent/types';
import { Subscription, skip  } from 'rxjs';

@Component({
  selector: 'app-patent-list-layout',
  templateUrl: './patent-list-layout.component.html',
  styleUrls: ['./patent-list-layout.component.scss']
})
export class PatentListLayoutComponent implements OnInit, OnDestroy {
  @Input() storeService: BaseStoreService;
  @Input() documents: any[] = [];
  @Input() isLoading: boolean = false;
  @Input() showDashboardActionBar: boolean = false;
  @Input() showMainTabs: boolean = true;
  @Input() alwaysShowDocumentsControlBar: boolean = false;
  @Input() menuItems: any = [{
    id: 'list_mode',
    icon: 'list-nav-icon',
    title: 'List',
    show: true,
  },{
    id: 'analysis_mode',
    icon: 'visual-analysis-nav-icon',
    title: 'Visual analysis',
    show: true,
  },{
    id: 'combined_mode',
    icon: 'combined-nav-icon',
    title: 'Combined',
    show: true,
  }];

  @Output() viewModeChange: EventEmitter<ViewModeTypeEnum> = new EventEmitter();

  private subscriptions = new Subscription();
  private savedSingleChartColumnForAnalysis: boolean = undefined;

  get viewMode(): ViewModeTypeEnum {
    return this.storeService.patentListViewMode;
  }

  set viewMode(val: ViewModeTypeEnum) {
    this.storeService.patentListViewMode = val;
  }

  get showList(): boolean {
    return this.viewMode === ViewModeTypeEnum.LIST || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get showVisual(): boolean {
    return this.viewMode === ViewModeTypeEnum.ANALYSIS || this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  get isCombinedMode(): boolean {
    return this.viewMode === ViewModeTypeEnum.COMBINED;
  }

  constructor(private route: ActivatedRoute, private router: Router) { }

  ngOnInit() {
    const queryParamMap$ = this.route.queryParamMap?.subscribe({
      next: params => {
        const mode = params.get('view-mode');
        if (mode && mode !== this.storeService.patentListViewMode) {
          this.storeService.patentListViewMode = mode as ViewModeTypeEnum;
        }
      }
    });
    if (queryParamMap$) {
      this.subscriptions.add(queryParamMap$);
    }

    const patentListViewMode$ = this.storeService.patentListViewMode$.pipe(skip(1)).subscribe({
      next: mode => {
        if (this.router?.routerState?.snapshot?.root?.queryParams) {
          let queryParams = {...this.router.routerState.snapshot.root.queryParams};
          if (mode && queryParams['view-mode'] !== mode) {
            queryParams['view-mode'] = mode;
            this.router.navigate([], {queryParams: queryParams});
          }
        }
      }
    });
    this.subscriptions.add(patentListViewMode$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
  onViewModeChanged(mode: ViewModeTypeEnum) {
    if (this.storeService.patentListViewMode === ViewModeTypeEnum.ANALYSIS) {
      this.savedSingleChartColumnForAnalysis = this.storeService.singleChartColumn;
      this.storeService.singleChartColumn = true;
    }

    if (mode === ViewModeTypeEnum.ANALYSIS) {
      this.storeService.singleChartColumn = this.savedSingleChartColumnForAnalysis !== undefined ? this.savedSingleChartColumnForAnalysis : false;
    } else {
      this.storeService.singleChartColumn = false;
    }

    this.storeService.patentListViewMode = mode;
    this.viewModeChange.emit(mode);
  }
}
