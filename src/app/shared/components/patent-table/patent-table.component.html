<div class="publication-table results-table-container" *ngIf="isListVisible" data-intercom-target="results-table">
  <div #patentTable *ngIf="!searchingTable else loaderTable">
    <div class="w-100 overflow-x-hidden overflow-y-auto patent-table-wrapper" [ngClass]="{'radius-big': !patents?.length}">
    <table class="table table-condensed publication-table w-100 table-hover mb-0 mt-0 {{tableClasses}}" >
      <thead class="w-100">
        <tr>
          <th *ngIf="showPatentSelection" width="30" class="position-relative patents-selection" ngbDropdown>
            <ng-container *ngIf="allowSelectAllDocuments else selectOnlyVisibleDocuments">
              <label class="patents-selector m-0 p-0 w-100 text-center cursor-pointer caret-off" [hidden]="!showSelectAll" ngbDropdownAnchor>
                <i class="fa fa-angle-down"></i>
              </label>
              <ul class="w-auto patents-selection-menu mt-0" [hidden]="!showSelectAll" ngbDropdownMenu>
                <li><a class="dropdown-item toggle-all-patents" href="javascript:void(0)" (click)="onSelectAllPatentClicked($event)">{{isAllChecked? 'Deselect' :'Select'}} all</a></li>
                <li><a class="dropdown-item toggle-visible-patents" href="javascript:void(0)" (click)="selectVisiblePatent($event)">{{isVisibleChecked? 'Deselect' :'Select'}} visible</a></li>
                <li *ngIf="!isAllChecked && hasSelectedPatents"><a class="dropdown-item toggle-clear-all-patents" href="javascript:void(0)" (click)="clearAllPatent()">Clear all</a></li>
              </ul>
            </ng-container>
            <ng-template #selectOnlyVisibleDocuments>
              <label class="checkbox m-0 p-0" [class.disabled]="isSelectionFull(patent)">
                <input type="checkbox" (change)="selectVisiblePatent($event)">
                <span class="no-text">&nbsp;</span>
              </label>
            </ng-template>
          </th>
          <th (click)="onHeadClick(null)" width="30"  *ngIf="showIndexColumn && !userService.isFreeUser()">
            <span [ngClass]="{'cursor-pointer': hasSorting}">#</span>
          </th>
          <th *ngIf="storeService.showColumn('citation_type')">
            <span>Type</span>
          </th>
          <th *ngIf="storeService.showColumn('legal_status')" class="text-center position-relative legal-status-selection"
              width="100" style="width: 100px; min-width: 100px; max-width: 100px">
            <ng-container *ngIf="allowFilteringByLegalStatus && patents?.length && allowSelectLegalStatus else legalStatusTemplate">
              <div class="figma-dropdown show-on-hover">
                <div class="figma-dropdown-btn" appDropdownToggle>
                  <i class="fa fa-angle-down"></i>
                  <ng-container *ngTemplateOutlet="legalStatusTemplate"></ng-container>
                </div>
                <div class="figma-dropdown-content figma-dropdown-content-left radius-big p-y-spacing-big p-x-spacing-md">
                  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary">Filter by</div>
                  <div class="">
                    <label class="figma-checkbox figma-dropdown-item figma-dropdown-item-hover content-capitalize content-body-medium" *ngFor="let lsf of legalStatusFilter" [class.active]="!lsf.unchecked" >
                      <input type="checkbox" (click)="selectLegalStatus($event, lsf)" [checked]="!lsf.unchecked">
                      <span class="p-r-spacing-xxx-big">{{lsf.name}}</span>
                    </label>
                  </div>
                </div>
              </div>
            </ng-container>
            <ng-template #legalStatusTemplate>
              <div class="text-ellipsis text-ellipsis-1"
                   [ngbPopover]="popoverLegalStatusTemp" #popoverLegalStatus="ngbPopover"
                   triggers="manual" [autoClose]="'outside'" popoverClass="white-popover" container="body"
                   appTruncatableTooltip [truncatableTooltipPopover]="popoverLegalStatus" truncatableTextCssSelector=".legal-status-title">
                <div class="legal-status-title text-ellipsis text-ellipsis-1">
                  Legal status
                </div>
                <ng-template #popoverLegalStatusTemp>
                  <div class="popover-descriptions">
                    Legal status
                  </div>
                </ng-template>
              </div>
            </ng-template>
          </th>
          <th *ngIf="storeService.showColumn('monitor_label')  && hideColumns.indexOf('monitor_label') === -1"
            data-intercom-target="Monitor method">
            <span>Search method</span>
          </th>
          <th (click)="onHeadClick('title', 'Title')" style="min-width: 280px;" width="auto"
              appTableSortIcon sortColumn="title" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Title
          </th>
          <th (click)="onHeadClick('raw_publication_number', 'Publ. No.')" style="min-width: 100px;"
              appTableSortIcon sortColumn="raw_publication_number" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Publ. No.
          </th>
          <th width="100" *ngIf="storeService.showColumn('citation_focal')">
            <span>Focal</span>
          </th>
          <th width="50" *ngIf="storeService.showColumn('citation_level')">
            <span>Level</span>
          </th>
          <th (click)="onHeadClick('ipc4', 'IPC 4')" style="min-width: 90px;" *ngIf="storeService.showColumn('ipc4')"
              appTableSortIcon sortColumn="ipc4" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            IPC 4
          </th>
          <th (click)="onHeadClick('applicants', 'Applicants')" style="min-width: 130px;" *ngIf="storeService.showColumn('applicants')"
              appTableSortIcon sortColumn="applicants" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Applicants
          </th>
          <th style="min-width: 130px;" *ngIf="storeService.showColumn('owners')">
            Owners
          </th>
          <th (click)="onHeadClick('ultimate_owners', 'Ultimate owners')" style="min-width: 130px;" *ngIf="storeService.showColumn('ultimate_owners')"
              appTableSortIcon sortColumn="ultimate_owners" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Ultimate owners
          </th>
          <th (click)="onHeadClick('priority_date', 'Priority date')" class="" style="min-width: 150px; width: 150px;" *ngIf="storeService.showColumn('priority_date')"
              appTableSortIcon sortColumn="priority_date" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Priority date
          </th>
          <th (click)="onHeadClick('publication_date', 'Publication date')" class="" style="min-width: 150px; width: 150px;" *ngIf="storeService.showColumn('publication_date')"
              appTableSortIcon sortColumn="publication_date" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Publication date
          </th>
          <th (click)="onHeadClick('risk', 'Risk')" class="" style="min-width: 80px;" *ngIf="storeService.showColumn('risk')"
              appTableSortIcon sortColumn="risk" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Risk
          </th>
          <th (click)="onHeadClick('impact', 'Value')" class="" style="min-width: 90px;" *ngIf="storeService.showColumn('impact')"
              appTableSortIcon sortColumn="impact" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
            Value
          </th>
          <th class="" style="min-width: 130px; position: relative;" *ngIf="storeService.showColumn('similarity_index')">
            <div class="d-flex justify-content-center gap-1">
              <div (click)="onHeadClick('similarity_index', 'Similarity')"
                   appTableSortIcon sortColumn="similarity_index" [sortingColumn]="sortField" [sortingOrder]="sortOrder" [sortVisible]="hasSorting">
                Similarity
              </div>
              <app-tooltip id='table-similarity' tooltipTitle='Similarity' [tooltipText]='similarityText' ></app-tooltip>
            </div>
          </th>
          <th class="" *ngIf="storeService.showColumn('collection_source_user')" style="min-width: 130px;width: 130px;">
            <div class="figma-dropdown collection-source-user-filter-by">
              <div ngbDropdown class="d-inline-block" container="body" autoClose="outside" #userDropdown="ngbDropdown" *ngIf="collectionSourceUsers">
                <span ngbDropdownToggle class="cursor-pointer caret-off" [class.p-l-spacing-xxx-big]="getNumberOfSelectedUsers() == 0">
                  <div class="dropdown-icon" *ngIf="getNumberOfSelectedUsers() == 0"></div>
                  <span class="content-label-small number-selected-users content-color-primary m-r-spacing-sm" *ngIf="getNumberOfSelectedUsers() > 0">{{getNumberOfSelectedUsers()}}</span>
                  {{storeService.showedColumnTitle('collection_source_user')}}
                </span>
                <div ngbDropdownMenu class="radius-big p-spacing-none">
                  <div class="content-heading-h6 content-color-tertiary m-spacing-md m-b-spacing-x-s">Select users</div>
                  <div class="m-spacing-sm ">
                    <label *ngFor="let item of collectionSourceUsers | keyvalue" [class.active]="item.value.isSelected" class="figma-checkbox figma-dropdown-item figma-dropdown-item-hover content-capitalize content-body-medium">
                        <input type="checkbox" [checked]="item.value.isSelected" (change)="filterByCollectionSourceUser($event, item.value)" />
                        <app-user-avatar [user]="item.value" [hasSubTitle]="false" size="xsmall" ></app-user-avatar>
                        <span>{{item.value | userTitle }}</span>
                    </label>
                  </div>
                  <div class="dropdown-divider"></div>
                  <div class="content-body-medium text-end m-spacing-md">
                    <span class="button-main-tertiary-grey button-small m-r-spacing-sm" *ngIf="storeService.getAppliedCollectionSourceUserFilter()" (click)="userDropdown.close();onResetUserFilter()">Reset all</span>
                    <span class="button-main-secondary-grey button-small m-r-spacing-sm" (click)="userDropdown.close();onCancelUserFilter()">Cancel</span>
                    <span class="button-main-primary button-small" (click)="userDropdown.close();onApplyUserFilter()">Apply</span>
                  </div>
                </div>
              </div>
              <div *ngIf="!collectionSourceUsers"> {{storeService.showedColumnTitle('collection_source_user')}}</div>
            </div>
          </th>
          <th *ngIf="storeService.showColumn('collection_source') && collectionSourceOption">
              <div class="figma-dropdown collection-source-filter-by">
                <div ngbDropdown class="d-inline-block" container="body" autoClose="outside" #sourceDropdown="ngbDropdown">
                  <span ngbDropdownToggle class="cursor-pointer caret-off" [class.p-r-spacing-xxx-big]="getNumberOfSelectedCollectionSource() === 0">
                    {{storeService.showedColumnTitle('collection_source')}}
                    <div class="dropdown-icon" *ngIf="getNumberOfSelectedCollectionSource() === 0"></div>
                    <span class="content-label-small number-selected-users content-color-primary m-l-spacing-sm" *ngIf="getNumberOfSelectedCollectionSource() > 0">{{getNumberOfSelectedCollectionSource()}}</span>
                  </span>
                  <div ngbDropdownMenu class="radius-big p-spacing-none">
                    <div class="content-heading-h6 content-color-tertiary m-spacing-md m-b-spacing-x-s">Filter by</div>
                    <div class="m-spacing-sm ">
                      <label *ngFor="let s of collectionSourceOption" [class.active]="s.selected" class=" figma-checkbox figma-dropdown-item figma-dropdown-item-hover content-capitalize content-body-medium">
                          <input type="checkbox" [checked]="s.selected" (change)="onFilterByCollectionSource($event, s)" /><span class=" p-r-spacing-xxx-big">{{s.name}}</span>
                      </label>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="content-body-medium text-end m-spacing-md">
                      <span class="button-main-tertiary-grey button-small m-r-spacing-sm" *ngIf="storeService.getAppliedCollectionSourceTypeFilter()" (click)="sourceDropdown.close();onResetSourceFilter()">Reset all</span>
                      <span class="button-main-secondary-grey button-small m-r-spacing-sm" (click)="sourceDropdown.close();onCancelSourceFilter()">Cancel</span>
                      <span class="button-main-primary button-small" (click)="sourceDropdown.close();onApplySourceFilter()">Apply</span>
                    </div>
                  </div>
                </div>
              </div>
          </th>
          <th *ngIf="storeService.showColumn('previous_legal_status')">
            <span>{{storeService.showedColumnTitle('previous_legal_status')}}</span>
          </th>
          <th *ngIf="storeService.showColumn('new_legal_status')">
            <span>{{storeService.showedColumnTitle('new_legal_status')}}</span>
          </th>
          <th *ngIf="storeService.showColumn('legal_status_reason')"  style="min-width: 150px;">
            <span>{{storeService.showedColumnTitle('legal_status_reason')}}</span>
          </th>
          <th *ngIf="storeService.showColumn('landscape_status')">
            <span>Source</span>
          </th>
          <th *ngIf="storeService.showColumn('monitor_feedback')  && hideColumns.indexOf('monitor_feedback') === -1">
            <span>Training</span>
          </th>
          <th *ngIf="storeService.showColumn('last_read')">Last opened on</th>
          <th *ngIf="storeService.showColumn('mark_as_unread')" class="empty-header"></th>
          <th *ngIf="storeService.showColumn('ratings')" style="min-width: 230px;">
            <div *ngIf="canSortByRatings" class="figma-dropdown collection-source-filter-by">
              <div ngbDropdown class="d-inline-block" container="body" #sortRatingsDropdown="ngbDropdown">
                <span ngbDropdownToggle class="cursor-pointer caret-off"
                      appTableSortIcon [sortColumns]="sortRatingsFields" [sortingColumn]="sortField" [sortVisible]="hasSorting">
                  Ratings
                </span>
                <div ngbDropdownMenu class="radius-big p-spacing-none" [style.min-width.rem]="15">
                  <div class="p-x-spacing-md p-t-spacing-md p-b-spacing-x-s content-heading-h6 content-color-tertiary">Sort by</div>
                  <div class="p-spacing-sm d-flex flex-column">
                    <div *ngFor="let op of sortRatingsOptions"
                         (click)="sortRatingsDropdown.close(); onSortRatingsClicked(op);"
                         [ngClass]="{'active content-style-semi-bold': sortField === getSortRatingsOptionName(op)}"
                         class="p-y-spacing-sm p-x-spacing-md figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium">
                      {{ op }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <ng-container *ngIf="!canSortByRatings">Ratings</ng-container>
          </th>
          <th class="empty-header">
            <div (click)="openAll()" class="m-0 p-0 cursor-pointer text-end" *ngIf="showOpenPatentIcon">
            </div>
          </th>
        </tr>
      </thead>
      <tbody [id]="!patent.general?.obfuscated ? patent.general?.docdb_family_id : 'xxx'"  *ngFor="let patent of patents | paginate:{
        itemsPerPage: pagination?.page_size,
        id: idPagination,
        currentPage: pagination?.current_page,
        totalItems: pagination?.total_hits
      }; index as indexi" [ngClass]="{
          'preview-border': isOpened(patent) && (indexi === 0 || !isOpened(patents[indexi-1])),
          'preview-border-vertical': isOpened(patent) && indexi > 0 && isOpened(patents[indexi-1])
        }" class="patent-table-row">
        <tr [ngClass]="{
            'detailed': isOpened(patent),
            'obfuscated':patent.general?.obfuscated,
            'patent-shared': patent?.users?.length > 0  || patent?.groups?.length > 0
          }"
            [class.selected-row]="isSelected(patent)"
            [class.no-border-bottom]="!!patent?.legal_status_changes"
            [attr.data-intercom-target]="indexi === 0 ? 'Similar patent' : null">
          <td *ngIf="showPatentSelection" (click)="openDetail(patent)">
            <label class="checkbox m-0 p-0" [class.disabled]="isSelectionFull(patent)" (click)="$event.stopImmediatePropagation();">
              <input type="checkbox" (change)="selectPatent($event, patent, indexi )"
                     [checked]="isSelected(patent)" *ngIf="!patent.general?.obfuscated"
                     data-intercom-target="Checkbox"
                     (click)="$event.stopImmediatePropagation();">
              <span class="no-text">&nbsp;</span>
            </label>
          </td>
          <td class="number-col cursor-default" nowrap="nowrap" (click)="openDetail(patent)" *ngIf="showIndexColumn && !userService.isFreeUser()">
            {{ showRank && patent?.general?.rank ? patent?.general?.rank : ( pagination | countSerial:indexi) }}
          </td>
          <td *ngIf="storeService.showColumn('citation_type')"
              (click)="openDetail(patent)">
            {{patent?.citations[0].direction}}
          </td>
          <td *ngIf="storeService.showColumn('legal_status')" class="" (click)="openDetail(patent)"
              width="100" style="width: 100px; min-width: 100px; max-width: 100px">
            <div class="d-flex flex-column justify-content-center align-items-center gap-spacing-x-s">
              <ng-container *ngFor="let ls of legalStatus[getDocumentNumber(patent)]"
                            [ngTemplateOutlet]="legalStatusTemplate"
                            [ngTemplateOutletContext]="{name: ls.name, tooltip: ls.tooltip}">
              </ng-container>
            </div>
          </td>
          <td *ngIf="storeService.showColumn('monitor_label')  && hideColumns.indexOf('monitor_label') === -1">
            <div *ngIf="checkProfile(patent,'MACHINE_LEARNING')" tooltipClass="white-tooltip"
                 [ngbTooltip]="checkProfile(patent,'MACHINE_LEARNING') ? monitorLabelTooltip : 'Deep learning Search'">Deep learning</div>
            <div *ngIf="checkProfile(patent,'SEMANTIC')"  tooltipClass="white-tooltip" [ngbTooltip]="checkProfile(patent,'SEMANTIC') ? monitorLabelTooltip : 'Semantic search'">Semantic</div>
            <div *ngIf="checkProfile(patent,'BOOLEAN')"  tooltipClass="white-tooltip" [ngbTooltip]="checkProfile(patent,'BOOLEAN') ? monitorLabelTooltip : 'Boolean search'">Boolean</div>
          </td>
          <td class="patent-title tag-selector-hover" (click)="openDetail(patent)" [attr.data-intercom-target]="indexi === 0 ? 'first-result' : null">
            <div class="annotation-status d-flex align-items-baseline justify-content-start float-start pe-2"
              *ngIf="canAnnotate && showAnnotatedStatus && (patent?.annotations?.labels || patent?.annotations?.comments)">
              <a [routerLink]="getViewPath(patent)" (click)="$event.stopPropagation()"
                [state]="{ data: linkData}" [queryParams]="queryParamsForAnnotateLink(patent, patentSideBarViewModeEnum.MODE_HIGHLIGHTS)"
                ngbTooltip="This document contains highlights" tooltipClass="white-tooltip"
                *ngIf="patent?.annotations?.labels">
                <span class="me-1"><i class="fa-regular fa-highlighter"></i></span>
              </a>
              <a [routerLink]="getViewPath(patent)" (click)="$event.stopPropagation()"
                [state]="{ data: linkData}" [queryParams]="queryParamsForAnnotateLink(patent, patentSideBarViewModeEnum.MODE_COMMENTS)"
                ngbTooltip="This document contains comments" tooltipClass="white-tooltip"
                *ngIf="patent?.annotations?.comments">
                <span class="me-1"><i class="fa-regular fa-message"></i></span>
              </a>
            </div>

            <div class="flex-fill">
              <span class="cursor-default" [innerHtml]="getPatentTitle(patent)"></span>
              <div *ngIf="userService.hasTagFeature()"
                   #containerTags class="flex-fill d-flex align-items-center">
                <app-tags-display [patent]="patent" [container]="containerTags"
                                  [canManageTags]="userService.isNotExternalUser()"
                                  [showIcon]="false" [updateTagsOnResize]="true"
                                  [storeService]="storeService"
                                  [collapsedDisplayRows]="1"
                                  [expandedDisplayRows]="3"
                                  (tagsChange)="onPatentTagsChanged($event, patent)"
                                  (click)="$event.stopPropagation()"
                                  (tagsDisplayHidden)="shownTagsDisplayDocId = null"
                                  (tagsDisplayShown)="shownTagsDisplayDocId = patent.general.docdb_family_id"
                                  [ngClass]="{'invisible': shownTagsDisplayDocId !== patent.general.docdb_family_id && !hasTags(patent)}"
                                  [addTagButtonCssClass]="'tag-item tag-item-action plus-button-tag'">
                </app-tags-display>
              </div>
            </div>
          </td>
          <td class="publication-number-col" nowrap="nowrap" [ngStyle]="{'font-style': patent.last_read ? 'italic' : 'normal'}"  (click)="openDetail(patent)">
            <a *ngIf="!patent.general?.obfuscated"
               (click)="onClickPatentViewer($event, patent, indexi, 'openSameTab');" class="cursor-pointer">
              <i [ngClass]="getFlagIcon(getPublicationNumberToDisplay(patent))"></i>
              {{ getPublicationNumberToDisplay(patent) }}
            </a>
          </td>
          <td *ngIf="storeService.showColumn('citation_focal')" (click)="openDetail(patent)">
            {{ patentTableService.getFocal(patent) }}
          </td>
          <td *ngIf="storeService.showColumn('citation_level')" (click)="openDetail(patent)">
            {{ patentTableService.getLevel(patent) }}
          </td>
          <td *ngIf="storeService.showColumn('ipc4')" (click)="openDetail(patent)" class="ip4-col">
            <span *ngFor="let ipc4 of patent?.bibliographic?.ipc4" class="d-block cursor-default" [ngbTooltip]="ipcDescriptionTooltip" tooltipClass="bibliographic-tooltip">
            <ng-template #ipcDescriptionTooltip>
              <div [innerHTML]="getIpcDescription(ipc4)" class="text-start"></div>
            </ng-template>
            {{ ipc4 }}
            </span>
          </td>
          <td *ngIf="storeService.showColumn('applicants')" (click)="openDetail(patent)" class="cursor-default">
            {{ patentTableService.getApplicants(patent) }}
          </td>
          <td *ngIf="storeService.showColumn('owners')" (click)="openDetail(patent)" class="cursor-default">
            {{ patentTableService.getOwners(patent) }}
          </td>
          <td *ngIf="storeService.showColumn('ultimate_owners')" (click)="openDetail(patent)" class="cursor-default">
            {{ patentTableService.getUltimateOwners(patent) }}
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('priority_date')" (click)="openDetail(patent)">
            {{ patent.bibliographic.priority_date | dateFormat: 'ShortDate' }}
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('publication_date')" (click)="openDetail(patent)">
            {{ patent.bibliographic.publication_date | dateFormat: 'ShortDate' }}
          </td>
          <td class="text-start cursor-default" *ngIf="storeService.showColumn('risk')" (click)="openDetail(patent)">
            {{ getAnalyticsRisk(patent) }}
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('impact')" (click)="openDetail(patent)">
            {{ getAnalyticsValue(patent) }}
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('similarity_index')" (click)="openDetail(patent)">
            {{ patent?.general?.similarity_index }}
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('collection_source_user')">
            <span *ngIf="!patent.collection_source || patent.collection_source.length === 0">Unknown</span>
            <div *ngIf="patent?.collection_source?.length>0">
              <app-user-avatars [users]="getSourceUsers(patent.collection_source)" [numberDisplayedUsers]="5"
                  [distanceBetweenAvatars]="10" avatarsTooltipPrefix="Added by"></app-user-avatars>
            </div>
          </td>
          <td class="cursor-default" *ngIf="storeService.showColumn('collection_source') && collectionSourceOption">
            <span class="tag-label-{{patent.collection_source?.length > 1 ? 'multiple' : getCollectionSourceTitle(patent.collection_source)}} source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize"
                  appSelectableTooltip [selectableTooltipPopover]="pColSources">
              <div [ngbPopover]="pColSourcesTemp" triggers="manual" #pColSources="ngbPopover" container="body"
                   popoverClass="white-popover collection-sources-popover" [autoClose]="'outside'">
                {{getCollectionSourceTitle(patent.collection_source)}}
              </div>
              <ng-template #pColSourcesTemp>
                <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon"></a>
                <div *ngIf="patent.collection_source?.length">
                  <div class="popover-title">{{'Source' | pluralize: patent.collection_source?.length }}</div>
                  <div class="popover-caption">This patent has been added from the following {{'source' | pluralize: patent.collection_source?.length }}. Click on {{patent.collection_source?.length===1? 'it':'any'}} to reload it.</div>
                  <div class="popover-divider m-b-spacing-md"></div>
                  <div class="sources-container">
                    <ng-container *ngFor="let s of patent.collection_source; let last = last">
                      <div class="popover-descriptions" [class.cursor-pointer]="allowOpenNewTab(getCollectionSourceTitle([s])) && canLoadSource(s)" (click)="onLoadSource($event, s)">
                        <div class="d-flex justify-content-between align-items-center">
                          <div class="tag-label-{{getCollectionSourceTitle([s])}} source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize"
                               [class.cursor-pointer]="allowOpenNewTab(getCollectionSourceTitle([s])) && canLoadSource(s)">
                            {{getCollectionSourceTitle([s])}}
                          </div>
                          <span class="cursor-pointer button-main-tertiary-grey p-x-spacing-sm"
                                (click)="onLoadSource($event, s, true)"
                                *ngIf="canLoadSource(s) && allowOpenNewTab(getCollectionSourceTitle([s]))">
                              <i class="fa-regular fa-up-right-from-square content-color-primary"></i>
                          </span>
                        </div>
                        <div class="source-query-added">Added: {{s.created_at | dateFormat: 'ShortDate'}}</div>
                        <div class="source-query" *ngIf="getSourceQuery(s)" [innerHTML]="getSourceQuery(s)"></div>
                      </div>
                      <div class="popover-divider m-y-spacing-md" *ngIf="!last"></div>
                    </ng-container>
                  </div>
                </div>
                <div *ngIf="!patent.collection_source?.length">
                  <div class="popover-caption">No information is recorded for the source of this patent.</div>
                </div>
              </ng-template>
            </span>
          </td>
          <td *ngIf="storeService.showColumn('previous_legal_status')">
            <ng-container *ngIf="getPreviousLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent))" [ngTemplateOutlet]="legalStatusTemplate"
                          [ngTemplateOutletContext]="{name: getPreviousLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent)).name, tooltip: getPreviousLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent)).tooltip}">
            </ng-container>
          </td>
          <td *ngIf="storeService.showColumn('new_legal_status')" >
            <ng-container *ngIf="getCurrentLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent))" [ngTemplateOutlet]="legalStatusTemplate"
                          [ngTemplateOutletContext]="{name: getCurrentLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent)).name, tooltip: getCurrentLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent)).tooltip}">
            </ng-container>
          </td>
          <td *ngIf="storeService.showColumn('legal_status_reason')">
            <span *ngIf="getCurrentLegalStatusIcon(patent, patentTableService.getPublicationNumber(patent))"
                  [innerHTML]="getCurrentLegalStatusReason(patent, patentTableService.getPublicationNumber(patent))">
            </span>
          </td>
          <td *ngIf="storeService.showColumn('landscape_status')">
            <span class="badge badge-primary cursor-pointer " (click)="onFilterRows({'source':patent?.landscape?.source})"
                  [ngClass]="patent?.landscape?.status === 'ACTIVE' ? 'badge--blue' : 'badge--gray'">
              {{patent?.landscape?.source.replace('_',' ')}}
            </span>
          </td>
          <td *ngIf="storeService.showColumn('monitor_feedback') && hideColumns.indexOf('monitor_feedback') === -1"
            class="text-nowrap pt-4" data-intercom-target="Feedback">
            <i class="fas fa-spinner fa-spin fa-spin-like"
               *ngIf="sendingFeedback[patent.general.docdb_family_id.toString()] === mlMatchTypeEnum.POSITIVE"></i>
            <i class="fas fa-thumbs-up btn-like" ngbTooltip="Like" tooltipClass="white-tooltip"
               *ngIf="sendingFeedback[patent.general.docdb_family_id.toString()] !== mlMatchTypeEnum.POSITIVE"
               [ngClass]="patent?.machine_learning_feedback?.match == mlMatchTypeEnum.POSITIVE ? 'active' : ''"
               (click)="updateFeedbackTraining(mlMatchTypeEnum.POSITIVE, patent)"></i>
            <i class="fas fa-thumbs-down fa-flip-horizontal btn-unlike" ngbTooltip="Dislike" tooltipClass="white-tooltip"
               *ngIf="sendingFeedback[patent.general.docdb_family_id.toString()] !== mlMatchTypeEnum.NEGATIVE"
               (click)="updateFeedbackTraining(mlMatchTypeEnum.NEGATIVE, patent)"
               [ngClass]="patent?.machine_learning_feedback?.match == mlMatchTypeEnum.NEGATIVE ? 'active' : ''"></i>
            <i class="fas fa-spinner fa-spin fa-spin-unlike"
               *ngIf="sendingFeedback[patent.general.docdb_family_id.toString()] === mlMatchTypeEnum.NEGATIVE"></i>
          </td>
          <td *ngIf="storeService.showColumn('last_read')">{{patent.last_read | dateFormat: 'ShortDateTime'}}</td>
          <td *ngIf="storeService.showColumn('mark_as_unread')" class="">
            <a ngbTooltip="Mark as unread" tooltipClass="white-tooltip" class="p-x-spacing-sm button-main-secondary-grey button-medium button-square" href="javascript:void(0)" (click)="onMarkAsUnread(patent)">
              <i class="fa-regular fa-fw fa-trash-alt"></i>
            </a>
          </td>
          <td *ngIf="storeService.showColumn('ratings')" (click)="openDetail(patent)">
            <app-task-stats [patent]="patent" [taskStats]="patent.taskStats" [storeService]="storeService"
                            [ratingFormPopper]="ratingFormPopper" [ratingAnswerPopper]="ratingAnswerPopper"
                            (rateMyselfEvent)="onRateMyselfEvent($event)"
                            (createTaskEvent)="onCreateTaskEvent($event)">
            </app-task-stats>
          </td>
          <td class="">
            <div *ngIf="!patent.general?.obfuscated" class="d-flex align-items-center justify-content-center gap-spacing-sm">
              <a class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square d-flex justify-content-center align-items-center"
                 [attr.data-intercom-target]="indexi === 0 ? 'open-patent-viewer' : null" ngbTooltip="Open patent" tooltipClass="white-tooltip"
                 (click)="onClickPatentViewer($event, patent, indexi, 'openSameTab')">
                <i class="fa-regular fa-file-magnifying-glass"></i>
              </a>
              <a class="patent-icon view-patent-icon button-main-secondary-grey button-medium button-square d-flex justify-content-center align-items-center"
                 data-intercom-target="Open patent viewer" ngbTooltip="Open in a new tab" tooltipClass="white-tooltip"
                 (click)="onClickPatentViewer($event, patent, indexi, 'openNewTab')">
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
              </a>
            </div>
          </td>
        </tr>
        <ng-container *ngIf="!!patent?.legal_status_changes">
          <tr class="legal-status-changes" *ngFor="let entity of getOtherLegalStatusChanges(patent); index as i; last as isLast"
              [class.no-border-bottom]="!isLast" [class.patent-shared]="patent?.users?.length > 0  || patent?.groups?.length > 0"
              [class.detailed-sides]="isOpened(patent)" (click)="openDetail(patent)">
            <td *ngIf="showPatentSelection"></td>
            <td *ngIf="showIndexColumn && !userService.isFreeUser()"></td>
            <td *ngIf="storeService.showColumn('citation_type')"></td>
            <td *ngIf="storeService.showColumn('legal_status')"></td>
            <td *ngIf="storeService.showColumn('monitor_label')  && hideColumns.indexOf('monitor_label') === -1"></td>
            <td class="line-anchor" [class.last-row]="isLast"></td>
            <td class="align-middle text-center">
              <a *ngIf="!patent.general?.obfuscated"
                 (click)="onClickPatentViewer($event, patent, indexi, 'openSameTab')" class="cursor-pointer">
                {{ entity.publication_number }}
              </a>
            </td>
            <td *ngIf="storeService.showColumn('citation_focal')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('citation_level')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('ipc4')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('applicants')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('owners')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('ultimate_owners')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('priority_date')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('publication_date')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('risk')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('impact')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('similarity_index')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('collection_source_user')" class="line-straight"></td>
            <td *ngIf="storeService.showColumn('collection_source') && collectionSourceOption" class="line-straight"></td>
            <td class="align-middle" *ngIf="storeService.showColumn('previous_legal_status')">
              <ng-container [ngTemplateOutlet]="legalStatusTemplate"
                            [ngTemplateOutletContext]="{name: getPreviousLegalStatusIcon(patent, entity.publication_number).name, tooltip: getPreviousLegalStatusIcon(patent, entity.publication_number).tooltip}">
              </ng-container>
            </td>
            <td class="align-middle" *ngIf="storeService.showColumn('new_legal_status')">
              <ng-container [ngTemplateOutlet]="legalStatusTemplate"
                            [ngTemplateOutletContext]="{name: getCurrentLegalStatusIcon(patent, entity.publication_number).name, tooltip: getCurrentLegalStatusIcon(patent, entity.publication_number).tooltip}">
              </ng-container>
            </td>
            <td *ngIf="storeService.showColumn('legal_status_reason')">
              <span [innerHTML]="getCurrentLegalStatusReason(patent, entity.publication_number)"></span>
            </td>
            <td *ngIf="storeService.showColumn('landscape_status')"></td>
            <td *ngIf="storeService.showColumn('monitor_feedback')"></td>
            <td *ngIf="storeService.showColumn('last_read')"></td>
            <td *ngIf="storeService.showColumn('mark_as_unread')"></td>
            <td *ngIf="storeService.showColumn('ratings')"></td>
            <td></td>
          </tr>
        </ng-container>
        <app-patent-detail *ngIf="isOpened(patent) && !patent.general?.obfuscated"
                           [patent]="patent"
                           [colspan]="(storeService.selectedColumnsToShow.length + 5 + (canAnnotate ? 1 : 0) + (showPatentSelection? 1 : 0)) - hideColumns.length"
                           [ngClass]="'patent-detail-'+indexi"
                           [linkData]="linkData"
                           [hasLinksToBooleanSearch]="hasLinksToBooleanSearch"
                           [showQueriedPublicationNumber]="showQueriedPublicationNumber"
                           [showAnalytics]="showAnalytics"
                           [openAnnotationNewTab]="openAnnotationNewTab"
                           [searchHash]="searchHash"
                           [backButtonTitle]="backButtonTitle"
                           [showAddToSearchButton]="showAddToSearchButton"
                           [storeService]="storeService"
                           [showHighlight]="showHighlight && isOpened(patent)"
                           [showHighlightSearchHash]="showHighlightSearchHash"
                           class="d-table-row patent-detail-container">
        </app-patent-detail>
      </tbody>
    </table>
    </div>
    <div #patentTableScroller class="table-scroller scrollbar-2024 p-x-spacing-xx-big p-t-spacing-sm p-b-spacing-md" [hidden]="patents?.length === 0">
      <div class="content-color-tertiary content-body-xsmall">{{patents?.length}} rows</div>
      <div class="table-scrollbar-container" (scroll)="onTableScroll($event)">
        <div class="table-scrollbar"></div>
      </div>
    </div>
  </div>

  <ng-template #loaderTable>
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
  </ng-template>

  <ng-template #legalStatusTemplate let-name="name" let-tooltip=tooltip>
    <div class="d-flex justify-content-center badge-{{name | lowercase}}"
         [ngbTooltip]="tooltip" tooltipClass="white-tooltip" container="body">
      {{ name | titlecase }}
    </div>
  </ng-template>
</div>

<app-popper #ratingFormPopper
            placement="top" [showArrow]="false" [resizePopperWidth]="false"
            customClass="rating-form-popper p-spacing-none"
            [allowedClickClasses]="['rating-form-popper', 'ngb-dp-body']">
  <app-patent-rating-form *ngIf="ratingFormPopper.isOpen"
                          [storeService]="storeService"
                          [showCloseButton]="false"
                          [inlineMode]="true"
                          [refreshTaskAfterDone]="true"
                          [task]="createRequestParams.task"
                          [patentFamilyId]="createRequestParams.patentFamilyId"
                          (hideRatingFormEvent)="onTaskCreatedEvent($event)">
  </app-patent-rating-form>
</app-popper>

<app-popper #ratingAnswerPopper
            placement="top" [showArrow]="false" [resizePopperWidth]="false"
            customClass="rating-answer-popper p-spacing-none"
            [allowedClickClasses]="['rating-answer-popper']">
  <app-patent-request-answer *ngIf="ratingAnswerPopper.isOpen"
                             [showCloseButton]="false"
                             [inlineMode]="true"
                             [refreshTaskAfterDone]="true"
                             [task]="rateMySelfParams.task"
                             (hideRequestAnswerEvent)="onRateMyselfDoneEvent($event)">
  </app-patent-request-answer>
</app-popper>
