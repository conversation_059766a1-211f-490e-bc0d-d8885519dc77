@import 'scss/layout2021/variables';
@import 'scss/components/publication-table';
@import 'scss/components/highlight';

.publication-table {
  .btn-like,
  .btn-unlike {
    color: #999999;
    cursor: pointer;

    &.active,
    &:focus,
    &:hover,
    &:active {
      color: $light-green-color;
    }
  }

  .fa-spin-like,
  .btn-like {
    margin-right: 10px;
  }

  .short-char {
    margin-bottom: 15px;
    width: 120px;
    min-width: 120px;

    span {
      background: $light-green-color;
      -webkit-border-radius: 3px;
      border-radius: 3px;
      font-size: 16px;
      text-transform: uppercase;
      color: #fff;
      padding: 0px 11px;
      line-height: 1;
      margin: 0px 1px;
      margin-bottom: 5px;

      &.inactive {
        background: rgba(177, 177, 177, 0.2);
      }
    }
  }
  .dropdown-item {
    color: #01526F !important
  }
  .patents-selection{
    &:hover .patents-selection-menu {
      display: block;
    }
  }
  .legal-status-selection{
    .figma-dropdown-content {
      min-width: 160px;
      text-align: left;
    }
  }

  .tag-selector-hover {
    &:hover {
      app-tags-display {
        visibility: visible !important;
      }
    }
  }
}
:host::ng-deep{
  .monitor-label-tooltip {
    .tooltip-inner {
      max-width: 450px !important;
    }
  }

  .opened-patent {
    td {
      background-color: #F8F8F8;
    }
  }

  tr{
    app-tags-display{
      .tags-container{
        .plus-button-tag{
          visibility: hidden !important;
        }
      }
    }

    &:hover{
      app-tags-display{
        .tags-container{
          .plus-button-tag{
            visibility: visible !important;
          }
        }
      }
    }
  }
}

.patent-shared{
  background: #FF67001A 0% 0% no-repeat padding-box;
}

tr {
  &.no-border-bottom {
    &>td {
      border-bottom: none !important;
    }
  }

  &.legal-status-changes {
   td{
    &:not(:first-child):not(:last-child){
      position: relative;
    }
    &.line-anchor::before {
      content: '';
      height: 100%;
      border-left: solid 1px $input-border-color;
      position: absolute;
      left: 50%;
      top: 0;
    }
    &.line-anchor.last-row::before{
      height: 48%;
    }
    &.line-anchor::after {
      content: '';
      width: 50%;
      border-bottom: solid 1px $input-border-color;
      position: absolute;
      top: 48%;
      right: 0;
    }
    &.line-straight::after{
      content: '';
      width: 100%;
      border-bottom: solid 1px $input-border-color;
      position: absolute;
      left: 0;
      top: 48%;
    }
   }
  }
  &.selected-row{
    td{
      background: $colour-blue-brand-100 !important;
    }
  }
}
.collection-source-user-filter-by{
  .dropdown-icon{
    right: unset;
    left: 2px;
  }
}

.number-selected-users {
  display: inline-block;
  border-radius: 20px;
  background: #C4E6E2;
  width: 20px;
  height: 20px;
  text-align: center;
}

::ng-deep {
  .rating-answer-popper {
    z-index: 9999;

    app-patent-request-answer {
      display: block;
      width: 20rem;
    }
  }

  .rating-form-popper {
    z-index: 9999;

    app-patent-rating-form {
      display: block;
      width: 20rem;
    }
  }

  .patent-table-row:hover {
    tr:not(.detailed-sides):not(.detailed) > * {
      --#{$prefix}table-accent-bg: var(--bs-table-hover-bg);
    }
  }

  .legal-status-changes.detailed-sides:hover > * {
    --bs-table-bg-state: white !important;
  }
}
