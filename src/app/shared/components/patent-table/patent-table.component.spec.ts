import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PatentTableComponent } from './patent-table.component';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { HighchartsChartModule } from 'highcharts-angular';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentTableComponent', () => {
  let component: PatentTableComponent;
  let fixture: ComponentFixture<PatentTableComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [],
      imports: [
        SharedModule,
        HighchartsChartModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        SemanticSearchStoreService, provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PatentTableComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
