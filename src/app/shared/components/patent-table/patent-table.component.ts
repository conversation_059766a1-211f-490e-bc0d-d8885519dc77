import { get } from 'lodash';
import {
  AfterContentChecked,
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild,
  viewChild
} from '@angular/core';
import {
  AvatarService,
  BaseMonitorStoreService,
  BaseStoreService,
  CitationSearchStoreService,
  CollectionStoreService,
  CountSerialPipe,
  LandscapeStoreService,
  MachineLearningFeedback,
  MatomoService,
  MonitorService,
  MonitorStoreService,
  Patent,
  PatentNumberService,
  PatentService,
  PatentTableService,
  RatingService,
  SortParams,
  SourceFilterOption,
  TagService,
  TaskModel,
  TaskService,
  TeamUser,
  UserService
} from 'app/core';
import { PaginationMetadata } from 'app/core/services/semantic-search/types';
import { ActivatedRoute, Router } from '@angular/router';
import { debounceTime, filter, Subscription } from 'rxjs';
import { MlMatchTypeEnum, MlSourceTypeEnum, MlStoreDatasetParam } from '@core/services';
import { finalize, mergeMap, skip, take } from 'rxjs/operators';
import { TagModel } from '@core/models/tag.model';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { PatentSideBarViewModeEnum } from '@core/services/patent/types';
import { ViewModeTypeEnum } from '@search/patent/types';
import { CreateTaskEventParams, RateMyselfEventParams } from '../task-stats/types';
import { PopperComponent } from '@shared/components/popper/popper.component';
import { ShowRatingsEventParams } from '@patent/patent-ratings/shared/types';


export enum SortRatingsOptionEnum {
  RATINGS_RECENTLY_ADDED = 'Recently added',
  RATINGS_OLDEST_ADDED = 'Oldest added',
}

@Component({
  selector: 'app-patent-table',
  templateUrl: './patent-table.component.html',
  styleUrls: ['./patent-table.component.scss']
})
export class PatentTableComponent implements OnInit, AfterViewInit, OnDestroy, AfterContentChecked {
  @Input() pagination: PaginationMetadata;
  @Input() showAnnotatedStatus = true;
  @Input() showOpenPatentIcon = true;
  @Input() idPagination = 'search-pagination';
  /**
   * whether to show the row selection checkbox
   */
  @Input() showPatentSelection = true;
  /**
   * whether to show select all checkbox on patent table header
   */
  @Input() showSelectAll = true;
  /**
   * limit patent selection on patent table if provided by parent component
   */
  @Input() selectionLimit: number;
  /**
   * Store patent selection on secondary list if provided by parent component
   */
  @Input() secondaryPatentSelection?: Array<string>;
  /**
   * hide specified columns form display. (helpful for monitor)
   */
  @Input() hideColumns: Array<string> = [];
  /**
   * reference for searchHash. Useful in analytic radar chart in detail section
   */
  @Input() searchHash?: string;
  /**
   * reference to show analytic chart option in patent detail subsection
   */
  @Input() showAnalytics = true;
  @Input() hasLinksToBooleanSearch = false;
  @Input() preSelected: string;
  @Input() preSelectedOn: string;
  @Input() openAnnotationNewTab = false;
  @Input() showQueriedPublicationNumber = false;
  @Input() backButtonTitle: string;
  @Input() hasSorting = true;
  @Input() showPatentUsers?: boolean;
  @Input() searchService?: MonitorService;
  @Input() showAddToSearchButton = false;
  @Input() pathUrl: string;
  @Input() linkData: Object;
  @Input() monitorLabelTooltip: string;
  @Input() currentTask: TaskModel = null;
  @Input() srcMonitorRunId: number;
  @Input() allowSelectLegalStatus = true;
  @Input() storeService: BaseStoreService | BaseMonitorStoreService;
  @Input() allowSelectAllDocuments = true;
  @Input() showIndexColumn = true;
  @Input() allowFilteringByLegalStatus = true;
  @Input() showSmartHighlight = false;
  @Input() showSmartHighlightSearchHash: string = null;
  @Input() showHighlight = false;
  @Input() showHighlightSearchHash: string = null;
  @Input() sideBarViewMode: PatentSideBarViewModeEnum = PatentSideBarViewModeEnum.MODE_CONTENT;
  @Input() showRank = true;
  @Input() collectionSourceOption?: SourceFilterOption[];
  @Input() collectionSourceUsers?: { [k: string]: TeamUser; };
  @Input() tableClasses: string = '';
  @Input() canSortByRatings: boolean = true;

  /**
   * secondary isPublication scope for patent table view rendering (useful in publication based monitor profile deep learning view input option)
   */
  @Input() secondaryIsPublicationSource?: boolean;

  @Output() sort: EventEmitter<SortParams> = new EventEmitter();
  /**
   * event emitter for single patent row checkbox event
   */
  @Output() patentChecked: EventEmitter<{ state: boolean, patent: Patent }> = new EventEmitter();
  /**
   * event emitter for check all Event on patent table
   */
  @Output() checkedAll: EventEmitter<{ state: boolean, patents: Array<Patent>, allPatents?: any }> = new EventEmitter();
  /**
   * event emitter for filter rows
   */
  @Output() filterRows: EventEmitter<Object> = new EventEmitter();
  /**
   * event emitter for error
   */
  @Output() errorMessage: EventEmitter<string> = new EventEmitter();
  /**
   * event emitter for select legal status
   */
  @Output() legalStatusSelected: EventEmitter<string> = new EventEmitter();
  /**
   * event emitter for opened document
   */
  @Output() markAsUnread: EventEmitter<number> = new EventEmitter();
  /**
   * event emitter for loading a specific collection search history source
   */
  @Output() loadSearchSource: EventEmitter<any> = new EventEmitter();
  /**
   * event emitter for loading a specific collection monitor run source
   */
  @Output() loadMonitorSource: EventEmitter<any> = new EventEmitter();

  /**
   * event emitter for feedback click
   */
  @Output() feedbackClick: EventEmitter<any> = new EventEmitter();

  selectedPublications = [];
  openedPatent = [];
  searchingTable = false;
  queryParams: string;
  similarityText = `<p>Similarity reflects statistical and semantic similarity between documents.</p>
                    <p>The index range is centered between a minimum of 0 and a maximum of 1000.</p>
                    <p>Independent of the underlying technology patents with a similarity of less than 100 will not be returned. </p>
                    <p>A similarity value of 1.000 means the text is not only semantically but also lexically identical. </p>
                    <p>Due to our complex algorithms the value of the index cannot be interpreted in a strictly linear manner (e.g. a
                       patent pair with a value of 400 is not a third more similar than a patent pair with a value of 300). </p>`;
  /**
   * reference for Monitor DL column
   */
  mlMatchTypeEnum = MlMatchTypeEnum;
  sendingFeedback = {};
  /**
   * reference for annotation patent view mode.
   */
  numberDisplayedUsers = 2;
  numberDisplayedGroups = 2;

  isLoadingTags: boolean = true;
  tags: TagModel[] = [];
  showTags: boolean;

  sortField = '';
  sortOrder: 'asc' | 'desc';

  shownTagsDisplayDocId: string = null;
  legalStatus = {};

  patentTable = viewChild<ElementRef<HTMLElement>>('patentTable');
  patentTableScroller = viewChild<ElementRef<HTMLElement>>('patentTableScroller');

  patentSideBarViewModeEnum = PatentSideBarViewModeEnum;

  rateMySelfParams: RateMyselfEventParams = {} as RateMyselfEventParams;
  createRequestParams: CreateTaskEventParams = {} as CreateTaskEventParams;

  sortRatingsOptions: SortRatingsOptionEnum[] = [
    SortRatingsOptionEnum.RATINGS_RECENTLY_ADDED, SortRatingsOptionEnum.RATINGS_OLDEST_ADDED
  ];
  sortRatingsFields: string[] = Object.keys(SortRatingsOptionEnum).map(val => val.toLowerCase());

  @ViewChild('ratingFormPopper') ratingFormPopper: PopperComponent;
  @ViewChild('ratingAnswerPopper') ratingAnswerPopper: PopperComponent;

  private subscriptions = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    public patentTableService: PatentTableService,
    private patentNumberService: PatentNumberService,
    private patentService: PatentService,
    public userService: UserService,
    public avatarService: AvatarService,
    public taskService: TaskService,
    private matomoService: MatomoService,
    private tagService: TagService,
    private renderer: Renderer2,
    private ratingService: RatingService,
    private countSerialPipe: CountSerialPipe
  ) {
  }

  private _patents: Array<Patent> = [];

  get patents(): Array<Patent> {
    return this._patents;
  }

  @Input() set patents(value: Array<Patent>) {
    this._patents = value;
    const loadIpc4Description$ = this.patentTableService.loadIpc4Description(value);
    this.subscriptions.add(loadIpc4Description$);
    this.setLegalStatuses();
    this.loadRatings();
  }

  get canAnnotate(): boolean {
    return this.userService.canUserAnnotate();
  }

  get isAllChecked() {
    if (this.secondaryPatentSelection) {
      return this.pagination?.total_hits === this.secondaryPatentSelection.length;
    }

    if (this.preSelected && this.preSelectedOn) {
      return this.patents && this.patents.every(p => get(p, this.preSelected, '') === this.preSelectedOn);
    }

    return this.pagination?.total_hits === this.storeService.selectedPublications.length;
  }

  get legalStatusFilter(){
    return this.patentTableService.legalStatusFilter;
  }

  isSelectionFull(patent: Patent): boolean{
    return this.selectionLimit && this.secondaryPatentSelection &&
      this.secondaryPatentSelection.indexOf(this.patentTableService.getQueriedPublicationNumber(patent)?.toString()) === -1 &&
      this.secondaryPatentSelection.length >= this.selectionLimit;
  }

  get isVisibleChecked() {
    if (this.secondaryPatentSelection) {
      return this.patents.every(o => {
        return this.secondaryPatentSelection
          .indexOf(this.patentTableService.getQueriedPublicationNumber(o)?.toString()) > -1;
      });
    }

    if (this.preSelected && this.preSelectedOn) {
      return this.patents && this.patents.every(p => get(p, this.preSelected, '') === this.preSelectedOn);
    }

    return this.patents && this.storeService.selectedPublications && this.patents.every(o => {
      return this.storeService.selectedPublications
        .indexOf(this.patentTableService.getPublicationNumber(o, false)?.toString()) > -1;
    });
  }

  get isListVisible(): boolean {
    return this.storeService.patentListViewMode !== ViewModeTypeEnum.ANALYSIS;
  }

  get IsPublication(): Boolean{
    if(this.secondaryIsPublicationSource !== undefined){
      return this.secondaryIsPublicationSource;
    }
    return this.storeService.isPublications;
  }

  ngOnInit() {
    if (!this.pathUrl) {
      this.pathUrl = this.router.url.split('?')[0];
    }

    const pagination$ = this.storeService.pagination$.subscribe({
      next: value => {
        this.openedPatent = [];
        this.patentTableService.closeAll();
      }
    });
    this.subscriptions.add(pagination$);

    const selectedPublications$ = this.storeService.selectedPublications$.pipe(take(1)).subscribe({
      next: value => {
        if (!value || !value.length) {
          this.patentTableService.resetSelectedPatent();
        }
      }
    });
    this.subscriptions.add(selectedPublications$);

    const queryParams$ = this.route.queryParams.subscribe({
      next: (value) => {
        if (value.code) {
          this.queryParams = value.code;
        }
      }
    });
    this.subscriptions.add(queryParams$);

    const filterRemoved$ = this.storeService.filterRemoved$.pipe(skip(1), take(1)).subscribe({
      next: item => {
        if (item['type'] === 'result-table') {
          this.legalStatusSelected.emit();
        }
      }
    });
    this.subscriptions.add(filterRemoved$);

    const patentTableSortParams$ = this.storeService.patentTableSortParams$
      .subscribe({
        next: (val) => {
          this.sortField = val?.field;
          this.sortOrder = val?.order;
        }
      });
    this.subscriptions.add(patentTableSortParams$);

    const getTags$ = this.tagService.getTags({page_size: 250}).subscribe();
    this.subscriptions.add(getTags$);

    const selectedColumns$ = this.storeService.selectedColumnsToShow$.pipe(skip(1)).subscribe({
      next: _ => {
        this.fixColumnWidths();
        this.checkScrollbar();
        this.loadRatings();
      }
    });
    this.subscriptions.add(selectedColumns$);

    const patentListViewMode$ = this.storeService.patentListViewMode$.pipe(skip(1)).subscribe({
      next: _ => {
        this.fixColumnWidths();
        this.checkScrollbar();
      }
    });
    this.subscriptions.add(patentListViewMode$);

    const selectAllDocuments$ = this.storeService.selectAllDocuments$.subscribe({
      next: isSelectAll => {
        if (isSelectAll != null) {
          if (isSelectAll) {
            this.selectAllPatent();
          } else {
            this.clearAllPatent();
          }
        }
      }
    });
    this.subscriptions.add(selectAllDocuments$);

    const reloadRatingColumn$ = this.ratingService.reloadRatingColumn$
      .pipe(
        filter((val) => val),
        debounceTime(500)
      )
      .subscribe({
        next: (val) => {
          this.loadRatings();
        }
      });
    this.subscriptions.add(reloadRatingColumn$);

    this.setLegalStatuses();
    this.fixColumnWidths();
    this.loadRatings();
  }

  ngAfterViewInit() {
    const searching$ = this.storeService.searching$.subscribe({
      next: value => {
        this.searchingTable = value;
        this.checkScrollbar();
      }
    });
    this.subscriptions.add(searching$);

    const searchingEvent$ = this.storeService.searchingEvent.subscribe({
      next: value => {
        this.searchingTable = value;
        this.checkScrollbar();
      }
    });
    this.subscriptions.add(searchingEvent$);
  }

  ngAfterContentChecked() {
    this.showTags = true;
  }

  getPublicationNumber(patent: Patent): string {
    return get(patent, 'general.raw_publication_number', '')
      .replace(/-|-/gi, '');
  }

  getAnalyticsValue(patent: Patent): string {
    const analyticsValues = [
      'Bottom 75%', 'Top 25%', 'Top 10%', 'Top 1%'
    ];
    return analyticsValues[get(patent, 'analytics.impact')] || 'N/A';
  }

  getAnalyticsRisk(patent: Patent): string {
    const analyticsValues = [
      'Bottom 75%', 'Top 25%', 'Top 10%', 'Top 1%'
    ];
    return analyticsValues[get(patent, 'analytics.risk')] || 'N/A';
  }

  onHeadClick(field: string, name?: string) {
    if (!this.patents?.length || !this.hasSorting) {
      return;
    }
    if (this.sortField === field) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortOrder = 'asc';
    }

    this.emitSortParams(name);
  }

  selectVisiblePatent(event) {
    if (this.selectionLimit && this.patents.length > this.selectionLimit) {
      this.errorMessage.emit('Selection list is limited to ' + this.selectionLimit + ' publications.');
      event.preventDefault();
      event.target.checked = false;
      return;
    }
    event.target.checked = !this.isVisibleChecked
    if (this.secondaryPatentSelection) {
      const allPatent = this.patents.map(p => this.patentTableService.getQueriedPublicationNumber(p)?.toString());
      if (event.target.checked) {
        allPatent.forEach(patent => {
          if (this.secondaryPatentSelection.indexOf(patent) === -1) {
            this.secondaryPatentSelection.push(patent);
          }
        });
      } else {
        allPatent.forEach(patent => {
          const patentIndex = this.secondaryPatentSelection.indexOf(patent);
          if (patentIndex > -1) {
            this.secondaryPatentSelection.splice(patentIndex, 1);
          }
        });
      }
    } else {
      this.selectedPublications = this.patentTableService.selectVisiblePatent(event, this.patents, this.storeService);
    }
    this.checkedAll.emit({state: event.target.checked, patents: this.patents});
  }

  onSelectAllPatentClicked(event) {
    if (this.selectionLimit && this.patents.length > this.selectionLimit) {
      this.errorMessage.emit('Selection list is limited to ' + this.selectionLimit + ' publications.');
      return;
    }
    event.target.checked = !this.isAllChecked
    if (event.target.checked) {
      this.selectAllPatent();
    } else {
      this.clearAllPatent();
    }
  }

  private selectAllPatent() {
    this.searchingTable = true;
    let searchFilters = {};
    if (this.storeService instanceof LandscapeStoreService || this.storeService instanceof MonitorStoreService ||
      this.storeService instanceof CollectionStoreService) {
      searchFilters = this.storeService.search['params']?.filters ? {free_text_query: this.storeService.search['params']['filters']} : {};
    } else {
      if (this.storeService.search['payload']?.search_filters) {
        searchFilters = this.storeService.search['payload']?.search_filters;
      }
    }
    const extractPatentList$ = this.patentNumberService.extractPatentList({
      search_hash: this.storeService.searchHash,
      include_patent_id: true,
      search_filters: searchFilters
    }).pipe(finalize(() => { this.searchingTable = false; this.checkScrollbar(); })).subscribe({
      next: response => {
        if (this.secondaryPatentSelection) {
          this.secondaryPatentSelection = response.patent_list;
        } else {
          if (this.storeService instanceof CitationSearchStoreService) {
            const patent_id_list = response.patent_id_list.filter(id => this.patentNumberService.getDocuments().find(doc => parseInt(doc.general.docdb_family_id) !== id));
            const patent_list = response.patent_list.filter(pn => this.patentNumberService.getDocuments().find(doc => doc.general.raw_publication_number !== pn));
            response = {patent_id_list, patent_list};
            this.selectedPublications = this.patentTableService.selectAllPatent(true, response, this.storeService);
          }
          this.selectedPublications = this.patentTableService.selectAllPatent(true, response, this.storeService);
        }
        response['patent_id_list'].map(x => x.toString())
        this.checkedAll.emit({state: true, patents: this.patents, allPatents: response});
      },
      error: error => {
        console.error(error);
      }
    });
    this.subscriptions.add(extractPatentList$);
  }

  clearAllPatent() {
    if (this.secondaryPatentSelection) {
      this.secondaryPatentSelection = [];
    } else {
      this.selectedPublications = this.patentTableService.selectAllPatent(false, null, this.storeService);
    }
    this.checkedAll.emit({state: false, patents: this.patents, allPatents: []});
  }

  get hasSelectedPatents(): boolean {
    return !!(this.storeService.selectedPatentIds.length) ;
  }

  selectPatent(event, patent, index: number) {
    if (this.selectionLimit && event.target.checked && this.secondaryPatentSelection.length >= this.selectionLimit) {
      this.errorMessage.emit('Selection list is limited to ' + this.selectionLimit + ' publications.');
      event.preventDefault();
      event.target.checked = false;
      return;
    }
    if (this.secondaryPatentSelection) {
      const patentIndex = this.secondaryPatentSelection.indexOf(this.patentTableService.getQueriedPublicationNumber(patent)?.toString());
      if (event.target.checked && patentIndex === -1) {
        this.secondaryPatentSelection.push(this.patentTableService.getQueriedPublicationNumber(patent)?.toString());
      } else if (!event.target.checked && patentIndex > -1) {
        this.secondaryPatentSelection.splice(patentIndex, 1);
      }
    } else {
      this.selectedPublications = this.patentTableService.selectPatent(event, patent, this.storeService);
    }
    this.patentChecked.emit({state: event.target.checked, patent: patent});
  }

  openDetail(patent: Patent) {
    if (patent.general?.obfuscated) {
      return;
    }
    if (!this.isExternalUser()) {
      const logActivity$ = this.userService.logActivity('PREVIEW_PATENT').subscribe();
      this.subscriptions.add(logActivity$);
    }
    this.openedPatent = this.patentTableService.openDetail(Number(patent.general?.docdb_family_id), patent.general?.obfuscated);
  }

  openAll() {
    if (!this.isExternalUser) {
      const logActivity$ = this.userService.logActivity('PREVIEW_PATENT').subscribe();
      this.subscriptions.add(logActivity$);
    }
    this.openedPatent = this.patentTableService.openAll(this.patents);
  }

  onFilterRows(queryParams: Object) {
    this.filterRows.emit(queryParams);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  isSelected(patent: Patent): boolean {
    if (this.preSelected && this.preSelectedOn) {
      return get(patent, this.preSelected, '') === this.preSelectedOn;
    }

    if (this.secondaryPatentSelection) {
      return this.secondaryPatentSelection.includes(this.patentTableService.getQueriedPublicationNumber(patent)?.toString());
    }

    const publicationNumber = this.patentTableService.getPublicationNumber(patent)?.toString();
    const isSelectedPublication = this.storeService.selectedPublications && this.storeService.selectedPublications
      .findIndex((p) => p && publicationNumber === p.replace(/-|-/gi, '')) > -1;

    if (this.IsPublication) {
      return isSelectedPublication;
    }

    if (this.storeService.selectedPatentIds) {
      return this.storeService.selectedPatentIds.includes(Number(patent.general.docdb_family_id));
    }

    return isSelectedPublication;
  }

  /**
   * checkProfile
   *
   * Monitor helper function: check patent object for specific method association (BOOLEAN, MACHINE_LEARNING, SEMANTIC, CITATION)
   * @param patentObj patent object
   * @param method method string
   */
  checkProfile(patentObj, method: string) {
    return (patentObj?.snapshots || []).find((rec) => rec.type === method);
  }

  /**
   * updateFeedbackTraining
   *
   * Monitor helper function: Store or delete an ML dataset entry. Deleting a dataset entry when a user clicks on an active feedback.
   * @param matchType: MlMatchTypeEnum
   * @param patent: Patent
   */
  updateFeedbackTraining(matchType: MlMatchTypeEnum, patent: Patent) {
    const docdbFamilyId = patent.general.docdb_family_id.toString();

    if (this.sendingFeedback[docdbFamilyId]) {
      return;
    }

    this.sendingFeedback[docdbFamilyId] = matchType;
    const profileId = this.route.snapshot.params.id;
    const isDeleteEntry = patent.machine_learning_feedback && matchType === patent.machine_learning_feedback.match;

    if (isDeleteEntry) {
      this.deleteDatasetEntry(profileId, docdbFamilyId, patent);
    } else {
      this.storeDatasetEntry(profileId, docdbFamilyId, patent, matchType);
    }
    this.matomoService.monitorTechnologyFeedbackThumbs();
  }

  getBaseQueryPrams(patent: Patent, patentIndex: number, backData: any) {
    const previousUrl = this.router.parseUrl(this.router.url);
    delete previousUrl.queryParams['smart_highlight_search_hash'];
    delete previousUrl.queryParams['search_hash'];
    delete previousUrl.queryParams['monitor_run_id'];
    delete previousUrl.queryParams['show_highlight'];
    delete previousUrl.queryParams['show_smart_highlight'];
    delete previousUrl.queryParams['filters_query'];
    delete previousUrl.queryParams['search'];

    const params = this.backButtonTitle ? {
      previousUrl: previousUrl.toString(),
      backButtonTitle: this.backButtonTitle,
      mode: this.sideBarViewMode
    } : {};

    params['search_hash'] = this.showHighlightSearchHash || this.storeService.searchHash;

    if (this.queryParams) {
      params['code'] = this.queryParams;
    }

    if (this.showSmartHighlight) {
      const includeMonitorRunId = this.srcMonitorRunId && this.checkProfile(patent, 'MACHINE_LEARNING');

      if (includeMonitorRunId) {
        params['monitor_run_id'] = this.srcMonitorRunId;
        params['show_smart_highlight'] = 1;
      }

      if (!this.srcMonitorRunId) {
        params['smart_highlight_search_hash'] = this.showSmartHighlightSearchHash || this.storeService.searchHash;
        params['show_smart_highlight'] = 1;
      }
    }

    if (this.showHighlight) {
      const filtersQuery = this.storeService.getAppliedFiltersQuery();
      if (this.storeService.isBooleanSearchStore() || filtersQuery?.length > 0) {
        params['show_highlight'] = 1;
      }

      if (filtersQuery?.length > 0) {
        params['filters_query'] = filtersQuery;
      }
    }

    if(patentIndex){
      params['patent_index'] = patentIndex;
    }

    if (backData) {
      params['back_data'] = JSON.stringify(backData);
    }

    return params;
  }

  onClickPatentViewer(event: MouseEvent, patent: Patent, index: number, openMode: 'openNewTab' | 'openSameTab') {
    event.stopPropagation();

    const patentViewUrl = this.getViewPath(patent);
    const patentIndex = this.countSerialPipe.transform(this.pagination, index);
    const storeCurrentStateId = new Date().getTime().toString(10);
    const backData = {
      storeCurrentStateId,
      ...this.linkData
    };
    const queryParams = this.getBaseQueryPrams(patent, patentIndex, backData);

    this.storeService.docdbFamilyIdFromPatentViewer = patent.general.docdb_family_id;
    this.storeService.storeCurrentState(storeCurrentStateId);
    this.storeService.storeSearchData(this.patents, this.pagination);

    if (this.storeService instanceof MonitorStoreService) {
      this.storeService.scrollTopPage = window.pageYOffset;
    }
    this.matomoService.resultListPatentViewerButton();

    if (openMode === 'openNewTab') {
      const url = this.router.serializeUrl(this.router.createUrlTree(patentViewUrl, {queryParams: queryParams}));
      window.open(url, '_blank');
      return;
    }

    this.router.navigate(patentViewUrl, {state: {data: backData}, queryParams: queryParams});
  }

  queryParamsForAnnotateLink(patent: Patent, mode: PatentSideBarViewModeEnum) {
    const params = this.getBaseQueryPrams(patent, null, null);
    params['mode'] = mode;
    return params;
  }

  getLegalStatusIcon(patent: Patent) {
    if (this.IsPublication) {
      return this.patentService.getPatentLegalStatusIcon(patent['bibliographic']);
    }
    return this.patentService.getFamilyLegalStatusIcon(patent['bibliographic']);
  }

  getPublicationNumberToDisplay(patent: any) {
    return this.showQueriedPublicationNumber ? this.patentTableService.getQueriedPublicationNumber(patent) : this.patentTableService.getPublicationNumber(patent)
  }

  getFlagIcon(publication_number: string) {
    return this.patentService.getFlagCssByPublication(publication_number, FlagSizeEnum.MD).toLowerCase();
  }

  getPreviousLegalStatusIcon(patent: any, patentNumber: string) {
    const entity = patent.legal_status_changes?.find(x => x.publication_number == patentNumber);
    return entity ? this.patentService.getPatentLegalStatusIcon({'legal_status': entity.previous_status['general'], 'legal_status_extended': entity.previous_status['extended']}) : null;
  }

  getCurrentLegalStatusIcon(patent: any, patentNumber: string) {
    const entity = patent.legal_status_changes?.find(x => x.publication_number == patentNumber);
    return entity ? this.patentService.getPatentLegalStatusIcon({'legal_status': entity.current_status['general'], 'legal_status_extended': entity.current_status['extended']}) : null;
  }

  getCurrentLegalStatusReason(patent: any, patentNumber: string) {
    const entity = patent.legal_status_changes?.find(x => x.publication_number == patentNumber);
    return entity ? this.patentService.getPatentLegalStatusIcon({'legal_status': entity.current_status['general'], 'legal_status_extended': entity.current_status['extended']}).tooltip : 'Change in patent family';
  }

  getOtherLegalStatusChanges(patent: Patent) {
    const patentNumber = this.getPublicationNumberToDisplay(patent);
    return patent.legal_status_changes.filter(x => x.publication_number !== patentNumber);
  }

  getIpcDescription(ipc4: string): string {
    const ipc4Val = ipc4.toString();
    const desc = this.patentTableService.getIpcDescription(ipc4Val);
    return !desc || desc === '' ? ipc4Val : desc;
  }

  isExternalUser(): boolean {
    return this.userService.isExternalUser() || !!(this.route.snapshot.params.share_code);
  }

  getViewPath(patent): Array<string> {
    const familyId = patent.family_id || patent.general.docdb_family_id;
    const baseViewPath = this.isExternalUser() ? `${this.pathUrl}/view/shared` : `${this.pathUrl}/view`;
    const viewPath = [baseViewPath, familyId];

    if (this.IsPublication) {
      const publicationNumber = patent.family_id ? patent.publication_number : this.getPublicationNumberToDisplay(patent);
      viewPath.push('publication', publicationNumber);
    }
    return viewPath;
  }

  selectLegalStatus(event, status) {
    event.preventDefault();
    event.stopPropagation();
    if(this.legalStatusFilter){
      this.legalStatusFilter.forEach(o => { if(o.name === status.name){ o.unchecked = !o.unchecked; } });
    }
    this.patentTableService.selectLegalStatus(this.storeService);
    this.legalStatusSelected.emit(status.value || status.name);
  }

  /**
   * storeDatasetEntry
   *
   * Monitor helper function: Save a feedback for a patent
   * @param profileId: string
   * @param docdbFamilyId: string
   * @param patent: Patent
   * @param matchType: MlMatchTypeEnum
   */
  private storeDatasetEntry(profileId: string, docdbFamilyId: string, patent: Patent, matchType: MlMatchTypeEnum) {
    const payload = new MlStoreDatasetParam(MlSourceTypeEnum.FEEDBACK, matchType, patent.general.docdb_family_id);

    const mlStoreDatasetEntry$ = this.searchService.mlStoreDatasetEntry(profileId, [payload])
      .subscribe({
        next: data => {
          this.callMLApiSuccess(patent, docdbFamilyId, matchType);
        },
        error: error => {
          this.sendingFeedback[docdbFamilyId] = null;
        }
      });
    this.subscriptions.add(mlStoreDatasetEntry$);
  }

  /**
   * deleteDatasetEntry
   *
   * Monitor helper function: Delete a feedback for a patent
   * @param profileId: string
   * @param docdbFamilyId: string
   * @param patent: Patent
   */
  private deleteDatasetEntry(profileId: string, docdbFamilyId: string, patent: Patent) {
    const mlDeleteDatasetEntry$ = this.searchService.mlDeleteDatasetEntry(profileId, docdbFamilyId)
      .subscribe({
        next: data => {
          this.callMLApiSuccess(patent, docdbFamilyId, null);
        },
        error: error => {
          this.sendingFeedback[docdbFamilyId] = null;
        }
      });
    this.subscriptions.add(mlDeleteDatasetEntry$);
  }

  /**
   * callMLApiSuccess
   *
   * Monitor helper function: Callback after storing/deleting a feedback successfully
   * @param patent: Patent
   * @param docdbFamilyId: string
   * @param matchType: MlMatchTypeEnum | null
   */
  private callMLApiSuccess(patent: Patent, docdbFamilyId: string, matchType: MlMatchTypeEnum) {
    this.sendingFeedback[docdbFamilyId] = null;

    if (!patent.machine_learning_feedback) {
      patent.machine_learning_feedback = {
        source: MlSourceTypeEnum.FEEDBACK,
        match: matchType
      } as MachineLearningFeedback;
    } else {
      patent.machine_learning_feedback.match = matchType;
    }
    this.feedbackClick.emit(patent);
  }

  hasTags(patent): boolean {
    return patent?.tags?.green  ||
           patent?.tags?.sep?.length > 0 ||
           patent?.custom_tags?.length > 0;
  }

  onMarkAsUnread(patent: Patent): void {
    this.markAsUnread.emit(Number(patent.general?.docdb_family_id));
  }

  getPatentTitle(patent: Patent): string {
    const showHighlight = this.showHighlight && this.isOpened(patent);
    return this.patentTableService.getPatentTitle(patent, showHighlight);
  }

  isOpened(patent: Patent): boolean {
    return this.openedPatent.includes(patent.general?.docdb_family_id);
  }

  fixColumnWidths() {
    setTimeout(() => {
      document.querySelectorAll('.results-table-container th:not([width])')
        .forEach((header: HTMLElement) => {
          this.renderer.removeStyle(header, 'width');
        });

      setTimeout(() => {
        document.querySelectorAll('.results-table-container th:not([width])')
          .forEach((header: HTMLElement) => {
            const width = header.offsetWidth;
            this.renderer.setStyle(header, 'width', `${width}px`);
          });
          this.checkScrollbar();
      }, 200);
    }, 200);
  }

  getCollectionSourceTitle(collection_sources){
    if(collection_sources?.length > 0){
      if(collection_sources?.length > 1){
        return collection_sources?.length +  ' sources'
      } else {
          if(collection_sources[0]?.run_type){
            return 'monitor'
          } else if(collection_sources[0]?.search_type){
            return collection_sources[0].search_type.toLowerCase();
          } else if(this.isManualSource(collection_sources[0])){
            return 'manual';
          }
      }
    }
    return 'unknown';
  }

  getSourceQuery(source): string{
    if(this.storeService instanceof CollectionStoreService){
      return this.storeService.getSourceDescription(source)
    }
    return '';
  }

  onLoadSource(event, source, newTab = false) {
    event.preventDefault();
    event.stopPropagation();

    if(!!source?.run_type && source.can_read){
      this.loadMonitorSource.emit({monitorRun: source, newTab: newTab});
    } else if(source?.search_input || source?.patent_numbers?.length>0) {
      this.loadSearchSource.emit({source: source, newTab: newTab});
    }
  }

  canLoadSource(source): boolean {
    return (!!source?.run_type && source.can_read) ||
      !!(source?.search_input) || source?.patent_numbers?.length > 0;
  }

  onFilterByCollectionSource(event, source){
    event.preventDefault();
    event.stopPropagation();
    if(this.collectionSourceOption){
      this.collectionSourceOption.forEach(o => { if(o === source){ o.selected = !o.selected; } });
    }
  }
  onResetSourceFilter(){
    if(this.collectionSourceOption){
      this.collectionSourceOption.forEach(o => o.selected = false);
      this.patentTableService.collectionSourceTypeFilter(null, this.storeService);
    }
  }
  onCancelSourceFilter(){
    const filter = this.storeService.filters.find(f => f.collectionSource === 'sourceType');
    this.collectionSourceOption.forEach(o => {
      if(filter && filter['value'].indexOf(o.value)>-1){
        o.selected = true;
      } else{
        o.selected = false;
      }
    });
  }
  onApplySourceFilter(){
    const sources = this.collectionSourceOption.filter(o => o.selected).map(o=>o.value);
    this.patentTableService.collectionSourceTypeFilter(sources.length>0 ? sources :null, this.storeService);
  }

  filterByCollectionSourceUser($event, user){
    if(this.collectionSourceUsers){
      this.collectionSourceUsers[user.id].isSelected = $event.target.checked;
    }
  }
  onResetUserFilter(){
    if(this.collectionSourceUsers){
      for (const [key, value] of Object.entries(this.collectionSourceUsers)) { value.isSelected = false; }
      this.patentTableService.collectionSourceUserFilter(null, this.storeService);
    }
  }
  onCancelUserFilter(){
    const filter = this.storeService.filters.find(f => f.collectionSource === 'sourceUser');
    for (const [k, o] of Object.entries(this.collectionSourceUsers)) {
      if(filter && filter['value'].indexOf(o.id)>-1){
        o.isSelected = true;
      } else{
        o.isSelected = false;
      }
    }
  }
  onApplyUserFilter(){
    const users = [];
    for (const [k, u] of Object.entries(this.collectionSourceUsers)) {
      if(u.isSelected){
        users.push(u);
      }
    }
    this.patentTableService.collectionSourceUserFilter(users.length>0?users:null, this.storeService);
  }

  getSourceUsers(sources: any[]){
    if(sources && this.collectionSourceUsers){
      const user_ids = [...new Set(sources.map(s => s.user_id))];
      return user_ids.map(id => this.collectionSourceUsers[id]);
    }
    return [];
  }
  isOnlyManualSources(sources: any[]){
    return sources.every(s=> this.isManualSource(s));
  }
  isManualSource(source){
    return source?.monitor_run_id === null && source?.monitor_run_id === source?.search_history_id;
  }

  onPatentTagsChanged(tags: TagModel[], patent: Patent) {
    if (!patent.custom_tags) {
      patent.custom_tags = [];
    }

    patent.custom_tags = tags;
  }

  private setLegalStatuses() {
    if (!this.storeService) {
      return;
    }
    this.patents?.forEach(document => {
      this.legalStatus[this.getDocumentNumber(document)] = this.patentService.getLegalStatuses(document).map(ls => {
        if (this.storeService.isPublications) {
          return this.patentService.getPatentLegalStatusIcon(ls);
        }
        const asDeadAndUnknown = document.bibliographic.legal_status?.includes('invalid') && document.bibliographic.legal_status?.includes('unknown');
        return this.patentService.getFamilyLegalStatusIcon(ls, asDeadAndUnknown);
      });
    })
    this.patentTableService.setLegalStatusFilter(this.storeService)
  }

  getDocumentNumber(document) {
    return this.storeService?.isPublications ? document.general.raw_publication_number : document.general.docdb_family_id;
  }

  getNumberOfSelectedUsers() {
    if (!this.collectionSourceUsers) {
      return 0;
    }
    return Object.values(this.collectionSourceUsers).filter(u => u.isSelected).length;
  }
  getNumberOfSelectedCollectionSource(){
    if(!this.collectionSourceOption){
      return 0;
    }
    return this.collectionSourceOption.filter(o => o.selected).length;
  }

  checkScrollbar() {
    let retries = 0;
    let intervalId = setInterval(() => {
      const el = this.patentTableScroller()?.nativeElement?.querySelector<HTMLElement>('.table-scrollbar');
      const table = this.patentTable()?.nativeElement?.querySelector<HTMLElement>('table');
      if (el && table) {
        el.style.width = `${table.offsetWidth - 48}px`;
        if (intervalId) {
          clearInterval(intervalId);
          intervalId =  undefined;
        }
      }
      retries += 1;

      if (retries > 50 && intervalId) {
        clearInterval(intervalId);
        intervalId = undefined;
      }
    }, 100);
  }
  onTableScroll(event) {
    this.patentTable().nativeElement.querySelector<HTMLElement>('.patent-table-wrapper').scrollLeft = event.target.scrollLeft;
  }

  allowOpenNewTab(title): boolean {
    if (['manual', 'unknown'].includes(title)) {
      return false;
    }
    return true;
  }

  private loadRatings() {
    const documentIds = [...new Set((this.patents || []).map(p => p.general.docdb_family_id))];
    if (this.storeService?.showColumn('ratings') && documentIds.length > 0) {
      const payload = {
        document_id: `in:${documentIds.join(',')}`,
        load_all: 1
      }
      const getRatings$ = this.taskService.getRatings(payload)
        .pipe(
          take(1),
          mergeMap(({tasks}) => {
            return this.taskService.getAuthorAndAssigneesForTasks(tasks);
          }),
        )
        .subscribe({
          next: (tasks) => {
            this.patents.forEach(p => {
              const tasksForDoc = tasks.filter((t) => Number(t.document_id) === Number(p.general.docdb_family_id));
              p.taskStats = this.taskService.classifyTasks(tasksForDoc);
            });
          }
        });
      this.subscriptions.add(getRatings$);
    }
  }

  onRateMyselfEvent(event: RateMyselfEventParams) {
    this.rateMySelfParams = event;
    this.ratingAnswerPopper.show(event.targetElement);
  }

  onRateMyselfDoneEvent(event: ShowRatingsEventParams) {
    this.ratingAnswerPopper.hide();
    this.classifyRatings(this.rateMySelfParams.patent, event.currentRatingTask);
  }

  onCreateTaskEvent(event: CreateTaskEventParams) {
    this.createRequestParams = event;
    this.ratingFormPopper.show(event.targetElement);
  }

  onTaskCreatedEvent(event: ShowRatingsEventParams) {
    this.ratingFormPopper.hide();
    this.classifyRatings(this.createRequestParams.patent, event.currentRatingTask);
  }

  onSortRatingsClicked(op: SortRatingsOptionEnum) {
    const sortName = this.getSortRatingsOptionName(op);
    if (sortName === this.sortField) {
      this.sortField = null;
      this.sortOrder = null;
    } else {
      this.sortField = sortName;
      this.sortOrder = 'asc';
    }

    this.emitSortParams('Ratings');
  }

  getSortRatingsOptionName(op: SortRatingsOptionEnum) {
    return Object.keys(SortRatingsOptionEnum)[Object.values(SortRatingsOptionEnum).indexOf(op)].toLowerCase();
  }

  private classifyRatings(patent: Patent, updatingTask: TaskModel) {
    if (updatingTask) {
      const tasks = patent.taskStats?.all_tasks || [];
      tasks.push(updatingTask);
      patent.taskStats = this.taskService.classifyTasks(tasks);
    }
  }

  private emitSortParams(name: string) {
    const sortParams = {field: this.sortField, order: this.sortOrder} as SortParams;
    if (name) {
      sortParams.name = name;
    }
    this.storeService.patentTableSort = sortParams;
    this.sort.emit(sortParams);
  }
}
