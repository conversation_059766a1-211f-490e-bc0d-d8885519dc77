import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PdfNotFoundDialogComponent } from './pdf-not-found-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('PdfNotFoundDialogComponent', () => {
  let component: PdfNotFoundDialogComponent;
  let fixture: ComponentFixture<PdfNotFoundDialogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [PdfNotFoundDialogComponent],
      providers: [NgbActiveModal, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      })],
      imports: [
        HttpClientTestingModule, 
        RouterModule.forRoot([]),
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PdfNotFoundDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
