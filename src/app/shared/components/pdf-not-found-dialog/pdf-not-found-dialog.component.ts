import { Component, Input, OnDestroy } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { take } from 'rxjs/operators';
import { EspacenetService, MatomoService, PatentService } from '@core/services';
import { Patent } from '@core/models';
import { Subscription } from 'rxjs';

@Component({
  templateUrl: './pdf-not-found-dialog.component.html',
  styleUrls: ['./pdf-not-found-dialog.component.scss']
})
export class PdfNotFoundDialogComponent implements OnDestroy {

  @Input()
  family: string;

  @Input()
  private _patent: Patent;

  downloadingFiles = {};
  isLoadingImage = true;

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private patentService: PatentService,
    private espacenetService: EspacenetService,
    private matomoService: MatomoService
  ) {
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  loadListPdf() {
    if (this.patent['pdfFamily']) {
      this.isLoadingImage = false;
      return;
    }

    const getListImage$ = this.patentService.getListPdf({patent_number: this.patent.general.raw_publication_number})
      .subscribe({
        next: ({attachments}) => {
          const results = new Set();
          for (const attachment of attachments) {
            results.add(attachment.patent_number);
          }
          this.patent['pdfFamily'] = Array.from(results);
          this.isLoadingImage = false;
        }
      });
    this.subscriptions.add(getListImage$);
  }

  getPdf(patentNumber) {
    if (this.downloadingFiles[patentNumber]) {
      return;
    }

    this.downloadingFiles[patentNumber] = true;
    this.matomoService.resultListViewPdfButton();

    const getPdf$ = this.patentService.getPdf({patent_number: patentNumber})
      .pipe(take(1))
      .subscribe({
        next: (file) => {
          const blob = new Blob([file], {type: file.type});
          if (blob.type === 'application/pdf') {
            this.patentService.downloadFile(blob, patentNumber, this.patent);
          } else {
            this.family = patentNumber;
          }

          this.downloadingFiles[patentNumber] = false;

          this.activeModal.close();
        },
        error: (err)=> {
          this.family = patentNumber;
          this.downloadingFiles[patentNumber] = false;
        }
      });
    this.subscriptions.add(getPdf$);
  }

  getWorldwideLink(publicationNumber: string): string {
    const docdbFamilyId = this.patent && this.patent.general ? this.patent.general.docdb_family_id : null;
    return this.espacenetService.getWorldwideLinkByPublicationNumber(publicationNumber, docdbFamilyId);
  }

  getRemainingPatents() {
    if (this.patent && this.patent['pdfFamily']) {
      return this.patent['pdfFamily'].filter(o => o !== this.family);
    }

    return [];
  }

  get pdfFamily(): Array<string> {
    return this.patent ? this.patent['pdfFamily'] : [];
  }

  get patent(): Patent {
    return this._patent;
  }

  set patent(value: Patent) {
    this._patent = value;
    this.loadListPdf();
  }
}
