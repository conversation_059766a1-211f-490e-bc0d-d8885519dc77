<div class="modal-header">
  <div class="modal-title">PDF Download</div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
  <div class="row">
    <div class="col-12 paragraph-1">Error in downloading the original PDF for <code>{{family}}</code>. What would you like to do?<br></div>
    <div class="col-2" [ngClass]="{'col-6': !pdfFamily && !isLoadingImage}">
      <div class="mb-3">Try again</div>

      <a (click)="getPdf(family)" class="btn btn btn-primary-outline" tabindex="0">
        <i class="far fa-file-pdf download-pdf-icon" ngbTooltip="Get PDF" style="color: #bd4a2a; font-size: 0.9em;"
           *ngIf="!downloadingFiles[family]"></i>
        <img src="/assets/images/octimine_blue_spinner.gif" style="width: 12px;"
             *ngIf="downloadingFiles[family]">
        View PDF
      </a>
    </div>
    <div class="col-4 border-start" [ngClass]="{'col-6': !pdfFamily && !isLoadingImage}">
      <div class="mb-3">Look for PDF on Espacenet</div>

      <a [href]="getWorldwideLink(family)" class="btn btn btn-primary-outline" target="_espacenet">
        Take me to Espacenet
      </a>
    </div>
    <div class="col-6 border-start" *ngIf="getRemainingPatents().length > 0 && !isLoadingImage">
      <div class="mb-3">Try a different document from the same patent family:</div>
      <div class="container px-0">
        <div class="row row-cols-2">
          <ng-container *ngFor="let n of pdfFamily">
            <div class="col" *ngIf="n != family">
              <a [href]="getWorldwideLink(n)" target="_espacenet">{{n}}</a>&nbsp;
              <a href="javascript:void(0)" (click)="getPdf(n)" *ngIf="!downloadingFiles[n]">
                <i class="far fa-file-pdf" style="color: #bd4a2a; font-size: 0.9em" ngbTooltip="Get PDF"></i>
              </a>
              <img *ngIf="downloadingFiles[n]" src="assets/images/octimine_blue_spinner.gif" style="width: 15px;" alt=""/>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
    <div class="col-6" style="text-align: center;" *ngIf="isLoadingImage">
      <img src="assets/images/octimine_blue_spinner.gif" alt="Please hold" style="width: 20%">
    </div>
  </div>
</div>
