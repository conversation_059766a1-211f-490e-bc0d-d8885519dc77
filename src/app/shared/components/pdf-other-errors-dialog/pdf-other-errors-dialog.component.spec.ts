import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PdfOtherErrorsDialogComponent } from './pdf-other-errors-dialog.component';
import { PdfNotFoundDialogComponent } from '../pdf-not-found-dialog/pdf-not-found-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('PdfOtherErrorsDialogComponent', () => {
  let component: PdfOtherErrorsDialogComponent;
  let fixture: ComponentFixture<PdfOtherErrorsDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PdfOtherErrorsDialogComponent, PdfNotFoundDialogComponent],
      providers: [NgbActiveModal, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      })],
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])       
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PdfOtherErrorsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
