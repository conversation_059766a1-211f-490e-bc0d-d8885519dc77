import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PdfViewerDialogComponent } from './pdf-viewer-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { BypassSecurityPipe } from '@core';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';

describe('PdfViewerDialogComponent', () => {
  let component: PdfViewerDialogComponent;
  let fixture: ComponentFixture<PdfViewerDialogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      providers: [NgbActiveModal, BypassSecurityPipe, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      declarations: [PdfViewerDialogComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PdfViewerDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
