import { Component, HostListener, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Patent } from '@core/models';

@Component({
  selector: 'app-pdf-viewer-dialog',
  templateUrl: './pdf-viewer-dialog.component.html',
  styleUrls: ['./pdf-viewer-dialog.component.scss']
})
export class PdfViewerDialogComponent {

  @Input()
  src: Blob;

  @Input()
  patent: Patent;

  constructor(
    public activeModal: NgbActiveModal,
  ) {
  }

  @HostListener('document:keydown.escape', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    this.activeModal.close();
  }

  getTitle(): string {
    if (this.patent) {
      return `${this.patent.general.raw_publication_number} - ${this.patent.bibliographic.title}`;
    }
  }
}
