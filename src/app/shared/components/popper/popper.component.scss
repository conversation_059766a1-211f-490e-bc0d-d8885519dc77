@import 'scss/figma2023/variables';
@import 'scss/layout2021/mixins';

.popper-instance {
  display: none;
  opacity: 1;
  background-color: $colour-grey-050 !important;
  color: $colour-grey-800 !important;
  box-shadow: 0px 10px 15px -3px rgba(40, 40, 40, 0.10), 0px 0px 6px 0px rgba(40, 40, 40, 0.25) !important;
  border: 1px solid $colour-grey-300 !important;
  @include border-radius($radius-big);
  padding: $spacing-system-spacing-sm;

  &[data-show] {
    display: block;
  }

  &[data-popper-placement^='top'] > .popper-arrow {
    bottom: -4px;

    &:before {
      border-top-color: #FFFEFD !important;
    }
  }

  &[data-popper-placement^='bottom'] > .popper-arrow {
    top: -4px;

    &:before {
      border-bottom-color: #FFFEFD !important;
    }
  }

  &[data-popper-placement^='left'] > .popper-arrow {
    right: -4px;

    &:before {
      border-left-color: #FFFEFD !important;
    }
  }

  &[data-popper-placement^='right'] > .popper-arrow {
    left: -4px;

    &:before {
      border-right-color: #FFFEFD !important;
    }
  }

  .popper-arrow, .popper-arrow::before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }

  .popper-arrow {
    visibility: hidden;

    &::before {
      visibility: visible;
      content: '';
      transform: rotate(45deg);
    }
  }
}
