import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { Placement, createPopper, State } from '@popperjs/core';

@Component({
  selector: 'app-popper',
  templateUrl: './popper.component.html',
  styleUrls: ['./popper.component.scss']
})
export class PopperComponent {
  @Input() placement: Placement = 'top';
  @Input() offset = 8;
  @Input() showArrow = true;
  @Input() customClass: string = '';
  @Input() resizePopperWidth: boolean = false;
  @Input() allowedClickClasses: string[] = [];
  @Input() fallbackPlacements: Placement[] = [];

  @Output() popperOpened = new EventEmitter<void>();
  @Output() popperClosed = new EventEmitter<void>();
  @ViewChild('popperInstance', {static: false}) popperInstance: ElementRef<HTMLElement>;

  private isClosingBlocked = false;

  popperInstanceObject: any;

  isOpen = false;

  private resizeObserver: ResizeObserver = null;
  private targetElement: HTMLElement;

  show(targetElement: HTMLElement, externalPopperElement?: HTMLElement) {
    this.targetElement = targetElement;
    this.popperInstance.nativeElement.setAttribute('data-show', '');
    this.popperInstanceObject = createPopper(targetElement, externalPopperElement || this.popperInstance.nativeElement, {
      placement: this.placement,
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, this.offset],
          },
        },
        {name: 'flip',
          enabled: this.fallbackPlacements.length > 0,
          options: {
            fallbackPlacements: this.fallbackPlacements
          }
        },
        {name: 'eventListeners', enabled: true},
      ]
    });

    // Use requestAnimationFrame to ensure the component is rendered
    requestAnimationFrame(() => {
      setTimeout(() => {
        this.popperInstanceObject.forceUpdate();
      });

      this.popperInstanceObject.update().then(state => {
        this.popperInstanceObject.setOptions((options) => ({
          ...options,
          placement: state.placement,
          modifiers: [
            ...options.modifiers.filter(m => m.name !== 'flip'),
            { name: 'flip', enabled: false }
          ],
        }));
      });

      this.isOpen = true;
      this.updatePopperWidth();
    });

    this.popperOpened.emit();
  }

  hide() {
    if (!this.isOpen) {
      return;
    }

    this.popperInstance.nativeElement.removeAttribute('data-show');
    if (this.popperInstanceObject) {
      this.popperInstanceObject.setOptions((options) => ({
        ...options,
        placement: 'top',
        modifiers: [
          ...options.modifiers,
          {name: 'eventListeners', enabled: false},
        ],
      }));
    }
    this.popperInstanceObject.update();
    this.isOpen = false;
    this.destroyResizeObserver();
    this.popperClosed.emit();
  }

  onClickOutside(event: MouseEvent) {
    if (!event?.target || !this.isOpen || this.isClosingBlocked) {
      return;
    }

    const target = event.target as HTMLElement;
    const eventPath = event.composedPath();
    const clickedOnAllowedElement = this.allowedClickClasses.length > 0 &&
      eventPath.some((element: HTMLElement) => {
        return this.allowedClickClasses.some(className =>
          element.classList?.contains(className) || element.tagName?.toLowerCase() === className
        );
      });

    if (this.popperInstance?.nativeElement.contains(target) ||
        this.targetElement?.contains(target) ||
        clickedOnAllowedElement) {
      return;
    }
    this.hide();
  }

  destroy() {
    this.popperInstance.nativeElement.removeAttribute('data-show');
    if (this.popperInstanceObject) {
      this.popperInstanceObject.destroy();
    }
    this.isOpen = false;
    this.destroyResizeObserver();
  }

  private updatePopperWidth() {
    if (this.resizePopperWidth) {
      this.resizeObserver = new ResizeObserver((entries) => {
        const width = `${this.targetElement.offsetWidth}px`;
        this.popperInstance.nativeElement.style.width = width;
        this.popperInstance.nativeElement.style.maxWidth = width;
        this.popperInstanceObject.update();
      });

      this.resizeObserver.observe(this.targetElement);
    }
  }

  private destroyResizeObserver() {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.targetElement);
      this.resizeObserver.disconnect();
    }
  }

  public blockClosing() {
    this.isClosingBlocked = true;
  }

  public unblockClosing() {
    this.isClosingBlocked = false;
  }
}
