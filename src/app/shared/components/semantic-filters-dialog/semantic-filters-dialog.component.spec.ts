import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SemanticFiltersDialogComponent } from './semantic-filters-dialog.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormGroup, FormsModule} from '@angular/forms';
import { provideMatomo } from 'ngx-matomo-client';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '@shared/shared.module';

describe('SemanticFiltersDialogComponent', () => {
  let component: SemanticFiltersDialogComponent;
  let fixture: ComponentFixture<SemanticFiltersDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SemanticFiltersDialogComponent ],
      providers: [ NgbActiveModal, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ],
      imports: [        
        HttpClientTestingModule, 
        FormsModule, 
        SharedModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SemanticFiltersDialogComponent);
    component = fixture.componentInstance;
    component.form = new FormGroup({})
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
