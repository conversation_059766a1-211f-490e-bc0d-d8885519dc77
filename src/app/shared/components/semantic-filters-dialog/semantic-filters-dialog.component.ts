import { Component, Input, ViewChild, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { AuthorityService, MAIN_TECHNOLOGY_AREA_KEYS, MAIN_TECHNOLOGY_AREAS, UserService } from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { tooltip } from '@search/patent/tooltip';
import { AutocompleteComponent } from '../autocomplete/autocomplete.component';
import { TypeCpcIpc } from '@shared/components';

@Component({
  selector: 'app-semantic-filters-dialog',
  templateUrl: './semantic-filters-dialog.component.html',
  styleUrls: ['./semantic-filters-dialog.component.scss']
})
export class SemanticFiltersDialogComponent implements OnInit, OnDestroy {
  @Input() form: UntypedFormGroup
  initialValues;

  filterMinDate = {year: 1900, month: 1, day: 1};
  tooltipText = tooltip.text;
  tooltipTitle = tooltip.title;
  isSavingFilters = false;
  isFiltersFormGroupInvalid = false;
  typeCpcIpc = TypeCpcIpc;

  private legalStatuses = [
    {value: 'valid', label: 'Alive'},
    {value: 'invalid', label: 'Dead'},
    {value: 'unknown', label: 'Unknown'},
  ];
  private mainAreas = MAIN_TECHNOLOGY_AREAS;
  private errorClassificationFilter: {field: string, value: any, message: string}[] = [];

  @ViewChild('main_areas') main_areas: AutocompleteComponent;
  @ViewChild('legal_status') legal_status: AutocompleteComponent;
  @ViewChild('authorities_plus') authorities_plus: AutocompleteComponent;
  @ViewChild('authorities_minus') authorities_minus: AutocompleteComponent;
  @ViewChild('applicants_plus') applicants_plus: AutocompleteComponent;
  @ViewChild('applicants_minus') applicants_minus: AutocompleteComponent;

  get filtersFormGroup(): UntypedFormGroup {
    return this.form?.get('search_filters') as UntypedFormGroup;
  }

  get cpcPlusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('cpc_plus') as UntypedFormControl;
  }

  get cpcMinusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('cpc_minus') as UntypedFormControl;
  }

  get ipcPlusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('ipc_plus') as UntypedFormControl;
  }

  get ipcMinusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('ipc_minus') as UntypedFormControl;
  }

  get applicantsPlusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('applicants_plus') as UntypedFormControl;
  }

  get applicantsMinusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('applicants_minus') as UntypedFormControl;
  }

  get authoritiesPlusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('authorities_plus') as UntypedFormControl;
  }

  get authoritiesMinusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('authorities_minus') as UntypedFormControl;
  }

  get legalStatusFormControl(): UntypedFormControl {
    return this.filtersFormGroup.get('legal_status') as UntypedFormControl;
  }

  constructor(
    public userService: UserService,
    public activeModal: NgbActiveModal,
  ) { }

  ngOnInit(): void {
    this.moveIntercomButton();
    if (this.filtersFormGroup) {
      this.filtersFormGroup.statusChanges.subscribe({
        next: (val) => {
          this.isFiltersFormGroupInvalid = this.filtersFormGroup.invalid;
        }
      });

      this.isFiltersFormGroupInvalid = this.filtersFormGroup.invalid;
    }
    setTimeout(() => { this.initialValues = {...this.form.value}; }, 200);
  }

  ngOnDestroy(): void {
    document.body.classList.remove('tree-dialog-open');
  }

  authoritiesAutocompleteFunc: (term: string) => Array<{ value: string, label: string }> = (term) => {
    return AuthorityService.filterAuthorities(term).map((value) => {
      return {value: value.id, label: `${value.id}-${value.name}`};
    });
  }

  legalStatusesAutocompleteFunc: (term: string) => Array<{ value: string, label: string }> = (term) => {
    if (term === null || term.trim() === '') {
      return this.legalStatuses;
    }
    const normalizedTerm = term.trim().toLowerCase();
    return this.legalStatuses.filter(entry => entry.label.toLowerCase().includes(normalizedTerm) || entry.value.toLowerCase() === normalizedTerm);
  }

  mainAreasAutocompleteFunc: (term: string) => Array<{ value: string, label: string }> = (term) => {
    if (term === null || term.trim() === '') {
      return this.mainAreas;
    }
    const normalizedTerm = term.trim().toLowerCase();
    return this.mainAreas.filter(entry => entry.label.toLowerCase().includes(normalizedTerm) || entry.value.toLowerCase() === normalizedTerm);
  }

  getAuthorityCodes(codes: string | Array<string>): Array<{ value: string, label: string }> {
    if (typeof (codes) === 'string') {
      codes = codes.split(',');
    }

    return AuthorityService.filterAuthoritiesByCodes(codes).map((value) => {
      return {value: value.id, label: `${value.id}-${value.name}`};
    });
  }

  getLegalStatuses(statuses: string | Array<string>): Array<{ value: string, label: string }> {
    if (statuses === null) {
      return [];
    }
    if (typeof(statuses) === 'string') {
      statuses = statuses.split(',');
    }
    return this.legalStatuses.filter(entry => statuses.includes(entry.value));
  }

  updateCpcIpcTag(event, field: string) {
    const text = event.target.value.trim().replace(',', '');
    if (text.length > 0) {
      event.stopPropagation();
      event.preventDefault();
      event.target.value = '';
      this.form.get(field).setValue([...this.form.get(field).value ? this.form.get(field).value : [], text]);
    }
  }

  onSearchClassificationFilter(input, field: string) {
    const valueArray = input.value.split(',');
    valueArray.filter(item => !input.results.includes(item.toUpperCase())).forEach(item => {
      this.errorClassificationFilter.push({field, value: item, message: 'Invalid classification code'});
    });
  }

  invalidClassificationTag = (item: string, field: string) => {
    if (this.errorClassificationFilter.length) {
      const error = this.errorClassificationFilter.find(o => o.field === field && o.value === item);
      return error?.message ?? error?.value;
    }
    return null;
  }

  getClassification(event: any[]){
    return event.length > 0 ? event.map(x => typeof x !== 'string'? x.value : x) : null;
  }

  onSaveFilterToProfile() {
    this.isSavingFilters = true;
    const search_filters = {...this.filtersFormGroup.value};
    Object.keys(search_filters).forEach(key => {
      if(search_filters[key]){
        search_filters[key] = key.indexOf('date') === -1 ? search_filters[key] : this.convertDate(search_filters[key]);
      } else {
        delete search_filters[key];
      }
    });
    this.userService.updateUISettings({search_filters}).subscribe({
      next: (data) => {
        this.isSavingFilters = false;
        this.commitFilter();
      }, error: error => {
        this.isSavingFilters = false;
        console.error(error);
      }
    });
  }

  convertDate(date): string {
    if (!date) {
      return '';
    }
    if (typeof date === 'string') {
      return date;
    }
    return date.year + '-' + date.month.toString().padStart(2, '0') + '-' + date.day.toString().padStart(2, '0');
  }

  getMainAreas(): Array<{ value: string, label: string }> {
    const form = this.filtersFormGroup;
    if (MAIN_TECHNOLOGY_AREA_KEYS.every(key => form.get(key).value)) {
      return [];
    }

    return MAIN_TECHNOLOGY_AREAS.filter(item => form.get(item.value).value);
  }

  updateMainArea(areas: string) {
    if (!areas) {
      for (let i = 1; i <= 5; i++) {
        this.filtersFormGroup.get('ma' + i).setValue(true);
      }
      return;
    }

    for (let i = 1; i <= 5; i++) {
      this.filtersFormGroup.get('ma' + i).setValue(areas.indexOf('ma' + i) > -1);
    }
  }

  clearFilters() {
    const {controls} = this.form;
    controls.search_filters.reset();
    controls.search_filters.patchValue({
      ma1: true,
      ma2: true,
      ma3: true,
      ma4: true,
      ma5: true
    });
    this.applicants_plus?.reset();
    this.applicants_minus?.reset();
    this.authorities_plus.reset();
    this.authorities_minus?.reset();
    this.legal_status?.reset();
    this.main_areas?.reset();
    this.cpcPlusFormControl?.reset();
    this.cpcMinusFormControl?.reset();
    this.ipcPlusFormControl?.reset();
    this.ipcMinusFormControl?.reset();
  }

  onCancel = () => {
    document.querySelector('.modal.show')?.classList.add('hide');
    setTimeout(() => {
      this.activeModal.dismiss();
    }, 50);
  }

  commitFilter = () => {
    document.querySelector('.modal.show')?.classList.add('hide');
    setTimeout(() => {
      this.activeModal.close();
    }, 50);
  }

  isFromModified(){
    return this.initialValues !== this.form.value;
  }

  hasFilters(): boolean {
    const searchFilters = this.filtersFormGroup;
    if (!searchFilters) {
      return;
    }
    if (Object.keys(searchFilters['controls']).some(key => {
      if (MAIN_TECHNOLOGY_AREA_KEYS.includes(key)) {
        return false;
      }

      if (key.indexOf('date') > -1 && searchFilters.get(key).value?.day > 0) {
        return true;
      } else if (searchFilters.get(key)?.value?.length > 0) {
        return true;
      }
    })) {
      return true;
    }
    if (!MAIN_TECHNOLOGY_AREA_KEYS.every(key => searchFilters.get(key).value)) {
      return true;
    }
    return false;
  }

  moveIntercomButton() {
    document.body.classList.add('tree-dialog-open');
    
    const originalDismiss = this.activeModal.dismiss;
    const originalClose = this.activeModal.close;
    
    this.activeModal.dismiss = (reason?: any) => {
      document.body.classList.remove('tree-dialog-open');
      return originalDismiss.call(this.activeModal, reason);
    };
    
    this.activeModal.close = (result?: any) => {
      document.body.classList.remove('tree-dialog-open');
      return originalClose.call(this.activeModal, result);
    };
  }
}
