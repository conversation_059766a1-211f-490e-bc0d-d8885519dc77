<div class="semantic-input-container" [formGroup]="form">
  <!--header-->
  <div class="d-flex justify-content-between align-items-center mx-1">
    <div class="col px-0 d-flex justify-content-between align-items-center select-language ps-1">
      <span class="text">Text</span>
      <div ngbDropdown #languageDrop="ngbDropdown" *ngIf="!userService.isFreeUser()" [class.me-3]="form.value.publications">
        <div ngbDropdownToggle class="caret-off">
          <div class="selected-item" #inputLanguageElement>
            <ng-container *ngIf="selectedLanguage">
              <b>{{ selectedLanguage.name}}</b><ng-container *ngIf="detectedLanguage"> (detected)</ng-container>
            </ng-container>
            <ng-container *ngIf="!selectedLanguage">Select language</ng-container>
          </div>
          <div class="dropdown-icon"></div>
        </div>
        <div ngbDropdownMenu>
          <a ngbDropdownItem class="p-2 d-flex align-items-center" *ngFor="let lang of inputLanguages"
             [ngClass]="{'selected-lang justify-content-between': selectedLanguage === lang, 'justify-content-start': selectedLanguage === lang}"
             (click)="onLanguageChanged(lang); inputLanguageElement.click()">
            <span>{{ lang.name }}</span>
            <i *ngIf="selectedLanguage === lang" class="fa-light fa-check ms-1"></i>
          </a>
          <hr class="mx-1">
          <label class="checkbox m-0 p-0">
            <input type="checkbox" (click)="onAutomaticTranslateClicked($event); languageDrop.close();" [checked]="translationsSetting">
            <span>Allow automatic translations always</span>
          </label>
        </div>
      </div>
    </div>
    <div class="col col-3" *ngIf="form.value.publications">
      <span class="text">Patent number</span>
    </div>
  </div>
  <!--input-->
  <div class="row d-flex m-0 m-b-spacing-big gap-spacing-big">
    <!--search input-->
    <div class="col d-flex align-items-start gap-spacing-xx-s semantic-input" data-intercom-target="text-input">
      <div class="button-square button-small btn-magnifying-glass" (click)="onSearch()" *ngIf="!hasExpandedText && isSemanticSearch">
        <i class="fa-regular fa-magnifying-glass"></i>
      </div>

      <div class="col p-0 input-container">
        <div class="text-improvement-container content-body-small m-b-spacing-sm" *ngIf="hasExpandedText" [@slideInAnimation]>
          <span class="color-grey-800">{{storeService.originalSearchInput}}</span>
          <button type="button" class="button-main-tertiary-grey content-label-small border-0" ngbTooltip="Search with your original text" (click)="onUseOriginal()">Use original</button>
        </div>
        <div class="d-flex gap-spacing-xx-s textarea-wrapper" [class.animate-textarea]="hasExpandedText">
          <div class="button-square button-small btn-expansion no-button text-not-changed" *ngIf="hasExpandedText && !isExpandedTextChanged"></div>

          <textarea #searchText id="searchText" formControlName="term"
                    placeholder="Describe what you are searching for and/or enter patent publication numbers."
                    [class]="'form-control psf-textarea mb-3 border-0 p-x-spacing-none' + (hasExpandedText || form.value.term? ' p-t-spacing-xx-s' : ' p-t-spacing-xxx-s')"
                    rows="5"
                    [ngClass]="{'psf-border-weighting': borderSizeText != '1px', 'm-l-spacing-none': isExpandedTextChanged}"
                    [readonly]="userService.isExternalUser()"
                    (focus)="onFocus()"
                    (blur)="onBlur()"
                    (keyup)="updateTextAreaHeight($event)">
          </textarea>
        </div>
      </div>
      <!--clear input-->
      <div class="btn-clear-input" *ngIf="!!form.value.term" [class.border-0]="!isSemanticSearch">
        <div id="cleanText" class="button-main-tertiary-grey button-square button-small" (click)="refreshTerm()" ngbTooltip="Clear">
          <i class="fa fa-regular fa-times"></i>
        </div>
      </div>
      <!--expansion button-->
      <ng-container *ngIf="isSemanticSearch">
        <div #expansionButton class="button-square button-small btn-expansion" [class.disabled]="disabledExpansionButton" (mouseenter)="toggleExpansionPopper($event)" (mouseleave)="closeExpansionPopper()">
        </div>
        <app-popper #expansionPopper customClass="expansion-popper" [fallbackPlacements]="['top', 'bottom']"
          (mouseenter)="blockClosing()"
          (mouseleave)="unblockClosing()">
          <ng-container [ngTemplateOutlet]="expansionPopperContent"></ng-container>
        </app-popper>
      </ng-container>
    </div>
    <!--patent numbers input-->
    <div class="col col-md-3 semantic-input" [hidden]="!form.value.publications">
      <div #patentNumbersText id="patentNumbers" contenteditable="true"
        class="form-control psf-textarea psf-textarea-number p-0 border-0" rows="6"
        [ngClass]="{'psf-border-weighting': borderSizeNumber != '1px'}"
        (keyup)="updateTextAreaHeight($event)"
        (input)="inputPublication($event)"
        [innerHTML]="highlightedPublicationNumbersHtml">
      </div>
    </div>
  </div>
  <!--weighting-->
  <div [hidden]="!form.value.publications || (!form.value.term && form.value.publications) || hiddenTextWeighting" class="pe-1 mb-3">
    <app-weighting-bar [weighting]="weighting" (weightingChange)="onWeightingChange($event)"></app-weighting-bar>
  </div>
</div>

<ng-template #expansionPopperContent>
  <div class="expansion-popper-content white-popover">
    <div class="popper-header d-flex align-items-center gap-spacing-xx-s m-b-spacing-sm">
      <i class="octi-icon-v5"></i>
      <span class="content-label-small color-grey-900">{{!isTextEnough ? 'Get Better Search Results!' : 'You are rocking it!'}}</span>
      <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon ms-auto" *ngIf="!isTextEnough" (click)="closeExpansionPopper(true)"></a>
    </div>
    <div class="popper-body p-spacing-none">
      <ng-container *ngIf="isTextEnough">
        <p class="content-body-xsmall m-0 content-color-secondary">The entered text or patent number has enough details to get solid results.</p>
      </ng-container>
      <ng-container *ngIf="!isTextEnough">
        <p class="content-body-xsmall m-b-spacing-x-s content-color-secondary">Short text may not give the best results due to lack of context. I'll automatically enhance it to better convey your idea and get better results.</p>
        <p class="content-body-xsmall m-b-spacing-sm content-color-secondary">You can always review and adjust it afterwards!</p>
        <div class="checkbox-container">
          <label class="checkbox m-0 p-0">
            <input type="checkbox" [(ngModel)]="executeExpandSearchInput" [ngModelOptions]="{standalone: true}" (ngModelChange)="onUpdateExpandSearchInput($event)">
            <span class="content-body-xsmall content-color-primary">Always improve short texts</span>
          </label>
        </div>
        <div class="d-flex justify-content-end m-t-spacing-md" *ngIf="isFirstTimeExpandSearchInput">
          <button type="button" class="button-main-primary button-medium content-body-small" (click)="searchFirstTime()">Search</button>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>
