@import 'scss/layout2021/variables';
@import 'scss/figma2023/variables';

.text {
  font-family: $font-open-sans-regular;
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.25rem;
  letter-spacing: 0em;
}

.select-language {
  font-family: $font-open-sans-regular;
  font-size: 0.9rem;
  line-height: 1.25rem;
  letter-spacing: 0em;
  color: $color-text-03;
  margin-bottom: 2px;

  .selected-item {
    font-weight: 400;
    text-align: end;
    padding-right: 1.8rem;
    border: none;
    cursor: pointer;
    outline: none;
  }

  .dropdown {
    border-radius: 4px;
    padding: 8px 16px;
    &:hover {
      background: $dropdown-item-background;
    }
  }

  .dropdown-menu {
    padding: 8px;
    left: -15px !important;
    top: 9px !important;
    min-width: 175px !important;
    a {
      color: $color-text-03;
      display: block;
      font-size: 0.875rem;
      line-height: 1.25rem;
      padding: 5px;
      cursor: pointer;
      &.active {
        color: $color-text-01;
      }
    }
  }
  .dropdown-item {
    &:hover {
      border-radius: 4px;
      background-color: $dropdown-item-background;
      color: $color-text-03 !important;
      font-size: 0.875rem;
    }

    &.selected-lang {
      color: $color-text-04;
      span {
        font-size: 0.875rem;
        font-weight: 600;
      }
    }
  }

  .checkbox {
    font-size: 0.875rem;
    color: $color-text-03;
    span {
      font-size: 0.875rem;
    }
  }
}

.btn-clear-input {
  padding-right: $spacing-system-spacing-xx-s;
  border-right: 1px solid $colours-border-subtle;
}

.feedback-bar {
  border-left: 1px solid;
  height: 231.99px;
  width: 5px;
  border-radius: 8px;
}

.semantic-input {
  position: relative;
  border-radius: $radius-big;
  padding: $spacing-system-spacing-md;
  padding-right: 0px;
  border: 1px solid $colours-border-subtle;
  min-height: 195px;

  .input-container {
    max-height: 600px;
    overflow-y: auto;
  }
}

.btn-magnifying-glass {
  display: flex;
  align-items: center;
  justify-content: center;
  color: $colours-buttons-main-tertiary-grey-content;
  position: absolute;
  top: $spacing-system-spacing-md;
  left: $spacing-system-spacing-md;
  z-index: 1;
}

.btn-clear-input {
  position: absolute;
  top: $spacing-system-spacing-md;
  right: 47px;
}

.btn-expansion {
  position: absolute;
  top: $spacing-system-spacing-md;
  right: $spacing-system-spacing-md;
  cursor: pointer;
  
  &.no-button {
    padding: 0;
    cursor: default;
  }
  
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $colours-content-content-active;
    -webkit-mask-image: url('/assets/images/regular-pen-sparkle.svg');
    mask-image: url('/assets/images/regular-pen-sparkle.svg');
    -webkit-mask-size: 20px;
    mask-size: 20px;
    -webkit-mask-position: center;
    mask-position: center;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    z-index: 1;
  }
  
  &:hover:not(.disabled, .no-button) {
    background-color: $colours-background-bg-brand;
  }
  
  &.disabled {
    cursor: default;
    
    &::before {
      background-color: $colours-buttons-main-tertiary-blue-content-disabled;
    }
  }

  &.text-not-changed{
    top: 0;
    left: 0;
  }
}

::ng-deep {
  .semantic-input {
    .placeholder-text {
      color: $colours-content-content-disabled;
      line-height: 20px;
      font-size: 14px;
    }

    &.psf-textarea {
      font-size: 14px;
      line-height: 20px;
    }
  }

  .highlight-text {
    background-color: #E8F0F3;
    border-radius: 2px;
    color: #000;
  }

  .highlight-patent-number {
    background-color: #CBDCE2;
    display: inline-flex !important;
    gap: 8px;
    border-radius: 4px;
    padding: 6px 8px 6px 8px;
    font-size: 14px;
    line-height: 20px;
  }
}

.psf-border-weighting {
  border: 2px solid $brand-green;
}


.text-improvement-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-system-spacing-xx-s;
  background-color: $colours-background-bg-secondary;
  border-radius: $radius-md;
  padding-left: $spacing-system-spacing-md;
  margin-right: 90px;
  margin-bottom: 8px;
  overflow: hidden;
  will-change: transform, opacity;
  transform-origin: right center;
  z-index: 2;
  position: absolute;
  top: 0;
  width: calc(100% - 90px);

  span {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    max-width: 89%;
  }
  .button-main-tertiary-grey {
    padding: $spacing-system-spacing-x-s $spacing-system-spacing-md;
  }
}

.input-container {
  position: relative;
  padding-top: 0;
}

.textarea-wrapper {
  transition: transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1) 50ms;
  transform: translateY(0);
  width: 100%;
}

.animate-textarea {
  transform: translateY(38px);
}

.psf-textarea {
  margin-left: 37px;
  margin-right: 90px;
  resize: none;
  overflow-y: hidden;
  
  &::placeholder {
    padding-top: $spacing-system-spacing-xx-s;
    color: $colours-content-content-disabled;
    line-height: 20px;
    font-size: 14px;
  }
  &.psf-textarea-number {
    min-height: 200px;
    max-height: 600px;
    margin-left: 0px;
  }
}

.expansion-popper-content {
  max-width: 293px;
  
  .popper-header {
    .octi-icon-v5 {
      width: 24px !important;
      height: 25px !important;
    }
  }

  .popper-body {
    padding: $spacing-system-spacing-sm;

    p {
      margin-bottom: $spacing-system-spacing-sm;
      color: $color-text-03;
      font-size: $font-size-sm;
      line-height: 1.4;

      &:last-of-type {
        margin-bottom: $spacing-system-spacing-md;
      }
    }

    .checkbox-container {
      display: flex;
      align-items: center;
      background-color: $colours-background-bg-secondary;
      border-radius: $radius-sm;
      padding: $spacing-system-spacing-sm;
      gap: $spacing-system-spacing-sm;

      .checkbox {
        span:before, span:after {
          top: -3px;
          padding-left: 0.2rem;
          width: 20px;
          height: 20px;
          border-radius: $radius-sm;

        }
        span:after {
          background-color: $colours-content-content-active;
        }
      }        
    }

    .button-main-primary {
      padding: $spacing-system-spacing-x-s $spacing-system-spacing-md;
    }
  }
}