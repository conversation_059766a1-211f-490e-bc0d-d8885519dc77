import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SemanticInputComponent } from './semantic-input.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { SharedModule } from '@shared/shared.module';
import { SemanticSearchStoreService } from '@core';

describe('SemanticInputComponent', () => {
  let component: SemanticInputComponent;
  let fixture: ComponentFixture<SemanticInputComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SemanticInputComponent],
      imports: [
        HttpClientTestingModule,
        FormsModule,
        SharedModule,
        ReactiveFormsModule,
        NgxSliderModule,
        RouterModule.forRoot([])
      ], providers: [ provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      }) ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SemanticInputComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
