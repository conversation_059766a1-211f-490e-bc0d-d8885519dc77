import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  ViewChild
} from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { extractPublications, UserService } from '@core/services';
import { ASIAN_ENGLISH_CODES, ENGLISH_LANG, sortedTranslatableLanguages } from '@core/data';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { BaseStoreService, DetectLanguageRequest, DetectLanguageResponse, LanguageService, SemanticSearchStoreService, User } from '@core';
import { TranslateLanguageDialogComponent } from '../translate-language-dialog/translate-language-dialog.component';
import { PopperComponent } from '@shared/components/popper/popper.component';
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  Observable,
  of,
  Subscription,
  switchMap,
  tap
} from 'rxjs';
import { filter, take } from 'rxjs/operators';
import { animate, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-semantic-input',
  templateUrl: './semantic-input.component.html',
  styleUrls: ['./semantic-input.component.scss'],
  animations: [
    trigger('slideInAnimation', [
      transition(':enter', [
        style({
          opacity: 0,
          transform: 'translateX(100%)'
        }),
        animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({
          opacity: 1,
          transform: 'translateX(0)'
        }))
      ])
    ])
  ]
})
export class SemanticInputComponent implements OnInit, OnDestroy {

  @Output() weightingChange = new EventEmitter<number>();
  @Output() inputChanged = new EventEmitter();
  @Output() submitInput = new EventEmitter<boolean>();
  @Output() selectedLanguageChange = new EventEmitter<{ code: string, name: string }>();
  @Output() selectedLanguageChanged = new EventEmitter<{ code: string, name: string }>();
  @Output() detectedLanguageChange = new EventEmitter<{ code: string, name: string }>();
  @Output() detectedLanguageChanged = new EventEmitter<{ code: string, name: string }>();
  @Output() detectingLanguage = new EventEmitter<boolean>();
  @Output() translationConfirmed = new EventEmitter<boolean>();

  @Input() selectedLanguage: { code: string, name: string };
  @Input() detectedLanguage: { code: string, name: string };
  @Input() hiddenTextWeighting: false;
  @Input()
  form: UntypedFormGroup = new UntypedFormGroup({
    term: new UntypedFormControl(''),
    publications: new UntypedFormControl('')
  });
  @Input() searchStarting = false;
  @Input() storeService: BaseStoreService;
  @Input() placeholder = 'Please enter a patent number (e.g. *********, ********* or WO2018158104) or any English text (e.g. the abstract of a patent, a scientific article or a product description).';

  debounce: any;
  debouncePublication: any;

  borderSizeText = '1px';
  borderSizeNumber = '1px';
  weighting = 4;

  private weightingArray = [5.0, 4.0, 3.0, 2.0, 1.0, 0.8, 0.6, 0.4, 0.2];
  private subscriptions = new Subscription();

  @ViewChild('searchFeedback') searchFeedbackEle: ElementRef;
  @ViewChild('searchText') searchTextEle: ElementRef;
  @ViewChild('patentNumbersText') patentNumbersTextEle: ElementRef;
  @ViewChild('expansionPopper') private expansionPopper: PopperComponent;
  @ViewChild('expansionButton') private expansionButton: ElementRef;

  user: User;
  inputEvaluationColor = {'background-color': '#FFF', 'border-color': '#FFF'};
  inputEvaluationTooltip: string;

  private LIMIT_WORD_SHORT_INPUT = 20;
  private LIMIT_WORD_AVERAGE_INPUT = 100;
  highlightedPublicationNumbersHtml: string | SafeHtml = '';
  listeners = [];

  private detectLanguageSubject = new BehaviorSubject<string>(null);
  inputLanguages = sortedTranslatableLanguages;
  isLanguageSelectedManually = false;

  private showTranslateBoxSubject = new BehaviorSubject<boolean>(false);
  private readonly showTranslateBox$ = this.showTranslateBoxSubject.asObservable();

  private _textWeighting: number;

  private readonly MAX_WORDS_FOR_IMPROVEMENT = 25;
  private isMouseOverPopper = false;
  private isMouseOverButton = false;

  @Input()
  set textWeighting(value: number) {
    this._textWeighting = value;

    if (this._textWeighting) {
      this.weighting = this.weightingArray.indexOf(this._textWeighting);
    }
    this.calculateBorder();
  }

  get textWeighting(): number {
    return this._textWeighting;
  }

  get translationsSetting(): boolean {
    return this.userService.getUISetting('semantic_automatic_translations', false);
  }

  get isFirstTimeExpandSearchInput(): boolean {
    return this.userService?.isFirstTimeExpandSearchInput();
  }

  get isTextShort(): boolean {
    return this.form.value.term.split(' ').length < this.MAX_WORDS_FOR_IMPROVEMENT;
  }

  get isTextEnough(): boolean {
    return !this.isTextShort || this.form.value.publications?.length > 0 || extractPublications(this.form.value.term).publications?.length > 0;
  }

  get hasExpandedText(): boolean {
    return !!this.storeService?.expandSearchInput;
  }

  get disabledExpansionButton(): boolean {
    return this.hasExpandedText || this.isTextEnough;
  }

  get isExpandedTextChanged(): boolean {
    return this.hasExpandedText && this.storeService?.expandSearchInput !== this.form.value.term;
  }

  get isSemanticSearch(): boolean {
    return this.storeService instanceof SemanticSearchStoreService;
  }

  get executeExpandSearchInput(): boolean {
    return this.storeService.executeExpandSearchInput;
  }

  set executeExpandSearchInput(val: boolean) {
    this.storeService.executeExpandSearchInput = val;
  }

  private isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;

  private currentTerm = '';
  private typedPublications = '';
  private lastKeyTyped = '';
  private isParseInputText = false;

  constructor(
    public userService: UserService,
    private modalService: NgbModal,
    private sanitizer: DomSanitizer,
    private renderer: Renderer2,
    private languageService: LanguageService,
  ) {}

  ngOnInit() {
    this.inputLanguages = [{code: '', name: 'Detect language'}, ...this.inputLanguages];

    this.user = this.userService.getUser();

    this.currentTerm = '';
    this.typedPublications = '';

    this.executeExpandSearchInput = this.userService.getUISetting('semantic_execute_expanded_search_input', true);
    const termChanges$ = this.form.get('term').valueChanges.subscribe({
      next: val => {
        if (val.trim() === this.currentTerm.trim()) {
          this.detectingLanguage.emit(false);
          return;
        }

        this.detectingLanguage.emit(true);

        if (this.debounce) {
          clearTimeout(this.debounce);
        }

        this.debounce = setTimeout(() => {
          if (extractPublications(val).remainingText.trim()) {
            this.startDetectSearchInputLanguage();
          } else {
            this.detectingLanguage.emit(false);
          }
          if (val === this.form.value.term) {
            this.parseInputText(val);
          }
        }, 1000);
      }
    });
    this.subscriptions.add(termChanges$);

    const publicationsChanges$ = this.form.get('publications').valueChanges.subscribe({
      next: val => {
        if (!val) {
          this.borderSizeText = '1px';
          this.borderSizeNumber = '1px';
          this.weighting = 4;
        }

        if (val === this.typedPublications) {
          return;
        }
        if (!val && this.isFirefox) {
          this.updateTextAreaHeight();
        }
        this.typedPublications = val;

        if (this.debouncePublication) {
          clearTimeout(this.debouncePublication);
        }

        this.debouncePublication = setTimeout(() => {
          this.parseInputPublications(val);
          this.startDetectSearchInputLanguage();
        }, 1000);

      }
    });
    this.subscriptions.add(publicationsChanges$);

    const detectLanguage$ = this.detectLanguageSubject.asObservable()
    .pipe(
      distinctUntilChanged((pv, cv)=>{
        if(cv.length>0 && this.selectedLanguage === null){
          return false;
        }
        return pv === cv;
      }),
      debounceTime(500),
      switchMap((val) => this.detectSearchInputLanguage(val))
    )
    .subscribe();
    this.subscriptions.add(detectLanguage$);

    const showTranslateBox$ = this.showTranslateBox$
      .pipe(
        filter((val) => val),
        distinctUntilChanged(),
        debounceTime(100),
        switchMap((val) => this.detectSearchInputLanguage(this.getTextToDetectLanguage()))
      )
      .subscribe({
        next: () => {
          this.openTranslationDialog();
        }
      });
    this.subscriptions.add(showTranslateBox$);

    const addNumberToSearch$ = this.storeService?.addNumberToSearch$.subscribe({
      next: publications => {
        if (publications?.length > 0 && this.form.value.term) {
          this.addToSearch(publications);
          this.storeService.addNumberToSearch = [];
        }
      }
    });
    this.subscriptions.add(addNumberToSearch$);

    if (this.form.value.term || this.form.value.publications) {
      setTimeout(() => {
        this.form.value.term ? this.parseInputText(this.form.value.term) : this.parseInputPublications(this.form.value.publications);
        this.focusInputText();
      });
     }
  }

  parseInputText(val) {
    const terms = extractPublications(val);
    if (this.form.get('term').value.trim() === this.currentTerm.trim()) {
      return;
    }

    let publicationsArr = [];
    let publicationsString = '';
    if (this.form.get('publications').value) {
      publicationsArr = this.form.get('publications').value.split(',');
    }
    publicationsArr = [...new Set(publicationsArr.concat(terms.publications))];
    publicationsString = publicationsArr.join(',');

    this.storeService.typedPublications = publicationsString.split(',').filter(publ => publ.length);

    if (terms.remainingText.trim()) {
      this.form.get('term').setValue(terms.remainingText);
      this.currentTerm = terms.remainingText;

      this.typedPublications = publicationsString;
      this.form.get('publications').setValue(publicationsString);
      if (publicationsString) {
        this.isParseInputText = false;
        this.highlightedPublicationNumbersHtml = this.highlightPublications(publicationsString.replace(/,/g, ' '), publicationsArr, this.patentNumbersTextEle.nativeElement) ??
                                                  this.highlightedPublicationNumbersHtml;
        this.focusInputText();
        if (this.isFirefox) {
          setTimeout(() => {
            this.updateTextAreaHeight();
          });
        }
      } else {
        if (this.shouldEnableExpandSearchInput(terms)) {
          this.executeExpandSearchInput = true;
        }
      }
    }

    publicationsArr = terms.publications;
    if (!terms.remainingText.trim()) {
      this.currentTerm = publicationsString;
      if (this.form.get('publications').value) {
        this.typedPublications = '';
        this.form.get('publications').setValue('');
        this.form.get('term').setValue(publicationsString);
        publicationsArr = publicationsString.split(',');
        this.storeService.typedPublications = publicationsArr;
      }
    }

    this.evaluateInputText();
  }

  private shouldEnableExpandSearchInput(terms): boolean {
    return !this.hasExpandedText && terms.remainingText.trim() !== this.storeService.originalSearchInput?.trim() && this.executeExpandSearchInput;
  }

  parseInputPublications(val) {
    if (!val && this.isFirefox) {
      this.updateTextAreaHeight();
    }
    const terms = extractPublications(val);
    this.storeService.typedPublications = terms.publications;

    this.typedPublications = terms.publications.join(',');
    this.form.get('publications').setValue(this.typedPublications);

    if (!this.form.get('term').value && !terms.remainingText) {
      this.currentTerm = this.typedPublications;
      this.form.get('term').setValue(this.typedPublications);
      this.form.get('publications').setValue('');
      this.focusInputText();
      return;
    }

    if (terms.remainingText) {
      this.currentTerm = this.form.get('term').value + ' ' + terms.remainingText;
      this.form.get('term').setValue(this.currentTerm);
    }

    const publicationsToHighlight = [...new Set(terms.publications)];
    const patentBox = this.patentNumbersTextEle.nativeElement;
    this.highlightedPublicationNumbersHtml = this.highlightPublications(this.typedPublications.replace(/,/g, ' '), publicationsToHighlight, patentBox) ??
                                              this.highlightedPublicationNumbersHtml;

  }

  evaluateInputText() {
    if (!this.form.value.term.length && !this.storeService.typedPublications.length) {
      this.inputEvaluationColor = {'background-color': '#FFF', 'border-color': '#FFF'};
      this.inputEvaluationTooltip = null;
      return;
    }
    if (!this.storeService.typedPublications.length) {
      let totalWords = 0;
      const {term} = this.form.value;

      if (ASIAN_ENGLISH_CODES.includes(this.detectedLanguage?.code)) {
        totalWords = Array.from(term).filter(char => char !== ' ').length;
      } else {
        totalWords = term.split(' ').filter(wrd => wrd.trim()).length;
      }

      if ( totalWords < this.LIMIT_WORD_SHORT_INPUT) {
        this.inputEvaluationColor = {'background-color': '#FF4D55', 'border-color': '#DE434A'};
        this.inputEvaluationTooltip = `Your input seems short. The results of our search engine are also based on the quantity of information you provide.
                Try to insert at least ${this.LIMIT_WORD_SHORT_INPUT - totalWords} more words.`;
        return;
      }
      if (totalWords < this.LIMIT_WORD_AVERAGE_INPUT) {
        this.inputEvaluationColor = {'background-color': '#FF944D', 'border-color': '#DC8042'};
        this.inputEvaluationTooltip = `The more relevant input you provide, the more our search engine will understand what you are looking for.
                Try to reach the length of an abstract, by adding ${this.LIMIT_WORD_AVERAGE_INPUT - totalWords} more words.`;
        return;
      }
    }
    this.inputEvaluationColor = {'background-color': '#00D464', 'border-color': '#00B756'};
    this.inputEvaluationTooltip = `Excellent! Your input seems to be long enough to perform a search.
            Nevertheless, remember that adding patents from the result list and reiterating the search will move you even closer.`;
  }

  private calculateBorder() {
    this.borderSizeNumber = '1px';
    this.borderSizeText = '1px';

    if (this.weighting < 4) {
      this.borderSizeText = '2px';
    } else if (this.weighting > 4) {
      this.borderSizeNumber = '2px';
    }
  }

  onWeightingChange(value) {
    this.weighting = value;
    this.calculateBorder();

    this.weightingArray = [5.0, 4.0, 3.0, 2.0, 1.0, 0.8, 0.6, 0.4, 0.2];
    this.textWeighting = this.weightingArray[this.weighting];
    this.weightingChange.emit(this.textWeighting);
  }

  onSubmit() {
    this.submitInput.emit();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onAutomaticTranslateClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.translationsSetting) {
      this.userService.updateUISettings({semantic_automatic_translations: false}).subscribe();
      this.setSelectedLanguage(null);
      this.setDetectedLanguage(null);
    } else {
      this.showTranslateBoxSubject.next(true);
    }
  }

  updateTextAreaHeight(event?: KeyboardEvent) {
    this.lastKeyTyped = event?.code;

    const textArea = this.searchTextEle?.nativeElement;
    if (!textArea) return;

    const container = textArea.closest('.input-container');

    const isNearBottom = container &&
      (container.scrollHeight - container.scrollTop <= container.clientHeight + 20);

    textArea.style.height = 'auto';

    const newHeight = this.getTextAreaHeight(this.searchTextEle);
    textArea.style.height = newHeight + 'px';

    if (this.patentNumbersTextEle) {
      const patentTextarea = this.patentNumbersTextEle.nativeElement;
      patentTextarea.style.height = 'auto';
      const patentHeight = this.getTextAreaHeight(this.patentNumbersTextEle);
      patentTextarea.style.height = patentHeight + 'px';
    }

    if (this.searchFeedbackEle && textArea) {
      this.searchFeedbackEle.nativeElement.style.height = textArea.clientHeight + 'px';
    }

    if (container && isNearBottom) {
      requestAnimationFrame(() => {
        if (this.lastKeyTyped) {
          container.scrollTop = container.scrollHeight;
        }
      });
    }
  }

  private getTextAreaHeight(ele: ElementRef): number {
    if (!ele) {
      return 0;
    }

    const nativeEle = ele.nativeElement;
    nativeEle.style.height = 'inherit';

    const computed = window.getComputedStyle(nativeEle);

    return parseInt(computed.getPropertyValue('border-top-width'), 10)
      + nativeEle.scrollHeight
      + parseInt(computed.getPropertyValue('border-bottom-width'), 10)
      + parseInt(computed.getPropertyValue('padding-bottom'), 10)
      + parseInt(computed.getPropertyValue('margin-bottom'), 10);
  }

  inputText(event) {
    if (event.target.innerText.trim().length) {
      this.form.get('term').setValue(event.target.innerText.replace(/</g, '&lt;').replace(/>/g, '&gt;'));
    } else {
      this.form.get('term').setValue('');
    }
  }

  inputPublication(event) {
    this.form.get('publications').setValue(event.target.innerText.replace(/</g, '&lt;').replace(/>/g, '&gt;'));
  }

  private getSelectionRange() {
    const selection = window.getSelection();
    let savedRange: Range;
    if (selection.rangeCount > 0) {
      savedRange = selection.getRangeAt(0);
    } else {
      savedRange = document.createRange();
    }
    return {selection, savedRange};
  }

  private highlightPublications(text: string, publicationNumbers: string[], inputBox: HTMLDivElement): string | SafeHtml {
    const {selection, savedRange} = this.getSelectionRange();
    const isPatentNumbers = inputBox.id == 'patentNumbers';
    const highlightedText = publicationNumbers?.length > 0 ? this.highlightText(text, publicationNumbers, isPatentNumbers) : text;
    if (savedRange) {
      const currentOffset = this.calculateCurrentOffset(savedRange, inputBox);
      setTimeout(() => {
        this.restoreSelection(inputBox, currentOffset, selection, isPatentNumbers);
        if (isPatentNumbers) {
          this.addEventListeners(inputBox);
        }
      });
    }
    return highlightedText;
  }

  private highlightText(text: string, numbers: string[], isPatentNumbers: boolean): SafeHtml {
    let highlightedText = text;

    for (const number of numbers) {
      const pattern = number.trim().replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&');
      const regex = new RegExp('\\b' + pattern  + '\\b', 'gi');

      highlightedText = highlightedText.replace(regex, (match) => {
        let extraContent = "";
        let contentEditableAttribute = "";
        let classAttribute = "highlight-text px-1 py-0";

        if (isPatentNumbers) {
          extraContent = `<i contenteditable="true" class="fa fa-times patent-number ms-2 cursor-pointer" data-patent-number="${match}"></i>`;
          contentEditableAttribute = ' contenteditable="false"';
          classAttribute = "highlight-patent-number align-items-center mb-2";
        }
        return `<mark class="${classAttribute}"${contentEditableAttribute}>${match}${extraContent}</mark>`;
      });
    }
    highlightedText += isPatentNumbers ? '<br>' : '';
    return this.sanitizer.bypassSecurityTrustHtml(highlightedText);
  }

  private calculateCurrentOffset(savedRange: Range, div: HTMLDivElement): number {
    const preCaretRange = savedRange.cloneRange();
    preCaretRange.selectNodeContents(div);
    preCaretRange.setEnd(savedRange.endContainer, savedRange.endOffset);
    return preCaretRange.toString().length;
  }

  addEventListeners(inputBox: HTMLDivElement) {
    this.removeEventListeners();

    const elements = inputBox.querySelectorAll('.fa-times.patent-number');
    elements.forEach(element => {
      const listener = this.renderer.listen(element, 'click', (event) => {
        const patentNumber = event.target.getAttribute('data-patent-number');
        const publications = this.form.get('publications').value.split(',').filter(publ => publ !== patentNumber);
        this.form.get('publications').setValue(publications.join(','));
      });
      this.listeners.push(listener);
    });
  }

  removeEventListeners() {
    this.listeners.forEach(listener => listener());
    this.listeners = [];
  }

  private restoreSelection(div: HTMLDivElement, currentOffset: number, selection: Selection, isPatentNumbers: boolean): void {
    const [node, offset] = this.getNodeAtOffset(div, currentOffset);
    let range = document.createRange();
    if (isPatentNumbers || (!node && !isPatentNumbers)) {
      range.selectNodeContents(div);
      range.collapse(false);
    } else {
      range.setStart(node, offset);
      range.setEnd(node, offset);
      range.collapse(true);
    }
    selection.removeAllRanges();
    selection.addRange(range);
  }

  getNodeAtOffset(node: Node, offset: number): [Node | null, number] {
    if (node.nodeType === Node.TEXT_NODE) {
      if (offset > node.nodeValue.length) {
        offset -= node.nodeValue.length;
      } else {
        return [node, offset];
      }
    } else {
      for (let i = 0; i < node.childNodes.length; i++) {
        const childNode = node.childNodes[i];
        const [foundNode, newOffset] = this.getNodeAtOffset(childNode, offset);

        if (foundNode) {
          return [foundNode, newOffset];
        }

        offset = newOffset;
      }
    }

    return [null, offset];
  }

  refreshTerm(): void {
    const {controls} = this.form;
    controls.term.setValue('');
    this.parseInputText(this.form.get('term').value);
    this.storeService.originalSearchInput = '';
    this.storeService.expandSearchInput = '';
    this.updateTextAreaHeight();
    this.focusInputText();
  }

  onFocus(): void {
    const len = this.searchTextEle.nativeElement.value.length;
    this.searchTextEle.nativeElement.setSelectionRange(len, len);
  }

  onBlur(): void {
    if (!this.form.get('term').value && !this.form.get('publications').value) {
      return;
    }
    if (this.isParseInputText || (this.lastKeyTyped === 'Delete' || this.lastKeyTyped === 'Backspace')) {
      this.parseInputText(this.form.get('term').value);
    }
  }

  activateText() {
    this.focusInputText();
  }

  private focusInputText() {
    setTimeout(() => {
      this.searchTextEle.nativeElement.focus();
    });
  }

  private getTextToDetectLanguage(): string {
    const {term} = this.form.value;
    const extractedVal = extractPublications(term);
    const text = (extractedVal.remainingText || '').replace(/\\\\n/g, " ");
    return text.trim();
  }

  private startDetectSearchInputLanguage(): void {
    this.detectLanguageSubject.next(this.getTextToDetectLanguage());
  }

  private detectSearchInputLanguage(text: string): Observable<DetectLanguageResponse> {
    const shouldTranslate = text?.length > 0 && !this.isLanguageSelectedManually;
    if (!shouldTranslate) {
      this.detectingLanguage.emit(false);
      return of(null);
    }

    const payload = {
      text: text
    } as DetectLanguageRequest;

    this.setDetectedLanguage(null);

    return this.languageService.detectLanguage(payload).pipe(
      tap((val) => {
        if (val?.lang) {
          const lang = val.lang.toLowerCase();
          const foundLang = this.inputLanguages.find((l) => l.code.toLowerCase() === lang);
          this.setSelectedLanguage(foundLang);
          this.setDetectedLanguage(foundLang);

          this.evaluateInputText();
        } else {
          this.setSelectedLanguage(null);
          this.setDetectedLanguage(null);
        }
        this.detectingLanguage.emit(false);
      })
    );
  }

  onLanguageChanged(lang: { code: string; name: string }) {
    const isDetectLanguage = lang.code === '';
    this.setSelectedLanguage(isDetectLanguage ? null : lang);

    if (this.detectedLanguage && lang.code !== this.detectedLanguage?.code) {
      this.setDetectedLanguage(null);
    }

    this.isLanguageSelectedManually = !isDetectLanguage;

    if (isDetectLanguage) {
      this.startDetectSearchInputLanguage();
    }
  }

  private openTranslationDialog() {
    if (this.modalService.hasOpenModals()) {
      return;
    }

    this.modalService.dismissAll();

    const modal = this.modalService.open(TranslateLanguageDialogComponent, {
      modalDialogClass: 'translate-language-dialog',
      scrollable: false
    });
    modal.componentInstance.selectedLanguage = this.selectedLanguage;
    modal.componentInstance.detectedLanguage = this.detectedLanguage;
    modal.result.then((result) => {
      if (result) {
        this.setSelectedLanguage(result);
        this.translationConfirmed.emit(true);
      } else {
        this.setSelectedLanguage(null);
        this.setDetectedLanguage(null);
      }

      this.evaluateInputText();
    }, (reason) => {
      if (reason?.useOriginal) {
        this.setSelectedLanguage(ENGLISH_LANG);
        this.setDetectedLanguage(null);
        this.isLanguageSelectedManually = true;
      } else {
        this.setSelectedLanguage(null);
        this.setDetectedLanguage(null);
      }

      this.evaluateInputText();
    });
  }

  private setSelectedLanguage(lang: { code: string; name: string }) {
    this.selectedLanguage = lang;
    this.selectedLanguageChange.emit(lang);
    this.detectedLanguageChanged.emit(lang);
  }

  private setDetectedLanguage(lang: { code: string; name: string }) {
    this.detectedLanguage = lang;
    this.detectedLanguageChange.emit(lang);
    this.detectedLanguageChanged.emit(lang);
  }

  addToSearch(selectedPublications) {
    const term = this.form.value.term;
    const publications = this.form.value.publications ? this.form.value.publications : [];

    const numbersToAdd = selectedPublications.filter(number => term.indexOf(number) === -1 &&
      publications.indexOf(number) === -1);

    if (numbersToAdd.length === 0) {
      return;
    }
    this.form.get('term').setValue(term + ',' + numbersToAdd.join(','));
    this.parseInputText(this.form.get('term').value);
  }

  toggleExpansionPopper(event?: MouseEvent) {
    if (this.hasExpandedText && !this.isExpandedTextChanged) {
      return;
    }
    this.isMouseOverButton = true;
    const buttonElement = this.expansionButton.nativeElement as HTMLElement;
    if (!this.expansionPopper.isOpen) {
      this.expansionPopper.show(buttonElement);
    }
  }

  closeExpansionPopper(forceClose = false) {
    this.isMouseOverButton = false;
    setTimeout(() => {
      if (!this.isMouseOverButton && (!this.isMouseOverPopper || forceClose)) {
        this.expansionPopper.hide();
      }
    }, 50);
  }

  blockClosing() {
    this.isMouseOverPopper = true;
    this.expansionPopper.blockClosing();
  }

  unblockClosing() {
    this.isMouseOverPopper = false;
    this.expansionPopper.unblockClosing();
    setTimeout(() => {
      if (!this.isMouseOverButton && !this.isMouseOverPopper) {
        this.expansionPopper.hide();
      }
    }, 50);
  }

  onSearch() {
    if (!this.form.value.term) {
      return;
    }

    this.closeExpansionPopper(true);
    this.submitInput.emit();
  }

  searchFirstTime() {
    if (this.isFirstTimeExpandSearchInput) {
      this.userService.updateUISettings({semantic_execute_expanded_search_input: true}).subscribe(
        () => {
          this.onSearch();
        }
      );
    } else {
      this.onSearch();
    }
  }

  onUseOriginal() {
    this.storeService.searching = true;
    this.form.get('term').setValue(this.storeService.originalSearchInput);
    this.executeExpandSearchInput = false;
    this.storeService.expandSearchInput = '';
    this.updateTextAreaHeight();
    this.detectingLanguage.pipe(filter(value => !value), take(1)).subscribe(() => this.submitInput.emit());
  }

  onUpdateExpandSearchInput(val: boolean) {
    this.userService.updateUISettings({semantic_execute_expanded_search_input: val}).subscribe();
  }

  onPaste($event: ClipboardEvent) {
    $event.preventDefault();
    $event.stopPropagation();

    let text = $event.clipboardData.getData('text/plain')?.trim();
    if (!text?.length) {
      return;
    }

    text = text.normalize('NFKC');

    const textarea = this.searchTextEle.nativeElement;
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;

    const currentValue = this.form.get('term').value;
    const newValue = currentValue.substring(0, startPos) + text + currentValue.substring(endPos);
    this.form.get('term').setValue(newValue);

    setTimeout(() => {
      const newPosition = startPos + text.length;
      textarea.setSelectionRange(newPosition, newPosition);
    });
  }
}
