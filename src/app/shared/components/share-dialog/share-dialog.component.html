<div class="modal-header">
  <div class="modal-title">{{getTitle()}}</div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
  <app-alert type="danger" [message]="sharingErrorMessage" *ngIf="sharingErrorMessage"></app-alert>

  <div *ngIf="!isShareLink()" class="sc-container d-flex flex-column justify-content-start align-items-stretch shared-view">
    <app-alert type="success" [message]="sharingSuccessMessage" *ngIf="sharingSuccessMessage"></app-alert>

    <div class="d-flex flex-row justify-content-between align-items-stretch">
      <select *ngIf="showGroups" class="form-select filter-type-select" [(ngModel)]="filterType" (ngModelChange)="onFilterTypeChanged($event)">
        <option [ngValue]="filterTypeAll">All</option>
        <option [ngValue]="filterTypeUsers">Only users</option>
        <option [ngValue]="filterTypeGroups">Only groups</option>
      </select>

      <div class="mb-3 flex-fill search-users">
        <input class="form-control filter-users-input" type="text" [placeholder]="getFilterInputPlaceholder()"
               [(ngModel)]="searchName" (ngModelChange)="onFilterInputChanged()">
        <label class="checkbox select-all" [ngClass]="{'disabled': isSelectAllDisabled()}" *ngIf="isResourceOwner">
          <input type="checkbox" (click)="selectAll()" [(ngModel)]="isSelectedAll" [disabled]="isSelectAllDisabled()">
          <span>Select all</span>
        </label>
      </div>
    </div>

    <div class="mb-2" [ngClass]="{'invisible': selectedTeamUsers.length + selectedGroups.length <= 0}">
      <span>Selected: </span>
      <span *ngIf="selectedTeamUsers.length > 0" class="fw-bold">
        {{selectedTeamUsers.length}} user{{selectedTeamUsers.length > 1 ? 's' : ''}}
      </span>
      <ng-container *ngIf="selectedGroups.length > 0">
        <span *ngIf="selectedTeamUsers.length"> and </span>
        <span class="fw-bold">
          {{selectedGroups.length}} group{{selectedGroups.length > 1 ? 's' : ''}}
        </span>
      </ng-container>
    </div>

    <ng-container *ngIf="filteredSharedTeamUsers.length + filteredSharedGroups.length > 0 && !isLoadingShared">
      <div class="d-flex flex-row flex-wrap users-team shared-users mb-3">
        <ng-container *ngFor="let user of filteredSharedTeamUsers; let i = index;">
          <ng-container *ngIf="user.id !== resource?.user_id" [ngTemplateOutlet]="userTeamTemplate" [ngTemplateOutletContext]="{user, index: i, sharedUser: true}"></ng-container>
        </ng-container>

        <ng-container *ngFor="let group of filteredSharedGroups; let i = index;">
          <ng-container [ngTemplateOutlet]="userGroupTemplate" [ngTemplateOutletContext]="{group, index: i, sharedGroup: true}"></ng-container>
        </ng-container>
      </div>
    </ng-container>

    <ng-container *ngIf="(filteredTeamUsers.length || filteredGroups.length) && !isLoadingShared">
      <div class="d-flex flex-row flex-wrap users-team">
        <ng-container *ngIf="filterType !== filterTypeGroups">
          <ng-container *ngFor="let user of filteredTeamUsers; let i = index;">
            <ng-container *ngIf="user.id !== resource?.user_id" [ngTemplateOutlet]="userTeamTemplate" [ngTemplateOutletContext]="{user, index: i, sharedUser: false}"></ng-container>
          </ng-container>
        </ng-container>

        <ng-container *ngIf="filterType !== filterTypeUsers">
          <ng-container *ngFor="let group of filteredGroups; let i = index;">
            <ng-container [ngTemplateOutlet]="userGroupTemplate" [ngTemplateOutletContext]="{group, index: i, sharedGroup: false}"></ng-container>
          </ng-container>
        </ng-container>
      </div>
    </ng-container>
    <ng-container *ngIf="!isLoadingShared && !isShareLink()">
      <div class="permission-switch" *ngIf="canSwitchPermission()">
        <div *ngIf="isCollectionResource" >Allow team members to add and remove patents. </div>
        <div *ngIf="isFolderResource" >Allow team members to edit this folder and any folder / list inside it. </div>
        <div class="m-l-spacing-big">
          <div ngbDropdown >
            <div ngbDropdownToggle class="form-control permission-selector caret-off" [class.disabled]="isSavingShare || (selectedTeamUsers.length + selectedGroups.length === 0 && sharedGroups.length + sharedTeamUsers.length === 0)" >
              <span class="">{{permission === CollaborationEnum.READ_WRITE ? 'Yes': 'No'}}</span>
              <span class="dropdown-icon"></span>
            </div>
            <div ngbDropdownMenu>
              <div ngbDropdownItem [disabled]="isSavingShare || (selectedTeamUsers.length + selectedGroups.length === 0 && sharedGroups.length + sharedTeamUsers.length === 0)" [class.active]="permission === CollaborationEnum.READ_WRITE" (click)="permission = CollaborationEnum.READ_WRITE">Yes</div>
              <div ngbDropdownItem [disabled]="isSavingShare || (selectedTeamUsers.length + selectedGroups.length === 0 && sharedGroups.length + sharedTeamUsers.length === 0)" [class.active]="permission === CollaborationEnum.READONLY" (click)="permission = CollaborationEnum.READONLY">No</div>
            </div>
          </div>
        </div>
      </div>
      <div class="permission-switch" *ngIf="!isResourceOwner && isReadOnlyCollection">
        <div>The shared collection will be read-only.</div>
      </div>
    </ng-container>

    <div *ngIf="isLoadingShared" class="d-flex justify-content-center align-items-center">
      <img src="assets/images/octimine_blue_spinner.gif" width="98px">
    </div>
  </div>

  <div *ngIf="isShareLink()" class="sc-container d-flex flex-column justify-content-start align-items-stretch">
    <div *ngIf="!isSharePatentLink" class="mb-3">
      <input autocomplete="off" class="form-control" name="title"
             placeholder="Please insert a title for sharing." type="text"
             [readonly]="nameReadOnly"
             [formControl]="titleFormControl" maxlength="255"
             [ngClass]="{'is-invalid': titleFormControl.touched && titleFormControl.invalid}"/>
      <div class="invalid-feedback">
        Please provide a title for sharing.
      </div>
    </div>

    <div class="row">
      <div class="col-9" [ngClass]="hasSharedLink ?'col-9': 'col-12'">
        <textarea #description autocomplete="off" class="form-control shared-link-text" name="description"
                  placeholder="Here you can write your own description and remarks about this search."
                  [readonly]="true" [(ngModel)]="savedSearch.description" [rows]="hasSharedLink ? 1 : 4" disabled>
        </textarea>
      </div>
      <div class="col-3" *ngIf="hasSharedLink">
        <a href="javascript:void(0)" (click)="copyShareLink()" ngbTooltip="Copy share link" class="text-green">
          <i class="fa-duotone fa-copy fa-2x"></i>
        </a>
      </div>
    </div>

    <div *ngIf="!isSharePatentLink" class="mb-3">
      <label class="d-flex justify-content-start align-items-center mt-2"
             [ngClass]="{'is-invalid': linkDaysFormControl.invalid}">
        <div>This link will expire after</div>
        <input type="number" min="1" max="365" [formControl]="linkDaysFormControl" class="form-control sc-number-days ms-1 me-1"/>
        <div>days</div>
      </label>
      <div class="invalid-feedback">
        The expiring days must be between 1 and 365.
      </div>
    </div>
  </div>
</div>

<div class="modal-footer">
  <div class="d-flex justify-content-between align-items-center w-100">
    <div class="modal-hint flex-fill">
      <div class="d-flex justify-content-start align-items-center me-1" [hidden]="hideAgreementCheck" *ngIf="isShareLink()">
        <label class="checkbox m-0 p-0">
          <input id="has-agreement" type="checkbox" [(ngModel)]="hasAgreement"/>
          <span>&nbsp;</span>
        </label>
        <label for="has-agreement" class="m-0">All information above will be saved in the database. Do you
          agree?</label>
      </div>

      <a *ngIf="!isShareLink() && canSwitchShareType" href="javascript:void(0)"
         (click)="switchShareType()">Share outside your team as a link ...</a>
      <a *ngIf="isShareLink() && hasWorkflowFeature() && canSwitchShareType"
         href="javascript:void(0)" (click)="switchShareType()">Share with your team instead
        ...</a>
    </div>

    <img src="/assets/images/octimine_blue_spinner.gif" style="width: 35px;" class="me-2" *ngIf="isSavingShare">

    <ng-container *ngIf="!isSharePatentLink">
      <button *ngIf="stopSharing" class="btn btn-md btn-primary me-2"
              [disabled]="!hasShared() || isSavingShare || !isResourceOwner" (click)="onStopShare()">Stop sharing
      </button>

      <button *ngIf="isShareLink()" class="btn btn-md btn-primary btn-share-by-link"
              [disabled]="(isFormInvalid() && !hideAgreementCheck) || (linkDaysFormControl.invalid || (hasSharedLink && !this.linkDaysFormControl.dirty)) || isSavingShare"
              (click)="onShare()" ngbTooltip="Share & Copy">Save
      </button>

      <button *ngIf="!isShareLink()" class="btn btn-md btn-primary btn-share-with-team"
              [disabled]="isSavingShare || (selectedTeamUsers.length + selectedGroups.length === 0 && sharedGroups.length + sharedTeamUsers.length === 0)"
              (click)="onShare()">{{shareText}}
      </button>
    </ng-container>
  </div>
</div>

<ng-template #upgradeUserTooltip>
  <div>This user needs to be upgraded to send monitor profile</div>
</ng-template>

<ng-template #userTeamTemplate let-user="user" let-index="index" let-sharedUser="sharedUser">
  <div class="col-6 d-flex flex-row user-team ps-0" [ngClass]="{'pe-0': index % 2 !== 0}"
       [ngbTooltip]="isUserDisabled(user) ? upgradeUserTooltip : null" container="body">
    <label class="checkbox" [ngClass]="{disabled: isUserDisabled(user) || (!isResourceOwner && sharedUser)}">
      <input type="checkbox" (click)="selectTeamUser(user)" [checked]="isSelectedUser(user)" [disabled]="isUserDisabled(user) || (!isResourceOwner && sharedUser)"/>
      <span class="no-text">&nbsp;</span>
    </label>
    <app-user-avatar [user]="user" [hasSubTitle]="false" [avatarSize]="30" [avatarFontSize]="15"></app-user-avatar>
    <span class="p-l-spacing-xx-s">
      {{ getUserName(user) }}
      <ng-container *ngIf="isUserDisabled(user) && showSubscriptionType && user.subscription?.type">({{user.subscription?.type}})</ng-container>
    </span>
  </div>
</ng-template>

<ng-template #userGroupTemplate let-group="group" let-index="index" let-sharedGroup="sharedGroup">
  <div class="col-6 d-flex flex-row user-group ps-0" [ngClass]="{'pe-0': index % 2 === 0}">
    <label class="checkbox" [class.disabled]="!isResourceOwner && sharedGroup">
      <input type="checkbox" (click)="selectGroup(group)" [checked]="isSelectedGroup(group)" [disabled]="!isResourceOwner && sharedGroup"/>
      <span class="no-text">&nbsp;</span>
    </label>
    <app-user-avatar [user]="group" [hasSubTitle]="false" [avatarSize]="30" [avatarFontSize]="15">
      <i class="fas fa-users user-group-icon"></i>
    </app-user-avatar>
    <span class="p-l-spacing-xx-s">{{ group.name }}</span>
  </div>
</ng-template>
