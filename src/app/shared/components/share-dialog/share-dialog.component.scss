@import 'scss/components/autocomplete';
@import 'scss/layout2021/variables';


.sc-container {
  .sc-number-days {
    width: 75px;
  }

  .sc-users-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .autocomplete {
    .autocomplete-spinner {
      right: 25px !important;
    }

    .fa-caret-down {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 20px;
      cursor: pointer;
      color: $dropdown-link-hover-color;
    }
  }

}

.users-team {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 1rem;

  .user-team, .user-group {
    margin-bottom: 10px;
    label, span{
      padding-top: 2px;
    }
  }

  .user-group {
    app-user-avatar {
      position: relative;
    }

    .user-group-icon {
      position: absolute;
      top: -1px;
      left: 20px;
      border: 1.5px solid white;
      border-radius: 50%;
      color: white;
      width: 15px;
      height: 15px;
      font-size: 15px;
      padding: 5px;
      background-color: $color-text-05;

      &:before {
        width: 15px !important;
        height: 15px !important;
        font-size: 8px;
        padding: 0;
        top: 2px;
        right: -4px;
        position: absolute;
        color: white;
      }
    }
  }
}

.filter-type-select {
  max-width: 140px;
  margin-right: 10px;
  height: 38px;
}

.search-users {
  position: relative;

  .filter-users-input {
    padding-right: 110px;
  }

  .select-all {
    position: absolute;
    top: 6px;
    right: 10px;

    span {
      color: #00a083;
    }
  }
}

.shared-users {
  border-bottom: solid 1px #cecece;
}

.shared-view {
  min-height: 544px;
}
.permission-switch {
  align-items: center;
  display: flex;
  flex-direction: row;
  margin-top: auto;
  border-top: 1px solid #cecece;
  padding-top: 1rem;
}
.permission-selector{
  min-width: 150px;
  &.disabled{
    color: #fff;
    border-color: #BCCACE;
    pointer-events: none;
    background-color: #BCCACE;
    opacity: .65;
    .dropdown-icon{
      display: none;
    }
  }
  &:not(.disabled){
    cursor: pointer;
  }
}
