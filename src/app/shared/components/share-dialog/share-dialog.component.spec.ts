import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { NgControl } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { RouterModule } from '@angular/router';
import { UserTitlePipe } from '@core/pipes';
import { ShareDialogComponent } from '@shared/components';
import { SharedModule } from '@shared/shared.module';
import { SemanticSearchStoreService } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('SaveSearchComponent', () => {
  let component: ShareDialogComponent;
  let fixture: ComponentFixture<ShareDialogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      providers: [NgbActiveModal, NgControl, UserTitlePipe, SemanticSearchStoreService, provideMatomo({
        siteId: '7',
        trackerUrl: 'https://stats.dennemeyer.digital/',
        disabled: true
      })],
      declarations: [ShareDialogComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ShareDialogComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
