import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormControl, Validators } from '@angular/forms';
import { forkJoin, of, Observable, Subscription } from 'rxjs';
import { finalize, map, mergeMap } from 'rxjs/operators';
import {
  AvatarService,
  BooleanSearchService,
  CitationSearchService,
  CollaborationService,
  Collection,
  CollectionService,
  CollectionsResponse,
  GroupService,
  LandscapeService,
  MonitorService, PatentListScopeEnum,
  SemanticSearchService,
  UserService
} from '@core/services';
import { TruncatePipe, UsersTitleTextPipe } from '@core/pipes';
import { BaseStoreService, CollectionStoreService, MonitorLegalStoreService, MonitorStoreService } from '@core/store';
import {
    CollaborationPermissionEnum,
  CollaborationResourceTypeEnum,
  CollaborationStatusEnum,
  SubscriptionType,
  TeamUser,
  UserGroup
} from '@core/models';
import _ from 'lodash';
import { ShareDialogResult, ShareDialogShareTypeEnum } from './types';
import copy from 'copy-to-clipboard';

@Component({
  selector: 'app-share-dialog',
  templateUrl: './share-dialog.component.html',
  styleUrls: ['./share-dialog.component.scss']
})
export class ShareDialogComponent implements OnInit, OnDestroy {
  @Input() storeService: BaseStoreService;
  textInput: string;
  nameReadOnly?: boolean;
  name?: string;
  shareUrl = '/collections/shared/';
  resource?: Collection;

  hasAgreement = false;
  headline?: string;
  hideAgreementCheck = false;
  hasSharedLink: boolean;

  linkDays = 10;

  savedSearch = {
    title: '',
    description: '',
    shareCode: ''
  };

  titleFormControl = new UntypedFormControl('', [Validators.required]);
  linkDaysFormControl = new UntypedFormControl(10, [Validators.required, Validators.min(1)]);

  @ViewChild('description') description: ElementRef;

  resourceId?: number;
  resourceType?: CollaborationResourceTypeEnum;
  permission: CollaborationPermissionEnum = CollaborationPermissionEnum.READ_WRITE;
  showPermissionSwitch: boolean = true;
  shareType = ShareDialogShareTypeEnum.SHARE_LINK;
  canSwitchShareType = true;
  filteredTeamUsers: Array<TeamUser> = [];
  allTeamUsers: Array<TeamUser> = [];
  selectedTeamUsers: Array<TeamUser> = [];
  sharedTeamUsers: Array<TeamUser> = [];
  filteredSharedTeamUsers: Array<TeamUser> = [];
  filteredGroups: Array<UserGroup> = [];
  allGroups: Array<UserGroup> = [];
  selectedGroups: Array<UserGroup> = [];
  sharedGroups: Array<UserGroup> = [];
  filteredSharedGroups: Array<UserGroup> = [];
  sharingErrorMessage: string = null;
  sharingSuccessMessage: string = null;
  isLoadingShared: boolean;
  isSavingShare = false;
  createNewCollection = false;
  searchService: SemanticSearchService | CitationSearchService | BooleanSearchService | CollectionService | MonitorService | LandscapeService;
  isSelectedAll = false;
  searchName = '';
  stopSharing = true;
  shareText = 'Share';
  disabledSubscriptionTypes: SubscriptionType[];
  showSubscriptionType = false;
  showGroups = true;
  filterTypeAll = 'all';
  filterTypeUsers = 'only_users';
  filterTypeGroups = 'only_groups';
  filterType = this.filterTypeAll;
  patentListScope: PatentListScopeEnum = PatentListScopeEnum.FAMILY;
  isResourceOwner: boolean = true;
  CollaborationEnum = CollaborationPermissionEnum

  private subscriptions = new Subscription();

  get isSharePatentLink(): boolean {
    return this.resourceType === CollaborationResourceTypeEnum.PATENT &&
      this.shareType === ShareDialogShareTypeEnum.SHARE_LINK;
  }

  constructor(
    public activeModal: NgbActiveModal,
    public userService: UserService,
    public groupService: GroupService,
    public avatarService: AvatarService,
    private collaborationService: CollaborationService,
    private monitorStoreService: MonitorStoreService,
    private monitorLegalStoreService: MonitorLegalStoreService,
    private collectionService: CollectionService,
    private collectionsStoreService: CollectionStoreService,
    private truncatePipe: TruncatePipe,
    private usersTitleTextPipe: UsersTitleTextPipe
  ) {
    if (this.hasWorkflowFeature()) {
      this.shareType = ShareDialogShareTypeEnum.SHARE_WITH_TEAM;
    }
  }

  private static generateShareCode() {
    let code = new Date().getTime().toString();
    while (code.length < 16) {
      code += String.fromCharCode(Math.floor(Math.random() * (90 - 65 + 1)) + 65);
    }
    return code;
  }

  get isFamilyCollectionType() {
    return this.patentListScope === PatentListScopeEnum.FAMILY;
  }

  get isReadOnlyCollection() {
    return this.permission === CollaborationPermissionEnum.READONLY;
  }

  get isCollectionResource(): boolean{
    return this.resourceType === CollaborationResourceTypeEnum.COLLECTION;
  }

  get isFolderResource(): boolean{
    return this.resourceType === CollaborationResourceTypeEnum.FOLDER;
  }

  ngOnInit() {
    if (this.resource) {
      this.savedSearch.title = this.resource.name;
      this.titleFormControl.setValue(this.resource.name);
      if (this.resource.share_code && this.resource.expires_at) {
        const diffDays = Math.ceil((Date.parse(this.resource.expires_at) - Date.now()) / (1000 * 60 * 60 * 24));
        if (diffDays > 0) {
          this.linkDaysFormControl.setValue(diffDays);
          if (this.resource.users && this.resource.users.length === 0 && this.shareType === ShareDialogShareTypeEnum.SHARE_WITH_TEAM) {
            this.switchShareType();
          }
        }
      }
      if (this.resourceType !== CollaborationResourceTypeEnum.PATENT) {
        this.isResourceOwner = this.resource.user_id === this.userService.getUser()?.profile?.id;
      }
      this.savedSearch.shareCode = this.resource.share_code ? this.resource.share_code : ShareDialogComponent.generateShareCode();
    } else {
      this.savedSearch.shareCode = ShareDialogComponent.generateShareCode();
    }

    if (this.resourceType === CollaborationResourceTypeEnum.PATENT) {
      this.savedSearch.description = `${window.location.origin}${this.shareUrl}${this.resourceId}`;
    } else {
      this.savedSearch.description = `${window.location.origin}${this.shareUrl}${this.savedSearch.shareCode}`;
    }

    this.loadAllUsersAndGroups();
  }

  loadAllUsersAndGroups(){
    this.isLoadingShared = true;
    const obs$ = forkJoin([this.userService.getTeamUsers({'load_all': 1}), this.groupService.getGroups({load_all: 1})])
    .pipe(finalize(() => this.isLoadingShared = false)).subscribe({
      next: (response) => {
        this.allTeamUsers = response[0].users;
        this.filteredTeamUsers = response[0].users;
        this.allGroups = response[1].groups;
        this.filteredGroups = response[1].groups;

        this.loadCollaborations();
      }
    });
    this.subscriptions.add(obs$);
  }

  loadCollaborations(){
    if (this.shareType === ShareDialogShareTypeEnum.SHARE_WITH_TEAM && this.resourceId) {
      this.isLoadingShared = true;
      const getCollaboration$ = this.collaborationService.getCollaboration(this.resourceId, this.resourceType)
        .pipe(finalize(() => this.isLoadingShared = false))
        .subscribe({
          next: ({users, groups}) => {
            this.selectedTeamUsers = users.filter(u => u.collaboration.status !== CollaborationStatusEnum.UNSHARED);
            this.sharedTeamUsers = [...this.selectedTeamUsers];
            this.filteredSharedTeamUsers = [...this.selectedTeamUsers];
            this.filteredTeamUsers = this.filteredTeamUsers.filter(u => !this.isSharedTeamUser(u));


            this.selectedGroups = groups.filter(u => u.collaboration.status !== CollaborationStatusEnum.UNSHARED);
            this.sharedGroups = [...this.selectedGroups];
            this.filteredSharedGroups = [...this.selectedGroups];
            this.filteredGroups = this.filteredGroups.filter(u => !this.isSharedUserGroup(u));
            if(this.isResourceOwner && (this.filteredSharedTeamUsers[0] || this.filteredSharedGroups[0])){
              this.permission = (this.filteredSharedTeamUsers[0] || this.filteredSharedGroups[0]).collaboration.permission
            }
          }
        });
      this.subscriptions.add(getCollaboration$);
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  canSwitchPermission(): boolean{
    return this.isResourceOwner && this.showPermissionSwitch && !(this.searchService instanceof LandscapeService) && !(this.searchService instanceof MonitorService);
  }

  getTitle() {
    return this.headline ? this.headline : this.hasShared() ? 'Save and share your search' : `Share ${this.getNumberOfDocumentsSelected()} document(s)`;
  }

  isFormInvalid() {
    const linkDaysInvalid = this.hasShared() ? this.linkDaysFormControl.invalid : false;
    return this.titleFormControl.invalid || !this.hasAgreement || linkDaysInvalid;
  }

  isShareLink() {
    return this.shareType === ShareDialogShareTypeEnum.SHARE_LINK;
  }

  selectTeamUser(user: TeamUser) {
    if (this.isSelectedUser(user)) {
      this.selectedTeamUsers = this.selectedTeamUsers.filter(u => user.id !== u.id);
    } else {
      this.selectedTeamUsers.unshift(user);
    }

    this.updateIsSelectedAll(this.filterType);
  }

  isSelectedUser(user: TeamUser) {
    return this.selectedTeamUsers.find(u => user.id === u.id) !== undefined;
  }

  selectGroup(group: UserGroup) {
    if (this.isSelectedGroup(group)) {
      this.selectedGroups = this.selectedGroups.filter(u => group.id !== u.id);
    } else {
      this.selectedGroups.unshift(group);
    }

    this.updateIsSelectedAll(this.filterType);
  }

  isSelectedGroup(group: UserGroup) {
    return this.selectedGroups.find(u => group.id === u.id) !== undefined;
  }

  switchShareType() {
    this.shareType = this.isShareLink() ?
      ShareDialogShareTypeEnum.SHARE_WITH_TEAM : ShareDialogShareTypeEnum.SHARE_LINK;
  }

  onShare() {
    this.isSavingShare = true;
    if (this.createNewCollection) {
      if (this.isMonitorService()) {
        this.saveNewCollectionForMonitorRuns();
      } else {
        this.saveNewCollection();
      }
    } else {
      if (this.isShareLink()) {
        this.onShareLink(this.resourceId);
      } else {
        this.onShareWithTeamUsers();
      }
    }
  }

  copyShareLink() {
    copy(this.description.nativeElement.value);
  }

  onStopShare() {
    if (this.isShareLink()) {
      this.savedSearch.shareCode = null;
      this.savedSearch['valid_to'] = null;
      this.onSaveShare(this.resourceId);
    } else {
      this.selectedTeamUsers = [];
      this.selectedGroups = [];
      this.onShareWithTeamUsers();
    }
  }

  hasWorkflowFeature(): boolean {
    return this.userService.canUseWorkflowFeature();
  }

  selectAll() {
    this.isSelectedAll = !this.isSelectedAll;

    if (this.filterType !== this.filterTypeGroups) {
      if (this.isSelectedAll) {
        this.selectedTeamUsers = [...this.filteredTeamUsers, ...this.filteredSharedTeamUsers];
      } else {
        this.selectedTeamUsers = [];
      }
    }

    if (this.filterType !== this.filterTypeUsers) {
      if (this.isSelectedAll) {
        this.selectedGroups = [...this.filteredGroups, ...this.filteredSharedGroups];
      } else {
        this.selectedGroups = [];
      }
    }
  }

  onFilterInputChanged(): void {
    this.filteredTeamUsers = this.allTeamUsers.filter(u => this.validateUserName(u) && !this.isSharedTeamUser(u));
    this.filteredSharedTeamUsers = this.sharedTeamUsers.filter(u => this.validateUserName(u));

    this.filteredGroups = this.allGroups.filter(g => this.validateGroupName(g) && !this.isSharedUserGroup(g));
    this.filteredSharedGroups = this.sharedGroups.filter(g => this.validateGroupName(g));

    this.updateIsSelectedAll(this.filterType);
  }

  hasShared(): boolean {
    if (this.isShareLink()) {
      return !!(this.resource?.share_code && this.resource?.expires_at);
    }
    return this.sharedTeamUsers.length + this.sharedGroups.length > 0;
  }

  getUserName(user: TeamUser): string {
    if (this.hasName(user)) {
      return (user.first_name || '') + ' ' + (user.last_name || '');
    }

    return user.email;
  }

  isUserDisabled(user: TeamUser): boolean {
    if (typeof (this.disabledSubscriptionTypes) !== 'undefined' && user.subscription?.type) {
      return this.disabledSubscriptionTypes.includes(user.subscription.type);
    }
    return false;
  }

  getFilterInputPlaceholder(): string {
    switch (this.filterType) {
      case this.filterTypeAll:
        return 'Search team members or groups';
      case this.filterTypeUsers:
        return 'Search team members';
      case this.filterTypeGroups:
        return 'Search user groups';
    }
  }

  isSelectAllDisabled(): boolean {
    if(!this.isResourceOwner){
      return true;
    }
    switch (this.filterType) {
      case this.filterTypeAll:
        return this.filteredTeamUsers.length + this.filteredGroups.length === 0;
      case this.filterTypeUsers:
        return this.filteredTeamUsers.length === 0;
      case this.filterTypeGroups:
        return this.filteredGroups.length === 0;
    }
  }

  onFilterTypeChanged(val) {
    this.updateIsSelectedAll(val);
  }

  private onShareWithTeamUsers() {
    this.sharingErrorMessage = null;
    this.sharingSuccessMessage = null;

    if (this.selectedTeamUsers.length + this.selectedGroups.length === 0 && !this.hasShared()) {
      this.isSavingShare = false;
      this.sharingErrorMessage = 'You have to choose some users to share this collection';
      return;
    }

    if (this.resourceId) {
      const selectedUserIds = this.selectedTeamUsers.map(u => u.id);
      const selectedGroupIds = this.selectedGroups.map(g => g.id);
      const payload = {
        resource_id: this.resourceId,
        resource_type: this.resourceType,
        permission: this.permission,
        user_ids: selectedUserIds.length > 0 ? selectedUserIds : null,
        group_ids: selectedGroupIds.length > 0 ? selectedGroupIds : null
      };

      this.isSavingShare = true;
      const create$ = this.collaborationService.create(payload)
        .pipe(finalize(() => this.isSavingShare = false))
        .subscribe({
          next: val => {
            this.onShareWithTeamUsersSuccess(val);
          }
        });
      this.subscriptions.add(create$);
    } else {
      this.activeModal.close({shared_users: this.selectedTeamUsers, shared_groups: this.selectedGroups} as ShareDialogResult);
    }
  }

  private onShareLink(resourceId) {
    this.linkDays = this.linkDaysFormControl.value;
    if (this.linkDays > 0) {
      const d = new Date();
      d.setDate(d.getDate() + this.linkDays);
      this.savedSearch['valid_to'] = d.toISOString();
    } else {
      this.isSavingShare = false;
      return;
    }
    this.copyShareLink();

    this.savedSearch.title = this.titleFormControl.value;
    this.onSaveShare(resourceId);
  }

  private onSaveShare(resourceId): void {
    this.isSavingShare = true;
    const payload = {
      expires_at: this.savedSearch['valid_to'],
      share_code: this.savedSearch['shareCode']
    } as Collection;
    const updateCollection$ = this.collectionService.updateCollection(resourceId, payload)
      .pipe(finalize(() => this.isSavingShare = false))
      .subscribe({
        next: (data) => {
          if (this.savedSearch.shareCode) {
            this.collectionService.alertAfterCopyShareLink();
          }
          this.activeModal.close({collection: data} as ShareDialogResult);
        },
        error: error => {
          console.error(error);
        }
      });
    this.subscriptions.add(updateCollection$);
  }

  private isSharedTeamUser(user: TeamUser): boolean {
    return this.sharedTeamUsers.find(u => user.id === u.id) !== undefined;
  }

  private isSharedUserGroup(group: UserGroup): boolean {
    return this.sharedGroups.find(g => group.id === g.id) !== undefined;
  }

  private checkRegisteredNames(): Observable<CollectionsResponse> {
    const collectionsPayload = {
      name: `like:${this.getCollectionName()}%`,
      sort_by: 'id',
      sort_order: 'desc',
      page_size: 100
    };
    return this.collectionService.getCollections(collectionsPayload, false);
  }

  private getMaxOrderName(collections: Collection[]): number {
    if (!collections?.length) {
      return 0;
    }

    return collections.map(c => {
      const matched = c.name.match(/\s+\((\d+)\)$/);
      return matched ? parseInt(matched[1], 10) : 0;
    }).reduce((a, b) => Math.max(a, b), 0) + 1;
  }

  private saveNewCollection(): void {

    const checkRegisteredNames$ = this.checkRegisteredNames()
      .subscribe({
        next: ({result_collections}) => {
          let maxOrder = this.getMaxOrderName(result_collections);

          const payload = this.buildCollectionPayload(maxOrder);

          this.isSavingShare = true;
          const createCollection$ = this.collectionService.createCollection(payload)
            .subscribe({
              next: (collection) => {
                this.resourceId = collection.id;
                if (this.isShareLink()) {
                  this.onShareLink(this.resourceId);
                } else {
                  this.onShareWithTeamUsers();
                }
              },
              error: (error) => {
                this.isSavingShare = false;
                this.sharingErrorMessage = error.error.message;
                console.error(error);
              }
            });
          this.subscriptions.add(createCollection$);
        }
      });
    this.subscriptions.add(checkRegisteredNames$);
  }

  private saveNewCollectionForMonitorRuns(): void {
    if (this.isShareLink()) {
      this.saveNewCollection();
      return;
    }

    this.sharingErrorMessage = null;
    this.sharingSuccessMessage = null;

    if (this.selectedTeamUsers.length + this.selectedGroups.length === 0 && !this.hasShared()) {
      this.isSavingShare = false;
      this.sharingErrorMessage = 'You have to choose some users to share this collection';
      return;
    }

    const checkRegisteredNames$ = this.checkRegisteredNames()
      .subscribe({
        next: ({result_collections}) => {
          let maxOrder = this.getMaxOrderName(result_collections);
          const observables = [];

          for (const user of this.selectedTeamUsers) {
            const payload = this.buildCollectionPayload(maxOrder);
            const obs = this.collectionService.createCollection(payload)
              .pipe(mergeMap((collection) => {
                return this.onShareMonitorRunWithUser(collection, user.id);
              }));
            maxOrder += 1;
            observables.push(obs);
          }

          for (const group of this.selectedGroups) {
            const payload = this.buildCollectionPayload(maxOrder);
            const obs = this.collectionService.createCollection(payload)
              .pipe(mergeMap((collection) => {
                return this.onShareMonitorRunWithGroup(collection, group.id);
              }));
            maxOrder += 1;
            observables.push(obs);
          }

          this.isSavingShare = true;
          const obs$ = forkJoin(observables)
            .pipe(finalize(() => this.isSavingShare = false))
            .subscribe({
              next: (val) => {
                this.onShareWithTeamUsersSuccess(val);
              },
              error: error => {
                console.warn(error);
              }
            });
          this.subscriptions.add(obs$);
        }
      });
    this.subscriptions.add(checkRegisteredNames$);
  }

  private buildCollectionPayload(collectionOrder: number): Collection {
    const payload = {
      name: this.getCollectionName() + (collectionOrder > 0 ? ` (${collectionOrder})` : ''),
      collection_type: this.patentListScope
    } as Collection;

    if (this.isMonitorService()) {
      payload.monitor_run_id = this.monitorStoreService.selectedMonitorRun || this.monitorLegalStoreService.selectedMonitorRun;
    } else {
      payload.search_hash = this.storeService.searchHash;
    }

    const freeTextQuery = this.storeService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['free_text_query'] = freeTextQuery;
    }

    if (this.isFamilyCollectionType) {
      payload.document_ids = this.getStoredDocumentIds();
    } else {
      payload.publication_numbers = this.storeService.selectedPublications;
    }

    return payload;
  }

  private isMonitorService(): boolean {
    return this.searchService instanceof MonitorService;
  }

  private isCollectionService(): boolean {
    return this.searchService instanceof CollectionService;
  }

  private getStoredDocumentIds(): string[] {
    return this.storeService.selectedPatentIds.map(o => o.toString()) || [];
  }

  private getCollectionName(): string {
    return this.isShareLink() ? this.titleFormControl.value : this.truncatePipe.transform(this.textInput, 95, true);
  }

  private validateUserName(user: TeamUser): boolean {
    if (!this.hasName(user)) {
      return user.email.indexOf(this.searchName.toLowerCase()) > -1;
    }
    const fullName = (user.first_name ? user.first_name + ' ' : '') + (user.last_name ? user.last_name : '');
    return fullName.toLowerCase().indexOf(this.searchName.toLowerCase()) > -1;
  }

  private validateGroupName(group: UserGroup): boolean {
    return group.name.toLowerCase().indexOf(this.searchName.toLowerCase()) > -1;
  }

  private hasName(user: TeamUser) {
    return !(!user.first_name && !user.last_name);
  }

  private getNumberOfDocumentsSelected() {
    return this.storeService.selectedPublications.length ? this.storeService.selectedPublications.length :
      this.storeService.pagination.total_hits;
  }

  private onShareMonitorRunWithUser(collection: Collection, userId) {
    if (collection) {
      const payload = {
        resource_id: collection.id,
        permission: this.permission,
        resource_type: CollaborationResourceTypeEnum.COLLECTION,
        user_ids: [userId]
      };
      return this.collaborationService.create(payload)
        .pipe(
          map((val) => collection),
          finalize(() => this.isSavingShare = false)
        );
    }
    return of(collection);
  }

  private onShareMonitorRunWithGroup(collection: Collection, groupId) {
    if (collection) {
      const payload = {
        resource_id: collection.id,
        permission: this.permission,
        resource_type: CollaborationResourceTypeEnum.COLLECTION,
        group_ids: [groupId]
      };
      return this.collaborationService.create(payload)
        .pipe(
          map((val) => collection),
          finalize(() => this.isSavingShare = false)
        );
    }
    return of(collection);
  }

  private onShareWithTeamUsersSuccess(val) {
    let message;
    if (this.searchService) {
      const count = this.getStoredDocumentIds().length;

      if (this.isCollectionService()) {
        message = `The list "${this.textInput?.toUpperCase()}" has`;
      } else if (count > 0 && this.resourceType) {
        message = count + ' document' + (count > 1 ? 's have' : ' has');
      } else {
        message = 'All documents have';
      }

      if (this.selectedTeamUsers.length || this.selectedGroups.length) {
        message += ' been shared successfully with ' + this.usersTitleTextPipe.transform(this.selectedTeamUsers.length ?
                                                                                         this.selectedTeamUsers :
                                                                                         this.selectedGroups);
      } else {
        message += ' been unshared successfully';
      }
    } else {
      const shareType = this.resourceType === CollaborationResourceTypeEnum.PATENT ? 'document' :
        !this.searchService ? 'collection' : 'search';
      if (this.selectedTeamUsers.length) {
        message = `This ${shareType} has been shared with the selected users successfully`;
      } else {
        message = `This ${shareType} has been unshared with the selected users successfully`;
      }
    }

    if (this.searchService) {
      this.collectionsStoreService.setSaveToCollectionSuccess(message);
      const result = this.isCollectionService() && !this.isFolderResource ? {collection: val} : {
        shared_users: this.selectedTeamUsers, shared_groups: this.selectedGroups
      };
      this.activeModal.close(result as ShareDialogResult);
    } else {
      this.sharingSuccessMessage = message;
      this.sharedTeamUsers = [...this.selectedTeamUsers];
      this.sharedGroups = [...this.selectedGroups];
    }
  }

  private updateIsSelectedAll(_filterType: string) {
    switch (_filterType) {
      case this.filterTypeAll:
        this.isSelectedAll = _.isEqual(this.filteredTeamUsers, this.selectedTeamUsers)
          && _.isEqual(this.filteredGroups, this.selectedGroups)
          && this.filteredTeamUsers.length > 0;
        break;
      case this.filterTypeUsers:
        this.isSelectedAll = _.isEqual(this.filteredTeamUsers, this.selectedTeamUsers) && this.filteredTeamUsers.length > 0;
        break;
      case this.filterTypeGroups:
        this.isSelectedAll = _.isEqual(this.filteredGroups, this.selectedGroups) && this.filteredGroups.length > 0;
        break;
    }
  }
}
