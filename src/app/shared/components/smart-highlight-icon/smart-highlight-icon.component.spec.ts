import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SmartHighlightIconComponent } from './smart-highlight-icon.component';

describe('SmartHighlightIconComponent', () => {
  let component: SmartHighlightIconComponent;
  let fixture: ComponentFixture<SmartHighlightIconComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SmartHighlightIconComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SmartHighlightIconComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
