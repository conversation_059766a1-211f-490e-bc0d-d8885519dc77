<div class="modal-header">
  <div class="modal-title">Sort your list by similarity</div>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" tabindex="-1"></button>
</div>
<div class="modal-body">
  <div>
    <app-semantic-input [form]="form" [textWeighting]="form.value.text_weighting" (weightingChange)="onWeightingChange($event)"
                        (inputChanged)="inputChanged(form)"  (submitInput)="onSubmit()"  [storeService]="storeService"
                        [(selectedLanguage)]="selectedLanguage"
                        (selectedLanguageChanged)="onSelectedLanguageChanged($event)"
                        [(detectedLanguage)]="detectedLanguage"
                        (detectedLanguageChanged)="onDetectedLanguageChanged($event)"
                        (translationConfirmed)="onTranslationConfirmed($event)">
    </app-semantic-input>
  </div>
</div>
<div class="modal-footer">
  <div class="d-flex justify-content-end">
    <button type="button" [disabled]="isFormEmpty"  class="btn btn-secondary-outline btn-md me-3 btn-icon" (click)="onReset()"><i class="fa fa-times"></i></button>
    <button class="btn btn-primary" [disabled]="!form.dirty && !form.touched" (click)="onSubmit()"><i class="fas fa-exchange-alt fa-rotate-90"></i> Sort by similarity</button>
  </div>
</div>
