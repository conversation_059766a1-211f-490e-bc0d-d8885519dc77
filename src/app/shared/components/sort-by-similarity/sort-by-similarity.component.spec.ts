import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SortBySimilarityComponent } from './sort-by-similarity.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';
import { SemanticSearchStoreService } from '@core';

describe('SortBySimilarityComponent', () => {
  let component: SortBySimilarityComponent;
  let fixture: ComponentFixture<SortBySimilarityComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SortBySimilarityComponent ],
      providers: [NgbActiveModal, provideMatomo({siteId: '', trackerUrl: '', disabled: true })],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SortBySimilarityComponent);
    component = fixture.componentInstance;
    component.storeService = TestBed.inject(SemanticSearchStoreService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
