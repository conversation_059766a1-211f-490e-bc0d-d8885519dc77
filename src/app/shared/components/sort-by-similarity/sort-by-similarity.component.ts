import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import {
  BaseStoreService,
  ENGLISH_CODE,
  ENGLISH_LANG,
  SortBySimilarityParam,
  UserService
} from '@core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateLanguageDialogComponent } from '@shared/components';

@Component({
  selector: 'app-sort-by-similarity',
  templateUrl: './sort-by-similarity.component.html',
  styleUrls: ['./sort-by-similarity.component.scss']
})
export class SortBySimilarityComponent implements OnInit { @Input()
  @Input() saveFromValue: SortBySimilarityParam;
  @Input() storeService: BaseStoreService;

  wasTranslationConfirmed = false;
  detectedLanguage: { code: string, name: string };
  selectedLanguage: { code: string, name: string };
  previousDetectedLanguage: { code: string, name: string };

  public form: UntypedFormGroup = new UntypedFormGroup({
    term: new UntypedFormControl(''),
    publications: new UntypedFormControl(''),
    text_weighting: new UntypedFormControl(1.0)
  });

  constructor(
    public activeModal: NgbActiveModal,
    private userService: UserService,
    private modalService: NgbModal,
  ) { }

  ngOnInit(): void {
    if(this.saveFromValue){
      if(this.saveFromValue.publications){
        this.form.get('publications').setValue(this.saveFromValue.publications);
      }
      if(this.saveFromValue.term){
        this.form.get('term').setValue(this.saveFromValue.term);
      }
      if(this.saveFromValue.text_weighting){
        this.form.get('text_weighting').setValue(this.saveFromValue.text_weighting);
      }
    }
  }
  /**
   * event listener for semantic input component
   * @param form updated form with values
   */
  public inputChanged(form) {
    this.form = form;
  }

  onWeightingChange(weight){
    this.form.get('text_weighting').setValue(weight);
  }

  onSubmit(){
    if (this.shouldShowTranslateBox()) {
      this.openTranslateBox();
    } else {
      this.doSubmit();
    }
  }

  onReset(){
    this.saveFromValue = undefined;
    this.selectedLanguage = null;
    this.detectedLanguage = null;
    this.wasTranslationConfirmed = false;
    this.activeModal.close(undefined);
  }

  /**
   * check if form input is empty
   */
  get isFormEmpty() {
    if (!this.form.get('term').value.trim() && !this.form.get('publications').value.trim()) {
      return true;
    }
    return false;
  }

  onSelectedLanguageChanged(event) {
    if (this.selectedLanguage?.code && this.selectedLanguage.code !== event?.code) {
      this.wasTranslationConfirmed = false;
    }
  }

  onDetectedLanguageChanged(event) {
    if (event?.code && this.previousDetectedLanguage?.code !== event?.code) {
      this.wasTranslationConfirmed = false;
    }

    if (event) {
      this.previousDetectedLanguage = event;
    }
  }

  onTranslationConfirmed(event) {
    this.wasTranslationConfirmed = event;
  }

  private doSubmit() {
    const values = this.isFormEmpty ? undefined : this.form.value;
    values['language'] = this.selectedLanguage || this.detectedLanguage;
    this.saveFromValue = values;
    this.activeModal.close(values);
  }

  private shouldShowTranslateBox(): boolean {
    const canTranslateAutomatically = this.userService.getUISetting('semantic_automatic_translations', false);
    const isLanguageNotSelected = !this.selectedLanguage?.code;
    const isEnglishSelected = this.selectedLanguage?.code === ENGLISH_CODE;
    return !(this.userService.isFreeUser() || isLanguageNotSelected || isEnglishSelected || canTranslateAutomatically || this.wasTranslationConfirmed);
  }

  private openTranslateBox() {
    this.wasTranslationConfirmed = false;
    const modal = this.modalService.open(TranslateLanguageDialogComponent, {
      modalDialogClass: 'translate-language-dialog',
      scrollable: false
    });
    modal.componentInstance.selectedLanguage = this.selectedLanguage;
    modal.componentInstance.detectedLanguage = this.detectedLanguage;
    modal.result.then((result) => {
      if (result) {
        this.selectedLanguage = result;

        if (this.detectedLanguage?.code !== this.selectedLanguage.code) {
          this.detectedLanguage = null;
          this.previousDetectedLanguage = null;
        }

        this.wasTranslationConfirmed = true;
        this.doSubmit();
      } else {
        this.wasTranslationConfirmed = false;
      }
    }, (reason) => {
      this.wasTranslationConfirmed = false;

      if (reason?.useOriginal) {
        this.selectedLanguage = ENGLISH_LANG;
        this.detectedLanguage = null;
        this.previousDetectedLanguage = null;
        this.doSubmit();
      }
    });
  }
}
