import { AfterViewInit, Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '@core';

declare var $: any;
declare var hbspt: any;

@Component({
  selector: 'app-support',
  templateUrl: './support.component.html',
  styleUrls: ['./support.component.scss']
})
export class SupportComponent implements AfterViewInit {

  @Input() title: string;
  @Input() message: string;
  @Input() onFormReadyCallback: (args: any) => void;
  @Input() onFormExistedCallback: (args: any) => void;
  @Input() onFormSubmittedCallback: (args: any) => void;
  @Input() fillUserFields = true;
  loading = true;

  constructor(
    private userService: UserService,
    public activeModal: NgbActiveModal,
  ) { }

  ngAfterViewInit() {
    this.initSupportForm();
  }

  getBodyStyle() {
    if (this.title) {
      return {height: "calc( 100% - 97px )"};
    }

    return {height: "100%"};
  }

  private initSupportForm() {
    if (typeof(hbspt) !== 'undefined' && hbspt.forms) {
      const formId = 'c443fdff-8efe-413c-b1ec-245a2d17991d';
      if (!$('.hs-form').length) {
        const user = this.userService.getUser().profile;
        const self = this;
        hbspt.forms.create({
          portalId: '2093377',
          formId: formId,
          target: '#bodyRequestForm',
          onBeforeFormInit: () => {
            self.loading = false;
          },
          onFormReady: () => {
            if (this.fillUserFields) {
              $(`#firstname-${formId}`).val(user.first_name).trigger('change');
              $(`#lastname-${formId}`).val(user.last_name).trigger('change');
              $(`#email-${formId}`).val(user.email).trigger('change');
              $(`#company-${formId}`).val(user.company_name).trigger('change');
              $(`#country-${formId}`).val(user.country).trigger('change');
            }
            if (self.message) {
              $(`#message-${formId}`).val(self.message).trigger('change');
            }
            if (self.onFormReadyCallback) {
              self.onFormReadyCallback(formId);
            }
          },
          onFormSubmit: () => {
          },
          onFormSubmitted: () => {
            if (self.onFormSubmittedCallback) {
              self.onFormSubmittedCallback(formId);
            }
          }
        });
      } else {
        if (this.onFormExistedCallback) {
          this.onFormExistedCallback(formId);
        }
        this.loading = false;
      }
    }
  }
}
