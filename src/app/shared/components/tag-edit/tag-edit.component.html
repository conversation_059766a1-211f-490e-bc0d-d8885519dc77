<div class="tag-edit-container inline-modal-container">
  <div class="inline-modal-block gap-spacing-md">
    <div class="tag-edit-form d-flex flex-column align-items-start gap-spacing-xx-s w-100">
      <input id="tag-edit-name" placeholder="Enter a tag name" type="text"
             [formControl]="tagNameFormControl" tabindex="0" (keydown.enter)="onTagNameEnterPressed()"
             [ngClass]="{'tag-edit-name-error': !tagNameFormValue?.length && tagNameFormControl.touched, 'p-r-spacing-xxx-big': tagOwner}"
             [disabled]="isSavingTag" required appAutofocus
             class="p-spacing-sm radius-sm border-1 border-subtle border-minimal figma-bg-transition w-100"/>
      <i class="fa-regular fa-circle-info tag-info-icon fa-1x" *ngIf="tagOwner" [ngbTooltip]="tagCreator"
         placement="right"></i>
    </div>

    <a *ngIf="openNewTab && tag?.collection_id"
       [routerLink]="['/collections',_tag?.collection_id]" target="_blank"
       class="cursor-pointer button-main-secondary-grey w-100 content-label-medium">
      Open tag collection <i class="fa-regular fa-arrow-up-right-from-square"></i>
    </a>
  </div>

  <ng-container *ngIf="canEditTag">
    <div class="popover-divider"></div>

    <div class="tag-edit-colors inline-modal-block">
      <div class="content-heading-h6 content-color-secondary p-x-spacing-xx-s">Colors</div>
      <div class="tag-edit-colors-container d-flex flex-wrap gap-spacing-sm p-spacing-xx-s">
        <div class="tag-edit-color-item" *ngFor="let color of colors"
             [ngbTooltip]="color['name']" tooltipClass="white-tooltip tooltip-text-only" container="body"
             [style.background-color]="color['code']" (click)="onColorClick(color['code'])">
          <i class="far fa-check tag-edit-color-selected" *ngIf="color['code'] === selectedColor" [style.color]="getTextColor(color['code'])"></i>
        </div>
      </div>
    </div>
  </ng-container>

  <div class="popover-divider"></div>

  <div class="tag-permission inline-modal-block">
    <div class="content-heading-h6 content-color-secondary p-x-spacing-xx-s">Who has access</div>
    <div class="tag-permission-container w-100">
      <div class="tag-permission-option p-x-spacing-sm p-y-spacing-x-s d-flex align-items-stretch justify-content-between radius-sm"
           (click)="onPermissionClick(false)"
           [class.disabled]="isSavingTag || (!canSwitchPermission && tagPermissionFormControl.value)">
      <div class="d-flex flex-column text-start">
          <div class="content-heading-h6"><i class="fa-regular fa-globe"></i> Public</div>
          <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
        </div>
        <div class="d-flex align-items-center">
          <i class="fa-regular fa-check" *ngIf="!tagPermissionFormControl.value"></i>
        </div>
      </div>
      <div class="tag-permission-option p-x-spacing-sm p-y-spacing-x-s d-flex align-items-stretch justify-content-between radius-sm"
           (click)="onPermissionClick(true)"
           [class.disabled]="isSavingTag || (!canSwitchPermission && !tagPermissionFormControl.value)">
      <div class="d-flex flex-column text-start">
          <div class="content-heading-h6"><i class="fa-regular fa-lock"></i> Private</div>
          <div class="content-body-xsmall content-color-tertiary">Only the creator can switch it to private</div>
        </div>
        <div class="d-flex align-items-center">
          <i class="fa-regular fa-check" *ngIf="tagPermissionFormControl.value"></i>
        </div>
      </div>
    </div>
  </div>

  <ng-container *ngIf="showButtons">
    <div class="popover-divider"></div>

    <div class="inline-modal-block justify-content-end flex-row">
      <button class="button-main-secondary-grey button-small" [disabled]="isSavingTag"
              (click)="onCancelClicked()">Cancel
      </button>
      <button class="button-main-primary button-small" [disabled]="isSavingTag || !tagNameFormControl.dirty || tagNameFormControl.invalid" (click)="onCreateClicked()">
        {{ isEditingTag ? 'Update' : 'Create' }}
      </button>
    </div>
  </ng-container>

  <ng-container *ngIf="canEditTag && !showButtons">
    <div class="popover-divider"></div>

    <div class="inline-modal-block">
      <a class="button-medium button-destructive-tertiary content-label-medium w-100 align-items-center justify-content-start d-flex" (click)="onDeleteTag()">
        <i class="fa-regular fa-trash-can m-r-spacing-xx-s"></i>Delete tag
      </a>
    </div>
  </ng-container>
</div>

<ng-template #tagCreator>
  <div *ngIf="tagOwner" class="text-start">
    <div class="d-block content-color-tertiary content-heading-h7">Tag created by</div>
    <div class="d-block content-body-small "><span class="content-color-primary">{{ tagOwner | userTitle}}</span>&nbsp; <span class="content-color-tertiary" *ngIf="isTagOwner">(you)</span></div>
    <div class="d-block content-body-small" *ngIf="!isTagOwner && isCollaboratorUser">
      <span class="content-color-tertiary">Premium users can change tags name</span>
    </div>
  </div>
</ng-template>
