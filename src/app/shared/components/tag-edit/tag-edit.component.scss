@import 'scss/figma2023/variables';

.tag-edit-container {

  .tag-edit-form {
    position: relative;

    .tag-info-icon{
      position: absolute;
      right: $spacing-system-spacing-sm;
      top: calc(50% - $spacing-system-spacing-sm + 0.5px);
    }

    input {
      color: $colours-content-content-primary;
      font-family: $font-family-base;
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;
      outline: none;

      &:disabled {
        background: $colours-background-bg-tertiary;
        color: $colours-content-content-disabled;
      }

      &::-ms-input-placeholder, &::placeholder {
        color: $colour-grey-500;
        font-family: $font-family-base;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.5rem;
      }

      &:focus:not(:disabled), &:active:not(:disabled) {
        border-color: $colours-border-contrast !important;
        background: $colours-background-bg-primary !important;
      }

      &.tag-edit-name-error {
        border-color: $colour-red-400;
      }
    }
  }

  .tag-edit-colors {
    .tag-edit-colors-container {
      .tag-edit-color-item {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: $radius-sm;
        border: 0.0625rem solid $colour-grey-500;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .tag-edit-color-selected {
          text-align: center;
          font-family: "Font Awesome 6 Pro";
          font-size: 1rem;
          font-style: normal;
          font-weight: 300;
          line-height: normal;
        }
      }
    }
  }

  .tag-permission{
    &-option{
      cursor: pointer;
      &:hover:not(.disabled){
        background-color: $colours-background-bg-secondary;
      }
      &.disabled{
        cursor: default;
        color: $colours-content-content-disabled;
      }
    }
  }
}
