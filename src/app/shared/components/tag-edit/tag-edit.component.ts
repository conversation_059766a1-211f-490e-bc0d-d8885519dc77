import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { BehaviorSubject, concatMap, Observable, of, Subscription } from 'rxjs';
import { ColorUtil, TagService, TeamUser, ToastService, ToastTypeEnum, UserService } from '@core';
import { TagModel } from '@core/models/tag.model';
import { catchError, filter, finalize, tap } from 'rxjs/operators';

@Component({
  selector: 'app-tag-edit',
  templateUrl: './tag-edit.component.html',
  styleUrls: ['./tag-edit.component.scss']
})
export class TagEditComponent implements OnInit, OnDestroy {
  @Input() tag: TagModel;
  @Input() openNewTab: boolean = true;
  @Input() showButtons: boolean = false;
  @Output() tagSaved = new EventEmitter<TagModel>();
  @Output() tagCanceled = new EventEmitter<void>();
  @Output() tagDeleted = new EventEmitter<TagModel>();
  tagNameFormControl = new FormControl(null, [Validators.required, Validators.minLength(1)]);
  tagPermissionFormControl = new FormControl(false);
  colors = ColorUtil.TAG_COLORS;
  _tag: TagModel;
  selectedColor: string = null;
  isSavingTag: boolean = false;
  tagOwner: TeamUser;
  isTagOwner: boolean;
  newTag: boolean;

  private saveTagSubject = new BehaviorSubject<TagModel>(null);
  private subscriptions = new Subscription();
  private updatingTags: Record<number, TagModel> = {};

  constructor(
    private tagService: TagService,
    private toastService: ToastService,
    private userService: UserService,
  ) {
  }

  get tagColor(): string {
    return this.tag.color[0] === '#' ? this.tag.color : `#${this.tag.color}`;
  }

  get tagNameFormValue(): string {
    return this.tagNameFormControl.value?.trim();
  }

  get isEditingTag(): boolean {
    return !!this.tag?.id;
  }

  get isCollaboratorUser(): boolean {
    return this.userService.isCollaboratorUser();
  }

  get isFreeUser(): boolean {
    return this.userService.isFreeUser();
  }

  get canEditTag(): boolean {
    return this.isTagOwner || (!this.isCollaboratorUser && !this.isFreeUser);
  }

  get canSwitchPermission(): boolean {
    if (this.isCollaboratorUser || this.isFreeUser) {
      return false;
    }

    return this.isTagOwner;
  }

  ngOnInit() {
    this._tag = this.tag;
    this.isTagOwner = this.tag?.user_id === this.userService.getUser().profile?.id;
    this.tagNameFormControl.setValue(this.tag.name);
    if(!this.canEditTag) {
      this.tagNameFormControl.disable();
    }
    this.tagPermissionFormControl.setValue(this.tag.private);
    this.newTag = !this.tag?.id;

    this.selectedColor = this.tagColor;

    const updateTag$ = this.saveTagSubject.asObservable()
      .pipe(
        filter(tag => !!tag?.name?.trim()?.length),
        tap((tag) => {
          this.tagNameFormControl.disable();
          this.isSavingTag = true;
          if (tag.id) {
            this.updatingTags[tag.id] = this.tag;
          }
        }),
        concatMap(tag => {
          let obs: Observable<TagModel>;
          if (tag?.id) {
            obs = this.tagService.update({name: tag.name, color: tag.color, private: tag.private}, tag.id);
          } else {
            obs = this.tagService.create({name: tag.name, color: tag.color, private: tag.private});
          }

          obs = obs.pipe(
            catchError(error => {
              this.toastService.show({
                type: ToastTypeEnum.ERROR,
                header: 'Error in saving tag',
                body: 'An error occurred while saving the tag. Please try again.',
                delay: 10000
              });
              return of(null);
            })
          );
          return obs;
        }),
        tap(tag => {
          this.isSavingTag = false;
          this.tagNameFormControl.enable();
          if (tag?.id) {
            const existingTag = this.updatingTags[tag.id];
            if (!existingTag) {
              this.toastService.show({
                type: ToastTypeEnum.SUCCESS,
                header: this.tag?.id ? 'Tag updated' : 'Tag created',
                body: `The tag has been ${this.tag?.id ? 'updated' : 'created'} successfully.`,
                delay: 5000
              });
            }
            if (existingTag) {
              existingTag.id = tag.id;
              existingTag.name = tag.name;
              existingTag.color = tag.color;
              existingTag.private = tag.private;
            }
            this.tagSaved.emit(tag);
          }
        }),
        finalize(() => {
          this.isSavingTag = false;
          this.tagNameFormControl.enable();
        })
      )
      .subscribe();
    this.subscriptions.add(updateTag$);

    if(this.tag?.user_id){
      if(this.isTagOwner){
        this.tagOwner = this.userService.getUser().profile;
      } else {
        this.subscriptions.add(this.userService.getTeamUser(this.tag?.user_id).pipe(tap(user => this.tagOwner = user)).subscribe());
      }
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onColorClick(color: string) {
    this.selectedColor = color;

    if (color !== this.tagColor) {
      this.tagNameFormControl.markAsDirty();
    }

    if (!this.showButtons) {
      this.saveTagSubject.next({
        id: this._tag.id,
        name: this.tagNameFormValue,
        private: this._tag.private,
        color: color.replace('#', '')
      });
    }
  }

  getTextColor(color: string) {
    return this.tagService.getTextColor(color);
  }

  onTagNameEnterPressed() {
    this.saveTag();
  }

  onCreateClicked() {
    this.saveTag();
  }

  onCancelClicked() {
    this.tagCanceled.emit();
  }

  private saveTag() {
    if (!this.tagNameFormControl.dirty || this.tagNameFormControl.invalid) {
      return;
    }

    const tagName = this.tagNameFormValue;
    const color = (this.selectedColor || this.tag.color).replace('#', '');
    this.saveTagSubject.next({
      id: this._tag.id,
      private: this.tagPermissionFormControl.value,
      color: color,
      name: tagName
    });
  }

  onPermissionClick(makePrivate: boolean) {
    if (!this.isTagOwner || this.isCollaboratorUser) {
      return;
    }
    this.tagPermissionFormControl.setValue(makePrivate);
    if (this._tag.private !== makePrivate) {
      this.tagNameFormControl.markAsDirty();
    }
    if (!this.showButtons && this.tag?.id) {
      this.saveTagSubject.next({
        id: this._tag.id,
        name: this._tag.name,
        private: this.tagPermissionFormControl.value,
        color: this._tag.color
      });
    }
  }

  onDeleteTag() {
    this.tagDeleted.emit(this.tag);
  }
}
