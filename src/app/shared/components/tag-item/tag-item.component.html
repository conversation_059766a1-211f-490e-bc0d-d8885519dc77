<div class="tag-item tag-custom" [class]="tagSizeClass"
     [id]="getTagId()"
     [ngStyle]="getBoxTagStyle()"
     [ngClass]="{'cursor-pointer': canManageTag, 'cursor-default': !canManageTag, 'tag-item-selected': tag?.id === selectedTagId}"
     [ngbPopover]="showTagTooltip?popoverTagTemp:null" #popover="ngbPopover"
     triggers="mouseenter:mouseleave" placement="top" popoverClass="white-popover tag-tooltip-popover" container="body">

  <div *ngIf="canManageTag" class="tag-name content-label-small"
       [ngbTooltip]="tagEditTemplate"
       tooltipClass="white-tooltip tooltip-big tag-edit-tooltip"
       triggers="manual"
       autoClose="outside"
       container="body"
       placement="bottom auto"
       #tagEditTooltip="ngbTooltip"
       (click)="onEditTagClicked(tag, tagEditTooltip); $event.stopPropagation();"
       (hidden)="onTagEditHidden()"
       [style.max-width]="truncateWidth"
       #tagNameElement>
    <i class="fa-regular fa-lock" *ngIf="tag?.private"></i> {{ tag?.name }}
  </div>

  <div *ngIf="!canManageTag" class="tag-name content-label-small"
       [style.max-width]="truncateWidth"
       #tagNameElement>
    <i class="fa-regular fa-lock" *ngIf="tag?.private"></i> {{ tag?.name }}
  </div>

  <div *ngIf="showUnassignButton" class="tag-unassign" (click)="onUnassignClick($event)">
    <i class="fa-regular fa-times"></i>
  </div>

  <ng-template #popoverTagTemp>
    <div class="inline-modal-container">
      <div class="content-body-small inline-modal-block d-block"
           *ngIf="showTruncatedName && isTagNameTruncated()" style="word-break: break-all; word-wrap: break-word;">
        <i class="fa-regular fa-lock p-t-spacing-xx-s p-r-spacing-xx-s p-b-spacing-xx-s float-left" *ngIf="tag?.private"></i>
        {{ tag?.name }}
      </div>
      <hr class="popover-divider" *ngIf="showTruncatedName && isTagNameTruncated()">
      <div class="inline-modal-block gap-spacing-xx-s">
        <div class="content-color-tertiary content-heading-h7">
          @if (tag?.assigned_by) { Added } @else { Created } by
        </div>
        <div class="content-body-small d-flex flex-column">
          @if (tag?.assigned_by || tag?.user) {
            <span class="content-color-primary">
            @if (tag?.assigned_by) {
              {{ tag?.assigned_by?.first_name }} {{ tag?.assigned_by?.last_name }}
            } @else {
              {{ tag?.user?.first_name }} {{ tag?.user?.last_name }}
            }
            </span>
          } @else {
            <span class="content-color-tertiary">User information unavailable</span>
          }

          <div class="content-color-primary">
            {{ (tag?.assigned_at || tag?.created_at) | dateFormat: 'ShortDate' }}
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #tagEditTemplate>
  <app-tag-edit [tag]="editingTag"
                [openNewTab]="showOpenTagCollection"
               (tagSaved)="onTagEditSaved($event)"
               (tagCanceled)="onTagEditCanceled()"
               (tagDeleted)="onTagDeleted()">
  </app-tag-edit>
</ng-template>
