import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { NgbModal, NgbPopover, NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { TagModel } from '@core/models/tag.model';
import { TagService, ToastService, ToastTypeEnum } from '@core/services';
import { Subscription } from 'rxjs';
import { ModalDialogComponent } from '../modal-dialog/modal-dialog.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-tag-item',
  templateUrl: './tag-item.component.html',
  styleUrls: ['./tag-item.component.scss']
})
export class TagItemComponent implements OnDestroy {
  @Input() tag: TagModel;
  @Input() canManageTag: boolean = true;
  @Input() size: 'small' | 'medium' | 'default' = 'default';
  @Input() showUnassignButton: boolean = false;
  @Input() idSuffix: string = '';
  @Input() showTruncatedName: boolean = true;
  @Input() truncateWidth: string = '10rem';
  @Input() selectedTagId: number = null;
  @Input() showOpenTagCollection: boolean = true;
  @Input() showTagTooltip: boolean = true;
  @Output() tagUnassign = new EventEmitter<TagModel>();
  @Output() tagSaved = new EventEmitter<TagModel>();
  @Output() tagDeleted = new EventEmitter<TagModel>();

  @ViewChild('tagNameElement') tagNameElement: ElementRef;
  @ViewChild('popover') popover: NgbPopover;
  @ViewChild('tagEditTooltip') tagEditTooltip: NgbTooltip;

  isNameTruncated: boolean = false;
  editingTag: TagModel = null;

  private subscriptions = new Subscription();

  constructor(
    public tagService: TagService,
    private ngbModal: NgbModal,
    private toastService: ToastService,
    private router: Router
  ) {}

  get isCollectionRoute(): boolean {
    const currentUrl = this.router.url;
    const isInCollectionRoute = currentUrl.match(/^\/collections\/[^\/]+\/collection\/(\d+)$/);
    const collectionId = isInCollectionRoute ? isInCollectionRoute[1] : null;
    return isInCollectionRoute && collectionId === this.tag?.collection_id?.toString();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get tagSizeClass(): string {
    return this.size !== 'default' ? `tag-${this.size}` : '';
  }

  get tagColor(): string {
    return this.tag?.color || '';
  }

  getBoxTagStyle() {
    const color = this.tagColor || '000000';
    const textColor = this.tagService.getTextColor('#' + color);
    return {
      'color': textColor,
      'background-color': '#' + color,
      '--hover-bg': '#' + color + (this.canManageTag ? 'A6' : '')
    };
  }

  getTagId(): string {
    return `doc-tag-${this.tag?.id || 'new'}-${this.idSuffix}`;
  }

  onEditTagClicked(tag: TagModel, tooltip: NgbTooltip): void {
    if (!this.canManageTag) {
      return;
    }

    this.editingTag = {...tag};
    tooltip.open();
  }

  onTagEditHidden(): void {
    this.editingTag = null;
  }

  onUnassignClick(event: MouseEvent): void {
    event.stopPropagation();
    this.tagUnassign.emit(this.tag);
  }

  onTagEditSaved(tag: TagModel): void {
    this.tagEditTooltip.close();
    this.editingTag = null;
    this.tag = tag;
    this.tagSaved.emit(tag);
  }

  onTagEditCanceled(): void {
    this.tagEditTooltip.close();
    this.editingTag = null;
  }

  isTagNameTruncated(): boolean {
    if (this.tagNameElement) {
      return this.tagNameElement.nativeElement.scrollWidth > this.tagNameElement.nativeElement.clientWidth;
    }
    return false;
  }

  onTagDeleted(): void {
    this.tagEditTooltip.close();
    this.tagService.deleteTagsWithConfirmation([this.tag]).subscribe(deletedTags => {
      this.tagDeleted.emit(this.tag);
    });
  }
}
