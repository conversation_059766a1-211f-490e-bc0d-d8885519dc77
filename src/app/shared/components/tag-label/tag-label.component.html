<div class="container-tags d-flex align-items-center justify-content-start">
  <div *ngIf="patent?.tags?.sep" class="me-4 my-1 px-2 cursor-pointer tag-item patent-standard flex-shrink-0"
       [ngbPopover]="popoverSepTemp" container="body" triggers="hover" popoverClass="white-popover">
    <img src="assets/images/relevant-tag-standard.svg"/> SEP
    <ng-template #popoverSepTemp>
      <div class="popover-descriptions">
        {{ standardText }}
      </div>
    </ng-template>
  </div>

  <div *ngIf="patent?.tags?.green" class="me-4 my-1 px-2 cursor-pointer tag-item patent-green flex-shrink-0"
       appSelectableTooltip [selectableTooltipPopover]="greenPatentPopover" [selectableTooltipCloseTimeout]="200"
       #greenPatentPopover="ngbPopover" [ngbPopover]="greenPatentPopoverTemp"
       triggers="manual" [autoClose]="'outside'" container="body" placement="top bottom right"
       popoverClass="green-patent-description-tooltip white-popover popover-lg">
    <img src="assets/images/relevant-tag-green.svg"/> Green
    <ng-template #greenPatentPopoverTemp>
      <div class="text-left py-2">This document is considered green based on the following classification codes:</div>
      <div [innerHTML]="greenTooltip" class="text-left"></div>
    </ng-template>
  </div>
  <div class="label patent-special text-center" ngbTooltip="Special patent">
    <i class="fa-regular fa-sun"></i>
  </div>

  <div class="flex-fill custom-tags-container">
    <div class="d-flex flex-wrap align-items-center justify-content-start custom-tags-inner-container">
      <div *ngFor="let tag of displayedTags;" [id]="getDocTagId(tag)" [ngClass]="isLoadingTags ? 'tag-hidden' : ''"
           class="me-1 my-1 tag-box d-inline-flex align-items-center inline-tag-label flex-shrink-0"
           [ngStyle]="getBoxTagStyle(tag.color)" (mouseover)="onMouseOver(tag)" (mouseout)="onMouseOut(tag)" >
        <div class="px-2 tag-item"
          [ngStyle]="isLoadingTags ? '' : {'background-color': '#' + tag.color}">
          {{ tag.name }}
        </div>
        <div class="tag-unassign cursor-pinter" (click)="unassign(tag)" *ngIf="canRemove(tag)">
          <i class="fa fa-close px-1 cursor-pointer"></i>
        </div>
      </div>
      <app-tag-selector *ngIf="canManageTags || overflowTags?.length > 0" [showIcon]="showIcon" [storeService]="storeService" [patent]="patent" [container]="container"
                        [tagLabel]="this" [canManageTags]="canManageTags" class="d-inline-block flex-shrink-0"></app-tag-selector>
    </div>
  </div>
</div>
