.patent-standard {
    background-color: #FF7700;
    border: 1px solid #DC6700;
    color: white;
}

.patent-green {
    background-color: rgba(21, 188, 49, 0.7);
    border: 1px solid #12A22A;
    color: white;
}

img {
    width: 18px;
    height: 18px;
}

.patent-special {
    background-color: #4325E1;
    border: 1px solid #281392;
    font-size: 14px;
    display: none;
}

.container-tags {
  max-height: 30px;
  overflow: hidden;
  width: 100%;

  .custom-tags-container {
    max-height: 30px;
    overflow: hidden;
  }
}

.tag-item {
  border: 1px solid #00000033;
  border-radius: 4px;
  font-size: 0.875rem;
  max-width: 25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-hidden {
  background: transparent;
  color: transparent;
  border: none;
}

.tag-box {
  border-radius: 4px;
  font-size: 0.775rem;
  transition: all ease-in-out .200s;
  display: inline-block;
  .tag-unassign {
    visibility: hidden;
  }
  &:hover {
    .tag-unassign {
      visibility: visible;
    }
  }
}

::ng-deep {
  .categories-text {
    li {
      text-align: left;
    }
  }

  .tag-selector-hover {
    .tag-selector-container:not(.show) .tag-button-inline{
      visibility: hidden;
    }
    &:hover {
      .tag-button-inline:not(.hide) {
        display: inline-block;
        visibility: visible;
      }
    }
  }
}
