import { BehaviorSubject, Subscription } from 'rxjs';
import { Component, EventEmitter, Input, OnDestroy, OnInit } from '@angular/core';
import { BaseStoreService, SubscriptionType, TagService, User } from '@core';
import { TagModel } from '@core/models/tag.model';
import { debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-tag-label',
  templateUrl: './tag-label.component.html',
  styleUrls: ['./tag-label.component.scss'],
  exportAs: 'tagLabelExport'
})
export class TagLabelComponent implements OnInit, OnDestroy {

  @Input() container: HTMLElement;
  @Input() canManageTags: boolean = false;
  @Input() showIcon: boolean;
  @Input() storeService: BaseStoreService;


  private _tags: TagModel[];
  get tags(): TagModel[] {
    return this._tags;
  }
  @Input() set tags(value: TagModel[]) {
    this._tags = value;
    this.displayedTags = [...this._tags];
    this.allTagsAssigned = [...this._tags];
    this.isLoadingTags = false;
    this.afterLoadEvent.emit();
  }

  afterLoadEvent = new EventEmitter(true);
  unassignEvent = new EventEmitter<TagModel>(null);
  allTagsAssigned: TagModel[];
  displayedTags: TagModel[] = [];
  overflowTags: TagModel[] = [];
  isLoadingTags = true;
  greenTooltip = '';
  user: User;

  private subscriptions = new Subscription();
  private updateDisplayedTagsSubject = new BehaviorSubject<boolean>(false);

  constructor(
    private tagService: TagService
  ) {
  }

  private _patent: any

  get patent() {
    return this._patent;
  }

  @Input() set patent(value) {
    this._patent = value;
    this.loadTags();
  }

  get standardText(): string {
    let text = ''
    if (this.patent.tags.sep.length === 1) {
      text = 'standard ' + this.patent.tags.sep[0];
    } else {
      text = 'standards ' + this.patent.tags.sep.join(', ').replace(/,([^,]*)$/, ' and $1');
    }

    return 'This is an essential patent for ETSI ' + text;
  }

  ngOnInit(): void {
    this.loadTags();
    if (this.patent?.general?.green_codes?.length > 0) {
      this.greenTooltip = this.groupSameCodes(this.patent.general.green_codes)
        .map(c => this.generateCategoryHtml(c))
        .reduce((a, b) => a + b, '');
    }
    const refreshLabelsEvent$ = this.tagService.refreshLabelsEvent.subscribe({
      next: patentId => {
        if (this.patent?.general?.docdb_family_id === patentId) {
          this.loadTags();
        }
      }
    });
    this.subscriptions.add(refreshLabelsEvent$);

    const updateDisplayedTags$ = this.updateDisplayedTagsSubject.asObservable()
      .pipe(
        debounceTime(100)
      )
      .subscribe({
        next: (val) => {
          if (val) {
            this.updateDisplayedTags();
          }
        }
      });
    this.subscriptions.add(updateDisplayedTags$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  loadTags() {
    if (!this.patent?.custom_tags) {
      return;
    }

    this.displayedTags = [...this.patent.custom_tags];
    this.updateDisplayedTagsSubject.next(true);
  }

  canRemove(tag: TagModel): boolean {
    return this.user?.subscription?.type !== SubscriptionType.Free || this.user?.profile?.id === tag.assigner_id;
  }

  getTextColor(color: string) {
    return this.tagService.getTextColor(color);
  }

  getBoxTagStyle(color: string) {
    const darkColor = this.tagService.getDarkColor(color);
    return {'border-color': darkColor, 'color': this.getTextColor(color)};
  }

  onMouseOver(tag: TagModel) {
    if (!this.canRemove(tag)) {
      return;
    }
    const element = this.getTagElement(tag);
    element.style.backgroundColor = this.tagService.getDarkColor(tag.color);
  }

  onMouseOut(tag: TagModel) {
    if (!this.canRemove(tag)) {
      return;
    }
    const element = this.getTagElement(tag);
    element.style.backgroundColor = 'transparent';
  }

  unassign(tag: TagModel) {
    this.unassignEvent.emit(tag);
  }

  getDocTagId(tag: TagModel) {
    return `doc-tag-${tag.id}-${this.patent?.general.docdb_family_id ?? 0}`;
  }

  private getTagElement(tag: TagModel) {
    return this.container.querySelector(`#${this.getDocTagId(tag)}`) as HTMLElement;
  }

  private generateCategoryHtml(item: { codes: string[], description: string, type: string, titleKey: string }): string {
    return `
      <div class="categories-text">
          <div class="open-sans-bold">
              <div>${item.type}</div>
              <div class="text-nowrap ml-3">${item.codes.join('<br/>')}</div>
          </div>
          <div class="pl-4">${item.description}</div>
      </div>`;
  }

  private getCodeType(code: string): string {
    return this.patent.bibliographic.ipc4?.find(c => code.startsWith(c.toString())) ? 'IPC' : 'CPC';
  }

  private generateDescription(data: { root_title: string, parent_title: string, title: string }): string {
    return `
      <ul>
          <li>
              ${data.root_title}
              <ul>
                  <li>
                      ${data.parent_title}
                      <ul>
                          <li>${data.title}</li>
                      </ul>
                  </li>
              </ul>
          </li>
    </ul>`;
  }

  private generateTitleIdentifier(codeType: string, data: {
    root_title: string,
    parent_title: string,
    title: string
  }): string {
    return (codeType + data.root_title.trim() + data.parent_title.trim() + data.title.trim()).toLowerCase();
  }

  private groupSameCodes(greenCodes: {}[]): { codes: string[], description: string, type: string, titleKey: string }[] {
    const groupedCodes: { codes: string[], description: string, type: string, titleKey: string }[] = [];

    greenCodes.forEach((item) => {
      const actualCode = Object.keys(item)[0];
      const codeType = this.getCodeType(actualCode);
      const titleKey = this.generateTitleIdentifier(codeType, item[actualCode]);
      const foundSameItem = groupedCodes.find(c => c.titleKey === titleKey);

      if (foundSameItem) {
        foundSameItem.codes.push(actualCode);
      } else {
        groupedCodes.push({
          type: codeType,
          codes: [actualCode],
          description: this.generateDescription(item[actualCode]),
          titleKey: titleKey
        });
      }
    });

    return groupedCodes;
  }

  private calculateVisibleWidthForTags() {
    if (!this.container) {
      return 0;
    }

    const assignTagOrMoreTagsWidth = 72;
    const ele = this.container.querySelector('.custom-tags-container') as HTMLElement;
    return ele ? ele.clientWidth - assignTagOrMoreTagsWidth : 0;
  }

  private updateDisplayedTags() {
    if (!this.patent?.custom_tags) {
      return;
    }

    const visibleWidth = this.calculateVisibleWidthForTags();
    if(visibleWidth<0){
      return;
    }
    const extraSpace = 2;
    let totalWidth = 0;
    let numberTagsToDisplay = 0;
    const tagElements = Array.from(this.container.querySelectorAll('.inline-tag-label'));
    for (let i = 0; i < tagElements.length; i++) {
      totalWidth += (tagElements[i] as HTMLElement).scrollWidth + extraSpace;
      if (totalWidth > visibleWidth) {
        break;
      }
      numberTagsToDisplay = i + 1;
    }

    this.allTagsAssigned = [...this.patent.custom_tags];
    this.displayedTags = [...this.patent.custom_tags.slice(0, numberTagsToDisplay)];
    this.overflowTags = [...this.patent.custom_tags.slice(numberTagsToDisplay)];
    this.isLoadingTags = false;
    this.afterLoadEvent.emit();
  }
}
