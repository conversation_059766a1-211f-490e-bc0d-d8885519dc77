
<div [id]="id" class="tag-selector-container position-relative" ngbDropdown #dropdown="ngbDropdown" container="body" [autoClose]="true"
  [open]="isEditingTag">
  <ng-container *ngIf="!editMode && !hideAddTagButton">
    <ng-container *ngIf="!overflowTags?.length">
      <a href="javascript:void(0)" data-toggle="dropdown" *ngIf="showIcon"
        [ngClass]="{'disabled': !hasSelectedPatents()}" ngbDropdownToggle (click)="openDropdown()"
        class="item-bar icon-add-tag caret-off" ngbTooltip="Add tags for selected patents "> Add tag
      </a>

      <a href="javascript:void(0)" *ngIf="!showIcon" data-toggle="dropdown" class="tag-button cursor-pointer caret-off tag-button-inline" ngbDropdownToggle
        (click)="openDropdown()">
        Add tag
      </a>
    </ng-container>

    <a href="javascript:void(0)" *ngIf="isOverflowShowing" data-toggle="dropdown" ngbDropdownToggle class="overflowTags"
      (click)="openDropdown()">
      +{{ overflowTags?.length }} <i class="fa fa-angle-down ms-1"></i>
    </a>
  </ng-container>
  <ng-container *ngIf="hideAddTagButton">
    <div ngbDropdownAnchor></div>
  </ng-container>

  <div ngbDropdownMenu class="dropdown-menu p-0 tag-selector-dropdown-menu" [class.color-container]="isEditingTag">
    <ng-container *ngIf="!isEditingTag else editTagTemplate">
      <div class="px-3 py-2 cursor-pointer assigned-tags" (click)="showAddedTags = false" *ngIf="showAddedTags && overflowTags?.length">
        Select existing tag
      </div>
      <div class="input-group" *ngIf="showAddedTags">
        <input id="filter-term" appAutofocus type="text" class="form-control border-0"
               placeholder="Search for tags" [(ngModel)]="filterTerm" (ngModelChange)="searchTags()">
        <div class="input-group-append">
          <button id="filter-button" ngbTooltip="Search within Results" class="btn border-0" (click)="searchTags()">
            <i class="fa fa-search icon"></i>
          </button>
        </div>
      </div>

      <div class="m-1 tag-container">
        <ng-container *ngFor="let tag of (!showAddedTags ? overflowTags : listTags)">
          <div id="{{tag.id}}-{{patent?.general?.docdb_family_id || ''}}" class="mx-2 my-2 tag-box d-inline-block list-tag-label" (mouseover)="onMouseOver(tag)" (mouseout)="onMouseOut(tag)"
            [ngStyle]="{'background-color': '#' + tag.color, 'color': getTextColor('#'  + tag.color)}">
            <div class="px-2 tag-item" [ngClass]="{'cursor-pointer': showAddedTags }" (click)="assign(tag)">
              {{ tag.name }}
            </div>
            <div class="tag-unassign cursor-pinter" (click)="unassign(tag)" *ngIf="!showAddedTags && canManageTags">
              <i class="fa fa-close px-1 cursor-pointer"></i>
            </div>
          </div>
          <br>
        </ng-container>
      </div>

      <div class="m-3 pt-3 tag-footer" *ngIf="((!userService.isFreeUser() && !userService.isCollaboratorUser()) || !showAddedTags) && canManageTags">
        <a href="javascript:void(0)" class="item-bar icon-add-tag" (click)="createNewTag()" *ngIf="!userService.isFreeUser() && showAddedTags"> Create tag</a>
        <a href="javascript:void(0)" class="item-bar icon-add-tag" (click)="addTag()" *ngIf="!showAddedTags"> Add tag</a>
      </div>
    </ng-container>

    <ng-template #editTagTemplate>
      <div *ngIf="isEditingTag" ngbDropdownAnchor></div>
      <div class="input-group border-0 d-flex align-items-center">
        <div class="ms-2 cursor-pointer color-button" [ngStyle]="{'background': '#' + tagEdit.color}" (click)="showColors = !showColors"
             [ngbTooltip]="selectedColor?.name" tooltipClass="white-tooltip">
        </div>
        <input type="text" class="form-control border-0 name-tag m-0 ps-2" placeholder="Name your tag" [(ngModel)]="tagEdit.name"
          [ngClass]="{'is-invalid': invalidField === 'name'}" (ngModelChange)="onChangeName()" (keyup.enter)="saveTag()" maxlength="255">
        <div class="input-group-append" [ngClass]="{'is-invalid': invalidField === 'color'}">
          <i class="fa fa-check pe-2 cursor-pointer" (click)="saveTag()" ></i>
          <i class="fa fa-close px-2 cursor-pointer" (click)="reset()"></i>
        </div>
        <div *ngIf="invalidMessage" class="invalid-feedback px-2">
          {{ invalidMessage }}
        </div>
      </div>
    </ng-template>
    <div class="colors-container p-1" *ngIf="showColors">
      <div class="m-2">Color</div>
      <div class="d-flex flex-wrap m-2">
        <div class="m-1 cursor-pointer color-button d-flex align-items-center" *ngFor="let df of defaultColors" [ngStyle]="{'background': df.code}"
          (click)="onSelectedColor(df.code)" [ngbTooltip]="df.name" tooltipClass="white-tooltip">
          <i class="far fa-check label-color-selected" *ngIf="df.code === selectedColor?.code"
             [style.color]="getTextColor(df['code'])"></i>
        </div>
      </div>
    </div>
  </div>

</div>

<ng-template #alertModal>
  <div class="modal-body p-0">
    <div class="alert alert-success m-0" role="alert">
      <a href="javascript:void(0)" class="close" (click)="modal.close()">
        <span aria-hidden="true">&times;</span>
      </a>
      <h4 class="alert-heading mb-3">{{getAlertTitle()}}</h4>
      <div class="d-flex align-items-center">
        <div class="me-2 my-2 px-2 cursor-pointer tag-item"
          [ngStyle]="{'background-color': '#' + tagEdit.color, 'color': getTextColor('#'  + tagEdit.color)}">
          {{ tagEdit.name }}
        </div> <span class="alert-external-creation" *ngIf="editMode">was created and added to your list</span>
        <span class="alert-message message" [innerHTML]="success"></span>
        <ng-container *ngIf="patent">
          <i class="mx-2" [ngClass]="getFlagIcon(patent.general.raw_publication_number)"></i>
          <span class="alert-message publication-number pt-1">{{patent.general.raw_publication_number}}</span>
        </ng-container>
      </div>
    </div>
  </div>
</ng-template>
