.tag-selector-container {
  .item-bat, .tag-button, .overflowTags, .dropdown-menu {
    z-index: 1050;
  }

  .tag-button {
    font-family: 'Open Sans Regular';
    font-size: 0.875rem;
    color: #4B5D66;
    border: thin dashed #0000004D;
    border-radius: 4px;
    padding-top: 0.5px;
    padding-left: 10px;
    padding-right: 10px;
    text-decoration: none;
  }

}
.tag-selector-dropdown-menu{
  &.dropdown-menu {
    font-family: 'Open Sans Regular';
    line-height: 1.5rem;
    z-index: 1050;
    &.show {
      min-width: 250px;
      max-width: 350px;
      overflow: auto;
      &.container-color-picker{
        max-height: 520px;
        overflow: visible;
      }
    }
    &.color-container{
      max-width: 265px;
    }
    .tag-container {
      height: 190px;
      overflow-y: auto;
    }
    .tag-footer {
      border-top: 1px solid #E8F0F3;
      .item-bar {
        font-size: 1rem;
        color: #0F2C35;
        padding-top: 3px;
        &:hover {
          color: #2F8A76;
          text-decoration: none;
        }
      }
    }
  }
}

.icon-add-tag {
  padding-right: 20px;
  padding-left: 25px;
  background-position: 0;
  background-position-y: 7px;
  background-size: 15px;
  background-repeat: no-repeat;
  transition: all 0.2s ease;
  background-image:  url('/assets/images/layout2022/icon-add-tag.svg');
  &:hover,&.active{
    background-image:  url('/assets/images/layout2022/icon-add-tag-hover.svg');
  }
  &.disabled {
    background-image: url('/assets/images/layout2022/icon-add-tag-disabled.svg');
  }
}

.assigned-tags {
  background: #E8F0F3 0% 0% no-repeat padding-box;
  border-radius: 2px 2px 0px 0px;
  color: #4B5D66;
  font-family: 'Open Sans Bold';
}

.input-group {
  border-top: 1px solid #698A95;
  border-bottom: 1px solid #698A95;
}

.tag-item {
  border: 1px solid #00000033;
  border-radius: 4px;
  display: inline-block;
  font-size: 0.875rem !important;
  font-weight: normal;
  max-width: 62.5rem;
  overflow-x: clip;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.alert-message {
  font-family: 'Open Sans Regular';
  font-size: 1rem;
  font-weight: normal;
}

.alert-message.message {
  color: #313133;
}
.alert-message.publication-number {
  color: #00A083;
}

.color-button {
  border: 1px solid #00000033;
  border-radius: 4px !important;
  width: 16px;
  height: 16px;

  .label-color-selected {
    text-align: center;
    font-family: "Font Awesome 6 Pro";
    font-size: 1rem;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
  }
}

.colors-container {
  top: 60px;
  background: #F8F8F8;
  box-shadow: 0px 5px 10px #00000029;
  border-radius: 5px;
  width: 264px;
  z-index: 1050;
}

.more-colors-button {
  border: 1px solid #337AB7;
  border-radius: 4px;
  background: #FFF;
  text-align: center;
  color: #337AB7;
  width: 16px;
  height: 16px;
}

.overflowTags {
  font-family: 'Open Sans Regular';
  font-size: 0.9rem;
  border: 1px solid #00000033;
  border-radius: 4px;
  display: block;
  height: 23px;
  padding-left: 10px;
  padding-right: 10px;
  color: #4B5D66;
  .fa-angle-down {
    font-size: 0.85rem;
  }
}

.alert-external-creation {
  font-family: 'Open Sans Regular';
  font-weight: normal;
  font-size: 0.875rem;
  color: #313133;
}

.tag-box {
  border-radius: 4px;
  font-size: 0.775rem;
  .tag-unassign {
    display: none;
  }
  &:hover {
    .tag-unassign {
      display: inline;
    }
  }
}

:host::ng-deep.container{
  &-theme-colors, &-standard-colors, &-recent-colors, &-more-colors{
    padding-left: .5rem;
    padding-right: .5rem;
  }
}
