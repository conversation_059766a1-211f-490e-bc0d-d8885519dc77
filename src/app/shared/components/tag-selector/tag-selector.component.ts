import {
  Component,
  Input,
  OnInit,
  ViewChild,
  TemplateRef,
  Output,
  EventEmitter,
  AfterContentChecked,
  ChangeDetectorRef,
  <PERSON><PERSON><PERSON>roy
} from '@angular/core';
import {
  BaseStoreService,
  BooleanSearchService,
  CitationSearchService,
  CollectionService,
  ColorUtil,
  MonitorService,
  MonitorStoreService,
  PatentNumberService,
  PatentService,
  SemanticSearchService,
  UserService
} from '@core';
import { TagModel } from '@core/models/tag.model';
import { TagService } from '@core/services/tag/tag.service';
import { NgbActiveModal, NgbDropdown, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TagLabelComponent } from '../tag-label/tag-label.component';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tag-selector',
  templateUrl: './tag-selector.component.html',
  styleUrls: ['./tag-selector.component.scss']
})
export class TagSelectorComponent implements OnInit, AfterContentChecked, OnDestroy {

  @Input() storeService: BaseStoreService;
  @Input() searchService: SemanticSearchService | CitationSearchService | BooleanSearchService | CollectionService | MonitorService;
  @Input() showIcon = true;
  @Input() patent: any;
  @Input() container: HTMLElement;
  @Input() editMode = false;
  @Input() canManageTags = false;
  @Input() tag: TagModel;
  @Input() hideAddTagButton = false

  @Output() saveTagEvent: EventEmitter<TagModel> = new EventEmitter();
  @Output() cancelTagEvent: EventEmitter<boolean> = new EventEmitter();
  @Output() selectTagEvent: EventEmitter<TagModel> = new EventEmitter();
  @Output() unSelectTagEvent: EventEmitter<TagModel> = new EventEmitter();

  _tagLabel: TagLabelComponent;
  @Input() set tagLabel(value: TagLabelComponent) {
    this._tagLabel = value;
    if (this._tagLabel) {
      const afterLoadEvent$ = this._tagLabel.afterLoadEvent.subscribe({
        next: () => {
          this.reset();
        }
      });
      this.subscriptions.add(afterLoadEvent$);

      const unassignEvent$ = this._tagLabel.unassignEvent.subscribe({
        next: (tag) => {
          this.unassign(tag);
        }
      });
      this.subscriptions.add(unassignEvent$);
    }
  };
  get tagLabel(): TagLabelComponent {
    return this._tagLabel;
  }

  @ViewChild('alertModal') alertModal: TemplateRef<any>;
  @ViewChild('dropdown') dropdown: NgbDropdown;

  filterTerm = '';
  showColors: boolean = false;
  success = '';

  defaultColors = ColorUtil.TAG_COLORS;

  tagEdit: TagModel;
  unassignedTags: TagModel[] = [];
  listTags: TagModel[] = [];
  overflowTags: TagModel[] = [];

  modal: NgbActiveModal = null;

  isEditingTag = false;
  isOverflowShowing = false;
  showAddedTags = false;

  invalidField: string;
  invalidMessage: string;
  id: string;
  selectedColor: { name: string, code: string };

  private subscriptions = new Subscription();

  constructor(
    private tagService: TagService,
    private patentService: PatentService,
    private patentNumberService: PatentNumberService,
    private modalService: NgbModal,
    public userService: UserService,
    private monitorStoreService: MonitorStoreService,
    private changeDetectorRef: ChangeDetectorRef
  ) { }

  get tags(){
    return this.tagService.tags;
  }
  get showInlineAddTag(): boolean{
    return this.tagLabel.overflowTags?.length > 0;
  }

  ngOnInit(): void {
    if (this.patent || this.hideAddTagButton) {
      this.reset();
    }
    if (!this.editMode) {
      this.id = Math.random().toString(36).substring(2, 9);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngAfterContentChecked(): void {
    if (this.editMode) {
      if (!this.isEditingTag && !this.tagEdit) {
        this.tagEdit = {...this.tag};
      }
      this.changeDetectorRef.detectChanges();
      this.isEditingTag = true;
      setTimeout(() => {
        this.focusInputName();
      });
    }
  }

  hasSelectedPatents(): boolean {
    return !!(this.storeService?.selectedPatentIds.length) ;
  }

  openDropdown(expandDropdown = false): void {
    this.reset();
    if (expandDropdown) {
      this.dropdown.open();
    }
  }

  searchTags(): void {
    if (this.unassignedTags.length === 0) {
      this.listTags = this.tags.filter(tag => tag.name.toLowerCase().includes(this.filterTerm.toLowerCase()));
    } else {
      this.listTags = this.unassignedTags.filter(tag => tag.name.toLowerCase().includes(this.filterTerm.toLowerCase()));
    }
  }

  getTextColor(color: string): string {
    return this.tagService.getTextColor(color);
  }

  assign(tag: TagModel): void {
    if (this.hideAddTagButton) {
      this.dropdown.close();
      this.selectTagEvent.emit(tag);
      return;
    }

    if (!this.showAddedTags && !this.isEditingTag) {
      return;
    }

    this.tagEdit = tag;
    this.dropdown.close();
    const payload = this.buildPayload();
    const assign$ = this.tagService.assign(tag, payload).subscribe({
      next: () => {
        tag.assigner_id = this.userService.getUser().profile.id;
          if (this.patent) {
            this.success = `tag was assigned to`;
          } else {
            this.success = `tag was assigned to <strong>${payload['document_ids'].length}</strong> patent document${payload['document_ids'].length === 1 ? '' : 's'}`;
          }
          if (this.patent) {
            if (!this.patent.custom_tags) {
              this.patent.custom_tags = [];
            }
            this.patent.custom_tags.push(tag);
            this.tagService.refreshLabelsEvent.emit(this.patent.general.docdb_family_id);
          } else {
            const documents = this.searchService.getDocuments();
            documents.filter(document => this.storeService.selectedPatentIds.includes(document['general'].docdb_family_id))
              .forEach(document => {
                if (!('custom_tags' in document)) {
                  document['custom_tags'] = [];
                }

                if (document['custom_tags'].findIndex(tg => tg.id === tag.id) === -1) {
                  document['custom_tags'].push(tag);
                  this.tagService.refreshLabelsEvent.emit(document['general'].docdb_family_id);
                }
              });
          }
      },
      error: (error) => {
        console.log(error);
      }
    });
    this.subscriptions.add(assign$);
  }

  getFlagIcon(publication_number: string): string {
    return this.patentService.getFlagCssByPublication(publication_number, FlagSizeEnum.MD).toLowerCase();
  }

  reset(): void {
    if (this.editMode) {
      this.cancelTagEvent.emit(true);
      return;
    }
    this.filterTerm = '';
    this.isEditingTag = false;
    this.showColors = false;

    if (this.tagLabel) {
      this.unassignedTags = this.tags.filter(tag => !this.tagLabel.allTagsAssigned?.find(tg => tg.id === tag.id)) || this.tags;
      this.listTags = [...this.unassignedTags];
      this.overflowTags = this.tagLabel.overflowTags;
      this.isOverflowShowing = this.tagLabel.overflowTags?.length > 0;
    } else {
      this.listTags = [...this.tags];
    }
    if (this.isOverflowShowing) {
      this.showAddedTags = false;
      this.container?.getElementsByTagName('app-tag-selector')[0].classList.add('show');
    } else {
      this.searchTags();
      this.showAddedTags = true;
      this.container?.getElementsByTagName('app-tag-selector')[0].classList.remove('show');
      this.container?.getElementsByTagName('app-tag-selector')[0].classList.remove('display-hover');
    }
  }

  createNewTag(): void {
    this.selectedColor = ColorUtil.getRandomTagColor();
    this.tagEdit = {name: '', color: this.selectedColor.code.replace('#', '')};
    this.isEditingTag = true;
    if (!this.isOverflowShowing && this.container) {
      this.container.getElementsByTagName('app-tag-selector')[0].classList.add('show');
    }
    setTimeout(() => {
      this.focusInputName();
    });
  }

  private focusInputName(): void {
    const newTagElement = <HTMLElement>document.getElementsByClassName('name-tag')[0];
    if (newTagElement) {
      newTagElement.focus();
    }
  }

  addTag() {
    this.showAddedTags = true;
  }

  saveTag(): void {
    this.invalidMessage = '';
    if (!this.isValid()) {
      this.invalidMessage = this.invalidField === 'name' ? 'Name is required' : 'Color is required';
      return;
    }

    if (!this.tagEdit.id) {
      const create$ = this.tagService.create(this.tagEdit).subscribe({
        next: (data) => {
          if (this.editMode) {
            this.modal = this.modalService.open(this.alertModal);
            this.saveTagEvent.emit(data);
          } else {
            this.tags.push(data);

            if (this.patent || this.storeService.selectedPatentIds.length) {
              this.assign(data);
            }
            this.reset();
            this.showAddedTags = false;
          }
        },
        error: ({error}) => {
          this.invalidMessage = error.message;
          this.invalidField = 'name';
        }
      });
      this.subscriptions.add(create$);
    } else {
      if (this.editMode) {
        const update$ = this.tagService.update({name: this.tagEdit.name, color: this.tagEdit.color}, this.tagEdit.id).subscribe({
          next: data => {
            this.saveTagEvent.emit(data);
          }
        });
        this.subscriptions.add(update$);
      }
    }
  }

  onSelectedColor(event: string): void {
    this.tagEdit.color = event.replace('#', '');
    this.showColors = false;
    if (this.invalidField === 'color') {
      this.invalidMessage = '';
      this.invalidField = '';
    }
    this.focusInputName();
  }

  isValid(): boolean {
    this.invalidField = (this.tagEdit.name ? '' : 'name') || (this.tagEdit.color ? '' : 'color');
    return !!this.tagEdit.name && !!this.tagEdit.color;
  }

  onChangeName(): void {
    if (this.invalidField === 'name') {
      this.invalidMessage = '';
      this.invalidField = '';
    }
  }

  getAlertTitle(): string {
    if (this.editMode) {
      return 'Tag created successfully';
    } else {
      return 'Tag added successfully';
    }
  }

  onMouseOver(tag: TagModel) {
    if (!this.isOverflowShowing) {
      return;
    }
    const element = document.getElementById(`${tag.id}-${this.patent.general.docdb_family_id}`);
    element.style.backgroundColor = this.tagService.getDarkColor(tag.color);
  }

  onMouseOut(tag: TagModel) {
    if (!this.isOverflowShowing) {
      return;
    }
    const element = document.getElementById(`${tag.id}-${this.patent.general.docdb_family_id}`);
    element.style.backgroundColor = '#' + tag.color;
  }

  unassign(tag: TagModel) {
    this.dropdown.close();
    if (this.hideAddTagButton) {
      this.unSelectTagEvent.emit(tag);
      return;
    }

    const unassign$ = this.tagService.unassign(tag.id, this.patent.general.docdb_family_id).subscribe({
      next: () => {
        this.patent.custom_tags = this.patent.custom_tags.filter(tg => tg.id !== tag.id);
        this._tagLabel.loadTags();
        this.reset();
      },
      error: (err) => { console.log(err); }
    });
    this.subscriptions.add(unassign$);
  }

  private buildPayload() {
    const payload = {};
    const documents = this.patent ? [this.patent?.general?.docdb_family_id] : this.storeService.selectedPatentIds.map(String);

    if (this.storeService.isMonitorSearch()) {
      payload['monitor_run_id'] = this.monitorStoreService.selectedMonitorRun;
    } else {
      payload['search_hash'] = this.storeService.searchHash;
    }

    payload['document_ids'] = documents;

    return payload;

  }
}
