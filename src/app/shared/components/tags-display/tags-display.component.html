<div class="tags-container d-flex align-items-center justify-content-start flex-wrap {{tagsDisplayCssClass}} {{tagsContainerCssClass}}"
     [ngClass]="isTagsSelectShown ? 'tags-select-shown' : ''"
     [ngStyle]="getTagsContainerStyle()"
     (hidden)="onTagsSelectHidden()" (shown)="onTagsSelectShown()" 
     (click)="onTagsContainerClicked($event);">
  <div class="tag-item tag-patent-standard flex-shrink-0 tag-mandatory-item" [ngbTooltip]="standardText"
       tooltipClass="white-tooltip"
       container="body" *ngIf="hasSepTag">
    <img src="assets/images/relevant-tag-standard.svg"> <span class="tag-name content-label-small">SEP</span>
  </div>

  <div *ngIf="hasGreenTag" appSelectableTooltip [selectableTooltipPopover]="greenPatentTooltip"
       class="tag-item content-label-small tag-green-patent tag-mandatory-item flex-shrink-0">
    <div #greenPatentTooltip="ngbPopover" [ngbPopover]="popoverGreenPatentTooltipTemplate"
         popoverClass="green-patent-popover scrollbar-2024-sm white-popover popover-lg" container="body" [autoClose]="'outside'">
      <i class="fa-regular fa-leaf fa-1x m-r-spacing-xx-s"></i>
      <span class="tag-name content-label-small">Green</span>
    </div>
    <ng-template #popoverGreenPatentTooltipTemplate>
      <div class="popover-title m-b-spacing-xx-s">This document is considered green based on the following classification codes:</div>
      <div class="popover-divider m-t-spacing-xx-s"></div>
      <div class="popover-descriptions m-y-spacing-md text-left" [innerHTML]="greenTooltip"></div>
    </ng-template>
  </div>

  <div *ngIf="false" class="tag-item tag-special-patent text-center tag-mandatory-item" ngbTooltip="Special patent"
       tooltipClass="white-tooltip">
    <i class="fa-regular fa-sun"></i>
  </div>

  <ng-container *ngFor="let tag of displayedTags;"
                [ngTemplateOutlet]="tagItemTemplate" [ngTemplateOutletContext]="{tag: tag, idSuffix: ''}">
  </ng-container>

  <ng-container *ngIf="isOverflowTagsDisplayed">
    <ng-container *ngFor="let tag of overflowTags;"
                  [ngTemplateOutlet]="tagItemTemplate" [ngTemplateOutletContext]="{tag: tag, idSuffix: ''}">
    </ng-container>
  </ng-container>

  <ng-container *ngIf="hasOverflowTags">
    <div *ngIf="!isOverflowTagsDisplayed" class="tag-item tag-item-action tag-item-show-more"
         (click)="onShowMoreTagsClicked($event)"
         [ngbTooltip]="'See ' + overflowTags.length + ' more ' + ('tag' | pluralize: overflowTags.length)"
         tooltipClass="white-tooltip">
      <div class="tag-item-action-text content-label-small">+{{ overflowTags.length }}</div>
    </div>

    <div *ngIf="isOverflowTagsDisplayed" class="tag-item tag-item-action" (click)="onShowLessTagsClicked($event)">
      <div class="tag-item-action-text content-label-small">Show less</div>
    </div>
  </ng-container>

  <div *ngIf="canManageTags" [ngbTooltip]="addTagTooltipTemplate"
       tooltipClass="white-tooltip" container="body">
    <div #tagsSelectTooltip="ngbTooltip"
         (hidden)="onTagsSelectHidden()" (shown)="onTagsSelectShown()" [ngbTooltip]="tagsSelectTemplate"
         tooltipClass="white-tooltip tooltip-big" triggers="click:blur" autoClose="outside" container="body"
         placement="bottom auto" (click)="$event.preventDefault(); $event.stopPropagation();"
         [ngClass]="addTagButtonCssClass ?? 'tag-item tag-item-action'">
      <i class="tag-item-action-icon fa-regular fa-plus"></i>
      <span class="tag-item-action-text" *ngIf="hasNoTags">Add tag</span>
    </div>

    <ng-template #tagsSelectTemplate>
      <app-tags-select [patent]="patent" [tags]="tags" [storeService]="storeService"
                       [searchInputPlaceholder]="searchInputPlaceholder"
                       [noTagsMessage]="noTagsMessage"
                       (tagsSelectCanceled)="tagsSelectTooltip.close(true)"
                       (tagDeleted)="tagsSelectTooltip.close(true)"
                       (tagsSelectAddedTag)="onTagsSelectAddedTag($event, tagsSelectTooltip)">
      </app-tags-select>
    </ng-template>
  </div>

  <div *ngIf="onlySelectTags" [ngClass]="{'d-none': !hasNoTags}" >
    <ng-content select="[selectorPlaceholder]"></ng-content>
  </div>

</div>

<div class="all-tags-container-hidden d-flex align-items-center justify-content-start flex-wrap"
     style="position: absolute; visibility: hidden;">
  <div *ngIf="hasSepTag" class="tag-item tag-patent-standard flex-shrink-0 tag-mandatory-item">
    <img src="assets/images/relevant-tag-standard.svg"> <span class="tag-name content-label-small">SEP</span>
  </div>

  <div *ngIf="hasGreenTag" class="tag-item content-label-small tag-green-patent tag-mandatory-item flex-shrink-0">
    <div >
      <i class="fa-regular fa-leaf fa-1x m-r-spacing-xx-s"></i>
      <span class="tag-name content-label-small">Green</span>
    </div>
  </div>

  <div *ngIf="false" class="tag-item tag-special-patent text-center tag-mandatory-item" ngbTooltip="Special patent"
       tooltipClass="white-tooltip">
    <i class="fa-regular fa-sun"></i>
  </div>

  <ng-container *ngFor="let tag of tagsToDisplay;"
                [ngTemplateOutlet]="tagItemTemplate" [ngTemplateOutletContext]="{tag: tag, idSuffix: '-tmp'}">
  </ng-container>

  <div class="tag-item tag-item-action tag-item-show-more">
    <div class="tag-item-action-text content-label-small">+{{ tagsToDisplay.length }}</div>
  </div>

  <div *ngIf="canManageTags">
    <div [ngClass]="addTagButtonCssClass ?? 'tag-item tag-item-action'">
      <i class="tag-item-action-icon fa-regular fa-plus"></i>
      <span class="tag-item-action-text" *ngIf="hasNoTags">Add tag</span>
    </div>
  </div>
</div>

<ng-template #tagItemTemplate let-tag="tag" let-idSuffix="idSuffix">
  <app-tag-item 
    [tag]="tag" 
    [idSuffix]="idSuffix || ''" 
    [canManageTag]="canManageTags" 
    [showUnassignButton]="canUnassignTag(tag)" 
    [selectedTagId]="editingTag?.id"
    [showTruncatedName]="showTruncatedNames"
    [showOpenTagCollection]="!isTagCollection || storeService['collection']?.tag.id !== tag.id"
    (tagUnassign)="unassignTag($event)">
  </app-tag-item>
</ng-template>

<ng-template #tagEditTemplate>
  <app-tag-edit [tag]="editingTag">
  </app-tag-edit>
</ng-template>

<ng-template #addTagTooltipTemplate>
  <div class="text-left p-spacing-sm">
    <div class="content-heading-h6 p-b-spacing-xx-s">Tags</div>
    <div class="content-body-xsmall">
      Add an existing tag or create a new one. Tags are applied on a family level.
    </div>
  </div>
</ng-template>
