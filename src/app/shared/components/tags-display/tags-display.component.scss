@import 'scss/figma2023/index';
@import 'scss/layout2021/variables';

:host {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  flex: 1 1 auto !important;
  width: 100%;
  overflow: hidden;
}

.tags-container, .all-tags-container-hidden {
  min-height: 2rem;
  padding: $spacing-system-spacing-xx-s;
  gap: $spacing-system-spacing-sm;

  @import "scss/components/tag_item";

  &.tags-select-shown {
    background-color: $colours-background-bg-tertiary;
    border-radius: $radius-sm;
  }

  &.tags-expanded-display {
    overflow: auto;
  }

  &.tags-collapsed-display {
    max-height: var(--tagsCollapsedDisplayHeight, 5rem) !important;
  }
}

::ng-deep {
  .categories-text {
    li {
      text-align: left;
    }
  }

  app-tags-select {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-content: flex-start;
    justify-content: flex-start;
    align-items: stretch;
    height: 100%;
  }
}
