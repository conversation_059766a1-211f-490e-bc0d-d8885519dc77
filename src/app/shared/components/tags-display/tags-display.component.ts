import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output, ViewChild,
  ViewContainerRef
} from '@angular/core';
import { TagModel } from '@core/models/tag.model';
import { BehaviorSubject, debounceTime, Subscription, timer } from 'rxjs';
import { BaseStoreService, SubscriptionType, TagService, TeamUser, User, UserService } from '@core';
import { debounce, filter, tap } from 'rxjs/operators';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-tags-display',
  templateUrl: './tags-display.component.html',
  styleUrls: ['./tags-display.component.scss']
})
export class TagsDisplayComponent implements OnInit, OnDestroy, AfterViewInit {

  @Input() container: HTMLElement;
  @Input() canManageTags: boolean = false;
  @Input() showIcon: boolean;
  @Input() collapsedDisplayRows: number = 2;
  @Input() expandedDisplayRows: number = 3;
  @Input() updateTagsOnResize: boolean = false;
  @Input() updateTagsDelay: number = 1000;
  @Input() storeService: BaseStoreService;
  @Input() addTagButtonCssClass: string = null;
  @Input() onlySelectTags: boolean = false;
  @Input() openAfterSelect: boolean = true;
  @Input() tagsDisplayCssClass: string = '';
  @Input() searchInputPlaceholder: string = null;
  @Input() noTagsMessage: string = null;
  @Input() showTruncatedNames: boolean = true;
  @Output() tagAddedEvent = new EventEmitter<TagModel>();
  @Output() tagRemovedEvent = new EventEmitter<TagModel>();
  @Output() tagsChange = new EventEmitter<TagModel[]>();
  @Output() tagsDisplayShown = new EventEmitter<boolean>();
  @Output() tagsDisplayHidden = new EventEmitter<boolean>();
  displayedTags: TagModel[] = [];
  overflowTags: TagModel[] = [];
  pendingOverflowTagIds: number[] = [];
  isCalculatingTagsToDisplay = true;
  greenTooltip = '';
  user: User;
  isOverflowTagsDisplayed: boolean = false;
  isTagsSelectShown = false;
  editingTag: TagModel = null;
  tagsContainerCssClass: string = '';

  @ViewChild('onlySelectTagsTooltip') onlySelectTagsTooltip: NgbTooltip;

  private subscriptions = new Subscription();
  private updateDisplayedTagsSubject = new BehaviorSubject<number>(null);
  private resizeObserver: ResizeObserver = null;
  private checkContainerWidthIntervalId = null;
  private lastContainerWidth: number = null;
  private isTagsChanged = false;
  private openTagsSelectTimeoutId = null;

  private readonly TAGS_CONTAINER_PADDING_WIDTH = 0.25 * 2;
  private readonly TAG_ITEM_HEIGHT = 2;
  private readonly TAG_ITEMS_GAP = 0.5;

  constructor(
    private tagService: TagService,
    private readonly viewRef: ViewContainerRef,
    private userService: UserService
  ) {
  }

  private _tags: TagModel[];

  get tags(): TagModel[] {
    return this._tags;
  }

  @Input() set tags(value: TagModel[]) {
    this._tags = value || [];
    this.assignDisplayedTags(this.updateTagsDelay);
  }

  private _patent: any;

  get patent() {
    return this._patent;
  }

  @Input() set patent(value) {
    this._patent = value;
    this.assignDisplayedTags(this.updateTagsDelay);
  }

  get standardText(): string {
    let text = ''
    if (this.patent.tags.sep.length === 1) {
      text = 'standard ' + this.patent.tags.sep[0];
    } else {
      text = 'standards ' + this.patent.tags.sep.join(', ').replace(/,([^,]*)$/, ' and $1');
    }

    return 'This is an essential patent for ETSI ' + text;
  }

  get hasSepTag(): boolean {
    return this.patent?.tags?.sep;
  }

  get hasGreenTag(): boolean {
    return this.patent?.tags?.green;
  }

  get hasOverflowTags(): boolean {
    return this.overflowTags?.length > 0;
  }

  get hasNoTags(): boolean {
    return !(this.hasGreenTag || this.hasSepTag || this.tagsToDisplay.length > 0);
  }

  get patentTags(): TagModel[] {
    return this.patent?.custom_tags || [];
  }

  get tagsToDisplay(): TagModel[] {
    return this.tags?.length > 0 ? this.tags : this.patentTags;
  }

  get patentFamilyId(): number {
    if (this.patent?.general?.docdb_family_id) {
      return Number(this.patent?.general?.docdb_family_id);
    }
    return null;
  }

  get isTagCollection(): boolean {
    return this.storeService.isCollectionStore() && this.storeService['collection']?.tag;  
  }
  
  onTagsContainerClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.onlySelectTags && this.onlySelectTagsTooltip && !this.onlySelectTagsTooltip.isOpen()) {
      this.onlySelectTagsTooltip.open();
    }
  }

  ngOnInit(): void {
    this.assignDisplayedTags(this.updateTagsDelay);
    
    this.user = this.userService.getUser();
    if (this.patent?.general?.green_codes?.length > 0) {
      this.greenTooltip = this.groupSameCodes(this.patent.general.green_codes)
        .map(c => this.generateCategoryHtml(c))
        .reduce((a, b) => a + b, '');
    }

    const updateDisplayedTags$ = this.updateDisplayedTagsSubject.asObservable()
      .pipe(
        filter(val => val !== null),
        tap(() => {
          this.isCalculatingTagsToDisplay = true;
          this.updateTagsContainerCssClass();
        }),
        debounce((val: number) => timer(val)),
      )
      .subscribe({
        next: (val) => {
          this.updateDisplayedTags();
        }
      });
    this.subscriptions.add(updateDisplayedTags$);

    const tagChanged$ = this.tagService.tagChanged$
      .pipe(
        filter(tag => !!tag?.id),
      )
      .subscribe({
        next: (tag) => {
          let isChanged = false;
          this.tagsToDisplay.forEach((t, index) => {
            if (t.id === tag.id) {
              this.tagsToDisplay[index] = {...t, ...tag};
              isChanged = true;
            }
          });
          if (isChanged) {
            this.assignDisplayedTags(10);
          }
        }
      });
    this.subscriptions.add(tagChanged$);

    const assignedTags$ = this.tagService.assignedTags$
      .pipe(
        filter((val) => !!val && !!val.tag && !!val.documentIds),
        debounceTime(100)
      )
      .subscribe({
        next: ({tag, documentIds}) => {
          if (this.patentFamilyId && documentIds.includes(this.patentFamilyId)) {
            const tags: TagModel[] = this.patent.custom_tags || [];
            const existingTag = tags.find((t) => t.id === tag.id);
            if (!existingTag) {
              tag.assigned_at = (new Date().toISOString().slice(0, 19).replace('T', ' ')).toString();
              tag.assigned_by = this.user?.profile as TeamUser;
              tags.push(tag);
              this.patent.custom_tags = [...tags];
              this.assignDisplayedTags(10);
            }
          }
        }
      });
    this.subscriptions.add(assignedTags$);

    const deletedTags$ = this.tagService.deletedTags$
      .pipe(
        filter(tag => !!tag?.id),
      )
      .subscribe({
        next: (tag) => {
          this.patent.custom_tags = this.patent.custom_tags.filter(t => t.id !== tag.id);
          this.assignDisplayedTags(10);
        }
      });
    this.subscriptions.add(deletedTags$);
  }

  ngAfterViewInit() {
    const collapsedHeight = this.calculateTagsContainerHeight(this.collapsedDisplayRows);
    this.viewRef.element.nativeElement.style.setProperty('--tagsCollapsedDisplayHeight', `${collapsedHeight}rem`);

    if (this.expandedDisplayRows > this.collapsedDisplayRows) {
      const expandedHeight = this.calculateTagsContainerHeight(this.expandedDisplayRows);
      this.viewRef.element.nativeElement.style.setProperty('--tagsExpandedDisplayHeight', `${expandedHeight}rem`);
    }

    if (this.container && this.updateTagsOnResize) {
      const self = this;
      this.checkContainerWidthIntervalId = setInterval(() => {
        if (self.container.clientWidth !== self.lastContainerWidth) {
          self.lastContainerWidth = self.container.clientWidth;
          self.updateDisplayedTagsSubject.next(1000);
        }
      }, 1000);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    if (this.checkContainerWidthIntervalId) {
      clearInterval(this.checkContainerWidthIntervalId);
      this.checkContainerWidthIntervalId = null;
    }

    if (this.openTagsSelectTimeoutId) {
      clearTimeout(this.openTagsSelectTimeoutId);
    }
  }

  canUnassignTag(tag: TagModel): boolean {
    if (this.onlySelectTags) {
      return true;
    }
    return this.canManageTags &&
      (this.user?.subscription?.type !== SubscriptionType.Free || this.user?.profile?.id === tag.assigner_id);
  }

  getTextColor(color: string) {
    return this.tagService.getTextColor(color);
  }

  getBoxTagStyle(color: string) {
    return {'color': this.getTextColor(color), 'background-color': '#' + color};
  }

  isTagNameTruncated(tag: TagModel, idSuffix?: string): boolean {
    if (!this.showTruncatedNames) return true;
    
    const tagId = this.getDocTagId(tag, idSuffix);
    const tagElement = document.getElementById(tagId);
    if (!tagElement) return false;
    
    const nameElement = tagElement.querySelector('.tag-name');
    if (!nameElement) return false;
    
    return nameElement.scrollWidth > nameElement.clientWidth;
  }

  unassignTag(tag: TagModel) {
    if (this.patentFamilyId) {
      const unassign$ = this.tagService.unassign(tag.id, this.patentFamilyId).subscribe({
        next: () => {
          this.tagRemovedEvent.emit(tag);
          this.patent.custom_tags = this.tagsToDisplay.filter(t => t.id !== tag.id);
          this.tagsChange.emit([...this.patent.custom_tags]);
          this.assignDisplayedTags(100);
        }
      });
      this.subscriptions.add(unassign$);
    } else {
      this.tagRemovedEvent.emit(tag);
      this.tagsChange.emit([...this.tagsToDisplay.filter(t => t.id !== tag.id)]);
    }
  }

  getDocTagId(tag: TagModel, idSuffix: string) {
    return `doc-tag-${tag.id}-${this.patentFamilyId}${idSuffix}`;
  }

  onShowMoreTagsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isOverflowTagsDisplayed = true;
    this.updateTagsContainerCssClass();
  }

  onShowLessTagsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isOverflowTagsDisplayed = false;
    this.updateTagsContainerCssClass();
  }

  getTagsContainerStyle() {
    if (this.isCalculatingTagsToDisplay && this.expandedDisplayRows) {
      const collapsedHeight = this.calculateTagsContainerHeight(this.expandedDisplayRows);
      return {'max-height': `${collapsedHeight}rem`};
    }
  }

  onTagsSelectHidden() {
    if (this.isTagsChanged) {
      this.assignDisplayedTags(100);
    }
    this.isTagsSelectShown = false;
    this.tagsDisplayHidden.emit(true);
  }

  onTagsSelectAddedTag(tag: TagModel, tagsSelectTooltip: NgbTooltip) {
    const updateTagsDelay = 100;
    tag.assigned_at = (new Date().toISOString().slice(0, 19).replace('T', ' ')).toString();
    tag.assigned_by = this.user?.profile as TeamUser;
    tagsSelectTooltip.close(false);
    this.isTagsChanged = true;
    this.assignDisplayedTags(updateTagsDelay);

    this.tagAddedEvent.emit(tag);
    this.tagsChange.emit([...this.tagsToDisplay, tag]);

    if (this.openTagsSelectTimeoutId) {
      clearTimeout(this.openTagsSelectTimeoutId);
    }

    if (this.openAfterSelect) {
      this.openTagsSelectTimeoutId = setTimeout(() => {
        tagsSelectTooltip.open();
      }, updateTagsDelay + Math.ceil(this.displayedTags.length / 10) * 50);
    }
  }

  onTagsSelectShown() {
    this.isTagsSelectShown = true;
    this.tagsDisplayShown.emit(true);
  }

  onEditTagClicked(tag: TagModel, tooltip: NgbTooltip) {
    if (this.userService.isFreeUser()) {
      return;
    }
    this.editingTag = tag;
    tooltip.open();
  }

  onTagEditHidden() {
    this.editingTag = null;
  }

  private assignDisplayedTags(updateTagsDelay: number) {
    if (!this.tagsToDisplay) {
      return;
    }

    this.updateDisplayedTagsSubject.next(updateTagsDelay);
    this.isTagsChanged = false;
  }

  private generateCategoryHtml(item: { codes: string[], description: string, type: string, titleKey: string }): string {
    return `
      <div class="categories-text">
          <div class="open-sans-bold">
              <div>${item.type}</div>
              <div class="text-nowrap ml-3">${item.codes.join('<br/>')}</div>
          </div>
          <div class="pl-4">${item.description}</div>
      </div>`;
  }

  private getCodeType(code: string): string {
    return this.patent.bibliographic.ipc4?.find(c => code.startsWith(c.toString())) ? 'IPC' : 'CPC';
  }

  private generateDescription(data: { root_title: string, parent_title: string, title: string }): string {
    return `
      <ul>
          <li>
              ${data.root_title}
              <ul>
                  <li>
                      ${data.parent_title}
                      <ul>
                          <li>${data.title}</li>
                      </ul>
                  </li>
              </ul>
          </li>
    </ul>`;
  }

  private generateTitleIdentifier(codeType: string, data: {
    root_title: string,
    parent_title: string,
    title: string
  }): string {
    return (codeType + data.root_title.trim() + data.parent_title.trim() + data.title.trim()).toLowerCase();
  }

  private groupSameCodes(greenCodes: {}[]): { codes: string[], description: string, type: string, titleKey: string }[] {
    const groupedCodes: { codes: string[], description: string, type: string, titleKey: string }[] = [];

    greenCodes.forEach((item) => {
      const actualCode = Object.keys(item)[0];
      const codeType = this.getCodeType(actualCode);
      const titleKey = this.generateTitleIdentifier(codeType, item[actualCode]);
      const foundSameItem = groupedCodes.find(c => c.titleKey === titleKey);

      if (foundSameItem) {
        foundSameItem.codes.push(actualCode);
      } else {
        groupedCodes.push({
          type: codeType,
          codes: [actualCode],
          description: this.generateDescription(item[actualCode]),
          titleKey: titleKey
        });
      }
    });

    return groupedCodes;
  }

  private widthOfMandatoryTags(): number {
    const mandatoryTags = this.container.querySelectorAll('.all-tags-container-hidden .tag-mandatory-item');
    const extraSpace = 0.5 * 16 + 1;
    let width = 0;
    mandatoryTags.forEach(tag => {
      width += (tag as HTMLElement).offsetWidth + extraSpace;
    });
    return width;
  }

  private widthOfActionTags(): number {
    const actionTags = this.container.querySelectorAll('.all-tags-container-hidden .tag-item-action');
    const extraSpace = 0.5 * 16 + 1;
    let width = 0;
    actionTags.forEach(tag => {
      width += (tag as HTMLElement).offsetWidth + extraSpace;
    });
    return width;
  }

  private updateDisplayedTags() {
    if (!this.tagsToDisplay.length) {
      this.displayedTags = [];
      this.overflowTags = [];
      this.pendingOverflowTagIds = [];
      this.isCalculatingTagsToDisplay = false;
      return;
    }

    if (!this.container) {
      return;
    }

    const containerWidth = this.container.offsetWidth;
    const extraSpace = 0.5 * 16 + 1; // space between tags: spacing-sm (rem) * 16 (px)
    const tagElements = Array.from(this.container.querySelectorAll('.all-tags-container-hidden .tag-item.tag-custom'));
    const mandatoryWidth = this.widthOfMandatoryTags();
    let numberTagsToDisplay = 0;
    let currentTagIndex = 0;
    let startTagIndex = 0;

    for (let i = 0; i < this.collapsedDisplayRows - 1; i++) {
      let occupiedWidthPerRow = i === 0 ? mandatoryWidth : 0;

      for (currentTagIndex = startTagIndex; currentTagIndex < tagElements.length; currentTagIndex++) {
        const tagEle = tagElements[currentTagIndex] as HTMLElement;
        occupiedWidthPerRow += (tagEle.offsetWidth + extraSpace);
        if (occupiedWidthPerRow > containerWidth) {
          break;
        }
        numberTagsToDisplay += 1;
      }
      startTagIndex = currentTagIndex;
    }

    let remainingWidthForLastRow = containerWidth - this.widthOfActionTags() - (numberTagsToDisplay > 0 ? 0 : mandatoryWidth);

    for (let i = currentTagIndex; i < tagElements.length; i++) {
      const tagEle = tagElements[i] as HTMLElement;
      remainingWidthForLastRow -= (tagEle.offsetWidth + extraSpace);
      if (remainingWidthForLastRow <= 0) {
        break;
      }
      numberTagsToDisplay += 1;
    }

    const clonedTags = this.tagsToDisplay.map(t => t);
    this.displayedTags = clonedTags.slice(0, numberTagsToDisplay);
    this.overflowTags = clonedTags.slice(numberTagsToDisplay);
    this.pendingOverflowTagIds = [];
    this.isCalculatingTagsToDisplay = false;

    this.updateTagsContainerCssClass();
    
  }

  private updateTagsContainerCssClass() {
    if (this.hasOverflowTags && !this.isOverflowTagsDisplayed) {
      this.tagsContainerCssClass = 'tags-collapsed-display';
      return;
    }

    this.tagsContainerCssClass = 'tags-expanded-display';
  }

  private calculateTagsContainerHeight(numberRows: number): number {
    return this.TAG_ITEM_HEIGHT * numberRows + (numberRows - 1) * this.TAG_ITEMS_GAP + this.TAGS_CONTAINER_PADDING_WIDTH;
  }
}
