<div class="tags-select-container inline-modal-container" #tagsSelectContainer>
  <div class="tags-select-search inline-modal-block p-b-spacing-big">
    <input id="tags-select-search-input" appAutofocus
            [placeholder]="searchInputPlaceholder ? searchInputPlaceholder : 'Search for a tag' + (userService.isCollaboratorUser() ? '' : ' or create one')"
            [formControl]="searchInputFormControl" tabindex="0"
           class="p-spacing-sm radius-sm border-1 border-minimal figma-bg-transition"/>
  </div>

  <div class="popover-divider"></div>

  <div class="tags-select-main-content inline-modal-block flex-grow-1 overflow-y-auto"
       [class.p-y-spacing-big]="!isLoadingTags">
    <ng-container *ngIf="!isLoadingTags">
      <div *ngIf="!hasNoAvailableTags" class="w-100 flex-grow-1 overflow-y-auto scrollbar-2024-sm">
        <div class="tags-select-items d-flex flex-column align-items-start w-100 h-100">
          <div *ngFor="let tagItem of availableTags;" [id]="getDocTagId(tagItem)" class="tag-item-container cursor-pointer d-flex"
               (click)="onTagItemClicked(tagItem)">
            <app-tag-item
              [tag]="tagItem"
              [canManageTag]="false"
              [showTagTooltip]="false"
              [size]="'default'"
              [showTruncatedName]="true"
              truncateWidth="100%"
              [idSuffix]="patent?.general.docdb_family_id ?? '0'"
              (click)="onTagItemClicked(tagItem); $event.stopPropagation()"
              class="m-r-spacing-xx-s text-ellipsis text-ellipsis-1">
            </app-tag-item>
            <div class="button-dots button-main-tertiary-grey button-square button-xsmall"
            (click)="onOpenTagEdit(tagItem, tagsSelectContainer); $event.stopImmediatePropagation();">
              <i class="fa-regular fa-ellipsis-vertical"></i>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="hasNoAvailableTags && getNoTagsMessage()"
           class="tags-select-no-results w-100 d-flex flex-column align-items-center justify-content-start">
        <i *ngIf="searchInputValue.length === 0" class="fa-light fa-tags fa-1.5x fa-border"></i>
        <div class="content-body-small text-center" [innerHTML]="getNoTagsMessage()"></div>
      </div>

      <ng-container *ngIf="isTagCreationShown && canAddTag">
        <div class="tags-select-actions-container d-flex align-items-center justify-content-between radius-sm p-spacing-xx-s figma-bg-secondary w-100">
        <div class="tag-item-container">
            <div class="tag-item tag-custom" [ngStyle]="getBoxTagStyle(temporaryTag.color)"
                 [ngbPopover]="popoverTagTemp" #popover="ngbPopover"
                 triggers="manual" [autoClose]="'outside'" popoverClass="white-popover" container="body"
                 appTruncatableTooltip [truncatableTooltipPopover]="popover" truncatableTextCssSelector=".tag-name"
                 [style]="{'--hover-bg': '#' + temporaryTag.color + 'A6'}">
              <div class="tag-name content-label-small">
                <i class="fa-regular fa-lock" *ngIf="temporaryTag.private"></i> {{ temporaryTag.name }}
              </div>
              <ng-template #popoverTagTemp>
                <div class="popover-descriptions">
                  <i class="fa-regular fa-lock" *ngIf="temporaryTag.private"></i> {{ temporaryTag.name }}
                </div>
              </ng-template>
            </div>
          </div>

          <div class="tags-select-actions d-flex flex-row gap-spacing-sm">
          <button class="button-main-secondary-grey button-small" [disabled]="isCreatingTag"
                    (click)="onCancelClicked()">Cancel
            </button>
            <button class="button-main-primary button-small" [disabled]="isCreatingTag" (click)="onCreateClicked()">
              Create
            </button>
          </div>
        </div>
      </ng-container>
    </ng-container>

    <app-loading-dropdown *ngIf="isLoadingTags"  text="Tags are loading..." class="w-100"
                          paddingCssClass="p-spacing-none">
    </app-loading-dropdown>
  </div>

  <ng-container *ngIf="isTagCreationShown && canAddTag && !isLoadingTags">
    <div class="popover-divider"></div>
    <div class="tag-permission inline-modal-block">
      <div class="content-heading-h6 content-color-secondary p-x-spacing-xx-s">Who has access</div>
      <div class="tag-permission-container w-100">
        <div class="tag-permission-option p-x-spacing-sm p-y-spacing-x-s d-flex align-items-stretch justify-content-between radius-sm"
             (click)="onPermissionClick(false)" [class.disabled]="isCreatingTag || userService.isCollaboratorUser() || userService.isFreeUser()">
          <div class="d-flex flex-column text-start"  tooltipClass="white-tooltip tooltip-text-only" container="body"
            [ngbTooltip]="userService.isCollaboratorUser() ? 'Premium users can create public tags' : ''">
            <div class="content-heading-h6"><i class="fa-regular fa-globe"></i> Public</div>
            <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
          </div>
          <div class="d-flex align-items-center">
            <i class="fa-regular fa-check" *ngIf="!isPrivateTag"></i>
          </div>
        </div>
        <div class="tag-permission-option p-x-spacing-sm p-y-spacing-x-s d-flex align-items-stretch justify-content-between radius-sm"
             (click)="onPermissionClick(true)" [class.disabled]="isCreatingTag">
          <div class="d-flex flex-column text-start">
            <div class="content-heading-h6"><i class="fa-regular fa-lock"></i> Private</div>
            <div class="content-body-xsmall content-color-tertiary">Only the creator can switch it to private</div>
          </div>
          <div class="d-flex align-items-center">
            <i class="fa-regular fa-check" *ngIf="isPrivateTag"></i>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <ng-container *ngIf="footerText?.length">
    <div class="popover-divider"></div>

    <div class="tags-select-footer inline-modal-block flex-row gap-spacing-xx-s">
      <div class="tags-select-footer-icon">
        <i class="far fa-info-circle"></i>
      </div>
      <div class="content-body-xsmall text-left">
        {{footerText}}
      </div>
    </div>
  </ng-container>

  <app-popper #tagEditTemplate placement="right" [showArrow]="false" [resizePopperWidth]="false" class="tag-select-popper" *ngIf="!hasNoAvailableTags" >
    <ng-container *ngIf="isTagEditVisible">
      <app-tag-edit [tag]="tagEdit" (close)="onCloseTagEdit()" (tagDeleted)="onTagDeleted($event)"></app-tag-edit>
    </ng-container>
  </app-popper>
</div>
