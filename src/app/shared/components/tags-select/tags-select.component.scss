@import 'scss/figma2023/variables';

.tags-select-container {
  max-height: 22.5rem;

  @import "scss/components/tag_item";

  .tags-select-search {
    input {
      width: 100%;
      color: $colours-content-content-primary;
      font-family: $font-family-base;
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;

      &::-ms-input-placeholder, &::placeholder {
        color: $colours-content-content-disabled;
        font-family: $font-family-base;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.5rem;
      }

      &:focus, &:active {
        border-color: $colours-border-contrast !important;
        background: $colours-background-bg-primary !important;
      }
    }
  }

  .tags-select-main-content {

    .tags-select-items {
      overflow: auto;

      .tag-item-container {
        padding: $spacing-system-spacing-xx-s;
        max-width: 100%;
        min-height: 32px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .button-dots {
          visibility: hidden;
          min-width: 1.5rem !important;
        }

        &:hover {
          background-color: $colours-background-bg-secondary;
          .button-dots {
            visibility: visible;
          }
        }
      }
    }

    .tags-select-actions-container {
      .tag-item-container {
        .tag-item {
          justify-content: start !important;
          .tag-name  {
            max-width: 9rem;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
    }

    .tags-select-no-results {
      .fa-tags {
        color: $colour-blue-brand-500;
        background: $colour-blue-brand-200;
        --fa-border-radius: 50%;
        --fa-border-width: 0.38rem;
        --fa-border-color: #E0F7FF;
        --fa-border-padding: 1rem;
      }
    }
  }

  .tags-select-footer {
    .tags-select-footer-icon {
      width: 1rem;
      min-width: 1rem;
      height: 1rem;
      min-height: 1rem;

      display: flex;
      align-content: flex-start;
      justify-content: center;
      align-items: center;

      i {
        color: $colour-grey-700;
        text-align: center;
        font-family: "Font Awesome 6 Pro";
        font-size: 0.75rem;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
      }
    }
  }

  .tag-permission{
    display: flex;
    flex-direction: column;
    width: 100%;
    &-option{
      padding: $spacing-system-spacing-x-s $spacing-system-spacing-sm;
      border-radius: $radius-sm;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      &:hover{
        background-color: $colours-background-bg-secondary;
      }

      &.disabled{
        cursor: default;
        color: $colours-content-content-disabled;
      }
    }
  }

}

::ng-deep {
  .tags-select-container {
    app-spinner {
      align-self: center;

      img {
        height: 5rem;
        width: 5rem;
      }
    }

    app-tag-item {
      .tag-item {
        cursor: pointer !important;
      }
    }
  }

  .tag-edit-template {
    .tooltip-arrow {
      display: none;
    }
  }

  .tag-select-popper {
    .popper-instance {
      padding: 0;
    }
  }
}
