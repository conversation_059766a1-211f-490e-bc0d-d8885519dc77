import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { catchError, debounceTime, distinctUntilChanged, filter, finalize, map, switchMap, tap } from 'rxjs/operators';
import { Observable, of, Subscription } from 'rxjs';
import { TagService } from '@core/services';
import { TagModel } from '@core/models/tag.model';
import { BaseStoreService, ColorUtil, MonitorStoreService, Patent, UserService } from '@core';
import { PopperComponent } from '../popper/popper.component';

@Component({
  selector: 'app-tags-select',
  templateUrl: './tags-select.component.html',
  styleUrls: ['./tags-select.component.scss']
})
export class TagsSelectComponent implements OnInit, OnDestroy {
  @Input() storeService: BaseStoreService;

  @Input() canAddTag: boolean = true;
  @Input() footerText: string = 'Tags are applied on a family level, and shared between documents belonging to it.';
  @Input() searchInputPlaceholder: string = null;
  @Input() noTagsMessage: string = null;
  @Input() multipleTaggingDocumentIds: number[] = [];
  @Output() tagsSelectCanceled = new EventEmitter<boolean>();
  @Output() tagsSelectAddedTag = new EventEmitter<TagModel>();
  @Output() tagDeleted = new EventEmitter<TagModel>();

  searchInputFormControl = new FormControl(null);
  availableTags: TagModel[] = [];
  isLoadingTags: boolean = false;
  isCreatingTag: boolean = false;
  temporaryTag: TagModel = null;
  isPrivateTag: boolean = false;
  isTagEditVisible = false;
  tagEdit: TagModel = {
    name: '',
    color: '',
    private: false
  };

  private subscriptions = new Subscription();

  constructor(
    private tagService: TagService,
    public userService: UserService,
    private monitorStoreService: MonitorStoreService
  ) {
  }

  @ViewChild('tagEditTemplate') tagEditTemplate: PopperComponent;

  private _patent: Patent;

  get patent() {
    return this._patent;
  }

  @Input() set patent(value) {
    this._patent = value;
    this.updateAvailableTags();
  }

  private _tags: TagModel[];

  get tags(): TagModel[] {
    return this._tags;
  }

  @Input() set tags(value: TagModel[]) {
    this._tags = value;
    this.updateAvailableTags();
  }

  get searchInputValue(): string {
    return this.searchInputFormControl.value?.trim() || '';
  }

  get patentTags(): TagModel[] {
    return this.patent?.custom_tags || [];
  }

  get tagsToDisplay(): TagModel[] {
    return this.tags?.length > 0 ? this.tags : this.patentTags;
  }

  get hasNoAvailableTags(): boolean {
    return this.availableTags?.length === 0;
  }

  get isTagCreationShown(): boolean {
    const searchInputValue = this.searchInputValue.toLowerCase();
    return searchInputValue.length > 0 && this.temporaryTag && !this.isTagNameAssigned(searchInputValue, true) &&
      !this.availableTags.some(tag => tag.name.toLowerCase() === searchInputValue);
  }

  ngOnInit() {
    const valueChanges$ = this.searchInputFormControl.valueChanges
      .pipe(
        map(value => value?.trim() || ''),
        filter(value => value.length > 1 || value.length === 0 || this.hasNoAvailableTags),
        debounceTime(500),
        distinctUntilChanged(),
        tap((value) => {
          this.temporaryTag = this.generateTemporaryTag(value);
        }),
        tap(() => this.isLoadingTags = true),
        switchMap(value => this.loadTags(value)),
        tap(() => {
          if (this.isTagCreationShown) {
            this.isPrivateTag = true;
          }
          this.isLoadingTags = false;
        })
      )
      .subscribe();
    this.subscriptions.add(valueChanges$);

    this.isLoadingTags = true;
    const loadTags$ = this.loadTags(null)
      .pipe(
        tap(() => this.isLoadingTags = false)
      )
      .subscribe();
    this.subscriptions.add(loadTags$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getDocTagId(tag: TagModel) {
    return `doc-tag-${tag.id}-${this.patent?.general.docdb_family_id ?? 0}`;
  }

  getBoxTagStyle(color: string) {
    return {'color': this.getTextColor(color), 'background-color': '#' + color};
  }

  onCancelClicked() {
    this.tagsSelectCanceled.emit(true);
  }

  onCreateClicked() {
    this.isCreatingTag = true;
    const create$ = this.tagService.create(this.temporaryTag)
      .pipe(
        switchMap((tag) => this.assignTag(tag)),
        tap((tag) => {
          this.temporaryTag = null;
          this.tagsSelectAddedTag.emit(tag);
          this.searchInputFormControl.setValue('');
        }),
        catchError((error) => {
          console.log(error);
          return of(null);
        }),
        finalize(() => this.isCreatingTag = false)
      )
      .subscribe();
    this.subscriptions.add(create$);
  }

  onTagItemClicked(tag: TagModel) {
    const assignTag$ = this.assignTag(tag)
      .pipe(
        tap((tag) => this.tagsSelectAddedTag.emit(tag))
      )
      .subscribe();
    this.subscriptions.add(assignTag$);
  }

  getNoTagsMessage() {
    if (this.noTagsMessage) {
      return this.noTagsMessage;
    }

    if (this.searchInputValue.length === 0) {
      if (this.tagsToDisplay.length === 0) {
        return 'Be the first one to create a tag by simply typing a name';
      } else {
        return 'All tags are added to this document, create new one';
      }
    }

    if (this.isTagNameAssigned(this.searchInputValue, true)) {
      return `The tag <b>${this.searchInputValue}</b> is already assigned`;
    }

    if (this.isTagNameAssigned(this.searchInputValue, false)) {
      return `The tags contain <b>${this.searchInputValue}</b> are already assigned`;
    }

    return '';
  }

  isTagNameAssigned(tagName: string, isMatchAllName: boolean): boolean {
    tagName = tagName?.toLowerCase();
    if (isMatchAllName) {
      return this.tagsToDisplay.some(tag => tag.name.toLowerCase() === tagName);
    } else {
      return this.tagsToDisplay.some(tag => tag.name.toLowerCase().includes(tagName));
    }
  }

  private generateTemporaryTag(name: string): TagModel {
    if (!name) {
      return null;
    }

    return {
      name: name,
      color: ColorUtil.getRandomTagColor().code.replace('#', ''),
      private: true,
    } as TagModel;
  }

  private assignTag(tag: TagModel): Observable<any> {
    const documentId = this.patent?.general?.docdb_family_id;

    if (documentId || this.multipleTaggingDocumentIds?.length) {
      const documentIds = this.multipleTaggingDocumentIds?.length ? this.multipleTaggingDocumentIds : [Number(documentId)];
      const payload = this.buildPayload(documentIds);
      return this.tagService.assign(tag, payload)
        .pipe(
          tap(() => {
            this.updateAvailableTags();
          }),
          map(() => tag)
        );
    }

    return of(tag.id ? tag : null);
  }

  private loadTags(name: string): Observable<any> {
    const payload = {
      sort_by: 'created_at',
      sort_order: 'desc',
      page_size: 100
    };
    if (name) {
      payload['name'] = `like:%${name}%`;
    }
    return this.tagService.getTags(payload)
      .pipe(
        tap(() => this.updateAvailableTags())
      );
  }

  private updateAvailableTags() {
    const assignedTagIds = this.tagsToDisplay.map(tag => tag.id);
    this.availableTags = this.tagService.tags.filter(tag => !assignedTagIds.includes(tag.id));
  }

  private getTextColor(color: string) {
    return this.tagService.getTextColor(color);
  }

  private buildPayload(documentIds: number[]): any {
    const payload = {
      document_ids: documentIds
    };

    if (this.storeService.isMonitorSearch()) {
      payload['monitor_run_id'] = this.monitorStoreService.selectedMonitorRun;
    } else {
      payload['search_hash'] = this.storeService.searchHash;
    }

    return payload;
  }

  onPermissionClick(makePrivate: boolean) {
    if (this.userService.isCollaboratorUser()) {
      return;
    }

    this.isPrivateTag = makePrivate;
    
    if (this.temporaryTag) {
      this.temporaryTag.private = makePrivate;
    }
  }

  onOpenTagEdit(tag: TagModel, targetElement: HTMLElement) {
    if (this.tagEditTemplate.isOpen) {
      this.tagEditTemplate.hide();
      this.isTagEditVisible = false;
      setTimeout(() => {
        this.onOpenTagEdit(tag, targetElement);
      });
      return;
    } 
    
    this.tagEdit = tag;
    this.isTagEditVisible = true;
    setTimeout(() => {  
      this.tagEditTemplate.show(targetElement);
    });
  }

  onCloseTagEdit() {
    this.isTagEditVisible = false;
  }

  onTagDeleted(tag: TagModel) {
    this.tagEditTemplate.hide();
    this.tagDeleted.emit(tag);
    this.tagService.deleteTagsWithConfirmation([tag])
      .pipe(
        tap(() => this.updateAvailableTags())
      )
      .subscribe();
  }
}
