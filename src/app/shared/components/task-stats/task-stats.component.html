<div *ngIf="taskStats && taskStats.all_tasks?.length else addTaskButtonTemplate;"
     class="d-flex flex-column gap-spacing-sm">
  <div class="d-flex flex-wrap gap-spacing-x-s">
    <ng-container *ngFor="let field of displayedStatsFields;"
                  [ngTemplateOutlet]="statsItemTemplate"
                  [ngTemplateOutletContext]="{field: field, tasks: taskStats[field.name]}">
    </ng-container>

    <ng-container *ngIf="isOverflowStatsItemsDisplayed">
      <ng-container *ngFor="let field of overflowStatsFields;"
                    [ngTemplateOutlet]="statsItemTemplate"
                    [ngTemplateOutletContext]="{field: field, tasks: taskStats[field.name]}">
      </ng-container>
    </ng-container>

    <ng-container *ngIf="hasOverflowStatsItems">
      <div *ngIf="!isOverflowStatsItemsDisplayed" (click)="onShowMoreStatsItemsClicked($event)"
           class="badge-grey d-flex justify-content-center gap-spacing-xxx-s cursor-pointer"
           ngbTooltip="Show more" tooltipClass="white-tooltip">
        <div class="content-label-small content-color-secondary">+{{ overflowStatsFields.length }}</div>
      </div>

      <div *ngIf="isOverflowStatsItemsDisplayed" (click)="onShowLessStatsItemsClicked($event)"
           class="badge-grey d-flex justify-content-center gap-spacing-xxx-s cursor-pointer">
        <div class="content-label-small content-color-secondary">Show less</div>
      </div>
    </ng-container>
  </div>

  <app-user-avatars [users]="users" [numberDisplayedUsers]="numberDisplayedUsers"
                    [groups]="groups" [numberDisplayedGroups]="numberDisplayedGroups"
                    [distanceBetweenAvatars]="16" size="xsmall"
                    [userExtraInfoTemplate]="userExtraInfoTemplate"
                    [popoverMoreUsersTemplate]="popoverMoreUsersTemplate"
                    [showUserTitleOnTooltip]="false"
                    [showUserEmailOnTooltip]="false"
                    [showUserTitleOnMoreUsers]="true"
                    [showUserEmailOnMoreUsers]="false"
                    [autoCloseAvatarTooltip]="true"
                    (avatarTooltipShown)="onAvatarTooltipShown($event)"
                    (avatarTooltipHidden)="onAvatarTooltipHidden($event)"
                    avatarTooltipCss="white-popover popover-user-avatar tasks-stats-user-popper">
    <ng-template #userExtraInfoTemplate let-user="user">
      <div *ngIf="assigneeOnlyHasRequests(getTasksByAssignee(user))"
           class="d-flex flex-column p-spacing-x-s gap-spacing-sm tasks-stats-user-has-one-request">
        <div class="content-label-xxs content-color-primary">{{ user | userTitle }}</div>

        <div *ngFor="let item of groupTasksByStatus(getTasksByAssignee(user).all_tasks);">
          <div class="d-flex gap-spacing-sm align-items-center justify-content-between">
            <div class="content-body-xsmall content-color-secondary">
              {{ item.tasks.length }} {{ 'Request' | pluralize: item.tasks.length }}
            </div>
            <div class="patent-task-status content-label-xsmall badge-grey badge-small"
                 [ngbTooltip]="getTaskAssignmentStatusTooltip(item.status)"
                 tooltipClass="white-tooltip">
              {{ getTaskAssignmentStatusLabel(item.status) }}
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="!assigneeOnlyHasRequests(getTasksByAssignee(user))" class="tasks-stats-user-full-width">
        <div *ngIf="!assigneeHasOneRating(getTasksByAssignee(user)) && getTasksByAssignee(user).ratings.length > 0"
             class="p-spacing-md content-heading-h6 content-color-tertiary border-0 border-b-0-5 border-subtle">
          {{ getTasksByAssignee(user).ratings.length }} {{ 'Rating' | pluralize: getTasksByAssignee(user).ratings.length }}
        </div>

        <div appScrollbarDetector scrollbarCss="has-scrollbar"
             class="task-stats-cards radius-big">
          <div *ngIf="countRequestsOfAssigneeStats(getTasksByAssignee(user)) > 0"
               class="p-spacing-md">
            <div appMouseHover hoverClass="figma-bg-tertiary"
                 class="radius-sm p-y-spacing-xx-s p-x-spacing-sm d-flex justify-content-between figma-bg-secondary cursor-pointer task-stats-requests-title"
                 [ngbTooltip]="isRequestsExpanded ? 'Collapse' : 'Expand'" tooltipClass="white-tooltip"
                 (click)="toggleRequests($event)">
              <div class="content-heading-h6 content-style-semi-bold d-flex align-items-center">
                {{ countRequestsOfAssigneeStats(getTasksByAssignee(user)) }} {{ 'Request' | pluralize: countRequestsOfAssigneeStats(getTasksByAssignee(user)) }}
              </div>
              <span class="button-square button-small d-flex justify-content-center align-items-center">
                <i class="fa-regular fa-chevron-up cursor-pointer p-spacing-s fa-fw fa-1x"
                   [ngClass]="{'fa-chevron-up': isRequestsExpanded, 'fa-chevron-down': !isRequestsExpanded}">
                </i>
              </span>
            </div>

            <div *ngIf="isRequestsExpanded"
                 class="w-100 d-flex flex-column align-items-stretch gap-spacing-sm p-spacing-sm task-stats-requests">
              <app-patent-request-card *ngFor="let request of getTasksByAssignee(user).answerableRequests;"
                                       [task]="request" class="d-block"
                                       [isAnswerable]="true" [showActionButton]="false" [openNewTab]="true">
              </app-patent-request-card>

              <app-patent-request-card *ngFor="let request of getTasksByAssignee(user).createdRequests;"
                                       [task]="request" class="d-block"
                                       [isAnswerable]="false" [showActionButton]="false" [openNewTab]="true">
              </app-patent-request-card>

              <app-patent-request-card *ngFor="let request of getTasksByAssignee(user).otherRequests;"
                                       [task]="request" class="d-block"
                                       [isAnswerable]="false" [showActionButton]="false" [openNewTab]="true">
              </app-patent-request-card>
            </div>
          </div>

          <div class="d-flex flex-column">
            <ng-container *ngFor="let rating of getTasksByAssignee(user).ratings; let first = first;">
              <div *ngIf="!first" class="w-100 horizontal-divider m-spacing-none"></div>
              <app-patent-request-result-card [task]="rating"
                                              [showActionButton]="false" [openNewTab]="true"
                                              [showDeletableInfo]="false"
                                              [showStarsTooltip]="false"
                                              [showTopicsTooltip]="false"
                                              class="d-block p-spacing-md figma-bg-primary"
                                              appMouseHover hoverClass="figma-bg-transition">
              </app-patent-request-result-card>
            </ng-container>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-template #popoverMoreUsersTemplate let-users="users">
      <div class="more-users-container" appScrollbarDetector scrollbarCss="has-scrollbar">
        <div *ngFor="let user of users;"
             class="p-x-spacing-md p-y-spacing-sm d-flex align-items-center justify-content-start gap-spacing-x-s radius-md"
             appMouseHover hoverClass="figma-bg-secondary"
             appSelectableTooltip [selectableTooltipPopover]="moreUserPopover" [parentPopover]="displayedUserPopper"
             [selectableTooltipCloseTimeout]="500" [autoCloseTooltip]="false"
             #moreUserPopover="ngbPopover" [ngbPopover]="userExtraInfoTemplate"
             [popoverContext]="{ user: user, showTooltip: false }"
             triggers="manual" [autoClose]="'outside'" container="body" placement="right left"
             popoverClass="white-popover popover-user-avatar tasks-stats-user-popper"
             (shown)="onMoreUserPopoverShown(user)" (hidden)="onMoreUserPopoverHidden()">
          <app-user-avatar [user]="user" [hasSubTitle]="false" [showTooltip]="false" size="xsmall">
          </app-user-avatar>
          <div class="user-info">
            <div class="content-heading-h6 content-color-primary">{{ user | userTitle }}</div>
          </div>
        </div>
      </div>
    </ng-template>
  </app-user-avatars>

  <div class="all-task-stats-hidden-items d-flex gap-spacing-xx-s position-absolute invisible">
    <ng-container *ngFor="let field of TASK_STATS_FIELDS;">
      <ng-container *ngIf="taskStats[field.name].length > 0" [ngTemplateOutlet]="statsItemTemplate"
                    [ngTemplateOutletContext]="{field: field, tasks: taskStats[field.name]}">
      </ng-container>
    </ng-container>

    <div class="badge-grey d-flex justify-content-center gap-spacing-xxx-s task-stats-show-more">
      <div class="content-label-small content-color-secondary">+{{ TASK_STATS_FIELDS.length }}</div>
    </div>
  </div>
</div>

<ng-template #statsItemTemplate let-field="field" let-tasks="tasks">
  <div class="d-flex align-items-center gap-spacing-xxx-s task-stats-item" [attr.data-field-name]="field.name"
       appSelectableTooltip [selectableTooltipPopover]="statsItemPopover"
       [selectableTooltipCloseTimeout]="500" [autoCloseTooltip]="true"
       [selectableTooltipOpenTimeout]="100"
       #statsItemPopover="ngbPopover" [ngbPopover]="statsItemPopoverTemp"
       triggers="manual" [autoClose]="'outside'" container="body" placement="top bottom left"
       popoverClass="white-popover {{ PENDING_FIELDS.includes(field.name) ? 'stats-requests-popover' : 'stats-ratings-popover'}}"
       (shown)="onStatsItemPopoverShown()">
    <div *ngIf="!taskStatsHasOneRating()"
         class="content-heading-h7 content-color-tertiary">
      {{ tasks.length }}
    </div>
    <div
      class="badge-grey d-flex justify-content-center align-items-center gap-spacing-xx-s rating-star border-moderate figma-bg-secondary"
      appMouseHover hoverClass="border-bold figma-bg-tertiary cursor-pointer">
      <span *ngIf="field.icon" class="fa-stack fa-2x fa-stack-star fa-stack-sm" [ngClass]="field.icon">
        <i class="fa-regular fa-circle fa-stack-2x"></i>
        <i class="fa-solid fa-star-circle fa-stack-1x"></i>
      </span>

      <span class="content-color-secondary" [ngClass]="field.title_class"
            appMouseHover hoverClass="content-color-primary">
        {{ field.title }}
      </span>
    </div>
    <ng-template #statsItemPopoverTemp>
      <div class="d-flex flex-column">
        <ng-container *ngIf="PENDING_FIELDS.includes(field.name)">
          <div
            class="p-y-spacing-xx-s p-x-spacing-sm content-heading-h7 content-color-primary border-0 border-b-0-5 border-subtle">
            {{ tasks.length }} {{ 'Assignee' | pluralize: tasks.length }}
          </div>

          <div class="d-flex flex-column gap-spacing-xx-s p-spacing-sm">
            <div *ngFor="let task of tasks;"
                 class="d-flex align-items-center gap-spacing-xx-s">
              <app-user-avatar [user]="task.team_users[0]" size="xxsmall"
                               subTitleCssClass="content-body-xsmall content-color-primary"
                               [hasSubTitle]="true" [showTooltip]="false"
                               class="flex-grow-1 text-ellipsis text-ellipsis-1 d-block">
              </app-user-avatar>
              <div *ngIf="task.deadline">
                <i class="fa-light fa-calendar-clock fa-xs"
                   [ngbTooltip]="deadlinePopoverTemp" triggers="hover" container="body"
                   tooltipClass="white-tooltip stats-task-deadline-tooltip">
                </i>

                <ng-template #deadlinePopoverTemp>
                  <div class="d-flex gap-spacing-xx-s aligns-item-center justify-content-between">
                    <div class="content-heading-h7 content-color-tertiary">
                      Due date
                    </div>
                    <div class="d-flex gap-spacing-xxx-s aligns-item-center">
                      <div><i class="fa-regular fa-calendar-clock fa-xs"></i></div>
                      <div class="content-heading-h7 content-color-secondary">
                        {{ task.deadline | dateFormat: 'ShortDate' }}
                      </div>
                    </div>
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </ng-container>

        <ng-container *ngIf="!PENDING_FIELDS.includes(field.name)">
          <div *ngIf="taskStats.all_ratings.length > 1"
               class="p-spacing-md content-heading-h5 content-color-primary border-0 border-b-0-5 border-subtle">
            {{ tasks.length }} {{ 'Rating' | pluralize: tasks.length }}
          </div>

          <div appScrollbarDetector scrollbarCss="has-scrollbar"
               class="task-stats-cards radius-big">
            <div class="d-flex flex-column">
              <ng-container *ngFor="let rating of tasks; let first = first;">
                <div *ngIf="!first" class="w-100 horizontal-divider m-spacing-none"></div>
                <app-patent-request-result-card [task]="rating"
                                                [showActionButton]="false" [openNewTab]="true"
                                                [showDeletableInfo]="false"
                                                [showStarsTooltip]="false"
                                                [showTopicsTooltip]="false"
                                                class="d-block p-spacing-md figma-bg-primary"
                                                appMouseHover hoverClass="figma-bg-transition">
                </app-patent-request-result-card>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </ng-template>
  </div>
</ng-template>

<ng-template #addTaskButtonTemplate>
  <div class="d-flex align-items-center justify-content-center h-100 w-100">
    <div ngbTooltip="Rate patent or request a rating." tooltipClass="white-tooltip"
         #addButtonTooltip="ngbTooltip" (click)="addButtonTooltip.close(); onAddTaskButtonClick($event);"
         class="add-task-button">
      <button #popoverAddTask="ngbPopover" [ngbPopover]="popoverAddTaskTemplate" [autoClose]="true"
              popoverClass="context-menu-popper" container="body" placement="bottom"
              class="button-main-secondary-grey button-pill" style="width: 2rem"
              [ngClass]="{'disabled': isFindingSelfRating}">
        <i class="fa-regular" [ngClass]="popoverAddTask.isOpen() ? 'fa-xmark' : 'fa-plus'"></i>
      </button>
    </div>
  </div>

  <ng-template #popoverAddTaskTemplate>
    <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium"
         (click)="popoverAddTask.close(); onRateMyselfClicked($event)">
      <span class="p-r-spacing-xxx-big">Rate it myself</span>
    </div>
    <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium"
         (click)="popoverAddTask.close(); onRequestRatingClicked($event)">
      <span class="p-r-spacing-xxx-big">Request a rating</span>
    </div>
  </ng-template>
</ng-template>
