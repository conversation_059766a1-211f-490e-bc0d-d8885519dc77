@import 'scss/components/rating';
@import 'scss/figma2023/index';
@import 'scss/components/task_status';

::ng-deep {
  .stats-requests-popover {
    .popover-body {
      padding: 0 !important;
      min-width: 10.5rem !important;
      max-width: 10.5rem !important;
    }
  }

  .stats-ratings-popover, .tasks-stats-user-popper:has(.tasks-stats-user-full-width) {
    .popover-body {
      padding: 0 !important;
      min-width: 20.5rem !important;
      max-width: 20.5rem !important;

      .task-stats-cards {
        max-height: 15.5rem;
        overflow-y: auto;

        &.has-scrollbar {
          margin-right: $spacing-system-spacing-xx-s !important;
        }
      }
    }
  }

  .tasks-stats-user-popper {
    &:has(.tasks-stats-user-full-width) {
      .popover-body {
        .popover-descriptions {
          min-width: 20.5rem !important;
          max-width: 20.5rem !important;
        }
      }
    }

    &:has(.tasks-stats-user-has-one-request) {
      .popover-body {
        padding: 0 !important;
        min-width: 10.5rem !important;
        max-width: 10.5rem !important;

        .popover-descriptions {
          min-width: 10.5rem !important;
          max-width: 10.5rem !important;
        }
      }
    }
  }

  .stats-task-deadline-tooltip {
    .tooltip-inner {
      padding: $spacing-system-spacing-x-s !important;
      min-width: 9.5rem !important;
    }
  }

  .ngb-dp-body.dropdown-menu.show {
    z-index: 9999;
  }
}
