import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskStatsComponent } from './task-stats.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { PluralizePipe, UsersTitleTextPipe, UserTitlePipe } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('TaskStatsComponent', () => {
  let component: TaskStatsComponent;
  let fixture: ComponentFixture<TaskStatsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TaskStatsComponent],
      imports: [
        SharedModule,
        NgbModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        UsersTitleTextPipe,
        UserTitlePipe,
        PluralizePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true})
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TaskStatsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
