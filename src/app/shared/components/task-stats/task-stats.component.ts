import { Component, ElementRef, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import {
  AssigneeTypeEnum,
  BaseStoreService,
  Patent,
  TaskAssignmentStatusEnum,
  TaskModel,
  TaskResourceTypeEnum,
  TasksByAssigneeStats,
  TaskService,
  TaskStats,
  TaskStatusEnum,
  TeamUser,
  TeamUserTypeEnum,
  UserGroup
} from '@core';
import { debounce, filter } from 'rxjs/operators';
import { BehaviorSubject, finalize, Subscription, tap, timer } from 'rxjs';
import { CreateTaskEventParams, RateMyselfEventParams } from './types';
import { PopperComponent } from '@shared/components/popper/popper.component';
import { getTaskAssignmentStatusLabel, getTaskAssignmentStatusTooltip } from '@patent/patent-ratings/shared/types';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-task-stats',
  templateUrl: './task-stats.component.html',
  styleUrl: './task-stats.component.scss'
})
export class TaskStatsComponent implements OnInit, OnDestroy {
  @Input() collapsedDisplayRows: number = 1;
  @Input() patent: Patent = null;
  @Input() storeService: BaseStoreService = null;
  @Input() ratingFormPopper: PopperComponent = null;
  @Input() ratingAnswerPopper: PopperComponent = null;

  @Output() rateMyselfEvent: EventEmitter<RateMyselfEventParams> = new EventEmitter<RateMyselfEventParams>();
  @Output() createTaskEvent: EventEmitter<CreateTaskEventParams> = new EventEmitter<CreateTaskEventParams>();

  readonly PENDING_FIELDS = ['todo', 'in_progress'];
  readonly TASK_STATS_FIELDS = [
    {name: 'todo', title: 'To do', icon: null, title_class: 'content-label-small'},
    {name: 'in_progress', title: 'In progress', icon: null, title_class: 'content-label-small'},
    {name: 'five_point', title: '5 pt', icon: 'rated-star-5', title_class: 'content-heading-h7'},
    {name: 'four_point', title: '4 pt', icon: 'rated-star-4', title_class: 'content-heading-h7'},
    {name: 'three_point', title: '3 pt', icon: 'rated-star-3', title_class: 'content-heading-h7'},
    {name: 'two_point', title: '2 pt', icon: 'rated-star-2', title_class: 'content-heading-h7'},
    {name: 'one_point', title: '1 pt', icon: 'rated-star-1', title_class: 'content-heading-h7'},
  ];
  readonly MAX_DISPLAY_ASSIGNEES = 4;

  users: TeamUser[] = [];
  groups: UserGroup[] = [];
  numberDisplayedUsers = this.MAX_DISPLAY_ASSIGNEES;
  numberDisplayedGroups = 0;
  displayedStatsFields: { name: string, title: string, icon: string }[] = [];
  overflowStatsFields: { name: string, title: string, icon: string }[] = [];

  isOverflowStatsItemsDisplayed: boolean = false;
  isRequestsExpanded: boolean = false;
  displayedUserPopper: NgbPopover = null;
  isFindingSelfRating: boolean = false;

  readonly getTaskAssignmentStatusTooltip = getTaskAssignmentStatusTooltip;
  readonly getTaskAssignmentStatusLabel = getTaskAssignmentStatusLabel;
  private subscriptions = new Subscription();
  private updateDisplayedStatsFieldsSubject = new BehaviorSubject<number>(null);
  private readonly ALL_TASK_STATS_HIDDEN_ITEMS_CSS = '.all-task-stats-hidden-items';

  constructor(
    private eleRef: ElementRef,
    private taskService: TaskService
  ) {
  }

  private _taskStats: TaskStats;

  get taskStats(): TaskStats {
    return this._taskStats;
  }

  @Input()
  set taskStats(val: TaskStats) {
    this._taskStats = val;
    this.updateUsersAndGroups();
    this.updateDisplayedStatsFieldsSubject.next(100);
  }

  get hasOverflowStatsItems(): boolean {
    return this.overflowStatsFields?.length > 0;
  }

  get documentId(): number {
    return Number(this.patent.general.docdb_family_id);
  }

  ngOnInit() {
    const updateDisplayedStatsFields$ = this.updateDisplayedStatsFieldsSubject.asObservable()
      .pipe(
        filter(val => val !== null),
        debounce((val: number) => timer(val)),
      )
      .subscribe({
        next: (val) => {
          this.updateDisplayedStatsFields();
        }
      });
    this.subscriptions.add(updateDisplayedStatsFields$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onShowMoreStatsItemsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isOverflowStatsItemsDisplayed = true;
  }

  onShowLessStatsItemsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isOverflowStatsItemsDisplayed = false;
  }

  onStatsItemPopoverShown() {
    this.updateRatingCardsMaxHeight();
  }

  onRateMyselfClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isFindingSelfRating) {
      return;
    }

    this.isFindingSelfRating = true;
    const findPendingSelfRatingOrNew$ = this.taskService.findPendingSelfRatingOrNew(this.documentId)
      .pipe(
        tap((task) => {
          this.rateMyselfEvent.emit({
            task,
            patent: this.patent,
            targetElement: this.getAddTaskButton()
          });
        }),
        finalize(() => this.isFindingSelfRating = false)
      )
      .subscribe();
    this.subscriptions.add(findPendingSelfRatingOrNew$);
  }

  onRequestRatingClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.createTaskEvent.emit({
      task: {} as TaskModel,
      patentFamilyId: this.documentId,
      patent: this.patent,
      targetElement: this.getAddTaskButton()
    });
  }

  onAddTaskButtonClick(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.ratingFormPopper?.hide();
    this.ratingAnswerPopper?.hide();
  }

  onAvatarTooltipShown({user, popover}) {
    if (user.more_users) {
      this.displayedUserPopper = popover;
    }
    this.updateRatingCardsMaxHeight();
  }

  onAvatarTooltipHidden(user: TeamUser) {
    this.isRequestsExpanded = false;
    this.displayedUserPopper = null;
  }

  onMoreUserPopoverShown(user: TeamUser) {
    this.updateRatingCardsMaxHeight();
  }

  onMoreUserPopoverHidden() {
    this.isRequestsExpanded = false;
    setTimeout(() => {
      if (this.displayedUserPopper) {
        this.displayedUserPopper.autoClose = 'outside';
      }
    }, 20);
  }

  getTasksByAssignee(user: TeamUser) {
    const result = this.taskStats?.tasks_by_assignee ? this.taskStats?.tasks_by_assignee[this.getAssigneeKey(user)] : null;
    if (result) {
      return result;
    }
    return {
      assignee: null,
      team_user: null,
      all_tasks: [],
      ratings: [],
      answerableRequests: [],
      createdRequests: [],
      otherRequests: [],
    } as TasksByAssigneeStats;
  }

  countRequestsOfAssigneeStats(val: TasksByAssigneeStats): number {
    return val.answerableRequests.length + val.createdRequests.length + val.otherRequests.length;
  }

  assigneeHasOneRating(val: TasksByAssigneeStats): boolean {
    return val.ratings.length === 1 && val.all_tasks.length === 1;
  }

  assigneeOnlyHasRequests(val: TasksByAssigneeStats): boolean {
    return val.ratings.length === 0;
  }

  taskStatsHasOneRating(): boolean {
    return this.taskStats?.all_ratings?.length === 1 && this.taskStats?.all_tasks?.length === 1
  }

  toggleRequests(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();
    this.isRequestsExpanded = !this.isRequestsExpanded;
    this.updateRatingCardsMaxHeight();
  }

  groupTasksByStatus(tasks: TaskModel[]): { status: TaskAssignmentStatusEnum, tasks: TaskModel[] }[] {
    const results = [];
    tasks.forEach(task => {
      const ta = task.assignments[0];
      const existing = results.find(r => r.status === ta.status);
      if (existing) {
        existing.tasks.push(task);
      } else {
        results.push({
          status: ta.status,
          tasks: [task]
        });
      }
    });
    return results;
  }

  private getAssigneeKey(user: TeamUser) {
    const type = user.type === TeamUserTypeEnum.USER ? 'USER' : 'GROUP';
    return `${user.id}_${type}`;
  }

  private getAddTaskButton(): HTMLElement {
    return this.eleRef.nativeElement.querySelector('.add-task-button') as HTMLElement;
  }

  private updateUsersAndGroups() {
    const tasksByAssignee = Object.values(this.taskStats?.tasks_by_assignee || {});
    this.users = tasksByAssignee
      .filter(r => r.assignee.type === AssigneeTypeEnum.USER)
      .map(r => r.team_user);
    this.groups = tasksByAssignee
      .filter(r => r.assignee.type === AssigneeTypeEnum.GROUP)
      .map(r => {
        return {
          id: r.team_user.id,
          name: r.team_user.first_name
        } as UserGroup;
      });
    this.numberDisplayedUsers = Math.max(this.MAX_DISPLAY_ASSIGNEES, this.users.length);
    this.numberDisplayedGroups = Math.max(0, this.MAX_DISPLAY_ASSIGNEES - this.numberDisplayedUsers);
  }

  private updateDisplayedStatsFields() {
    this.displayedStatsFields = [];
    const statsElements = Array.from(this.eleRef.nativeElement.querySelectorAll(`${this.ALL_TASK_STATS_HIDDEN_ITEMS_CSS} .task-stats-item`));

    if (statsElements.length === 0) {
      return;
    }

    const containerWidth = this.eleRef.nativeElement.offsetWidth;
    const extraSpace = 0.255 * 16 + 1; // space between items: spacing-xx-s (rem) * 16 (px)
    const mandatoryWidth = this.widthOfMandatoryTags();
    let currentItemIndex = 0;
    let startItemIndex = 0;

    for (let i = 0; i < this.collapsedDisplayRows - 1; i++) {
      let occupiedWidthPerRow = i === 0 ? mandatoryWidth : 0;

      for (currentItemIndex = startItemIndex; currentItemIndex < statsElements.length; currentItemIndex++) {
        const itemEle = statsElements[currentItemIndex] as HTMLElement;
        occupiedWidthPerRow += (itemEle.offsetWidth + extraSpace);
        if (occupiedWidthPerRow > containerWidth) {
          break;
        }
        const field = this.TASK_STATS_FIELDS.find(f => f.name === itemEle.dataset.fieldName);
        if (field) {
          this.displayedStatsFields.push(field);
        }
      }
      startItemIndex = currentItemIndex;
    }

    let remainingWidthForLastRow = containerWidth - this.widthOfActionTags() - (this.displayedStatsFields.length > 0 ? 0 : mandatoryWidth);

    for (let i = currentItemIndex; i < statsElements.length; i++) {
      const itemEle = statsElements[i] as HTMLElement;
      remainingWidthForLastRow -= (itemEle.offsetWidth + extraSpace);
      if (remainingWidthForLastRow <= 0) {
        break;
      }
      const field = this.TASK_STATS_FIELDS.find(f => f.name === itemEle.dataset.fieldName);
      if (field) {
        this.displayedStatsFields.push(field);
      }
    }

    const displayedFields = this.displayedStatsFields.map(f => f.name);

    statsElements.forEach((itemEle: HTMLElement) => {
      const field = this.TASK_STATS_FIELDS.find(f => f.name === itemEle.dataset.fieldName);
      if (field && !displayedFields.includes(field.name)) {
        this.overflowStatsFields.push(field);
      }
    });
  }

  private widthOfMandatoryTags() {
    return 10;
  }

  private widthOfActionTags() {
    return this.eleRef.nativeElement.querySelector(`${this.ALL_TASK_STATS_HIDDEN_ITEMS_CSS} .task-stats-show-more`).offsetWidth;
  }

  private updateRatingCardsMaxHeight() {
    setTimeout(() => {
      const scrollableContainer = document.querySelector<HTMLDivElement>('.task-stats-cards');
      if (!scrollableContainer) {
        return;
      }

      const requestsTitleEle = document.querySelector<HTMLDivElement>('.task-stats-requests-title');
      let requestsTitleHeight = 0;
      let paddingSpace = 0;
      if (requestsTitleEle) {
        paddingSpace = 0.75 * 2 * 16;
        requestsTitleHeight = requestsTitleEle.offsetHeight;
      }

      if (!this.isRequestsExpanded) {
        const ratingCardElements = scrollableContainer.querySelectorAll<HTMLDivElement>('app-patent-request-result-card');
        const ratingCardElementsHeight = Array.from(ratingCardElements).slice(0, 2).reduce((acc, el) => acc + el.offsetHeight, 0);
        if (ratingCardElements?.length >= 2) {
          const maxHeight = ratingCardElementsHeight + requestsTitleHeight + paddingSpace + 1;
          scrollableContainer.style.maxHeight = `${maxHeight}px`;
          return;
        }
      }

      const requestCardElements = scrollableContainer.querySelectorAll<HTMLDivElement>('app-patent-request-card');
      if (requestCardElements?.length >= 3) {
        paddingSpace = 0.75 * 16 * (requestCardElements?.length === 3 ? 3 : 1) + 0.5 * 16 * 3;
        const requestCardElementsHeight = Array.from(requestCardElements).slice(0, 3).reduce((acc, el) => acc + el.offsetHeight, 0);
        const maxHeight = requestCardElementsHeight + requestsTitleHeight + paddingSpace + 1;
        scrollableContainer.style.maxHeight = `${maxHeight}px`;
        return;
      }

      scrollableContainer.style.maxHeight = 'unset';
    }, 50);
  }
}
