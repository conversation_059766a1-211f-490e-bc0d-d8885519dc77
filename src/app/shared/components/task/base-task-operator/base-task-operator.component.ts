import { Component, Injector, Input } from '@angular/core';
import { DateFormatPipe, Label, Patent, TaskAssignmentModel, TaskModel, TaskService } from '@core';

@Component({
  selector: 'app-base-task-operator',
  templateUrl: './base-task-operator.component.html',
  styleUrls: ['./base-task-operator.component.scss']
})
export class BaseTaskOperatorComponent {
  @Input() task: TaskModel = {} as TaskModel;

  maxDisplayedLabels: number = 3;

  taskService: TaskService;
  private dateFormatPipe: DateFormatPipe;

  constructor(
    protected injector: Injector,
  ) {
    this.taskService = injector.get(TaskService);
    this.dateFormatPipe = injector.get(DateFormatPipe);
  }

  get isLabelsAnswerTaskType(): boolean {
    return this.taskService.isLabelsAnswerTaskType(this.task);
  }

  get isStarRatingTaskType(): boolean {
    return this.taskService.isStarRatingTaskType(this.task);
  }

  get isYesNoAnswerTaskType(): boolean {
    return this.taskService.isYesNoAnswerTaskType(this.task);
  }

  get isReplyTaskType(): boolean {
    return this.taskService.isReplyTaskType(this.task);
  }

  calculateAvgStarRating(documentId: number): any {
    return this.taskService.calculateAvgStarRating(this.task, documentId);
  }

  answerToRating(answer: string): number {
    return this.taskService.answerToRating(answer);
  }

  countAnswers(documentId: number): number {
    return this.getAnswers(documentId).length;
  }

  countDoneAnswers(documentId: number): number {
    return this.getDoneAnswers(documentId).length;
  }

  getCountDoneAnswersText(documentId: number): string {
    const count = this.getDoneAnswers(documentId).length;
    return count > 0 ? (count > 1 ? `${count} replies` : `${count} reply`) : 'No replies';
  }

  getAnswers(documentId: number): TaskAssignmentModel[] {
    return this.taskService.getAnswers(this.task, documentId);
  }

  getDoneAnswers(documentId: number): TaskAssignmentModel[] {
    return this.taskService.getDoneAnswers(this.task, documentId);
  }

  groupAnswersByYesNo(documentId: number): { value: string, title: string, count: number, ratio: string }[] {
    const results = [
      {value: 'YES', title: 'Yes', count: 0, ratio: '0%'},
      {value: 'NO', title: 'No', count: 0, ratio: '0%'},
    ];

    const answers = this.getAnswers(documentId);
    const len = answers.length;
    for (let assignment of answers) {
      const existingResult = results.find((r) => r.value === assignment.answer);
      if (existingResult) {
        existingResult.count++;
        existingResult.ratio = ((existingResult.count / len) * 100).toFixed(0);
      }
    }

    return results.filter((r) => r.count > 0);
  }

  getFirstReplyAnswer(documentId: number): TaskAssignmentModel {
    return this.getAnswers(documentId).find((a) => a.answer?.trim()?.length > 0);
  }

  getRemainingReplyAnswers(documentId: number): TaskAssignmentModel[] {
    const firstAnswer = this.getFirstReplyAnswer(documentId);
    return this.getAnswers(documentId).filter((a) => a.id !== firstAnswer.id);
  }

  getAssignedTags(patent: Patent, ta: TaskAssignmentModel) {
    return patent?.custom_tags?.filter((ct) => ct.assigner_id == ta.assignee_id) || [];
  }

  groupAnnotatedLabelsByLabelId(labels: Label[]): [Label, number][][] {
    const results: [Label, number][] = [];
    for (let label of (labels || [])) {
      const existingResult = results.find((r) => r[0].id === label.id);
      if (existingResult) {
        existingResult[1]++;
      } else {
        results.push([label, 1]);
      }
    }
    const firstPart = results.splice(0, this.maxDisplayedLabels);
    return [firstPart, results];
  }

  getYesNoAnswerStatsTooltip(s: { value: string; title: string; count: number; ratio: string }): string {
    return `${s.count} user${s.count > 1 ? 's have' : ' has'} voted ${s.title.toUpperCase()} accounting for ${s.ratio}% of the total votes`;
  }

  getTaskAssignmentTooltip(taskAssignment: TaskAssignmentModel): string {
    if (taskAssignment && (this.taskService.isTaskAssignmentDone(taskAssignment) || taskAssignment.answer)) {
      return `Answered by you on ${this.dateFormatPipe.transform(taskAssignment.answered_at.toString(), 'ShortDateTime')}`;
    }

    return this.taskService.getTaskTypeItem(this.task.task_type)?.description;
  }
}
