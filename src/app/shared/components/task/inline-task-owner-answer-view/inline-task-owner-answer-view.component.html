
<div class="pt-1 pb-1 m-0 itoa-container">
  <ng-container *ngIf="countAnswers(documentId) > 0 else noAnswerTemplate">
    <div *ngIf="isStarRatingTaskType && countAnswers(documentId) > 0" class="d-flex justify-content-start align-items-center"
         [ngbTooltip]="getStarRatingTooltip()" container="body">
      <div class="task-avg-star-rating me-2">{{calculateAvgStarRating(documentId)}}</div>
      <ngb-rating [max]="5" [rate]="calculateAvgStarRating(documentId)" [readonly]="true">
        <ng-template let-fill="fill" let-index="index">
          <i class="fas fa-star taa-star-icon" *ngIf="fill === 100"></i>
          <i class="far fa-star taa-star-icon" *ngIf="fill === 0"></i>
          <i class="fas fa-star-half-alt taa-star-icon" *ngIf="fill > 0 && fill < 100"></i>
        </ng-template>
      </ngb-rating>
    </div>

    <div *ngIf="isYesNoAnswerTaskType" class="mb-0 itoa-yes-no-answers d-flex flex-column">
      <ng-container *ngFor="let r of groupAnswersByYesNo(documentId); let first = first;">
        <hr class="m-0 w-100" *ngIf="!first">
        <div class="d-flex justify-content-between align-items-center itoa-yes-no-answer"
             [ngbTooltip]="getYesNoAnswerStatsTooltip(r)">
          <div class="itoa-yes-no-title">{{r.title}}</div>
          <div class="itoa-yes-no-stats">({{r.count}} {{r.count > 1 ? 'replies' : 'reply'}} - {{r.ratio}}%)</div>
        </div>
      </ng-container>
    </div>

    <div *ngIf="isLabelsAnswerTaskType" class="itoa-labels-container">
      <ng-container *ngIf="isLoadingLabels">Loading labels...</ng-container>

      <div *ngIf="!isLoadingLabels && hasMoreLabels" class="border rounded p-1 itoa-labels-toggle"
           [ngbTooltip]="getShowMoreLabelsTitle()" (click)="showAllLabels = !showAllLabels">
        <i class="fa-solid" [ngClass]="showAllLabels ? 'fa-minus' : 'fa-plus'"></i>
      </div>

      <div *ngIf="!isLoadingLabels" class="d-flex flex-wrap justify-content-start itoa-labels"
           [ngClass]="{'itoa-show-all-labels': showAllLabels, 'itoa-has-more-labels': hasMoreLabels}">
        <ng-container *ngFor="let resultPart of groupAnnotatedLabelsByLabelId(annotatedLabels); let index=index;">
          <ng-container *ngIf="showAllLabels || index === 0">
            <div *ngFor="let result of resultPart" class="border rounded itoa-label-name pt-1 pb-1 ps-2 pe-2"
                 [ngStyle]="{'background-color': '#' + result[0].color}"
                 [ngbTooltip]="result[1] + ' user' + (result[1] > 1 ? 's have ' : ' has ') + 'assigned this label on the document'">
              {{result[0].name}} ({{result[1]}})
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>

    <div class="itoa-reply-type-answers p-0 m-0" *ngIf="isReplyTaskType && getFirstReplyAnswer(documentId)">
      <ng-container [ngTemplateOutlet]="replyTypeAnswerTemplate"
                    [ngTemplateOutletContext]="{taskAssignment: getFirstReplyAnswer(documentId), showMoreRepliesText: true}">
      </ng-container>

      <div *ngIf="showAllReplies" class="itoa-more-replies position-absolute">
        <ng-container *ngFor="let ta of getRemainingReplyAnswers(documentId)" [ngTemplateOutlet]="replyTypeAnswerTemplate"
                      [ngTemplateOutletContext]="{taskAssignment: ta, showMoreRepliesText: false}">
        </ng-container>
      </div>
    </div>
  </ng-container>
</div>

<ng-template #noAnswerTemplate>
  <div class="itoa-no-answer p-2">{{getNoAnswerText()}}</div>
</ng-template>

<ng-template #replyTypeAnswerTemplate let-taskAssignment="taskAssignment" let-showMoreRepliesText="showMoreRepliesText">
  <div class="itoa-reply-type-answer">
    <div class="d-flex justify-content-between align-items-center p-2" *ngIf="taskAssignment">
      <div class="flex-fill pe-1">
        <div class="itoa-answer-text">{{taskAssignment.answer}}</div>

        <a *ngIf="showMoreRepliesText && countAnswers(documentId) > 1" class="itoa-more-replies-text mt-1 d-block"
           (click)="toggleShowMoreReplies()">
          {{getShowMoreRepliesText()}}
        </a>
      </div>

      <app-text-avatar [user]="taskAssignment.assignee" [hasSubTitle]="false"
                       [ngbTooltip]="'Answered by ' + (taskAssignment.assignee | userTitle) + ' on ' + (taskAssignment.answered_at + 'Z' | dateFormat: 'ShortDateTime')"
                       container="body" class="itoa-text-avatar">
      </app-text-avatar>
    </div>
  </div>
</ng-template>
