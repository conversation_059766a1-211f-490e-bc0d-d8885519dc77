@import 'scss/layout2021/variables';
@import 'scss/components/star-rating';
@import '../shared/style';

$avatar_size : 30px;

.itoa-container {
  .task-avg-star-rating {
    font-size: 16px;
    width: 29px;
    height: 29px;
    line-height: 29px;
  }

  .itoa-reply-type-answers {
    .itoa-reply-type-answer {
      width: 250px;
      min-width: 250px;
      font-size: 14px;
      background-color: #FFF9E5;
      border: 1px solid #DCE7EC;

      .itoa-answer-text {
        max-height: 65px;
        overflow-y: auto;
      }
    }

    .itoa-more-replies-text {
      font-size: 12px;
      font-family: $font-open-sans-semi-bold;
      cursor: pointer;
      color: $link-color;
    }

    .itoa-more-replies  {
      box-shadow: 0 10px 15px #00000029;
      z-index: 99;
      overflow: auto;
      max-height: 350px;
    }

    .itoa-text-avatar {
      ::ng-deep {
        .ta-user {
          margin-bottom: 0 !important;
          width: $avatar_size !important;
          max-width: $avatar_size !important;

          .ta-avatar {
            height: $avatar_size !important;
            width: $avatar_size !important;
            font-size: 13px !important;
          }
        }
      }
    }
  }

  .itoa-labels-container {
    width: 250px;
    min-width: 250px;
    position: relative;

    .itoa-label-name {
      color: $color-text-04;
      font-size: 14px;
      line-height: 15px;
      font-family: $font-open-sans-regular;
    }

    .itoa-labels-toggle {
      float: right;
      cursor: pointer;
      color: $color-text-04;
      font-size: 14px;
      line-height: 15px;
      font-family: $font-open-sans-semi-bold;
    }

    .itoa-labels {
      gap: 3px;

      &.itoa-has-more-labels {
        margin-right: 25px;
      }

      &.itoa-show-all-labels {
        max-height: 350px;
        overflow: auto;
        position: absolute;
        z-index: 99;
        margin-right: 25px;
        padding: 3px;
        box-shadow: 0 10px 15px #00000029;
        background-color: white;
        border: 1px solid #DCE7EC;
        border-radius: 2px;
      }
    }
  }

  .itoa-yes-no-answers {
    .itoa-yes-no-answer {
      min-width: 160px;

      .itoa-yes-no-title {
        font-size: 16px;
        font-family: $font-open-sans-semi-bold;
        color: $color-text-04;
      }
      .itoa-yes-no-stats {
        font-size: 14px;
        font-family: $font-open-sans-semi-bold;
        color: $color-text-03;
      }
    }
  }

  .itoa-no-answer {
    font-size: 14px;
    background-color: #FFF9E5;
  }

  ::ng-deep {
    .taa-star-icon {
      font-size: 16px;
    }
  }
}
