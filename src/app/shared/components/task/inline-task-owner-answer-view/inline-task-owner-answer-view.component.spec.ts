import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InlineTaskOwnerAnswerViewComponent } from './inline-task-owner-answer-view.component';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('InlineTaskOwnerAnswerViewComponent', () => {
  let component: InlineTaskOwnerAnswerViewComponent;
  let fixture: ComponentFixture<InlineTaskOwnerAnswerViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      declarations: [ InlineTaskOwnerAnswerViewComponent ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InlineTaskOwnerAnswerViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
