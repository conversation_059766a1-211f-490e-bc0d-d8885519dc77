import { Component, Injector, Input } from '@angular/core';
import { Label } from '@core';
import { BaseTaskOperatorComponent } from '../base-task-operator/base-task-operator.component';

@Component({
  selector: 'app-inline-task-owner-answer-view',
  templateUrl: './inline-task-owner-answer-view.component.html',
  styleUrls: ['./inline-task-owner-answer-view.component.scss']
})
export class InlineTaskOwnerAnswerViewComponent extends BaseTaskOperatorComponent {
  @Input() annotatedLabels: Label[] = [];
  @Input() isLoadingLabels: boolean = false;
  @Input() showDocumentTitle: boolean = false;
  @Input() documentId: number;

  showAllReplies = false;
  showAllLabels = false;
  maxDisplayedLabels = 3;

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
  }

  get hasMoreLabels(): boolean {
    return this.countUniqueLabels > this.maxDisplayedLabels;
  }

  get countUniqueLabels(): number {
    return this.annotatedLabels ? [...new Set(this.annotatedLabels.map((a) => a.id))].length : 0;
  }

  get countHiddenLabels(): number {
    return this.countUniqueLabels - this.maxDisplayedLabels;
  }

  getStarRatingTooltip() {
    const count = this.getAnswers(this.documentId).length;
    const prefix = count > 0 ? (count > 1 ? `${count} users have` : `${count} user has`) : 'No users have';
    const suffix = count > 0 ? `with ${this.calculateAvgStarRating(this.documentId)} average star rating` : '';
    return `${prefix} rated this document ${suffix}`;
  }

  getShowMoreRepliesText(): string {
    const count = this.getAnswers(this.documentId).length;
    if (this.showAllReplies) {
      return `Hide ${count === 2 ? 'reply' : 'replies'}`;
    }
    return count > 1 ? `Show ${count - 1} more ${count === 2 ? 'reply' : 'replies'}` : '';
  }

  toggleShowMoreReplies() {
    this.showAllReplies = !this.showAllReplies;
  }

  getShowMoreLabelsTitle(): string {
    const countRemaining = this.countHiddenLabels;
    return (this.showAllLabels ? 'Hide ' : 'Show more ') + countRemaining + ' label' + (countRemaining > 1 ? 's' : '');
  }

  getNoAnswerText(): string {
    if (this.isReplyTaskType) {
      return 'No replies yet';
    }

    return 'No answers yet';
  }
}
