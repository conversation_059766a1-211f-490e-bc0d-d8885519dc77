@import 'scss/layout2021/variables';
@import 'scss/layout2021/mixins';

$statuses:
  "new" #0088FF #FFF,
  "open" #0088FF #FFF,
  "closed" #FF4D55 #FFF,
  "done" #00A083 #FFF,
  "overdue" #FF6700 #FFF;

@each $status, $bg, $color in $statuses {
  .badge-status-#{$status} {
    background-color: $bg !important;
    color: $color !important;
    padding: 5px 5px;
    width: 70px;
    min-width: 70px;
  }
}

.task-type-icon {
  font-size: 16px;
  color: $label-color;
}

.task-type-label {
  color: $color-text-03;
  font-family: $font-open-sans-bold;
  font-size: 16px;
}

.task-avg-star-rating {
  width: 48px;
  height: 48px;
  background: #FFC400;
  text-align: center;
  vertical-align: middle;
  color: $color-text-04;
  font-size: 24px;
  font-family: $font-open-sans-bold;
  line-height: 48px;

  span {
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
  }
}

.task-yes-no-stats  {
  width: 80px;
  height: 60px;
  background: #FFC400;
  text-align: center;
  vertical-align: middle;
  color: $color-text-04;
  font-size: 15px;
  font-family: $font-open-sans-bold;
}

.task-direction-icon {
  width: 18px;
  height: 18px;
}

.task-form-group {
    label {
      color: $label-color;
      font-size: 14px;
      font-family: $font-open-sans-regular;
    }

    .form-control {
      border: 1px solid #DCE7EC;

      @include placeholder() {
        color: #BCCACE !important;
        font-family: $font-open-sans-regular;
      }
    }
}
