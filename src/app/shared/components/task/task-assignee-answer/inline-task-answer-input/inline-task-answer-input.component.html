
<div *ngIf="showHeaderFooter"  class="d-flex justify-content-between align-items-center itai-header rounded-top border border-bottom-0"
     [ngStyle]="getStyle()"  (mouseenter)="setShowHeaderFooter(true)"
     (mouseover)="setShowHeaderFooter(true)"  (mouseleave)="setShowHeaderFooter(false)">
  <div class="d-flex justify-content-start align-items-center">
    <i class="{{taskService.getTaskTypeItem(task.task_type)?.icon}} task-type-icon me-2"></i>
    <div class="task-type-label">{{taskService.getTaskTypeItem(task.task_type)?.label}}</div>
  </div>

  <app-tooltip [tooltipTitle]="taskService.getTaskTypeItem(task.task_type)?.label"
               [tooltipText]="getTaskAssignmentTooltip(taskAssignment)">
  </app-tooltip>
</div>

<div class="pt-2 pb-2 ps-3 pe-3 m-0 itai-container" (mouseenter)="setShowHeaderFooter(true)"
     (mouseover)="setShowHeaderFooter(true)" [ngStyle]="getStyle()"
     (mouseleave)="setShowHeaderFooter(false, isLabelsAnswerTaskType || canAnswerTaskAssignment ? 300 : 10)"
     [ngClass]="{'rounded-bottom': showHeaderFooter && !(isLabelsAnswerTaskType || canAnswerTaskAssignment),
     'border-right border-start position-absolute itai-show-header-footer': showHeaderFooter,
     'border-bottom': showHeaderFooter && !(isLabelsAnswerTaskType || canAnswerTaskAssignment)}">

  <ngb-rating #starRatingEle *ngIf="isStarRatingTaskType" [max]="5" [(rate)]="currentRate" [readonly]="disableFields()">
    <ng-template let-fill="fill" let-index="index">
      <i class="fas fa-star taa-star-icon" *ngIf="fill === 100"></i>
      <i class="far fa-star taa-star-icon" *ngIf="fill === 0"></i>
      <i class="fas fa-star-half-alt taa-star-icon" *ngIf="fill > 0 && fill < 100"></i>
    </ng-template>
  </ngb-rating>

  <div *ngIf="isYesNoAnswerTaskType" class="">
    <div class="custom-radio-button" *ngFor="let opt of yesNoOptions; let last = last;">
      <input class="custom-radio-input" type="radio" [value]="opt.value" [id]="taskAssignment.id + '_' + opt.value"
             [name]="taskAssignment.id + '_yes_no'" [(ngModel)]="yesNoAnswer" [disabled]="disableFields()">
      <label class="custom-radio-label" [for]="taskAssignment.id + '_' + opt.value" [ngClass]="{'mb-0': last}">{{opt.label}}</label>
    </div>
  </div>

  <div *ngIf="isLabelsAnswerTaskType" class="taa-labels-container" [id]="labelsContainerEleId">
    <ng-container *ngIf="isLoadingTags">Loading tags...</ng-container>

    <ng-container *ngIf="!isLoadingTags">
      <ng-container *ngIf="getAvailableTags().length > 0 else noAnswerTemplate">
        <ng-container *ngFor="let t of getAvailableTags();" [ngTemplateOutlet]="tagRowTemplate"
                      [ngTemplateOutletContext]="{tag: t, checked: isTagSelected(t)}">
        </ng-container>
      </ng-container>
    </ng-container>
  </div>

  <div class="" *ngIf="isReplyTaskType">
      <textarea *ngIf="(replyText || canAnswerTaskAssignment) else noAnswerTemplate" class="form-control" maxlength="1000"
                [(ngModel)]="replyText" [disabled]="disableFields()" rows="2"
                [placeholder]="isReplyTaskType ? 'Reply / comment' : 'Reply / comment (optional)'">
      </textarea>
  </div>

  <div *ngIf="showHeaderFooter && (isLabelsAnswerTaskType || canAnswerTaskAssignment)"
       (mouseenter)="setShowHeaderFooter(true)" (mouseover)="setShowHeaderFooter(true)"
       (mouseleave)="setShowHeaderFooter(false)" [ngStyle]="getStyle()"
       class="itai-footer rounded-bottom border border-top-0 pt-3">
    <div class="pb-2 text-end font-open-sans-light" *ngIf="isLabelsAnswerTaskType">
      <ng-container *ngIf="selectedTags.length === 0 && canAnswerTaskAssignment">
        No tags selected
      </ng-container>
      <ng-container *ngIf="selectedTags.length > 0">
        Selected {{selectedTags.length}} tag{{selectedTags.length > 1 ? 's' : ''}}
      </ng-container>
    </div>

    <!-- TODO remove ngIf when BE allows adding a reply for task assignment -->
    <div class="mb-2" *ngIf="isLabelsAnswerTaskType && (replyText || canAnswerTaskAssignment)">
      <textarea class="form-control" maxlength="1000" [(ngModel)]="replyText" [disabled]="disableFields()"
                rows="2" [placeholder]="isReplyTaskType ? 'Reply / comment' : 'Reply / comment (optional)'">
      </textarea>
    </div>

    <div class="d-flex justify-content-end" *ngIf="canAnswerTaskAssignment">
      <button class="btn btn-sm btn-primary" (click)="onAnswerClicked()" [disabled]="disableSubmitButton()">Submit</button>
    </div>
  </div>
</div>

<ng-template #noAnswerTemplate>
  <div class="itai-no-answer p-2">Not answered yet</div>
</ng-template>

<ng-template #tagRowTemplate let-tag="tag" let-checked="checked">
  <div class="d-flex justify-content-between align-items-center taa-label-row ms-0 ps-2 pt-2 pb-2 pe-0 cursor-pointer"
       [ngClass]="{'disabled': disableFields()}" (click)="toggleTag($event, tag)">
    <div class="border rounded taa-box-color"
         [ngStyle]="{'background-color': '#' + tag.color}">#</div>
    <div class="flex-fill taa-label-name ms-2 me-2">
      {{tag.name}}
    </div>
    <div *ngIf="!showHeaderFooter && selectedTags?.length > 1">
      (+{{selectedTags.length - 1}})
    </div>
    <div>
      <label class="checkbox mb-0" [ngClass]="{'disabled': disableFields()}">
        <input type="checkbox" class="form-check-input" [checked]="checked"
               (change)="toggleTag($event, tag)" id="taa-label-{{tag.id}}" [disabled]="disableFields()"/>
        <span class="form-check-label">&nbsp;</span>
      </label>
    </div>
  </div>
</ng-template>
