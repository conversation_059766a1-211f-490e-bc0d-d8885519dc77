@import 'scss/layout2021/variables';
@import '../task-answer-input/task-answer-input.component';

:host {
  position: relative;

  .itai-header {
    position: absolute;
    top: -44px;
    left: 0;
    padding: 10px 10px 10px 15px;
    z-index: 99;
  }

  .itai-footer {
    position: absolute;
    left: -1px;
    padding: 0 10px 10px 15px;
    z-index: 99;
  }

  .itai-container {
    top: 0;

    .taa-labels-container {
      max-height: 40px !important;
      overflow-y: hidden;

      .taa-label-row {
        .taa-label-name {
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
        }
      }
    }

    &.itai-show-header-footer {
      z-index: 99;

      .taa-labels-container {
        max-height: 150px !important;
        overflow-y: auto;

        .taa-label-row {
          .taa-label-name {
            -webkit-line-clamp: 2;
          }
        }
      }
    }

    ::ng-deep {
      .taa-star-icon {
        font-size: 24px !important;
      }

      .fas.fa-star {
        font-size: 24px !important;
      }
    }
  }

}
