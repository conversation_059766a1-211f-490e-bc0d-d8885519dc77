import { Component, Injector, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { TaskAnswerInputComponent } from '../task-answer-input/task-answer-input.component';
import { BehaviorSubject, Subscription, timer } from 'rxjs';
import { debounce, distinctUntilChanged } from 'rxjs/operators';
import { TaskAssignmentModel, TaskTypeEnum } from '@core';
import { NgbRating } from '@ng-bootstrap/ng-bootstrap/rating/rating';
import { TagModel } from '@core/models/tag.model';

function generateUniqueName(prefix: string, length: number = 25): string {
  const randomNumber = Math.floor(Math.random() * Math.floor(1000000));
  return `${prefix}${(new Date()).getTime() + randomNumber}`.slice(0, length);
}

@Component({
  selector: 'app-inline-task-answer-input',
  templateUrl: './inline-task-answer-input.component.html',
  styleUrls: ['./inline-task-answer-input.component.scss']
})
export class InlineTaskAnswerInputComponent extends TaskAnswerInputComponent implements OnInit, OnDestroy {
  showHeaderFooter = false;
  labelsContainerEleId = generateUniqueName('labels-container-');

  @ViewChild('starRatingEle', {static: false}) private starRatingEle: NgbRating;

  private bgColor = '#FFC4001A';
  private hoveredBgColor = '#FFF0BE';

  private showHeaderFooterSub = new BehaviorSubject<[boolean, number]>([false, 0]);
  private readonly showHeaderFooter$ = this.showHeaderFooterSub.asObservable();

  constructor(
    protected injector: Injector
  ) {
    super(injector);
  }

  ngOnInit() {
    super.ngOnInit();

    const showHeaderFooter$ = this.showHeaderFooter$
      .pipe(
        debounce((val: [boolean, number]) => timer(val[0] ? 0 : val[1])),
        distinctUntilChanged()
      )
      .subscribe({
        next: (val) => {
          if (!val[0]) {
            this.scrollTagsToTop();
          }
          this.showHeaderFooter = val[0];
        }
      });
    this.subscriptions.add(showHeaderFooter$);
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  getAvailableTags(): TagModel[] {
    if (this.showHeaderFooter) {
      return super.getAvailableTags();
    }

    const tagAssignments = this.getAssignedTags(this.patent, this.taskAssignment);
    if (tagAssignments.length > 0) {
      return tagAssignments;
    }

    return this.canAnswerTaskAssignment ? this.tags : [];
  }

  setShowHeaderFooter(val: boolean, dueTime = 0) {
    this.showHeaderFooterSub.next([val, dueTime]);

    if (!val && this.isStarRatingTaskType && !this.currentRate && this.starRatingEle) {
      this.starRatingEle.reset();
    }
  }

  getStyle() {
    const width = this.getWidth();
    return {
      'background-color': this.getBackgroundColor(),
      'width': width,
      'min-width': width
    };
  }

  protected savedAnswerSuccessCallback(assignment: TaskAssignmentModel) {
    this.showHeaderFooterSub.next([false, 0]);
  }

  private getWidth(): string {
    switch (this.task.task_type) {
      case TaskTypeEnum.YES_NO_ANSWER:
        return this.showHeaderFooter ? '200px' : '150px';
      case TaskTypeEnum.LABELS:
        return '250px';
      case TaskTypeEnum.TEXT_REPLY:
        return '250px';
      case TaskTypeEnum.STAR_RATING:
        return '167px';
    }
  }

  private getBackgroundColor(): string {
    return this.showHeaderFooter ? this.hoveredBgColor : this.bgColor;
  }

  private scrollTagsToTop() {
    const ele = document.getElementById(this.labelsContainerEleId);
    if (ele) {
      ele.scroll({top: 0, behavior: 'smooth'});
    }
  }
}
