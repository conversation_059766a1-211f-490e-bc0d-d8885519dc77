
<div class="pt-3 ps-3 pe-3" *ngIf="errorMessages.length || successMessage"
     [ngClass]="showDocumentTitle && taskAssignment.document ? 'pb-0' : 'pb-2'">
  <app-alert type="danger" [message]="errorMessages" *ngIf="errorMessages.length"></app-alert>
  <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>
</div>

<div class="pb-3 ps-3 pe-3 pt-3 d-flex justify-content-between align-items-start" *ngIf="showDocumentTitle && taskAssignment.document">
  <a class="taa-document-title pe-3" target="_blank" [routerLink]="['/ratings', task.id]"
     [queryParams]="{document_id: taskAssignment.document_id, redirect_mode: taskRedirectModeEnum.PATENT_VIEW}">
    # {{getPublicationNumber(taskAssignment.document)}} - {{taskAssignment.document.bibliographic.title}}
  </a>

  <div class="badge badge-status-{{taskAssignment.status.toLowerCase()}}">{{taskAssignment.status | titlecase}}</div>
</div>

<ngb-rating *ngIf="isStarRatingTaskType" [max]="5" [(rate)]="currentRate" [readonly]="disableFields()" class="ps-3 pe-3">
  <ng-template let-fill="fill" let-index="index">
    <i class="fas fa-star taa-star-icon" *ngIf="fill === 100"></i>
    <i class="far fa-star taa-star-icon" *ngIf="fill === 0"></i>
    <i class="fas fa-star-half-alt taa-star-icon" *ngIf="fill > 0 && fill < 100"></i>
  </ng-template>
</ngb-rating>

<div *ngIf="isYesNoAnswerTaskType" class="mb-3 ps-3 pe-3">
  <ng-container *ngFor="let opt of yesNoOptions; let last = last;">
    <div *ngIf="canAnswerTaskAssignment || opt.value === taskAssignment.answer" class="custom-radio-button">
      <input class="custom-radio-input" type="radio" [value]="opt.value" [id]="taskAssignment.id + '_' + opt.value"
             [name]="taskAssignment.id + '_yes_no'" [(ngModel)]="yesNoAnswer" [disabled]="disableFields()">
      <label class="custom-radio-label" [for]="taskAssignment.id + '_' + opt.value" [ngClass]="{'mb-0': last}">{{opt.label}}</label>
    </div>
  </ng-container>
</div>

<div *ngIf="isLabelsAnswerTaskType" class="taa-labels-container me-3">
  <ng-container *ngFor="let t of getAvailableTags();" [ngTemplateOutlet]="tagRowTemplate"
                [ngTemplateOutletContext]="{tag: t, checked: isTagSelected(t)}">
  </ng-container>
</div>

<div class="pt-2 pb-2 pe-3 text-end font-open-sans-light" *ngIf="isLabelsAnswerTaskType">
  <ng-container *ngIf="selectedTags.length === 0 && canAnswerTaskAssignment">
    No tags selected
  </ng-container>
  <ng-container *ngIf="selectedTags.length > 0">
    Selected {{selectedTags.length}} tag{{selectedTags.length > 1 ? 's' : ''}}
  </ng-container>
</div>

<!-- TODO remove ngIf when BE allows adding a reply for task assignment -->
<div class="ps-3 pe-3 mb-2" *ngIf="isReplyTaskType || (isLabelsAnswerTaskType && (replyText || canAnswerTaskAssignment))">
    <textarea class="form-control" maxlength="1000" [(ngModel)]="replyText" [disabled]="disableFields()"
              [rows]="isReplyTaskType ? 3 : 2" [placeholder]="isReplyTaskType ? 'Reply / comment' : 'Reply / comment (optional)'">
    </textarea>
</div>

<div class="d-flex justify-content-end ps-3 pe-3" *ngIf="canAnswerTaskAssignment">
  <button class="btn btn-sm btn-primary" (click)="onAnswerClicked()" [disabled]="disableSubmitButton()">Submit</button>
</div>

<ng-template #tagRowTemplate let-tag="tag" let-checked="checked">
  <div class="d-flex justify-content-between align-items-center taa-label-row ms-2 ps-2 pt-2 pb-2 pe-0 cursor-pointer"
       [ngClass]="{'disabled': disableFields()}" (click)="toggleTag($event, tag)">
    <div class="border rounded taa-box-color"
         [ngStyle]="{'background-color': '#' + tag.color}">#</div>
    <div class="flex-fill taa-label-name ms-2 me-2">{{tag.name}}</div>
    <div>
      <label class="checkbox mb-0" [ngClass]="{'disabled': disableFields()}">
        <input type="checkbox" class="form-check-input" [checked]="checked"
               (change)="toggleTag($event, tag)" id="taa-label-{{tag.id}}" [disabled]="disableFields()"/>
        <span class="form-check-label">&nbsp;</span>
      </label>
    </div>
  </div>
</ng-template>
