@import 'scss/layout2021/variables';
@import 'scss/components/star-rating';
@import 'scss/components/custom-radio-button';
@import '../../shared/style';

:host{
  .custom-radio-label, .taa-label-name {
    color: $color-text-04;
    font-size: 16px;
    font-family: $font-open-sans-regular;
  }

  .taa-label-name {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .taa-labels-container {
    max-height: 150px !important;
    overflow-y: auto;

    .taa-label-row {
      .taa-box-color {
        width: 24px;
        height: 24px;
        text-align: center;
        color: white;
        min-width: 24px;
      }

      &:not(.disabled):hover {
        background-color: #FFE282;
      }
    }
  }

  ::ng-deep {
    .taa-star-icon {
      font-size: 24px;
    }

    .alert {
      margin-bottom: 0 !important;
    }
  }

  .taa-document-title {
    color: $link-color;
    font-size: 16px;
    font-family: $font-open-sans-semi-bold;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    max-width: 100%;
  }

}
