import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskAnswerInputComponent } from './task-answer-input.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('TaskAnswerInputComponent', () => {
  let component: TaskAnswerInputComponent;
  let fixture: ComponentFixture<TaskAnswerInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskAnswerInputComponent ],
      imports: [HttpClientTestingModule, SharedModule, RouterModule.forRoot([])],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TaskAnswerInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
