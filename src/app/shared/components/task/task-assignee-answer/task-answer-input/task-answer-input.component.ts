import { Component, Injector, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import {
  MatomoService,
  Patent,
  TagService,
  TaskAssignmentModel,
  TaskRedirectModeEnum,
  TaskTypeEnum,
} from '@core';
import { finalize, map, switchMap } from 'rxjs/operators';
import { forkJoin, Observable, of, Subscription } from 'rxjs';
import { BaseTaskOperatorComponent } from '../../base-task-operator/base-task-operator.component';
import { TagModel } from '@core/models/tag.model';

@Component({
  selector: 'app-task-answer-input',
  templateUrl: './task-answer-input.component.html',
  styleUrls: ['./task-answer-input.component.scss']
})
export class TaskAnswerInputComponent extends BaseTaskOperatorComponent implements OnInit, OnChanges, OnDestroy {
  @Input() patent: Patent;
  @Input() taskAssignment: TaskAssignmentModel;
  @Input() tags: TagModel[] = [];
  @Input() isLoadingTags: boolean = false;
  @Input() showDocumentTitle: boolean = false;

  selectedTags: TagModel[] = [];

  currentRate = 0;
  yesNoAnswer: string = null;
  replyText = '';
  isSaving = false;
  errorMessages: string[] = [];
  successMessage: string = null;

  yesNoOptions = [
    {value: 'YES', label: 'Yes'},
    {value: 'NO', label: 'No'},
  ];

  taskRedirectModeEnum = TaskRedirectModeEnum;

  private tagService: TagService;
  private matomoService: MatomoService;
  protected subscriptions = new Subscription();

  get canAnswerTaskAssignment(): boolean {
    return this.taskService.canAnswerTaskAssignment(this.task, this.taskAssignment);
  }

  constructor(
    protected injector: Injector
  ) {
    super(injector);
    this.tagService = injector.get(TagService);
    this.matomoService = injector.get(MatomoService);
  }

  ngOnInit(): void {
    this.setAnswer();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['patent']) {
      this.selectedTags = this.getAssignedTags(this.patent, this.taskAssignment);
    }
    if (changes['selectedTags']) {
      this.selectedTags = (changes['selectedTags']?.currentValue || []).map((t) => t);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onAnswerClicked() {
    this.errorMessages = [];
    this.successMessage = null;

    if (!this.taskAssignment) {
      this.errorMessages = ['This task was not assigned to you'];
      return;
    }

    const payload = this.buildPayload();

    if (this.isValidAnswer(payload)) {
      this.isSaving = true;
      const updateTaskAssignment$ = this.taskService.answerTaskAssignment(this.taskAssignment.id, payload)
        .pipe(
          switchMap((assignment: TaskAssignmentModel) => this.assignTags(assignment)),
          switchMap((assignment: TaskAssignmentModel) => this.saveReplyText(assignment, this.replyText)),
          switchMap((assignment: TaskAssignmentModel) => this.getTaskStatus(assignment)),
          finalize(() => this.isSaving = false)
        )
        .subscribe({
          next: (data) => {
          this.successMessage = 'Your answer has been saved';
          Object.assign(this.taskAssignment, data);
          this.savedAnswerSuccessCallback(data);
          if (data.status === 'DONE') {
            this.matomoService.taskManagementTaskAccomplished();
          }
        },
        error: (error) => {
          if (error.error?.message) {
            this.errorMessages = error.error.message;
          } else {
            this.errorMessages = ['Error when submitting your answer. ' + (error.details ? 'Please try again.' : '')];

            if (error.message) {
              this.errorMessages.push(error.message);
            }
          }
        }});
      this.subscriptions.add(updateTaskAssignment$);
    } else {
      this.setErrorMessages();
    }
  }

  disableSubmitButton(): boolean {
    if (this.isSaving || !this.canAnswerTaskAssignment) {
      return true;
    }

    return !this.isValidAnswer(this.buildPayload());
  }

  disableFields() {
    return this.isSaving || !this.canAnswerTaskAssignment;
  }

  getAvailableTags(): TagModel[] {
    return this.disableFields() ? this.getAssignedTags(this.patent, this.taskAssignment) : this.tags;
  }

  toggleTag(event: Event, t: TagModel) {
    event.preventDefault();
    event.stopPropagation();

    if (!this.disableFields()) {
      if (this.selectedTags.find(al => al.id === t.id)) {
        this.selectedTags = this.selectedTags.filter(al => al.id !== t.id);
      } else {
        this.selectedTags.push(t);
      }
    }
  }

  isTagSelected(tag: TagModel): boolean {
    return this.selectedTags.findIndex((t) => t.id === tag.id) > -1;
  }

  getPublicationNumber(patent: Patent): string {
    return patent && patent.general && patent.general.raw_publication_number ?
      patent.general.raw_publication_number.replace(/-|-/gi, '') : 'N/A';
  }

  protected savedAnswerSuccessCallback(assignment: TaskAssignmentModel) {

  }

  protected savedAnswerFailedCallback() {
  }

  private isValidAnswer(payload: { answer: string }): boolean {
    if (this.isLabelsAnswerTaskType) {
      return this.selectedTags.length > 0;
    }

    return payload.answer !== null;
  }

  private setAnswer() {
    if (!this.taskAssignment) {
      return;
    }

    switch (this.task.task_type) {
      case TaskTypeEnum.STAR_RATING:
        this.currentRate = Number(this.taskAssignment.answer);
        break;
      case TaskTypeEnum.YES_NO_ANSWER:
        this.yesNoAnswer = this.taskAssignment.answer;
        break;
      case TaskTypeEnum.TEXT_REPLY:
        this.replyText = this.taskAssignment.answer;
        break;
      case TaskTypeEnum.LABELS:
        this.replyText = this.taskAssignment.answer;
        break;
    }

    this.replyText = this.replyText?.trim() || null;
  }

  private setErrorMessages() {
    switch (this.task.task_type) {
      case TaskTypeEnum.TEXT_REPLY:
        this.errorMessages = ['Please enter your answer'];
        break;
      case TaskTypeEnum.STAR_RATING:
        this.errorMessages = ['Please select a rating'];
        break;
      case TaskTypeEnum.YES_NO_ANSWER:
        this.errorMessages = ['Please choose a YES/NO option'];
        break;
      case TaskTypeEnum.LABELS:
        this.errorMessages = ['Please choose a label'];
        break;
    }

    return false;
  }

  private buildPayload(): { answer: string } {
    const trimmedText = this.replyText && this.replyText.trim().length > 0 ? this.replyText.trim() : null;

    switch (this.task.task_type) {
      case TaskTypeEnum.TEXT_REPLY:
        return {answer: trimmedText};
      case TaskTypeEnum.STAR_RATING:
        return {answer: this.currentRate > 0 ? this.currentRate.toString(10) : null};
      case TaskTypeEnum.YES_NO_ANSWER:
        return {answer: this.yesNoAnswer ? this.yesNoAnswer : null};
      case TaskTypeEnum.LABELS:
        return {answer: trimmedText ? trimmedText : ' '};
    }
  }

  private assignTags(assignment: TaskAssignmentModel): Observable<TaskAssignmentModel> {
    if (this.isLabelsAnswerTaskType && this.selectedTags.length) {
      const insertingObs = this.selectedTags.map((t) => this.tagService.assign(t, {'document_ids': [assignment.document_id]}));
      this.isSaving = true;
      return forkJoin(insertingObs)
        .pipe(
          map(() => assignment),
          finalize(() => {
            this.selectedTags.forEach((t) => {
              if (this.patent.custom_tags?.filter((ct) => ct.id === t.id).length == 0) {
                t.assigner_id = assignment.assignee_id;
                this.patent.custom_tags?.push(t);
              }
            })
            this.isSaving = false;
          })
        );
    }

    return of(assignment);
  }

  /**
   * TODO: save the reply text for some kind of task type
   *
   * @param assignment
   * @param replyText
   * @private
   */
  private saveReplyText(assignment: TaskAssignmentModel, replyText: string): Observable<TaskAssignmentModel> {
    switch (this.task.task_type) {
      case TaskTypeEnum.STAR_RATING:
        return of(assignment);
      case TaskTypeEnum.YES_NO_ANSWER:
        return of(assignment);
    }

    return of(assignment);
  }

  private getTaskStatus(assignment: TaskAssignmentModel): Observable<TaskAssignmentModel> {
    return this.taskService.getTask(this.task.id)
      .pipe(
        map((t) => this.task.status = t.status),
        map(() => assignment)
      );
  }
}
