<div class="modal-header" *ngIf="isDialog">
  <div class="modal-title">
    <span class="label-options">{{canAnswerTaskAssignment ? 'Answer task' : 'View task'}}</span>
  </div>
  <button (click)="onCloseDialog()" aria-label="Close" class="close" tabindex="-1" type="button"></button>
</div>

<div class="task-assignee-answer-container d-flex flex-column justify-content-start overflow-auto" *ngIf="task"
     [ngClass]="{'modal-body': isDialog, 'mh-100': !isDialog}">

  <app-task-short-summary [task]="task"></app-task-short-summary>

  <ng-container *ngIf="showTaskAnswerInput">
    <div class="taa-task-assignment-title pt-3 pb-2">
      Task assignment{{task.assignments.length > 1 ? 's' : ''}}
      <ng-container *ngIf="task.assignments.length > 0">
        ({{getStatistics()}} / {{task.assignments.length}} Total)
      </ng-container>
    </div>

    <div class="taa-task-assignment-type">
      <div class="d-flex justify-content-between align-items-center p-3">
        <div class="d-flex justify-content-start align-items-center">
          <i class="{{taskService.getTaskTypeItem(task.task_type)?.icon}} task-type-icon me-2"></i>
          <div class="task-type-label">{{taskService.getTaskTypeItem(task.task_type)?.label}}</div>
        </div>

        <app-tooltip [tooltipTitle]="taskService.getTaskTypeItem(task.task_type)?.label"
                     [tooltipText]="getTaskAssignmentTooltip(taskService.myAssignment(task))">
        </app-tooltip>
      </div>

      <hr class="w-100 mt-0 mb-0" *ngIf="task.assignments.length > 1"/>
    </div>

    <div class="taa-task-assignment d-flex flex-column justify-content-start align-items-stretch task-form-group flex-fill pb-3 pt-0">
      <ng-container *ngIf="(!isLabelsAnswerTaskType || !(isLoadingTags)) else loadingSpinner">
        <ng-container *ngFor="let ta of sortAssignments(); let last = last;">
          <app-task-answer-input [task]="task" [taskAssignment]="ta" [tags]="tags" [patent]="ta.document"
                                 [showDocumentTitle]="showDocumentTitle">
          </app-task-answer-input>

          <hr class="w-100 mt-3 mb-0" *ngIf="!last && task.assignments.length > 1"/>
        </ng-container>
      </ng-container>
    </div>
  </ng-container>
</div>

<div class="modal-footer d-flex justify-content-end align-items-center" *ngIf="isDialog">
  <button type="button" class="btn btn-md btn-primary-outline" (click)="onCloseDialog()" tabindex="-1">Close</button>
</div>

<ng-template #loadingSpinner>
  <div class="text-center align-items-center">
    <img src="/assets/images/octimine_blue_spinner.gif" class="loading-spinner">
  </div>
</ng-template>
