import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskAssigneeAnswerComponent } from './task-assignee-answer.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { TaskModel } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('TaskAssigneeAnswerComponent', () => {
  let component: TaskAssigneeAnswerComponent;
  let fixture: ComponentFixture<TaskAssigneeAnswerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskAssigneeAnswerComponent ],
      imports: [HttpClientTestingModule, SharedModule, RouterModule.forRoot([])],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    const task = {} as TaskModel;
    task.team_users = [];
    task.assignments = [];
    fixture = TestBed.createComponent(TaskAssigneeAnswerComponent);
    component = fixture.componentInstance;
    component.task = task;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
