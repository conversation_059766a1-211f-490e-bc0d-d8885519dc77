import { Component, Injector, OnDestroy, OnInit } from '@angular/core';
import { TaskViewBaseComponent } from '../task-view-base/task-view-base.component';
import { TaskAssignmentModel, TaskAssignmentStatusEnum } from '@core';
import { finalize } from 'rxjs/operators';
import { TitleCasePipe } from '@angular/common';
import { TagModel } from '@core/models/tag.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-task-assignee-answer',
  templateUrl: './task-assignee-answer.component.html',
  styleUrls: ['./task-assignee-answer.component.scss']
})
export class TaskAssigneeAnswerComponent extends TaskViewBaseComponent implements OnInit, OnDestroy {
  isLoadingTags: boolean = true;
  tags: TagModel[] = [];

  constructor(
    protected injector: Injector,
    private titleCasePipe: TitleCasePipe
  ) {
    super(injector);
  }

  get canAnswerTaskAssignment(): boolean {
    return this.taskService.canAnswerTaskAssignment(this.task, this.taskService.myAssignment(this.task));
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.loadTags();
    this.markTaskAssignmentAsOpen();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  markTaskAssignmentAsOpen() {
    this.subscriptions.add(this.taskService.markMyTaskAssignmentsAsOpen([this.task]).subscribe());
  }

  sortAssignments(): TaskAssignmentModel[] {
    if (this.task.assignments) {
      const statuses = [
        TaskAssignmentStatusEnum.OVERDUE,
        TaskAssignmentStatusEnum.NEW,
        TaskAssignmentStatusEnum.OPEN,
        TaskAssignmentStatusEnum.DONE,
        TaskAssignmentStatusEnum.CLOSED
      ];
      const results = [];
      for (let status of statuses) {
        this.task.assignments.filter(assignment => assignment.status === status)
          .forEach(assignment => results.push(assignment));
      }
      return results;
    }

    return [];
  }

  getStatistics(): string {
    const statuses = [
      TaskAssignmentStatusEnum.OVERDUE,
      TaskAssignmentStatusEnum.NEW,
      TaskAssignmentStatusEnum.OPEN,
      TaskAssignmentStatusEnum.DONE
    ];
    const results = [];

    for (let status of statuses) {
      const count = this.task.assignments.filter(assignment => assignment.status === status).length;
      if (count > 0) {
        results.push(`${count} ${this.titleCasePipe.transform(status)}`);
      }
    }

    return results.length ? results.join(' - ') : '0 Done';
  }

  private loadTags() {
    if (this.isLabelsAnswerTaskType && this.canAnswerTaskAssignment) {
      this.isLoadingTags = true;
      const getTags$ = this.tagService.getTags({page_size: 250})
        .pipe(
          finalize(() => this.isLoadingTags = false)
        )
        .subscribe({
          next: ({tags}) => {
            this.tags = tags;
          }
        });
      this.subscriptions.add(getTags$);
    } else {
      this.isLoadingTags = false;
    }
  }
}
