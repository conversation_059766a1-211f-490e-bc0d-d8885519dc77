<div class="modal-header" *ngIf="isDialog">
  <div class="modal-title">
    <span class="label-options">{{ getDialogTitle() }}</span>
  </div>
  <button (click)="onCloseTaskForm()" aria-label="Close" class="close" tabindex="-1" type="button"></button>
</div>

<div class="task-form-container" [ngClass]="{'modal-body': isDialog}">
  <app-alert type="danger" [message]="taskFormError.messages" *ngIf="taskFormError?.messages"></app-alert>
  <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>

  <form [formGroup]="taskForm" class="task-form-group">
    <div class="mb-3">
      <label>Task name</label>
      <input type="text" name="subject" formControlName="subject" class="form-control" required maxlength="255"
             [ngClass]="{'is-invalid': isFieldInvalid('subject')}"/>
    </div>

    <div class="mb-3">
      <label>Task assignee</label>

     <app-team-users-selection formControlName="assignee_ids" [isMultiple]="true" [isDisabled]="taskForm.disabled"
                               [isInvalid]="isFieldInvalid('assignee_ids')" [includeMe]="false">
     </app-team-users-selection>
    </div>

    <ng-container *ngIf="!isDialog">
      <ng-container [ngTemplateOutlet]="descriptionTemplate" [ngTemplateOutletContext]="{formGroup: taskForm}"></ng-container>
      <ng-container [ngTemplateOutlet]="deadlineTemplate" [ngTemplateOutletContext]="{formGroup: taskForm}"></ng-container>
      <ng-container [ngTemplateOutlet]="taskTypesTemplate"></ng-container>
    </ng-container>

    <ng-container *ngIf="isDialog">
      <div class="d-flex justify-content-between">
        <div class="w-50 pe-4">
          <ng-container [ngTemplateOutlet]="deadlineTemplate" [ngTemplateOutletContext]="{formGroup: taskForm}"></ng-container>
          <ng-container [ngTemplateOutlet]="descriptionTemplate" [ngTemplateOutletContext]="{formGroup: taskForm}"></ng-container>
        </div>
        <div class="w-50">
          <ng-container [ngTemplateOutlet]="taskTypesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

  </form>
</div>

<div class="modal-footer d-flex justify-content-end align-items-center" *ngIf="isDialog">
  <button type="button" class="btn btn-md btn-primary-outline me-1" (click)="onCloseTaskForm()" tabindex="-1"
          [disabled]="isSavingTask">Cancel</button>
  <button type="button" class="btn btn-md btn-primary" (click)="createOrUpdateTask()" [disabled]="isSavingTask || taskForm.invalid || !taskForm.dirty">
    Save
  </button>
</div>

<ng-template #descriptionTemplate let-formGroup="formGroup">
  <div class="mb-3" [formGroup]="formGroup">
    <label>Task description (optional)</label>
    <textarea name="description" formControlName="description" class="form-control" rows="5" maxlength="1000"
              [ngClass]="{'is-invalid': isFieldInvalid('description')}"></textarea>
  </div>
</ng-template>

<ng-template #deadlineTemplate let-formGroup="formGroup">
  <div class="mb-3" [formGroup]="formGroup">
    <label>Deadline (optional)</label>
    <input appMaskDate ngbDatepicker #dp="ngbDatepicker" (click)="dp.open()" [minDate]="getDeadlineMinDate()"
           [ngClass]="{'is-invalid': isFieldInvalid('deadline')}"
           autocomplete="off" class="form-control" formControlName="deadline"
           name="deadline" type="text"/>
  </div>
</ng-template>

<ng-template #taskTypesTemplate>
  <div class="mb-3">
    <label>Assessment requirements</label>

    <div *ngFor="let type of getAvailableTaskTypes();" class="d-flex justify-content-between align-items-center task-type mb-2">
      <label class="checkbox mb-0" *ngIf="!this.isEditing()">
        <input type="checkbox" class="form-check-input" [value]="type.value" [checked]="isTaskTypeSelected(type)"
               (change)="toggleTaskType(type)" id="task-type-{{type.value}}" name="task_type" [disabled]="isEditing()" (click)="onTaskTypeClicked(type, $event)"/>
        <span class="form-check-label"></span>
      </label>

      <div class="ms-2 me-3"><i class="{{type.icon}} task-type-icon"></i></div>

      <label class="d-flex flex-fill flex-column justify-content-start" for="task-type-{{type.value}}" [ngClass]="{'cursor-pointer': !this.isEditing()}">
        <div class="task-type-label">{{type.label}}</div>
        <div class="task-type-description">{{type.description}}</div>
      </label>

      <app-tooltip [tooltipTitle]="type.label" [tooltipText]="type.description"></app-tooltip>
    </div>
  </div>
</ng-template>
