@import "scss/layout2021/variables";
@import '../shared/style';

.task-form-container {

  .task-type {
    .checkbox {
      span {
        &:before, &:after {
          top: -14px !important;
          border-radius: 50%;
        }
      }

      &:not(:disabled):not(.disabled) input[type=checkbox]:checked + span:after {
        padding-left: 0.18rem !important;
      }
    }

    .task-type-description {
      color: $label-color;
      font-size: 12px;
    }
  }
}
