import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import {
  Collection,
  CollectionService,
  dateStr2NgbDate,
  ngbDate2DateStr,
  PatentListScopeEnum,
  TaskFormError,
  TaskModel,
  TaskResourceTypeEnum,
  TaskService,
  TaskTypeEnum,
  TaskTypeItem,
  UserService
} from '@core';
import { TASK_TYPES } from '@core/data/task_types';
import { forkJoin, Observable, Subscription } from 'rxjs';
import { finalize, mergeMap } from 'rxjs/operators';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';

@Component({
  selector: 'app-task-form',
  templateUrl: './task-form.component.html',
  styleUrls: ['./task-form.component.scss']
})
export class TaskFormComponent implements On<PERSON><PERSON>t, OnChanges, On<PERSON><PERSON>roy {

  @Input() task = {} as TaskModel;
  @Input() isDialog: boolean = false;
  @Input() resourceId: number = null;
  @Input() resourceType: TaskResourceTypeEnum = null;
  @Input() documentIds: number[] = [];
  @Input() isPublications: boolean = false;
  @Output() taskFormChanged = new EventEmitter<UntypedFormGroup>();
  @Output() taskSaved = new EventEmitter<{ message: string, payload: TaskModel, savedTasks: TaskModel[] }>();

  taskForm: UntypedFormGroup;
  taskFormError: TaskFormError = null;
  successMessage: string = null;

  taskTypes = TASK_TYPES;
  isSavingTask: boolean = false;

  private subscriptions = new Subscription();

  constructor(
    private formBuilder: UntypedFormBuilder,
    private taskService: TaskService,
    private collectionService: CollectionService,
    public userService: UserService,
    private activeModal: NgbActiveModal
  ) {
  }

  get assigneeIdsFormControl(): UntypedFormControl {
    return this.taskForm.get('assignee_ids') as UntypedFormControl;
  }

  get taskTypesFormControl(): UntypedFormControl {
    return this.taskForm.get('task_types') as UntypedFormControl;
  }

  get taskAssigneeIds(): number[] {
    return this.task.assignee_ids || [];
  }

  private get mustCreateCollection(): boolean {
    return ![TaskResourceTypeEnum.DOCUMENT, TaskResourceTypeEnum.COLLECTION].includes(this.resourceType) || this.isPublications;
  }

  ngOnInit(): void {
    this.initializeValues();
    this.buildForm(true);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['task'] && !changes['task'].firstChange) {
      this.initializeValues();
      this.buildForm(false);
      this.updateAssigneeFormControlValue();
      this.taskForm.enable();
    }
  }

  onCloseTaskForm() {
    if (this.isDialog) {
      this.activeModal.dismiss();
    }
  }

  isFieldInvalid(name: string): boolean {
    const field = this.taskForm.get(name);
    return (field && (field.touched || field.value) && field.invalid) || (this.taskFormError?.details && this.taskFormError.details[name]?.length > 0);
  }

  toggleTaskType(type: TaskTypeItem) {
    if (this.isEditing() || this.isTaskTypeSelected(type)) {
      return;
    }

    this.taskTypesFormControl.patchValue([type.value]);
  }

  isTaskTypeSelected(type: TaskTypeItem): boolean {
    return (this.taskTypesFormControl.value || []).includes(type.value);
  }

  isEditing(): boolean {
    return !!(this.task?.id);
  }

  disableTaskForm(disabled: boolean) {
    if (this.taskForm) {
      if (disabled) {
        this.taskForm.disable();
      } else {
        this.taskForm.enable();
      }
    }
  }

  clearTaskForm() {
    if (this.taskForm) {
      this.taskForm.reset();
      this.taskForm.markAsPristine();
      this.taskForm.markAsUntouched();
      this.taskForm.updateValueAndValidity();
      this.taskForm.enable();
    }
  }

  getDeadlineMinDate() {
    const today = new Date();
    const todayNgbDate = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate()
    };

    if (this.task.deadline) {
      const deadlineNgbDate = dateStr2NgbDate(moment.utc(this.task.deadline).local().format('YYYY-MM-DD'));
      return deadlineNgbDate.after(todayNgbDate) ? todayNgbDate : deadlineNgbDate;
    }

    return todayNgbDate;
  }

  createOrUpdateTask() {
    this.taskForm.markAllAsTouched();
    this.clearFormMessages();

    const formValue = this.getTaskFormValue();
    const isValidFormData = this.taskForm.valid && formValue.task_types?.length > 0;
    const isValidDocumentIds = this.isEditing() || this.documentIds.length > 0;
    const isValidResource = (this.resourceId && this.resourceType) || !(this.resourceId || this.resourceType);

    if (isValidFormData && isValidResource && isValidDocumentIds) {

      this.disableTaskForm(true);
      this.isSavingTask = true;

      const taskPayloads = this.buildTaskPayloads(formValue);
      let observables;

      if (this.isEditing()) {
        observables = this.updateTasks(taskPayloads);
      } else {
        observables = this.mustCreateCollection ? this.createCollectionThenCreateTasks(formValue, taskPayloads) : this.createTasks(taskPayloads);
      }

      const obs$ = observables
        .pipe(
          finalize(() => {
            this.disableTaskForm(false);
            this.isSavingTask = false;
          }),
        )
        .subscribe({
          next: (tasks: TaskModel[]) => {
            this.successMessage = `Task <b>${formValue.subject}</b> has been ${this.isEditing() ? 'updated' : 'created'} successfully`;
            const payload = {...formValue} as TaskModel;
            payload.document_ids = this.documentIds;
            this.taskSaved.emit({message: this.successMessage, payload: payload, savedTasks: tasks});
            this.clearTaskForm();
          },
          error: ({error}) => {
            const messages = ['Error when saving the task. ' + (error.details ? 'Please correct the form and try again.' : '')];

            if (error.message) {
              messages.push(error.message);
            }

            if (!formValue.task_types.length) {
              messages.push('Please select at least one task type');
            }

            this.taskFormError = {details: error.details || {}, messages} as TaskFormError;

            this.scrollToTaskFormError();
          }
        });
      this.subscriptions.add(obs$);
    } else {
      const messages = [];
      if (!this.resourceId || !this.resourceType) {
        messages.push('Please select specify a resource for task');
      } else {
        messages.push('Please correct the form and try again');

        if (!formValue.task_types?.length) {
          messages.push('Please select at least one task type');
        }

        if (!this.isEditing() && !this.documentIds?.length) {
          messages.push('Please select some documents for task');
        }
      }

      this.taskFormError = {messages} as TaskFormError;
      this.scrollToTaskFormError();
    }
  }

  getAvailableTaskTypes(): TaskTypeItem[] {
    return this.taskTypes.filter((taskType) => {
      return !this.task.task_type || taskType.value === this.task.task_type;
    });
  }

  clearFormMessages() {
    this.taskFormError = {} as TaskFormError;
    this.successMessage = null;
  }

  getDialogTitle(): string {
    if (this.isEditing()) {
      return 'Edit task';
    }

    const countDocuments = this.documentIds?.length || 0;

    switch (this.resourceType) {
      case TaskResourceTypeEnum.DOCUMENT:
        return 'Create new task for document';
      case TaskResourceTypeEnum.COLLECTION:
        return 'Create new task for ' + countDocuments + ' documents in list';
      case TaskResourceTypeEnum.MONITOR_RUN:
        return 'Create new task for ' + countDocuments + ' documents in monitor results';
      default:
        return 'Create new task' + (countDocuments > 0 ? ' for ' + countDocuments + ' selected documents' : '');
    }
  }

  onTaskTypeClicked(type: TaskTypeItem, event: MouseEvent) {
    if (this.isTaskTypeSelected(type)) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  /**
   * When the task is being edited, set task_types as a one-element array from task_type of task.
   */
  private buildForm(subscribeValueChanges = false) {
    const taskTypes = this.task.task_type ? [this.task.task_type] : [];
    const deadline = this.task.deadline ? dateStr2NgbDate(moment.utc(this.task.deadline).local().format('YYYY-MM-DD')) : null;

    this.taskForm = this.formBuilder.group({
      subject: [this.task.subject, [Validators.required, Validators.maxLength(255)]],
      description: [this.task.description, [Validators.maxLength(1000)]],
      deadline: [deadline],
      assignee_ids: [this.taskAssigneeIds, [Validators.required, Validators.minLength(1)]],
      task_types: [taskTypes, [Validators.required, Validators.minLength(1)]],
    });

    if (subscribeValueChanges) {
      const valueChanges$ = this.taskForm.valueChanges.subscribe({
        next: (val) => {
          this.taskFormChanged.emit(this.taskForm);
        }
      });
      this.subscriptions.add(valueChanges$);
    }

    this.taskFormChanged.emit(this.taskForm);
  }

  private updateAssigneeFormControlValue() {
    this.assigneeIdsFormControl.patchValue(this.taskAssigneeIds);
    this.assigneeIdsFormControl.markAsPristine();
    this.assigneeIdsFormControl.markAsUntouched();
  }

  private getTaskFormValue(): TaskModel {
    const data = this.taskForm.value;
    const deadline = data.deadline ?
      moment(ngbDate2DateStr(data.deadline, 'T23:59:59')).utc().format('YYYY-MM-DDTHH:mm:ss') : null;

    return {
      subject: data.subject,
      description: data.description,
      deadline: deadline,
      assignee_ids: data.assignee_ids,
      task_types: data.task_types
    } as TaskModel;
  }

  private buildTaskPayloads(formData: TaskModel): TaskModel[] {
    if (this.isEditing()) {
      return [
        {
          subject: formData.subject,
          description: formData.description,
          deadline: formData.deadline,
          assignee_ids: formData.assignee_ids
        } as TaskModel
      ]
    }

    return formData.task_types.map((taskType: TaskTypeEnum) => {
      const payload = {...formData} as TaskModel;
      delete payload.task_types;
      payload.resource_id = this.resourceId;
      payload.resource_type = this.resourceType;
      payload.task_type = taskType;
      payload.document_ids = this.documentIds;
      return payload;
    });
  }

  private scrollToTaskFormError() {
    setTimeout(() => {
      const errorElement = document.querySelector('#task-form-component app-alert');
      if (errorElement) {
        errorElement.scrollIntoView({behavior: 'smooth'});
      }
    }, 100);
  }

  private initializeValues() {
    if (this.isEditing()) {
      this.resourceId = this.task.resource_id;
      this.resourceType = this.task.resource_type;
    }
  }

  private buildCollectionPayload(task: TaskModel): Collection {
    const payload = {
      name: task.subject,
      description: task.description,
      document_ids: this.documentIds.map((id) => id.toString()),
      collection_type: PatentListScopeEnum.FAMILY
    } as Collection;

    if ( this.resourceType === TaskResourceTypeEnum.MONITOR_RUN){
      payload.monitor_run_id = this.resourceId;
    }

    return payload;
  }

  private updateTasks(taskPayloads: TaskModel[]): Observable<TaskModel[]> {
    return forkJoin(taskPayloads.map((payload) => {
      return this.taskService.updateTask(this.task.id, payload);
    }));
  }

  private createTasks(taskPayloads: TaskModel[]): Observable<TaskModel[]> {
    return forkJoin(taskPayloads.map((payload: TaskModel) => {
      return this.taskService.createTask(payload);
    }));
  }

  private createCollectionThenCreateTasks(formValue: TaskModel, taskPayloads: TaskModel[]): Observable<TaskModel[]> {
    return this.collectionService.createCollection(this.buildCollectionPayload(formValue))
      .pipe(
        mergeMap((collection: Collection) => {
          return forkJoin(taskPayloads.map((payload: TaskModel) => {
            const newPayload = {...payload} as TaskModel;
            newPayload.resource_id = collection.id;
            newPayload.resource_type = TaskResourceTypeEnum.COLLECTION;
            return this.taskService.createTask(newPayload);
          }));
        })
      );
  }
}
