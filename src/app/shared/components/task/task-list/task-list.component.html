<ng-container *ngIf="currentViewMode === viewModeEnum.FORM">
  <ng-container *ngIf="!isLoadingTasks else loadingSpinner">
    <div class="d-flex justify-content-between m-3">
      <div class="task-management-title">{{editingTask?.id ? 'Edit' : 'Create'}} task</div>
      <button class="btn btn-md btn-ghost pe-0 text-end" (click)="onViewTasksClicked()">View tasks</button>
    </div>

    <app-task-form class="p-0 flex-fill overflow-y-auto me-3 ms-3" id="task-form-component"
                   [task]="editingTask" (taskFormChanged)="onTaskFormChanged($event)" (taskSaved)="onTaskSavedEvent($event)"
                   [resourceId]="resourceId" [resourceType]="resourceType" [documentIds]="[resourceId]">
    </app-task-form>

    <div class="d-flex justify-content-end align-items-center p-3 task-form-actions">
      <img src="assets/images/octimine_blue_spinner.gif" class="me-2 saving-task-spinner" *ngIf="isSavingTask">
      <button class="btn btn-md btn-gray-ghost me-2" [disabled]="isSavingTask" (click)="onCancelTaskClicked()">Cancel</button>
      <button class="btn btn-md btn-primary" (click)="onSaveTaskClicked()" [disabled]="isSavingTask || taskForm?.invalid || !taskForm?.dirty">Save task</button>
    </div>
  </ng-container>
</ng-container>

<ng-container *ngIf="currentViewMode === viewModeEnum.OVERVIEW || currentViewMode === viewModeEnum.ANSWER">
  <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>

  <app-alert type="success" [message]="saveTaskSuccessMessage" *ngIf="saveTaskSuccessMessage" class="me-3 ms-3"></app-alert>

  <app-task-owner-overview *ngIf="selectedTask && taskService.isOutgoingTask(selectedTask) && currentViewMode === viewModeEnum.OVERVIEW"
                           [task]="selectedTask" [showDocumentTitle]="showDocumentTitle"
                           (editTaskEvent)="onEditTaskEvent($event)" class="flex-fill overflow-y-auto me-3 ms-3">
  </app-task-owner-overview>

  <app-task-assignee-answer *ngIf="selectedTask && taskService.isIncomingTask(selectedTask) && currentViewMode === viewModeEnum.ANSWER"
                            [task]="selectedTask" [showDocumentTitle]="showDocumentTitle" [showTaskAnswerInput]="showTaskAnswerInput"
                            class="flex-fill overflow-y-auto me-3 ms-3">
  </app-task-assignee-answer>
</ng-container>

<ng-container *ngIf="currentViewMode === viewModeEnum.LIST">
  <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>

  <div class="flex-fill p-0 overflow-y-auto mb-3 me-3 ms-3">
    <div class="task-list-container" *ngIf="!isLoadingTasks else loadingSpinner">
      <div *ngFor="let task of tasks" class="task-list-row w-100 border-bottom p-2" (click)="onTaskClicked(task)">
        <div class="d-flex justify-content-start align-items-center">
          <img *ngIf="taskService.isIncomingTask(task)" class="task-direction-icon" ngbTooltip="Assigned to you" src="assets/images/in-bound-tm-icon-16x16.svg"/>
          <img *ngIf="taskService.isOutgoingTask(task)" class="task-direction-icon" ngbTooltip="Created by you" src="assets/images/out-bound-tm-icon-16x16.svg"/>

          <div class="d-flex flex-column justify-content-between align-items-start flex-fill ps-3">
            <div class="d-flex justify-content-between align-items-start w-100 mb-2">
              <div class="task-subject pe-2">{{task.subject}}</div>

              <div>
                <div *ngIf="taskService.isIncomingTask(task)" class="badge badge-status-{{taskService.myAssignment(task)?.status?.toLowerCase()}}">
                  {{taskService.myAssignment(task)?.status | titlecase}}
                </div>
                <div *ngIf="taskService.isOutgoingTask(task)" class="badge badge-status-{{task.status.toLowerCase()}}">{{task.status | titlecase}}</div>
              </div>
            </div>

            <div class="d-flex justify-content-between align-items-center w-100">
              <div class="d-flex justify-content-start align-items-center flex-fill">
                <i class="{{taskService.getTaskTypeItem(task.task_type).icon}} task-type-icon me-2"></i>
                <div class="task-type-label">{{taskService.getTaskTypeItem(task.task_type).label}}</div>
              </div>

              <app-user-avatars *ngIf="taskService.isOutgoingTask(task)" class="pe-2"
                                [users]="task.team_users" [numberDisplayedUsers]="3" [distanceBetweenAvatars]="25" avatarsTooltipPrefix="Assigned to">
              </app-user-avatars>

              <app-user-avatars *ngIf="taskService.isIncomingTask(task)" class="pe-2"
                                [users]="[task.author]" [numberDisplayedUsers]="1" avatarsTooltipPrefix="Created by">
              </app-user-avatars>

              <div class="d-flex flex-column justify-content-start align-items-end task-deadline-col">
                <ng-container *ngIf="task.deadline">
                  <div class="task-deadline-title">Deadline</div>
                  <div class="task-deadline-value">{{task.deadline + 'Z' | dateFormat: 'ShortDate'}}</div>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>

      <app-alert type="warning" [message]="getNoTasksMessage()" *ngIf="!tasks.length"></app-alert>
    </div>
  </div>
</ng-container>

<ng-template #loadingSpinner>
  <div class="text-center align-items-center">
    <img src="/assets/images/octimine_blue_spinner.gif" class="mt-2 loading-spinner">
  </div>
</ng-template>

<ng-template #headerTemplate>
  <div class="d-flex justify-content-between m-3">
    <div class="task-management-title">{{getTaskManagementTitle(selectedTask)}}</div>
    <div>
      <button *ngIf="selectedTask" class="btn btn-md btn-ghost pe-0 text-end" (click)="onViewTasksClicked()">Back to tasks</button>
      <button class="btn btn-sm btn-primary" *ngIf="!selectedTask && showCreateTaskButton && !isLoadingTasks" (click)="onCreateTaskClicked()">
        <img src="/assets/images/tool-icon-task-white.svg" class="me-2 task-create-button">Create task
      </button>
    </div>
  </div>
</ng-template>
