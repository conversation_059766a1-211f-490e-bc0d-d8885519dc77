@import 'scss/layout2021/variables';
@import '../shared/style';

.task-list-container {

  .task-list-row {
    cursor: pointer;

    &:hover {
      background: #F1F1F1;
    }

    .task-subject {
      color: $brand-green;
      font-size: 14px;
      word-break: break-word;
    }

    .task-deadline-col {
      min-width: 95px;
    }

    .task-deadline-title {
      color: $label-color;
      font-size: 14px;
    }

    .task-deadline-value {
      color: $color-text-04;
      font-size: 14px;
    }

    .task-type-icon, .task-type-label {
      font-size: 14px;
    }
  }
}

.task-management-title {
  color: $color-text-04;
  font-size: 24px;
  font-family: $font-open-sans-bold;
}

.task-form-actions {
  background: $modal-footer-background;
  border-top: 1px solid rgba(105, 138, 149, 0.2);

  .saving-task-spinner {
    height: 36px;
  }
}

.task-create-button {
  height: 15px;
  width: 15px;
}
