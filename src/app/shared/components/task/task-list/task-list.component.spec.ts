import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskListComponent } from './task-list.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { TaskFormComponent, TaskOwnerOverviewComponent } from '@shared/components';
import { provideMatomo } from 'ngx-matomo-client';

describe('TaskListComponent', () => {
  let component: TaskListComponent;
  let fixture: ComponentFixture<TaskListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskListComponent, TaskFormComponent, TaskOwnerOverviewComponent ],
      imports: [HttpClientTestingModule, SharedModule, RouterModule.forRoot([])],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TaskListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    component.taskFormComponent = TestBed.createComponent(TaskFormComponent).componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
