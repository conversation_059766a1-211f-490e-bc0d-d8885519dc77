import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { AvatarService, MatomoService, NotificationsService, TaskModel, TaskResourceTypeEnum, TaskService } from '@core';
import { finalize, mergeMap } from 'rxjs/operators';
import { TaskFormComponent } from '../task-form/task-form.component';
import { ActivatedRoute } from '@angular/router';
import { UntypedFormGroup } from '@angular/forms';
import { Subscription } from 'rxjs';
import { Location } from '@angular/common';

enum TaskManagementViewModeEnum {
  LIST = 'LIST',
  OVERVIEW = 'OVERVIEW',
  FORM = 'FORM',
  ANSWER = 'ANSWER',
}

@Component({
  selector: 'app-task-list',
  templateUrl: './task-list.component.html',
  styleUrls: ['./task-list.component.scss']
})
export class TaskListComponent implements OnInit, On<PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  @ViewChild(TaskFormComponent, {static: false}) taskFormComponent: TaskFormComponent;

  @Input() resourceId: number = null;
  @Input() resourceType: TaskResourceTypeEnum = null;
  @Input() showCreateTaskButton: boolean = true;
  @Input() showDocumentTitle: boolean = false;
  @Input() showTaskAnswerInput: boolean = true;
  @Output() afterInit = new EventEmitter();
  @Output() taskSelectedEvent = new EventEmitter<TaskModel>();

  isLoadingTasks = true;
  tasks: TaskModel[] = [];
  numberDisplayedUsers = 5;
  taskForm: UntypedFormGroup = null;
  saveTaskSuccessMessage: string = null;
  isSavingTask = false;
  selectedTask: TaskModel = null;
  editingTask: TaskModel = {} as TaskModel;
  currentViewMode = TaskManagementViewModeEnum.LIST;
  viewModeEnum = TaskManagementViewModeEnum;

  private subscriptions = new Subscription();

  constructor(
    public taskService: TaskService,
    public avatarService: AvatarService,
    private activatedRoute: ActivatedRoute,
    private matomoService: MatomoService,
    private location: Location,
    private notificationService: NotificationsService
  ) {
  }

  private get queryTaskId(): number {
    return +this.activatedRoute.snapshot.queryParams.task_id;
  }

  ngOnInit(): void {
    this.loadTasksAndAssignees(true, true);
  }

  ngOnDestroy(): void {
    this.clearSelectedTask();
    this.subscriptions.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.resourceId && !changes.resourceId.firstChange) {
      this.clearSelectedTask();
      this.loadTasksAndAssignees(true);
    }
  }

  onSaveTaskClicked() {
    if (this.taskFormComponent) {
      this.taskFormComponent.createOrUpdateTask();
    }
  }

  onTaskClicked(task: TaskModel) {
    this.setSelectedTask(task);
    this.clearTaskFormMessages();
  }

  onViewTasksClicked() {
    this.currentViewMode = TaskManagementViewModeEnum.LIST;
    this.clearSelectedTask();
  }

  onCreateTaskClicked() {
    this.matomoService.taskManagementAddTaskButton();
    this.editingTask = {} as TaskModel;
    this.currentViewMode = TaskManagementViewModeEnum.FORM;
    this.clearTaskFormMessages();
    this.clearSelectedTask();
  }

  onCancelTaskClicked() {
    this.clearTaskFormMessages();

    if (this.selectedTask?.id) {
      this.currentViewMode = this.taskService.isOutgoingTask(this.selectedTask) ?
        TaskManagementViewModeEnum.OVERVIEW : TaskManagementViewModeEnum.ANSWER;
      this.updateQueryParams({task_id: this.selectedTask.id});
    } else {
      this.taskFormComponent.clearTaskForm();
      this.currentViewMode = TaskManagementViewModeEnum.LIST;
      this.updateQueryParams({task_id: null});
    }
  }

  getTaskManagementTitle(selectedTask: TaskModel): string {
    if (!selectedTask) {
      return 'Tasks';
    }

    if (this.showTaskAnswerInput && this.taskService.isIncomingTask(selectedTask) &&
      this.taskService.canAnswerTaskAssignment(selectedTask, this.taskService.myAssignment(selectedTask))) {
      return 'Answer task';
    }

    return 'View task';
  }

  onTaskSavedEvent(val: { message: string, payload: TaskModel, savedTasks: TaskModel[] }) {
    this.saveTaskSuccessMessage = val.message;
    this.loadTasksAndAssignees(false);
    this.updateQueryParams({task_id: val.savedTasks[0].id});
  }

  onTaskFormChanged($event: UntypedFormGroup) {
    this.taskForm = $event;
  }

  onEditTaskEvent($event: TaskModel) {
    this.editingTask = $event;
    this.currentViewMode = TaskManagementViewModeEnum.FORM;
    this.clearTaskFormMessages();
    this.updateQueryParams({task_id: $event.id});
  }

  getNoTasksMessage(): string {
    switch (this.resourceType) {
      case TaskResourceTypeEnum.DOCUMENT:
        return 'This patent doesn\'t have any tasks yet';
      case TaskResourceTypeEnum.COLLECTION:
        return 'This list doesn\'t have any tasks yet';
      case TaskResourceTypeEnum.MONITOR_RUN:
        return 'This monitor run doesn\'t have any tasks yet';
    }
  }

  private loadTasksAndAssignees(canDisplayTaskForm: boolean, isInitialization = false) {
    if (this.resourceId && this.resourceType) {
      this.tasks = [];
      this.isLoadingTasks = true;
      const payload = {
        load_all: 1,
      };

      if (this.resourceType === TaskResourceTypeEnum.DOCUMENT) {
        payload['document_id'] = this.resourceId;
      } else {
        payload['resource_id'] = this.resourceId;
        payload['resource_type'] = this.resourceType;
      }

      const getTasks$ = this.taskService.getTasks(payload)
        .pipe(
          mergeMap(({tasks}) => {
            return this.taskService.getAuthorAndAssigneesForTasks(tasks);
          }),
          finalize(() => this.isLoadingTasks = false)
        )
        .subscribe({
          next: (tasks: TaskModel[]) => {
            this.tasks = tasks;

            if (isInitialization) {
              this.afterInit.emit();
            }

            const task = this.tasks.find(t => t.id === this.queryTaskId);

            if (task) {
              this.setSelectedTask(task);
            } else {
              if (canDisplayTaskForm && tasks.length === 0) {
                this.currentViewMode = TaskManagementViewModeEnum.FORM;
              } else {
                this.currentViewMode = TaskManagementViewModeEnum.LIST;
              }
            }
          },
          error: error => {
            console.error(error);
          }
        });
      this.subscriptions.add(getTasks$);
    }
  }

  private updateQueryParams(params: any) {
    const pathUrl = this.location.path().split('?')[0] + (params.task_id ? '?' + new URLSearchParams(params).toString() : '');
    this.location.replaceState(pathUrl);
  }

  private clearTaskFormMessages() {
    this.saveTaskSuccessMessage = null;
    if (this.taskFormComponent) {
      this.taskFormComponent.clearFormMessages();
    }
  }

  private setSelectedTask(task: TaskModel) {
    this.selectedTask = task;
    this.currentViewMode = this.taskService.isOutgoingTask(task) ?
      TaskManagementViewModeEnum.OVERVIEW : TaskManagementViewModeEnum.ANSWER;

    const notification$ = this.notificationService.markAsReadForResource(task.id, 'TASK').subscribe();
    this.subscriptions.add(notification$);

    this.updateQueryParams({task_id: task.id});

    this.markTaskAssignmentsAsOpened(task);
    this.taskSelectedEvent.emit(task);
  }

  private markTaskAssignmentsAsOpened(task: TaskModel) {
    this.subscriptions.add(this.taskService.markMyTaskAssignmentsAsOpen([task]).subscribe());
  }

  private clearSelectedTask() {
    this.selectedTask = null;
    this.updateQueryParams({task_id: null});
    this.taskSelectedEvent.emit(null);
  }
}
