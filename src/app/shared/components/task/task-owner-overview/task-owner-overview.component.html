<div class="modal-header" *ngIf="isDialog">
  <div class="modal-title">
    <span class="label-options">View task assignments</span>
  </div>
  <button (click)="onCloseDialog()" aria-label="Close" class="close" tabindex="-1" type="button"></button>
</div>

<div class="task-owner-overview-container" *ngIf="task" [ngClass]="{'modal-body': isDialog}">
  <app-task-short-summary [task]="task" (editTaskEvent)="onEditTaskEvent($event)" [isDialog]="isDialog"></app-task-short-summary>

  <ng-container *ngIf="!isLoadingTask">
    <div class="d-flex justify-content-between mt-3 mb-3">
      <div class="d-flex justify-content-start align-items-center">
        <i class="{{taskService.getTaskTypeItem(task.task_type)?.icon}} task-type-icon me-2"></i>
        <div class="task-type-label">{{taskService.getTaskTypeItem(task.task_type)?.label}}</div>
      </div>

      <div *ngIf="(isReplyTaskType || isLabelsAnswerTaskType) && countAnswers(null) > 0" class="task-count-replies">
        {{ countAnswers(null) === 0 ? 'No replies' : countAnswers(null) + ' ' + ('reply'| pluralize: countAnswers(null)) }}
      </div>

      <div *ngIf="isStarRatingTaskType" class="task-avg-star-rating">
        {{calculateAvgStarRating(null)}}
      </div>

      <div *ngIf="isYesNoAnswerTaskType && countAnswers(null) > 0" class="d-flex flex-column justify-content-around align-items-center">
        <div class="task-yes-no-stats p-1">
          <ng-container *ngIf="countAnswers(null) > 0">
            <ng-container *ngFor="let r of groupAnswersByYesNo(null); let first=first;">
              <hr *ngIf="!first" class="w-100 mb-1 mt-1"/>
              <div class="task-yes-no-answer" [ngbTooltip]="getYesNoAnswerStatsTooltip(r)">
                {{r.title}} {{r.ratio}}%
              </div>
            </ng-container>
          </ng-container>
        </div>
      </div>
    </div>

    <div class="task-assignments">
      <div *ngIf="countAnswers(null) === 0" class="task-count-no-replies">No replies</div>

      <div class="task-assignment-container mt-3 mb-2" *ngFor="let ta of getAnswers(null); let last = last;"
           [ngClass]="{'pb-4': last}">
        <div class="d-flex justify-content-between align-items-center mt-2">
          <app-user-avatar [user]="ta.assignee" [hasSubTitle]="false" class="task-assignee-text-avatar" [avatarSize]="30" [avatarFontSize]="12"></app-user-avatar>

          <div class="flex-fill ps-2 ta-assignee">{{ta.assignee | userTitle}}</div>
          <div *ngIf="taskService.isTaskAssignmentOverdue(ta)" class="badge badge-status-{{ta.status.toLowerCase()}} ps-2">{{ta.status | titlecase}}</div>
          <div class="ta-answered-date ps-2" *ngIf="ta.answered_at">{{ta.answered_at + 'Z' | dateFormat: 'ShortDate'}}</div>
        </div>

        <div class="d-flex justify-content-start">
          <div class="ta-vertical-bar-col d-flex flex-column ta-vertical-bar-no-reply-action"
               [ngClass]="{'ta-vertical-bar-reply-box': isReplyingAssignmentIds[ta.id], 'ta-vertical-bar-reply-button': !isReplyingAssignmentIds[ta.id]}">

            <!-- TODO disabled while backend is not ready-->
            <ng-container *ngIf="false">
              <div class="ta-vertical-bar-top"></div>
              <div class="ta-vertical-bar-bottom"></div>
            </ng-container>
          </div>

          <div class="d-flex flex-column justify-content-start align-items-start mt-1 w-100">

            <div *ngIf="isStarRatingTaskType" class="ta-star-rating d-flex justify-content-start align-items-baseline">
              <div class="ta-star-rating-value me-2">{{answerToRating(ta.answer)}}</div>

              <ngb-rating [max]="5" [readonly]="true" [rate]="answerToRating(ta.answer)">
                <ng-template let-fill="fill" let-index="index">
                  <i class="fas fa-star" *ngIf="fill === 100"></i>
                  <i class="far fa-star" *ngIf="fill === 0"></i>
                  <i class="fas fa-star-half-alt" *ngIf="fill > 0 && fill < 100"></i>
                </ng-template>
              </ngb-rating>
            </div>

            <div *ngIf="isYesNoAnswerTaskType" class="ta-yes-no-answer-value">{{ta.answer}}</div>

            <div *ngIf="isLabelsAnswerTaskType" class="d-flex flex-wrap justify-content-start ta-label-box">
              <div *ngFor="let tag of getAssignedTags(ta.document, ta)"
                   class="d-flex justify-content-start border rounded ta-label-name pt-1 pb-1 ps-2 pe-2"
                   [ngStyle]="{'background-color': '#' + tag.color, 'color': getTextColor('#'  + tag.color)}">
                {{tag.name}}
              </div>
            </div>

            <div *ngIf="ta.answer?.trim()?.length && (isReplyTaskType || isLabelsAnswerTaskType)" class="ta-answer-text mt-1">{{ta.answer}}</div>

            <a *ngIf="showDocumentTitle && ta.document" class="ta-document-title pt-2" target="_blank"
               [routerLink]="['/ratings', task.id]" [queryParams]="{document_id: ta.document_id, redirect_mode: taskRedirectModeEnum.PATENT_VIEW}">
              # {{getPublicationNumber(ta.document)}} - {{ta.document.bibliographic.title}}
            </a>

            <!-- TODO disabled while backend is not ready-->
            <div class="ta-reply-button" *ngIf="!isReplyingAssignmentIds[ta.id] && false" (click)="showReplyBox(ta)">Reply</div>

            <div class="ta-reply-box d-flex justify-content-between align-items-center ps-2 pe-2 pt-1 pb-1 mt-2"
                 *ngIf="isReplyingAssignmentIds[ta.id]" [ngClass]="{'disabled': isSavingReply[ta.id]}">
              <textarea #replyInput class="flex-fill" rows="1" placeholder="Typing something to reply"
                        [disabled]="isSavingReply[ta.id]"></textarea>
              <div class="ta-reply-button ps-2 pe-2" (click)="onReplyButtonClicked(ta, replyInput.value)">Reply</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <div class="text-center align-items-center" *ngIf="isLoadingTask">
    <img src="/assets/images/octimine_blue_spinner.gif" class="mt-2 loading-spinner">
  </div>
</div>

<div class="modal-footer d-flex justify-content-end align-items-center" *ngIf="isDialog">
  <button type="button" class="btn btn-md btn-primary-outline" (click)="onCloseDialog()" tabindex="-1">Close</button>
</div>
