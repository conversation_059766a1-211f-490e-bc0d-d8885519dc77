@import 'scss/layout2021/variables';
@import 'scss/layout2021/mixins';
@import 'scss/components/star-rating';
@import '../shared/style';

.task-owner-overview-container {

  .task-count-replies {
    color: $color-text-04;
    font-size: 14px;
    font-family: $font-open-sans-bold;
  }

  &.modal-body {
    max-height: calc(100vh - 250px);
    overflow-y: auto;
  }

  .task-assignments {

    .task-assignment-container {
      .ta-overdue {
        color: $brand-orange;
        font-size: 10px;
        line-height: 10px;
        font-family: $font-open-sans-regular;
      }

      .ta-assignee {
        color: $color-text-04;
        font-size: 14px;
        font-family: $font-open-sans-semi-bold;
        word-break: break-word;
      }

      .ta-answer-text {
        color: $color-text-04;
        font-size: 14px;
        font-family: $font-open-sans-regular;
        max-height: 65px;
        overflow-y: auto;
      }

      .ta-answered-date {
        color: $color-text-04;
        font-size: 12px;
        font-family: $font-open-sans-regular;
      }

      .ta-reply-button {
        color: $brand-green;
        font-size: 14px;
        font-family: $font-open-sans-regular;
        padding: 5px 0;
        cursor: pointer;
      }

      .ta-reply-box {
        width: 100%;
        position: relative;
        background: white;
        border: 1px solid #DCE7EC;
        border-radius: 3px;

        textarea, textarea:focus {
          width: 100%;
          resize: none;
          border: none;
          background-color: transparent;
          outline: none;

          @include placeholder() {
            color: #BCCACE !important;
            font-family: $font-open-sans-regular;
          }
        }

        &.disabled {
          background-color: #FBFBFB;
        }
      }

      .ta-vertical-bar-col {
        width: 39px;
        max-width: 39px;
        min-width: 39px;

        .ta-vertical-bar-top {
          border-right: 1.5px solid $border-default;
          margin-right: 23px;
        }

        .ta-vertical-bar-bottom {
          height: 0;
          border-top: 1.5px solid $border-default;
          margin: 0 8px 0 14.7px;
        }

        &.ta-vertical-bar-no-reply-action {
          .ta-vertical-bar-top {
            height: 50%;
          }
        }

        &.ta-vertical-bar-reply-button {
          .ta-vertical-bar-top {
            height: calc(100% - 16px) !important;
          }
        }

        &.ta-vertical-bar-reply-box {
          .ta-vertical-bar-top {
            height: calc(100% - 20px) !important;
          }
        }
      }

      .ta-star-rating {
        .ta-star-rating-value {
          font-size: 14px;
          font-family: $font-open-sans-bold;
          color: $color-text-04;
        }
      }

      .ta-yes-no-answer-value {
        font-size: 14px;
        font-family: $font-open-sans-bold;
        color: $color-text-04;
      }

      .ta-label-box {
        gap: 5px;

        .ta-label-name {
          color: $color-text-04;
          font-size: 14px;
          line-height: 15px;
          font-family: $font-open-sans-regular;
        }
      }

      .ta-document-title {
        color: $link-color;
        font-size: 13px;
        font-family: $font-open-sans-regular;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-width: 100%;
      }
    }

    .task-count-no-replies {
      color: $color-text-03;
      font-size: 14px;
      font-family: $font-open-sans-semi-bold;
    }

    ::ng-deep {
      .fa-star {
        font-size: 14px;
      }
    }
  }
}
