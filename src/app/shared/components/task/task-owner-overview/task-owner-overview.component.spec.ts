import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskOwnerOverviewComponent } from './task-owner-overview.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ViewReplyComponent', () => {
  let component: TaskOwnerOverviewComponent;
  let fixture: ComponentFixture<TaskOwnerOverviewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskOwnerOverviewComponent ],
      imports: [HttpClientTestingModule, SharedModule, RouterModule.forRoot([])],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TaskOwnerOverviewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
