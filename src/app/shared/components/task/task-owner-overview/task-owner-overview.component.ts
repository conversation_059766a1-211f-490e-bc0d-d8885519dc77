import { Component, EventEmitter, Injector, Output } from '@angular/core';
import { TaskAssignmentModel, TaskModel } from '@core';
import { TaskViewBaseComponent } from '../task-view-base/task-view-base.component';

@Component({
  selector: 'app-task-owner-overview',
  templateUrl: './task-owner-overview.component.html',
  styleUrls: ['./task-owner-overview.component.scss']
})
export class TaskOwnerOverviewComponent extends TaskViewBaseComponent {
  @Output() editTaskEvent = new EventEmitter<TaskModel>();

  isReplyingAssignmentIds: Record<number, boolean> = {};
  isSavingReply: Record<number, boolean> = {};

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
  }

  showReplyBox(ta: TaskAssignmentModel) {
    this.isReplyingAssignmentIds[ta.id] = !this.isReplyingAssignmentIds[ta.id];
  }

  /**
   * TODO need to implement this
   */
  onReplyButtonClicked(ta: TaskAssignmentModel, replyText: string) {
    if (this.isSavingReply[ta.id]) {
      return;
    }

    this.isReplyingAssignmentIds[ta.id] = false;
    this.isSavingReply[ta.id] = true;
  }

  onEditTaskEvent($event: TaskModel) {
    this.editTaskEvent.emit($event);
  }

  getTextColor(color: string): string {
    return this.tagService.getTextColor(color);
  }
}
