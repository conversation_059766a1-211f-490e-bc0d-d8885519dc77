<div class="task-short-summary-container" *ngIf="task">
  <div class="tss-view-header d-flex justify-content-between align-items-start">
    <div class="d-flex flex-column justify-content-start align-items-start">
      <div class="task-subject">{{task.subject}}</div>

      <div *ngIf="taskService.isOutgoingTask(task)" class="badge badge-status-{{task.status?.toLowerCase()}} mt-2">
        {{task.status | titlecase}}
      </div>

      <div *ngIf="taskService.isIncomingTask(task)" class="badge badge-status-{{taskService.myAssignment(task)?.status?.toLowerCase()}} mt-2">
        {{taskService.myAssignment(task)?.status | titlecase}}
      </div>
    </div>

    <div *ngIf="!isDialog && taskService.isOutgoingTask(task)" class="d-flex justify-content-end align-items-center ms-3 mb-3 tss-actions">
      <button class="btn btn-primary btn-icon" *ngIf="taskService.canEditTask(task)" (click)="editTask()" [disabled]="isUpdating"
              [ngClass]="{'me-2': taskService.canCloseTask(task) || taskService.canReopenTask(task)}"
              ngbTooltip="Edit the task" container="body">
        <i class="fa-solid fa-pen-to-square"></i>
      </button>
      <button class="btn btn-primary btn-icon" *ngIf="taskService.canCloseTask(task)" (click)="cancelTask()" [disabled]="isUpdating"
              ngbTooltip="Cancel the task" container="body">
        <i class="fa-solid fa-box-archive"></i>
      </button>
      <button class="btn btn-primary btn-icon" *ngIf="taskService.canReopenTask(task)" (click)="reopenTask()" [disabled]="isUpdating"
              ngbTooltip="Reopen the task" container="body">
        <i class="fa-solid fa-box-open"></i>
      </button>
    </div>
  </div>
  <hr class="w-100"/>

  <div class="d-flex justify-content-between">
    <div class="flex-fill pe-2" *ngIf="taskService.isOutgoingTask(task)">
      <div class="task-assignees-title">Task assignee{{task.team_users?.length > 1 ? 's' : ''}}</div>

      <app-user-avatars [users]="task.team_users" [numberDisplayedUsers]="3" [distanceBetweenAvatars]="25" avatarsTooltipPrefix="Assigned to">
      </app-user-avatars>
    </div>

    <div class="flex-fill pe-2" *ngIf="taskService.isIncomingTask(task)">
      <div class="task-assignees-title">Task owner</div>

      <app-user-avatars [users]="[task.author]" [numberDisplayedUsers]="1" avatarsTooltipPrefix="Created by">
      </app-user-avatars>
    </div>

    <div class="d-flex flex-column justify-content-start align-items-center task-created-col">
      <div class="task-created-title">Created</div>
      <div class="task-created-value">{{task.created_at + 'Z' | dateFormat: 'ShortDate'}}</div>
    </div>

    <div class="d-flex flex-column justify-content-start align-items-center task-deadline-col" *ngIf="task.deadline">
      <div class="task-deadline-title">Deadline</div>
      <div class="task-deadline-value">{{task.deadline + 'Z' | dateFormat: 'ShortDate'}}</div>
    </div>
  </div>
  <hr class="w-100"/>

  <div class="d-flex flex-column justify-content-start">
    <div class="task-description-title">Description</div>
    <div class="task-description-value mt-2">{{task.description}}</div>
  </div>
</div>
