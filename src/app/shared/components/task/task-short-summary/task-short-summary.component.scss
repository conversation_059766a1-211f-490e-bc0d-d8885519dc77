@import 'scss/layout2021/variables';
@import '../shared/style';

.task-short-summary-container {

  .task-subject {
    color: $color-text-03;
    font-size: 18px;
    font-family: $font-open-sans-bold;
    word-break: break-word;
  }

  .task-created-col {
    width: 100px;
  }

  .task-deadline-col {
    width: 100px;
  }

  .task-assignees-title, .task-created-title, .task-deadline-title, .task-description-title {
    color: $label-color;
    font-size: 14px;
  }

  .task-created-value, .task-deadline-value, .task-description-value {
    color: $color-text-04;
    font-size: 14px;
  }

  .task-description-value {
    max-height: 100px;
    overflow-y: auto;
  }

  .tss-view-header {
    .tss-actions {
      .btn-icon {
        height: 35px !important;
        width: 35px !important;
      }
    }
  }
}
