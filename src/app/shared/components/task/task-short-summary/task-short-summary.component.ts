import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { AvatarService, TaskModel, TaskService, TaskStatusEnum } from '@core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-task-short-summary',
  templateUrl: './task-short-summary.component.html',
  styleUrls: ['./task-short-summary.component.scss']
})
export class TaskShortSummaryComponent implements OnDestroy {
  @Input() task: TaskModel = {} as TaskModel;
  @Output() editTaskEvent = new EventEmitter<TaskModel>();
  @Input() isDialog: boolean = false;

  numberDisplayedUsers = 5;
  isUpdating = false;

  private subscriptions = new Subscription();

  constructor(
    public taskService: TaskService,
    public avatarService: AvatarService
  ) {
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  editTask() {
    this.editTaskEvent.emit(this.task);
  }

  reopenTask() {
    this.updateTaskStatus(TaskStatusEnum.OPEN);
  }

  cancelTask() {
    this.updateTaskStatus(TaskStatusEnum.CLOSED);
  }

  private updateTaskStatus(status: TaskStatusEnum): void {
    this.isUpdating = true;
    const updateTask$ = this.taskService.updateTask(this.task.id, {status: status})
      .pipe(
        finalize(() => this.isUpdating = false)
      )
      .subscribe({
        next: () => {
          this.task.status = status;
        }
      });
    this.subscriptions.add(updateTask$);
  }
}
