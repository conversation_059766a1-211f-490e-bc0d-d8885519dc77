import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TaskViewBaseComponent } from './task-view-base.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('ViewBaseComponent', () => {
  let component: TaskViewBaseComponent;
  let fixture: ComponentFixture<TaskViewBaseComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TaskViewBaseComponent ],
      imports: [HttpClientTestingModule, SharedModule, RouterModule.forRoot([])],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TaskViewBaseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
