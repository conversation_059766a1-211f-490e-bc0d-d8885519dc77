import { Component, Injector, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  TagService,
  AvatarService,
  DocumentService,
  Patent,
  TaskRedirectModeEnum,
  TaskResourceTypeEnum,
  UserService
} from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DocumentsBodyRequest, DocumentsQueryParams } from '@core/services/document/types';
import { BaseTaskOperatorComponent } from '../base-task-operator/base-task-operator.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-task-view-base',
  templateUrl: './task-view-base.component.html',
  styleUrls: ['./task-view-base.component.scss']
})
export class TaskViewBaseComponent extends BaseTaskOperatorComponent implements OnInit, OnDestroy {
  @Input() isDialog: boolean = false;
  @Input() showDocumentTitle: boolean = false;
  @Input() showTaskAnswerInput: boolean = true;
  numberDisplayedAssignees = 5;
  isLoadingTask = false;
  taskRedirectModeEnum = TaskRedirectModeEnum;

  avatarService: AvatarService;
  userService: UserService;
  tagService: TagService;
  activeModal: NgbActiveModal;

  protected subscriptions = new Subscription();

  private documentService: DocumentService;

  constructor(
    protected injector: Injector,
  ) {
    super(injector);
    this.avatarService = injector.get(AvatarService);
    this.userService = injector.get(UserService);
    this.tagService = injector.get(TagService);
    this.activeModal = injector.get(NgbActiveModal);
    this.documentService = injector.get(DocumentService);
  }

  ngOnInit(): void {
    this.loadTask();
    this.loadDocuments();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onCloseDialog() {
    if (this.isDialog) {
      this.activeModal.dismiss();
    }
  }

  getPublicationNumber(patent: Patent): string {
    return patent?.general?.raw_publication_number ? patent.general.raw_publication_number.replace(/-|-/gi, '') : 'N/A';
  }

  protected loadTask() {

  }

  private loadDocuments() {
    if (this.showTaskAnswerInput && this.task?.id) {
      const payload = {
        documents_ids: this.task.assignments.map((a) => a.document_id)
      } as DocumentsBodyRequest;

      const query = {
        show_analytics: 0,
        show_general: 1,
        show_bibliographic: 1,
        show_fulltext: 0,
        show_preprocessed: 0,
        page_size: 1000,
      } as DocumentsQueryParams;

      const getDocuments$ = this.documentService.getDocuments(payload, query).subscribe({
        next: ({documents, page}) => {
          this.task.assignments.forEach((a) => {
            a.document = documents.find((d) => Number(d.general.docdb_family_id) === Number(a.document_id));
          });
        }
      });
      this.subscriptions.add(getDocuments$);
    }
  }
}
