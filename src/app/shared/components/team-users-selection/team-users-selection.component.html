<ng-select [items]="teamUsers" [addTag]="false" [multiple]="isMultiple" [selectOnTab]="true"
           [hideSelected]="true" [clearable]="true" [loading]="isLoadingTeamUsers" [searchFn]="searchTeamUsersFn"
           (change)="onSelectChanged($event)" [ngClass]="{'is-invalid': isInvalid}" [readonly]="isDisabled || isLoadingTeamUsers"
           [(ngModel)]="selectedTeamUsers" [placeholder]="placeholder"
           notFoundText="No team users found" class="m-0 p-0 form-control height-auto" *ngIf="tagLayout else avatarLayout">

  <ng-template ng-option-tmp let-item="item" let-index="index">
    <div [ngbTooltip]="item | userTitle" container="body" class="d-flex justify-content-start align-items-center">
      <app-user-avatar [user]="item" class="tus-text-avatar me-2" [avatarSize]="38" [avatarFontSize]="12" ></app-user-avatar>
    </div>
  </ng-template>

  <ng-template ng-label-tmp let-item="item" let-clear="clear">
    <div class="ng-tag-value d-flex justify-content-start align-items-center" [ngbTooltip]="item | userTitle"
         container="body" *ngIf="item?.id">
      <span class="ng-value-label">{{item | userTitle}}</span>
      <span class="ng-value-icon left" (click)="clear(item)" aria-hidden="true">×</span>
    </div>
  </ng-template>

  <ng-template ng-loadingspinner-tmp>
    <img src="assets/images/octimine_blue_spinner.gif" class="me-2 loading-team-users-spinner"/>
  </ng-template>
</ng-select>

<ng-template #avatarLayout>
  <div ngbDropdown>
    <div class="form-control caret-off cursor-pointer" ngbDropdownToggle>
      <div class="d-flex justify-content-start align-items-center">
          <app-user-avatars [users]="selectedTeamUsers" [numberDisplayedUsers]="15" [distanceBetweenAvatars]="15" [avatarSize]="25" avatarsTooltipPrefix="" *ngIf="!isLoadingTeamUsers" [avatarOpacity]="0"></app-user-avatars>
          <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isLoadingTeamUsers" class="me-2 loading-team-users-spinner"/>
      </div>
      <div class="dropdown-icon"></div>
    </div>
    <div ngbDropdownMenu class="p-0">
      <div *ngIf="!isLoadingTeamUsers">
        <div class="d-flex justify-content-between align-items-center team-control-bar py-1">
          <input type="text" class="flex-fill search-team-member" placeholder="Search" aria-label="Search" [(ngModel)]="filterTerm"/>
          <div class="select-all-option">
            <label class="checkbox mb-0">
              <input type="checkbox" id="select_all" name="select_all" (change)="onSelectAll($event)"
                [checked]="isAllSelect()">
              <span class="text-green">Select all</span>
            </label>
          </div>
        </div>
        <div class="team-list">
          <div class="team-list-option d-flex" *ngFor="let u of displayedTeamUsers;">
            <div [ngbTooltip]="u | userTitle" container="body" class="d-flex justify-content-start align-items-center"
              (click)="onSelectUser(u)">
              <div class="checkbox"><input type="checkbox" [checked]="isSelectedUser(u)"
                  (click)="$event.preventDefault()" hidden><span>&nbsp;</span></div>
              <app-user-avatar [user]="u"class="tus-text-avatar-2 mr-2" [avatarSize]="38" [avatarFontSize]="12" ></app-user-avatar>
            </div>
          </div>
          <div *ngIf="displayedTeamUsers?.length === 0">
            No team users found
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
