@import 'scss/layout2021/variables';
$avatar_size_tag_layout: 38px;
$avatar_size_list_layout: 25px;

::ng-deep {
  .tus-text-avatar {
    .ta-user {
      margin-bottom: 0 !important;
      width: $avatar_size_tag_layout !important;
      max-width: $avatar_size_tag_layout !important;

      .ta-avatar {
        height: $avatar_size_tag_layout !important;
        width: $avatar_size_tag_layout !important;
        font-size: 12px !important;
      }
    }
  }
  .tus-text-avatar-2{
    .ta-user {
      margin-bottom: 0 !important;
      width: $avatar_size_list_layout !important;
      max-width: $avatar_size_list_layout !important;

      .ta-avatar {
        height: $avatar_size_list_layout !important;
        width: $avatar_size_list_layout !important;
        font-size: 12px !important;
      }
    }
    &.selected-user{
      margin-left: -15px;
    }
  }

  .loading-team-users-spinner {
    height: 24px;
  }

  .ng-select.form-control {
    padding: 0 !important;

    .ng-select-container {
      border: none !important;
    }
  }
}
.team-control-bar{
  border-bottom: 1px solid $input-border-color;
  .select-all-option{
    min-width: 100px;
    span{
      color: $brand-green;
    }
  }
  .search-team-member{
    padding: 0 1rem;
    &, &:focus, &:focus-visible{
      border: none;
      outline: none;
    }
  }
}
.team-list{
  &-option{
    cursor: pointer;
    margin: 1rem .750rem;
  }
}
