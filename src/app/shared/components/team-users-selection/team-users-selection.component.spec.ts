import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TeamUsersSelectionComponent } from './team-users-selection.component';
import { UntypedFormControl, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('TeamUsersSelectionComponent', () => {
  let component: TeamUsersSelectionComponent;
  let fixture: ComponentFixture<TeamUsersSelectionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TeamUsersSelectionComponent ],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TeamUsersSelectionComponent);
    component = fixture.componentInstance;
    component.formControl = new UntypedFormControl('test');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
