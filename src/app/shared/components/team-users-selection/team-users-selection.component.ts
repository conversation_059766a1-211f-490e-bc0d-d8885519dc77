import { Component, Injector, Input, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { Control<PERSON>ontainer, NG_VALUE_ACCESSOR } from '@angular/forms';
import {
  ControlValueAccessorComponent
} from '@shared/components/control-value-accessor/control-value-accessor.component';
import { TeamUser, UserService } from '@core';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-team-users-selection',
  templateUrl: './team-users-selection.component.html',
  styleUrls: ['./team-users-selection.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: TeamUsersSelectionComponent
    }
  ]
})
export class TeamUsersSelectionComponent extends ControlValueAccessorComponent implements OnInit, OnDestroy {

  @Input() isMultiple = false;
  @Input() tagLayout = true;
  @Input() includeMe = false;
  @Input() placeholder = 'Enter to search team users';

  teamUsers: TeamUser[] = [];
  isLoadingTeamUsers = true;
  selectedTeamUsers: TeamUser | TeamUser[] = null;
  filterTerm: string = '';

  private subscriptions = new Subscription();

  constructor(
    @Optional() protected controlContainer: ControlContainer,
    private userService: UserService
  ) {
    super(controlContainer);
  }

  ngOnInit(): void {
    this.loadTeamUsers();

    const valueChanges$ = this.control.valueChanges.subscribe({
      next: (value) => {
        this.updateSelectedTeamUsers(value);
      }
    });
    this.subscriptions.add(valueChanges$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.teamUsers = null;
    this.selectedTeamUsers = null;
  }

  searchTeamUsersFn(term: string, item: TeamUser) {
    term = term.toLocaleLowerCase();
    const title = (`${item.first_name || ' '} ${item.last_name || ' '} ${item.email || ' '}`).toLocaleLowerCase();
    return title.indexOf(term) > -1;
  }

  onSelectChanged(data: TeamUser | TeamUser[]) {
    const ids = (data instanceof Array) ? data.map(item => item.id) : [data.id];
    this.setControlValue(ids, true);
  }

  private loadTeamUsers() {
    this.isLoadingTeamUsers = true;
    const getTeamUsers$ = this.userService.getTeamUsers({load_all: 1, include_me: this.includeMe ? 1 : 0})
      .pipe(
        finalize(() => this.isLoadingTeamUsers = false)
      )
      .subscribe({
        next: ({users}) => {
          this.teamUsers = users;
          this.updateSelectedTeamUsers(this.control.value);
        }
      });
    this.subscriptions.add(getTeamUsers$);
  }

  private updateSelectedTeamUsers(value: number | number[]) {
    if (value) {
      if (this.isMultiple && value instanceof Array) {
        this.selectedTeamUsers = this.teamUsers.filter((item: TeamUser) => value.includes(item.id));
      } else {
        this.selectedTeamUsers = this.teamUsers.find((item: TeamUser) => value === item.id);
      }
    } else {
      if (this.isMultiple) {
        this.selectedTeamUsers = [];
      } else {
        this.selectedTeamUsers = null;
      }
    }
  }
  isAllSelect(): boolean{
    if(Array.isArray(this.selectedTeamUsers) && this.isMultiple){
      this.selectedTeamUsers.length === this.teamUsers.length;
    }
    return false;
  }
  isSelectedUser(user: TeamUser): boolean{
    if(this.control && this.control.value){
      return this.control.value.indexOf(user.id)> -1;
    }
    return false;
  }
  onSelectAll(event){
    event.preventDefault();
    const ids = event.target.checked ? this.teamUsers.map(item => item.id): [];
    this.setControlValue(ids, true);
  }
  onSelectUser(user: TeamUser){
    if(this.control.value.indexOf(user.id) > -1){
      const ids = this.control.value.filter(id => id!== user.id);
      this.setControlValue(ids, true);
    } else {
      const ids = [...this.control.value, user.id];
      this.setControlValue(ids, true);
    }
  }

  get displayedTeamUsers(){
    if(this.filterTerm.trim().length> 0){
      return this.teamUsers.filter(u => this.searchTeamUsersFn(this.filterTerm.trim(), u));
    }
    return this.teamUsers;
  }
}
