<div *ngIf="isDropdownMode"
     #containerEle appClickOutside (clickOutside)="showDropdown(false)"
     [excludedClasses]="['teams-selector-dropdown-mode', 'teams-popper']"
     appMouseHover hoverClass="border-contrast" [hoverDisabled]="controlDisabled"
     class="teams-selector-container teams-selector-dropdown-mode radius-sm d-flex align-items-center justify-content-start flex-wrap gap-spacing-xx-s flex-shrink-0"
     [ngClass]="{
     'border-1 p-y-spacing-xx-s p-x-spacing-sm min-h-spacing-x-lg': !onlyDisplay,
     'min-h-spacing-xx-big': onlyDisplay,
     'border-subtle figma-bg-secondary': !showingDropdown && !onlyDisplay,
     'border-contrast figma-bg-primary': showingDropdown && !onlyDisplay,
     'cursor-pointer': !isLoading && controlEnabled && !onlyDisplay
     }"
     (click)="showDropdown(true)">
  <ng-container *ngIf="!showingDropdown && !hasSelectedTeams">
    <div class="content-body-small content-color-disabled">{{ placeholder }}</div>
  </ng-container>

  <ng-container *ngFor="let team of selectedUsers"
                [ngTemplateOutlet]="userTeamItemTemplate"
                [ngTemplateOutletContext]="{team: team, isGroup: false, isExcluded: isTeamUserExcluded(team)}">
  </ng-container>

  <ng-container *ngFor="let team of selectedGroups"
                [ngTemplateOutlet]="userTeamItemTemplate"
                [ngTemplateOutletContext]="{team: team, isGroup: true, isExcluded: isTeamUserExcluded(team)}">
  </ng-container>

  <input *ngIf="showingDropdown && controlEnabled && showingInput"
         #searchInputEle appAutofocus type="text"
         [placeholder]="hasSelectedTeams ? '' : placeholder"
         class="h-spacing-xx-big border-0 figma-bg-primary p-0 flex-grow-1 content-body-small teams-search-input"
         [formControl]="searchInputFormControl"
         [disabled]="isLoading"
         (keydown)="showDropdown(true)"
         (focus)="isFocused = true"
         (blur)="isFocused = false">
</div>

<ng-container *ngIf="isInlineMode">
  <div #containerEle appClickOutside (clickOutside)="isFocused = false; searchInputFormControl.setValue('')"
       [excludedClasses]="['teams-selector-inline-mode']"
       appMouseHover hoverClass="border-contrast" [hoverDisabled]="controlDisabled"
       class="teams-selector-container teams-selector-inline-mode radius-sm d-flex align-items-center justify-content-start flex-wrap gap-spacing-xx-s m-spacing-sm border-1 p-y-spacing-x-s p-x-spacing-sm min-h-spacing-x-lg flex-shrink-0"
       [ngClass]="{
         'cursor-pointer': !isLoading,
         'border-subtle figma-bg-secondary': !isFocused,
         'border-contrast figma-bg-primary': isFocused,
         'overflow-y-auto': countSelectedTeams > 3 || isFocused
       }"
       (click)="isFocused = true">

    <div *ngIf="!hasSelectedTeams && !isFocused"
         class="content-color-disabled d-flex gap-spacing-xx-s align-items-center justify-content-start w-100"
         (click)="isFocused = true">
      <i class="fa-regular fa-search fa-1x"></i>
      <div class="content-body-small">{{ placeholder }}</div>
    </div>

    <ng-container *ngFor="let team of selectedUsers"
                  [ngTemplateOutlet]="userTeamItemTemplate"
                  [ngTemplateOutletContext]="{team: team, isGroup: false, isExcluded: isTeamUserExcluded(team)}">
    </ng-container>

    <ng-container *ngFor="let team of selectedGroups"
                  [ngTemplateOutlet]="userTeamItemTemplate"
                  [ngTemplateOutletContext]="{team: team, isGroup: true, isExcluded: isTeamUserExcluded(team)}">
    </ng-container>

    <div *ngIf="isFocused"
         class="d-flex gap-spacing-xx-s align-items-center justify-content-start w-100">
      <i *ngIf="!hasSelectedTeams" class="fa-regular fa-search fa-1x"></i>
      <input #searchInputEle appAutofocus type="text"
             [placeholder]="hasSelectedTeams ? '' : placeholder"
             class="h-spacing-xx-big border-0 figma-bg-primary p-0 flex-grow-1 content-body-small teams-search-input"
             [formControl]="searchInputFormControl"
             [disabled]="isLoading"
             (click)="isFocused = true"
             (focus)="isFocused = true">
    </div>

    <i *ngIf="hasSelectedTeams" class="fa-solid fa-circle-xmark fa-1x position-absolute cursor-pointer"
       [style.right.rem]="1" [style.top.rem]="1.5" (click)="onRemoveSelectedTeamsClicked($event)"
       ngbTooltip="Remove all selected users and teams"
       tooltipClass="white-tooltip" container="body">
    </i>
  </div>

  <div class="popover-divider"></div>

  <ng-container [ngTemplateOutlet]="teamSelectorItemsTemplate"></ng-container>
</ng-container>

<ng-template #userTeamItemTemplate let-team="team" let-isGroup="isGroup" let-isExcluded="isExcluded">
  <div class="tag-item tag-custom justify-content-start flex-shrink-0 content-style-semi-bold content-color-secondary mw-100"
       [ngClass]="getTeamItemCssClass(team)"
       (click)="showDropdown(true)"
       [style]="{'--hover-bg': '#F6F6F6A6'}"
       [ngbTooltip]="isExcluded && excludedTooltipFunc ? tooltipContent : null"
       tooltipClass="white-tooltip" container="body">
    <app-user-avatar *ngIf="!isGroup" [user]="team" [hasSubTitle]="true" subtitleMaxWidth="150px"
                     [showTooltip]="false"
                     [showTruncatableTooltip]="!(isExcluded && excludedTooltipFunc)">
    </app-user-avatar>

    <app-user-avatar *ngIf="isGroup" [user]="team" [hasSubTitle]="true" [hasCustomAvatar]="true"
                     subtitleMaxWidth="150px" [size]="onlyDisplay ? 'xxsmall' : 'xsmall'"
                     [showTooltip]="false"
                     [showTruncatableTooltip]="!(isExcluded && excludedTooltipFunc)">
      <i customAvatar class="fa-regular fa-users"></i>
    </app-user-avatar>

    <div *ngIf="(showingDropdown || isInlineMode) && isRemovable(team)" class="tag-unassign cursor-pointer">
      <i *ngIf="!isGroup" class="far fa-close" (click)="onUserRemoved($event, team)"></i>
      <i *ngIf="isGroup" class="far fa-close" (click)="onGroupRemoved($event, team)"></i>
    </div>

    <ng-template #tooltipContent>
      <div [innerHTML]="excludedTooltipFunc(team)"></div>
    </ng-template>
  </div>
</ng-template>

<ng-template #teamSelectorItemsTemplate>
  <ng-container *ngIf="!isLoading">
    <div class="team-items-container flex-grow-1 overflow-y-auto d-flex flex-column scrollbar-2024-sm">
      <div *ngIf="hasAvailableTeams" class="d-flex flex-column p-spacing-sm">
        <div class="content-heading-h6 content-color-tertiary p-y-spacing-x-s p-x-spacing-md">{{ label }}</div>

        <ng-container *ngFor="let user of availableUsers;">
          <div *ngIf="showMe && isMe(user)"
               class="tag-item tag-custom justify-content-start flex-shrink-0 mw-100 p-y-spacing-sm p-x-spacing-md border-0 h-spacing-x-lg p-l-spacing-none"
               (click)="onUserSelected($event, user)"
               [style]="{'--hover-bg': '#F6F6F6A6'}"
               [ngbTooltip]="isTeamUserExcluded(user) && excludedTooltipFunc ? tooltipContent : null"
               tooltipClass="white-tooltip" container="body">
            <app-user-avatar [user]="user" [hasSubTitle]="true"
                             class="w-100 cursor-pointer h-spacing-xx-big" [showTooltip]="false"
                             [showTruncatableTooltip]="!(isTeamUserExcluded(user) && excludedTooltipFunc)">
            </app-user-avatar>
            <ng-template #tooltipContent>
              <div [innerHTML]="excludedTooltipFunc(user)"></div>
            </ng-template>
          </div>
        </ng-container>
      </div>

      <ng-container *ngIf="!hasAvailableTeams">
        <ng-container *ngIf="notFoundTemplate">
          <ng-container [ngTemplateOutlet]="notFoundTemplate"></ng-container>
        </ng-container>

        <ng-container *ngIf="!notFoundTemplate">
          <div class="d-flex flex-column p-spacing-md gap-spacing-sm">
            <div class="content-heading-h6">No matches found.</div>
            <div class="content-body-small content-color-secondary">
              To add new users or teams go to <a href="/users" target="_blank" class="content-color-info" appMouseHover
                                                 hoverClass="text-decoration-underline">"Workspace / Members’ admin"</a>.
            </div>
          </div>
        </ng-container>
      </ng-container>

      <div *ngIf="showMe && hasAvailableTeams && isMeAvailable" class="popover-divider m-b-spacing-sm"></div>

      <div *ngIf="hasAvailableTeams" id="team-list-container"
           appScrollbarDetector scrollbarCss="m-r-spacing-xx-s" removingScrollbarCss="p-r-spacing-sm"
           class="flex-grow-1 overflow-y-auto d-flex flex-column p-l-spacing-sm p-r-spacing-sm m-b-spacing-sm"
           (scroll)="onScroll($event)">
        <ng-container *ngIf="!teamsPopper || teamsPopper.isOpen">
          <ng-container *ngFor="let user of displayedTeamUsers;">
            <div *ngIf="!isMe(user) && !isGroupType(user)"
                 class="tag-item tag-custom justify-content-start flex-shrink-0 mw-100 p-y-spacing-sm p-x-spacing-md border-0 h-spacing-x-lg p-l-spacing-none"
                 (click)="onUserSelected($event, user)"
                 [style]="{'--hover-bg': '#F6F6F6A6'}"
                 [ngbTooltip]="isTeamUserExcluded(user) && excludedTooltipFunc ? tooltipContent : null"
                 tooltipClass="white-tooltip" container="body">
              <app-user-avatar [user]="user" [hasSubTitle]="true"
                               class="w-100 cursor-pointer h-spacing-xx-big" [showTooltip]="false"
                               [showTruncatableTooltip]="!(isTeamUserExcluded(user) && excludedTooltipFunc)">
              </app-user-avatar>
              <ng-template #tooltipContent>
                <div [innerHTML]="excludedTooltipFunc(user)"></div>
              </ng-template>
            </div>

            <div *ngIf="isGroupType(user)"
                 class="tag-item tag-custom justify-content-start flex-shrink-0 mw-100 p-y-spacing-sm p-x-spacing-md border-0 h-spacing-x-lg p-l-spacing-none"
                 (click)="onGroupSelected($event, user)"
                 [style]="{'--hover-bg': '#F6F6F6A6'}"
                 [ngbTooltip]="isTeamUserExcluded(user) && excludedTooltipFunc ? tooltipContent : null"
                 tooltipClass="white-tooltip" container="body">
              <app-user-avatar [user]="user" [hasSubTitle]="true" [hasCustomAvatar]="true"
                               class="w-100 cursor-pointer h-spacing-xx-big" [showTooltip]="false"
                               [showTruncatableTooltip]="!(isTeamUserExcluded(user) && excludedTooltipFunc)">
                <i customAvatar class="fa-regular fa-users"></i>
              </app-user-avatar>
              <ng-template #tooltipContent>
                <div [innerHTML]="excludedTooltipFunc(user)"></div>
              </ng-template>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>

    <ng-container *ngIf="showFooter">
      <div class="popover-divider"></div>

      <div class="d-flex align-items-start justify-content-start gap-spacing-xx-s p-spacing-md">
        <ng-container *ngIf="footerTemplate">
          <ng-container [ngTemplateOutlet]="footerTemplate"></ng-container>
        </ng-container>

        <ng-container *ngIf="!footerTemplate">
          <div class="teams-select-footer-icon">
            <i class="fa-light fa-info-circle"></i>
          </div>
          <div class="content-body-xsmall text-left content-color-tertiary">
            By creating teams, any member has the right to rate a patent on behalf of the team.
            You can set up your teams <a href="/groups" target="_blank" class="content-color-info" appMouseHover
                                         hoverClass="text-decoration-underline">here</a>.
          </div>
        </ng-container>
      </div>
    </ng-container>
  </ng-container>

  <app-loading-dropdown *ngIf="isLoading" text="Users and teams are loading..."></app-loading-dropdown>
</ng-template>

<app-popper *ngIf="isDropdownMode"
            #teamsPopper placement="bottom" [showArrow]="false" [resizePopperWidth]="true"
            customClass="teams-popper p-spacing-none"
            (popperOpened)="onPopperOpened()"
            (popperClosed)="onPopperClosed()">
  <ng-container [ngTemplateOutlet]="teamSelectorItemsTemplate"></ng-container>
</app-popper>
