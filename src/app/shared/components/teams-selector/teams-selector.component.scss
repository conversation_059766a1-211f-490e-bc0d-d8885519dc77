@import 'scss/figma2023/index';
@import "scss/components/tag_item";

.teams-selector-container {
  .teams-search-input {
    &::placeholder {
      @include add-properties(map-get(map-get($typography, 'body'), 'small'), false);
    }

    &:focus {
      -webkit-box-shadow: none;
      box-shadow: none;
      outline: none;
    }
  }

  &.teams-selector-inline-mode {
    max-height: 115px;
  }
}

::ng-deep {
  .teams-popper {
    z-index: 9999;

    .fa-book-open-cover {
      color: $colour-blue-brand-500;
      background: $colour-blue-brand-200;
      --fa-border-radius: 50%;
      --fa-border-width: 1rem;
      --fa-border-color: #E0F7FF;
      --fa-border-padding: 1rem;
    }

    .popper-inner {
      height: 100% !important;

      .team-items-container {
        max-height: 18.5rem;
        overflow-x: hidden;
      }
    }
  }
}
