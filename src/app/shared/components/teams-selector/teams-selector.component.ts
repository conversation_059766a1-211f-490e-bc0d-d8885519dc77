import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { ControlContainer, FormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { PopperComponent } from '@shared/components/popper/popper.component';
import { Assignee, AssigneeTypeEnum, GroupService, TeamUser, TeamUserTypeEnum, UserProfile, UserService } from '@core';
import { BehaviorSubject, concatMap, debounceTime, filter, Observable, of, Subscription } from 'rxjs';
import { finalize, map, tap } from 'rxjs/operators';
import { ControlValueAccessorComponent } from '@shared/components';
import { differenceWith, isEqual } from 'lodash';

@Component({
  selector: 'app-teams-selector',
  templateUrl: './teams-selector.component.html',
  styleUrls: ['./teams-selector.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: TeamsSelectorComponent
    }
  ]
})
export class TeamsSelectorComponent extends ControlValueAccessorComponent implements OnInit, OnDestroy {
  @Input() label: string = 'Select an user or group';
  @Input() placeholder: string = 'Search for a person or team...';
  @Input() multiple: boolean = true;
  @Input() showUsers: boolean = true;
  @Input() showGroups: boolean = true;
  @Input() showMe: boolean = false;
  @Input() onlyDisplay: boolean = false;
  @Input() isSystemGroupRemovable: boolean = true;
  @Input() emitTeamsAfterInitial: boolean = false;
  @Input() displayMode: 'dropdown' | 'inline' = 'dropdown';
  @Input() showFooter: boolean = true;
  @Input() footerTemplate: TemplateRef<any> = null;
  @Input() notFoundTemplate: TemplateRef<any> = null;
  @Input() teams: TeamUser[] = [];
  @Input() excludedTeams: TeamUser[] = [];
  @Input() excludedTooltipFunc: (team: TeamUser) => string = null;
  @Input() initialTeams: TeamUser[] = [];

  @Output() teamsChange: EventEmitter<Array<TeamUser>> = new EventEmitter();

  @ViewChild('containerEle') containerEle: ElementRef<HTMLDivElement>;
  @ViewChild('searchInputEle', {read: ElementRef, static: false}) searchInputEle: ElementRef<HTMLInputElement>;
  @ViewChild('teamsPopper') teamsPopper: PopperComponent;

  selectedUsers: TeamUser[] = [];
  selectedGroups: TeamUser[] = [];

  showingDropdown: boolean = false;
  showingDropdownInProgress: boolean = false;
  showingInput: boolean = true;

  allUsers: TeamUser[] = [];
  availableUsers: TeamUser[] = [];
  userPage = 1;

  allGroups: TeamUser[] = [];
  availableGroups: TeamUser[] = [];
  groupPage = 1;

  isLoading: boolean = false;
  displayedTeamUsers: TeamUser[] = [];

  searchInputFormControl = new FormControl(null);

  isFocused: boolean = false;

  private isLoadingTeamsExecuted = false;
  private isControlEnabledByDefault: boolean = true;
  private subscriptions = new Subscription();
  private lastScrollTop = 0;
  private readonly PAGE_SIZE = 10;
  private scrollBehaviorSubject = new BehaviorSubject<boolean>(false);


  constructor(
    @Optional() protected controlContainer: ControlContainer,
    private groupService: GroupService,
    private userService: UserService
  ) {
    super(controlContainer);
  }

  get countSelectedTeams(): number {
    return this.selectedUsers.length + this.selectedGroups.length;
  }

  get hasSelectedTeams(): boolean {
    return this.selectedUsers?.length > 0 || this.selectedGroups?.length > 0;
  }

  get hasAvailableTeams(): boolean {
    return this.displayedTeamUsers?.length > 0;
  }

  get searchInputValue(): string {
    return this.searchInputFormControl.value?.trim() || '';
  }

  get myProfile(): UserProfile {
    return this.userService.getUser().profile;
  }

  get isInlineMode(): boolean {
    return this.displayMode === 'inline';
  }

  get isDropdownMode(): boolean {
    return this.displayMode === 'dropdown';
  }

  get isMeAvailable(): boolean {
    return this.availableUsers.some(user => user.id === this.myProfile.id) && (
      this.availableUsers.length > 1 || this.availableGroups.length > 0
    );
  }

  ngOnInit(): void {
    this.isControlEnabledByDefault = this.controlEnabled;

    const valueChanges$ = this.searchInputFormControl.valueChanges
      .pipe(
        filter((val) => !this.isLoading),
        map(value => value?.trim()?.toLowerCase() || ''),
        tap((value) => {
          this.updateAvailableUsers();
          this.updateAvailableGroups();
          this.userPage = 1;
          this.groupPage = 1;
          this.displayedTeamUsers = [];
          this.filterAvailableUsers(value);
          this.filterAvailableGroups(value);
        })
      )
      .subscribe();
    this.subscriptions.add(valueChanges$);

    const scrollBehavior$ = this.scrollBehaviorSubject.asObservable()
      .pipe(
        filter((val) => val && !this.isLoading),
        debounceTime(500)
      )
      .subscribe({
        next: () => {
          if (this.availableUsers.length > this.userPage * this.PAGE_SIZE) {
            this.userPage++;
            this.updateDisplayedUsers();
          }

          if (this.availableGroups.length > this.groupPage * this.PAGE_SIZE) {
            this.groupPage++;
            this.updateDisplayedGroups();
          }
        }
      });
    this.subscriptions.add(scrollBehavior$);

    if (this.hasFormControl) {
      const controlValueChanges$ = this.control.valueChanges.subscribe({
        next: (val) => {
          this.initializeSelectedTeamUsers();
        }
      });
      this.subscriptions.add(controlValueChanges$);
    }

    if (this.initialTeams?.length) {
      this.allUsers = this.getOnlyDisplayUsers();
      this.allGroups = this.getOnlyDisplayGroups();
      this.initializeSelectedTeamUsers();
    } else if (!this.isLoadingTeamsExecuted || this.isInlineMode) {
      this.loadTeams();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  showDropdown(val: boolean) {
    if (this.controlDisabled || this.onlyDisplay || this.isInlineMode) {
      return;
    }

    this.showingDropdown = val;
    this.control.markAsTouched();

    if (this.showingDropdown) {
      this.showingDropdownInProgress = true;
      this.showPopper();

      if (!this.allUsers?.length && !this.allGroups?.length) {
        this.loadTeams();
      }

      this.focusSearchInput();
    } else {
      this.hidePopper();
    }
  }

  onUserRemoved(event: MouseEvent, user: TeamUser) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.multiple) {
      this.selectedUsers = this.selectedUsers.filter(t => t.id !== user.id);
    } else {
      this.selectedUsers = [];
    }

    if (this.isInlineMode) {
      this.isFocused = false;
    }

    this.updateAvailableUsers();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onUserSelected(event: MouseEvent, user: TeamUser) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.multiple) {
      this.selectedUsers.push(user);
    } else {
      this.selectedUsers = [user];
      this.selectedGroups = [];
    }

    this.updateAvailableUsers();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onGroupRemoved(event: MouseEvent, group: TeamUser) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.multiple) {
      this.selectedGroups = this.selectedGroups.filter(t => t.id !== group.id);
    } else {
      this.selectedGroups = [];
    }

    if (this.isInlineMode) {
      this.isFocused = false;
    }

    this.updateAvailableGroups();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onGroupSelected(event: MouseEvent, group: TeamUser) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.multiple) {
      this.selectedGroups.push(group);
    } else {
      this.selectedGroups = [group];
      this.selectedUsers = [];
    }

    this.updateAvailableGroups();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  isMe(user: TeamUser): boolean {
    return user.id === this.myProfile.id;
  }

  isGroupType(t: TeamUser): boolean {
    return t.type === TeamUserTypeEnum.GROUP;
  }

  getTeamItemCssClass(team: TeamUser) {
    const css = [];
    const isRemovable = this.isRemovable(team);

    if (this.controlDisabled || this.onlyDisplay || !isRemovable) {
      css.push('cursor-default');
    }

    const borderStyle = isRemovable ? 'border-1 border-bold' : 'border-1 border-subtle';

    if (this.onlyDisplay) {
      css.push(`h-spacing-xx-big p-x-spacing-x-s p-y-spacing-xxx-s ${borderStyle}`);
    } else {
      css.push('h-spacing-xxx-big p-l-spacing-none');

      if (this.showingDropdown || this.isFocused || this.isInlineMode) {
        css.push(`border-1 p-l-spacing-xx-s ${borderStyle}`);
      }
    }

    return css.join(' ');
  }

  isTeamUserExcluded(team: TeamUser): boolean {
    return (this.excludedTeams || []).some(t => t.id === team.id);
  }

  reset() {
    this.selectedUsers = [];
    this.selectedGroups = [];
    this.teams = [];
    this.teamsChange.emit([]);
    this.updateControlValue();
    this.resetInternalValues();
  }

  onRemoveSelectedTeamsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopImmediatePropagation();
    this.reset();
  }

  onScroll(event: Event) {
    const element = event.target as HTMLElement;
    const remainingScrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight;
    this.scrollBehaviorSubject.next(element.scrollTop > this.lastScrollTop && remainingScrollDistance < 20);
    this.lastScrollTop = element.scrollTop;
  }

  onPopperOpened() {
    this.scrollListToTop(10);
  }

  onPopperClosed() {
    this.resetInternalValues();
  }

  isRemovable(t: TeamUser): boolean {
    if (this.isSystemGroupRemovable || t.type === TeamUserTypeEnum.USER) {
      return true;
    }
    return !t.is_system;
  }

  private resetInternalValues() {
    this.isLoading = false;
    this.displayedTeamUsers = [];
    this.userPage = 1;
    this.groupPage = 1;
    this.updateAvailableUsers();
    this.updateAvailableGroups();
    this.lastScrollTop = 0;
  }

  private updateControlValue() {
    if (this.hasFormControl) {
      const users = this.selectedUsers.map(user => ({id: user.id, type: AssigneeTypeEnum.USER}));
      const groups = this.selectedGroups.map(user => ({id: user.id, type: AssigneeTypeEnum.GROUP}));
      const teams = users.concat(groups);
      const isDifferent = differenceWith(this.controlValue, teams, isEqual).length !== 0 ||
        differenceWith(teams, this.controlValue, isEqual).length !== 0;
      if (isDifferent) {
        this.setControlValue(teams, true, true);
        this.emitTeamsChanged();
      }
    } else {
      this.emitTeamsChanged();
    }
  }

  private initializeSelectedTeamUsers() {
    this.initializeUsers();
    this.initializeGroups();
  }

  private emitTeamsChanged() {
    this.teamsChange.emit(this.selectedUsers.concat(this.selectedGroups));
  }

  private showPopper() {
    this.teamsPopper?.show(this.containerEle.nativeElement);
  }

  private hidePopper() {
    this.teamsPopper?.hide();
    this.searchInputFormControl.setValue('');
  }

  private loadTeams() {
    this.disableControl();
    this.isLoading = true;
    this.subscriptions.add(
      this.getUsers()
        .pipe(
          concatMap(() => this.getGroups()),
          finalize(() => {
            this.isLoadingTeamsExecuted = true;
            this.isLoading = false;
            this.enableControl(this.isControlEnabledByDefault);
            this.scrollListToTop();
            if (this.emitTeamsAfterInitial) {
              this.emitTeamsChanged();
            }
          })
        )
        .subscribe()
    );
  }

  private getUsers(): Observable<TeamUser[]> {
    if (!this.showUsers) {
      return of([]);
    }

    return this.userService.getTeamUsers({load_all: 1, include_me: 1})
      .pipe(
        map(({users}) => this.sortUsersByMe(users).map(u => {
          return {...u, type: TeamUserTypeEnum.USER} as TeamUser;
        })),
        tap(users => this.allUsers = users),
        tap(() => this.initializeUsers()),
        tap(() => this.updateAvailableUsers()),
        tap(() => this.filterAvailableUsers(this.searchInputValue))
      );
  }

  private sortUsersByMe(users: TeamUser[]): TeamUser[] {
    const me = users.find(user => user.id === this.myProfile.id);
    if (me) {
      if (!me.last_name) {
        me.last_name = '';
      }
      if (!me.last_name?.includes('(you)')) {
        me.last_name += ' (you)';
        me.last_name = me.last_name.trim();
      }
      const remainingUsers = users.filter(user => user.id !== this.myProfile.id);
      return this.showMe ? [me, ...remainingUsers] : remainingUsers;
    }
    return users;
  }

  private getGroups(): Observable<TeamUser[]> {
    if (!this.showGroups) {
      return of([]);
    }

    return this.groupService.getGroups({load_all: 1})
      .pipe(
        map(({groups}) => groups.map(g => {
          return {...g, first_name: g.name || '', type: TeamUserTypeEnum.GROUP} as any as TeamUser;
        })),
        tap(groups => this.allGroups = groups),
        tap(() => this.initializeGroups()),
        tap(() => this.updateAvailableGroups()),
        tap(() => this.filterAvailableGroups(this.searchInputValue))
      );
  }

  private getControlValueUsers(): Assignee[] {
    return (this.controlValue || []).filter((v: Assignee) => v.type === AssigneeTypeEnum.USER);
  }

  private getControlValueGroups(): Assignee[] {
    return (this.controlValue || []).filter((v: Assignee) => v.type === AssigneeTypeEnum.GROUP);
  }

  private getOnlyDisplayUsers(): TeamUser[] {
    return this.initialTeams.filter((v: TeamUser) => v.type === TeamUserTypeEnum.USER);
  }

  private getOnlyDisplayGroups(): TeamUser[] {
    return this.initialTeams.filter((v: TeamUser) => v.type === TeamUserTypeEnum.GROUP);
  }

  private initializeUsers() {
    const userIds = this.getControlValueUsers().map((u: Assignee) => u.id);
    this.selectedUsers = this.sortUsersByMe(this.allUsers.filter(user => userIds.includes(user.id)));
    this.updateAvailableUsers();
  }

  private initializeGroups() {
    const groupIds = this.getControlValueGroups().map((u: Assignee) => u.id);
    this.selectedGroups = this.allGroups.filter(group => groupIds.includes(group.id));
    this.updateAvailableGroups();
    this.sortSelectedGroups();
  }

  private updateAvailableUsers() {
    const userIds = this.selectedUsers.map(team => team.id);
    this.availableUsers = this.allUsers.filter(team => !userIds.includes(team.id));
    this.updateDisplayedUsers();
  }

  private updateAvailableGroups() {
    const groupIds = this.selectedGroups.map(group => group.id);
    this.availableGroups = this.allGroups.filter(group => !groupIds.includes(group.id));
    this.updateDisplayedGroups();
  }

  private sortSelectedGroups() {
    this.selectedGroups.sort((a, b) => {
      return (b.is_system ? 1 : 0) - (a.is_system ? 1 : 0);
    });
  }

  private filterAvailableUsers(term: string) {
    if (term?.trim()?.length > 0) {
      term = term.trim().toLowerCase();
      this.availableUsers = this.availableUsers.filter(user => this.matchTeamUser(user, term));
    }

    this.updateDisplayedUsers();
    this.scrollListToTop();
  }

  private filterAvailableGroups(term: string) {
    if (term?.trim()?.length > 0) {
      term = term.trim().toLowerCase();
      this.availableGroups = this.availableGroups.filter(group => this.matchTeamUser(group, term));
    }

    this.updateDisplayedGroups();
    this.scrollListToTop();
  }

  private updateDisplayedUsers() {
    const start = (this.userPage - 1) * this.PAGE_SIZE;
    const end = this.userPage * this.PAGE_SIZE;
    const existingUserIds = this.displayedTeamUsers.filter(t => t.type === TeamUserTypeEnum.USER).map(t => t.id);
    const users = this.availableUsers.slice(start, end).filter(t => !existingUserIds.includes(t.id));
    this.displayedTeamUsers.push(...users);
  }

  private updateDisplayedGroups() {
    const start = (this.groupPage - 1) * this.PAGE_SIZE;
    const end = this.groupPage * this.PAGE_SIZE;
    const existingGroupIds = this.displayedTeamUsers.filter(t => t.type === TeamUserTypeEnum.GROUP).map(t => t.id);
    const groups = this.availableGroups.slice(start, end).filter(t => !existingGroupIds.includes(t.id));
    this.displayedTeamUsers.push(...groups);
  }

  private matchTeamUser(user: TeamUser, term: string): boolean {
    if (user.first_name || user.last_name) {
      const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim().toLowerCase();
      if (fullName.includes(term)) {
        return true;
      }
    }

    if (user['name'] && user['name'].toLowerCase().includes(term)) {
      return true;
    }

    return user.email?.toLowerCase()?.includes(term);
  }

  private focusSearchInput() {
    setTimeout(() => {
      if (this.showingDropdown || (this.isInlineMode && this.isFocused)) {
        this.searchInputEle?.nativeElement?.focus();
        this.showingDropdownInProgress = false;
      } else {
        this.searchInputEle?.nativeElement?.blur();
      }
    }, 100);
  }

  private updateInputDisplay(callbackFunc: () => void) {
    if (this.isInlineMode) {
      callbackFunc();
    } else {
      this.showingInput = false;
      setTimeout(() => {
        this.showingInput = true;
        setTimeout(() => callbackFunc(), 50);
      }, 50);
    }
  }

  private scrollListToTop(timeout = 500) {
    if (this.userPage === 1 && this.groupPage === 1 && !this.isLoading) {
      setTimeout(() => {
        const ele = document.getElementById('team-list-container');
        if (ele) {
          ele.scrollTop = 0;
        }
      }, timeout);
    }
  }
}
