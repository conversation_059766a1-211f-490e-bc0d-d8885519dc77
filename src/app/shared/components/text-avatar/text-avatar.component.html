<div *ngIf="user" class="ta-user">
  <div *ngIf="avatarData" class="ta-avatar" [ngStyle]="{'background-image': 'url(' + avatarData + ')'}"></div>
  <div *ngIf="!avatarData" class="ta-avatar" [ngClass]="{'more-user': isMoreUsers}">{{getUserAvatarTitle()}}</div>
  <div *ngIf="hasSubTitle && (user.first_name || user.last_name)" [ngbTooltip]="user | userTitle"
       class="d-flex flex-column justify-content-center align-items-center">
    <div *ngIf="user.first_name" class="ta-title">{{user.first_name}}</div>
    <div *ngIf="user.last_name" class="ta-title">{{user.last_name}}</div>
  </div>
  <div *ngIf="hasSubTitle && !user.first_name && !user.last_name" class="ta-title"
       [ngbTooltip]="user.email">{{user.email}}</div>
  <div *ngIf="canRemove" class="ta-user-action-icon" (click)="onRemoveTeamUserClicked()"
       ngbTooltip="Unshare this user"><i class="far fa-times-circle"></i></div>
  <div *ngIf="canReshare" class="ta-user-action-icon" (click)="onReshareTeamUserClicked()"
     ngbTooltip="Share this user again"><i class="fas fa-plus-circle"></i></div>
</div>

<ng-content></ng-content>
