@import 'scss/layout2021/variables';

.ta-user {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 10px;
  width: 98px;
  max-width: 98px;
  position: relative;

  &:not(:last-child) {
    margin-right: 10px;
  }

  .ta-avatar {
    height: 80px;
    width: 80px;
    background: $brand-green;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    text-align: center;
    padding: 0;
    margin: 0;
    border-radius: 50%;
    font-size: 35px;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
  }

  .ta-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 95px;
  }

  .ta-user-action-icon {
    position: absolute;
    color: $brand-green;
    right: 0;
    top: 0;
    display: none;
    cursor: pointer;
  }

  &:hover {
    .ta-user-action-icon {
      display: block;
    }
  }
}
