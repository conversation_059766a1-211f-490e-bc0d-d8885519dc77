import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UserService } from '@core/services';
import { TeamUser } from '@core/models';
import { Subscription } from 'rxjs';

/**
* @deprecated this component will be removed, use app-user-avatar component instead
*/
@Component({
  selector: 'app-text-avatar',
  templateUrl: './text-avatar.component.html',
  styleUrls: ['./text-avatar.component.scss']
})
export class TextAvatarComponent implements OnInit, OnChanges, OnDestroy {
  @Input() user: TeamUser;
  @Input() canRemove = false;
  @Input() canReshare = false;
  @Input() hasSubTitle = true;
  @Input() avatarBlob = null;
  @Input() refreshAvatar = false;

  @Output() removeTeamUser = new EventEmitter<TeamUser>(true);
  @Output() reshareTeamUser = new EventEmitter<TeamUser>(true);
  @Output() hasAvatar = new EventEmitter<boolean>(true);

  avatarData = null;

  private subscriptions = new Subscription();

  constructor(
    private userService: UserService
  ) {
  }

  get isMoreUsers(): boolean {
    return this.user ? this.user['more_users'] : false;
  }

  get isUserData(): boolean {
    if (this.user) {
      const attributes = Object.keys(this.user);
      return ['id', 'email'].every((v) => attributes.includes(v));
    }

    return false;
  }

  ngOnInit(): void {
    this.getUserAvatar();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.avatarBlob) {
      if (changes.avatarBlob.currentValue) {
        this.avatarData = this.createImageUrl(changes.avatarBlob.currentValue);
        this.hasAvatar.emit(true);
      } else {
        this.avatarData = null;
        this.hasAvatar.emit(false);
      }
    }

    if (changes.refreshAvatar?.currentValue) {
      if (this.avatarBlob) {
        this.avatarData = this.createImageUrl(this.avatarBlob);
        this.hasAvatar.emit(true);
      } else {
        this.getUserAvatar();
      }
    }
  }

  getUserAvatar() {
    if (!this.avatarBlob && this.isUserData) {
      if (this.user?.id) {
        const getAvatar$ = this.userService.getAvatar(this.user?.id).subscribe({
          next: (val) => {
            this.avatarData = this.createImageUrl(val);
            this.hasAvatar.emit(true);
          },
          error: error => {
            this.avatarData = null;
            this.hasAvatar.emit(false);
          }
        });
        this.subscriptions.add(getAvatar$);
      } else {
        this.avatarData = null;
        this.hasAvatar.emit(false);
      }
    }
  }

  getUserAvatarTitle(): string {
    if (this.isMoreUsers) {
      return this.user.first_name + this.user.last_name;
    }

    if (this.user.first_name || this.user.last_name) {
      return (this.user.first_name ? this.user.first_name[0] : '') + (this.user.last_name ? this.user.last_name[0] : '');
    }

    if (this.user['name']) {
      const nameParts = this.user['name'].split(' ').filter((n) => n && n.trim().length > 0);
      return (nameParts.length > 0 ? nameParts[0][0] : '') + (nameParts.length > 1 ? nameParts[nameParts.length - 1][0] : '');
    }

    return this.user.email ? `${this.user.email[0]}${this.user.email[1]}` : 'UN';
  }

  onRemoveTeamUserClicked() {
    this.removeTeamUser.emit(this.user);
  }

  onReshareTeamUserClicked() {
    this.reshareTeamUser.emit(this.user);
  }

  private createImageUrl(data: Blob | File): string {
    const creator = window.URL || window.webkitURL;
    return creator.createObjectURL(data);
  }
}
