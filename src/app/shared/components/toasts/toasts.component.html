<ngb-toast class="toast-container m-t-spacing-md"
           *ngFor="let toast of toastService.toasts"
           [ngClass]="'toast-' + toast.type + ' ' + (toast.classname || '')"
           [autohide]="getAutohide(toast)"
           [delay]="toast.delay || 5000"
           (hidden)="toastService.remove(toast)">
  <div class="toast-item d-flex align-items-baseline justify-content-around p-0 m-0">
    <div class="toast-icon fa-solid" *ngIf="toast.type !== toastTypeEnum.DEFAULT"
         [ngClass]="getIconClass(toast)"></div>

    <div class="toast-content d-flex flex-column align-items-start justify-content-start">
      <div class="toast-title content-heading-h6" *ngIf="toast.header?.length">{{ toast.header }}</div>
      <div class="toast-body-text content-body-small" *ngIf="toast.body?.length"
           [innerHTML]="toast.body">
      </div>
      <div class="toast-link button-info-ghost p-spacing-none m-spacing-none" *ngIf="toast.link" (click)="openToastLink(toast)"
           [innerHTML]="toast.link.text">
      </div>
    </div>

    <div class="toast-close fa-light fa-xmark"
         *ngIf="toast.closable !== undefined ? toast.closable : true"
         (click)="onCloseClicked(toast)">
    </div>
  </div>
</ngb-toast>
