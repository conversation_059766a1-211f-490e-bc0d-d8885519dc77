@use 'scss/figma2023/index' as variables;
@import 'scss/layout2021/mixins';

$toast-types: (
  info: (variables.$colour-info-300, variables.$colour-info-600),
  success: (variables.$colour-green-300, variables.$colour-green-500),
  warning: (variables.$colour-yellow-300, variables.$colour-yellow-800),
  error: (variables.$colour-red-300, variables.$colour-red-500)
);

:host {
  z-index: 9999;
  position: fixed;
  top: 0;
  right: 0;
  width: 21.75rem;

  ::ng-deep {
    .toast-container {
      z-index: 9999;
      position: relative;

      .toast-body {
        padding: variables.$spacing-system-spacing-md variables.$spacing-system-spacing-md variables.$spacing-system-spacing-md variables.$spacing-system-spacing-big !important;
        background: variables.$colour-base-white;
        color: variables.$colours-content-content-primary;

        .toast-item {
          gap: variables.$spacing-system-spacing-sm;

          .toast-icon {
            font-size: 1rem;
            padding: variables.$spacing-system-spacing-xx-s;
          }

          .toast-content {
            gap: variables.$spacing-system-spacing-sm;

            .toast-link {
              pointer-events: all;
              cursor: pointer;
            }
          }

          .toast-close {
            font-size: 1rem;
            padding: variables.$spacing-system-spacing-xx-s;
            pointer-events: all;
            cursor: pointer;
          }
        }
      }

      @each $type, $colors in $toast-types {
        &.toast-#{$type} {
          .toast-body {
            border-left: 0.25rem solid nth($colors, 1);
            @include border-left-radius(variables.$spacing-system-spacing-xx-s);
          }

          .toast-icon {
            color: nth($colors, 2);
          }
        }
      }
    }
  }
}
