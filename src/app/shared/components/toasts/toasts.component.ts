import { Component } from '@angular/core';
import { ToastInfo, ToastService, ToastTypeEnum } from '@core/services';
import { Router } from '@angular/router';

@Component({
  selector: 'app-toasts',
  templateUrl: './toasts.component.html',
  styleUrls: ['./toasts.component.scss']
})
export class ToastsComponent {
  toastTypeEnum = ToastTypeEnum;

  constructor(
    public toastService: ToastService,
    private router: Router
  ) {
  }

  getIconClass(toast: ToastInfo): string {
    switch (toast.type) {
      case ToastTypeEnum.INFO:
        return 'fa-info-circle';
      case ToastTypeEnum.SUCCESS:
        return 'fa-check-circle';
      case ToastTypeEnum.ERROR:
        return 'fa-circle-xmark';
      case ToastTypeEnum.WARNING:
        return 'fa-triangle-exclamation';
      default:
        return '';
    }
  }

  openToastLink(toast: ToastInfo) {
    if (toast.link) {
      if (toast.link.new_tab_url) {
        window.open(toast.link.new_tab_url, '_blank');
        this.onCloseClicked(toast);
      }

      if (toast.link.current_tab_params) {
        this.router.navigate(toast.link.current_tab_params.commands, toast.link.current_tab_params.extras).then();
        this.onCloseClicked(toast);
      }
    }
  }

  onCloseClicked(toast: ToastInfo) {
    this.toastService.remove(toast);
  }

  getAutohide(toast: any): boolean {
    return typeof toast.autohide !== 'undefined' ? toast.autohide : true;
  }
}
