<div class="d-flex justify-content-{{labelPosition}} align-items-{{labelAlign}}" *ngIf="label else tooltipContent">
  <label class="tooltip-label" [ngClass]="labelClass" [innerHTML]="label"></label>
  <div>
    <ng-container [ngTemplateOutlet]="tooltipContent"></ng-container>
  </div>
</div>

<ng-template #tooltipContent>
  <div *ngIf="!showIcon" (click)="openTooltip($event)" #info="ngbPopover"
    [ngbPopover]="ngbContent" popoverClass="info-popover" triggers="manual" [autoClose]="'outside'"
    appSelectableTooltip [selectableTooltipPopover]="info">
    <ng-content></ng-content>
  </div>

  <i *ngIf="showIcon && tooltipClickable" class="fa-regular fa-circle-info tooltip-icon" (click)="openTooltip($event)" #info="ngbPopover"
    [ngClass]="'tooltip-icon-' + tooltipIconSize + ' ' + classPosition + (tooltipClickable ? ' cursor-pointer' : '')"
    [ngbPopover]="ngbContent" popoverClass="info-popover" triggers="manual" [autoClose]="'outside'"
    appSelectableTooltip [selectableTooltipPopover]="info" [placement]="tooltipPosition"></i>

  <i *ngIf="showIcon && !tooltipClickable" class="fa-regular fa-circle-info tooltip-icon"
     [ngClass]="'tooltip-icon-' + tooltipIconSize + ' ' + classPosition + (tooltipClickable ? ' cursor-pointer' : '')"
     [ngbTooltip]="ngbContent" tooltipClass="info-popover" triggers="hover" [placement]="tooltipPosition"></i>
</ng-template>

<ng-template #ngbContent>
  <div class="content-body-xsmall content-color-secondary"
       [innerHTML]="tooltipText" [ngClass]="tooltipTextClass">
  </div>
</ng-template>
