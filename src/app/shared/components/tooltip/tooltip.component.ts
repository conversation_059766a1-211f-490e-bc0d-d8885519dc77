import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-tooltip',
  templateUrl: './tooltip.component.html',
  styleUrls: ['./tooltip.component.scss']
})
export class TooltipComponent {

  @Input() id: string;
  @Input() tooltipTitle: string;
  @Input() tooltipText: string;
  @Input() tooltipTextClass: string = '';
  @Input() popupText: string;
  @Input() tooltipPosition:
    'right' | 'left' | 'top' | 'top-right' | 'top-left' | 'bottom' | 'bottom-right' | 'bottom-left' | 'auto'  = 'auto';
  @Input() classPosition = '';
  @Input() label: string;
  @Input() labelClass: string;
  @Input() showIcon = true;
  @Input() labelPosition: 'between' | 'start' | 'end' = 'between';
  @Input() labelAlign: 'center' | 'start' | 'end' | 'baseline' | 'stretch' = 'end';
  @Input() tooltipIconSize: 'xs' | 'sm' | 'md' | 'lg' = 'sm';
  @Input() urlNewTab: string;
  @Input() tooltipClickable: boolean = true;

  start = true;

  private popupSize = 600;

  constructor() {
  }

  openTooltip(event: MouseEvent) {
    if (!this.tooltipClickable) {
      return;
    }

    if (this.urlNewTab) {
      window.open(this.urlNewTab, '_blank');
      return;
    }
    const bufferDistance = 10;
    const left = screen.width - event.screenX > this.popupSize ? event.screenX + bufferDistance : event.screenX - this.popupSize - 2 * bufferDistance;
    const top = screen.height - event.screenY > this.popupSize ? event.screenY + bufferDistance : event.screenY - this.popupSize - 2 * bufferDistance;
    const windowTip = window.open('', '', `scrollbars=yes,resizable=yes,top=${top},left=${left},width=${this.popupSize},height=${this.popupSize}`);
    windowTip.document.write('<html><head><title>Print it!</title><link rel="stylesheet" type="text/css" href="/assets/tooltip-popup-style.css"></head><body>');
    windowTip.document.write(this.popupText || this.tooltipText);
    windowTip.document.write('</body></html>');
    windowTip.document.title = (this.tooltipTitle);
  }
}
