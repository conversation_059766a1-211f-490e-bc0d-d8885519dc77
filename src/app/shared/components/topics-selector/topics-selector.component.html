<div *ngIf="isDropdownMode"
     #containerEle appClickOutside (clickOutside)="showDropdown(false)" [clickOutsideDisabled]="onlyDisplay"
     [excludedClasses]="['topics-selector-dropdown-mode', 'topics-popper']"
     appMouseHover [hoverClass]="onlyDisplay ? 'figma-bg-secondary topics-hovered' : 'border-contrast'"
     [hoverDisabled]="controlDisabled && !onlyDisplay || !showTooltip"
     class="topics-selector-container topics-selector-dropdown-mode radius-sm d-flex align-items-center justify-content-start flex-wrap gap-spacing-xx-s flex-shrink-0"
     [ngClass]="{
     'border-1 p-y-spacing-x-s p-x-spacing-sm min-h-spacing-x-lg justify-content-start flex-wrap': !onlyDisplay,
     'min-h-spacing-xx-big p-y-spacing-xx-s p-x-spacing-xx-s justify-content-between': onlyDisplay,
     'border-subtle figma-bg-secondary': !showingDropdown && !onlyDisplay,
     'border-contrast figma-bg-primary': showingDropdown && !onlyDisplay,
     'cursor-pointer': !isLoadingTopics && controlEnabled && !onlyDisplay
     }"
     (click)="showDropdown(true)">
  <ng-container *ngIf="!topics?.length && !showingDropdown">
    <div class="content-body-small content-color-disabled">Search for a topic...</div>
  </ng-container>

  <div class="d-flex gap-spacing-xx-s flex-wrap">
    <ng-container *ngFor="let topic of topics"
                  [ngTemplateOutlet]="topicItemTemplate"
                  [ngTemplateOutletContext]="{topic: topic}">
    </ng-container>

    <input *ngIf="showingDropdown && controlEnabled && showingInput"
           #searchInputEle appAutofocus type="text"
           class="h-spacing-xx-big border-0 figma-bg-primary p-0 flex-grow-1 content-body-small"
           [placeholder]="topics?.length ? '' : 'Search for a topic...'"
           [formControl]="searchInputFormControl"
           (keydown)="showDropdown(true)"
           [disabled]="isLoadingTopics">
  </div>

  <app-tooltip *ngIf="showTooltip"
    tooltipText='Topics define the context in which you are rating and help others to understand it better.'
    tooltipPosition="left" tooltipIconSize="sm"
    [tooltipClickable]="false" tooltipTextClass="text-left" class="d-none">
  </app-tooltip>
</div>

<ng-container *ngIf="isInlineMode">
  <div #containerEle appClickOutside (clickOutside)="isFocused = false; searchInputFormControl.setValue('')"
       [excludedClasses]="['topics-selector-inline-mode']"
       appMouseHover [hoverClass]="onlyDisplay ? 'figma-bg-secondary topics-hovered' : 'border-contrast'"
       [hoverDisabled]="controlDisabled && !onlyDisplay"
       class="topics-selector-container topics-selector-inline-mode radius-sm d-flex align-items-center justify-content-start flex-wrap gap-spacing-xx-s m-spacing-sm border-1 p-y-spacing-x-s p-x-spacing-sm min-h-spacing-x-lg flex-shrink-0"
       [ngClass]="{
         'border-subtle figma-bg-secondary': !isFocused,
         'border-contrast figma-bg-primary': isFocused,
         'cursor-pointer': !isLoadingTopics && controlEnabled,
         'overflow-y-auto': topics.length > 3 || isFocused
       }"
       (click)="isFocused = true">

    <div *ngIf="!topics?.length && !isFocused"
         class="content-color-disabled d-flex gap-spacing-xx-s align-items-center justify-content-start w-100"
         (click)="isFocused = true">
      <i class="fa-regular fa-search fa-1x"></i>
      <div class="content-body-small">{{ placeholder }}</div>
    </div>

    <ng-container *ngFor="let topic of topics"
                  [ngTemplateOutlet]="topicItemTemplate"
                  [ngTemplateOutletContext]="{topic: topic}">
    </ng-container>

    <div *ngIf="isFocused"
         class="d-flex gap-spacing-xx-s align-items-center justify-content-start w-100">
      <i *ngIf="!topics?.length" class="fa-regular fa-search fa-1x"></i>
      <input #searchInputEle appAutofocus type="text"
             class="h-spacing-xx-big border-0 figma-bg-primary p-0 flex-grow-1 content-body-small topics-search-input"
             [placeholder]="topics?.length ? '' : placeholder"
             [formControl]="searchInputFormControl"
             [disabled]="isLoadingTopics"
             (click)="isFocused = true"
             (focus)="isFocused = true">
    </div>

    <i *ngIf="topics?.length" class="fa-solid fa-circle-xmark fa-1x position-absolute cursor-pointer"
       [style.right.rem]="1" [style.top.rem]="1.2" (click)="onRemoveSelectedTopicsClicked($event)"
       ngbTooltip="Remove all selected topics"
       tooltipClass="white-tooltip" container="body">
    </i>
  </div>

  <div class="popover-divider"></div>

  <ng-container [ngTemplateOutlet]="topicSelectorItemsTemplate"></ng-container>
</ng-container>

<ng-template #topicItemTemplate let-topic="topic">
  <div class="tag-item tag-custom flex-shrink-0 border-1 border-bold content-style-semi-bold content-color-secondary h-spacing-xx-big"
       (click)="showDropdown(true)"
       [ngClass]="{'cursor-default': controlDisabled || onlyDisplay}"
       [ngbPopover]="popoverTopicTemp" #popover="ngbPopover"
       triggers="manual" [autoClose]="'outside'" popoverClass="white-popover" container="body"
       appTruncatableTooltip [truncatableTooltipPopover]="popover" truncatableTextCssSelector=".topic-name"
       [style]="{'--hover-bg': '#F6F6F6A6'}">
    <div class="topic-name content-label-small content-color-secondary text-ellipsis text-ellipsis-1 p-x-spacing-xx-s">
      {{ topic.name }}
    </div>
    <div *ngIf="showingDropdown || isInlineMode" class="tag-unassign cursor-pointer">
      <i class="far fa-close" (click)="onTopicRemoved($event, topic)"></i>
    </div>
    <ng-template #popoverTopicTemp>
      <div class="popover-descriptions">
        {{ topic.name }}
      </div>
    </ng-template>
  </div>
</ng-template>

<ng-template #topicSelectorItemsTemplate>
  <ng-container *ngIf="!isLoadingTopics">
    <div *ngIf="hasAvailableTopics"
         class="topic-items-container flex-grow-1 overflow-y-auto d-flex flex-column p-spacing-sm scrollbar-2024-sm">
      <div class="content-heading-h6 content-color-tertiary m-y-spacing-x-s m-x-spacing-xx-s">
        {{ label }}
      </div>

      <div
        class="w-100 flex-grow-1 overflow-y-auto d-flex flex-column align-items-start gap-spacing-sm p-r-spacing-sm p-x-spacing-xx-s p-y-spacing-x-s">
        <div *ngFor="let topic of availableTopics;" (click)="onTopicSelected($event, topic)"
             class="tag-item tag-custom border-1 border-bold content-style-semi-bold content-color-secondary mw-100 h-spacing-xx-big"
             [ngbPopover]="popoverTopicTemp" #popover="ngbPopover"
             triggers="manual" [autoClose]="'outside'" popoverClass="white-popover" container="body"
             appTruncatableTooltip [truncatableTooltipPopover]="popover" truncatableTextCssSelector=".topic-name"
             [style]="{'--hover-bg': '#F6F6F6A6'}">
          <div
            class="topic-name content-label-small content-color-secondary text-ellipsis text-ellipsis-1 p-x-spacing-xx-s">
            {{ topic.name }}
          </div>
          <ng-template #popoverTopicTemp>
            <div class="popover-descriptions">
              {{ topic.name }}
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <div *ngIf="noAnyTopics && !searchInputValue.length"
         class="flex-grow-1 d-flex flex-column align-items-center justify-content-start m-y-spacing-md p-spacing-sm">
      <i class="fa-regular fa-book-open-cover fa-border"></i>

      <div class="content-body-medium text-center content-color-secondary m-t-spacing-md">
        Be the first one to create a topic by simply typing a name.
      </div>
    </div>

    <div *ngIf="(!noAnyTopics || searchInputValue.length) && !hasAvailableTopics"
         class="w-100 d-flex flex-column align-items-start justify-content-start p-spacing-sm">
      <div class="content-heading-h6 m-y-spacing-x-s m-x-spacing-xx-s">Select a used topic or create one</div>
      <div class="content-heading-h6 m-y-spacing-x-s m-x-spacing-xx-s">No topics found.</div>
    </div>

    <div *ngIf="canCreateTopic"
         class="m-t-spacing-md d-flex align-items-center justify-content-between p-spacing-xx-s w-100 radius-sm figma-bg-secondary">
      <div
        class="tag-item tag-custom border-1 border-bold content-style-semi-bold content-color-secondary text-ellipsis text-ellipsis-1 cursor-default">
        <div class="content-label-small">{{ searchInputValue }}</div>
      </div>

      <div class="d-flex gap-spacing-sm m-l-spacing-md">
        <button class="button-main-secondary-grey button-small" [disabled]="isCreatingTopic"
                (click)="onCreateTopicCanceled()">
          Cancel
        </button>
        <button class="button-main-primary button-small" [disabled]="isCreatingTopic"
                (click)="onCreateTopicClicked()">
          Create
        </button>
      </div>
    </div>

    <ng-container *ngIf="showFooter">
      <div class="popover-divider"></div>

      <div class="d-flex align-items-start justify-content-start gap-spacing-xx-s p-spacing-md">
        <ng-container *ngIf="footerTemplate">
          <ng-container [ngTemplateOutlet]="footerTemplate"></ng-container>
        </ng-container>

        <ng-container *ngIf="!footerTemplate">
          <div class="topics-select-footer-icon">
            <i class="fa-light fa-info-circle"></i>
          </div>
          <div class="content-body-xsmall text-left content-color-tertiary">
            Topics define the context in which you are rating and help others to understand it better.
          </div>
        </ng-container>
      </div>
    </ng-container>

  </ng-container>

  <app-loading-dropdown *ngIf="isLoadingTopics" text="Topics are loading..."></app-loading-dropdown>
</ng-template>

<app-popper *ngIf="isDropdownMode" #topicsPopper placement="bottom" [showArrow]="false" [resizePopperWidth]="true"
            customClass="topics-popper p-spacing-none">
  <ng-container [ngTemplateOutlet]="topicSelectorItemsTemplate"></ng-container>
</app-popper>
