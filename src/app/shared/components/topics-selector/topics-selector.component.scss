@import 'scss/figma2023/index';
@import "scss/components/tag_item";

.topics-selector-container {
  .topics-search-input {
    &::placeholder {
      @include add-properties(map-get(map-get($typography, 'body'), 'small'), false);
    }

    &:focus {
      -webkit-box-shadow: none;
      box-shadow: none;
      outline: none;
    }
  }

  .tag-item {
    max-width: 200px;
  }

  &.topics-selector-inline-mode {
    max-height: 91px;
  }
}

::ng-deep {
  .topics-hovered {
    app-tooltip {
      display: block !important;
    }
  }

  .topics-popper {
    z-index: 9999;

    .fa-book-open-cover {
      color: $colour-blue-brand-500;
      background: $colour-blue-brand-200;
      --fa-border-radius: 50%;
      --fa-border-width: 1rem;
      --fa-border-color: #E0F7FF;
      --fa-border-padding: 1rem;
    }

    .tag-item {
      width: max-content;
    }

    .popper-inner {
      height: 100% !important;

      .topic-items-container {
        max-height: 12.65rem;
        overflow-x: hidden;
      }
    }
  }
}
