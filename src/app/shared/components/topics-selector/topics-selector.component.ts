import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { ControlContainer, FormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ControlValueAccessorComponent } from '@shared/components';
import { ToastService, ToastTypeEnum, TopicModel, TopicService } from '@core';
import { Subscription } from 'rxjs';
import { catchError, finalize, map, tap } from 'rxjs/operators';
import { PopperComponent } from '@shared/components/popper/popper.component';

@Component({
  selector: 'app-topics-selector',
  templateUrl: './topics-selector.component.html',
  styleUrls: ['./topics-selector.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: TopicsSelectorComponent
    }
  ]
})
export class TopicsSelectorComponent extends ControlValueAccessorComponent implements OnInit, OnDestroy {
  @Input() topics: TopicModel[] = [];
  @Output() topicsChange: EventEmitter<Array<TopicModel>> = new EventEmitter();
  @Input() initializeGeneralTopic: boolean = true;
  @Input() onlyDisplay: boolean = false;
  @Input() showCreationButton: boolean = true;
  @Input() displayMode: 'dropdown' | 'inline' = 'dropdown';
  @Input() showFooter: boolean = true;
  @Input() footerTemplate: TemplateRef<any> = null;
  @Input() notFoundTemplate: TemplateRef<any> = null;
  @Input() label: string = 'Select a used topic or create one';
  @Input() placeholder: string = 'Search for a topic...';
  @Input() showTooltip: boolean = true;

  @ViewChild('containerEle') containerEle: ElementRef<HTMLDivElement>;
  @ViewChild('searchInputEle', {read: ElementRef, static: false}) searchInputEle: ElementRef<HTMLInputElement>;
  @ViewChild('topicsPopper') topicsPopper: PopperComponent;

  showingDropdown: boolean = false;
  showingInput: boolean = true;
  allTopics: TopicModel[] = [];
  availableTopics: TopicModel[] = [];
  isLoadingTopics: boolean = false;
  searchInputFormControl = new FormControl(null);
  isCreatingTopic: boolean = false;

  isFocused: boolean = false;

  private isLoadingTopicsExecuted = false;
  private isControlEnabledByDefault: boolean = true;
  private subscriptions = new Subscription();

  constructor(
    @Optional() protected controlContainer: ControlContainer,
    private toastService: ToastService,
    private topicService: TopicService,
  ) {
    super(controlContainer);
  }

  get noAnyTopics(): boolean {
    return !this.allTopics?.length;
  }

  get hasAvailableTopics(): boolean {
    return this.availableTopics?.length > 0;
  }

  get searchInputValue(): string {
    return this.searchInputFormControl.value?.trim() || '';
  }

  get canCreateTopic(): boolean {
    return this.showCreationButton && this.searchInputValue.length > 2 && !this.isTopicNameExisted(this.searchInputValue, true);
  }

  get topicIdsFormValue(): number[] {
    return this.controlValue || [];
  }

  get isInlineMode(): boolean {
    return this.displayMode === 'inline';
  }

  get isDropdownMode(): boolean {
    return this.displayMode === 'dropdown';
  }

  ngOnInit(): void {
    this.isControlEnabledByDefault = this.controlEnabled;

    const valueChanges$ = this.searchInputFormControl.valueChanges
      .pipe(
        map(value => value?.trim()?.toLowerCase() || ''),
        tap((value) => {
          this.filterAvailableTopics(value);
        })
      )
      .subscribe();
    this.subscriptions.add(valueChanges$);

    if (this.initializeGeneralTopic) {
      this.disableControl();
      const createGeneralTopic$ = this.topicService.createGeneralTopic()
        .pipe(
          tap((topic) => {
            if (topic || this.topicIdsFormValue.length) {
              return this.loadTopics();
            }
          }),
          finalize(() => this.enableControl(this.isControlEnabledByDefault))
        )
        .subscribe();
      this.subscriptions.add(createGeneralTopic$);
    } else {
      if (this.topicIdsFormValue.length && !this.topics?.length || this.isInlineMode) {
        this.loadTopics();
      }
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  showDropdown(val: boolean) {
    if (this.controlDisabled || this.onlyDisplay) {
      return;
    }

    this.showingDropdown = val;

    if (this.showingDropdown) {
      this.showPopper();

      if (!this.isLoadingTopicsExecuted) {
        this.loadTopics();
      }

      this.focusSearchInput();
    } else {
      this.hidePopper();
    }
  }

  onTopicRemoved(event: MouseEvent, topic: TopicModel) {
    event.preventDefault();
    event.stopImmediatePropagation();

    if (this.isInlineMode) {
      this.isFocused = false;
    }

    this.topics = this.topics.filter(t => t.id !== topic.id);
    this.updateAvailableTopics();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onTopicSelected(event: MouseEvent, topic: TopicModel) {
    event.preventDefault();
    event.stopImmediatePropagation();

    this.topics.push(topic);
    this.updateAvailableTopics();
    this.updateControlValue();
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onCreateTopicCanceled() {
    this.hidePopper();

    this.updateInputDisplay(() => this.focusSearchInput());
  }

  onCreateTopicClicked() {
    this.isCreatingTopic = true;
    const payload = {name: this.searchInputValue};
    const createTopic$ = this.topicService.createTopic(payload)
      .pipe(
        tap((topic) => {
          this.allTopics.push(topic);
          this.topics.push(topic);
          this.updateAvailableTopics();
          this.updateControlValue();
          this.hidePopper();

          this.updateInputDisplay(() => this.focusSearchInput());
        }),
        finalize(() => this.isCreatingTopic = false)
      )
      .subscribe();
    this.subscriptions.add(createTopic$);
  }

  reset(keepPredefinedTopics: boolean = true) {
    if (keepPredefinedTopics) {
      this.topics = this.findPredefinedTopics(this.allTopics);
    } else {
      this.topics = [];
    }
    this.updateAvailableTopics();
    this.updateControlValue();
  }

  onRemoveSelectedTopicsClicked(event: MouseEvent) {
    event.preventDefault();
    event.stopImmediatePropagation();
    this.reset(false);
  }

  private showPopper() {
    this.topicsPopper?.show(this.containerEle.nativeElement);
  }

  private hidePopper() {
    this.topicsPopper?.hide();
    this.searchInputFormControl.setValue('');
  }

  private loadTopics() {
    if (this.isLoadingTopics) {
      return;
    }

    const payload = {page_size: 1000};
    this.isLoadingTopics = true;

    this.disableControl();
    this.subscriptions.add(
      this.topicService.getTopics(payload, false)
        .pipe(
          tap(({topics}) => this.allTopics = topics),
          tap(() => this.initializeSelectedTopics()),
          tap(() => this.filterAvailableTopics(this.searchInputValue)),
          finalize(() => {
            this.isLoadingTopics = false;
            this.isLoadingTopicsExecuted = true;
            this.enableControl(this.isControlEnabledByDefault);
          }),
          catchError((error) => {
            console.log(error);
            this.toastService.show({
              type: ToastTypeEnum.ERROR,
              header: 'Error in loading topics',
              body: 'An error occurred while retrieving the topics',
              delay: 5000
            });
            throw error;
          })
        )
        .subscribe()
    );
  }

  private initializeSelectedTopics() {
    if (this.topicIdsFormValue.length) {
      const initialTopics = this.allTopics.filter(topic => this.topicIdsFormValue.includes(topic.id));
      this.topics = this.sortPredefinedTopicsFirst(initialTopics);
    } else {
      if (this.initializeGeneralTopic) {
        this.topics = this.findPredefinedTopics(this.allTopics);
        const values = this.topics.map(t => t.id);
        this.setControlValue(values, true, true);
      }
    }

    this.updateAvailableTopics();
    this.topicsChange.emit(this.topics);
  }

  private findPredefinedTopics(topics: TopicModel[]): TopicModel[] {
    const results = topics.filter(topic => topic.is_predefined);
    return results?.length ? results : [];
  }

  private updateAvailableTopics() {
    const selectedTopicIds = this.topics.map(topic => topic.id);
    const availableTopics = this.allTopics.filter(topic => !selectedTopicIds.includes(topic.id));
    this.availableTopics = this.sortPredefinedTopicsFirst(availableTopics);
  }

  private sortPredefinedTopicsFirst(topics: TopicModel[]): TopicModel[] {
    const predefinedTopics = this.findPredefinedTopics(topics);
    const predefinedTopicIds = predefinedTopics.map(t => t.id);
    const remainingTopics = topics.filter(topic => !predefinedTopicIds.includes(topic.id));
    return [...predefinedTopics, ...remainingTopics];
  }

  private filterAvailableTopics(searchTerm: string) {
    this.updateAvailableTopics();

    if (searchTerm?.trim()?.length > 0) {
      searchTerm = searchTerm.trim().toLowerCase();
      this.availableTopics = this.availableTopics.filter(topic => {
        return topic.name?.toLowerCase()?.includes(searchTerm);
      });
    }
  }

  private isTopicNameExisted(name: string, isFullMatched: boolean): boolean {
    name = name?.toLowerCase();
    if (isFullMatched) {
      return this.allTopics.some(topic => {
        return topic.name?.trim()?.toLowerCase() == name;
      });
    } else {
      return this.allTopics.some(topic => {
        return topic.name?.toLowerCase()?.includes(name);
      });
    }
  }

  private updateControlValue() {
    const selectedIds = this.topics.map(topic => topic.id);
    this.setControlValue(selectedIds, true, true);
    this.searchInputFormControl.setValue('');
    this.topicsChange.emit(this.topics);
  }

  private focusSearchInput() {
    setTimeout(() => {
      if (this.showingDropdown || (this.isInlineMode && this.isFocused)) {
        this.searchInputEle?.nativeElement?.focus();
      } else {
        this.searchInputEle?.nativeElement?.blur();
      }
    }, 100);
  }

  private updateInputDisplay(callbackFunc: () => void) {
    if (this.isInlineMode) {
      callbackFunc();
    } else {
      this.showingInput = false;
      setTimeout(() => {
        this.showingInput = true;
        setTimeout(() => callbackFunc(), 50);
      }, 50);
    }
  }
}
