@import 'scss/layout2021/variables';

.dialog {
    color: $color-text-04 !important;

    a {
        color: $color-text-04 !important;
    }

    .select-language {
        color: $color-text-03;

        .selected-item {
            font-weight: 400;
            text-align: end;
            padding-right: 1.8rem;
            border: none;
            cursor: pointer;
            outline: none;
        }
        .dropdown {
            border-radius: 4px;
            padding: 8px 16px;
            border: 1px solid $color-text-03;
        }
        .dropdown-menu {
            padding: 8px;
            left: -15px !important;
            top: 9px !important;
            min-width: 175px !important;
            a {
              color: $color-text-03;
              display: block;
              font-size: 0.9rem;
              line-height: 1.25rem;
              padding: 5px;
              cursor: pointer;
              &.active {
                color: #FFF;
              }
            }
        }
        .dropdown-item {
            &:hover {
                border-radius: 4px;
                background-color: $dropdown-item-background;
                color: $color-text-03 !important;
            }

            &.selected-lang {
              color: $color-text-04;

              span {
                font-size: 0.875rem;
                font-weight: 600;
              }
            }
        }

        .checkbox-items {
            background-color: $modal-footer-background;
            padding: 12px 20px;
            border-radius: 4px;
        }
    }

    .modal-footer {
        background: transparent;
        .btn {
            height: 36px;
            font-size: 0.875rem;
            line-height: 20px;
        }
        .btn-btn-primary-outline {
            width: 74px;
        }
        .btn-primary {
            width: 109px;
        }
    }
}
