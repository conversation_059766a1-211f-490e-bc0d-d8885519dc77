import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { sortedTranslatableLanguages } from '@core/data';
import { UserService } from '@core';

@Component({
  selector: 'app-translate-language-dialog',
  templateUrl: './translate-language-dialog.component.html',
  styleUrls: ['./translate-language-dialog.component.scss']
})
export class TranslateLanguageDialogComponent implements OnInit {

  selectedLanguage: { code: string, name: string };
  detectedLanguage: { code: string, name: string };
  inputLanguages = sortedTranslatableLanguages;
  acceptToc: boolean = false;
  semantic_automatic_translations: boolean;

  constructor(
    private userService: UserService,
    public activeModal: NgbActiveModal
  ) {
  }

  ngOnInit(): void {
    this.semantic_automatic_translations = this.getTranslationsSetting();
  }

  translate() {
    if (!this.acceptToc) {
      return;
    }

    const currentTranslationsSetting = this.getTranslationsSetting();
    if (this.semantic_automatic_translations !== currentTranslationsSetting) {
      this.userService.updateUISettings({semantic_automatic_translations: this.semantic_automatic_translations}).subscribe();
    }
    this.activeModal.close(this.selectedLanguage);
  }

  onUseOriginalClicked() {
    this.activeModal.dismiss({useOriginal: true});
  }

  onLanguageChanged(lang: { code: string; name: string }) {
    this.selectedLanguage = lang;
    if (this.detectedLanguage && lang.code !== this.detectedLanguage?.code) {
      this.detectedLanguage = null;
    }
  }

  private getTranslationsSetting() {
    return this.userService.getUISetting('semantic_automatic_translations', false);
  }
}
