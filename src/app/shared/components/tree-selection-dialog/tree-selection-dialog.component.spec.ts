import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { TreeSelectionDialogComponent } from './tree-selection-dialog.component';
import { SharedModule } from '@shared/shared.module';
import { NgbActiveModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CommonModule } from '@angular/common';

describe('TreeSelectionDialogComponent', () => {
  let component: TreeSelectionDialogComponent;
  let fixture: ComponentFixture<TreeSelectionDialogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        SharedModule,
        NgbModule
      ],
      providers: [
        NgbActiveModal
      ],
      declarations: []
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TreeSelectionDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
