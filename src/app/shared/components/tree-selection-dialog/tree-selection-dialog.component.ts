import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { IActionMapping, ITreeOptions, TreeNode } from '@ali-hm/angular-tree-component';

@Component({
  selector: 'app-tree-selection-dialog',
  templateUrl: './tree-selection-dialog.component.html',
  styleUrls: ['./tree-selection-dialog.component.scss']
})
export class TreeSelectionDialogComponent implements OnInit, AfterViewInit, OnDestroy {

  selectedTreeId: string = null;

  @Input() treeNodes = [];
  @Input() title = 'Select a item';
  @Input() subTitle = '';
  @Input() enableSearch = false;
  @Input() loading = false;
  @Input() rootNodesSelectable = true;
  @Input() selectedNodes?: any[];
  @Input() hasMultipleSelect: boolean = false;
  @Input() isValueComparedByStartWiths: boolean = true;
  @Input() searchInputPlaceholder: string = 'Search by code or description';
  @Input() filterTerm: string = '';
  @Input() showOnlyName: boolean = false;
  @Output() multipleSelection = new EventEmitter<string[]>(null);
  @Output() treeNodesFiltered = new EventEmitter<string>(null);

  @ViewChild('tree', {static: false}) tree;
  @ViewChild('treeNodeTemplate', {static: false}) treeNodeTemplate;

  hasChildren = new EventEmitter<boolean>(true);
  initialNodeId: string;
  treeModified: boolean;
  _selectedNodes: any[] = [];

  private actionMapping: IActionMapping = {
    mouse: {
      click: (tree, node, $event) => {
        this.treeModified = true;
        if (this.hasMultipleSelect) {
          $event.preventDefault();
          this.onNodeToggle(node);
          return;
        }
        if (this.rootNodesSelectable || !node.isRoot) {
          this.activeModal.close(node.data.value ? node.data.value : node.id);
        } else {
          if (node.isExpanded) {
            node.collapse();
          } else {
            node.expand();
          }
        }
      }
    }
  };

  treeOptions = {
    actionMapping: this.actionMapping,
    allowDrag: false,
    allowDrop: false,
    useVirtualScroll: false,
    scrollContainer: <HTMLElement>document.body
  } as ITreeOptions;

  constructor(
    public activeModal: NgbActiveModal
  ) {}

  ngOnInit() {
    this.moveIntercomButton();
  }

  ngAfterViewInit() {
    this.hasChildren.next(this.treeHasChildren());
    if (this.selectedNodes) {
      this._selectedNodes = [...this.selectedNodes];
    }
  }
  
  ngOnDestroy() {
    document.body.classList.remove('tree-dialog-open');
  }

  onTreeItemActive($event: any) {
    this.selectedTreeId = $event.node.data.id;
  }

  onTreeItemDeactivate($event: any) {
    this.selectedTreeId = null;
  }

  displayExpandAll() {
    return this.tree && this.tree.treeModel.roots.find(n => n.isCollapsed) && this.treeHasChildren();
  }

  displayCollapseAll() {
    return this.tree && this.tree.treeModel.roots.find(n => n.isExpanded) && this.treeHasChildren();
  }

  onChangeFilter() {
    this.treeNodesFiltered.emit(this.filterTerm);
  }

  onFilter() {
    this.treeNodesFiltered.emit(this.filterTerm);
  }

  onUpdateData($event: any) {
    this.expandTreeNodes(this.treeNodes);
  }

  findNode(node: TreeNode, nodes = this._selectedNodes) {
    if (!node || !nodes) {
      return -1;
    }

    return nodes.findIndex(x => {
      if (typeof x !== 'string') {
        return x.id === node.id;
      }
      return x === node.data.value;
    });
  }

  getValue(i): string {
    return typeof i !== 'string' ? i.value.toString() : i;
  }

  onNodeToggle(node: TreeNode) {
    const i = this.findNode(node), o = node;
    if (i === -1) {
      o.setIsSelected(true);
      this._selectedNodes.push({id: node.id, value: node.data.value, name: node.data.name});
    } else {
      o.setIsSelected(false);
      this._selectedNodes.splice(i, 1);
    }
  }

  isParentSelected(node, level = 0) {
    if (this.hasMultipleSelect) {
      const r = this.findNode(node);
      if (r > -1) {
        return level;
      } else if (node.parent) {
        level++;
        return this.isParentSelected(node.parent, level);
      }
    }
    return -1;
  }

  isSelectedNode(node, nodes = this._selectedNodes) {
    return this.hasMultipleSelect && this.findNode(node, nodes) > -1;
  }

  hasBackground(node) {
    return this.hasMultipleSelect && (this.isParentSelected(node) > -1 || this.findNode(node) > -1);
  }

  getNodeClasses(node: TreeNode) {
    if (this.findNode(node) > -1) {
      return ' completely-selected ';
    }
    if (this._selectedNodes.find(x => this.compareValue(x, node.data.value || node.data.id) || this.isParentPartiallySelected(x.id, node)) || node.isPartiallySelected) {
      return ' partially-selected ';
    }
    return '';
  }

  onConfirm() {
    this.selectedNodes = [...new Set(this._selectedNodes)].filter(nd => typeof nd === "string" || (!!nd.value || !!nd.id));
    this.multipleSelection.emit(this.selectedNodes);
    this.activeModal.dismiss();
  }

  private compareValue(a: string | number, b: string | number): boolean {
    if (this.isValueComparedByStartWiths && typeof a === 'string' && typeof b === 'string') {
      return a.startsWith(b);
    }
    return a === b;
  }

  private isParentPartiallySelected(selectedId, node: TreeNode): boolean {
    if (!node.children) {
      return false;
    }
    for (const child of node.children) {
      if (child.data.id === selectedId) {
        return true;
      }
      if (this.isParentPartiallySelected(selectedId, child)) {
        return true;
      }
    }
    return false;
  }

  private scrollToInitialNode(nodeId) {
    if (nodeId === this.initialNodeId) {
      setTimeout(() => {
        const nodeEle = document.getElementById(`node-${nodeId}`);
        if (nodeEle) {
          nodeEle.scrollIntoView({behavior: 'smooth', block: 'start', inline: 'nearest'});
        }
      }, 500);
    }
  }

  private expandTreeNodes(nodes: any[]) {
    for (const treeNode of nodes) {
      if (treeNode.isExpanded) {
        const node = this.tree.treeModel.getNodeById(treeNode.id);
        node.expand();
        this.expandTreeNodes(treeNode.children);
      }

      this.scrollToInitialNode(treeNode.id);
    }
  }

  private treeHasChildren() {
    if (this.tree) {
      return this.tree.treeModel && this.tree.treeModel.roots && this.tree.treeModel.roots.find(n => n.children && n.children.length > 0);
    }
    return this.treeNodeTemplate && this.treeNodeTemplate.treeModel && this.treeNodeTemplate.treeModel.roots &&
      this.treeNodeTemplate.treeModel.roots.find(n => n.children && n.children.length > 0);
  }

  removeIdSpaces(id: string): string {
    return id.toString().replace(/\s+/g, '');
  }

  private moveIntercomButton() {
    document.body.classList.add('tree-dialog-open');
    
    const originalDismiss = this.activeModal.dismiss;
    const originalClose = this.activeModal.close;
    
    this.activeModal.dismiss = (reason?: any) => {
      document.body.classList.remove('tree-dialog-open');
      return originalDismiss.call(this.activeModal, reason);
    };
    
    this.activeModal.close = (result?: any) => {
      document.body.classList.remove('tree-dialog-open');
      return originalClose.call(this.activeModal, result);
    };
  }
}
