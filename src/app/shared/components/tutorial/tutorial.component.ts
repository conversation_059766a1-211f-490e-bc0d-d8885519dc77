import { AfterViewInit, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';

@Component({
  selector: 'app-tutorial',
  templateUrl: './tutorial.component.html',
  styleUrls: ['./tutorial.component.scss']
})
export class TutorialComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() title: string;
  @Input() src: string;
  @Input() description: string;

  safeSrc: SafeUrl;
  loading = true;

  constructor(
    public activeModal: NgbActiveModal,
    public sanitizer: DomSanitizer
  ) {
  }

  ngOnInit(): void {
    this.updateSafeSrc(this.src);
  }

  ngOnChanges(changes: SimpleChanges) {
    this.updateSafeSrc(changes.src?.currentValue);
  }

  ngAfterViewInit() {
    const srcEle = document.getElementById('iframeSrc');

    if (srcEle) {
      (srcEle as HTMLIFrameElement).addEventListener('load', () => {
        this.loading = false;
      }, true);
    }
  }

  getBodyStyle() {
    if (this.title) {
      return {height: "calc( 100% - 97px )"};
    }

    return {height: "100%"};
  }

  private updateSafeSrc(_src: string) {
    if (_src) {
      this.loading = true;
      this.safeSrc = this.sanitizer.bypassSecurityTrustResourceUrl(_src);
    }
  }
}
