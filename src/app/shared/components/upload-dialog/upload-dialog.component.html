<div class="modal-header">
    <div class="modal-title">Upload file</div>
    <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss()" appAutofocus></button>
</div>
<div class="modal-body">
    <app-alert type="danger" [message]="uploadErrorMessage" *ngIf="uploadErrorMessage"></app-alert>

    <div class="d-flex flex-row justify-content-between">
        <input class="form-control me-2" type="text" placeholder="Select a file to upload (.csv, .ods, .xlx, .xlsx)" [ngModel]="file?.name" disabled>
        <label class="btn btn-primary btn-md" for="load-file" ngbTooltip="Text and CSV file supported only">
            <span>
                Browse
            </span>
            <input type="file" class="hidden" id="load-file" accept=".csv,.ods,.xlx,.xlsx"
                   (change)="onChange($event)" [disabled]="isOpeningFile">
        </label>
    </div>
    <div class="row pt-3">
        <div class="col-4">
            <span class="align-self-center column-label">{{columnLabel}}</span>
        </div>
        <div class="col-4">
            <input type="number" min="0" class="form-control align-self-center col-2 ms-3" [(ngModel)]="columnNumber"/>
        </div>
        <div class="col-4">
            <label class="checkbox ms-4 pt-2 align-self-center">
                <input type="checkbox" [(ngModel)]="header">
                <span class="left">Header</span>
            </label>
        </div>
    </div>

    <div class="panel-table overflow-auto mt-2 mb-2" *ngIf="dataSheet.length && !isOpeningFile;">
        <table class="table table-condensed w-100 table-hover mb-0 mt-0">
            <thead class="w-100" *ngIf="header">
                <tr class="cursor-pointer">
                    <th *ngFor="let column of dataSheet[0]; let i=index" (click)="selectColumn(i)" [ngClass]="{'column-selected': i == columnNumber}">
                        <span style="cursor: pointer;">{{column}}</span>
                    </th>
                </tr>
            </thead>
            <tbody *ngFor="let row of dataSheet; let i=index">
                <tr *ngIf="i > 0 || !header" [ngClass]="{'cursor-pointer': i===0}">
                  <td *ngFor="let column of row; let j=index" [ngClass]="{'column-selected': j == columnNumber}"
                      (click)="selectColumn(j)" [title]="i === 0 ? column : ''">
                    <div class="file-row">{{column}}</div>
                  </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="d-flex justify-content-center mt-3" *ngIf="isOpeningFile">
        <img src="assets/images/octimine_blue_spinner.gif">
    </div>
    <div class="d-flex justify-content-end align-items-center">
        <img src="/assets/images/octimine_blue_spinner.gif" style="width: 35px; height: 35px" class="me-2" *ngIf="isUploading">
        <button class="btn btn-secondary btn-lg" [disabled]="!hasFile() || isUploading" (click)="upload()">UPLOAD</button>
    </div>
</div>
<div class="modal-footer">
</div>
