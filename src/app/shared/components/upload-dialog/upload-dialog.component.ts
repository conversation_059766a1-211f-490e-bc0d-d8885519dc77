import { Component, Input, OnDestroy } from '@angular/core';
import { PatentNumberService } from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize } from 'rxjs/operators';

import * as XLSX from 'xlsx';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-upload-dialog',
  templateUrl: './upload-dialog.component.html',
  styleUrls: ['./upload-dialog.component.scss']
})
export class UploadDialogComponent implements OnDestroy {

  @Input()
  columnLabel: string = 'Column number: ';
  file: File;
  columnNumber = 0;
  header = false;
  isUploading = false;
  isOpeningFile = false;
  uploadErrorMessage = '';
  dataSheet = [];

  private subscriptions = new Subscription();

  constructor(
    private service: PatentNumberService,
    public activeModal: NgbActiveModal,
  ) { }

  onChange(event) {
    if (!event.target.files[0]) {
      return;
    }
    this.file = this.getFileInstance(event.target.files[0])
    this.readFile();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  readFile() {
    this.dataSheet = [];
    this.isOpeningFile = true;
    const reader = new FileReader();
    reader.onloadend = ( event => {
      const wb = XLSX.read(reader.result, {sheetRows: 10});
      const wsName = wb.SheetNames[0];
      const ws = wb.Sheets[wsName];

      this.dataSheet = XLSX.utils.sheet_to_json(ws, {header: 1});
      this.isOpeningFile = false;
    });
    reader.readAsArrayBuffer(this.file);
  }

  getFileInstance(file: File) {
    // Looks like content-type resolves incorrectly for csv files under Windows.
    // So we will patch the content type based on the file extension.
    // Since this only seems to happen to csv files, for others we just use the original file
    const extension = file.name.split('.').pop();
    if (extension === 'csv') {
      return new File([file], file.name, {'type': 'text/csv'})
    }
    return file;
  }

  selectHeader() {
    console.log(this.header);
  }

  selectColumn(column: number) {
    this.columnNumber = column;
  }

  hasFile(): boolean {
    return this.file != null;
  }

  upload() {
    this.uploadErrorMessage = '';
    this.isUploading = true;
    const formData = new FormData();
    formData.append('file', this.file);
    const params = {column: this.columnNumber, header: this.header ? 1 : 0};
    const sendFile$ = this.service.sendFile(formData, params)
      .pipe(finalize(() => this.isUploading = false))
      .subscribe({
        next: (data) => {
          if (data['data'].valid.length) {
            this.activeModal.close(data);
          } else {
            this.uploadErrorMessage = 'There are no valid patent numbers';
          }
        },
        error: error => {
          if (error.status === 500) {
            this.uploadErrorMessage = 'Unable to parse file. Try again later.';
          } else {
            this.uploadErrorMessage = error.error.message;
          }
          console.error(error);
        }
      });
    this.subscriptions.add(sendFile$);
  }

}
