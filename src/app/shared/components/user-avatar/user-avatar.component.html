<div *ngIf="user" #avatarContainerEle class="user-avatar" [ngClass]="'user-avatar-' + size"
     [class.custom-avatar-size]="avatarSize && avatarFontSize"
     [ngbPopover]="showTooltip ? popoverUserTitleTemp : null" #userTitlePopover="ngbPopover"
     triggers="manual" [autoClose]="'outside'" popoverClass="white-popover" container="body"
     appTruncatableTooltip [truncatableTooltipPopover]="showTruncatableTooltip && showTooltip ? userTitlePopover : null"
     truncatableTextCssSelector=".user-subtitle">

  <ng-container *ngIf="showTooltip">
    <div *ngIf="hasCustomAvatar" class="user-avatar-photo user-avatar-custom show-tooltip"
         [ngbPopover]="popoverUserTooltipTemp"
         appSelectableTooltip [selectableTooltipPopover]="userAvatarPopover" #userAvatarPopover="ngbPopover"
         triggers="manual" [autoClose]="'outside'"
         [popoverClass]="avatarTooltipCss" container="body"
         placement="auto" [ngClass]="{'active': isActive}"
         [autoCloseTooltip]="autoCloseAvatarTooltip"
         (shown)="avatarTooltipShown.emit(userAvatarPopover)"
         (hidden)="avatarTooltipHidden.emit(userAvatarPopover)">
      <ng-container *ngTemplateOutlet="customAvatarTemp"></ng-container>
    </div>

    <ng-container *ngIf="!hasCustomAvatar">
      <div #avatarEle class="user-avatar-photo user-avatar-image show-tooltip"
           [ngbPopover]="popoverUserTooltipTemp"
           appSelectableTooltip [selectableTooltipPopover]="userAvatarPopover" #userAvatarPopover="ngbPopover"
           triggers="manual" [autoClose]="'outside'"
           [popoverClass]="avatarTooltipCss" container="body"
           placement="auto" [hidden]="!avatarDataUrl" [ngClass]="{'active': isActive}"
           [autoCloseTooltip]="autoCloseAvatarTooltip"
           (shown)="avatarTooltipShown.emit(userAvatarPopover)"
           (hidden)="avatarTooltipHidden.emit(userAvatarPopover)">
      </div>
      <div *ngIf="!avatarDataUrl" class="user-avatar-photo user-avatar-text show-tooltip"
           [ngbPopover]="popoverUserTooltipTemp"
           appSelectableTooltip [selectableTooltipPopover]="userAvatarPopover" #userAvatarPopover="ngbPopover"
           triggers="manual" [autoClose]="'outside'"
           [popoverClass]="avatarTooltipCss" container="body"
           placement="auto" [ngClass]="{'active': isActive}"
           [autoCloseTooltip]="autoCloseAvatarTooltip"
           (shown)="avatarTooltipShown.emit(userAvatarPopover)"
           (hidden)="avatarTooltipHidden.emit(userAvatarPopover)">
        {{ getTextAvatarTitle() }}
      </div>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="!showTooltip">
    <div *ngIf="hasCustomAvatar" class="user-avatar-photo user-avatar-custom"
         [ngClass]="{'active': isActive}">
      <ng-container *ngTemplateOutlet="customAvatarTemp"></ng-container>
    </div>

    <ng-container *ngIf="!hasCustomAvatar">
      <div #avatarEle class="user-avatar-photo user-avatar-image" [hidden]="!avatarDataUrl"
           [ngClass]="{'active': isActive}">
      </div>
      <div *ngIf="!avatarDataUrl" class="user-avatar-photo user-avatar-text"
           [ngClass]="{'active': isActive}">
        {{ getTextAvatarTitle() }}
      </div>
    </ng-container>
  </ng-container>

  <div *ngIf="hasSubTitle" class="user-avatar-title m-l-spacing-xx-s"
       [style]="subtitleMaxWidth ? {'max-width': subtitleMaxWidth} : {}">
    <div class="user-subtitle text-ellipsis text-ellipsis-1 d-block"
         [ngClass]="subTitleCssClass">{{ user | userTitle }}
    </div>
  </div>

  <ng-template #popoverUserTitleTemp>
    <div class="popover-descriptions">{{ user | userTitle }}</div>
  </ng-template>

  <ng-template #popoverUserTooltipTemp>
    <div class="popover-descriptions scrollbar-2024-sm">
      <ng-container *ngIf="popoverUserTemplate" [ngTemplateOutlet]="popoverUserTemplate"
                    [ngTemplateOutletContext]="{ user: user }">
      </ng-container>

      <div *ngIf="!popoverUserTemplate" class="user-info">
        <div *ngIf="tooltipPrefix" class="content-heading-h6 content-color-tertiary p-b-spacing-sm">
          {{ tooltipPrefix }}
        </div>
        <div *ngIf="showUserTitleOnTooltip" class="content-heading-h6 content-color-primary">{{ user | userTitle }}
        </div>
        <div *ngIf="showUserEmailOnTooltip && user.email"
             class="content-heading-h7 content-color-primary">{{ user.email }}
        </div>

        <ng-container *ngIf="userExtraInfoTemplate" [ngTemplateOutlet]="userExtraInfoTemplate"
                      [ngTemplateOutletContext]="{ user: user }">
        </ng-container>
      </div>
    </div>
  </ng-template>

  <ng-template #customAvatarTemp>
    <ng-content select="[customAvatar]"></ng-content>
  </ng-template>
</div>

<ng-content></ng-content>
