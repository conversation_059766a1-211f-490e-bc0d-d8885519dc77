@import 'scss/layout2021/variables';
@import 'scss/layout2021/mixins';
@import "scss/figma2023/index";


$sizes: (
  'xxxsmall' 1rem 0.5rem,
  'xxsmall' 1.25rem 0.625rem,
  'xsmall' 1.5rem 0.625rem,
  'small' 2rem 0.875rem,
  'medium' 2.5rem 1rem,
  'large' 3rem 1.125rem,
  'xlarge' 3.5rem 1.25rem,
  'xxlarge' 4rem 1.5rem,
  'huge' 6rem 2rem,
  'xhuge' 8rem 3rem,
);
$avatar-size: var(--avatarSize, 30px);
$font-size: var(--avatarFontSize, 12px);

.user-avatar {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
  position: relative;

  .user-avatar-photo {
    background: $colours-background-bg-primary;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    text-align: center;
    padding: 0;
    margin: 0;
    color: $colours-content-content-primary;
    display: flex;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
    border: 1.5px solid $colours-border-subtle;
    font-family: $font-open-sans-semi-bold;
    cursor: default !important;
    @include border-radius(50%);
    @include none-select();

    &.show-tooltip:hover {
      border: 1.5px solid $colours-border-bold !important;
    }

    &.active {
      border: 1.5px solid $colours-border-bold !important;
    }
  }

  .user-avatar-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    @include add-properties(map-deep-get($typography, label, xsmall), true);
  }

  @each $name, $size, $fontSize in $sizes {
    &.user-avatar-#{$name} {
      .user-avatar-photo {
        width: $size;
        min-width: $size;
        height: $size;
        min-height: $size;
        font-size: $fontSize;
      }
    }
  }
  &.custom-avatar-size{
    .user-avatar-photo {
      width: $avatar-size;
      min-width: $avatar-size;
      height: $avatar-size;
      min-height: $avatar-size;
      font-size: $font-size;
    }
  }
}
