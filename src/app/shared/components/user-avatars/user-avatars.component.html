<div *ngIf="avatarService.hasUsersAndGroups(users, groups)" class="user-avatars-container"
     [ngStyle]="getContainerStyle">
  <app-user-avatar *ngFor="let u of getVisibleAvatars; let index = index;"
                   [user]="u" [ngStyle]="getAvatarStyle(index)" [hasSubTitle]="false"
                   [tooltipPrefix]="avatarsTooltipPrefix" [size]="size"
                   [userExtraInfoTemplate]="userExtraInfoTemplate"
                   [showUserTitleOnTooltip]="showUserTitleOnTooltip"
                   [showUserEmailOnTooltip]="showUserEmailOnTooltip"
                   [avatarTooltipCss]="avatarTooltipCss"
                   [isActive]="isActive(u)"
                   (avatarTooltipShown)="avatarTooltipShown.emit({user: u, popover: $event})"
                   (avatarTooltipHidden)="avatarTooltipHidden.emit(u)"
                   [autoCloseAvatarTooltip]="autoCloseAvatarTooltip">
  </app-user-avatar>

  <app-user-avatar *ngIf="hasMoreAvatars" [user]="moreUserData" [ngStyle]="getMoreUserStyle"
                   [hasSubTitle]="false" [size]="size"
                   [popoverUserTemplate]="popoverMoreUsersTemp"
                   [showUserTitleOnTooltip]="showUserTitleOnTooltip"
                   [showUserEmailOnTooltip]="showUserEmailOnTooltip"
                   [avatarTooltipCss]="avatarTooltipCss"
                   (avatarTooltipShown)="avatarTooltipShown.emit({user: moreUserData, popover: $event})"
                   (avatarTooltipHidden)="avatarTooltipHidden.emit(moreUserData)"
                   [autoCloseAvatarTooltip]="autoCloseAvatarTooltip">
  </app-user-avatar>

  <ng-template #popoverMoreUsersTemp>
    <ng-container *ngIf="popoverMoreUsersTemplate" [ngTemplateOutlet]="popoverMoreUsersTemplate"
                  [ngTemplateOutletContext]="{ users: remainingUsersAndGroups }">
    </ng-container>
    <ng-container *ngIf="!popoverMoreUsersTemplate">
      <div class="more-users-container" appScrollbarDetector scrollbarCss="has-scrollbar">
        <div *ngIf="avatarsTooltipPrefix"
             class="content-heading-h6 content-color-tertiary p-x-spacing-md p-b-spacing-sm">
          {{ avatarsTooltipPrefix }}
        </div>
        <div *ngFor="let user of remainingUsersAndGroups;"
             class="p-x-spacing-md p-y-spacing-sm d-flex align-items-start justify-content-start gap-spacing-x-s">
          <app-user-avatar [user]="user" [hasSubTitle]="false" [showTooltip]="false" size="xsmall">
          </app-user-avatar>
          <div class="user-info">
            <div *ngIf="showUserTitleOnMoreUsers" class="content-heading-h6 content-color-primary">{{ user | userTitle }}</div>
            <div *ngIf="showUserEmailOnMoreUsers && user.email" class="content-heading-h7 content-color-primary">{{ user.email }}</div>
            <ng-container *ngIf="moreUsersExtraInfoTemplate" [ngTemplateOutlet]="moreUsersExtraInfoTemplate"
                          [ngTemplateOutletContext]="{ user: user }">
            </ng-container>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-template>
</div>
