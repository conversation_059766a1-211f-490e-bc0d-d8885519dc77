@import 'scss/figma2023/index';

$avatar-size: var(--avatarSize, 30px);
$font-size: var(--avatarFontSize, 12px);
$hover-z-index: var(--hoverZIndex, 1);

:host {
  height: $avatar-size;
  display: inline-block;
}

.user-avatars-container {
  position: relative;
  display: inline-block;
  height: $avatar-size;

  app-text-avatar, app-user-avatar {
    position: absolute;
    height: $avatar-size;

    ::ng-deep {
      .ta-user, .user-avatar-photo {
        margin-bottom: 0;
        width: $avatar-size !important;
        max-width: $avatar-size !important;
        height: $avatar-size !important;
        max-height: $avatar-size !important;

        &:not(:last-child) {
          margin-right: 0 !important;
        }

        .ta-avatar {
          height: $avatar-size !important;
          width: $avatar-size !important;
          font-size: $font-size !important;
          line-height: $font-size !important;
          border: 1.2px solid white;
        }
      }
    }
  }
}

::ng-deep {
  .popover-user-avatar {
    width: fit-content;
    max-width: fit-content;

    .popover-body {
      width: fit-content;
      max-width: fit-content;

      &:has(.has-scrollbar) {
        padding-right: $spacing-system-spacing-xx-s !important;
      }

      .popover-descriptions {
        width: fit-content;
        max-width: fit-content;
      }
    }
  }

  .more-users-container {
    max-height: 4 * 4.75rem;
    overflow-y: auto;
  }
}
