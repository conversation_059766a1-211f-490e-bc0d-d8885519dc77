import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewContainerRef } from '@angular/core';
import { AvatarService, TeamUser, TeamUserTypeEnum, UserGroup } from '@core';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

/**
 * Todo:
 * Need to clean up the size of the avatars, just use the size property.
 * Also remove the avatarSize and avatarFontSize properties
 */
@Component({
  selector: 'app-user-avatars',
  templateUrl: './user-avatars.component.html',
  styleUrls: ['./user-avatars.component.scss']
})
export class UserAvatarsComponent implements OnInit {
  @Input() users: TeamUser[] = [];
  @Input() groups: UserGroup[] = [];
  @Input() distanceBetweenAvatars: number = 25;
  @Input() numberDisplayedUsers: number = 0;
  @Input() numberDisplayedGroups: number = 0;
  @Input() avatarsTooltipPrefix: string = null;
  @Input() avatarSize: number = null;
  @Input() avatarFontSize: number = null;
  @Input() avatarsDirection: 'left' | 'right' = 'right';
  @Input() avatarOpacity: number = null;
  @Input() size: 'xxxsmall' | 'xxsmall' | 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge' | 'xxlarge' | 'huge' | 'xhuge' = 'xsmall';
  @Input() tooltipFunc: (user: TeamUser | UserGroup) => string = null;
  @Input() popoverUserTemplate: TemplateRef<any> = null;
  @Input() popoverMoreUsersTemplate: TemplateRef<any> = null;
  @Input() userExtraInfoTemplate: TemplateRef<any> = null;
  @Input() moreUsersExtraInfoTemplate: TemplateRef<any> = null;
  @Input() activeUserIds: number[] = [];
  @Input() activeGroupIds: number[] = [];
  @Input() showUserTitleOnTooltip: boolean = true;
  @Input() showUserEmailOnTooltip: boolean = true;
  @Input() showUserTitleOnMoreUsers: boolean = true;
  @Input() showUserEmailOnMoreUsers: boolean = true;
  @Input() avatarTooltipCss: string = 'white-popover popover-user-avatar';
  @Input() autoCloseAvatarTooltip: boolean = true;

  @Output() avatarTooltipShown = new EventEmitter<{ user: TeamUser, popover: NgbPopover }>();
  @Output() avatarTooltipHidden = new EventEmitter<TeamUser>();

  private readonly defaultAvatarSize = 30;
  private readonly defaultAvatarFontSize = 12;

  constructor(
    private readonly viewRef: ViewContainerRef,
    public avatarService: AvatarService
  ) {
  }

  get moreUserData(): TeamUser {
    return this.avatarService.genMoreUsersData(this.users, this.groups, this.numberDisplayedUsers, this.numberDisplayedGroups);
  }

  get hasMoreAvatars(): boolean {
    return this.avatarService.hasMoreUsersAndGroups(this.users, this.groups, this.numberDisplayedUsers, this.numberDisplayedGroups);
  };

  get getContainerStyle(): object {
    let len = this.avatarService.calculateMaxDisplayedUsersAndGroups(this.users, this.groups, this.numberDisplayedUsers, this.numberDisplayedGroups) - 1;
    if (this.hasMoreAvatars) {
      len++;
    }
    const width = (len * this.distanceBetweenAvatars + this.getAvatarSize) + 'px';
    return {
      'width': width,
      'min-width': width,
      'max-width': width
    };
  }

  get remainingUsersAndGroups(): TeamUser[] {
    return this.avatarService.getRemainingUsersAndGroups(this.users, this.groups, this.numberDisplayedUsers,
      this.numberDisplayedGroups);
  }

  get getVisibleAvatars(): TeamUser[] {
    return this.avatarService.getDisplayedUsersAndGroups(this.users, this.groups, this.numberDisplayedUsers, this.numberDisplayedGroups);
  }

  get getMoreUserStyle() {
    return this.avatarService.getAvatarStyle(0, 0, this.avatarsDirection, this.distanceBetweenAvatars);
  }

  get getAvatarSize(): number {
    if (this.avatarSize) {
      return this.avatarSize;
    }

    switch (this.size) {
      case 'xxxsmall':
        return 16;
      case 'xxsmall':
        return 1.25 * 16;
      case 'xsmall':
        return 1.5 * 16;
      case 'small':
        return 2 * 16;
      case 'medium':
        return 2.5 * 16;
      case 'large':
        return 3 * 16;
      case 'xlarge':
        return 3.5 * 16;
      case 'xxlarge':
        return 4 * 16;
      case 'huge':
        return 6 * 16;
      case 'xhuge':
        return 8 * 16;
    }

    return this.defaultAvatarSize;
  }

  get getAvatarFontSize(): number {
    return this.avatarFontSize || this.defaultAvatarFontSize;
  }

  ngOnInit(): void {
    this.viewRef.element.nativeElement.style.setProperty('--avatarSize', this.getAvatarSize + 'px');
    this.viewRef.element.nativeElement.style.setProperty('--avatarFontSize', this.getAvatarFontSize + 'px');
  }

  getAvatarStyle(baseIndex: number) {
    const directionIndex = this.avatarService.getAvatarIndex(this.users, this.groups, this.numberDisplayedUsers, this.numberDisplayedGroups, baseIndex);
    return this.avatarService.getAvatarStyle(directionIndex, this.avatarOpacity, this.avatarsDirection, this.distanceBetweenAvatars);
  }

  isActive(user: TeamUser): boolean {
    if (!user) {
      return false;
    }
    if (user.type === TeamUserTypeEnum.USER) {
      return this.activeUserIds.includes(user.id);
    }
    return this.activeGroupIds.includes(user.id);
  }
}
