<div appClickOutside (clickOutside)="showInput(false)" [excludedClasses]="['user-dialogue-container']"
     class="user-dialogue-container p-spacing-sm radius-sm border-1 cursor-pointer d-flex flex-column align-items-stretch gap-spacing-sm"
     [ngClass]="{'border-subtle figma-bg-secondary': !showingInput, 'border-contrast figma-bg-primary': showingInput}"
     (click)="showInput(true)">
  <div *ngIf="showUserAvatar" class="d-flex gap-spacing-xx-s align-items-center">
    <app-user-avatar [user]="userService.getUser()?.profile" [hidden]="!(showingInput || hasValue)"
                     [hasSubTitle]="showingInput || hasValue" [showTooltip]="false">
    </app-user-avatar>

    <app-user-avatar [user]="userService.getUser()?.profile" [hidden]="showingInput || hasValue"
                     [hasSubTitle]="showingInput || hasValue" [showTooltip]="true">
    </app-user-avatar>

    <div *ngIf="!showingInput && !hasValue" class="content-body-small text-left content-color-disabled flex-grow-1">
      {{ placeholder }}
    </div>
  </div>

  <textarea *ngIf="showingInput || hasValue || !showUserAvatar"
            #inputEle appTextareaAutoresize
            [formControl]="control"
            class="content-body-small content-color-primary p-spacing-none border-0"
            [ngClass]="{'border-subtle figma-bg-secondary': !showingInput, 'border-contrast figma-bg-primary': showingInput}"
            [placeholder]="placeholder"
            [value]="controlValue"
            [ngStyle]="minHeight ? {'min-height': minHeight} : {}"
            (keydown.enter)="onInputEnter($event)"
            (blur)="onInputBlur()"
            [disabled]="control?.disabled"
            [maxLength]="maxLength">
  </textarea>
</div>
