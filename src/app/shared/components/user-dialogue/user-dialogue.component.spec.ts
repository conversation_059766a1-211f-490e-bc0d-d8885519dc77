import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UserDialogueComponent } from './user-dialogue.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('UserDialogueComponent', () => {
  let component: UserDialogueComponent;
  let fixture: ComponentFixture<UserDialogueComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UserDialogueComponent ],
      imports: [
        SharedModule, HttpClientTestingModule, HttpClientTestingModule, RouterModule.forRoot([])
      ],
      providers: [ provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UserDialogueComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
