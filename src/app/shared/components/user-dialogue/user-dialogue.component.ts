import { Component, ElementRef, EventEmitter, Input, Optional, Output, ViewChild } from '@angular/core';
import { UserService } from '@core';
import { ControlValueAccessorComponent } from '@shared/components';
import { ControlContainer, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-user-dialogue',
  templateUrl: './user-dialogue.component.html',
  styleUrls: ['./user-dialogue.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: UserDialogueComponent
    }
  ]
})
export class UserDialogueComponent extends ControlValueAccessorComponent {
  @Input() showUserAvatar: boolean = true;
  @Input() handleSubmit: boolean = false;
  @Input() placeholder: string = null;
  @Input() minHeight: string = null;
  @Input() maxLength: number = 1000;

  @Output() submitEvent: EventEmitter<string> = new EventEmitter<string>();

  showingInput: boolean = false;
  @ViewChild('inputEle', {read: ElementRef, static: false}) inputEle: ElementRef<HTMLInputElement>;

  constructor(
    @Optional() protected controlContainer: ControlContainer,
    public userService: UserService
  ) {
    super(controlContainer);
  }

  get hasValue(): boolean {
    return this.controlValue?.length > 0;
  }

  showInput(val: boolean) {
    if (val && !this.control?.enabled) {
      return;
    }

    this.showingInput = val;

    if (val) {
      setTimeout(() => {
        if (this.inputEle?.nativeElement) {
          this.inputEle.nativeElement.focus();
        }

        if (this.controlValue?.trim().length == 0) {
          this.setControlValue('');
        }
      }, 100);
    }
  }

  onInputEnter(event: KeyboardEvent) {
    if (this.handleSubmit) {
      event.preventDefault();
      event.stopPropagation();
      this.inputEle.nativeElement.blur();
    }
  }

  onInputBlur() {
    if (this.handleSubmit) {
      this.submitEvent.emit(this.controlValue);
    }

    this.showInput(false);
  }
}
