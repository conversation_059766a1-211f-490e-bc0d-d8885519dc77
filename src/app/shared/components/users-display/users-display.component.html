<div class="users-container d-flex align-items-center justify-content-start flex-wrap"
     [ngClass]="getUsersContainerCssClass()" [ngStyle]="getUsersContainerStyle()">

  <ng-container *ngFor="let user of displayedUsers; trackBy: trackByUserId"
                [ngTemplateOutlet]="userItemTemplate" [ngTemplateOutletContext]="{user: user}"></ng-container>

  <ng-container *ngIf="isOverflowUsersDisplayed">
    <ng-container *ngFor="let user of overflowUsers; trackBy: trackByUserId"
                  [ngTemplateOutlet]="userItemTemplate" [ngTemplateOutletContext]="{user: user}"></ng-container>
  </ng-container>

  <ng-container *ngIf="hasOverflowUsers">
    <div *ngIf="!isOverflowUsersDisplayed" class="user-item user-item-action" (click)="onShowMoreUsersClicked()"
         [ngbTooltip]="'Show ' + overflowUsers.length + ' assigned user' + (overflowUsers.length > 1 ? 's that were' : ' that was') + ' hidden'"
         tooltipClass="white-tooltip">
      <div class="user-item-action-text">+{{ overflowUsers.length }}</div>
    </div>

    <div *ngIf="isOverflowUsersDisplayed" class="user-item user-item-action" (click)="onShowLessUsersClicked()">
      <div class="user-item-action-text">Show less</div>
    </div>
  </ng-container>

  <div [ngbTooltip]="addUserTooltipTemplate" tooltipClass="white-tooltip" container="body">
    <div class="button-main-primary button-small" #usersSelectTooltip="ngbTooltip"
         (hidden)="onUsersSelectHidden()" (shown)="onUsersSelectShown()" [ngbTooltip]="usersSelectTemplate"
         tooltipClass="white-tooltip tooltip-modal" triggers="click:blur" autoClose="outside" container="body"
         placement="bottom auto" [ngClass]="{'button-square p-spacing-none': !hasNoUsers}">
      <i class="user-item-action-icon far fa-plus"></i>
      <span class="user-item-action-text" *ngIf="hasNoUsers">{{addUserButtonTitle}}</span>
    </div>
  </div>
</div>

<ng-template #userItemTemplate let-user="user">
  <div [id]="getUserElementId(user)" class="user-item user-custom user-item-rounded flex-shrink-0">
    <app-user-avatar [user]="user" [hasSubTitle]="true" subtitleMaxWidth="150px"></app-user-avatar>
    <div *ngIf="canRemoveUsers" class="user-unassign m-l-spacing-xx-s cursor-pointer" (click)="unassignUser(user)">
      <i class="far fa-close"></i>
    </div>
  </div>
</ng-template>

<ng-template #usersSelectTemplate>
  <app-users-select [assignedUsers]="allAssignedUsers" [(searchTerm)]="usersSearchTerm"
                    (canceledEvent)="usersSelectTooltip.close(true)"
                    (addedUserEvent)="onAddedUserEvent($event, usersSelectTooltip)">
  </app-users-select>
</ng-template>


<ng-template #addUserTooltipTemplate>
  <div class="text-left p-spacing-sm">
    <div class="content-body-xsmall">
      Select users to add
    </div>
  </div>
</ng-template>
