@import 'scss/figma2023/index';
@import 'scss/layout2021/variables';

.users-container {
  min-height: 2.5rem;
  width: 100%;
  padding: $spacing-system-spacing-xx-s;
  gap: $spacing-system-spacing-sm;
  background: $colours-background-bg-secondary;
  @include border-radius($radius-sm);

  @import "scss/components/user_item";

  &.users-select-shown {
    background-color: $colour-grey-100;
    border-radius: $radius-sm;
  }

  &.users-are-calculating {
    overflow: hidden !important;
  }

  &.users-display-all {
    overflow: auto;
    max-height: 7.5rem;
  }

  &.users-display-less {
    max-height: 5rem;
  }
}

::ng-deep {
  .categories-text {
    li {
      text-align: left;
    }
  }

  app-users-select {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-content: flex-start;
    justify-content: flex-start;
    align-items: stretch;
    height: 100%;
  }
}
