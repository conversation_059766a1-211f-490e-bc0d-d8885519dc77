import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UsersDisplayComponent } from './users-display.component';
import { ControlContainer, FormGroup, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('UsersDisplayComponent', () => {
  let component: UsersDisplayComponent;
  let fixture: ComponentFixture<UsersDisplayComponent>;

  beforeEach(async () => {
    const formGroupDirective: FormGroupDirective = new FormGroupDirective([], []);
    formGroupDirective.form = new FormGroup({});

    await TestBed.configureTestingModule({
      declarations: [ UsersDisplayComponent ],
      providers: [
        {provide: ControlContainer, useValue: formGroupDirective}, provideMatomo({siteId: '', trackerUrl: '', disabled: true }) ],
      imports: [
        ReactiveFormsModule, SharedModule, NgbModule,
        HttpClientTestingModule, RouterModule.forRoot([])
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(UsersDisplayComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
