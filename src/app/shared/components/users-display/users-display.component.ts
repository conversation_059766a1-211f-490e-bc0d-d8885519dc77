import { AfterViewInit, Component, Injector, Input, OnD<PERSON>roy, OnInit, Optional } from '@angular/core';
import { TeamUser, ToastService, ToastTypeEnum, UserService } from '@core';
import { BehaviorSubject, Observable, of, Subscription, timer } from 'rxjs';
import { catchError, debounce, filter, tap } from 'rxjs/operators';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { ControlContainer, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ControlValueAccessorComponent } from '@shared/components';

@Component({
  selector: 'app-users-display',
  templateUrl: './users-display.component.html',
  styleUrls: ['./users-display.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: UsersDisplayComponent
    }
  ]
})
export class UsersDisplayComponent extends ControlValueAccessorComponent implements OnInit, OnD<PERSON>roy, AfterViewInit {

  @Input() container: HTMLElement;
  @Input() defaultDisplayRows: number = 2;
  @Input() updateUsersOnResize: boolean = false;
  @Input() updateUsersDelay: number = 1000;
  @Input() canRemoveUsers: boolean = true;
  @Input() addUserButtonTitle: string = 'Add user';
  allAssignedUsers: TeamUser[];
  displayedUsers: TeamUser[] = [];
  overflowUsers: TeamUser[] = [];
  isCalculatingUsersToDisplay = true;
  isOverflowUsersDisplayed: boolean = false;
  isUsersSelectShown = false;
  usersSearchTerm: string = null;
  private subscriptions = new Subscription();
  private updateDisplayedUsersSubject = new BehaviorSubject<number>(null);
  private resizeObserver: ResizeObserver = null;
  private checkContainerWidthIntervalId = null;
  private lastContainerWidth: number = null;
  private isUsersChanged = false;
  private openUsersSelectTimeoutId = null;
  private defaultItemHeight = 2.5;

  constructor(
    @Optional() protected controlContainer: ControlContainer,
    private userService: UserService,
    private toastService: ToastService
  ) {
    super(controlContainer);
  }

  get hasOverflowUsers(): boolean {
    return this.overflowUsers?.length > 0;
  }

  get hasNoUsers(): boolean {
    return !(this.allAssignedUsers?.length > 0);
  }

  get initialUserIds(): number[] {
    return this.controlValue ? this.controlValue as number[] : [];
  }

  ngOnInit(): void {
    const updateDisplayedUsers$ = this.updateDisplayedUsersSubject.asObservable()
      .pipe(
        filter(val => val !== null),
        tap(() => this.isCalculatingUsersToDisplay = true),
        debounce((val: number) => timer(val)),
      )
      .subscribe({
        next: (val) => {
          this.updateDisplayedUsers();
        }
      });
    this.subscriptions.add(updateDisplayedUsers$);

    const loadInitialUsers$ = this.loadInitialUsers()
      .pipe(
        tap(() => this.assignDisplayedUsers(this.updateUsersDelay))
      )
      .subscribe();
    this.subscriptions.add(loadInitialUsers$);
  }

  ngAfterViewInit() {
    if (this.container && this.updateUsersOnResize) {
      const self = this;
      this.checkContainerWidthIntervalId = setInterval(() => {
        if (self.container.clientWidth !== self.lastContainerWidth) {
          self.lastContainerWidth = self.container.clientWidth;
          self.displayAllUsers();
          self.updateDisplayedUsersSubject.next(1000);
        }
      }, 3000);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    if (this.checkContainerWidthIntervalId) {
      clearInterval(this.checkContainerWidthIntervalId);
      this.checkContainerWidthIntervalId = null;
    }

    if (this.openUsersSelectTimeoutId) {
      clearTimeout(this.openUsersSelectTimeoutId);
    }
  }

  unassignUser(user: TeamUser) {
    if (!this.canRemoveUsers) {
      return;
    }

    const newUserIds = this.initialUserIds.filter(id => id !== user.id);
    this.setControlValue(newUserIds, true);
    this.allAssignedUsers = this.allAssignedUsers.filter(u => u.id !== user.id);
    this.assignDisplayedUsers(100);
  }

  getUserElementId(user: TeamUser) {
    return `user-avatar-${user.id}`;
  }

  onShowMoreUsersClicked() {
    this.isOverflowUsersDisplayed = true;
  }

  onShowLessUsersClicked() {
    this.isOverflowUsersDisplayed = false;
  }

  getUsersContainerCssClass(): string {
    const usersSelectShownClass = this.isUsersSelectShown ? 'users-select-shown' : '';
    if (this.isCalculatingUsersToDisplay) {
      return `${usersSelectShownClass} users-are-calculating`;
    }

    if (this.hasOverflowUsers && !this.isOverflowUsersDisplayed) {
      return `${usersSelectShownClass} users-display-less`;
    }

    return `${usersSelectShownClass} users-display-all`;
  }

  getUsersContainerStyle() {
    if (this.isCalculatingUsersToDisplay) {
      return {'max-height': `${this.defaultItemHeight * this.defaultDisplayRows}rem`};
    }
  }

  onUsersSelectHidden() {
    if (this.isUsersChanged) {
      this.assignDisplayedUsers(100);
    }
    this.isUsersSelectShown = false;
  }

  onAddedUserEvent(user: TeamUser, usersSelectTooltip: NgbTooltip) {
    const updateUsersDelay = 100;
    usersSelectTooltip.close(false);
    this.isUsersChanged = true;
    const newUserIds = [...this.initialUserIds, user.id];
    this.setControlValue(newUserIds, true);
    this.allAssignedUsers.push(user);
    this.assignDisplayedUsers(updateUsersDelay);

    if (this.openUsersSelectTimeoutId) {
      clearTimeout(this.openUsersSelectTimeoutId);
    }

    this.openUsersSelectTimeoutId = setTimeout(() => {
      usersSelectTooltip.open();
    }, updateUsersDelay + Math.ceil(this.displayedUsers.length / 10) * 50);
  }

  onUsersSelectShown() {
    this.isUsersSelectShown = true;
  }

  trackByUserId(index: number, user: TeamUser) {
    return user.id;
  }

  private loadInitialUsers(): Observable<any> {
    this.isCalculatingUsersToDisplay = false;
    if (this.initialUserIds?.length > 0) {
      const payload = {
        id: 'in:' + this.initialUserIds.join(','),
        load_all: 1
      };
      return this.userService.getTeamUsers(payload)
        .pipe(
          tap(({users}) => this.allAssignedUsers = users),
          catchError((error) => {
            console.log(error);
            this.toastService.show({
              type: ToastTypeEnum.ERROR,
              header: 'Error in loading users',
              body: 'An error occurred while retrieving the initial users',
              delay: 5000
            });
            throw error;
          })
        );
    }
    this.allAssignedUsers = [];
    return of([]);
  }

  private assignDisplayedUsers(updateUsersDelay: number) {
    if (!this.allAssignedUsers) {
      return;
    }

    this.displayedUsers = [...this.allAssignedUsers];
    this.updateDisplayedUsersSubject.next(updateUsersDelay);
    this.isUsersChanged = false;
  }

  private calculateVisibleWidthForUsers() {
    if (!this.container) {
      return 0;
    }

    const extraSpace = 0.5 * 16 + 1; // space between users: spacing-sm (rem) * 16 (px)
    const userSelectorWidth = this.container.querySelector('.user-selector-ele')?.clientWidth ?? 80;
    const usersContainerEle = this.container.querySelector('.users-container') as HTMLElement;
    return usersContainerEle ? this.container.clientWidth - userSelectorWidth - extraSpace : 0;
  }

  private countMandatoryUsers() {
    return this.container.querySelectorAll('.user-mandatory-item').length;
  }

  private displayAllUsers() {
    this.displayedUsers = this.allAssignedUsers ? [...this.allAssignedUsers] : [];
    this.overflowUsers = [];
  }

  private updateDisplayedUsers() {
    if (!this.allAssignedUsers || !this.container) {
      return;
    }

    const containerWidth = this.container.clientWidth;
    const extraSpace = 0.5 * 16 + 1; // space between users: spacing-sm (rem) * 16 (px)
    const userElements = Array.from(this.container.querySelectorAll('.user-item'));
    let numberUsersToDisplay = 0;
    let currentUserIndex = 0;
    let startUserIndex = 0;

    for (let i = 0; i < this.defaultDisplayRows - 1; i++) {
      let occupiedWidthPerRow = 0;

      // Calculating for first row of users. This row doesn't contain user selector element.
      for (currentUserIndex = startUserIndex; currentUserIndex < userElements.length; currentUserIndex++) {
        const userEle = userElements[currentUserIndex] as HTMLElement;
        occupiedWidthPerRow += (userEle.scrollWidth + extraSpace);
        if (occupiedWidthPerRow > containerWidth) {
          break;
        }
        numberUsersToDisplay += 1;
      }
      startUserIndex = currentUserIndex;
    }

    let remainingWidthSecondRow = this.calculateVisibleWidthForUsers();

    for (let i = currentUserIndex; i < userElements.length; i++) {
      const userEle = userElements[i] as HTMLElement;
      remainingWidthSecondRow -= (userEle.scrollWidth + extraSpace);
      if (remainingWidthSecondRow <= 0) {
        break;
      }
      numberUsersToDisplay += 1;
    }

    numberUsersToDisplay -= this.countMandatoryUsers();

    this.displayedUsers = [...this.allAssignedUsers.slice(0, numberUsersToDisplay)];
    this.overflowUsers = [...this.allAssignedUsers.slice(numberUsersToDisplay)];
    this.isCalculatingUsersToDisplay = false;
  }
}
