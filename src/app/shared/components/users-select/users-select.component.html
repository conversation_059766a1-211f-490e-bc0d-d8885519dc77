<div class="users-select-container">
  <div class="users-select-search">
    <input #searchInputEle placeholder="Search for users" [formControl]="searchInputFormControl" appAutofocus/>
  </div>

  <hr class="users-select-divider">

  <div class="users-select-main-content flex-grow-1 overflow-y-auto">
    <ng-container *ngIf="!isLoadingUsers">
      <div class="w-100 flex-grow-1 overflow-y-auto">
        <div *ngIf="!hasNoAvailableUsers" class="users-select-items w-100 h-100">
          <div *ngFor="let user of availableUsers;" [id]="getUserElementId(user)" (click)="onUserItemClicked(user)"
               class="user-item-container w-100">
            <div class="user-item user-custom w-100">
              <app-user-avatar [user]="user" [hasSubTitle]="true" class="w-100"></app-user-avatar>
            </div>
          </div>
        </div>

        <div class="users-select-no-results w-100 d-flex flex-column align-items-center justify-content-start"
             *ngIf="hasNoAvailableUsers">
          <i class="fa-light fa-user-group fa-1.5x fa-border"></i>
          <div class="content-body-small text-center m-t-spacing-md m-b-spacing-lg" [innerHTML]="getNoUsersMessage()"></div>
        </div>
      </div>
    </ng-container>

    <app-spinner *ngIf="isLoadingUsers"></app-spinner>
  </div>
</div>
