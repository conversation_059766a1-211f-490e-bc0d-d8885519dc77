@import 'scss/figma2023/index';
@import 'scss/layout2021/variables';

.users-select-container {
  width: 100%;
  max-height: 17.5rem;
  display: flex;
  padding: $spacing-system-spacing-none;
  gap: $spacing-system-spacing-big;
  justify-content: flex-start;
  align-content: flex-start;
  flex-direction: column;
  align-items: flex-start;

  @import "scss/components/user_item";

  .users-select-search {
    width: 100%;
    display: flex;
    padding: $spacing-system-spacing-none;
    gap: $spacing-system-spacing-xx-s;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;

    input {
      width: 100%;
      padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
      border-radius: $radius-sm;
      border: 0.0625rem solid $colour-grey-300;
      background: $colour-grey-100;

      color: $colour-grey-900;
      font-family: $font-open-sans-regular;
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;

      &::-ms-input-placeholder, &::placeholder {
        color: $colour-grey-500;
        font-family: $font-open-sans-regular;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 1.5rem;
      }

      &:focus, &:active {
        border: 0.0625rem solid $colour-grey-900;
        background: $colour-grey-050;
      }
    }
  }

  .users-select-divider {
    height: 0.0625rem;
    width: 100%;
    margin: 0;
    padding: 0;
    background: $colour-grey-300;
  }

  .users-select-main-content {
    width: 100%;
    display: flex;
    padding: $spacing-system-spacing-none;
    gap: $spacing-system-spacing-big;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    align-content: flex-start;

    .users-select-items {
      display: flex;
      padding: $spacing-system-spacing-none;
      gap: $spacing-system-spacing-none;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
      align-content: flex-start;
      overflow: auto;

      .user-item-container {
        padding: $spacing-system-spacing-xx-s $spacing-system-spacing-md;
        max-width: 100%;
      }
    }

    .users-select-actions-container {
      display: flex;
      padding: $spacing-system-spacing-xx-s;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      border-radius: $radius-sm;
      background: $colours-background-bg-secondary;

      .users-select-actions {
        display: flex;
        justify-content: flex-end;
        align-items: flex-start;
        gap: $spacing-system-spacing-sm;
      }
    }

    .users-select-no-results {
      margin-top: $spacing-system-spacing-md;

      .fa-user-group {
        color: $colour-blue-brand-500;
        background: $colour-blue-brand-200;
        --fa-border-radius: 50%;
        --fa-border-width: 0.38rem;
        --fa-border-color: #E0F7FF;
        --fa-border-padding: 1rem;
      }
    }
  }
}

::ng-deep {
  .users-select-container {
    app-spinner {
      align-self: center;

      img {
        height: 5rem;
        width: 5rem;
      }
    }
  }
}
