import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { Observable, of, Subscription } from 'rxjs';
import { TeamUser, ToastService, ToastTypeEnum, UserService } from '@core';
import { catchError, finalize, map, tap } from 'rxjs/operators';

@Component({
  selector: 'app-users-select',
  templateUrl: './users-select.component.html',
  styleUrls: ['./users-select.component.scss']
})
export class UsersSelectComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() assignedUsers: TeamUser[] = [];
  @Input() searchTerm: string = null;
  @Output() searchTermChange = new EventEmitter<string>();
  @Output() canceledEvent = new EventEmitter<boolean>();
  @Output() addedUserEvent = new EventEmitter<TeamUser>();

  @ViewChild('searchInputEle') searchInputEle: ElementRef;

  searchInputFormControl = new FormControl(null);
  allUsers: TeamUser[] = [];
  availableUsers: TeamUser[] = [];
  isLoadingUsers: boolean = false;

  private subscriptions = new Subscription();

  constructor(
    private userService: UserService,
    private toastService: ToastService
  ) {
  }

  get searchInputValue(): string {
    return this.searchInputFormControl.value?.trim() || '';
  }

  get hasNoAvailableUsers(): boolean {
    return this.availableUsers?.length === 0;
  }

  ngOnInit() {
    if (this.searchTerm) {
      this.searchInputFormControl.setValue(this.searchTerm);
    }

    const valueChanges$ = this.searchInputFormControl.valueChanges
      .pipe(
        map(value => value?.trim()?.toLowerCase() || ''),
        tap((value) => {
          this.searchTerm = value;
          this.searchTermChange.emit(value);
          this.filterAvailableUsers(value);
        })
      )
      .subscribe();
    this.subscriptions.add(valueChanges$);

    const loadUsers$ = this.loadUsers().subscribe();
    this.subscriptions.add(loadUsers$);
  }

  ngAfterViewInit() {
    this.searchInputEle.nativeElement.focus();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getUserElementId(user: TeamUser) {
    return `user-avatar-${user.id}`;
  }

  onUserItemClicked(user: TeamUser) {
    this.addedUserEvent.emit(user);
  }

  getNoUsersMessage() {
    if (this.searchInputValue.length === 0) {
      return 'There are no users available';
    }

    if (this.isUserAssigned(this.searchInputValue, true)) {
      return `The user <b>${this.searchInputValue}</b> is already assigned`;
    }

    if (this.isUserAssigned(this.searchInputValue, false)) {
      return `The users contain <b>${this.searchInputValue}</b> is already assigned`;
    }

    return 'No users found';
  }

  isUserAssigned(term: string, isMatchAllName: boolean): boolean {
    term = term?.toLowerCase();
    if (isMatchAllName) {
      return this.assignedUsers.some(user => {
        if (user.first_name || user.last_name) {
          const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim().toLowerCase();
          if (fullName === term) {
            return true;
          }
        }

        if (user['name'] && user['name'].toLowerCase() === term) {
          return true;
        }

        return user.email?.toLowerCase() === term;
      });
    } else {
      return this.assignedUsers.some(user => {
        if (user.first_name || user.last_name) {
          const fullName = `${user.first_name || ''} ${user.last_name || ''}`.trim().toLowerCase();
          if (fullName.includes(term)) {
            return true;
          }
        }

        if (user['name'] && user['name'].toLowerCase().includes(term)) {
          return true;
        }

        return user.email?.toLowerCase()?.includes(term);
      });
    }
  }

  private loadUsers(): Observable<any> {
    this.isLoadingUsers = true;

    const obs = this.userService.teamUsers?.length > 0 ? of({users: this.userService.teamUsers}) :
      this.userService.getTeamUsers({'load_all': 1}, true);

    return obs.pipe(
      tap(({users}) => this.allUsers = users),
      tap(() => this.filterAvailableUsers(this.searchInputValue)),
      finalize(() => this.isLoadingUsers = false),
      catchError((error) => {
        console.log(error);
        this.toastService.show({
          type: ToastTypeEnum.ERROR,
          header: 'Error in loading users',
          body: 'An error occurred while retrieving the users',
          delay: 5000
        });
        throw error;
      })
    );
  }

  private updateAvailableUsers() {
    const assignedUserIds = this.assignedUsers.map(user => user.id);
    this.availableUsers = this.allUsers.filter(user => !assignedUserIds.includes(user.id));
  }

  private filterAvailableUsers(searchTerm: string) {
    this.updateAvailableUsers();

    if (searchTerm?.trim()?.length > 0) {
      searchTerm = searchTerm.trim().toLowerCase();
      this.availableUsers = this.availableUsers.filter(user => {
        if (user.email && user.email.toLowerCase().includes(searchTerm)) {
          return true;
        }

        const fullName = `${user.first_name || ''} ${user.last_name || ''}`.toLowerCase();
        return fullName.includes(searchTerm);
      });
    }
  }
}
