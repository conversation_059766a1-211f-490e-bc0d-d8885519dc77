<div class="weighting-bar-container p-3">
  <p class="importance-text">Importance</p>

  <div class="d-flex justify-content-between align-items-center w-100">
    <div class="flex-fill">
      <div class="wb-slider-label d-flex justify-content-between w-100">
        <span>Text</span>
        <span>Patent number</span>
      </div>
      <ngx-slider [options]="sliderSetting" [(value)]="weighting" (userChangeEnd)="onChange()"
                  class="w-100 m-0 p-0"></ngx-slider>

      <div class="row wb-slider-label align-items-center w-100 p-0 m-0">
        <div class="col wb-slider-label ps-0">Max.</div>
        <div class="col-2 wb-slider-label text-center">Equal</div>
        <div class="col wb-slider-label pe-0 text-end">Max.</div>
      </div>
    </div>
  </div>
</div>
