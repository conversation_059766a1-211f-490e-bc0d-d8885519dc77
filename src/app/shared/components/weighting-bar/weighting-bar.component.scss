@import 'scss/figma2023/variables';
@import 'scss/layout2021/variables';

.weighting-bar-container {
  border-radius: 8px;
  border: 1px solid $colours-border-subtle;

  .importance-text {
    font-size: 1rem;
    font-weight: 700;
    color: $color-text-04;
  }

  .wb-slider-label {
    font-size: 0.875rem;
    color: $color-text-03;
    line-height: 20px;
  }

  .wb-weight-label {
    font-size: 1rem;
    color: $color-text-03 !important;
  }

  ::ng-deep {
    .ngx-slider {
      .ngx-slider-bar {
        background-color: $input-border-color;
        opacity: 0.3;
        height: 14px;
        top: 12px;
      }

      .ngx-slider-tick {
        background-color: $input-border-color;
      }

      .ngx-slider-bar-wrapper {
        margin-top: -16px !important;
      }

      .ngx-slider-pointer::after {
        background-color: $colours-content-content-active !important;
      }
    }
  }
}
