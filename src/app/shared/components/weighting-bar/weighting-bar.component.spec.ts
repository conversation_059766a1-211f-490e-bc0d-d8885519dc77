import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { WeightingBarComponent } from './weighting-bar.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';

describe('WeightingBarComponent', () => {
  let component: WeightingBarComponent;
  let fixture: ComponentFixture<WeightingBarComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [WeightingBarComponent],
      imports: [NgxSliderModule]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WeightingBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
