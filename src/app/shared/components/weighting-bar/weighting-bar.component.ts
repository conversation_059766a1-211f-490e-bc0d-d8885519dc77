import { Component, EventEmitter, Input, Output } from '@angular/core';
import { LabelType } from '@angular-slider/ngx-slider';

@Component({
  selector: 'app-weighting-bar',
  templateUrl: './weighting-bar.component.html',
  styleUrls: ['./weighting-bar.component.scss']
})
export class WeightingBarComponent {

  @Output()
  weightingChange = new EventEmitter<number>();

  @Input()
  weighting: number;

  @Input()
  sliderSetting = {
    floor: 0,
    ceil: 8,
    ticksArray: [0, 1, 2, 3, 4, 5, 6, 7, 8],
    translate: (value: number, label: LabelType): string => {
      return '';
    }
  };

  constructor() {
  }

  getTooltipText(): string {
    return `This slider allows you to specify the relevance or importance of
    patent numbers and text respectively for your query. If you move the slider to the left,
    the text will be emphasized while moving the button to the right will emphasize the
    patent publication number(s) in your search.`;
  }

  onChange() {
    this.weightingChange.emit(this.weighting);
  }
}
