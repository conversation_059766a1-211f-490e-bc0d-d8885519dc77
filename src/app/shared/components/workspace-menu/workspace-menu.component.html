<div class="bg-gray">
  <!-- Nav tabs -->
  <ul ngbNav class="nav-tabs main-tabs nav-1 container mb-0 d-flex justify-content-center" [activeId]="workspaceTab">
    <li [ngbNavItem]="'team'" *ngIf="userService.getUser()?.profile?.company_id">
      <a ngbNavLink routerLink="/users" >My team</a>
    </li>
    <li [ngbNavItem]="'applicant-aliases'">
      <a ngbNavLink routerLink="/applicant-aliases" >Applicant aliases</a>
    </li>
    <li [ngbNavItem]="'tags'"  *ngIf="userService.hasTagFeature()" >
      <a ngbNavLink routerLink="/tags" >Tags manager</a>
    </li>
    <li [ngbNavItem]="'ratings'" *ngIf="userService.hasTaskFeature()">
      <a ngbNavLink routerLink="/ratings" >Ratings</a>
    </li>

  </ul>
</div>

