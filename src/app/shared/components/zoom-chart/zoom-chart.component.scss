@import 'scss/layout2021/variables';

.zoom-chart-dialog {
  width: 97%;
  max-width: none;
  margin: 1% auto;

  .modal-body {
    padding: 0;
    margin: 0;
  }

  .chart-container {
    margin: 0 auto;
    padding: 0 15px 15px 15px;
    height: calc(90vh);
    min-height: calc(90vh);
    max-height: calc(90vh);

    ::ng-deep {
      app-base-card-chart {
        height: 100%;
        .chart-item {
          height: 100%;
        }
      }

      .chart-slider {
        + app-base-card-chart {
          height: calc(100% - 54px);
          .chart-item {
            height: calc(100% - 54px);
          }
        }

        &.chart-slider-lg + app-base-card-chart {
          height: calc(100% - 86px);

          .chart-item {
            height: calc(100% - 86px);
          }
        }

        .label-slider {
          color: $color-text-03 !important;
        }

        .ngx-slider {
          .ngx-slider-limit, .ngx-slider-bubble {
            color: $color-text-03 !important;
          }
        }
      }
    }
  }
}
