import { Component, ComponentRef, Input, On<PERSON><PERSON>roy, OnInit, Type, ViewChild, ViewContainerRef } from '@angular/core';
import { cloneDeep } from 'lodash';
import * as Highcharts from 'highcharts';
import { BaseStoreService } from '@core';
import { Subscription } from 'rxjs';

declare var $: any;

@Component({
  selector: 'app-zoom-chart',
  templateUrl: './zoom-chart.component.html',
  styleUrls: ['./zoom-chart.component.scss']
})
export class ZoomChartComponent implements OnInit, OnDestroy {
  @Input() showFavoriteOption = false;
  @Input() storeService: BaseStoreService;

  @ViewChild('container', {read: ViewContainerRef, static: true}) viewContainerRef: ViewContainerRef;

  private subscriptions = new Subscription();

  constructor() {
  }

  ngOnInit() {
    const self = this;

    $('#zoom').on('hide.bs.modal', function () {
      if (self.viewContainerRef) {
        self.viewContainerRef.clear();
      }
    });

    const expandingChartComponent$ = this.storeService.expandingChartComponent$.subscribe({
      next: (chart) => {
        if (chart && chart instanceof Type) {
          this.viewContainerRef.clear();
          const componentRef: ComponentRef<any> = this.viewContainerRef.createComponent(<Type<any>>chart);
          componentRef.instance.showFavoriteOption = this.showFavoriteOption;
          componentRef.instance.showZoomOption = false;
          componentRef.instance.storeService = this.storeService;
          componentRef.instance.chartItemHeight = '100%';
          componentRef.instance.chartOptions = cloneDeep(componentRef.instance.chartOptions);

          $('#zoom').on('shown.bs.modal', function () {
            $('[data-chart-area="similarities"]').css('pointer-events', self.storeService.chartFilterDisabled ? 'none' : 'auto');
            Highcharts.charts.forEach((c) => {
              if (c) {
                try {
                  c.reflow();
                } catch (e) {
                  console.warn(e);
                }
              }
            });
          });

          $('#zoom').modal('show');

          this.storeService.expandingChartComponent = null;
        }
      }
    });
    this.subscriptions.add(expandingChartComponent$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
