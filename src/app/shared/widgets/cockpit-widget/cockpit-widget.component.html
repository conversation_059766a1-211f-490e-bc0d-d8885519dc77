<div #widgetEle class="widget-container" [ngClass]="widgetClass + ' widget-container-' + cockPitContentType"
    (scroll)="onScrolled(widgetEle)">
    <span class="widget-no-content mt-3" *ngIf="!isLoading && list?.length === 0">
        No {{cockPitContentType | lowercase}} currently
    </span>
    <div class="widget-content" *ngIf="!isLoading">
        <div *ngFor="let item of list" [ngClass]="'widget-content-' + cockPitContentType | lowercase">
            <a class="widget-item p-0 m-3" href="javascript:void(0)" (click)="onOpenModel(modalWidget, item)">
                <span>
                    {{getTitle(item)}}
                </span>
                <div class="widget-date" *ngIf="item?.Date">
                    {{item.Date | dateFormat: 'ShortDate'}}
                </div>
            </a>
        </div>
    </div>
    <div class="text-center" *ngIf="isLoading">
        <img src="assets/images/octimine_blue_spinner.gif" class="loading-spinner">
    </div>

    <div class="row mt-4" [hidden]="loadOnScrolling || list?.length === 0">
        <div class="col-12 text-end widget-load-more">
            <a href="javascript:void(0)" class="caption" *ngIf="loadMoreBtn" (click)="loadNext()">Load more</a>
        </div>
    </div>
</div>
<ng-template #modalWidget>
    <div class="modal-header ps-2 pe-2" *ngIf="currentContent">
        <div class="modal-title d-flex flex-wrap text-start">
            <div class="w-100">
                {{getTitle(currentContent)}}
            </div>
            <div class="widget-subtitle" *ngIf="currentContent?.Author && currentContent?.Date">
                {{currentContent?.Author}} - <span
                    style="font-size: 0.8rem;">{{currentContent?.Date | dateFormat: 'ShortDate'}}</span>
            </div>
        </div>
        <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')"
            tabindex="-1"></button>
    </div>
    <div class="modal-body ps-2 pe-2" *ngIf="currentContent">
        <div class="widget-model-text ps-3 pe-3" [innerHTML]="getDescription(currentContent)"></div>
    </div>
</ng-template>
