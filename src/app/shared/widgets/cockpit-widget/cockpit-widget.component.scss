@import 'scss/layout2021/variables';

.widget-item {
  padding: 1rem;
  cursor: pointer;
  font-size: 0.88rem;
  font-weight: bold;
  text-decoration: none;
  span:hover {
    color: $color-text-05;
  }
}

.widget-date {
  color: $color-text-03;
  font-size: 0.8rem;
}

.loading-spinner {
  height: 50px;
}

.widget-subtitle {
  font-size: 1rem;
  font-family: 'Open Sans Regular';
}

.widget-model-text {
  word-break: normal;
  text-align: justify;
  text-justify: inter-word;
}

.widget {
  &-container{
    max-height: 305px;
    scroll-behavior: smooth;
  }
  &-content {
    &-news {
      display: flex;
      flex-wrap: wrap;
      border-bottom: 1px solid #dee2e6;
    }

    &-faq {
      &:before {
        content: "• ";
      }

      .widget-date {
        display: none;
      }
    }
  }
}
.widget-load-more {
    font-size: .875rem;
}
