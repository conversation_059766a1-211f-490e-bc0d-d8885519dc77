import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CockpitWidgetComponent } from './cockpit-widget.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';

describe('CockpitWidgetComponent', () => {
  let component: CockpitWidgetComponent;
  let fixture: ComponentFixture<CockpitWidgetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CockpitWidgetComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([{path: 'launchpad', component: CockpitWidgetComponent}])
      ],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CockpitWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
