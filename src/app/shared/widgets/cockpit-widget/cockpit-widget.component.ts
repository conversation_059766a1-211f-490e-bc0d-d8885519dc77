import { Component, ElementRef, Input, OnDestroy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { CockpitService, PaginationMetadata } from '@core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, take } from 'rxjs/operators';
import { ContentTypeEnum } from '@core/services/cockpit/types';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-cockpit-widget',
  templateUrl: './cockpit-widget.component.html',
  styleUrls: ['./cockpit-widget.component.scss']
})
export class CockpitWidgetComponent implements OnInit, OnDestroy {

  @Input() loadOnScrolling = false;
  @Input() loadMoreBtn = true;
  @Input() cockPitContentType: ContentTypeEnum;
  @Input() modelSize = 'lg';
  @Input() widgetClass = '';
  @Input() pageSize = 5;

  isLoading = false;
  list = [];
  pagination: PaginationMetadata;
  currentContent;
  contentText: SafeHtml;
  modal: NgbActiveModal = null;

  @ViewChild('widgetEle') widgetEle: ElementRef;

  private loadNextSubject = new BehaviorSubject<boolean>(false);
  private subscriptions = new Subscription();

  constructor(
    private cockpitService: CockpitService,
    private sanitizer: DomSanitizer,
    private modalService: NgbModal, ) {
    this.pagination = {
      page_size: this.pageSize,
      current_page: 1,
      total_hits: 0,
      last_page: 1,
      origin_total_hits: 0
    };
  }

  ngOnInit(): void {
    this.loadList();
    if (this.loadOnScrolling) {
      this.loadNextOnScrolling();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onScrolled(ele: HTMLUListElement) {
    const shouldScroll = ele.scrollHeight - ele.scrollTop - 3 <= ele.clientHeight;
    this.loadNextSubject.next(shouldScroll);
  }
  loadList() {
    switch (this.cockPitContentType) {
      case 'NEWS':
        this.loadNews();
        break;
      case 'FAQ':
        this.loadFAQS();
        break;
      default:
        console.error('no widget type');
        break;
    }
  }
  buildPayload(): {} {
    const payload = {};
    if (this.pageSize > 0) {
      const limit = this.pagination.page_size * this.pagination.current_page;
      payload['limit'] = limit;
      payload['skip'] = 0;
    }
    return payload;
  }

  loadNews() {
    this.isLoading = true;
    const limit = this.pagination.page_size * this.pagination.current_page;
    const params = this.buildPayload();
    params['sort'] = '{"Date": -1}';
    const scrollTo = !!(this.widgetEle?.nativeElement.scrollHeight) ? this.widgetEle?.nativeElement.scrollHeight - 100 : 0;
    const getNews$ = this.cockpitService.getNews(params)
      .pipe(
        finalize(() => {
          this.isLoading = false;
          setTimeout(() => {
            this.widgetEle.nativeElement.scrollTop = scrollTo;
          });
        }), take(1)
      )
      .subscribe({
        next: news => {
          this.list = news;
          this.pagination.total_hits = news.length;
          if (limit === news.length) {
            this.pagination.last_page++;
          } else {
            this.loadMoreBtn = false;
          }
        }
      });
    this.subscriptions.add(getNews$);
  }

  loadFAQS() {
    this.isLoading = true;
    const limit = this.pagination.page_size * this.pagination.current_page;
    const params = this.buildPayload();
    const getFAQS$ = this.cockpitService.getFAQS(params)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        }),
        take(1)
      )
      .subscribe({
        next: faqs => {
          this.list = faqs;
          this.pagination.total_hits = faqs.length;
          if (limit === faqs.length) {
            this.pagination.last_page++;
          } else {
            this.loadMoreBtn = false;
          }
        }
      });
    this.subscriptions.add(getFAQS$);
  }

  getTitle(content) {
    switch (this.cockPitContentType) {
      case 'NEWS':
        return content?.Title;
      case 'FAQ':
        return content?.Question;
      default:
        console.error('no widget type');
        return '';
    }
  }

  getDescription(content) {
    switch (this.cockPitContentType) {
      case 'NEWS':
        return content?.Text;
      case 'FAQ':
        return content?.Answer;
      default:
        console.error('no widget type');
        return '';
    }
  }


  private loadNextOnScrolling() {
    const loadNext$ = this.loadNextSubject.asObservable()
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe({
        next: val => {
          if (val) {
            this.loadNext();
          }
        }
      });
    this.subscriptions.add(loadNext$);
  }

  loadNext() {
    if (!this.isLoading &&
      this.pagination &&
      this.pagination.current_page < this.pagination.last_page) {
      this.pagination.current_page += 1;
      this.loadList();
    }
  }

  onOpenModel(modalContent: TemplateRef<any>, content: any) {
    this.currentContent = content;
    this.contentText = this.sanitizer.bypassSecurityTrustHtml(content.Text);
    this.modal = this.modalService.open(modalContent, { size: this.modelSize });
  }

}
