<div #widgetEle class="widget-container d-flex flex-column align-items-stretch pb-1" (scroll)="onScrolled(widgetEle)">
    <span class="widget-no-content mt-2" *ngIf="!loadListErrorMsg && !isLoading && list?.length === 0">Here, you will be able to see the latest results of your monitoring profiles. Find out more <a href='/monitor'>here</a>.</span>
    <span class="widget-no-content mt-2" *ngIf="loadListErrorMsg && !isLoading">
        <app-alert type="warning" [message]="loadListErrorMsg" [hideCloseBtn]="true"></app-alert>
    </span>
    <div class="row mt-1 widget-content widget-underline" *ngFor="let item of list">
        <div class="col-12 pt-2 pb-2">
            <a class="open-sans-bold d-block widget-content-title text-decoration-none mb-1"
                [href]="getProfileLink(item)">{{ item?.name }}</a>
            <div class="d-flex justify-content-between">
                <div >
                    <div class="short-char" *ngIf="item?.last_monitor_run?.status==='Finished' || item?.last_monitor_run?.status==='Error' ">
                        <a class="me-2" [ngClass]="{'color-3': item?.last_monitor_run?.number_of_documents?.MACHINE_LEARNING===0}" ngbTooltip="Deep learning"
                        [hidden]="item?.legal_status_active">
                            <span class="badge me-1"
                                [ngClass]="item?.last_monitor_run?.ml_status!=='Finished' ? 'badge-light' : 'badge-primary'">DL</span>
                            {{item?.last_monitor_run?.number_of_documents?.MACHINE_LEARNING}}
                        </a>
                        <a class="me-2" [ngClass]="{'color-3': item?.last_monitor_run?.number_of_documents?.SEMANTIC===0}" ngbTooltip="Semantic"
                        [hidden]="item?.legal_status_active">
                            <span class="badge me-1"
                                [ngClass]="item?.last_monitor_run?.semantic_status!=='Finished' ? 'badge-light' : 'badge-primary'">S</span>
                            {{item?.last_monitor_run?.number_of_documents?.SEMANTIC}}
                        </a>
                        <a class="me-2" [ngClass]="{'color-3': item?.last_monitor_run?.number_of_documents?.BOOLEAN===0}" ngbTooltip="Boolean"
                        [hidden]="item?.legal_status_active">
                            <span class="badge me-1"
                                [ngClass]="item?.last_monitor_run?.boolean_status!=='Finished' ? 'badge-light' : 'badge-primary'">B</span>
                            {{item?.last_monitor_run?.number_of_documents?.BOOLEAN}}
                        </a>
                        <a class="me-2" [ngClass]="{'color-3': item?.last_monitor_run?.number_of_documents?.LEGAL_STATUS===0}" ngbTooltip="Patent list"
                        [hidden]="!item?.legal_status_active">
                            <span class="badge badge-primary me-1">L</span>
                            {{item?.last_monitor_run?.number_of_documents?.LEGAL_STATUS}}
                        </a>
                    </div>
                    <div class="short-char" *ngIf="item?.last_monitor_run?.status==='Pending'">
                      <span class="badge me-1 badge-secondary">Pending</span>
                    </div>
                    <div class="short-char" *ngIf="item?.last_monitor_run?.status==='Processing'">
                      <span class="badge me-1 badge-primary">Processing</span>
                    </div>
                </div>
                <div class="email-sent" *ngIf="item?.last_monitor_run?.email_sent">
                    {{item?.last_monitor_run?.email_sent | dateFormat: 'Date'}}
                </div>
                <div class="complete-profile-message" *ngIf="!item?.last_monitor_run">Profile setup incomplete</div>
                <div class="complete-profile-button" *ngIf="!item?.last_monitor_run">
                    <a class="btn btn-primary-outline" [href]="'/monitor/profile/'+ item.id">Finish setup</a>
                </div>
            </div>
        </div>
    </div>
    <div class="text-center" *ngIf="isLoading">
        <img src="assets/images/octimine_blue_spinner.gif" class="loading-spinner">
    </div>
    <div class="row" [hidden]="loadOnScrolling || list?.length === 0">
        <div class="col-12 pt-1" [ngClass]="showAllBtn ? 'text-start' : 'text-end'">
            <a href="javascript:void(0)" *ngIf="loadMoreBtn" (click)="loadNext()">Load more</a>
            <a routerLink="/monitor" *ngIf="showAllBtn"><i class="fa fa-angle-right" aria-hidden="true"></i> View all</a>
        </div>
    </div>
</div>
