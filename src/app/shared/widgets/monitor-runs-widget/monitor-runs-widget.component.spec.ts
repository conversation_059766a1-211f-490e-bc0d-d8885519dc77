import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MonitorRunsWidgetComponent } from './monitor-runs-widget.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { provideMatomo } from 'ngx-matomo-client';

describe('MonitorRunsWidgetComponent', () => {
  let component: MonitorRunsWidgetComponent;
  let fixture: ComponentFixture<MonitorRunsWidgetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MonitorRunsWidgetComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers:[provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MonitorRunsWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
