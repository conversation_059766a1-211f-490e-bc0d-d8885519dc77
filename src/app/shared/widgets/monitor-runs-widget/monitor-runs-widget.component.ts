import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MonitorService, PaginationMetadata } from '@core';
import { BehaviorSubject, of, Subscription } from 'rxjs';
import { catchError, debounceTime, distinctUntilChanged, finalize, take } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-monitor-runs-widget',
  templateUrl: './monitor-runs-widget.component.html',
  styleUrls: ['./monitor-runs-widget.component.scss']
})
export class MonitorRunsWidgetComponent implements OnInit, OnDestroy {
  @Input() loadOnScrolling = false;
  @Input() loadMoreBtn = false;
  @Input() showAllBtn = true;

  isLoading: boolean = false;
  list = [];
  pagination: PaginationMetadata;
  pageSize = 5;

  loadListErrorMsg: string = null;

  private loadNextSubject = new BehaviorSubject<boolean>(false);
  private subscriptions = new Subscription();

  constructor(
    private widgetService: MonitorService) { }

  ngOnInit(): void {
    this.loadList();
    if (this.loadOnScrolling) {
      this.loadNextOnScrolling();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  loadList() {
    this.isLoading = true;
    this.loadListErrorMsg = null;
    const payload = this.buildPayload(this.pagination ? this.pagination.current_page : 1);
    const getWidgetProfiles$ = this.widgetService.getWidgetProfiles(payload, false)
      .pipe(
        take(1),
        catchError((err) => {
          console.warn(err);
          if (err instanceof HttpErrorResponse && err.status === 403) {
            this.loadListErrorMsg = 'Sorry, you don\'t have permission to access this page. If your account permissions ' +
              'changed recently, try logging in again. Otherwise, reach out to us at ' +
              '<a href="mailto:<EMAIL>"><EMAIL></a> or by using our live chat support.';
          } else {
            this.loadListErrorMsg = 'Error in loading the monitor runs';
          }
          return of(null);
        }),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: data => {
          if (data) {
            this.pagination = data.page;
            this.list = [...this.list, ...data.monitor_profiles];
          }
        },
        error: error => {
          console.warn(error);
        }
      });
    this.subscriptions.add(getWidgetProfiles$);
  }


  private buildPayload(page: number): any {
    const payload = {
      page_size: this.pageSize,
      page: page,
      sort_by: 'created_at',
      sort_order: 'desc'
    };
    return payload;
  }

  onScrolled(ele: HTMLUListElement) {
    const shouldScroll = ele.scrollHeight - ele.scrollTop - 3 <= ele.clientHeight;
    this.loadNextSubject.next(shouldScroll);
  }

  private loadNextOnScrolling() {
    const loadNext$ = this.loadNextSubject.asObservable()
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe({
        next: val => {
          if (val) {
            this.loadNext();
          }
        }
      });
    this.subscriptions.add(loadNext$);
  }

  getProfileLink(item){
    return (`/monitor/profile/${item.id}` + (item.last_monitor_run ? `/result?runID=${item.last_monitor_run.id}` : ''));
  }

  loadNext() {
    if (!this.isLoading &&
      this.pagination &&
      this.pagination.current_page < this.pagination.last_page) {
      this.pagination.current_page += 1;
      this.loadList();
    }
  }

}
