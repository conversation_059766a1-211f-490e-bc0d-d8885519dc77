<div #widgetEle class="widget-container d-flex flex-column align-items-stretch" (scroll)="onScrolled(widgetEle)">
    <span class="widget-no-content mt-3" *ngIf="!loadSearchHistoryErrorMsg && !isLoading && listHistory?.length === 0">Start a new <a href='/search'>semantic</a> or <a href='/boolean'>boolean</a> search.</span>
    <span class="widget-no-content mt-3" *ngIf="loadSearchHistoryErrorMsg && !isLoading">
      <app-alert type="warning" [message]="loadSearchHistoryErrorMsg" [hideCloseBtn]="true"></app-alert>
    </span>
    <div class="row mt-3 widget-content" *ngFor="let item of listHistory">
        <div class="col-12 pt-2 pb-2">
            <a class="open-sans-bold d-block widget-content-title mb-2 text-decoration-none" href="javascript:void(0)"
                (click)="loadSearch(item)">
              <div class="text-ellipsis text-ellipsis-1">{{ textAndNumberSearch(item) }}</div>
            </a>
            <span class="widget-content-subtitle color-3">{{ getType(item) }} &#9679;
                {{ item.created_at | dateFormat: 'ShortDateTime' }}</span>
        </div>
    </div>
    <div class="text-center" *ngIf="isLoading">
        <img src="assets/images/octimine_blue_spinner.gif" class="loading-spinner">
    </div>
    <div class="row" [ngClass]="{'pt-2':pageSize == 5}" [hidden]="loadOnScrolling || listHistory?.length === 0">
        <div class="col-12 pt-3" [ngClass]="showAllBtn ? 'text-start' : 'text-end'">
            <a routerLink="/history-saves/history" *ngIf="showAllBtn" ><i class="fa fa-angle-right" aria-hidden="true"></i> View all</a>
            <a href="javascript:void(0)" *ngIf="loadMoreBtn" (click)="loadNext()">Load more</a>
        </div>
    </div>
</div>
