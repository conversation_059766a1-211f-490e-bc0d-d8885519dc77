import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SearchHistoryWidgetComponent } from './search-history-widget.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { SharedModule } from '@shared/shared.module';
import { TruncatePipe } from '@core/pipes';
import { provideMatomo } from 'ngx-matomo-client';

describe('SearchHistoryWidgetComponent', () => {
  let component: SearchHistoryWidgetComponent;
  let fixture: ComponentFixture<SearchHistoryWidgetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SearchHistoryWidgetComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([{path: 'launchpad', component: SearchHistoryWidgetComponent}])
      ],
      providers: [ TruncatePipe, provideMatomo({siteId: '', trackerUrl: '', disabled: true })]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SearchHistoryWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
