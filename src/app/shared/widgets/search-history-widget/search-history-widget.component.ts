import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { PaginationMetadata, SearchHistoryService } from '@core';
import { catchError, debounceTime, distinctUntilChanged, finalize, take } from 'rxjs/operators';
import { BehaviorSubject, of, Subscription } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-search-history-widget',
  templateUrl: './search-history-widget.component.html',
  styleUrls: ['./search-history-widget.component.scss']
})
export class SearchHistoryWidgetComponent implements OnInit, OnDestroy {

  @Input() loadOnScrolling = false;
  @Input() loadMoreBtn = false;
  @Input() showAllBtn = true;
  @Input() pageSize = 5;
  isLoading = false;
  listHistory = [];
  pagination: PaginationMetadata;

  filterTerm = '';
  loadSearchHistoryErrorMsg: string = null;

  private sortBy = 'created_at';
  private sortOrder = 'desc';

  private loadNextSubject = new BehaviorSubject<boolean>(false);
  private subscriptions = new Subscription();

  constructor(private searchHistoryService: SearchHistoryService) {
  }

  ngOnInit(): void {
    this.loadSearchHistory();
    if (this.loadOnScrolling) {
      this.loadNextOnScrolling();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onScrolled(ele: HTMLUListElement) {
    const shouldScroll = ele.scrollHeight - ele.scrollTop - 3 <= ele.clientHeight;
    this.loadNextSubject.next(shouldScroll);
  }

  loadNext() {
    if (!this.isLoading &&
      this.pagination &&
      this.pagination.current_page < this.pagination.last_page) {
      this.pagination.current_page += 1;
      this.loadSearchHistory();
    }
  }

  textAndNumberSearch(search): string {
    return this.searchHistoryService.textAndNumberSearch(search);
  }

  getType(search): string {
    return this.searchHistoryService.getType(search);
  }

  loadSearch(item) {
    this.searchHistoryService.loadSavedSearch(item);
  }

  private loadSearchHistory(): void {
    this.isLoading = true;
    this.loadSearchHistoryErrorMsg = null;
    const payload = this.buildPayload(this.pagination ? this.pagination.current_page : 1);
    const getHistories$ = this.searchHistoryService.getHistories(payload, false)
      .pipe(
        take(1),
        catchError((err) => {
          console.warn(err);
          if (err instanceof HttpErrorResponse && err.status === 403) {
            this.loadSearchHistoryErrorMsg = 'Sorry, you don\'t have permission to access this page. If your account permissions ' +
              'changed recently, try logging in again. Otherwise, reach out to us at ' +
              '<a href="mailto:<EMAIL>"><EMAIL></a> or by using our live chat support.';
          } else {
            this.loadSearchHistoryErrorMsg = 'Error in loading the collections';
          }
          return of(null);
        }),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: ({searches, page}) => {
          this.pagination = page;
          this.listHistory = [...this.listHistory, ...searches];
        },
        error: error => {
          console.warn(error);
        }
      });
    this.subscriptions.add(getHistories$);
  }

  private buildPayload(page: number): any {
    const payload = {
      page_size: this.pageSize,
      page: page,
      sort_by: this.sortBy,
      sort_order: this.sortOrder,
      search_type: 'in:SEMANTIC,BOOLEAN,NPL'
    };

    if (this.filterTerm) {
      payload['search_input'] = `like:%${this.filterTerm}%`;
    }
    return payload;
  }

  private loadNextOnScrolling() {
    const loadNext$ = this.loadNextSubject.asObservable()
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe({
        next: val => {
          if (val) {
            this.loadNext();
          }
        }
      });
    this.subscriptions.add(loadNext$);
  }
}
