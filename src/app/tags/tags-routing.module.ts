import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard, FeatureGuard } from '@core';
import { TagsComponent } from './tags.component';

const routes: Routes = [
  {path: '', component: TagsComponent, canActivate: [AuthGuard, FeatureGuard]},];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TagsRoutingModule {
}
