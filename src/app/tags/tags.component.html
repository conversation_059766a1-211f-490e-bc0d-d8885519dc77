<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>
  <app-workspace-menu workspaceTab="tags"></app-workspace-menu>

  <div class="flex-fill container mb-4">
    <app-alert type="warning" message="Creating, editing, or deleting tags is not possible for you, but you still have full access to assign or remove existing ones."
    [headline]="'Collaborator’s user rights'" version="figma" *ngIf="isCollaboratorUser"></app-alert>

    <div class="d-flex flex-column">

      <app-page-bar pageTitle="Tags manager">
        <app-filter-term-input leftSide placeHolder="Search for tags" (termChanged)="searchTags($event)"></app-filter-term-input>
        <ng-container rightSide>
          <a class="item-bar icon-add-tag cursor-pointer" *ngIf="!userService.isFreeUser()"
             #createTagTooltip="ngbTooltip" [ngbTooltip]="tagFormTemplate" tooltipClass="white-tooltip tooltip-big tag-edit-tooltip"
             triggers="click:blur" autoClose="outside" container="body" placement="bottom"
             (click)="onCreateTagClicked(createTagTooltip)" data-intercom-target="create-tag">
            Create Tag
          </a>

          <a href="javascript:void(0)" class="item-bar icon-delete" [ngClass]="{'disabled': !hasSelectedTags()}" (click)="deleteSelectedTags()"
             *ngIf="!userService.isFreeUser() && !isCollaboratorUser">
            Delete
          </a>
        </ng-container>
      </app-page-bar>

      <app-alert *ngIf="!isLoadingTags && !hasTags && !isCollaboratorUser" type="info" message='In order to add tags, click "Create Tag" to get started.' headline="No tags found"></app-alert>

      <ng-container *ngIf="!isLoadingTags else loadingSpinner">
        <table class="table table-hover" *ngIf="hasTags">
          <thead>
            <tr>
              <th width="15" *ngIf="!userService.isFreeUser() && !isCollaboratorUser">
                <label class="checkbox m-0 p-0" >
                  <input type="checkbox" (click)="selectAllTags($event)">
                  <span class="no-text">&nbsp;</span>
                </label>
              </th>
              <th class="tasks-main-col" appTableSortIcon sortColumn="name" [sortingColumn]="sortColumn" [sortingOrder]="sortOrder" (click)="sortTags()">
                Tag
              </th>
              <th width="10" *ngIf="!userService.isFreeUser() && !isCollaboratorUser"></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let tag of tags | paginate: paginationParams" class="task-row">
              <td *ngIf="!userService.isFreeUser() && !isCollaboratorUser">
                <label class="checkbox">
                  <input type="checkbox" [checked]="isSelected(tag)" (click)="selectTag(tag)">
                  <span class="no-text">&nbsp;</span>
                </label>
              </td>
              <td>
                <div class="d-flex align-items-center">
                  <ng-container [ngTemplateOutlet]="tagItemTemplate"
                                [ngTemplateOutletContext]="{tag: tag}"></ng-container>
                  <div class="ms-2 d-inline">{{ tag.documents_count }}</div>
                </div>
              </td>
              <td class="table-delete" (click)="deleteTag(tag)" *ngIf="!userService.isFreeUser() && !isCollaboratorUser">
                <div class="button-main-secondary-grey button-medium button-square">
                  <i class="fa-regular fa-trash-can"></i>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="d-flex justify-content-between mt-2 align-items-center" *ngIf="hasTags">
          <app-page-size [pageSize]="pageSize" (changeSize)="onPageSizeChanged($event)"></app-page-size>

          <div class="flex-fill">
            <pagination-controls id="tags-pagination" class="d-flex justify-content-end"
                                  (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
            </pagination-controls>
          </div>
        </div>

      </ng-container>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #loadingSpinner>
  <app-spinner></app-spinner>
</ng-template>

<ng-template #tagFormTemplate>
  <app-tag-edit [tag]="editingTag" [showButtons]="true"
                (tagSaved)="onTagCreated($event)"
                (tagCanceled)="onTagCanceled()">
  </app-tag-edit>
</ng-template>

<ng-template #tagItemTemplate let-tag="tag">
  <app-tag-item
    [tag]="tag"
    [canManageTag]="!userService.isFreeUser() && !isCollaboratorUser"
    [selectedTagId]="editingTag?.id"
    (tagSaved)="onTagEditSaved($event)"
    (tagDeleted)="onTagDeleted($event)">
  </app-tag-item>
</ng-template>
