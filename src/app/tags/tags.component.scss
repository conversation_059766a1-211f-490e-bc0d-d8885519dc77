@import 'scss/components/input-group';
@import "scss/components/tag_item";

.icon {
  &-add-tag, &-delete {
    padding-left: 25px;
    background-position: 0;
    background-position-y: 7px;
    background-size: 15px;
    background-repeat: no-repeat;
    transition: all 0.2s ease;
  }

  &-add-tag {
    background-image:  url('/assets/images/layout2022/icon-add-tag.svg');
    &:hover,&.active{
      background-image:  url('/assets/images/layout2022/icon-add-tag-hover.svg');
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-add-tag-disabled.svg');
    }
  }

  &-delete {
    background-image: url("/assets/images/layout2022/icon-delete.svg");
    &:hover,
    &.active {
      background-image: url("/assets/images/layout2022/icon-delete-hover.svg");
    }
    &.disabled {
      background-image: url('/assets/images/layout2022/icon-delete-disabled.svg');
    }
  }
}

.table-go-to-list {
  color: #389A85;
  cursor: pointer;
}

.table-delete {
  color: #0F2C34;
  cursor: pointer;
}

.text-bold {
  font-weight: 700;
}

.tags-manager-display {
  display: inline-flex;
  align-items: center;
  
  ::ng-deep .tags-container {
    margin-bottom: 0;
    flex-wrap: nowrap;
  }

  ::ng-deep .tag-item {
    margin-bottom: 0;
  }
}

.tag-manager-container {
  min-width: 150px;
  display: inline-flex;
  
  ::ng-deep .tags-container {
    margin-bottom: 0;
  }
}
