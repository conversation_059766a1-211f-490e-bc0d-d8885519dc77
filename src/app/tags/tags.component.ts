import { Component, OnDestroy, OnInit } from '@angular/core';
import { ColorUtil, Page, TagService, UserService } from '@core';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { TagModel } from '@core/models/tag.model';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tags',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss']
})
export class TagsComponent implements OnInit, OnDestroy {

  isLoadingTags = false;
  selectedTag: TagModel;
  tags: TagModel[] = [];
  listSelectedTags: TagModel[] = [];
  totalDocuments = 0;
  editingTag: TagModel = null;
  private currentTagFormTooltip: NgbTooltip;

  pageSize = 25;
  pagination = {current_page: 1, page_size: this.pageSize, last_page: 0, total_hits: 0} as Page;

  sortOrder: 'asc' | 'desc';
  sortColumn: string = null;

  private termToSearch = '';
  private subscriptions = new Subscription();

  constructor(
    private tagService: TagService,
    public userService: UserService
  ) {
  }

  get hasTags() {
    return this.tags.length > 0;
  }

  get paginationParams(): any {
    return {
      itemsPerPage: this.pagination.page_size,
      id: 'tags-pagination',
      currentPage: this.pagination.current_page,
      totalItems: this.pagination.total_hits
    };
  }

  get isEditingTag(): boolean {
    return !!this.editingTag?.id;
  }

  get isCollaboratorUser(): boolean {
    return this.userService.isCollaboratorUser();
  }

  ngOnInit(): void {
    this.loadTags();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  isSelected(tag: TagModel): boolean {
    return this.listSelectedTags.findIndex(tg => tg.id === tag.id) > -1;
  }

  hasSelectedTags(): boolean {
    return this.listSelectedTags.length > 0;
  }

  onPageSizeChanged(size: number) {
    this.pageSize = size;
    this.pagination.current_page = 1;
    this.loadTags();
  }

  onPageChange(page: number) {
    this.pagination.current_page = page;
    this.loadTags();
  }

  getTextColor(color: string): string {
    return this.tagService.getTextColor(color);
  }

  deleteTag(tag: TagModel): void {
    this.tagService.deleteTagsWithConfirmation([tag]).subscribe(deletedTags => {
      this.removeTagFromLists(tag);
    });
  }

  deleteSelectedTags(): void {
    if (this.listSelectedTags.length === 0) {
      return;
    }
    
    this.tagService.deleteTagsWithConfirmation([...this.listSelectedTags]).subscribe({
      next: () => {
        this.listSelectedTags = [];
        this.selectedTag = null;
        this.pagination.current_page = 1;
        this.totalDocuments = 0;
        this.loadTags();
      },
      error: (err) => {
        console.log(err);
      }
    });
  }

  onTagDeleted(tag: TagModel): void {
    this.removeTagFromLists(tag);
  }

  selectTag(tag: TagModel): void {
    const index = this.listSelectedTags.findIndex(tg => tg.id === tag.id);
    if (index > -1) {
      this.listSelectedTags.splice(index, 1);
      this.totalDocuments -= tag.documents_count;
    } else {
      this.listSelectedTags.push(tag);
      this.totalDocuments += tag.documents_count;
    }
  }

  selectAllTags(event): void {
    if (event.target.checked) {
      this.listSelectedTags = [...this.tags];
      this.totalDocuments = this.listSelectedTags.reduce((total, tag) => total + tag.documents_count, 0);
    } else {
      this.listSelectedTags = [];
      this.totalDocuments = 0;
    }
  }

  searchTags(term: string): void {
    this.termToSearch = term;
    this.loadTags();
  }

  sortTags(): void {
    if (this.sortOrder === 'desc') {
      this.sortColumn = null;
      this.sortOrder = null;
    } else {
      this.sortColumn = 'name';
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    }
    this.loadTags();
  }

  onTagCreated(tag: TagModel) {
    this.currentTagFormTooltip?.close(true);

    if (!this.isEditingTag) {
      this.loadTags();
    }
  }

  onTagCanceled() {
    this.currentTagFormTooltip?.close(true);
    this.editingTag = null;
  }

  getBoxTagStyle(color: string) {
    return {'color': this.getTextColor(color), 'background-color': '#' + color};
  }

  isTagNameTruncated(tag: TagModel, element: HTMLElement): boolean {
    const nameElement = element?.querySelector('.tag-name');
    if (!nameElement) return false;
    
    return nameElement.scrollWidth > nameElement.clientWidth;
  }

  onEditTagClicked(tag: TagModel, editTagTooltip: NgbTooltip) {
    this.currentTagFormTooltip = editTagTooltip;
    if (this.isCollaboratorUser) {
      return;
    }
    this.editingTag = tag;
  }

  onCreateTagClicked(createTagTooltip: NgbTooltip) {
    this.currentTagFormTooltip = createTagTooltip;
    this.initializeNewTag();
  }

  onTagEditSaved(tag: TagModel): void {
    const index = this.tags.findIndex(t => t.id === tag.id);
    if (index !== -1) {
      this.tags[index] = { ...this.tags[index], ...tag };
    }
    
    const selectedIndex = this.listSelectedTags.findIndex(t => t.id === tag.id);
    if (selectedIndex !== -1) {
      this.listSelectedTags[selectedIndex] = { ...this.listSelectedTags[selectedIndex], ...tag };
    }
    
    if (this.editingTag?.id === tag.id) {
      this.editingTag = { ...this.editingTag, ...tag };
    }
    
    if (this.selectedTag?.id === tag.id) {
      this.selectedTag = { ...this.selectedTag, ...tag };
    }
  }

  private loadTags(): void {
    this.isLoadingTags = true;
    const payload = this.buildPayload();
    const getTags$ = this.tagService.getTags(payload).subscribe({
      next: data => {
        this.isLoadingTags = false;
        this.tags = data.tags;
        this.pagination = data.page;
      }
    });
    this.subscriptions.add(getTags$);
  }

  private buildPayload() {
    const payload = {
      page_size: this.pageSize,
      page: this.pagination ? this.pagination.current_page : 1
    };
    if (this.sortColumn) {
      payload['sort_by'] = this.sortColumn;
      payload['sort_order'] = this.sortOrder;
    } else {
      payload['sort_by'] = 'created_at';
      payload['sort_order'] = 'desc';
    }
    if (this.termToSearch) {
      payload['name'] = `like:%${this.termToSearch}%`;
    }
    return payload;
  }

  private initializeNewTag() {
    this.editingTag = {
      id: null,
      name: '',
      color: ColorUtil.getRandomTagColor().code.replace('#', ''),
      private: true,
      user_id: this.userService.getUser().profile?.id
    } as TagModel
  }

  private removeTagFromLists(tag: TagModel): void {
    this.tags = this.tags.filter(t => t.id !== tag.id);
    
    const selectedIndex = this.listSelectedTags.findIndex(t => t.id === tag.id);
    if (selectedIndex > -1) {
      this.listSelectedTags.splice(selectedIndex, 1);
      this.totalDocuments -= tag.documents_count || 0;
    }
    
    if (this.editingTag?.id === tag.id) {
      this.editingTag = null;
    }
    
    if (this.selectedTag?.id === tag.id) {
      this.selectedTag = null;
    }
  }
}
