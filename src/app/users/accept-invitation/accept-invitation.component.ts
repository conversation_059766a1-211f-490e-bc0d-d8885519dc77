import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '@core';
import { Subscription, take } from 'rxjs';

@Component({
  selector: 'app-accept-invitation',
  templateUrl: './accept-invitation.component.html',
  styleUrls: ['../../auth/shared/shared.scss', './accept-invitation.component.scss']
})
export class AcceptInvitationComponent implements OnInit, OnDestroy {
  public errors: Array<string> = [];
  public loading = true;

  private subscriptions = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService
  ) {
  }

  ngOnInit() {
    const queryParams$ = this.route.queryParams.subscribe({
      next: params => {
        const {token} = params;
        this.loading = true;
        const acceptInvitation$ = this.userService.acceptInvitation({token}).pipe(take(1))
          .subscribe({
            next: (next) => this.handleSuccess(next),
            error: ({error}) => this.handleError(error)
          });
        this.subscriptions.add(acceptInvitation$);
      }
    });
    this.subscriptions.add(queryParams$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  private handleSuccess(resp): void {
    this.errors = [];
    this.loading = false;
    this.userService.getUser().profile.company_id = resp['data']['company_id'];
    this.userService.refreshToken() // So that new claims are reflected in token
      .then(() =>this.router.navigate(['/']))
  }

  private handleError(error): void {
    this.loading = false;
    this.errors = [error.message];
  }

}
