<div class="modal-header">
  <div class="modal-title" [innerHTML]="getTitle()"></div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>
<div class="modal-body">
  <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

  <form [formGroup]="form">
    <div class="mb-3">
      <label for="password">New password</label>
      <input [ngClass]="{'is-invalid': isFieldInvalid('password')}" autocomplete="off" class="form-control"
             formControlName="password" id="password" maxlength="32" name="password" type="password" required
             placeholder="Enter new value to change password"/>
      <div *ngIf="fieldErrors['password']" class="invalid-feedback">{{fieldErrors['password'].join(' ')}}</div>
    </div>

    <div class="mb-3">
      <label for="confirm_password">Confirm password</label>
      <input [ngClass]="{'is-invalid': isFieldInvalid('confirm_password')}" autocomplete="off" class="form-control"
             formControlName="confirm_password" id="confirm_password" maxlength="32" name="confirm_password" required
             type="password" placeholder="Confirm new password"/>
      <div *ngIf="fieldErrors['confirm_password']"
           class="invalid-feedback">{{fieldErrors['confirm_password'].join(' ')}}</div>
    </div>
  </form>

</div>

<div class="modal-footer d-flex justify-content-between">
  <div class="text-black-50"></div>
  <div class="d-flex justify-content-end">
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
    <button (click)="activeModal.dismiss('Cancel')" class="btn btn-ghost btn-md">Cancel</button>
    <button (click)="saveUser()" class="btn btn-primary btn-md"
            [disabled]="!form.dirty || form.invalid || isSaving">
      Change password
    </button>
  </div>
</div>
