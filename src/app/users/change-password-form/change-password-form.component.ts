import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TeamUser, UserService, UserTitlePipe } from '@core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize, take } from 'rxjs/operators';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-change-password-form',
  templateUrl: './change-password-form.component.html',
  styleUrls: ['./change-password-form.component.scss']
})
export class ChangePasswordFormComponent implements OnInit, OnDestroy {
  @Input() user = {} as TeamUser;

  form: UntypedFormGroup;
  fieldErrors = {};
  isSaving = false;
  errors: string[] = [];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private userTitlePipe: UserTitlePipe
  ) {
  }

  ngOnInit(): void {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  saveUser() {
    if (this.form.valid) {
      const {password, confirm_password} = this.form.value;

      if (password && password === confirm_password) {
        this.form.disable();
        this.errors = [];
        this.isSaving = true;
        const updateUser$ = this.userService.updateUser(this.user.id, {password})
          .pipe(
            take(1),
            finalize(() => this.isSaving = false)
          )
          .subscribe({
            next: (val) => {
              this.activeModal.close(val.profile as TeamUser);
            },
            error: ({error}) => {
              console.warn(error);
              let errorMsg = ['Error when changing password for the user. '+ error.message];
              this.form.enable();
              this.fieldErrors = error.details || {};
              if (error['details']['_schema']){
                errorMsg = error['details']['_schema'];
              }
              this.errors = errorMsg;
            }
          });
        this.subscriptions.add(updateUser$);
      } else {
        this.errors = ['Password and confirm password are not equal'];
      }
    }
  }

  getTitle(): string {
    const userTitle = this.userTitlePipe.transform(this.user);
    return `Reset password for <span class="text-green">${userTitle}</span>`;
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return this.fieldErrors[name] || (field && field.touched && field.invalid);
  }

  private buildForm() {
    this.form = new UntypedFormGroup({
      password: new UntypedFormControl(null, [
        Validators.required, Validators.maxLength(128)
      ]),
      confirm_password: new UntypedFormControl(null, [
        Validators.required, Validators.maxLength(128)
      ]),
    });
  }

}
