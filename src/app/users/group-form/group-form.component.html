<div class="modal-header">
    <div class="modal-title" [innerHtml]="getTitle()"></div>
    <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
            type="button"></button>
  </div>
  <div class="modal-body">
    <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

    <form [formGroup]="form" (ngSubmit)="saveGroup()">
      <div class="row">
        <div class="col-12">
          <div class="mb-3">
            <label for="name" class="form-label">Group name <span class="text-danger">*</span></label>
            <input [ngClass]="{'is-invalid': isFieldInvalid('name')}" autocomplete="off" class="form-control"
                   formControlName="name" id="name" maxlength="100" name="name" required type="text"/>

            <div *ngIf="form.get('name').touched && form.get('name').hasError('required')"
                 class="invalid-feedback">
              Please enter Group name
            </div>

            <div *ngIf="fieldErrors['name']"
                 class="invalid-feedback">{{fieldErrors['name'].join(' ')}}</div>
          </div>
        </div>
        <div class="col-12">
          <div class="mb-3">
            <label class="form-label">Select team members <span class="text-danger">*</span></label>
            <app-teams-selector class="w-100" formControlName="teamUsers"
                                [showGroups]="false" [multiple]="true" [showMe]="true" [showFooter]="false"
                                label="Select users" placeholder="Search for a person"
                                [ngClass]="{'is-invalid': isFieldInvalid('teamUsers')}">
            </app-teams-selector>

            <div *ngIf="form.get('teamUsers').touched && form.get('teamUsers').hasError('required')"
                 class="invalid-feedback">
              Please select users
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="mb-3">
            <label for="description" class="form-label">Description</label>
            <textarea class="form-control" formControlName="description" id="description"
            [ngClass]="{'is-invalid': isFieldInvalid('description')}" ></textarea>

            <div *ngIf="fieldErrors['description']" class="invalid-feedback">{{fieldErrors['description'].join(' ')}}</div>
          </div>
        </div>
      </div>

      <input type="submit" hidden>
    </form>

  </div>

  <div class="modal-footer d-flex justify-content-between">
    <div class="text-black-50"><small>Press <span class="fw-bold">Enter</span> to save</small></div>
    <div class="d-flex justify-content-end">
      <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
      <button (click)="activeModal.dismiss('Cancel')" class="btn btn-ghost btn-md" [disabled]="isSaving">Cancel</button>
      <button (click)="saveGroup()" class="btn btn-primary btn-md"
              [disabled]="!form.dirty || form.invalid || isSaving">
        Save
      </button>
    </div>
  </div>

  <ng-template #loader>
    <div class="d-flex justify-content-center">
      <img src="assets/images/octimine_blue_spinner.gif">
    </div>
  </ng-template>
