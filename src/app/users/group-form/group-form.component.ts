import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Assignee, AssigneeTypeEnum, GroupService, TeamUser, UserGroup, UserService } from '@core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { catchError, finalize, map, take } from 'rxjs/operators';
import { forkJoin, Observable, of, Subscription, tap } from 'rxjs';
import { difference } from 'lodash';

@Component({
  selector: 'app-group-form',
  templateUrl: './group-form.component.html',
  styleUrls: ['./group-form.component.scss']
})
export class GroupFormComponent implements OnInit, OnDestroy {
  @Input() group: UserGroup;

  form: UntypedFormGroup;
  fieldErrors = {};
  isSaving = false;
  errors: string[] = [];
  currentTeamUsers: Assignee[] = [];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    private formBuilder: UntypedFormBuilder,
    public userService: UserService,
    public groupService: GroupService,
  ) {
  }

  ngOnInit(): void {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getTitle(): string {
    return this.group ? `Edit group <span class="text-green">${this.group.name}</span>` : 'Create new group';
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return this.fieldErrors[name] || (field && field.touched && field.invalid);
  }

  saveGroup() {
    if (this.form.valid) {
      this.form.disable();
      this.errors = [];
      this.isSaving = true;
      const formValue = this.form.value;
      const payload = {
        name: formValue.name,
        description: formValue.description,
      }
      const teamUsers = formValue.teamUsers;
      const obs = this.group ? this.groupService.updateGroup(this.group.id, payload) :
        this.groupService.createGroup(payload);
      const obs$ = obs.pipe(take(1)).subscribe({
        next: (val) => {
          this.updateGroupMembers(val, teamUsers);
        },
        error: ({error}) => {
          console.warn(error);
          this.form.enable();
          this.fieldErrors = error.details || {};
          let errorMsg = ['Error when saving the user group. '];
          if (error['details']['_schema']){
            errorMsg.push(...error['details']['_schema']);
          }
          this.errors = errorMsg;
          this.isSaving = false;
        }
      });
      this.subscriptions.add(obs$);
    }
  }

  closeFrom(group: UserGroup){
    this.activeModal.close(group)
  }

  updateGroupMembers(group: UserGroup, updatingTeamUsers: Assignee[]){
    const currentUserIds = this.currentTeamUsers.map((user) => user.id);
    const updatingUserIds = updatingTeamUsers.map((user) => user.id);
    const removingUserIds = difference(currentUserIds, updatingUserIds);
    const addingUserIds = difference(updatingUserIds, currentUserIds);
    const observables: Observable<any>[] = [
      ...removingUserIds.map((v: number) => this.groupService.removeFromGroup(group.id, v)),
      ...addingUserIds.map((v: number) => this.groupService.addToGroup(group.id, v)),
    ];
    if (observables.length > 0) {
      const obs$ = forkJoin(observables)
        .pipe(
          take(1),
          finalize(() => this.isSaving = false)
        )
        .subscribe({
          next: (val) => {
            this.closeFrom(group);
          },
          error: (error) => {
            console.warn(error);
            this.errors = ['Error while updating group member user. ' + error.message];
            this.form.enable();
          }
        });
      this.subscriptions.add(obs$);
    } else {
      this.closeFrom(group);
    }
  }

  private buildForm() {
    this.form = this.formBuilder.group({
      name: [this.group ? this.group?.name : '', [Validators.required, Validators.maxLength(255)]],
      description: [this.group ? this.group?.description : ''],
      teamUsers: [[], [Validators.required]],
    });
    this.subscriptions.add(this.loadGroupMembers().subscribe());
  }

  private loadGroupMembers(): Observable<Assignee[]> {
    if (!this.group?.id) {
      return of([]);
    }
    this.form.disable();
    return this.groupService.getGroupMember(this.group.id)
      .pipe(
        map(({page, users}) => {
          return users.map((u) => ({id: u.id, type: AssigneeTypeEnum.USER}) as Assignee);
        }),
        tap((teamUsers) => {
          this.currentTeamUsers = teamUsers;
          this.form.get('teamUsers').setValue(teamUsers);
          this.form.enable();
        }),
        catchError((err) => {
          console.error(err);
          return of([]);
        })
      );
  }
}
