import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard, FeatureGuard } from '@core';
import { GroupsComponent } from '@users/groups/groups.component';

const routes: Routes = [
  {path: '', component: GroupsComponent, canActivate: [AuthGuard, FeatureGuard]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GroupsRoutingModule {
}
