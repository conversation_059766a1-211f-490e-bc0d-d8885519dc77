import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared/shared.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { GroupFormComponent } from './group-form/group-form.component';
import { GroupsRoutingModule } from './groups-routing.module';
import { SharedUsersModule } from '@users/shared-users.module';
import { GroupsComponent } from '@users/groups/groups.component';


@NgModule({
  declarations: [
    GroupFormComponent,
    GroupsComponent
  ],
  imports: [
    CommonModule,
    GroupsRoutingModule,
    SharedModule,
    NgbModule,
    SharedUsersModule
  ],
})
export class GroupsModule {
}
