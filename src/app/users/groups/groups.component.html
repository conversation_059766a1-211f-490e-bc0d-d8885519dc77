<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>
  <app-workspace-menu workspaceTab="team"></app-workspace-menu>

  <div class="flex-fill page-content container justify-content-start mb-4">
    <div class="d-flex flex-column">

      <app-page-bar [pageTitle]="pageTitle">
        <ng-container pageSubTitle *ngIf="userService.canUseWorkflowFeature()">
          <app-users-top-navigation></app-users-top-navigation>
        </ng-container>
        <app-filter-term-input leftSide placeHolder="Search for groups" (termChanged)="onFilterChanged($event)"></app-filter-term-input>
        <ng-container rightSide>
          <a href="javascript:void(0)" class="item-bar icon-delete" (click)="deleteSelectedUserGroups()"
             ngbTooltip="Delete selected groups" container="body" [ngClass]="{'disabled': selectedUserGroups.length == 0}">
            Delete
          </a>
          <a href="javascript:void(0)" class="item-bar icon-add" (click)="showGroupForm(null)"
             ngbTooltip="Add new group" container="body">
            Add group
          </a>
        </ng-container>
      </app-page-bar>

      <div class="overflow-auto flex-fill">
        <div *ngIf="!loading else loadingTemplate">
          <app-alert type="danger" [message]="message" *ngIf="message"></app-alert>
          <app-alert type="success" [message]="messageAlert" *ngIf="messageAlert"></app-alert>
          <app-alert type="warning" message="No user groups found" *ngIf="!userGroups.length && filterTerm?.length && !messageAlert"></app-alert>

          <table id="groups-table" class="table table-condensed publication-table w-100 table-hover" *ngIf="userGroups.length">
            <thead class="w-100">
            <tr>
              <th width="30px">
                <label class="checkbox m-0 p-0">
                  <input type="checkbox" (change)="selectAllUserGroups($event)" [checked]="areGroupsSelected">
                  <span class="no-text">&nbsp;</span>
                </label>
              </th>
              <th (click)="sort('name')" class="cursor-pointer"
                  appTableSortIcon sortColumn="name" [sortingColumn]="sortBy" [sortingOrder]="sortOrder">
                Group name
              </th>
              <th>Description</th>
              <th width="90px"></th>
            </tr>
            </thead>

            <tbody *ngFor="let g of userGroups | paginate:
            {
            itemsPerPage: pagination?.page_size,
            id: 'groups-pagination',
            currentPage: pagination?.current_page,
            totalItems: pagination?.total_hits < 400 ? pagination?.total_hits : 400
            };">
            <tr [ngbTooltip]="g.is_system ? 'A system group can not be edited or deleted' : null"
                tooltipClass="white-tooltip">
              <td>
                <label *ngIf="!g.is_system" class="checkbox m-0 p-0">
                  <input type="checkbox" (change)="selectUserGroup($event, g)" [checked]="isUserGroupSelected(g)">
                  <span class="no-text">&nbsp;</span>
                </label>
              </td>
              <td>{{g.name}}</td>
              <td>{{ g.description }}</td>
              <td class="table-actions">
                <div *ngIf="!g.is_system" class="group-actions inline-dropdown text-end">
                  <div class="abs" ngbDropdown>
                      <span class="icon" ngbDropdownToggle>
                        <i class="fas fa-ellipsis-v"></i>
                      </span>

                    <ul class=" shadow-lg" ngbDropdownMenu>
                      <li class="dropdown-item" (click)="showGroupForm(g)">
                        <a href="javascript:void(0)"><i class="fa fa-edit"></i>Edit</a>
                      </li>
                      <li class="dropdown-item dropdown-menu-separator" (click)="deleteUserGroup(g)">
                        <a href="javascript:void(0)"><i class="fa fa-trash"></i>Delete</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </td>
            </tr>
            </tbody>
          </table>

          <div class="d-flex justify-content-between mt-4 align-items-center" *ngIf="pagination?.last_page > 1 && userGroups.length">
            <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)" [pageOptions]="pageOptions"></app-page-size>

            <div class="flex-fill">
              <pagination-controls id="groups-pagination" class="d-flex justify-content-end"
                                   (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-footer></app-footer>
</div>

<ng-template #loadingTemplate>
  <app-spinner></app-spinner>
</ng-template>
