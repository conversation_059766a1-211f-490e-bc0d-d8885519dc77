import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ConfirmationDialogService, GroupService, PaginationMetadata, UserGroup, UserService } from '@core';
import { BehaviorSubject, forkJoin, Observable, of, Subscription, timer } from 'rxjs';
import { catchError, concatMap, debounce, take, tap } from 'rxjs/operators';
import { GroupFormComponent } from '@users/group-form/group-form.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-groups',
  templateUrl: './groups.component.html',
  styleUrls: ['./groups.component.scss']
})
export class GroupsComponent implements OnInit, OnDestroy {
  filterTerm = '';
  message: string;
  messageAlert: string;

  userGroups: Array<UserGroup> = [];
  payload: any;
  pagination: PaginationMetadata;
  loading = false;
  pageSize = 25;
  pageOptions = [25, 50, 100];
  selectedUserGroups: Array<UserGroup> = [];

  sortBy = 'id';
  sortOrder: 'asc' | 'desc' = 'desc';

  private userGroupsSubject = new BehaviorSubject<string>(null);
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    private confirmationDialogService: ConfirmationDialogService,
    private groupService: GroupService,
    private modalService: NgbModal,
  ) {
  }

  get areGroupsSelected(): boolean {
    const selectedIds = this.selectedUserGroups.map((hs) => hs.id);
    return this.userGroups.every((hs) => selectedIds.includes(hs.id));
  }

  get pageTitle(): string {
    const companyName = this.userService.getUser()?.profile?.company_name;
    if (this.userService.canUseWorkflowFeature() && companyName?.length) {
      return `${companyName} - My team`;
    }
    return 'My team';
  }

  ngOnInit() {
    const userGroups$ = this.userGroupsSubject.asObservable()
      .pipe(
        tap((val) => {
          this.loading = true;
        }),
        debounce((val: string) => timer(val?.length > 0 ? 1000 : 0)),
        concatMap((val) => this.searchUserGroups(val)),
        tap(() => {
          this.loading = false;
        })
      )
      .subscribe();
    this.subscriptions.add(userGroups$);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  sort(field): void {
    if (field === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }

    this.userGroupsSubject.next(this.filterTerm);
  }

  onChangePageSize(event: number): void {
    this.pageSize = event;
    this.resetCurrentPage();
    this.userGroupsSubject.next(this.filterTerm);
  }

  selectAllUserGroups(event: Event) {
    if (event.target['checked']) {
      const historyIds = this.selectedUserGroups.map((hs) => hs.id);
      this.selectedUserGroups = [...this.selectedUserGroups, ...this.userGroups.filter((a) => !historyIds.includes(a.id))];
    } else {
      const historyIds = this.userGroups.map((hs) => hs.id);
      this.selectedUserGroups = this.selectedUserGroups.filter((a) => !historyIds.includes(a.id));
    }
  }

  selectUserGroup(event: Event, g: UserGroup) {
    if (event.target['checked']) {
      if (!this.selectedUserGroups.some((a) => a.id === g.id)) {
        this.selectedUserGroups.push(g);
      }
    } else {
      this.selectedUserGroups = this.selectedUserGroups.filter((a) => a.id !== g.id);
    }
  }

  isUserGroupSelected(g: UserGroup): boolean {
    return this.selectedUserGroups.some((a) => a.id === g.id);
  }

  deleteUserGroup(hs: UserGroup) {
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${hs.name}</span> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const removeGroupFromCompany$ = this.groupService.removeGroupFromCompany(hs.id)
          .pipe(take(1))
          .subscribe({
            next: () => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.userGroups.length === 1);
              this.userGroupsSubject.next(this.filterTerm);
            },
            error: error => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(removeGroupFromCompany$);
      }
    });
  }

  deleteSelectedUserGroups() {
    const pluralNoun = this.selectedUserGroups.length > 1 ? 'user groups' : 'user group';
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${this.selectedUserGroups.length} selected ${pluralNoun}</span> ?</div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        this.loading = true;
        const obs = this.selectedUserGroups.map((hs) => this.groupService.removeGroupFromCompany(hs.id));
        const obs$ = forkJoin(obs)
          .pipe(take(1))
          .subscribe({
            next: (val) => {
              this.loading = false;
              this.resetCurrentPageAfterDeleting(this.areGroupsSelected);
              this.selectedUserGroups = [];
              this.userGroupsSubject.next(this.filterTerm);
            },
            error: (error) => {
              this.loading = false;
              console.log(error);
            }
          });
        this.subscriptions.add(obs$);
      }
    });
  }

  onFilterChanged(event: string) {
    this.filterTerm = event;
    this.resetCurrentPage();
    this.userGroupsSubject.next(event);
  }

  onPageChange($event): void {
    const {current_page} = this.pagination;

    if (current_page === $event) {
      return;
    }

    this.pagination.current_page = $event;
    this.userGroupsSubject.next(this.filterTerm);
  }

  showGroupForm(group = null) {
    const modal = this.modalService.open(GroupFormComponent, {size: 'md'});
    if (group) {
      modal.componentInstance.group = group;
    }

    modal.result.then((value) => {
      this.messageAlert = `Group <span class="text-green">${value.name}</span> has been ${group ? 'updated' : 'created'} successfully.`;
      this.filterTerm = null;
      this.resetCurrentPage();
      this.userGroupsSubject.next(null);
    }, reason => {
    });
  }

  private searchUserGroups(filterTerm: string): Observable<{
    groups: UserGroup[],
    page: PaginationMetadata
  }> {
    if (!this.userService.canManageGroup()) {
      return of({groups: [], page: null});
    }

    this.message = null;
    const payload = this.buildPayload(this.pagination ? this.pagination.current_page : 1, filterTerm);

    return this.groupService.getGroups(payload)
      .pipe(
        tap(({groups, page}) => {
          this.userGroups = groups;
          this.pagination = page;

          if (this.userGroups.length === 0 && !this.filterTerm) {
            this.message = 'No user groups found';
          }
        }),
        catchError((error) => {
          console.log(error);
          this.message = 'An error occurred while loading user groups';
          throw error;
        })
      );
  }

  private buildPayload(page: number, filterTerm: string): any {
    const payload = {
      page_size: this.pageSize,
      page: page,
      sort_by: this.sortBy,
      sort_order: this.sortOrder
    };

    if (filterTerm) {
      payload['name'] = `like:%${filterTerm}%`;
    }

    return payload;
  }

  private resetCurrentPage() {
    if (this.pagination) {
      this.pagination.current_page = 1;
    }
  }

  private resetCurrentPageAfterDeleting(shouldDecreaseCurrentPage = false) {
    if (this.pagination) {
      if (this.pagination.current_page > 1 && this.pagination.current_page === this.pagination.last_page && shouldDecreaseCurrentPage) {
        this.pagination.current_page--;
      }
    }
  }
}
