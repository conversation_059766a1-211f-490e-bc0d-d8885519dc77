<div class="modal-header">
  <div class="modal-title">
    Invite user(s) to your team
  </div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>
<div class="modal-body">
  <app-alert type="success"
             [message]="successEmails.join(', ') + ' ' + (successEmails.length > 1 ? 'have' : 'has') + ' been invited successfully'"
             *ngIf="successEmails.length"></app-alert>
  <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>

  <form [formGroup]="form">
    <ng-select [(items)]="items" [addTag]="onAddTagFunc" [multiple]="true" [selectOnTab]="true" [isOpen]="false" (keydown)="onTypeAhead($event)"
               [clearable]="true" [keyDownFn]="onKeyDownFunc" (clear)="errors = [];typeAheadText=null;" (blur)="onBlur($event)"
               formControlName="emails" placeholder="Press enter or tab to insert new email">
    </ng-select>
  </form>

</div>

<div class="modal-footer d-flex justify-content-between">
  <div class="text-black-50">
    <ng-container *ngIf="!isSaving">
      <small>You can paste a list of emails which are separated by <span class="fw-bold">comma</span> or <span class="fw-bold">space</span> or <span class="fw-bold">new line</span></small>
    </ng-container>
  </div>
  <div class="d-flex justify-content-end">
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
    <button (click)="activeModal.dismiss('Cancel')" class="btn btn-ghost btn-md">Cancel</button>
    <button (click)="onSendInvitationClicked()" class="btn btn-primary btn-md"
            [disabled]="isFormDisabled">
      Invite
    </button>
  </div>
</div>
