import { Component, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UserService } from '@core';
import { AbstractControl, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { catchError, finalize, map, mergeMap } from 'rxjs/operators';
import { from, of, Subscription } from 'rxjs';

@Component({
  selector: 'app-invite-user',
  templateUrl: './invite-user.component.html',
  styleUrls: ['./invite-user.component.scss']
})
export class InviteUserComponent implements OnInit, OnDestroy {

  form: UntypedFormGroup;
  isSaving = false;
  errors: string[] = [];
  successEmails: string[] = [];
  typeAheadText: string;
  items: string[]= [];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    public userService: UserService
  ) {
  }

  get isFormDisabled(): boolean{
    if(this.typeAheadText && this.typeAheadText.length> 0){
      return false;
    }
    return !this.form.dirty || this.form.invalid || this.isSaving;
  }

  onAddTagFunc = (term: string) => {
    if (this.isValidEmail(term)) {
      this.errors = [];
      this.typeAheadText= null;
      return term;
    } else {
      this.errors = ['Please enter a valid email address'];
      return false;
    }
  }

  onKeyDownFunc = (event: KeyboardEvent) => {
    const key = event.key.toLowerCase();
    if (['backspace'].includes(key)) {
      this.errors = [];
    } else if (['enter'].includes(key)) {
      const value = (event.target as HTMLInputElement).value;

      if (value) {
        const emails = this.form.value.emails || [];
        this.errors = emails.includes(value) ? [`Email ${value} has already been added`] : [];
      }
    }
    return true;
  }

  @HostListener('paste', ['$event'])
  handlePasteEvent(event: ClipboardEvent) {
    const target = event.target as HTMLElement;
    if (target.nodeName === 'INPUT') {
      const input = event.target as HTMLInputElement;
      this.extractEmails(input, event.clipboardData.getData('text/plain'));
      input.blur();
    }
  }

  ngOnInit(): void {
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onBlur(event: FocusEvent) {
    const target = event.target as HTMLElement;
    if (target.nodeName === 'INPUT') {
      const input = event.target as HTMLInputElement;
      this.extractEmails(input, input.value);
    }
  }

  onSendInvitationClicked() {
    this.sendInvitation();
  }

  private sendInvitation() {
    if (this.form.valid) {
      this.form.disable();
      this.isSaving = true;
      this.errors = [];
      this.successEmails = [];
      const errorMessages = [];
      const doneEmails = [];
      const errorEmails = [];
      const notFoundEmails = [];
      const existingEmails = [];
      const emails = this.form.value.emails;
      const obs$ = from(emails)
        .pipe(
          mergeMap((val: string) => {
            return this.userService.inviteUser(val)
              .pipe(
                map((v) => {
                  doneEmails.push(val);
                  return {email: val, error: null};
                }),
                catchError(({error}) => {
                  if (error.status === 404) {
                    notFoundEmails.push(val);
                  } else {
                    errorMessages.push(error.message);
                  }

                  errorEmails.push(val);

                  return of({email: val, error: error.message});
                })
              );
          }),
          finalize(() => {
            this.isSaving = false;
            this.errors = errorMessages;
            this.successEmails = doneEmails;
            this.form.enable();

            if (notFoundEmails?.length > 0) {
              const plural = notFoundEmails.length > 1 ? 's' : '';
              this.errors.push('Could not find the user' + plural + ' for the email' + plural + ': ' +
                notFoundEmails.join(', '));
            }

            if (existingEmails?.length > 0) {
              const plural = existingEmails.length > 1 ? 's' : '';
              const verb = existingEmails.length > 1 ? 'have' : 'has';
              this.errors.push('The user' + plural + ' ' + existingEmails.join(', ') + ' ' + verb +
                ' been assigned to another company');
            }

            if (this.errors?.length === 0) {
              this.activeModal.close(emails);
            }

            this.form.get('emails').setValue(errorEmails);
          })
        )
        .subscribe();
      this.subscriptions.add(obs$);
    }
  }

  private extractEmails(input: HTMLInputElement, value: string) {
    if (value) {
      const emails = value.split(/[,\s\n]/g).filter((v) => this.isValidEmail(v));
      if (emails?.length > 0) {
        const emailsCtrl = this.form.controls.emails as AbstractControl;
        emailsCtrl.setValue([...new Set([...(emailsCtrl.value || []), ...emails])]);
        input.value = '';
        this.typeAheadText= null;
        this.form.markAsDirty();
      }
    }
  }

  private buildForm() {
    this.form = new UntypedFormGroup({
      emails: new UntypedFormControl(null, [
        Validators.required
      ])
    });
  }

  private isValidEmail(val: string): boolean {
    if (!val) {
      return false;
    }
    const control = new UntypedFormControl(val, Validators.email);
    return !control.errors && !!(val.match(/.+@.+\./g));
  }

  onTypeAhead(event: KeyboardEvent){
    this.typeAheadText=null;
    const input = event.target as HTMLInputElement;
    if(this.isValidEmail(input.value)){
      this.typeAheadText = input.value;
    }
    if(['Comma', 'Space'].includes(event.code)){
      this.extractEmails( input, input.value);
    }
  }
}
