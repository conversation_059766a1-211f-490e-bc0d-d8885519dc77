<div class="modal-header">
  <div class="modal-title" [innerHtml]="getTitle()"></div>
  <button (click)="activeModal.dismiss('Cross click')" aria-label="Close" class="close" tabindex="-1"
          type="button"></button>
</div>
<div class="modal-body">
  <app-alert type="danger" [message]="errors" *ngIf="errors" [hideCloseBtn]="true"></app-alert>

  <form [formGroup]="form" (ngSubmit)="saveUser()">
    <div class="row">
      <div class="col-6">
        <div class="mb-3">
          <label for="first_name">First name*</label>
          <input [ngClass]="{'is-invalid': isFieldInvalid('first_name')}" autocomplete="off" class="form-control"
                 formControlName="first_name" id="first_name" maxlength="100" name="first_name" required type="text"/>

          <div *ngIf="form.get('first_name').touched && form.get('first_name').hasError('required')"
               class="invalid-feedback">
            Please enter first name
          </div>

          <div *ngIf="fieldErrors['first_name']"
               class="invalid-feedback">{{fieldErrors['first_name'].join(' ')}}</div>
        </div>
      </div>
      <div class="col-6">
        <div class="mb-3">
          <label for="last_name">Last name*</label>
          <input [ngClass]="{'is-invalid': isFieldInvalid('last_name')}" autocomplete="off" class="form-control"
                 formControlName="last_name" id="last_name" maxlength="100" name="last_name" required type="text"/>

          <div *ngIf="form.get('last_name').touched && form.get('last_name').hasError('required')"
               class="invalid-feedback">
            Please enter last name
          </div>

          <div *ngIf="fieldErrors['last_name']" class="invalid-feedback">{{fieldErrors['last_name'].join(' ')}}</div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12" *ngIf="userService.isAdmin() || userService.isManager()">
        <div class="mb-3">
          <label for="last_name">Add groups to the user</label>
          <app-teams-selector class="w-100" formControlName="groups"
                              [showUsers]="false" [multiple]="true" [showMe]="true"
                              [isSystemGroupRemovable]="false" [(teams)]="selectedTeamUsers"
                              label="Select groups" placeholder="Search for a team...">
          </app-teams-selector>
        </div>
        <div class="loading-groups" *ngIf="loadingGroups">
          <div class="d-flex justify-content-center">
            <img src="assets/images/octimine_blue_spinner.gif">
          </div>
        </div>
      </div>
      <div class="col-12">
        <div class="d-flex justify-content-start">
          <div class="me-3">
            <label class="check-on-off m-0 p-0 align-middle" (click)="setIsManager()">
              <input type="checkbox" class="left" [checked]="editingUser?.is_manager"/>
            </label>
          </div>

          <label>Team manager</label>
        </div>
      </div>
      <div class="col-12 mt-2">
        <div class="d-flex justify-content-start">
          <div class="me-3">
            <label class="check-on-off m-0 p-0 align-middle" (click)="setUserSubscription($event)" [class.disabled]="isSubscriptionSaving">
              <input type="checkbox" class="left" [checked]="editingUser?.subscription?.type !== 'FREE' && editingUser?.subscription?.type !== 'COLLABORATOR'"/>
            </label>
          </div>
          <label>Premium user</label>
        </div>
      </div>
    </div>

    <input type="submit" hidden>
  </form>

</div>

<div class="modal-footer d-flex justify-content-between align-items-center">
  <div class="text-black-50"><small>Press <span class="fw-bold">Enter</span> to save</small></div>
  <div class="d-flex justify-content-end">
    <img src="assets/images/octimine_blue_spinner.gif" *ngIf="isSaving">
    <button (mousedown)="activeModal.dismiss('Cancel');" class="btn btn-ghost btn-md">Cancel</button>
    <button (click)="saveUser()" class="btn btn-primary btn-md"
            [disabled]="!form.dirty || form.invalid || isSaving">
      Save
    </button>
  </div>
</div>
