import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  <PERSON>signee,
  AssigneeTypeEnum,
  GroupService,
  SubscriptionType,
  TeamUser,
  User,
  UserGroup,
  UserService,
  UserTitlePipe
} from '@core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import _ from 'lodash';
import { concatMap, finalize, map, take } from 'rxjs/operators';
import { forkJoin, Observable, of, Subscription } from 'rxjs';

@Component({
  selector: 'app-user-form',
  templateUrl: './user-form.component.html',
  styleUrls: ['./user-form.component.scss']
})
export class UserFormComponent implements OnInit, OnDestroy {
  @Input() user = {} as TeamUser;

  editingUser = {} as TeamUser;
  form: UntypedFormGroup;
  fieldErrors = {};
  isSaving = false;
  isSubscriptionSaving = false;
  errors: string[] = [];
  selectedTeamUsers: TeamUser[];

  private subscriptions = new Subscription();

  constructor(
    public activeModal: NgbActiveModal,
    public userService: UserService,
    public groupService: GroupService,
    private userTitlePipe: UserTitlePipe
  ) {
  }

  get isEditing(): boolean {
    return !!(this.editingUser.id);
  }

  ngOnInit(): void {
    this.editingUser = _.cloneDeep(this.user);
    this.buildForm();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  saveUser() {
    if (this.form.valid) {
      this.form.disable();
      this.errors = [];
      this.isSaving = true;
      const payload = this.form.value;
      delete payload.groups;
      delete payload.is_free_user;

      const obs = this.isEditing ? this.userService.updateUser(this.editingUser.id, payload) :
        this.userService.createUser(payload);

      const obs$ = obs.pipe(
        finalize(() => this.isSaving = false),
        concatMap((val) => this.updateUserGroups(val)),
      ).subscribe({
        next: (val) => {
          const u = val.profile as TeamUser;
          u.groups = this.selectedTeamUsers.map((t) => ({...t} as any as UserGroup));
          u.subscription = this.editingUser.subscription;
          this.activeModal.close(u);
        },
        error: ({error}) => {
          console.warn(error);
          this.errors = ['Error when saving the user. ' + error.message];
          this.form.enable();
          this.fieldErrors = error.details || {};
        }
      });
      this.subscriptions.add(obs$);
    }
  }

  getTitle(): string {
    const userTitle = this.userTitlePipe.transform(this.editingUser);
    return this.isEditing ? `Edit user <span class="text-green">${userTitle}</span>` : 'Create new user';
  }

  isFieldInvalid(name: string): boolean {
    const field = this.form.get(name);
    return this.fieldErrors[name] || (field && field.touched && field.invalid);
  }

  setIsManager() {
    if (this.editingUser) {
      this.editingUser.is_manager = !this.editingUser.is_manager;
      const formField = this.form.get('is_manager');
      formField.setValue(this.editingUser.is_manager);
      formField.markAsDirty();
    }
  }

  setUserSubscription(event) {
    event.preventDefault();
    if (this.editingUser) {
      this.errors = [];
      const type = this.userService.isFreeTeamUser(this.editingUser) ? SubscriptionType.Professional : SubscriptionType.Free;
      const updateSubscription$ = this.userService.updateSubscription(this.editingUser.id, type)
        .pipe(
          take(1),
          finalize(() => this.isSubscriptionSaving = false)
        )
        .subscribe({
          next: (data) => {
            this.editingUser.subscription.type = type;
            const formField = this.form.get('is_free_user');
            formField.setValue(this.userService.isFreeTeamUser(this.editingUser));
            formField.markAsDirty();
          },
          error: (error) => {
            console.log(error.error);
            this.errors.push(error.error.message);
          }
        });
      this.subscriptions.add(updateSubscription$);
    }
  }

  private buildForm() {
    const userGroups = this.editingUser?.groups || [];
    const groups = userGroups.map((g) => this.userGroupToAssignee(g));

    this.form = new UntypedFormGroup({
      first_name: new UntypedFormControl(this.editingUser?.first_name, [
        Validators.required, Validators.maxLength(100)
      ]),
      last_name: new UntypedFormControl(this.editingUser?.last_name, [
        Validators.required, Validators.maxLength(100)
      ]),
      is_free_user: new UntypedFormControl(this.userService.isFreeTeamUser(this.editingUser)),
      is_manager: new UntypedFormControl(this.editingUser?.is_manager),
      groups: new UntypedFormControl(groups)
    });
  }

  private userGroupToAssignee(g: UserGroup): Assignee {
    return {id: g.id, type: AssigneeTypeEnum.GROUP} as Assignee;
  }

  private updateUserGroups(user: User): Observable<User> {
    const existingGroups = (this.user.groups || []).map((g) => this.userGroupToAssignee(g));
    const updatingGroups: Assignee[] = this.form.get('groups').value;
    const removingObs = _.differenceBy(existingGroups, updatingGroups, (x: Assignee) => x.id)
      .map((group: Assignee) => this.groupService.removeFromGroup(group.id, user.profile.id));
    const insertingObs = _.differenceBy(updatingGroups, existingGroups, (x: Assignee) => x.id)
      .map((group: Assignee) => this.groupService.addToGroup(group.id, user.profile.id));

    if (removingObs.length + insertingObs.length === 0) {
      return of(user);
    }

    return forkJoin(removingObs.concat(insertingObs)).pipe(map(() => user));
  }
}
