import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UsersComponent } from './users.component';
import { AuthGuard, FeatureGuard } from '@core';
import { AcceptInvitationComponent } from '@users/accept-invitation/accept-invitation.component';

const routes: Routes = [
  {path: '', component: UsersComponent, canActivate: [AuthGuard, FeatureGuard]},
  {path: 'accept-invitation', component: AcceptInvitationComponent, canActivate: [AuthGuard]},
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UsersRoutingModule {
}
