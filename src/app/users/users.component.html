<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>
  <app-workspace-menu workspaceTab="team"></app-workspace-menu>

  <div class="flex-fill page-content mb-4 container users-container">
    <div class="row">
      <app-page-bar [pageTitle]="pageTitle">
        <ng-container pageSubTitle *ngIf="userService.canManageGroup()">
          <app-users-top-navigation></app-users-top-navigation>
        </ng-container>
        <div leftSide class="d-flex justify-content-start align-items-center">
          <app-filter-term-input placeHolder="Search for team users" (termChanged)="filterUsers($event)"></app-filter-term-input>

          <ng-container>
            <a #statusBtn *ngFor="let status of filteringStatuses;"
               class="btn btn-md ms-1 cursor-pointer d-flex align-items-center" (click)="filterByStatus(status)"
               [ngClass]="filteringStatus == status ? 'btn-primary' : 'btn-ghost'">
              <div class="d-flex align-items-center"><i class="fas fa-circle user-status me-1" [ngClass]="status.toLowerCase()"></i> <div>{{status}}</div></div>
              <i *ngIf="filteringStatus == status" class="fas fa-times icon-right"
                 (click)="filterByStatus(null); $event.stopPropagation(); statusBtn.blur();"></i>
            </a>
          </ng-container>
        </div>
        <ng-container rightSide>
          <a *ngIf="canEdit" href="javascript:void(0)" class="item-bar icon-delete" [ngClass]="{'disabled': !hasSelectedUsers}"
             (click)="deleteSelectedUsers()" ngbTooltip="Delete selected users" container="body">
            Delete
          </a>

          <a *ngIf="canEdit" href="javascript:void(0)" class="item-bar" (click)="inviteUser()"
             ngbTooltip="Invite an user to your team" container="body">
            <i class="fas fa-user-plus icon-left"></i> Invite
          </a>
        </ng-container>
      </app-page-bar>

      <div class="d-flex flex-column justify-content-center col-12 mb-3" *ngIf="!isLoadingUsers else loadingTemplate">
        <app-alert type="success" [message]="successMessage" *ngIf="successMessage"></app-alert>
        <app-alert type="danger" [message]="errors" *ngIf="errors"></app-alert>
        <app-alert type="warning" [message]="noUsersWarningMessage" *ngIf="!teamUsers.length && !errors?.length"></app-alert>

        <table id="users-table" class="table table-condensed publication-table w-100 table-hover" *ngIf="teamUsers.length">
          <thead class="w-100">
            <tr>
              <th width="30px">
                <label *ngIf="canSelectAllUsers" class="checkbox m-0 p-0">
                  <input type="checkbox" (change)="selectAllUsers($event)" [checked]="areUsersSelected">
                  <span class="no-text">&nbsp;</span>
                </label>
              </th>
              <th class="text-center"></th>
              <th appTableSortIcon sortColumn="email" [sortingColumn]="sortBy" [sortingOrder]="sortOrder"
                  (click)="sort('email')" class="cursor-pointer">Email</th>
              <th appTableSortIcon sortColumn="first_name" [sortingColumn]="sortBy" [sortingOrder]="sortOrder"
                  (click)="sort('first_name')" class="cursor-pointer" width="150px">First name</th>
              <th appTableSortIcon sortColumn="last_name" [sortingColumn]="sortBy" [sortingOrder]="sortOrder"
                  (click)="sort('last_name')" class="cursor-pointer" width="150px">Last name</th>
              <th appTableSortIcon sortColumn="status" [sortingColumn]="sortBy" [sortingOrder]="sortOrder"
                  (click)="sort('status')" class="cursor-pointer" width="150px">Status</th>
              <th class="cursor-pointer" width="100px">Type</th>
              <th>Module(s)</th>
              <th>Group(s)</th>
              <th width="40px"></th>
            </tr>
          </thead>

          <tbody *ngFor="let u of teamUsers | paginate: paginationParams; let first = first;">
            <tr>
              <td>
                <label *ngIf="canEditUser(u)" class="checkbox m-0 p-0">
                  <input type="checkbox" (change)="selectUser($event, u)" [checked]="isUserSelected(u)">
                  <span class="no-text">&nbsp;</span>
                </label>
              </td>
              <td align="center">
                <app-user-avatar [user]="u" [hasSubTitle]="false" size="medium" [showTooltip]="false"></app-user-avatar>
              </td>
              <td>{{u.email}}</td>
              <td class="text-break">{{u.first_name}}</td>
              <td class="text-break">{{u.last_name}}</td>
              <td nowrap="nowrap">
                <div class="d-flex align-items-center">
                  <i class="fas fa-circle user-status me-1" [ngClass]="u.status.toLowerCase()"></i>
                  <div>{{ u.status }}</div>
                </div>
              </td>
              <td>
                <span class="badge rounded-pill" [ngClass]="userService.isPremiumTeamUser(u) ? 'badge-primary' : 'badge-secondary'">
                  {{userService.isPremiumTeamUser(u) ? 'Premium' : userService.isFreeTeamUser(u) ? 'Free': 'Collaborator' }}
                </span>
              </td>
              <td class="text-center" nowrap="nowrap">
                <img ngSrc="/assets/images/nav/nav-search-hover.svg" width="16" height="16"
                     ngbTooltip="Search" tooltipClass="white-tooltip" class="me-2">
                <img ngSrc="/assets/images/nav/nav-monitor-hover.svg" width="16" height="16"
                     ngbTooltip="Monitor" tooltipClass="white-tooltip" class="me-2"
                     [ngClass]="{'invisible': !userHasFeature(u, 'monitor')}">
                <img ngSrc="/assets/images/nav/nav-landscape-hover.svg" width="16" height="16"
                     ngbTooltip="Landscape" tooltipClass="white-tooltip"
                     [ngClass]="{'invisible': !userHasFeature(u, 'landscape')}">
              </td>
              <td>{{ getUserGroups(u) }}</td>
              <td>
                <div class="user-actions inline-dropdown text-end">
                  <div *ngIf="canEditUser(u)" class="abs" ngbDropdown placement="bottom-end">
                      <span class="icon" ngbDropdownToggle>
                        <i class="fas fa-ellipsis-v"></i>
                      </span>

                    <ul class=" shadow-lg" ngbDropdownMenu>
                      <li class="dropdown-item" (click)="editUser(u)">
                        <a href="javascript:void(0)"><i class="fa fa-edit"></i>Edit</a>
                      </li>
                      <li class="dropdown-item" (click)="changePassword(u)">
                        <a href="javascript:void(0)"><i class="fa fa-sync"></i>Reset password</a>
                      </li>
                      <li class="dropdown-item dropdown-menu-separator" (click)="removeUser(u)">
                        <a href="javascript:void(0)"><i class="fa fa-trash"></i>Remove</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="d-flex justify-content-between mt-2 align-items-center" *ngIf="!errors?.length && teamUsers?.length">
          <app-page-size [pageSize]="pageSize" (changeSize)="onPageSizeChanged($event)" [pageOptions]="pageOptions"></app-page-size>

          <div class="flex-fill">
            <pagination-controls *ngIf="pagination?.last_page > 1" id="users-pagination" class="d-flex justify-content-end"
                                 (pageChange)="onPageChange($event)" maxSize="10" directionLinks="false" autoHide="true">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>

  </div>

  <app-footer></app-footer>
</div>

<ng-template #loadingTemplate>
  <app-spinner></app-spinner>
</ng-template>
