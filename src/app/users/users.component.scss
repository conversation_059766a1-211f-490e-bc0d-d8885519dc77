@import 'scss/layout2021/variables';
@import "bootstrap/scss/variables";
@import 'scss/components/publication-table';

:host {
  @import 'scss/components/input-group';
  @import 'scss/components/inline-dropdown';

  $statues: ('active': #1CB1FF, 'blocked': #FF0000, 'delegated': #4B5D66, 'registered': #0EF5E5);

  .user-status {
    font-size: 10px;

    @each $status, $color in $statues {
      &.#{$status} {
        color: $color;
      }
    }
    &:before {
      border: 2px solid #fff;
      border-radius: 50%;
    }
  }

  $icons: ("delete": "icon-delete");

  .icon {
    @each $name, $icon in $icons {
      &-#{$name} {
        padding-left: 25px;
        background-position: 0;
        background-position-y: 7px;
        background-size: 15px;
        background-repeat: no-repeat;
        transition: all 0.2s ease;
        background-image:  url('/assets/images/layout2022/#{$icon}.svg');
        &:hover,&.active{
          background-image:  url('/assets/images/layout2022/#{$icon}-hover.svg');
        }
        &.disabled {
          background-image: url('/assets/images/layout2022/#{$icon}-disabled.svg');
        }
      }
    }
  }

  .user-actions {
    .dropdown.abs {
      top: 50% !important;
    }
  }
}

::ng-deep {
  .modal-sub-title{
    font-family: $font-open-sans-regular;
    font-size: 14px;
    color: #698A95;
  }
}
