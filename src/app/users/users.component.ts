import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ConfirmationDialogService, Feature, Page, TeamUser, UserService, UserStatusEnum, UserTitlePipe } from '@core';
import { debounce, map, switchMap, tap, take } from 'rxjs/operators';
import { BehaviorSubject, forkJoin, Observable, Subscription, timer } from 'rxjs';
import { UserFormComponent } from './user-form/user-form.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangePasswordFormComponent } from './change-password-form/change-password-form.component';
import { InviteUserComponent } from './invite-user/invite-user.component';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit, OnDestroy {
  isLoadingUsers = true;
  isFilteringOffline = false;
  filteringStatus: UserStatusEnum = null;
  teamUsers: TeamUser[] = [];
  pageSize = 25;
  pagination = {current_page: 0, page_size: this.pageSize, last_page: 0, total_hits: 0} as Page;
  successMessage = null;
  errors: string[] = [];
  pageOptions = [25, 50, 100];
  filterTerm = '';

  selectedUsers: Set<number> = new Set<number>();
  filteringStatuses = [UserStatusEnum.ACTIVE, UserStatusEnum.BLOCKED, UserStatusEnum.DELEGATED, UserStatusEnum.REGISTERED];

  sortBy = 'created_at';
  sortOrder: 'asc' | 'desc' = 'desc';
  private loadUsersSubject = new BehaviorSubject<{ debounceTime: number, payload: any }>({
    debounceTime: 0,
    payload: {
      sort_by: this.sortBy,
      sort_order: this.sortOrder,
      include_blocked: 1
    }
  });
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    private modalService: NgbModal,
    private userTitlePipe: UserTitlePipe,
    private confirmationDialogService: ConfirmationDialogService
  ) {
  }

  get noUsersWarningMessage(): string {
    if (this.filterTerm?.length) {
      return 'No user has been found. Please change your search term.';
    }

    switch (this.filteringStatus) {
      case UserStatusEnum.ACTIVE:
        return 'There are no active users.';
      case UserStatusEnum.BLOCKED:
        return 'There are no blocked users.';
      case UserStatusEnum.DELEGATED:
        return 'There are no delegated users.';
      case UserStatusEnum.REGISTERED:
        return 'There are no users that have registered but are not active yet.';
      default:
        return 'Your company does not have any users yet.';
    }
  }

  get paginationParams(): any {
    return {
      itemsPerPage: this.pagination.page_size,
      id: 'users-pagination',
      currentPage: this.pagination.current_page,
      totalItems: this.pagination.total_hits
    };
  }

  get canEdit(): boolean {
    return this.userService.isManager();
  }

  get pageTitle(): string {
    if (this.userService.canUseWorkflowFeature() && this.userService.getUser()?.profile?.company?.name?.length) {
      return `${this.userService.getUser()?.profile?.company?.name} - My team`;
    }
    return 'My team';
  }

  get areUsersSelected(): boolean {
    return this.teamUsers.every((d) => this.selectedUsers.has(d.id));
  }

  get hasSelectedUsers() {
    return this.selectedUsers.size > 0;
  }

  get canSelectAllUsers(): boolean {
    return this.teamUsers.some(u => this.canEditUser(u));
  }

  ngOnInit(): void {
    this.subscribeToLoadUsers();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onPageSizeChanged(size: number) {
    this.pageSize = size;
    this.pagination.current_page = 1;
    this.reloadUsers(0);
    this.clearMessages();
  }

  onPageChange(page: number) {
    this.pagination.current_page = page;
    this.reloadUsers(0);
    this.clearMessages();
  }

  selectAllUsers(event: Event) {
    const displayedUserIds = this.teamUsers.filter(u => this.canEditUser(u))
      .map((d) => d.id);
    if (event.target['checked']) {
      this.selectedUsers = new Set([...this.selectedUsers, ...displayedUserIds]);
    } else {
      this.selectedUsers = new Set([...this.selectedUsers].filter((id) => !displayedUserIds.includes(id)));
    }
  }

  selectUser(event: Event, u: TeamUser) {
    if (event.target['checked']) {
      this.selectedUsers.add(u.id);
    } else {
      this.selectedUsers.delete(u.id);
    }
  }

  isUserSelected(u: TeamUser): boolean {
    return this.selectedUsers.has(u.id);
  }

  editUser(user: TeamUser) {
    if (!user.is_admin) {
      this.showUserFormDialog(user);
    }
  }

  changePassword(user: TeamUser) {
    if (!user.is_admin) {
      this.showChangePasswordFormDialog(user);
    }
  }

  removeUser(user: TeamUser) {
    if (!user.is_admin) {
      this.showRemoveUserConfirmDialog(user);
    }
  }

  filterByStatus(stt: UserStatusEnum) {
    this.filteringStatus = stt;
    this.pagination.current_page = 1;
    this.reloadUsers(0);
  }

  inviteUser() {
    this.showInviteUserDialog();
  }

  canEditUser(u: TeamUser): boolean {
    return this.userService.isManager() && !u.is_admin;
  }

  getUserGroups(u: TeamUser): string {
    return u.groups.map(g => g.name).join(', ');
  }

  userHasFeature(u: TeamUser, featureName: 'monitor' | 'landscape'): boolean {
    if (!u.features?.length) {
      return false;
    }
    return u.features.some(f => f.name.toLowerCase() === featureName);
  }

  deleteSelectedUsers() {
    const subject = this.selectedUsers.size > 1 ? 'users' : 'user';
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green">${this.selectedUsers.size} selected ${subject}</span> ?
        <div class="mt-3 modal-sub-title">The ${subject} will be also <b>removed from all groups</b></div></div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');
    modalRef.then(val => {
      if (val) {
        const obs: Observable<any>[] = [];

        for (const u of this.selectedUsers) {
          obs.push(this.userService.removeUserFromCompany(u));
        }

        const obs$ = forkJoin(obs).pipe(take(1)).subscribe({next: response => {
          this.successMessage = `The ${this.selectedUsers.size} selected ${subject} ${this.selectedUsers.size > 1 ? 'have' : 'has'} been successfully deleted!`;
          this.selectedUsers.clear();
          this.reloadUsers(0);
        }, error: err => {
          console.log(err);
          this.errors = [`The ${this.selectedUsers.size} selected ${subject} could not be deleted!`];
          this.selectedUsers.clear();
        }});
        this.subscriptions.add(obs$);
      }
    });
  }

  filterUsers(val: string) {
    this.filterTerm = val;
    this.pagination = null;
    this.clearMessages();
    this.selectedUsers.clear();
    this.reloadUsers(val?.length ? 1000 : 0);
  }

  sort(field): void {
    if (field === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortOrder = 'asc';
    }

    this.reloadUsers(0);
  }

  private buildPayload(term: string, page_size: number, page: number, status: string): any {
    const payload = {page_size, page, include_blocked: 1};

    term = term?.trim();

    if (term) {
      payload['fullname'] = `like:%${term.trim()}%`;
    }

    if (status) {
      payload['status'] = status;
    }

    if (this.sortBy) {
      payload['sort_by'] = this.sortBy
    }

    if (this.sortOrder) {
      payload['sort_order'] = this.sortOrder;
    }

    return payload;
  }

  private loadUsers(payload): Observable<any> {
    return this.userService.getTeamUsers(payload)
      .pipe(
        tap(({page, users}) => {
          this.teamUsers = users;
          this.pagination = page as Page;
        })
      );
  }

  private subscribeToLoadUsers() {
    const loadUsers$ = this.loadUsersSubject.asObservable()
      .pipe(
        tap(() => this.isLoadingUsers = true),
        debounce(({debounceTime, payload}) => timer(debounceTime)),
        map(({payload}) => payload),
        switchMap((payload) => {
          return this.loadUsers(payload);
        }),
        tap(() => this.resetLoadingStatuses()),
      )
      .subscribe();
    this.subscriptions.add(loadUsers$);
  }

  private resetLoadingStatuses() {
    this.isLoadingUsers = false;
    this.isFilteringOffline = false;
  }

  private showUserFormDialog(user: TeamUser) {
    this.clearMessages();

    const modal = this.modalService.open(UserFormComponent, {size: 'xl'});
    modal.componentInstance.user = user;

    modal.result.then((value: TeamUser) => {
      const userTitle = this.userTitlePipe.transform(value);
      this.successMessage = `User <span class="text-green">${userTitle}</span> has been updated successfully.`;
      const i = this.teamUsers.findIndex(u => u.id === value.id);
      this.teamUsers.splice(i, 1, value);
    }, reason => {
    });
  }

  private showChangePasswordFormDialog(user: TeamUser) {
    this.clearMessages();

    const modal = this.modalService.open(ChangePasswordFormComponent, {size: 'md'});
    modal.componentInstance.user = user;

    modal.result.then(value => {
      const userTitle = this.userTitlePipe.transform(value);
      this.successMessage = `Password for user <span class="text-green">${userTitle}</span> has been changed successfully.`;
    }, reason => {
    });
  }

  private showRemoveUserConfirmDialog(user: TeamUser) {
    this.clearMessages();
    const userTitle = this.userTitlePipe.transform(user);
    const title = '<i class="fas fa-trash-alt fa-2x"></i>';
    const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green fw-bold">${userTitle}</span> ?
        <div class="mt-3 modal-sub-title">The user will be also <b>removed from all groups</b></div></div>`;
    const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'md');
    modalRef.then(val => {
      if (val) {
        const removeUserFromCompany$ = this.userService.removeUserFromCompany(user.id).pipe(take(1))
          .subscribe({next: (val) => {
            this.successMessage = `User <span class="text-green">${userTitle}</span> has been removed from your team successfully.`;
            this.reloadUsers(0);
          }, error: ({error}) => {
            console.warn(error);
            this.errors = [`Error when removing the user ${userTitle}. ` + error.message];
          }});
        this.subscriptions.add(removeUserFromCompany$);
      }
    });
  }

  private clearMessages() {
    this.successMessage = null;
    this.errors = [];
  }

  private reloadUsers(debounceTime: number) {
    const page = this.pagination?.current_page || 1;
    const payload = this.buildPayload(this.filterTerm, this.pageSize, page, this.filteringStatus);
    this.loadUsersSubject.next({debounceTime, payload});
  }

  private showInviteUserDialog() {
    this.clearMessages();

    const modal = this.modalService.open(InviteUserComponent, {size: 'lg'});
    modal.result.then(results => {
      if (results && Array.isArray(results)) {
        const plural = results.length > 1 ? 's' : '';
        const verb = results.length > 1 ? 'have' : 'has';
        this.successMessage = `User${plural} ${results.join(', ')} ${verb} been invited to your team successfully.`;
        this.reloadUsers(0);
      }
    }, reason => {
    });
  }
}
