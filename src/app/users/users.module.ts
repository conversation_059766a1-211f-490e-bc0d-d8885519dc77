import { NgModule } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';

import { UsersRoutingModule } from './users-routing.module';
import { UsersComponent } from './users.component';
import { SharedModule } from '@shared/shared.module';
import { UserFormComponent } from './user-form/user-form.component';
import { ChangePasswordFormComponent } from './change-password-form/change-password-form.component';
import { InviteUserComponent } from './invite-user/invite-user.component';
import { AcceptInvitationComponent } from './accept-invitation/accept-invitation.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedUsersModule } from './shared-users.module';


@NgModule({
  declarations: [
    UsersComponent,
    UserFormComponent,
    ChangePasswordFormComponent,
    InviteUserComponent,
    AcceptInvitationComponent
  ],
  imports: [
    CommonModule,
    UsersRoutingModule,
    SharedModule,
    NgbModule,
    SharedUsersModule,
    NgOptimizedImage
  ],
})
export class UsersModule {
}

