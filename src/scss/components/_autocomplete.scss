@import 'scss/layout2021/variables';

.autocomplete {
  position: relative;

  .autocomplete-spinner {
    position: absolute;
    top: calc(50% - 0.5rem);
    right: 8px;
    color: $autocomplete-spinner-color;
  }
}

::ng-deep {
  .ng-select-container {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;

    input {
      height: 28px;
    }
  }

  .ng-arrow-wrapper {
    display: none;
  }

  .ng-value-container .ng-value {
    margin: 0 5px;
    font-family: Open Sans Regular;
    font-size: .75rem;
    line-height: 24px;
    background-color: #00a083 !important;
    color: #fff;
    border-radius: 24px !important;
  }

  .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon:hover {
    background-color: #2F8A76;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
    background-color: #f5f7fa;
    color: #389a85;
  }

  .ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container {
    border-color: #80bdff;
    box-shadow: none;
  }

  .semantic-filters, .boosted-factors {
    .ng-value-container {
      padding-top: 3px;
      padding-bottom: 2px;
    }

    .ng-value {
      font-size: 0.875rem !important;
      border-radius: 4px !important;
      line-height: 24px !important;
      background-color: #00A08333 !important;
      color: #0F2C35 !important;
      padding-top: 2px !important;
      padding-bottom: 2px !important;

      .ng-value-icon {
        padding-right: 8px !important;
      }
      .ng-value-label {
        padding-left: 8px !important;
        padding-right: 8px !important;
      }

      &:hover,
      &:focus {
        background-color: #00A08333 !important;
        color: #0F2C35 !important;
      }
  
      .ng-value-icon {
        border: none !important;
        &:hover {
          background-color: #00A08333 !important;
          color: #0F2C35 !important;
          border-radius: 0px 4px 4px 0px !important;
        }
      }
    }
  }

  .boosted-factors .ng-select-container input {
    min-height: 32px !important;
  }
}

