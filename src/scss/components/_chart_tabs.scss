@import 'scss/layout2021/variables';


$icons:
  "basic" "icon-basic",
  "applicant" "icon-applicant",
  "authority" "icon-authority",
  "technology" "icon-technology",
  "classification" "icon-new-classification",
  "sustainability" "icon-sustainability",
  "analytics" "icon-new-analytics",
  "trend" "icon-trend-landscape",
  "competitors" "icon-competitors",
  "dashboard" "icon-my-dashboard",
  "add-dashboard" "icon-plus",
  "grid-1column" "icon-grid-1column",
  "grid-2columns" "icon-grid-2columns",
  "others" "icon-others";

.tabs-container {
  background-color: $chart-section-background;


  .charts-control-bar, .tabs-title {
    color: $brand-dark;
    margin: 1.6rem 0 .875rem;
  }
  .charts-control-optio n{
    font-size: 14px;
    &.single-btn {
      cursor: pointer;
      &:hover{
        color: $brand-green;
      }
    }
    &.list-btn {
      .dropdown-menu {
        font-size: 14px;
        margin-top: -2px;
      }
      .charts-control-option-icon {
        border-radius: 3px;
        line-height: 1.5rem;
        padding: 0 10px;
      }
      &:hover {
        .charts-control-option-icon {
          background-color: #D4E4E9;
        }
        .dropdown-menu {
          display: block;
          top: 97%;
          min-width: 240px;
        }
      }
    }

    &.disabled {
      cursor: not-allowed !important;
      pointer-events: none !important;
      opacity: .5;
      color: inherit;
    }
  }
  .tabs-title {
    font-family: $font-open-sans-semi-bold;
    font-size: 1.125rem;
    margin: 1.6rem 0 1rem;
  }

  .chart-icon {
    padding-left: 40px;
    padding-right: 1rem;
    background-size: 15px;
    background-repeat: no-repeat;
    background-position: 15px;
    transition: color 0.2s ease, background-image 0.2s ease;

    &.dropdown-item {
      background-position: 15px;
    }

    @each $name, $icon in $icons {
      &-#{$name} {
        background-image: url('/assets/images/layout2022/#{$icon}.svg');

        &:hover, &.active {
          background-image: url('/assets/images/layout2022/#{$icon}-hover.svg');
        }

        &.active.dropdown-item {
          background-image: url('/assets/images/layout2022/#{$icon}-active.svg');

          &:hover {
            background-image: url('/assets/images/layout2022/#{$icon}-hover.svg');
          }
        }
      }
    }
  }

  a {
    &.event-inprogress {
      cursor: progress !important;
      pointer-events: none !important;
    }
    &.disable {
      cursor: not-allowed !important;
      pointer-events: none !important;
      opacity: .5;
    }
  }
}
