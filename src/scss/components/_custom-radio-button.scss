$color1: #f4f4f4;
$color2: #00A083;
$color3: #666666;

.custom-radio-button {

  input[type="radio"] {
    position: absolute;
    opacity: 0;
    + .custom-radio-label {
      cursor: pointer;
      &:before {
        content: '';
        background: $color1;
        border-radius: 100%;
        border: 1px solid $color3;
        display: inline-block;
        width: 18px;
        height: 18px;
        position: relative;
        top: 0;
        margin-right: 10px;
        vertical-align: middle;
        cursor: pointer;
        text-align: center;
        transition: all 250ms ease;
      }
    }
    &:checked {
      + .custom-radio-label {
        &:before {
          background-color: $color2;
          box-shadow: inset 0 0 0 3px $color1;
        }
      }

      &:disabled {
        + .custom-radio-label {
          cursor: default;
          &:before {
            box-shadow: inset 0 0 0 3px $color1;
            border-color: $color3;
            background: $color3;
            cursor: default;
          }
        }
      }
    }
    &:focus, &:hover {
      + .custom-radio-label {
        &:before {
          outline: none;
          border-color: $color2;
        }
      }
    }
    + .custom-radio-label {
      &:empty {
        &:before {
          margin-right: 0;
        }
      }
    }
  }
}
