@import 'scss/layout2021/layout2021';

.filters-container {
  color: white;
  font-size: 1em;
  overflow: auto;

  &.sticky {
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #283339;
    max-height: 120px;
    min-height: 44px;
    position: fixed;
    z-index: 1020;
    margin-bottom: 0 !important;
    padding-bottom: 8px;
  }

  button {
    background-color: $brand-green;
    border: none;
    padding: 2px;
    color: white;

    &.disabled {
      background-color: rgba(255, 165, 0, 0.4);
    }

    &.inactive{
      opacity: .3;
    }
  }

  .title {
    background-color: #FFFFFF;
    padding: 3px 0 3px 10px;
    color: $color-text-03;
  }

  .value {
    background-color: #FFFFFF;
    padding: 3px 10px 3px 5px;
    color: $color-text-02;
  }

  .separator-icon {
    color: #FFFFFF;
  }
}
