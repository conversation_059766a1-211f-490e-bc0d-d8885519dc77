/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
body {
    margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block;
}
audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline;
}
audio:not([controls]) {
    display: none;
    height: 0;
}
[hidden],
template {
    display: none;
}
a {
    background-color: transparent;
}
a:active,
a:hover {
    outline: 0;
}
abbr[title] {
    border-bottom: 1px dotted;
}
b,
strong {
    font-weight: bold;
}
dfn {
    font-style: italic;
}
h1 {
    font-size: 2em;
    margin: 0.67em 0;
}
mark {
    background: #ff0;
    color: #000;
}
small {
    font-size: 80%;
}
sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
sup {
    top: -0.5em;
}
sub {
    bottom: -0.25em;
}
svg:not(:root) {
    overflow: hidden;
}
figure {
    margin: 1em 40px;
}
hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
}
pre {
    overflow: auto;
}
code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}
button {
    overflow: visible;
}
button,
select {
    text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}
button[disabled],
html input[disabled] {
    cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
input {
    line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
input[type="search"] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}
legend {
    border: 0;
    padding: 0;
}
textarea {
    overflow: auto;
}
optgroup {
    font-weight: bold;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
td,
th {
    padding: 0;
}
@-webkit-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-moz-keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes fadein {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-webkit-keyframes fadeinpost {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.2;
    }
}
@-moz-keyframes fadeinpost {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.2;
    }
}
@keyframes fadeinpost {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.2;
    }
}
@-webkit-keyframes slidefade {
    from {
        -webkit-transform: translateY(-40px);
        opacity: 0;
    }
    to {
        -webkit-transform: translateY(0px);
        opacity: 1;
    }
}
@-moz-keyframes slidefade {
    from {
        -moz-transform: translateY(-40px);
        opacity: 0;
    }
    to {
        -moz-transform: translateY(0px);
        opacity: 1;
    }
}
@keyframes slidefade {
    from {
        -webkit-transform: translateY(-40px);
        -moz-transform: translateY(-40px);
        -ms-transform: translateY(-40px);
        -o-transform: translateY(-40px);
        transform: translateY(-40px);
        opacity: 0;
    }
    to {
        -webkit-transform: translateY(0px);
        -moz-transform: translateY(0px);
        -ms-transform: translateY(0px);
        -o-transform: translateY(0px);
        transform: translateY(0px);
        opacity: 1;
    }
}
@-webkit-keyframes scroll {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
        -webkit-transform: translateY(0px);
        -ms-transform: translateY(0px);
        transform: translateY(0px);
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(10px);
        -ms-transform: translateY(10px);
        transform: translateY(10px);
    }
}
@-moz-keyframes scroll {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
        -webkit-transform: translateY(0px);
        -ms-transform: translateY(0px);
        transform: translateY(0px);
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(10px);
        -ms-transform: translateY(10px);
        transform: translateY(10px);
    }
}
@keyframes scroll {
    0% {
        opacity: 0;
    }
    10% {
        opacity: 1;
        -webkit-transform: translateY(0px);
        -ms-transform: translateY(0px);
        transform: translateY(0px);
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(10px);
        -ms-transform: translateY(10px);
        transform: translateY(10px);
    }
}
* {
    box-sizing: border-box;
}
html {
    height: 100%;
}
body {
    height: 100%;
    overflow-x: hidden;
    background-color: #fff;
    font: 300 17px/28px "Oxygen", "Arial", Sans-Serif;
}
a {
    color: #202a3a;
}
.wrapper {
    max-width: 900px;
    width: 90%;
    margin: 0 auto;
}
input:active,
input:focus {
    outline: none;
}
.tags,
.tagcloud {
    padding: 0;
    margin: 0;
    list-style: none;
}
.tags a,
.tagcloud a {
    display: inline-block;
    padding: 12px 25px;
    border-radius: 3px;
    background: #f1f3f4;
    color: #62646c;
    margin-right: 10px;
    margin-bottom: 10px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 11px;
    line-height: normal;
    text-transform: uppercase;
    outline: none !important;
}
.tags a:hover,
.tagcloud a:hover {
    background: #dee0e5;
    color: #4f5357;
}
.tags {
    float: left;
    width: 65%;
}
.tagcloud a {
    font-size: 9px !important;
    padding: 9px 17px;
    margin-bottom: 5px;
    margin-right: 5px;
}
.headerspace {
    height: 90px;
    width: 100%;
}
.hide {
    visibility: hidden !important;
}
::selection {
    background: #373b43;
    color: #fff;
}
::-moz-selection {
    background: #373b43;
    color: #fff;
}
a {
    text-decoration: none;
    -webkit-transition: color 0.15s ease-in, background 0.15s ease-in, border-color 0.15s ease-in;
    -moz-transition: color 0.15s ease-in, background 0.15s ease-in, border-color 0.15s ease-in;
    transition: color 0.15s ease-in, background 0.15s ease-in, border-color 0.15s ease-in;
}
strong {
    font-weight: 600;
}
input[type="text"],
input[type="email"],
input[type="password"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: inline-block;
    margin-bottom: 30px;
    background: #efefef;
    color: #373737;
    border: 0;
    border-radius: 3px;
    padding: 14px 18px;
    width: 34%;
    height: 55px;
}
textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: #efefef;
    color: #373737;
    border: 0;
    border-radius: 3px;
    width: 100%;
    padding: 14px 18px;
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 14px;
}
input[type="submit"],
button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 12px 32px;
    font-size: 12px;
    border: 0;
    border-radius: 90px;
    width: 25%;
    background: #efefef;
    color: #373737;
    margin-top: 20px;
    text-transform: uppercase;
    -webkit-transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
    -moz-transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
    transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
}
input[type="submit"]:hover,
button:hover {
    background: #373737;
    color: #fff;
}
body.admin-bar .headroom {
    padding-top: 50px;
    height: 102px;
}
body.admin-bar .drawer {
    padding-top: 32px;
}
.tag.cedar_format_quote,
.tag.cedar_format_video,
.tag.cedar_format_audio,
.tag.cedar_format_status,
.tag.cedar_format_image,
.category.cedar_format_quote,
.category.cedar_format_video,
.category.cedar_format_audio,
.category.cedar_format_status,
.category.cedar_format_image {
    display: none !important;
}
#wp-calendar {
    width: 100%;
    border-collapse: separate;
    border-spacing: 8px;
    margin: 0;
    font-family: "Montserrat", "Arial", Sans-Serif;
}
#wp-calendar caption {
    display: none;
}
#wp-calendar thead {
    font-weight: 400;
    font-size: 13px;
    color: #626262;
}
#wp-calendar thead th {
    padding-bottom: 20px;
    text-align: center;
}
#wp-calendar tbody {
    color: #aaa;
}
#wp-calendar tbody tr {
    margin-bottom: 10px;
}
#wp-calendar tbody td {
    font-weight: 400;
    font-size: 12px;
    margin-bottom: 10px;
    color: #939393;
    background: #f7f7f7;
    text-align: center;
    padding: 5px 9px;
    border-radius: 3px;
    -webkit-transition: background 0.15s ease-in-out;
    -moz-transition: background 0.15s ease-in-out;
    transition: background 0.15s ease-in-out;
}
#wp-calendar tbody td#today {
    background: #696b6c;
    color: #fff;
}
#wp-calendar tbody td#today a {
    color: #fff;
}
#wp-calendar tbody td:hover {
    background: #dbdbdb;
}
#wp-calendar tbody td:hover {
    background: #dbdbdb;
}
#wp-calendar tbody .pad {
    background: none;
}
#wp-calendar tbody .pad:hover {
    background: none;
}
#wp-calendar tbody td a {
    font-weight: 400;
    font-size: 13px;
    border-radius: 3px;
    color: #eee;
}
#wp-calendar tfoot {
    border-spacing: 0px;
    margin-top: 8px;
}
#wp-calendar tfoot a {
    font-weight: 400;
    font-size: 14px;
    color: #909090;
}
#wp-calendar a:hover {
    color: #7e7e7e;
}
#wp-calendar tfoot #next {
    text-transform: uppercase;
    text-align: right;
}
#wp-calendar tfoot #prev {
    text-transform: uppercase;
    padding-top: 10px;
}
#greenbutton {
    background-color: #4caf50;
    color: white;
    border-radius: 4px;
    display: block;
    margin: -4px auto auto auto;
    width: 210px;
    height: 38px;
}
.footerunit {
    width: 18%;
    border: none;
    display: block;
    float: left;
    font-weight: 900;
}
#logounit {
    width: 28%;
    display: block;
    float: left;
}
.legalunit {
    width: 18%;
    border: none;
    display: block;
    float: left;
    font-weight: 900;
}
@media (min-width: 800px) {
    li.option.drawernav {
        display: none;
    }
}
@media (max-width: 420px) {
    #wp-calendar {
        display: none;
    }
}
.postcontents .aligncenter,
div.aligncenter {
    display: block;
    margin: 5px auto 5px auto;
}
.postcontents .alignright {
    margin-left: 40px;
    float: right;
}
.postcontents .alignleft {
    margin-right: 40px;
    float: left;
}
.postcontents .aligncenter {
    display: block;
    margin: 30px auto 30px auto;
}
.postcontents a img.alignright {
    float: right;
}
.postcontents a img.alignleft {
    float: left;
}
.postcontents a img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.postcontents .wp-caption {
    background: #fff;
    border: 1px solid #f0f0f0;
    max-width: 96%;
    padding: 5px 3px 10px;
    text-align: center;
}
.postcontents .wp-caption.alignnone {
    margin: 5px 0 15px 0;
}
.postcontents .wp-caption.alignleft {
    margin: 5px 5% 15px 0;
}
@media (max-width: 700px) {
    .postcontents .wp-caption.alignleft {
        float: none;
        margin: 0 auto;
    }
}
.postcontents .wp-caption.alignright {
    margin: 5px 0 15px 5%;
}
@media (max-width: 700px) {
    .postcontents .wp-caption.alignright {
        float: none;
        margin: 0 auto;
    }
}
.postcontents .wp-caption img {
    border: 0 none;
    height: auto;
    margin: 0;
    max-width: 98.5%;
    padding: 0;
    width: auto;
}
.postcontents .wp-caption p.wp-caption-text {
    font-size: 11px;
    line-height: 17px;
    margin: 0;
    padding: 0 4px 5px;
}
.postcontents img.alignright {
    margin: 5px 0 15px 5%;
}
@media (max-width: 700px) {
    .postcontents img.alignright {
        float: none;
        margin: 0 auto;
    }
}
.postcontents img.alignleft {
    margin: 5px 5% 15px 0;
}
@media (max-width: 700px) {
    .postcontents img.alignleft {
        float: none;
        margin: 0 auto;
    }
}
.postcontents img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.postcontents .alignright {
    float: right;
}
.postcontents .alignleft {
    float: left;
}
.postcontents .aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
@media (max-width: 420px) {
    header.bloginfo.transparent {
        padding: 25px;
    }
}
header.bloginfo.headroom {
    display: none;
}
header.bloginfo.headroom--pinned {
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%);
}
header.bloginfo.headroom--unpinned {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%);
}
nav.main div {
    float: left;
}
nav.main ul li:hover a {
    color: #0f0f0f;
}
nav.main ul a,
nav.main ul span {
    cursor: pointer;
    display: inline-block;
    padding: 3px 12px 15px;
    text-transform: uppercase;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 14px;
    color: #1b4f8c;
    -webkit-transition: color 0.15s ease-in;
    -moz-transition: color 0.15s ease-in;
    transition: color 0.15s ease-in;
}
nav.main ul a:hover,
nav.main ul span:hover {
    color: #0f0f0f;
}
nav.main ul i {
    margin-left: 10px;
    font-size: 10px;
    vertical-align: 2px;
}
nav.main ul > li:before {
    font-family: "arial";
    font-size: 8px;
    content: "\25CF";
    vertical-align: 3px;
    color: #dfdfdf;
}
nav.main ul > li:hover ul {
    display: block;
}
nav.main ul li ul {
    position: absolute;
    top: 40px;
    right: 0;
    background: #fff;
    border-radius: 3px;
    padding: 10px;
    width: 225px;
    display: none;
    border: 2px solid #eee;
}
nav.main ul li ul:before {
    content: "";
    position: absolute;
    top: -10px;
    right: 7px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #eee;
}
nav.main ul li ul li {
    float: none;
    text-align: right;
    display: block;
    -webkit-transition: padding 0.125s ease-in;
    -moz-transition: padding 0.125s ease-in;
    transition: padding 0.125s ease-in;
}
nav.main ul li ul li:hover {
    padding-right: 5px;
}
nav.main ul li ul li:hover a {
    color: #46484d;
}
nav.main ul li ul a {
    display: block;
    font-size: 12px;
    color: #46484d;
    padding: 5px 10px;
}
nav.main ul li ul a:hover {
    color: #000;
}
nav.main ul li ul li:before {
    content: "";
}
nav.main ul .option i {
    margin: 0;
    font-size: 22px;
    vertical-align: -4px;
}
nav.main ul .option:before {
    content: "|";
    font-size: 16px;
    vertical-align: 0px;
}
nav.main ul li:first-child:before {
    content: "";
}
nav.main ul .option:first-child:before {
    content: "|";
}
@media (max-width: 1024px) {
    nav.main ul.nav-items {
    }
    nav.main ul .option:first-child:before {
        content: "";
    }
}
@media (max-width: 880px) {
    nav.main ul .option {
        display: none;
    }
    input[type="submit"],
    button {
        width: 70%;
    }
    nav.main ul .option:first-child:before {
        content: "";
    }
}
@media (max-width: 630px) {
    nav.main ul .option.searchnav {
        display: none;
    }
    nav.main ul .option:before {
        content: "";
    }
}
nav.light ul > li:hover > a {
    color: #fff;
}
nav.light ul a,
nav.light ul span {
    color: #d9d9d9;
}
nav.light ul a:hover,
nav.light ul span:hover {
    color: #fff;
}
nav.light ul li:before {
    color: #737373;
}
nav.light ul li ul {
    border: 2px solid #fff;
}
nav.light ul li ul li a {
    color: #46484d;
}
nav.light ul li ul li a:hover {
    color: #000;
}
nav.light ul li ul:before {
    border-bottom: 10px solid #fff;
}
.cover.short {
    height: 65vh;
}
.cover .patternbg {
    position: absolute;
    width: 100%;
    height: 100%;
    display: none;
}
@media (max-width: 560px) {
    .cover {
        height: 60vh;
    }
}
@media (max-height: 850px) {
    .cover {
    }
}
.mouse {
    width: 25px;
    position: absolute;
    height: 36px;
    border-radius: 15px;
    border: 2px solid #888;
    border: 2px solid rgba(255, 255, 255, 0.27);
    bottom: 40px;
    right: 40px;
    margin-left: -12px;
    cursor: pointer;
    -webkit-transition: border-color 0.2s ease-in;
    -moz-transition: border-color 0.2s ease-in;
    transition: border-color 0.2s ease-in;
}
.mouse .scroll {
    display: block;
    margin: 6px auto;
    width: 3px;
    height: 6px;
    border-radius: 4px;
    background: #b0b1b1;
    -webkit-animation-duration: 2s;
    -moz-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-name: scroll;
    -moz-animation-name: scroll;
    animation-name: scroll;
    -webkit-animation-iteration-count: infinite;
    -moz-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}
.mouse:hover {
    border: 2px solid rgba(255, 255, 255, 0.4);
}
@media (max-width: 560px) {
    .mouse {
        visibility: hidden;
    }
}
.cover.pagetitle {
    position: relative;
}
body.page-template .cover {
}
.vjs-poster,
.vjs-loading-spinner,
.vjs-text-track-display,
.vjs-control-bar,
.vjs-caption-settings {
    display: none;
}
.cover.author,
.cover.category,
.cover.search,
.cover.archive,
body.paged .cover {
    height: 65vh;
}
body.single .cover .background {
    -webkit-animation: fadeinpost 0.5s ease-in-out;
    -moz-animation: fadeinpost 0.5s ease-in-out;
    animation: fadeinpost 0.5s ease-in-out;
}
body.error404 .mouse {
    visibility: hidden;
}
.blogtitle,
.errortitle {
    display: table-cell;
    vertical-align: middle;
    width: 900px;
    position: relative;
    z-index: 1000;
}
.blogtitle h1,
.errortitle h1 {
    margin: 10% 0 50px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 44px;
    font-weight: 600;
    line-height: 52px;
    color: #fff;
}
.blogtitle .description,
.errortitle .description {
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 23px;
    line-height: 38px;
    color: #b3b3b3;
    letter-spacing: -1px;
    margin: 40px 0 35px;
}
.blogtitle .description a,
.errortitle .description a {
    color: #939393;
}
.blogtitle hr,
.errortitle hr {
    border: 0;
    height: 0px;
    width: 10%;
    margin: 0;
}
@media (max-width: 880px) {
    .blogtitle,
    .errortitle {
        width: 70%;
        text-align: center;
    }
    input[type="text"],
    input[type="email"],
    input[type="password"] {
        width: 70%;
    }
    .blogtitle .description,
    .errortitle .description {
        font-size: 20px;
        line-height: 32px;
    }
    .blogtitle hr,
    .errortitle hr {
        margin: 0 auto;
        width: 20%;
    }
}
@media (max-width: 560px) {
    .blogtitle,
    .errortitle {
        width: 80%;
    }
    .blogtitle img,
    .errortitle img {
        margin-top: 25%;
    }
    .blogtitle .description,
    .errortitle .description {
        font-size: 18px;
        line-height: 28px;
        margin-top: 25px;
    }
    .blogtitle hr,
    .errortitle hr {
        display: none;
    }
}
@media (max-height: 520px) {
    .blogtitle .description,
    .blogtitle hr,
    .errortitle .description,
    .errortitle hr {
        display: none;
    }
}
.categorytitle {
    display: table-cell;
    vertical-align: middle;
    width: 700px;
    position: relative;
    z-index: 1000;
}
.categorytitle .name {
    margin: 10% 0 25px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 44px;
    font-weight: 600;
    color: #fff;
    text-align: center;
    line-height: 60px;
    text-transform: capitalize;
}
.categorytitle p {
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 23px;
    line-height: 38px;
    color: #939393;
    letter-spacing: -1px;
    margin: 40px 0 35px;
    text-align: center;
}
.categorytitle hr {
    border: 0;
    height: 3px;
    width: 10%;
    background: #5a5a5a;
    margin: 0;
}
@media (max-width: 880px) {
    .categorytitle {
        width: 70%;
        text-align: center;
    }
    .categorytitle .name {
        font-size: 36px;
        line-height: 34px;
        margin: 20% 0 25px;
    }
    .categorytitle p {
        font-size: 20px;
        line-height: 32px;
    }
}
@media (max-width: 560px) {
    .categorytitle {
        width: 80%;
    }
    p {
        font-size: 0.9em;
    }
    .categorytitle .name {
        font-size: 30px;
        line-height: 32px;
    }
    .categorytitle p {
        font-size: 18px;
        line-height: 28px;
    }
}
@media (max-height: 675px) {
    .categorytitle p {
        display: none;
    }
}
.postlist .postitem:nth-child(even) {
    background: #f9f9f9;
}
.postitem .meta {
    overflow: hidden;
    padding: 0;
    margin: 0;
    list-style: none;
}
.postitem .meta li {
    display: block;
}
.postitem .meta li:last-child:before {
    content: "";
}
.postitem .meta li:nth-child(2) a {
    padding-right: 0;
}
.postitem .meta .category {
    float: left;
    display: none;
}
.postitem .meta .category a {
    display: inline-block;
    background: #1b4f8c;
    border-radius: 3px;
    padding: 3px 15px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    text-transform: uppercase;
    color: #ffffff;
}
.postitem .meta .category:first-child {
    display: initial;
}
.postitem .meta .date,
.postitem .meta .readtime,
.postitem .meta .issticky {
    float: right;
    margin-top: 3px;
}
.postitem .meta .date a,
.postitem .meta .readtime a,
.postitem .meta .issticky a {
    color: #b5b5b5;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 15px;
    letter-spacing: -1px;
    padding: 0 8px;
}
.postitem .meta .date a:hover,
.postitem .meta .readtime a:hover,
.postitem .meta .issticky a:hover {
    color: #5c5c5c;
}
.postitem .meta .date:before,
.postitem .meta .readtime:before,
.postitem .meta .issticky:before {
    margin: 0 4px;
    font-family: "arial";
    font-size: 8px;
    content: "\25CF";
    vertical-align: 3px;
    color: #e0e0e0;
}
.postitem .meta .date:first-child:before,
.postitem .meta .readtime:first-child:before,
.postitem .meta .issticky:first-child:before {
    content: "";
}
.postitem .meta .issticky {
    display: none;
    color: #b5b5b5;
}
.postitem .meta .issticky i {
    margin-left: 10px;
}
.postitem h1 a,
.postitem h2 a {
    display: block;
    color: #373737;
}
.postitem h1 a:hover,
.postitem h2 a:hover {
    color: #5e5e5e;
}
.postitem .excerpt {
    margin: 40px 0;
    font-family: "Oxygen", "Varela Round", Serif;
    font-size: 19px;
    font-weight: 400;
    line-height: 35px;
    color: #62646c;
    color: rgba(35, 37, 40, 0.7);
    overflow: hidden;
}
.postitem .author {
    display: none;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 15px;
    letter-spacing: -0.5px;
    color: #b5b5b5;
}
.postitem .author:hover {
    color: #7a7b8e;
}
.postitem .author img {
    width: 25px;
    height: 25px;
    border-radius: 50px;
    display: inline-block;
    vertical-align: -7px;
    margin-right: 15px;
}
@media (max-width: 750px) {
    .postitem {
        padding: 27% 0 50% 0;
    }
    .postitem h2 {
        margin: 40px 0;
        font-size: 38px;
        font-weight: 600;
        line-height: 52px;
    }
}
@media (max-width: 490px) {
    .postitem h2 {
        margin: 30px 0;
        font-size: 32px;
        line-height: 46px;
    }
    .postitem .excerpt {
        margin: 30px 0;
        font-size: 17px;
        line-height: 28px;
    }
    .postitem .meta .readtime {
        display: none;
    }
    .postitem .meta .category a {
        font-size: 10px;
    }
}
@media (max-width: 370px) {
    .postitem {
        text-align: center;
    }
    .postitem h2 {
        margin: 30px 0;
        font-size: 28px;
        line-height: 42px;
    }
    .postitem .meta .date {
        display: none;
    }
    .postitem .excerpt {
        display: none;
        font-size: 16px;
    }
    .postitem .meta .category {
        float: none;
        margin: 0 auto;
    }
}
.post-list-advrt {
    padding: 90px 0;
}
.post-list-advrt .advrt {
    width: 728px;
    max-width: 100%;
    height: 90px;
    margin: 0 auto;
}
@media (max-width: 860px) {
    .post-list-advrt {
        display: none;
    }
    .post-list-advrt + hr {
        display: none;
    }
}
nav.pagination {
    position: relative;
    border-top: 1px solid #f3f3f3;
    padding: 80px 0;
    overflow: hidden;
}
nav.pagination ul {
    padding: 0;
    margin: 0;
    list-style: none;
}
nav.pagination .previous {
    float: right;
}
nav.pagination .next {
    float: left;
}
nav.pagination .previous,
nav.pagination .next {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 13px;
    text-transform: uppercase;
    color: #373737;
    border-radius: 90px;
    border: 2px solid #e9e9e9;
    padding: 7px 36px;
    letter-spacing: -0.5px;
    -webkit-transition: border-color 0.1s ease-in-out;
    -moz-transition: border-color 0.1s ease-in-out;
    transition: border-color 0.1s ease-in-out;
}
nav.pagination .previous:hover,
nav.pagination .next:hover {
    border-color: #b6b7c5;
}
nav.pagination .previous i,
nav.pagination .next i {
    display: none;
}
nav.pagination ul.page-numbers {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    cursor: default;
}
nav.pagination ul.page-numbers li {
    display: inline-block;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 13px;
    text-transform: uppercase;
    color: #373737;
    margin: 0 2px;
}
nav.pagination ul.page-numbers a,
nav.pagination ul.page-numbers span {
    display: block;
    text-align: center;
    line-height: 39px;
    border: 2px solid #e9e9e9;
    width: 42px;
    height: 42px;
    border-radius: 90px;
    color: #373737;
    -webkit-transition: border-color 0.15s ease-in-out;
    -moz-transition: border-color 0.15s ease-in-out;
    transition: border-color 0.15s ease-in-out;
}
nav.pagination ul.page-numbers a {
    cursor: pointer;
}
nav.pagination ul.page-numbers a:hover {
    border-color: #b6b7c5;
}
nav.pagination ul.page-numbers span {
    background: #f9f9f9;
}
nav.pagination ul.page-numbers .dots {
    background: transparent;
    border: 0;
    margin: 0;
    padding: 0;
    width: 16px;
}
nav.pagination .button .sub {
    display: block;
    color: #373737;
}
@media (max-width: 740px) {
    nav.pagination ul.page-numbers {
        display: none;
    }
}
@media (max-width: 520px) {
    nav.pagination .button .main {
        display: none;
    }
    nav.pagination .button .sub i {
        display: inline-block;
    }
}
footer.main {
    overflow: hidden;
    padding: 30px;
    background: #1b4f8c;
    bottom: 0;
    width: 100%;
}
footer.main .copyright {
    float: left;
    padding: 0;
    border: 0;
}
footer.main .copyright:after {
    display: none;
}
footer.main .copyright .main {
    margin: 0 0 10px;
    font-family: "Oxygen", "Varela Round", Sans-Serif;
    font-size: 13px;
    line-height: normal;
    letter-spacing: -0.5px;
    color: #ced3d8;
}
footer.main .copyright .main a {
    color: #ced3d8;
}
footer.main .copyright .main a:hover {
    color: #fff;
}
footer.main .copyright .alt {
    margin: 0;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 13px;
    line-height: normal;
    letter-spacing: -0.5px;
    color: #515151;
}
footer.main .copyright .alt a {
    color: #515151;
}
footer.main .copyright .alt a:hover {
    color: #797979;
}
footer.main .social {
    float: right;
    padding: 0;
    margin: 5px 0;
    overflow: hidden;
    list-style: none;
}
footer.main .social li {
    display: block;
    float: left;
    border-left: 1px solid #ced3d8;
}
footer.main .social li:first-child {
    border: 0;
}
footer.main .social li:last-child a {
    padding-right: 0;
}
footer.main .social a {
    display: inline-block;
    margin: 0;
    padding: 0 17px;
    font-size: 14px;
    color: #fff;
}
footer.main .social .twitter:hover {
    color: #55acee;
}
footer.main .social .facebook:hover {
    color: #3b5998;
}
footer.main .social .github:hover {
    color: #999;
}
footer.main .social .youtube:hover {
    color: #c4302b;
}
footer.main .social .dribbble:hover {
    color: #ea4c89;
}
footer.main .social .google-plus:hover,
footer.main .social .google:hover {
    color: #dd4b39;
}
footer.main .social .instagram:hover {
    color: #3f729b;
}
footer.main .social .linkedin:hover {
    color: #0e76a8;
}
footer.main .social .pinterest:hover {
    color: #c8232c;
}
footer.main .social .skype:hover {
    color: #00aff0;
}
footer.main .social .tumblr:hover {
    color: #35465c;
}
footer.main .social .flickr:hover {
    color: #ff0084;
}
footer.main .social .reddit:hover {
    color: #5f99cf;
}
footer.main .social .stackoverflow:hover {
    color: #fe7a15;
}
footer.main .social .twitch:hover {
    color: #6441a5;
}
footer.main .social .vine:hover {
    color: #00b488;
}
footer.main .social .vk:hover {
    color: #45668e;
}
footer.main .social .vimeo:hover {
    color: #1ab7ea;
}
footer.main .social .weibo:hover {
    color: #d72928;
}
footer.main .social .soundcloud:hover {
    color: #f80;
}
footer.main .social .rss:hover {
    color: #ee802f;
}
@media (max-width: 900px) {
    footer.main .copyright,
    footer.main .social {
        clear: both;
        float: none;
        margin: 0 auto;
        text-align: center;
    }
    footer.main .social {
        display: inline-block;
        width: 100%;
        margin-top: 20px;
    }
    footer.main .social li {
        float: none;
        display: inline-block;
        margin-bottom: 10px;
    }
}
footer.postinfo {
    transform: translateY(0%);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    height: 0;
    border-top: 2px solid #f2f2f2;
    z-index: 1500;
    -webkit-transition: left 500ms ease-in-out;
    -moz-transition: left 500ms ease-in-out;
    transition: left 500ms ease-in-out;
    -webkit-transform: translateY(0%);
    -moz-transform: translateY(0%);
    -ms-transform: translateY(0%);
    -o-transform: translateY(0%);
    transform: translateY(0%);
}
footer.postinfo.slide {
    left: -400px;
}
footer.postinfo .socialoptions {
    position: absolute;
    bottom: 38px;
    width: 100%;
    background: #f4f4f4;
    background: rgba(244, 244, 244, 0.5);
    height: 75px;
    margin-left: -25px;
    display: none;
}
footer.postinfo .socialoptions .closeshare {
    cursor: pointer;
}
footer.postinfo .socialoptions ul {
    float: right;
    list-style: none;
    margin: 23px 15px 0 0;
}
footer.postinfo .socialoptions ul li {
    float: left;
    margin-right: 10px;
}
footer.postinfo .default {
    position: relative;
    padding: 22px 25px 15px;
}
footer.postinfo .authorinfo {
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 15px;
    font-weight: 400;
    line-height: normal;
    float: left;
}
footer.postinfo .authorinfo .gravatar {
    width: 30px;
    height: 30px;
    border-radius: 60px;
    vertical-align: -9px;
    margin-right: 10px;
}
footer.postinfo .authorinfo .authorname {
    color: #585858;
}
footer.postinfo .authorinfo .authordate {
    color: #b5b5b5;
    font-size: 15px;
}
footer.postinfo .authorinfo .authordate:before {
    font-family: "arial";
    font-size: 8px;
    content: "\25CF";
    vertical-align: 3px;
    margin: 0 10px;
}
footer.postinfo .postoptions {
    float: right;
    margin: 0;
    list-style: none;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 11px;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
}
footer.postinfo .postoptions li {
    display: block;
    float: left;
    border-left: 1px solid #dadada;
    padding: 11px 20px;
    color: #858585;
    cursor: pointer;
    -webkit-transition: color 0.15s ease-in-out;
    -moz-transition: color 0.15s ease-in-out;
    transition: color 0.15s ease-in-out;
}
footer.postinfo .postoptions li:hover {
    color: #323232;
}
footer.postinfo .postoptions i {
    margin-right: 10px;
}
footer.postinfo .postoptions li:first-child {
    border: 0;
}
footer.postinfo .postoptions li:last-child {
    padding-right: 0;
}
footer.postinfo .relatedposts {
    overflow: hidden;
    position: relative;
    display: none;
    padding: 22px 25px 15px;
}
footer.postinfo .relatedposts .previouspost,
footer.postinfo .relatedposts .nextpost {
    float: left;
    display: block;
}
footer.postinfo .relatedposts .nextpost {
    float: right;
    text-align: right;
}
footer.postinfo .relatedposts img {
    display: block;
    float: left;
    width: 30px;
    height: 30px;
    border-radius: 60px;
    margin-right: 10px;
}
footer.postinfo .relatedposts .nextpost img {
    float: right;
    margin-right: 0px;
    margin-left: 10px;
}
footer.postinfo .relatedposts .info {
    float: left;
}
footer.postinfo .relatedposts .nextpost img {
    float: right;
}
footer.postinfo .relatedposts span {
    display: block;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    line-height: normal;
    color: #8f99a3;
    text-transform: uppercase;
}
footer.postinfo .relatedposts .title {
    color: #585858;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 15px;
    text-transform: none;
}
footer.postinfo .relatedposts .backtotop {
    display: block;
    cursor: pointer;
    width: 28px;
    height: 28px;
    position: absolute;
    background: #e8e8e8;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    line-height: 25px;
    border-radius: 90px;
    -webkit-transition: background 0.15s ease-in-out;
    -moz-transition: background 0.15s ease-in-out;
    transition: background 0.15s ease-in-out;
}
footer.postinfo .relatedposts .backtotop i {
    font-size: 11px;
    color: #fff;
}
footer.postinfo .relatedposts .backtotop:hover {
    background: #4a5153;
}
@media (max-width: 750px) {
    footer.postinfo {
        display: none !important;
    }
}
body.post-template footer.main {
}
body.page-template footer.main {
    display: block;
}
body.page-template footer.postinfo {
    display: none !important;
}
.searchoverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #191a1a;
    background: rgba(25, 26, 26, 0.89);
    z-index: 2000;
    display: none;
    -webkit-transition: left 500ms ease-in-out;
    -moz-transition: left 500ms ease-in-out;
    transition: left 500ms ease-in-out;
}
.searchoverlay.slide {
    left: -400px;
}
.searchoverlay .closesearch {
    color: #fff;
    position: absolute;
    font-size: 28px;
    top: 30px;
    right: 30px;
    cursor: pointer;
}
.searchoverlay form {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 50%;
    max-width: 700px;
}
.searchoverlay form input {
    font: 600 32px "Varela Round", "Arial", Sans-Serif;
    color: #fff;
    text-transform: uppercase;
    background: transparent;
    border: 0;
    border-radius: 0;
    border-bottom: 2px solid #545555;
    padding: 40px 50px 40px 0;
    width: 100%;
}
.searchoverlay form input::-webkit-input-placeholder {
    color: #e7e7e7;
}
.searchoverlay form input:-moz-placeholder {
    color: #e7e7e7;
    opacity: 1;
}
.searchoverlay form input::-moz-placeholder {
    color: #e7e7e7;
    opacity: 1;
}
.searchoverlay form input:-ms-input-placeholder {
    color: #e7e7e7;
}
.searchoverlay form .fa-search {
    position: absolute;
    top: 48px;
    right: 10px;
    font-size: 24px;
    cursor: pointer;
    color: #fff8f8;
}
@media (max-width: 800px) {
    .searchoverlay form {
        width: 80%;
    }
}
.drawer .closedrawer {
    position: absolute;
    top: 0;
    right: 0;
    padding: 20px;
    cursor: pointer;
}
@media (max-width: 420px) {
    .drawer {
        width: 325px;
    }
}
.pagewrapper {
    position: relative;
    left: 0;
    -webkit-transition: left 500ms ease-in-out;
    -moz-transition: left 500ms ease-in-out;
    transition: left 500ms ease-in-out;
    -webkit-transform: translate3d(0, 0, 0);
}
.pagewrapper.slide {
    cursor: pointer;
    left: -400px;
}
@media (max-width: 420px) {
    .pagewrapper.slide {
        left: -325px;
    }
}
body.home-template.no-header .pagewrapper,
body.archive-template.no-header .pagewrapper {
    margin-top: 80px;
}
.widget {
    padding: 35px 0 0;
}
.widget:last-child {
    border: 0;
}
.widget > *:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}
.widget > *:first-child {
    margin-top: 0;
    padding-top: 0;
}
.widget .widget-title {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 16px;
    font-weight: 400;
    color: #242526;
    line-height: normal;
    text-transform: uppercase;
    margin-top: 0;
    padding-top: 0;
    margin-bottom: 25px;
}
.widget .widget-title a {
    color: #242526;
}
.widget hr {
    display: none;
    margin: 0;
    height: 1px;
    border: 0;
    width: 20%;
    background: #000;
}
.widget p {
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 15px;
    line-height: 33px;
    color: #7a7e81;
}
.widget ul {
    padding: 0;
    margin: 0;
    list-style: none;
}
.widget li a {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: -0.5px;
    display: inline-block;
    padding: 5px 0 5px 0;
    color: #8b8b8b;
}
.widget li a:hover {
    color: #4f4f4f;
}
.widget input,
.widget select,
.widget button {
    max-width: 100%;
}
.widget li:last-child a {
    margin-bottom: 0;
    padding-bottom: 0;
}
.widget:after {
    display: block;
    clear: both;
    content: "";
    width: 20%;
    background: #ececec;
    height: 1px;
    margin-top: 35px;
}
.widget.blog_info h1 {
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 34px;
    font-weight: 600;
    color: #373737;
}
.widget.blog_info img {
    max-width: 80%;
}
.widget.navigation {
    padding: 20px 0 0;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 14px;
    letter-spacing: -0.5px;
    line-height: normal;
    text-transform: uppercase;
}
.widget.navigation ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.widget.navigation li {
    padding: 0;
    margin: 0;
}
.widget.navigation li:last-child {
    border-bottom: 0;
}
.widget.navigation li a {
    display: block;
    padding: 15px 0;
    color: #242526;
    outline: none;
}
.widget.navigation li a i {
    color: #c6cdd6;
    font-size: 13px;
}
.widget.navigation li a:hover {
    color: #151516;
}
.widget.navigation li a:hover i {
    color: #9ca1a8;
}
.widget.navigation i {
    -webkit-transition: color 0.25s ease-in-out;
    -moz-transition: color 0.25s ease-in-out;
    transition: color 0.25s ease-in-out;
}
.widget.navigation .sub-menu {
    display: none;
    margin-bottom: 10px;
}
.widget.navigation .sub-menu li:last-child {
    border: 0;
}
.widget.navigation .sub-menu li a {
    padding: 10px 0 10px 10px;
    font-size: 14px;
    color: #8b8b8b;
}
.widget.navigation .sub-menu li a:hover {
    color: #4f4f4f;
}
.widget.navigation i {
    margin-left: 15px;
    color: #4e4e4e;
}
.widget.navigation:after {
    margin-top: 20px;
}
.widget.twitter .tweet {
    clear: both;
    margin-bottom: 35px;
    overflow: hidden;
}
.widget.twitter .tweet:last-child {
    margin-bottom: 0;
}
.widget.twitter .text {
    margin-top: 0;
    margin-bottom: 25px;
    font: 400 0.41667 "Oxygen", "Arial", Sans-Serif;
    color: #888e92;
}
.widget.twitter .info {
    overflow: hidden;
}
.widget.twitter .author {
    display: inline-block;
    float: left;
    font: 300 14px "Montserrat", "Arial", Sans-Serif;
    color: #7d8489;
}
.widget.twitter .author i {
    margin-right: 5px;
    color: #55acee;
    font-size: 16px;
    -webkit-transition: color 0.25s ease-in-out, background 0.25s ease-in-out, opacity 0.25s ease-in-out;
    -moz-transition: color 0.25s ease-in-out, background 0.25s ease-in-out, opacity 0.25s ease-in-out;
    transition: color 0.25s ease-in-out, background 0.25s ease-in-out, opacity 0.25s ease-in-out;
}
.widget.twitter .author:hover {
    color: #5e6266;
}
.widget.twitter .date {
    display: inline-block;
    float: right;
    font-weight: 400;
    font-size: 12px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    line-height: normal;
    color: #cfcfcf;
    margin-top: 2px;
    text-transform: uppercase;
}
.widget.twitter .date:hover {
    color: #9b9b9b;
}
.widget.social {
    padding: 35px 0 0;
}
.widget.social .socialdark {
    line-height: 27px;
}
.widget.social:after {
    margin-top: 25px;
}
.widget .searchform {
    position: relative;
}
.widget .searchform input {
    width: 100%;
    height: 47px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: -0.5px;
    border-radius: 100px;
    border: 1px solid #e0e0e0;
    padding: 0 50px;
    background: transparent;
    margin-bottom: 0;
}
.widget .searchform .fa-search {
    position: absolute;
    top: 18px;
    left: 22px;
    font-size: 13px;
    color: #b5b5b5;
}
.widget .searchform .submit {
    position: absolute;
    top: 18px;
    right: 22px;
    font-size: 13px;
    color: #858f96;
}
.widget.latestposts {
    overflow: hidden;
}
.widget.latestposts .post {
    overflow: hidden;
    margin-bottom: 40px;
}
.widget.latestposts .post:last-child {
    margin: 0;
}
.widget.latestposts .info {
    margin-top: -4px;
}
.widget.latestposts .category {
    display: inline-block;
    background: #3f9bd5;
    border-radius: 3px;
    padding: 6px 10px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 8px;
    line-height: normal;
    text-transform: uppercase;
    color: #ffffff;
    white-space: nowrap;
}
.widget.latestposts .category:hover {
    color: #e1e1e1;
}
.widget.latestposts .thumbnail {
    width: 25%;
    max-width: 65px;
    height: 65px;
    float: left;
    border-radius: 3px;
    display: block;
    overflow: hidden;
    position: relative;
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}
.widget.latestposts .thumbnail span {
    display: block;
    width: 100%;
    height: 100%;
    background-size: cover;
    -webkit-transition: opacity 0.25s ease-in-out;
    -moz-transition: opacity 0.25s ease-in-out;
    transition: opacity 0.25s ease-in-out;
}
.widget.latestposts .thumbnail .fa-link {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 24px;
    opacity: 0;
    z-index: 600;
    -webkit-transition: opacity 0.25s ease-in-out;
    -moz-transition: opacity 0.25s ease-in-out;
    transition: opacity 0.25s ease-in-out;
}
.widget.latestposts .thumbnail:hover span {
    opacity: 0.4;
}
.widget.latestposts .thumbnail:hover .fa-link {
    opacity: 1;
}
.widget.latestposts .excerpt {
    display: none;
}
.widget.latestposts .info {
    max-width: 67.5%;
    margin-left: 7.5%;
    float: left;
}
.widget.latestposts h5 {
    margin: 10px 0 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    letter-spacing: -1px;
    font-weight: 400;
    font-size: 16px;
    line-height: 25px;
    letter-spacing: -0.5px;
    color: #62646c;
}
.widget.latestposts h5 a {
    color: #62646c;
}
.widget.latestposts h5 a:hover {
    color: #62646c;
}
.widget.latestposts .meta {
    display: none;
}
.widget.relatedposts,
.widget.randomposts {
    overflow: hidden;
}
.widget.relatedposts .category,
.widget.randomposts .category {
    display: none;
}
.widget.relatedposts .feature,
.widget.randomposts .feature {
    display: block;
    height: 150px;
    width: 100%;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}
.widget.relatedposts .feature:after,
.widget.randomposts .feature:after {
    content: "Read Article";
    text-transform: uppercase;
    text-align: center;
    height: 150px;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    font-weight: 400;
    font-size: 17px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    line-height: normal;
    padding-top: 70px;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    -webkit-transition: opacity 0.3s ease-in-out;
    -moz-transition: opacity 0.3s ease-in-out;
    transition: opacity 0.3s ease-in-out;
}
.widget.relatedposts .feature:hover:after,
.widget.randomposts .feature:hover:after {
    opacity: 1;
}
.widget.relatedposts .feature:hover img,
.widget.randomposts .feature:hover img {
    -webkit-transform: scale(1.05);
    -moz-transform: scale(1.05);
    -ms-transform: scale(1.05);
    -o-transform: scale(1.05);
    transform: scale(1.05);
}
.widget.relatedposts .feature img,
.widget.randomposts .feature img {
    width: 100%;
    min-height: 100%;
    -webkit-transition: -webkit-transform 0.3s ease-in-out;
    -moz-transition: -moz-transform 0.3s ease-in-out;
    transition: transform 0.3s ease-in-out;
}
.widget.relatedposts article,
.widget.randomposts article {
    padding: 15px 0;
}
.widget.relatedposts article:first-of-type,
.widget.randomposts article:first-of-type {
    padding-top: 0;
}
.widget.relatedposts article:last-of-type,
.widget.randomposts article:last-of-type {
    padding-bottom: 0;
    border-bottom: 0;
}
.widget.relatedposts h4,
.widget.randomposts h4 {
    font-weight: 400;
    font-size: 18px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    line-height: 32px;
    letter-spacing: -1px;
    margin: 17px 0 9px;
}
.widget.relatedposts h4 a,
.widget.randomposts h4 a {
    color: #62646c;
    display: block;
    width: 100%;
}
.widget.relatedposts h4 a:hover,
.widget.randomposts h4 a:hover {
    color: #999999;
}
.widget.relatedposts .meta,
.widget.randomposts .meta {
    display: none;
}
.widget.subscribe {
    position: relative;
    overflow: hidden;
}
.widget.subscribe .email {
    padding: 18px 18px 18px 50px;
    border-radius: 0px;
    -webkit-border-top-right-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    -moz-border-radius-topright: 3px;
    -moz-border-radius-topleft: 3px;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.widget.subscribe label {
    display: none;
}
.widget.subscribe i {
    position: absolute;
    font-size: 14px;
    bottom: 54px;
    left: 20px;
    color: #a8a8a8;
    z-index: 100;
}
.widget.subscribe input[type="email"] {
    margin-top: 10px;
    width: 100%;
    height: 48px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: -0.5px;
    border-radius: 100px;
    border: 1px solid #e0e0e0;
    padding: 0 125px 0 50px;
    background: #fff;
    margin-bottom: 0;
}
.widget.subscribe input[type="submit"] {
    position: absolute;
    top: 15px;
    right: 5px;
    width: 35%;
    height: 38px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: -0.5px;
    border-radius: 100px;
    text-transform: uppercase;
    background: #f4f4f4;
    border: 0;
    margin-top: 0;
    padding: 0;
    -webkit-transition: background 0.15s ease-in;
    -moz-transition: background 0.15s ease-in;
    transition: background 0.15s ease-in;
}
.widget.subscribe input[type="submit"]:hover {
    background: #e5e5e5;
    color: #373737;
}
.widget.subscribe form {
    position: relative;
    overflow: hidden;
}
.widget.socialshare {
    overflow: hidden;
}
.widget.socialshare .options {
    overflow: hidden;
    padding-bottom: 0px;
}
.widget.socialshare > *:last-child {
    margin-bottom: 0;
}
.widget.socialshare > *:first-child {
    margin-top: 0;
}
.widget.socialshare a:nth-child(odd) {
    float: left;
}
.widget.socialshare a:nth-child(even) {
    float: right;
}
.widget.copyright .main {
    margin: 0 0 10px;
    font-family: "Oxygen", "Varela Round", "Arial", Sans-Serif;
    font-size: 13px;
    line-height: normal;
    letter-spacing: -0.5px;
    color: #505357;
}
.widget.copyright .main a {
    color: #505357;
}
.widget.copyright .main a:hover {
    color: #333538;
}
.widget.copyright .alt {
    margin: 0;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    line-height: normal;
    letter-spacing: -0.5px;
    color: #a9b1b9;
}
.widget.copyright .alt a {
    color: #a9b1b9;
}
.widget.copyright .alt a:hover {
    color: #797979;
}
.drawer .widget.authorprofile {
    padding: 35px 0 0;
}
.drawer .widget.authorprofile .info {
    overflow: hidden;
}
.drawer .widget.authorprofile .profile {
    float: left;
    width: 20%;
}
.drawer .widget.authorprofile .profile img {
    border-radius: 90px;
}
.drawer .widget.authorprofile .meta {
    float: left;
    width: 75%;
    margin-left: 5%;
}
.drawer .widget.authorprofile .meta .title,
.drawer .widget.authorprofile .meta .twittertag {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 13px;
    line-height: normal;
    text-transform: uppercase;
    color: #c9c9c9;
}
.drawer .widget.authorprofile .meta .title a,
.drawer .widget.authorprofile .meta .twittertag a {
    color: #c9c9c9;
}
.drawer .widget.authorprofile .meta .title {
    display: block;
}
.drawer .widget.authorprofile .meta h3 {
    margin: 0;
}
.drawer .widget.authorprofile .meta h3 a {
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-weight: 600;
    font-size: 18px;
    line-height: normal;
    color: #403f3f;
}
.drawer .widget.authorprofile .meta .twittertag {
    color: #c9c9c9;
    font-size: 13px;
    text-transform: none;
}
.drawer .widget.authorprofile .meta .twittertag:hover {
    color: #959595;
}
.drawer .widget.authorprofile p {
    margin: 20px 0 25px;
}
.drawer .widget.authorprofile .authorsocial li {
    display: inline-block;
    margin-bottom: 4px;
}
.drawer .widget.authorprofile .authorsocial a {
    font-size: 12px;
    line-height: 22px;
    width: 30px;
    height: 30px;
}
.drawer .widget.authorprofile .authorsocial a:hover {
    color: #fff;
}
.drawer .widget.copyright .ecko {
    display: block;
    margin-bottom: 10px;
}
.widget.advrt {
    text-align: center;
}
.widget.advrt > * {
    display: inline-block;
    margin: 0 auto;
}
.widget.advrt > script {
    display: none;
}
nav.social ul {
    padding: 0;
    margin: 0;
    list-style: none;
    overflow: hidden;
}
nav.social ul li {
    float: left;
    margin-right: 9px;
    margin-bottom: 10px;
}
nav.social .socialdark,
.socialdark {
    display: block;
    width: 35px;
    height: 35px;
    background: #f2f2f2;
    color: #636c77;
    border-radius: 70px;
    font-size: 12px;
    text-align: center;
    line-height: 35px;
    -webkit-transition: background 0.15s ease-in;
    -moz-transition: background 0.15s ease-in;
    transition: background 0.15s ease-in;
}
nav.social .socialdark.twitter,
.socialdark.twitter {
    background: #55acee;
    color: #fff;
}
nav.social .socialdark.twitter:hover,
.socialdark.twitter:hover {
    background: #1689e0;
}
nav.social .socialdark.facebook,
.socialdark.facebook {
    background: #3b5998;
    color: #fff;
}
nav.social .socialdark.facebook:hover,
.socialdark.facebook:hover {
    background: #263961;
}
nav.social .socialdark.github,
.socialdark.github {
    background: #333;
    color: #fff;
}
nav.social .socialdark.github:hover,
.socialdark.github:hover {
    background: #0d0d0d;
}
nav.social .socialdark.youtube,
.socialdark.youtube {
    background: #c4302b;
    color: #fff;
}
nav.social .socialdark.youtube:hover,
.socialdark.youtube:hover {
    background: #85211d;
}
nav.social .socialdark.dribbble,
.socialdark.dribbble {
    background: #ea4c89;
    color: #fff;
}
nav.social .socialdark.dribbble:hover,
.socialdark.dribbble:hover {
    background: #d11960;
}
nav.social .socialdark.google,
.socialdark.google {
    background: #dd4b39;
    color: #fff;
}
nav.social .socialdark.google:hover,
.socialdark.google:hover {
    background: #ac2d1e;
}
nav.social .socialdark.instagram,
.socialdark.instagram {
    background: #3f729b;
    color: #fff;
}
nav.social .socialdark.instagram:hover,
.socialdark.instagram:hover {
    background: #294a65;
}
nav.social .socialdark.linkedin,
.socialdark.linkedin {
    background: #0e76a8;
    color: #fff;
}
nav.social .socialdark.linkedin:hover,
.socialdark.linkedin:hover {
    background: #084461;
}
nav.social .socialdark.pinterest,
.socialdark.pinterest {
    background: #c8232c;
    color: #fff;
}
nav.social .socialdark.pinterest:hover,
.socialdark.pinterest:hover {
    background: #87181e;
}
nav.social .socialdark.skype,
.socialdark.skype {
    background: #00aff0;
    color: #fff;
}
nav.social .socialdark.skype:hover,
.socialdark.skype:hover {
    background: #0077a4;
}
nav.social .socialdark.tumblr,
.socialdark.tumblr {
    background: #35465c;
    color: #fff;
}
nav.social .socialdark.tumblr:hover,
.socialdark.tumblr:hover {
    background: #19212b;
}
nav.social .socialdark.flickr,
.socialdark.flickr {
    background: #ff0084;
    color: #fff;
}
nav.social .socialdark.flickr:hover,
.socialdark.flickr:hover {
    background: #b3005c;
}
nav.social .socialdark.reddit,
.socialdark.reddit {
    background: #5f99cf;
    color: #fff;
}
nav.social .socialdark.reddit:hover,
.socialdark.reddit:hover {
    background: #3473ad;
}
nav.social .socialdark.stackoverflow,
.socialdark.stackoverflow {
    background: #fe7a15;
    color: #fff;
}
nav.social .socialdark.stackoverflow:hover,
.socialdark.stackoverflow:hover {
    background: #c65601;
}
nav.social .socialdark.twitch,
.socialdark.twitch {
    background: #6441a5;
    color: #fff;
}
nav.social .socialdark.twitch:hover,
.socialdark.twitch:hover {
    background: #432b6e;
}
nav.social .socialdark.vine,
.socialdark.vine {
    background: #00b488;
    color: #fff;
}
nav.social .socialdark.vine:hover,
.socialdark.vine:hover {
    background: #00684e;
}
nav.social .socialdark.vk,
.socialdark.vk {
    background: #45668e;
    color: #fff;
}
nav.social .socialdark.vk:hover,
.socialdark.vk:hover {
    background: #2c415b;
}
nav.social .socialdark.vimeo,
.socialdark.vimeo {
    background: #1ab7ea;
    color: #fff;
}
nav.social .socialdark.vimeo:hover,
.socialdark.vimeo:hover {
    background: #0f83a8;
}
nav.social .socialdark.weibo,
.socialdark.weibo {
    background: #d72928;
    color: #fff;
}
nav.social .socialdark.weibo:hover,
.socialdark.weibo:hover {
    background: #971d1c;
}
nav.social .socialdark.email,
.socialdark.email {
    background: #76b852;
    color: #fff;
}
nav.social .socialdark.email:hover,
.socialdark.email:hover {
    background: #538637;
}
nav.social .socialdark.soundcloud,
.socialdark.soundcloud {
    background: #f80;
    color: #fff;
}
nav.social .socialdark.soundcloud:hover,
.socialdark.soundcloud:hover {
    background: #b35f00;
}
.sharebutton {
    font: 400 12px "Montserrat", "Arial", Sans-Serif;
    color: #fff;
    width: 47%;
    text-align: center;
    padding: 13px 0;
    background: #242424;
    border-radius: 90px;
    display: block;
    margin-top: 20px;
    -webkit-transition: background 0.2s ease-in-out;
    -moz-transition: background 0.2s ease-in-out;
    transition: background 0.2s ease-in-out;
}
.sharebutton:nth-child(1),
.sharebutton:nth-child(2) {
    margin-top: 0;
}
.sharebutton i {
    margin-right: 10px;
}
.sharebutton.left {
    float: left;
}
.sharebutton.right {
    float: right;
}
.sharebutton.twitter {
    background: #55acee;
}
.sharebutton.twitter:hover {
    background: #147bc9;
}
.sharebutton.facebook {
    background: #3b5998;
}
.sharebutton.facebook:hover {
    background: #1e2e4f;
}
.sharebutton.google {
    background: #dd4b39;
}
.sharebutton.google:hover {
    background: #96271a;
}
.sharebutton.reddit {
    background: #5f99cf;
}
.sharebutton.reddit:hover {
    background: #2e669a;
}
.sharebutton.pinterest {
    background: #c8232c;
}
.sharebutton.pinterest:hover {
    background: #711419;
}
.sharebutton.linkedin {
    background: #0e76a8;
}
.sharebutton.linkedin:hover {
    background: #06344a;
}
.sharebutton.email {
    background: #76b852;
}
.sharebutton.email:hover {
    background: #487430;
}
.posttitle .meta .category {
    position: relative;
}
.posttitle .meta .date,
.posttitle .meta .readtime {
    display: none;
}
.posttitle .title a {
    color: #fff;
}
.posttitle .excerpt {
    margin: 35px 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 24px;
    line-height: 38px;
    color: #939393;
    letter-spacing: -1px;
}
.posttitle .author {
    display: none;
}
.posttitle hr {
    border: 0;
    height: 3px;
    width: 10%;
    background: #5a5a5a;
    margin: 0;
}
.posttitle:after {
    display: block;
    content: "";
    clear: both;
    height: 0px;
    width: 15%;
}
.posttitle.middle {
    text-align: center;
}
.posttitle.middle .meta .category {
    float: none;
    margin: 0 auto;
}
.posttitle.middle:after {
    margin: 0 auto;
}
.posttitle.center h1 {
    color: #fff;
}
.posttitle.center h1 a:hover,
.posttitle.center h2 a:hover {
    color: #fff;
}
@media (max-width: 880px) {
    .posttitle.center {
        width: 70%;
        text-align: center;
    }
}
@media (max-width: 560px) {
    .posttitle.center {
        width: 85%;
        vertical-align: bottom;
    }
}
@media (max-width: 880px) and (max-height: 850px) {
    .posttitle.center:after {
        display: none;
    }
    .posttitle.center .excerpt {
        margin-bottom: 0;
    }
}
@media (max-width: 880px) and (max-height: 500px) {
    .posttitle.center h1,
    .posttitle.center h2 {
        font-size: 24px;
    }
}
@media (max-width: 880px) {
    .posttitle h1,
    .posttitle h2 {
        font-size: 2.9em;
        line-height: 52px;
    }
    .posttitle p {
        font-size: 2.5em;
    }
    .posttitle .meta .category {
        font-size: 10px;
        margin: 0 auto;
        float: none;
    }
    .posttitle .excerpt {
        font-size: 20px;
        line-height: 32px;
    }
    .posttitle:after {
        margin: 0 auto;
    }
}
@media (max-width: 560px) {
    .posttitle h1,
    .posttitle h2 {
        font-size: 2.5em;
        line-height: 38px;
        font-size: 2.6em;
    }
    .posttitle .meta .category {
        font-size: 10px;
        margin: 0 auto;
        float: none;
    }
    .posttitle .excerpt {
        font-size: 18px;
        line-height: 28px;
    }
}
.posttitle.single {
    margin-top: 90px;
    padding-bottom: 0;
}
.posttitle.single .title a {
    color: #292929;
    margin-bottom: 35px;
}
.posttitle.single .excerpt {
    color: #b5b5b5;
}
.posttitle.single:after {
    background: #f1f1f1;
}
@media (max-width: 880px) {
    body.single .posttitle,
    body.page .posttitle {
        text-align: center;
    }
    body.single .posttitle h1,
    body.single .posttitle .excerpt,
    body.page .posttitle h1,
    body.page .posttitle .excerpt {
        text-align: center;
    }
    body.single .posttitle .meta .category,
    body.page .posttitle .meta .category {
        margin: 0 auto;
    }
}
.cover.front .posttitle .meta .date,
.cover.front .posttitle .meta .readtime {
    display: block;
}
.cover.front .posttitle .meta .date:hover,
.cover.front .posttitle .meta .date a:hover,
.cover.front .posttitle .meta .readtime:hover,
.cover.front .posttitle .meta .readtime a:hover {
    color: #fff;
}
.cover.front .posttitle .author {
    display: none;
}
.cover.front .posttitle .author:hover,
.cover.front .posttitle .author a:hover {
    color: #fff;
}
.cover.front .posttitle .excerpt {
    font-size: 22px;
    line-height: 36px;
}
.cover.front .posttitle:after {
    height: 0px;
}
@media (max-width: 880px) {
    .cover.front .posttitle .meta .date,
    .cover.front .posttitle .meta .readtime,
    .cover.front .posttitle .author {
        display: none;
    }
}
@media (max-width: 560px) {
    .cover.front .posttitle {
        vertical-align: middle;
    }
}
.postcontents {
    marginfont-family: "Oxygen", "Varela Round", Serif;
    font-size: 19px;
    font-weight: 400;
    line-height: 35px;
    color: rgba(35, 37, 40, 0.7);
}
.postcontents > *:first-child,
.postcontents .wrapper > *:first-child {
    margin-top: 0;
}
.postcontents > *:last-child,
.postcontents .wrapper > *:last-child {
    margin-bottom: 0;
}
.postcontents h1 {
    margin: 40px 0 5px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 48px;
    font-weight: 600;
    line-height: 62px;
    letter-spacing: -1px;
    color: #373737;
}
.postcontents h2 {
    margin: 50px 0 30px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 28px;
    font-weight: 600;
    line-height: 44px;
    letter-spacing: -1px;
    color: #494a50;
}
.postcontents h3 {
    margin: 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 28px;
    font-weight: 400;
    line-height: 44px;
    letter-spacing: -1px;
    color: #b2b2b2;
}
.postcontents h4 {
    margin: 50px 0 30px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 22px;
    font-weight: 400;
    line-height: 34px;
    color: #b2b2b2;
}
.postcontents h5 {
    margin: 50px 0 30px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 18px;
    font-weight: 400;
    line-height: 28px;
    color: #b2b2b2;
}
.postcontents h6 {
    margin: 50px 0 30px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    color: #b2b2b2;
}
.postcontents hr {
    height: 1px;
    width: 60%;
    border: 0;
    margin: 50px auto;
    background: #dfdfdf;
}
.postcontents p,
.postcontents ul,
.postcontents ol {
    margin: 45px 0;
    padding: 0;
    font-family: "Oxygen", "Varela Round", Serif;
    font-size: 19px;
    font-weight: 400;
    line-height: 35px;
    color: rgba(35, 37, 40, 0.7);
}
.postcontents p {
    overflow: none;
}
.postcontents h1 + *,
.postcontents h2 + *,
.postcontents h3 + *,
.postcontents h4 + *,
.postcontents h5 + *,
.postcontents h6 + * {
    margin-top: 2px;
}
.postcontents ul,
.postcontents ol,
.postcontents dl {
    margin-left: 10%;
}
.postcontents li {
    margin: 0 0 20px;
    padding: 0 2.5%;
}
.postcontents li ul,
.postcontents li ol {
    margin: 25px 2.5%;
}
.postcontents li:last-child {
    margin-bottom: 0;
}
.postcontents dt {
    font-weight: 600;
}
.postcontents dd {
    margin: 0 0 20px 5%;
    font-size: 16px;
}
.postcontents pre {
    margin: 50px 0;
}
.postcontents .gallery {
    width: 102.5%;
}
.postcontents .gallery a {
    border: 0;
}
.postcontents .gallery img {
    border: 0 !important;
}
.postcontents .gallery .gallery-item {
    padding-right: 2.5% !important;
}
.postcontents .gallery dl {
    margin-left: inherit;
}
.postcontents .gallery-caption {
    margin: 0;
    padding: 10px 5%;
    line-height: 24px;
}
.postcontents .annotation {
    position: relative;
    width: 100%;
}
.postcontents .annotation .main {
    position: absolute;
    width: 250px;
    top: 0;
    right: -290px;
    border-bottom: 1px solid #eeeeef;
}
.postcontents .annotation .main h5 {
    margin: 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    color: #585858;
}
.postcontents .annotation .main p {
    margin: 15px 0 25px;
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 15px;
    color: #979aa3;
    line-height: 28px;
}
.postcontents table {
    clear: both;
    border-collapse: collapse;
    width: 100%;
}
.postcontents th {
    background: #f0f0f0;
    font-weight: 700;
}
.postcontents td,
.postcontents th {
    border: 1px solid #dbdbdb;
    padding: 0.5rem;
    text-align: left;
}
.postcontents .wide {
    display: block;
    margin: 20px -75px;
}
.postcontents .wide blockquote {
    padding: 0;
    margin: 20px 0;
    color: #38393c;
    font-family: "Valera Round", "Oxygen", Sans-Serif;
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    text-align: center;
    border: 0;
}
.postcontents .wide blockquote cite {
    display: block;
    margin-top: 0px;
    color: #c5c5c6;
    font-size: 22px;
    font-style: normal;
}
.postcontents .wide blockquote:after,
.postcontents .wide blockquote:before {
    display: block;
    content: "";
    background: #d0d0d0;
    width: 100%;
    height: 1px;
    margin: 0 0 30px 0;
}
.postcontents .wide blockquote:after {
    margin: 15px 0 15px 0;
}
.postcontents .wide blockquote:before {
    margin: 0 0 15px 0;
}
.postcontents blockquote {
    padding: 10px 0 10px 5%;
    margin: 50px 0;
    color: #38393c;
    font-family: "Valera Round", "Oxygen", Sans-Serif;
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    text-align: left;
    border-left: 6px solid #e8eaed;
    letter-spacing: -0.5px;
}
.postcontents blockquote p {
    margin: 15px 0 15px;
}
.postcontents blockquote cite {
    display: block;
    margin-top: 35px;
    color: #c5c5c6;
    font-size: 20px;
    font-style: normal;
}
.postcontents .subtitle {
    display: none;
}
.postcontents.dark {
    padding: 90px 0;
    background: #2c2e32;
    color: #e2e2e2 !important;
}
.postcontents.dark h1,
.postcontents.dark h2,
.postcontents.dark h3,
.postcontents.dark h4,
.postcontents.dark h5,
.postcontents.dark h6,
.postcontents.dark p,
.postcontents.dark ul,
.postcontents.dark li,
.postcontents.dark ol,
.postcontents.dark a {
    color: #e2e2e2;
}
.postcontents.dark p {
    color: #e2e2e2;
}
.postcontents .full {
    margin: 70px -35vw;
    overflow: hidden;
}
.postcontents .full img {
    width: 100%;
}
.postcontents a {
    color: #656770;
    border-bottom: 1px solid #d4d5de;
}
.postcontents a:hover {
    border-color: #a2a3ab;
}
.postcontents input[type="text"],
.postcontents input[type="email"],
.postcontents input[type="password"] {
    width: 100%;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 14px;
    background: #f5f5f5;
    border: 2px solid #f5f5f5;
    -webkit-transition: border-color 0.2s ease-in;
    -moz-transition: border-color 0.2s ease-in;
    transition: border-color 0.2s ease-in;
}
.postcontents input[type="text"]:focus,
.postcontents input[type="email"]:focus,
.postcontents input[type="password"]:focus {
    border-color: #cdcdcd;
}
.postcontents input[type="submit"],
.postcontents button {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 14px;
    margin-bottom: 30px;
    float: left;
    clear: both;
    margin-top: 0;
}
.postcontents button {
    padding: 4px 32px;
}
.postcontents textarea {
    margin-bottom: 30px;
    background: #f5f5f5;
    border: 2px solid #f5f5f5;
    -webkit-transition: border-color 0.2s ease-in;
    -moz-transition: border-color 0.2s ease-in;
    transition: border-color 0.2s ease-in;
}
.postcontents textarea:active,
.postcontents textarea:focus {
    outline: none;
}
.postcontents textarea:focus {
    border-color: #cdcdcd;
}
.postcontents q {
    display: block;
    max-width: 45%;
    float: right;
    text-align: right;
    margin-top: 0 !important;
    padding: 25px 0 25px 2.5%;
    border-top: 2px solid #eee;
    border-bottom: 2px solid #eee;
    margin: 0 0 2.5% 25px;
    font-family: "Varela Round", "Arial", Sans-Serif;
    color: #000;
    font-size: 21px;
    line-height: 34px;
}
.postcontents q:after,
.postcontents q:before {
    content: "";
}
.postcontents q.left {
    float: left;
    text-align: left;
    padding: 25px 2.5% 25px 0;
    margin: 0 2.5% 25px 0;
}
.postcontents q cite {
    text-decoration: none;
    font-style: normal;
    display: block;
    font-size: 18px;
    margin-top: 15px;
    color: #7f8088;
}
@media (max-width: 1375px) {
    .postcontents .annotation .main {
        display: none;
    }
    .postcontents .annotation .main p {
        margin: 10px 0;
    }
}
@media (max-width: 1050px) {
    .postcontents .wide {
        display: block;
        margin: 50px 0;
    }
}
@media (max-width: 840px) {
    .postcontents {
        margin: 10% auto;
    }
}
@media (max-width: 640px) {
    .postcontents {
        font-size: 18px;
    }
    .postcontents.dark {
        padding: 40px 0;
    }
    .postcontents h1 {
        font-size: 38px;
        line-height: 48px;
    }
    .postcontents h2 {
        font-size: 24px;
        line-height: 38px;
    }
    .postcontents h3 {
        font-size: 24px;
        line-height: 38px;
    }
    .postcontents h4 {
        font-size: 20px;
        line-height: 30px;
    }
    .postcontents h5 {
        font-size: 16px;
        line-height: 28px;
    }
    .postcontents h1,
    .postcontents h2,
    .postcontents h3,
    .postcontents h4,
    .postcontents h5,
    .postcontents h6,
    .postcontents p,
    .postcontents ul,
    .postcontents ol,
    .postcontents blockquote,
    .postcontents pre,
    .postcontents .wide blockquote,
    .postcontents .wide {
        margin-top: 40px;
        margin-bottom: 40px;
    }
    .postcontents hr {
        margin: 40px auto;
    }
    .postcontents p,
    .postcontents ul,
    .postcontents ol {
        font-size: 18px;
        line-height: 32px;
    }
}
@media (max-width: 450px) {
    #saveleft,
    #saveright {
        display: none;
    }
    .postcontents {
        font-size: 16px;
    }
    .postcontents.dark {
        padding: 30px 0;
    }
    .postcontents h1 {
        font-size: 36px;
        line-height: 46px;
    }
    .postcontents h2 {
        font-size: 22px;
        line-height: 36px;
    }
    .postcontents h3 {
        font-size: 22px;
        line-height: 36px;
    }
    .postcontents h4 {
        font-size: 18px;
        line-height: 28px;
    }
    .postcontents h5 {
        font-size: 15px;
        line-height: 26px;
    }
    .postcontents h1,
    .postcontents h2,
    .postcontents h3,
    .postcontents h4,
    .postcontents h5,
    .postcontents h6,
    .postcontents p,
    .postcontents ul,
    .postcontents ol,
    .postcontents blockquote,
    .postcontents pre,
    .postcontents .wide blockquote,
    .postcontents .wide {
        margin-top: 30px;
        margin-bottom: 30px;
    }
    .postcontents hr {
        margin: 30px auto;
    }
    .postcontents ul,
    .postcontents ol {
        font-size: 16px;
        line-height: 30px;
        display: none;
    }
    .postitem h1.landingpage {
        font-size: 2.6em !important;
    }
    .postitem p.landingpage {
        display: none;
    }
    header.bloginfo .title a img {
        left: 32%;
    }
    #stellbrink {
        display: none;
    }
    .space {
        display: none;
    }
    .footerunit,
    #logounit,
    #contactunit {
        display: none;
    }
    .legalunit {
        width: 100%;
    }
    #greenbutton {
        display: none;
    }
} /*!
 * Justified Gallery - v3.6.0
 * http://miromannino.github.io/Justified-Gallery/
 * Copyright (c) 2015 Miro Mannino
 * Licensed under the MIT license.
 */
@-webkit-keyframes justified-gallery-show-caption-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.7;
    }
}
@-moz-keyframes justified-gallery-show-caption-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.7;
    }
}
@-o-keyframes justified-gallery-show-caption-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.7;
    }
}
@keyframes justified-gallery-show-caption-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.7;
    }
}
@-webkit-keyframes justified-gallery-show-entry-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-moz-keyframes justified-gallery-show-entry-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-o-keyframes justified-gallery-show-entry-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes justified-gallery-show-entry-animation {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
.justified-gallery {
    width: 100%;
    position: relative;
    overflow: hidden;
}
.justified-gallery > a,
.justified-gallery > div {
    position: absolute;
    display: inline-block;
    overflow: hidden;
    opacity: 0;
    filter: alpha(opacity=0);
}
.justified-gallery > a > img,
.justified-gallery > div > img,
.justified-gallery > a > a > img,
.justified-gallery > div > a > img {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: 0;
    padding: 0;
    border: 0;
}
.justified-gallery > a > .caption,
.justified-gallery > div > .caption {
    display: none;
    position: absolute;
    bottom: 0;
    padding: 5px;
    background-color: #000;
    left: 0;
    right: 0;
    margin: 0;
    color: #fff;
    font-size: 12px;
    font-weight: 300;
    font-family: sans-serif;
}
.justified-gallery > a > .caption.caption-visible,
.justified-gallery > div > .caption.caption-visible {
    display: initial;
    opacity: 0.7;
    filter: "alpha(opacity=70)";
    -webkit-animation: justified-gallery-show-caption-animation 500ms 0 ease;
    -moz-animation: justified-gallery-show-caption-animation 500ms 0 ease;
    -ms-animation: justified-gallery-show-caption-animation 500ms 0 ease;
}
.justified-gallery > .entry-visible {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-animation: justified-gallery-show-entry-animation 500ms 0 ease;
    -moz-animation: justified-gallery-show-entry-animation 500ms 0 ease;
    -ms-animation: justified-gallery-show-entry-animation 500ms 0 ease;
}
.justified-gallery > .jg-filtered {
    display: none;
}
.justified-gallery > .spinner {
    position: absolute;
    bottom: 0;
    margin-left: -24px;
    padding: 10px 0;
    left: 50%;
    opacity: initial;
    filter: initial;
    overflow: initial;
}
.justified-gallery > .spinner > span {
    display: inline-block;
    opacity: 0;
    filter: alpha(opacity=0);
    width: 8px;
    height: 8px;
    margin: 0 4px;
    background-color: #000;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
}
.postbottom {
    margin: 90px auto 80px;
}
.postbottom ul.tags {
    float: left;
}
.postbottom ul.tags li:first-child {
    display: none;
}
.postbottom .info {
    clear: both;
    overflow: hidden;
}
.postbottom .info .tags a:first-child {
    display: none;
}
.postbottom .categories .category {
    float: right;
    width: 30%;
    display: none;
    padding: 12px 25px;
    border-radius: 3px;
    background: #1b4f8c;
    color: #fff;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 11px;
    line-height: normal;
    text-transform: uppercase;
    text-align: center;
}
.postbottom .categories .category:first-child {
    display: block;
}
.postbottom .shareoptions {
    margin-top: 30px;
    padding-bottom: 50px;
    clear: both;
    overflow: hidden;
}
.postbottom .permalink {
    width: 65%;
    overflow: hidden;
    display: block;
    float: left;
    margin-top: 11px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 11px;
    line-height: normal;
    color: #858585;
}
.postbottom .permalink:hover {
    color: #575757;
}
.postbottom .sharebuttons {
    padding: 0;
    margin: 0;
    float: right;
}
.postbottom .sharebuttons li {
    display: block;
    float: left;
    margin-left: 8px;
}
.postbottom .sharebuttons li:first-child {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 11px;
    line-height: normal;
    text-transform: uppercase;
    color: #868686;
    margin-top: 11px;
}
@media (max-width: 640px) {
    .postbottom {
        margin: 45px auto 40px;
    }
    .postbottom:before {
        margin-bottom: 40px;
    }
}
@media (max-width: 450px) {
    .postbottom {
        margin: 30px auto 35px;
    }
    .postbottom:before {
        margin-bottom: 35px;
    }
}
@media (max-width: 800px) {
    .postbottom .permalink {
        display: none;
    }
}
@media (max-width: 630px) {
    .postbottom {
        text-align: center;
    }
    .postbottom .tags {
        width: 100%;
        float: none;
        text-align: center;
    }
    .postbottom .tags a {
        float: none;
        display: inline-block;
    }
    .postbottom .categories .category {
        clear: both;
        float: none;
        width: auto;
        margin-top: 30px;
    }
    .postbottom .sharebuttons {
        float: none;
        margin: 0 auto;
        overflow: hidden;
    }
    .postbottom .sharebuttons li {
        float: none;
        display: inline-block;
    }
}
.pagewrapper .widget.authorprofile,
.pagewrapper .widget.authorprofile,
.authortitle {
    margin: 110px 0 0;
    background: #f9f9f9;
    border-top: 1px solid #f3f3f3;
    text-align: center;
}
.pagewrapper .widget.authorprofile .info,
.pagewrapper .widget.authorprofile .info,
.authortitle .info {
    margin: -73px 0 30px;
}
.pagewrapper .widget.authorprofile .gravatar img,
.pagewrapper .widget.authorprofile .gravatar img,
.authortitle .gravatar img {
    width: 80px;
    height: 80px;
    border-radius: 90px;
    overflow: hidden;
}
.pagewrapper .widget.authorprofile .meta,
.pagewrapper .widget.authorprofile .meta,
.authortitle .meta {
    margin-top: 40px;
}
.pagewrapper .widget.authorprofile .meta .title,
.pagewrapper .widget.authorprofile .meta .twittertag,
.pagewrapper .widget.authorprofile .meta .title,
.pagewrapper .widget.authorprofile .meta .twittertag,
.authortitle .meta .title,
.authortitle .meta .twittertag {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 15px;
    line-height: normal;
    text-transform: uppercase;
    color: #c9c9c9;
}
.pagewrapper .widget.authorprofile .meta .title,
.pagewrapper .widget.authorprofile .meta .title,
.authortitle .meta .title {
    display: block;
    margin-top: 26px;
}
.pagewrapper .widget.authorprofile .meta .twittertag,
.pagewrapper .widget.authorprofile .meta .twittertag,
.authortitle .meta .twittertag {
    color: #c9c9c9;
    font-size: 14px;
    text-transform: none;
}
.pagewrapper .widget.authorprofile .meta .twittertag:hover,
.pagewrapper .widget.authorprofile .meta .twittertag:hover,
.authortitle .meta .twittertag:hover {
    color: #959595;
}
.pagewrapper .widget.authorprofile .meta h3,
.pagewrapper .widget.authorprofile .meta h3,
.authortitle .meta h3 {
    margin: 5px 0 6px;
}
.pagewrapper .widget.authorprofile .meta h3 a,
.pagewrapper .widget.authorprofile .meta h3 a,
.authortitle .meta h3 a {
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-weight: 600;
    font-size: 24px;
    line-height: normal;
    color: #403f3f;
}
.pagewrapper .widget.authorprofile p,
.pagewrapper .widget.authorprofile p,
.authortitle p {
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 17px;
    line-height: 32px;
    margin: 20px 0 65px;
    color: #62646c;
}
.pagewrapper .widget.authorprofile .authorsocial,
.pagewrapper .widget.authorprofile .authorsocial,
.authortitle .authorsocial {
    overflow: hidden;
    display: inline-block;
    margin-bottom: 40px;
    text-align: center;
    padding: 0;
}
.pagewrapper .widget.authorprofile .authorsocial li,
.pagewrapper .widget.authorprofile .authorsocial li,
.authortitle .authorsocial li {
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 10px;
}
.pagewrapper .widget.authorprofile .authorsocial li:last-child,
.pagewrapper .widget.authorprofile .authorsocial li:last-child,
.authortitle .authorsocial li:last-child {
    margin-right: 0;
}
.pagewrapper .widget.authorprofile .authorsocial li a,
.pagewrapper .widget.authorprofile .authorsocial li a,
.authortitle .authorsocial li a {
    font-size: 12px;
    line-height: 26px;
}
.pagewrapper .widget.authorprofile .authorsocial li a:hover,
.pagewrapper .widget.authorprofile .authorsocial li a:hover,
.authortitle .authorsocial li a:hover {
    color: #fff;
}
.comments {
    clear: both;
    margin-bottom: 0;
}
.comments a {
    color: #1b4f8c;
}
.commentheader {
    cursor: pointer;
    padding: 30px 0;
    background: #f6f6f6;
    border-top: 1px solid #eeeeee;
    text-align: center;
    text-transform: uppercase;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 18px;
    font-weight: 400;
    line-height: normal;
    color: #373737;
}
.commentheader .commentcount {
    display: inline-block;
    padding: 6px 16px;
    background: #4a5153;
    border-radius: 100px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    color: #ffffff;
    margin-left: 15px;
    vertical-align: 3px;
}
.commentheader i {
    font-size: 18px;
    color: #b5bcbf;
    margin-right: 10px;
}
.commentitems {
    background: #222326;
    padding: 90px 0 140px;
}
.commentitems .notification {
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 15px;
    color: #ffffff;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 50px;
    padding-bottom: 50px;
    border-bottom: 1px solid #353535;
}
.commentitems .notification i {
    margin-right: 10px;
}
@media (max-width: 700px) {
    .commentitems {
        padding-top: 40px;
        padding-bottom: 60px;
    }
}
@media (max-width: 400px) {
    .commentitems {
        padding-top: 20px;
        padding-bottom: 40px;
    }
}
.commentwrap {
    padding: 0;
    margin: 0;
    list-style: none;
}
.children {
    -webkit-padding-start: 12%;
}
li.comment {
    list-style: none;
}
li.comment .contents {
    overflow: hidden;
}
li.comment .contents .body a {
    border-bottom: 1px solid #7c7c7c;
}
li.comment .contents .body * {
    color: #fff !important;
}
li.comment .commentinfo {
    overflow: hidden;
    margin-bottom: 35px;
}
li.comment .profile {
    display: block;
    width: 12%;
    float: left;
}
li.comment .profile img {
    border-radius: 90px;
    max-width: 64px;
    height: 64px;
}
li.comment .main {
    width: 88%;
    float: right;
}
li.comment hr {
    margin: 45px 0;
    height: 1px;
    width: 100%;
    border: 0;
    background: #393a3d;
}
li.comment h4 {
    clear: both;
    margin: 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 18px;
    color: #f4f4f4;
    font-weight: 400;
    line-height: normal;
}
li.comment h4 a {
    color: #f4f4f4;
}
li.comment h4 img {
    display: none;
    margin-right: 10px;
    vertical-align: -6px;
}
li.comment .meta {
    float: left;
    overflow: hidden;
}
li.comment .meta .info {
    margin: 10px 0 8px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    color: #696969;
    font-weight: 400;
    line-height: normal;
    text-transform: uppercase;
}
li.comment .meta span {
    color: #757575;
}
li.comment .body p {
    margin: 35px 0;
    clear: both;
    color: #c5cbd2;
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 30px;
}
li.comment .author {
    float: left;
}
li.comment .buttons {
    float: right;
    margin-top: 17px;
}
li.comment .buttons a,
li.comment .buttons span {
    display: block;
    float: right;
    margin-right: 10px;
    border-radius: 3px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 10px;
    color: #e1e1e1;
    font-weight: 400;
    line-height: normal;
    background: #484f5c;
    padding: 8px 18px;
    text-transform: uppercase;
}
li.comment .buttons .isauthor {
    display: none;
}
li.comment.bypostauthor > .contents .buttons .isauthor {
    display: block;
}
li.comment .buttons a {
    background: #151617;
    color: #6a7376;
    -webkit-transition: background 0.1s ease-in, color 0.1s ease-in;
    -moz-transition: background 0.1s ease-in, color 0.1s ease-in;
    transition: background 0.1s ease-in, color 0.1s ease-in;
}
li.comment .buttons a:hover {
    color: #e1e1e1;
    background: #484f5c;
}
li.comment .buttons > *:first-child {
    margin-right: 0;
}
@media (max-width: 800px) {
    li.comment .profile {
        display: none;
    }
    li.comment .main {
        width: 100%;
    }
    li.comment h4 img {
        display: inline-block;
    }
}
@media (max-width: 640px) {
    li.comment hr {
        margin: 25px 0;
    }
}
@media (max-width: 485px) {
    li.comment .meta .info {
        display: none;
    }
    li.comment .meta h4 {
        margin-top: 20px;
    }
    li.comment .buttons span {
        display: none !important;
    }
}
.comment-respond {
    overflow: hidden;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-weight: 400;
    font-size: 12px;
    text-transform: uppercase;
    overflow: hidden;
}
.comment-respond #author,
.comment-respond #email,
.comment-respond #url {
    display: inline-block;
    width: 30%;
    margin-left: 4.25%;
    margin-bottom: 30px;
    background: #151617;
    color: #e7e8ea;
    border: 0;
    border-radius: 3px;
    padding: 14px 18px;
}
.comment-respond textarea {
    background: #151617;
    color: #e7e8ea;
    border: 0;
    border-radius: 3px;
    width: 100%;
    padding: 14px 18px;
    font-family: "Oxygen", "Arial", Sans-Serif;
    font-size: 14px;
}
.comment-respond #author,
.comment-respond #email,
.comment-respond #url,
.comment-respond textarea {
    outline: none;
}
.comment-respond .logged-in-as {
    color: #fff;
    font-size: 15px;
    margin-bottom: 30px;
}
.comment-respond .logged-in-as a {
    color: #fff;
}
.comment-respond ::-webkit-input-placeholder {
    color: #e7e8ea;
}
.comment-respond :-moz-placeholder {
    color: #e7e8ea;
}
.comment-respond ::-moz-placeholder {
    color: #e7e8ea;
}
.comment-respond :-ms-input-placeholder {
    color: #e7e8ea;
}
.comment-respond #author {
    margin-left: 0;
}
.comment-respond .logged-in-as {
    font-size: 15px;
}
.comment-respond #cancel-comment-reply-link,
.comment-respond .submit {
    float: right;
    padding: 12px 32px;
    font-size: 12px;
    border: 0;
    border-radius: 90px;
    background: #16191b;
    color: #bfc6ce;
    margin-top: 20px;
    text-transform: uppercase;
    -webkit-transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
    -moz-transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
    transition: color 0.3s ease-out, background 0.3s ease-out, opacity 0.3s ease-out, border-color 0.3s ease-out;
}
.comment-respond #cancel-comment-reply-link:hover,
.comment-respond .submit:hover {
    background: #e7e8e8;
    color: #616971;
}
.comment-respond #cancel-comment-reply-link {
    float: none;
    font-size: 10px;
    padding: 10px 24px;
}
@media (max-width: 540px) {
    .comment-respond #author,
    .comment-respond #email,
    .comment-respond #url {
        width: 100%;
        margin-left: 0;
        margin-bottom: 20px;
    }
}
li.comment.depth-2 .profile img,
li.comment.depth-3 .profile img,
li.comment.depth-4 .profile img,
li.comment.depth-5 .profile img,
li.comment.depth-6 .profile img,
li.comment.depth-7 .profile img,
li.comment.depth-8 .profile img,
li.comment.depth-9 .profile img,
li.comment.depth-10 .profile img {
    display: none;
}
li.comment.depth-2 h4 img,
li.comment.depth-3 h4 img,
li.comment.depth-4 h4 img,
li.comment.depth-5 h4 img,
li.comment.depth-6 h4 img,
li.comment.depth-7 h4 img,
li.comment.depth-8 h4 img,
li.comment.depth-9 h4 img,
li.comment.depth-10 h4 img {
    display: inline-block;
}
li.comment.depth-2 hr,
li.comment.depth-3 hr,
li.comment.depth-4 hr,
li.comment.depth-5 hr,
li.comment.depth-6 hr,
li.comment.depth-7 hr,
li.comment.depth-8 hr,
li.comment.depth-9 hr,
li.comment.depth-10 hr {
    margin: 25px 0;
}
li.comment.depth-2 .main,
li.comment.depth-3 .main,
li.comment.depth-4 .main,
li.comment.depth-5 .main,
li.comment.depth-6 .main,
li.comment.depth-7 .main,
li.comment.depth-8 .main,
li.comment.depth-9 .main,
li.comment.depth-10 .main {
    width: 95%;
}
body.single .pagewrapper {
    padding-bottom: 75px;
}
@media (max-width: 750px) {
    body.single .pagewrapper {
        padding-bottom: 0px;
    }
}
.disquscomments {
    margin-bottom: 60px;
}
.disquscomments:before {
    content: "";
    display: block;
    height: 1px;
    width: 70%;
    margin: 0 auto 90px;
    background: #dfdfdf;
}
pre {
    word-wrap: initial;
    padding: 20px 30px;
    line-height: 19px;
    margin: 30px 0;
}
code,
kbd,
tt {
    border: 1px solid #eaeaea;
    margin: 0px 2px;
    padding: 0px 5px;
    font-size: 16px;
    line-height: 32px;
}
pre code {
    border: 0px;
    padding: 0px;
    margin: 0px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
pre,
code,
kbd,
tt {
    font-family: Consolas, "Liberation Mono", Courier, monospace;
    color: #444;
    background: #f8f8f8;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
pre,
pre code {
    font-size: 16px;
}
pre .comment {
    color: #998;
}
pre .support {
    color: #0086b3;
}
pre .tag,
pre .tag-name {
    color: navy;
}
pre .keyword,
pre .css-property,
pre .vendor-prefix,
pre .sass,
pre .class,
pre .id,
pre .css-value,
pre .entity.function,
pre .storage.function {
    font-weight: bold;
}
pre .css-property,
pre .css-value,
pre .vendor-prefix,
pre .support.namespace {
    color: #333;
}
pre .constant.numeric,
pre .keyword.unit,
pre .hex-color {
    font-weight: normal;
    color: #099;
}
pre .entity.class {
    color: #458;
}
pre .entity.id,
pre .entity.function {
    color: #900;
}
pre .attribute,
pre .variable {
    color: teal;
}
pre .string,
pre .support.value {
    font-weight: normal;
    color: #d14;
}
pre .regexp {
    color: #009926;
}
.postlist article.format-standard .content {
    display: block;
    width: 100%;
    border-radius: 3px;
    margin-bottom: 50px;
    height: 500px;
    background-position: center;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -ms-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
@media (max-width: 650px) {
    .postlist article.format-standard .content {
        height: 250px;
    }
}
@media (max-width: 550px) {
    .postlist article.format-standard .content {
        height: 200px;
    }
}
@media (max-width: 370px) {
    .postlist article.format-standard .content {
        display: none;
    }
}
.postlist article.format-status .content > * {
    display: none;
}
.postlist article.format-status .content > p:first-child {
    display: block;
    margin: 0 0 40px;
    font-family: "Oxygen", "Varela Round", Serif;
    font-size: 19px;
    font-weight: 400;
    line-height: 35px;
    color: #62646c;
    color: rgba(35, 37, 40, 0.7);
    overflow: hidden;
}
@media (max-width: 640px) {
    .postlist article.format-status .content > p:first-child {
        font-size: 18px;
        line-height: 32px;
    }
}
@media (max-width: 450px) {
    .postlist article.format-status .content > p:first-child {
        font-size: 16px;
        line-height: 30px;
    }
}
.post-status .excerpt {
    margin-top: 0;
}
.postlist article.format-quote .content > * {
    display: none;
}
.postlist article.format-quote .content > blockquote:first-child {
    display: block;
    padding: 0;
    margin: 0;
    color: #3c3e41;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 26px;
    font-weight: 400;
    line-height: 42px;
    letter-spacing: -0.5px;
    text-align: center;
    border: 0;
}
.postlist article.format-quote .content > blockquote:first-child p {
    margin: 0;
}
.postlist article.format-quote .content > blockquote:first-child cite {
    display: block;
    margin-top: 25px;
    color: #939393;
    font-size: 20px;
    font-style: normal;
    font-family: "Varela Round", "Arial", Sans-Serif;
}
.postlist article.format-quote .content > blockquote:first-child cite a {
    color: #939393;
}
@media (max-width: 640px) {
    .postlist article.format-quote .content > blockquote:first-child {
        font-size: 20px;
        line-height: 34px;
    }
    .postlist article.format-quote .content > blockquote:first-child cite {
        font-size: 18px;
    }
}
@media (max-width: 450px) {
    .postlist article.format-quote .content > blockquote:first-child {
        font-size: 18px;
        line-height: 32px;
    }
    .postlist article.format-quote .content > blockquote:first-child cite {
        font-size: 16px;
    }
}
.postlist article.format-video .content > *,
.postlist article.format-audio .content > * {
    display: none;
}
.postlist article.format-video .content > *:first-child,
.postlist article.format-audio .content > *:first-child {
    margin-top: 0;
    display: block;
    margin-bottom: 60px;
}
.post-video .content {
    margin-bottom: 50px;
}
.postlist article.format-image .content > * {
    display: none;
}
.postlist article.format-image .content > a:first-child {
    margin-top: 0;
    display: block;
    margin-bottom: 60px;
}
.post-image-header .content .imageheader {
    display: block;
    height: 300px;
    width: 100%;
    margin-bottom: 50px;
    border-radius: 3px;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -ms-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
.postlist article.format-link .linkicon {
    color: #d5d5d5;
    margin-right: 15px;
    font-size: 40px;
}
.postlist article.sticky .meta .issticky {
    display: block;
}
.authortitle {
    display: table-cell;
    vertical-align: middle;
    width: 700px;
    position: relative;
    z-index: 1000;
    margin: 0;
    background: transparent;
    border-top: 0;
}
.authortitle .info {
    overflow: hidden;
    margin-top: 10%;
}
.authortitle .info img {
    width: 80px;
    height: 80px;
}
.authortitle .profile {
    display: inline-block;
    border-radius: 90px;
    margin-right: 5%;
}
.authortitle .meta {
    display: inline-block;
    vertical-align: 20px;
}
.authortitle .meta .twittertag {
    color: #6e6e6e;
}
.authortitle h3 {
    color: #fff;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-weight: 600;
    font-size: 29px;
}
.authortitle p {
    font-size: 20px;
    color: #939393;
    margin: 20px 0;
}
.authortitle .authorsocial li a {
    line-height: 36px;
}
@media (max-width: 880px) {
    .authortitle {
        width: 70%;
    }
    .authortitle .authorsocial {
        display: none;
    }
}
@media (max-width: 560px) {
    .authortitle {
        width: 80%;
    }
    .authortitle .profile {
        display: none;
    }
}
@media (max-height: 700px) {
    .authortitle p {
        display: none;
    }
}
.altpageheader {
    background: #f9f9f9;
    padding: 40px 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 18px;
    font-weight: 400;
    line-height: normal;
    color: #373737;
    text-align: center;
    text-transform: capitalize;
}
.altpageheader i {
    font-size: 18px;
    color: #b5bcbf;
    margin-right: 10px;
}
.altpageheader .postcount {
    display: inline-block;
    padding: 6px 16px;
    background: #4a5153;
    border-radius: 100px;
    font-family: "Montserrat", "Arial", Sans-Serif;
    font-size: 12px;
    color: #ffffff;
    margin-left: 15px;
    vertical-align: 3px;
}
.alert,
.shorttoggle {
    width: 100%;
    background: #f5f5f5;
    padding: 20px;
    color: #1c1c1c;
    margin: 20px 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.alert.blue,
.shorttoggle.blue {
    color: #fff;
    background: #1793d1;
}
.alert.green,
.shorttoggle.green {
    color: #fff;
    background: #7fbb00;
}
.alert.orange,
.shorttoggle.orange {
    color: #fff;
    background: #dc5034;
}
.alert.red,
.shorttoggle.red {
    color: #fff;
    background: #ce1126;
}

.shortbutton {
    display: inline-block;
    font: 400 13px "Montserrat", Arial, Sans-Serif;
    padding: 6px 20px;
    background: #f5f5f5;
    color: #1c1c1c;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    -webkit-transition: background 0.2s ease-out, border 0.2s ease-out, color 0.2s ease-out;
    -moz-transition: background 0.2s ease-out, border 0.2s ease-out, color 0.2s ease-out;
    transition: background 0.2s ease-out, border 0.2s ease-out, color 0.2s ease-out;
}
.shortbutton.small {
    padding: 4px 15px;
    font-weight: 400;
    font-size: 12px;
    border-bottom: 0;
}
.shortbutton.normal {
    font-weight: 400;
    border-bottom: 0;
}
.shortbutton.large {
    padding: 8px 30px;
    font-weight: 400;
    font-size: 16px;
    border-bottom: 0;
}
.shortbutton.blue {
    color: #fff;
    background: #1793d1;
}
.shortbutton.blue:hover {
    background: #1273a3;
    border-color: #1273a3;
}
.shortbutton.green {
    color: #fff;
    background: #7fbb00;
}
.shortbutton.green:hover {
    background: #5c8800;
    border-color: #5c8800;
}
.shortbutton.orange {
    color: #fff;
    background: #dc5034;
}
.shortbutton.orange:hover {
    background: #bd3b21;
    border-color: #bd3b21;
}
.shortbutton.red {
    color: #fff;
    background: #ce1126;
}
.shortbutton.red:hover {
    background: #9f0d1d;
    border-color: #9f0d1d;
}
.shortbutton:hover {
    border-bottom: 0;
}
.shorttoggle .toggleheader {
    overflow: hidden;
    font: 400 18px "Montserrat", Arial, Sans-Serif;
    cursor: pointer;
}
.shorttoggle .left {
    float: left;
}
.shorttoggle .right {
    float: right;
}
.shorttoggle .togglecontent {
    margin-top: 20px;
    border-top: 1px solid #c9c9c9;
    padding-top: 20px;
}
.shorttoggle .togglecontent > *:first-child {
    margin-top: 0;
}
.shorttoggle .togglecontent > *:last-child {
    margin-bottom: 0;
}
.shorttoggle.outline {
    background: #fff;
    border: 1px solid #dcdcdc;
}
.shorttoggle.closed .togglecontent {
    display: none;
}
.shorttoggle.open .togglecontent {
    display: block;
}
.shorttoggle p {
    margin: 20px 0;
}
.shorttoggle h1,
.shorttoggle h2,
.shorttoggle h3,
.shorttoggle h4,
.shorttoggle h5 {
    margin: 20px 0;
}
.postcontents .grid {
    margin: 30px 0;
    overflow: hidden;
}
.postcontents .grid .half {
    width: 47.5%;
}
.postcontents .grid .left {
    float: left;
    margin: 0;
    margin-right: 2.5%;
}
.postcontents .grid .right {
    float: right;
    margin: 0;
    margin-left: 2.5%;
}
.postcontents .grid p.left,
.postcontents .grid p.right {
    font-weight: 400;
}
.postcontents .grid .left *:last-child,
.postcontents .grid .right *:last-child {
    margin-bottom: 0;
}
.postcontents .grid .left *:first-child,
.postcontents .grid .right *:first-child {
    margin-top: 0;
}
.postcontents .grid p {
    clear: none;
    font-weight: 300;
}
@media (max-width: 585px) {
    .postcontents .grid .left,
    .postcontents .grid .right {
        margin: 0;
        float: none;
        width: 100%;
    }
    .postcontents .grid .right {
        margin-top: 50px;
    }
}
.shorttabs {
    margin: 30px 0;
    overflow: hidden;
}
.shorttabs .shorttabsheader {
    border: 1px solid #dedede;
    background: #f1f1f1;
    border-bottom: 0;
    display: inline-block;
    padding: 10px 20px;
    cursor: pointer;
    color: #1c1c1c;
    font: 400 16px Montserrat, Arial, Sans-Serif;
    -webkit-border-top-left-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    -moz-border-radius-topleft: 3px;
    -moz-border-radius-topright: 3px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    margin-top: 3px;
}
.shorttabs .shorttabsheader:hover {
    background: #fff;
}
.shorttabs .shorttabsheader.active {
    background: #fff;
    border: 1px solid #eee;
    border-bottom: 0;
    padding: 10px 20px;
    color: #282828;
}
.shorttabs .shorttabscontent {
    border: 1px solid #eee;
    padding: 2.5%;
    -webkit-border-bottom-right-radius: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -moz-border-radius-bottomright: 3px;
    -moz-border-radius-bottomleft: 3px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    margin-top: -1px;
    display: block;
}
.shorttabs .shorttabscontent > *:first-child {
    margin-top: 0;
    padding-top: 0;
}
.shorttabs .shorttabscontent > *:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}
.shortprogress {
    width: 100%;
    background: #eee;
    border-radius: 3px;
    padding: 6px;
    position: relative;
    margin: 30px 0;
}
.shortprogress .inner {
    background: #217dcb;
    border-radius: 3px;
    width: 80%;
    height: 42px;
    padding: 0;
}
.shortprogress .inner.green {
    background: #7fbb00;
}
.shortprogress .inner.orange {
    background: #dc5034;
}
.shortprogress .inner.red {
    background: #ce1126;
}
.shortprogress .percentage {
    position: absolute;
    font-family: "Montserrat", Arial, Sans-Serif;
    color: #393434;
    top: 14px;
    right: 21px;
    font-size: 17px;
}


.posttitle.center {
    display: table-cell;
    vertical-align: middle;
    width: 900px;
}

.cover .posttitle {
    position: relative;
}

.postitem {
    padding: 30px 0;
}

.posttitle h1,
.posttitle h2 {
    margin: 35px 0;
    font-size: 3.6em;
}

.postitem h1.landingpage {
    font-size: 4.6em !important;
    color: #ffffff;
    margin: 0;
    font-weight: 600;
    text-align: center;
}

.postitem p.landingpage {
    color: #ffffff;
    margin: 0;
    font-weight: 100;
    font-size: 1.4em;
    text-align: center;
    margin: 15px;
}

.postitem h1,
.postitem h2 {
    margin: 45px 0;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 48px;
    font-weight: 600;
    line-height: 62px;
    letter-spacing: -1px;
}

p {
    font-size: 1.4em;
}

img {
    max-width: 100%;
    height: auto;
    border: 0;
    border-radius: 3px;
    outline: none !important
}

body {
    height: 100%;
    overflow-x: hidden;
    background-color: #fff;
    font: 300 17px/28px "Oxygen", "Arial", Sans-Serif;
    margin: 0;
}

header.bloginfo {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #fff;
    padding: 22px 25px 15px;
    height: 75px;
    border-bottom: 2px solid #f2f2f2;
    z-index: 1500;
    -webkit-transition: -webkit-transform 500ms ease-in-out, left 500ms ease-in-out;
    -moz-transition: -moz-transform 500ms ease-in-out, left 500ms ease-in-out;
    transition: transform 500ms ease-in-out, left 500ms ease-in-out;
}

header.bloginfo .title {
    float: left;
    font-family: "Varela Round", "Arial", Sans-Serif;
    font-size: 18px;
    font-weight: 600;
    color: #373737
}

header.bloginfo .title a {
    color: #373737
}

header.bloginfo .title a img {
    top: 15px;
    max-height: 38px;
    position: fixed;
}

header.bloginfo nav {
    float: right
}

header.bloginfo.slide {
    left: -400px
}

header.bloginfo.transparent {
    background: transparent;
    border: 0;
    padding: 15px;
    position: absolute
}

header.bloginfo.transparent .title a {
    color: #fff
}

header.bloginfo.transparent.slide {
    left: 0px
}

.drawer {
    position: fixed;
    z-index: 3000;
    width: 400px;
    height: 100%;
    top: 0;
    right: 0;
    background: #ffffff;
    overflow-y: auto;
    border-left: 1px solid #eaeaea;
    padding: 0 35px;
    -webkit-overflow-scrolling: touch;
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
    -ms-transform: translateX(100%);
    -o-transform: translateX(100%);
    transform: translateX(100%);
    -webkit-transition: -webkit-transform 500ms ease-in-out;
    -moz-transition: -moz-transform 500ms ease-in-out;
    transition: transform 500ms ease-in-out
}

.drawer.show {
    -webkit-transform: translateX(0%);
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -o-transform: translateX(0%);
    transform: translateX(0%);
}

.cover {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #080808;
    display: table;
    overflow: hidden
}

.cover .background {
    position: absolute;
    overflow: hidden;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    background-position: center;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -ms-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

nav.main ul {
    padding: 5px 0 0 0;
    margin: 0;
    list-style: none;
    float: left;
}

nav.main ul li {
    display: block;
    float: left;
    position: relative;
}

#greenbutton {
    background-color: #4caf50;
    color: white;
    border-radius: 4px;
    display: block;
    margin: -4px auto auto auto;
    width: 210px;
    height: 38px;
}

header.bloginfo nav {
    float: right;
}
