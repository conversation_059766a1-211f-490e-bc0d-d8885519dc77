@import 'scss/layout2021/variables';

.inline-dropdown {
  position: relative;
  width: 70px;

  .icon {
    display: inline-block;
    cursor: pointer;
    position: absolute;
    color: $single-card-icon-color;
    top: 3px;
    right: 5px;
    padding: 2px 11px;
    border-radius: 50%;
    transition: .3s ease all;
    width: 30px;
    border: $single-card-icon-border-hover;
    border-color: $single-card-background-color;

    &:hover {
      border-color: $single-card-icon-color;
    }

    &:focus,
    &:active {
      color: $single-card-icon-color-focus;
      background: $single-card-icon-background;
    }
  }

  .dropdown.abs {
    top: calc(50% - 15px);
    outline: unset !important;
    right: 15px;
    position: absolute;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled):focus-within {
      outline: none !important;
    }

    .dropdown-toggle::after {
      display: none;
    }

    .dropdown-menu {
      min-width: 200px;
      border: $single-card-border;

      a {
        padding: 8px 13px;
        color: #273239;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          text-decoration: none;
        }

        i {
          color: $brand-gray;
          margin-right: 20px;
        }
      }

      li {
        transition: .3s background-color;

        &.dropdown-menu-separator {
          border-top: 1px solid $brand-gray;
        }
      }
    }
  }
}
