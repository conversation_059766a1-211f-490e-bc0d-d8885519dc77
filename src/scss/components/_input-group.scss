@import 'scss/layout2021/variables';

.input-group {
  width: 300px;

  .form-control {
    border: 1px solid #CBDCE2;
    margin-right: 1px;
    z-index: 9;
  }

    .btn {
      border: 1px solid #CBDCE2;
      background-color: #fff;
      // height: 38px;
      margin-left: -5px;
      border-radius: 0px 6px 6px 0px;

      .icon {
        font-size: 15px;
        color: #698A95;
      }
    }

  .input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap;
  }

  .icon {
    color: #698A95 !important;
    width: 11px;
  }

  .input-group-addon,
  .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
  }

  .input-group-addon,
  .input-group-btn,
  .input-group .form-control {
    display: table-cell;
  }

  .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group {
    margin-right: -1px;
  }

  .input-group .form-control:first-child,
  .input-group-addon:first-child,
  .input-group-btn:first-child > .btn,
  .input-group-btn:first-child > .btn-group > .btn,
  .input-group-btn:first-child > .dropdown-toggle,
  .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
  .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .input-group .btn {
    height: 44px;
    border-radius: 6px 0px 0px 6px;
    border: 1px solid #666766;
    border-right-color: transparent;
    background: rgba(255, 255, 255, .5);
  }
}
