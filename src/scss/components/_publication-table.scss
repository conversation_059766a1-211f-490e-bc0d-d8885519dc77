@import 'scss/layout2021/variables';
@import 'scss/figma2023/variables';

.publication-table {
  min-width: 650px;

  thead {
    th {
      font-family: $font-open-sans-bold, sans-serif;
      padding-top: 13px;
      padding-bottom: 14px;
    }
  }

  tbody {
    td {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  .annotation-status span {
    color: $brand-dark;
  }

  .obfuscated {
    td {
      filter: blur(3px);
      opacity: .6;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      a {
        pointer-events: none;
      }
    }
  }

  .number-col {
    color: #0F2C35;
  }

  .publication-number-col {
    a {
      color: #389A85;
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .ip4-col {
    color: #738993
  }

  .preview-border, .preview-border-vertical {
    border: none !important;

    tr {
      > * {
        --bs-table-accent-bg: white !important;
      }
    }

    &.has-detail-bg {
      tr {
        > * {
          --bs-table-accent-bg: #E8F0F3 !important;
        }
      }

      .detailed {
        > td {
          background: #E8F0F3 !important;
          &:hover {
            background: #E8F0F3 !important;
          }
        }
      }
    }
  }

  .preview-border {
    .detailed {
      > td:first-child {
        box-shadow: 3px 3px 0 0 #00a08380 inset;
      }
      > td:last-child {
        box-shadow: -3px 3px 0 0 #00a08380 inset;
      }
      > td {
        box-shadow: 0 3px 0 #00a08380 inset;
      }
    }
    .detailed-sides{
      > td {
        &:first-child {
          box-shadow: 3px 0 0 0 #00a08380 inset;
        }
        &:last-child {
          box-shadow: -3px 0 0 0 #00a08380 inset;
        }
      }
    }
  }

  .preview-border-vertical {
    .detailed, .detailed-sides {
      > td:first-child {
        box-shadow: 3px 0 0 0 #00a08380 inset;
      }
      > td:last-child {
        box-shadow: -3px 0 0 0 #00a08380 inset;
      }
    }
  }

  .patent-detail-container {
    border-bottom: 1px solid $table-row-border-color;

    .patent-detail {
      .patent-detail-content {
        max-height: 350px;
        overflow-y: auto;
      }
    }

    &.has-detail-bg {
      .patent-detail {
        background: #E8F0F3 !important;
        &:hover {
          background: #E8F0F3 !important;
        }
      }
    }

    .row-border-top {
      border-top: 1px solid #CBDCE2;
    }

    .row-border-bottom {
      border-bottom: 1px solid #CBDCE2;
    }

    .patent-detail-title {
      color: #0F2C34;
      font-size: 18px;
      font-family: $font-open-sans-bold;
      margin: 0 auto;
    }
  }

  .table-actions {
    text-align: center;
    vertical-align: top;
  }
  .patent-table-wrapper{
    border: 1px solid $colours-border-subtle;
    border-bottom: none;
    border-top-left-radius: $radius-big;
    border-top-right-radius: $radius-big;
  }
  .table-scroller{
    background-color: #FAFAFA;
    border: 1px solid $colours-border-subtle;
    position: sticky;
    inset-block-end: 0;
    border-bottom-left-radius: $radius-big;
    border-bottom-right-radius: $radius-big;
    z-index: 1010;
  }
  .table-scrollbar{
    height: 2px;
    @-moz-document url-prefix() {
      height: 10px !important;
    }
    &-container{
      overflow: auto;
    }
  }
}

::ng-deep {
  .publication-table {
    tbody {
      .patent-detail {
        padding-top: 30px;
        overflow-x: hidden;
        box-shadow: -3px 0px 0 0 #00a08380 inset, 3px -3px 0 0 #00a08380 inset;
      }
    }
  }
}
