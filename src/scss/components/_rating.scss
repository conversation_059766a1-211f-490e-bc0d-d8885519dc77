@import 'scss/figma2023/index';

$colors: (
  "1": $colour-red-400,
  "2": $colour-global-dennemeyer-orange,
  "3": $colour-yellow-500,
  "4": $colour-green-400,
  "5": $colour-green-500
);

$sizes: (
  "lg" 1.5rem 0.85rem,
  "md" 1.25rem 0.75rem,
  "sm" 1rem 0.63rem
);

::ng-deep {
  .rating-star {
    .fa-stack-star {
      border-radius: 50%;
      background: $colours-content-content-quartary;
      color: $colours-background-bg-disable;

      @each $name, $size, $star-size in $sizes {
        &.fa-stack-#{$name} {
          font-size: $size;
          width: $size;
          height: $size;

          .fa-circle {
            font-size: $size;
            line-height: $size;
            width: $size;
            height: $size;
          }

          .fa-star-circle {
            font-size: $star-size;
            line-height: $size;
            width: $size;
            height: $size;
          }
        }
      }

      @each $star, $color in $colors {
        &.rated-star-#{$star} {
          background: $colours-content-content-reversed-grey;
          color: $color;
        }
      }
    }
  }
}
