@import 'scss/layout2021/variables';

.right-side-container {
  background-color: #FFF;
  box-shadow: 0 10px 30px #00000029;

  .right-side-title {
    color: $color-text-04;
    font-size: 24px;
    font-family: $font-open-sans-bold;
  }

  .button-back-container {
    height: 58px;
    background-color: white;
  }

  .header-options {
    border-bottom: solid 1px #c1c1c1;
    padding-bottom: 13px;

    .label-options {
      font-size: 1rem;
      color: #485D66;
    }
  }

  ::ng-deep {
    .loading-spinner {
      width: 100px;
    }
  }

  &.sticky-top {
    height: calc(100vh - 86px);
  }

  &.was-sticky {
    &.sticky-top {
      height: 100vh !important;
    }
  }
}
