@import 'scss/figma2023/variables';

$tag_item_border: 0.0625rem;

.tag-item {
  height: $spacing-system-spacing-xx-big;
  border-radius: $radius-sm;
  border: $tag_item_border solid transparent;
  padding: ($spacing-system-spacing-xxx-s - $tag_item_border) $spacing-system-spacing-x-s;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .tag-name {
    font-weight: 500 !important;
  }

  &.tag-green-patent {
    color: $colours-content-content-success;
    background-color: $colours-background-bg-success;

    &:hover, &:active {
      background-color: $colour-transparency-65green !important;
    }
  }

  &.tag-small {
    height: $spacing-system-spacing-xx-big !important;
    padding: ($spacing-system-spacing-xxx-s - $tag_item_border) $spacing-system-spacing-xx-s !important;
  }

  &.tag-medium {
    height: $spacing-system-spacing-xx-big !important;
    padding: ($spacing-system-spacing-xxx-s - $tag_item_border) $spacing-system-spacing-x-s !important;
  }

  &.tag-custom {
    &:hover, &:active {
      background-color: var(--hover-bg) !important;
    }

    .tag-name {
      padding: 0 $spacing-system-spacing-xx-s;
      text-align: left;
      text-wrap: nowrap;
      max-width: 10rem;
      overflow: hidden;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
    }

    .tag-unassign {
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-content: center;
      justify-content: center;
      align-items: center;
    }
  }

  &.tag-patent-standard {
    background-color: #FF7700;
    border: 1px solid #DC6700;
    color: white;
  }

  &.tag-special-patent {
    background-color: #4325E1;
    border: 1px solid #281392;
    font-size: 14px;
  }

  &.tag-item-action {
    display: flex;
    padding: $spacing-system-spacing-xxx-s;
    justify-content: center;
    align-items: center;
    border-radius: $spacing-system-spacing-xx-s;
    border: 1px solid $colour-grey-300;
    background: $colour-grey-100;

    &:hover, &:active {
      background: $colour-grey-200;
    }

    :first-child {
      display: flex;
      padding: 0 $spacing-system-spacing-xx-s;
      justify-content: center;
      align-items: center;
      gap: $spacing-system-spacing-sm;
      color: $colour-grey-800;
      text-align: center;
    }

    .tag-item-action-icon {
      font-family: "Font Awesome 6 Pro";
      font-size: 1rem;
      font-style: normal;
      line-height: normal;
    }

    .tag-item-action-text {
      padding-left: $spacing-system-spacing-xx-s;
      padding-right: $spacing-system-spacing-xx-s;
      font-weight: 500 !important;
    }
  }

  &.tag-item-selected {
    border: 0.0625rem solid $colour-grey-700;
  }

  img {
    width: 1.25rem;
    height: 1.25rem;
  }
}
