@import 'scss/figma2023/index';

$statuses: (
  new: (
    color: $colours-content-content-active,
    border-color: $colours-border-brand,
    background: $colours-background-bg-brand
  ),
  open: (
    color: $colours-content-content-warning,
    border-color: $colours-border-warning,
    background: $colours-background-bg-warning
  ),
  overdue: (
    color: $colours-content-content-danger,
    border-color: $colours-border-danger,
    background: $colours-background-bg-danger
  ),
  closed: (
    color: $colours-content-content-secondary,
    border-color: $colours-border-subtle,
    background: $colours-background-bg-secondary
  ),
  done: (
    color: $colours-content-content-success,
    border-color: $colours-border-success,
    background: $colours-background-bg-success
  )
);

.patent-task-status {
  padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-x-s;
  border: 1px solid;
  text-wrap: nowrap;
  word-break: keep-all;
  font-weight: 400 !important;
  @include border-radius($spacing-system-spacing-xx-s);

  @each $status, $map in $statuses {
    &.patent-task-status-#{$status} {
      color: map-get($map, color);
      border-color: map-get($map, border-color);
      background: map-get($map, background);
    }
  }
}
