@import 'scss/figma2023/index';
@import 'scss/layout2021/variables';

.user-item {
  height: 2rem;
  border: 0.0625rem solid transparent;
  padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-x-s;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;

  &.user-item-rounded {
    padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
    border-color: $colours-border-subtle;
    background: $colours-background-bg-primary;
    @include border-radius($radius-sm);
  }

  &.user-custom {
    &:hover, &:active {
      opacity: 0.75;
    }

    .user-unassign {
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-content: center;
      justify-content: center;
      align-items: center;
    }
  }

  &.user-item-action {
    display: flex;
    padding: $spacing-system-spacing-xxx-s;
    justify-content: center;
    align-items: center;
    border-radius: $spacing-system-spacing-xx-s;
    border: 1px solid $colours-border-subtle;
    background: $colours-background-bg-primary;
    @include add-properties(map-deep-get($typography, label, xsmall), true);

    &:hover, &:active {
      background: $colour-grey-200;
    }

    :first-child {
      display: flex;
      padding: 0 $spacing-system-spacing-xx-s;
      justify-content: center;
      align-items: center;
      gap: $spacing-system-spacing-sm;
      color: $colour-grey-800;
      text-align: center;
    }

    .user-item-action-icon {
      font-family: "Font Awesome 6 Pro";
      font-size: 1rem;
      font-style: normal;
      font-weight: 300;
      line-height: normal;
    }

    .user-item-action-text {
      padding-left: $spacing-system-spacing-xx-s;
      padding-right: $spacing-system-spacing-xx-s;
      font-weight: 500 !important;
    }
  }

  &.user-item-selected {
    border: 0.0625rem solid $colour-grey-700;
  }

  img {
    width: 1.25rem;
    height: 1.25rem;
  }
}
