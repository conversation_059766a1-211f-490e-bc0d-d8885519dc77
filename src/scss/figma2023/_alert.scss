$message-types: ( 
    info: ( $colours-background-bg-info, $colours-content-content-info), 
    warning: ($colours-background-bg-warning, $colours-content-content-warning), 
    brand: ($colours-background-bg-brand, $colours-content-content-active), 
    danger: ($colours-background-bg-danger, $colours-content-content-danger));

@each $type, $colors in $message-types {
    .alert-#{$type}-2024 {
        padding: $spacing-system-spacing-sm;
        gap: $spacing-system-spacing-sm;
        border-radius: $radius-sm;
        background: nth($colors, 1);
        .icon {
            color: nth($colors, 2);
            width: $spacing-system-spacing-x-big;
            min-width: $spacing-system-spacing-x-big;
            height: $spacing-system-spacing-x-big;
            text-align: center;
            i {
                font-size: $font-size-md;
                line-height: $font-size-md;
            }
        }
        .close {
            padding: $spacing-system-spacing-xxx-s;
            color: $colours-content-content-primary;
            cursor: pointer;
            i {
                width: $spacing-system-spacing-x-big;
                height: $spacing-system-spacing-x-big;
                text-align: center;
                font-size: $font-size-md;
                line-height: $font-size-md;
            }
        }
    }
}