$tag_item_border: 0.0625rem;

@mixin badge-variant($color, $border-color, $bg-color) {
  color: $color;
  border: $tag_item_border solid $border-color;
  background-color: $bg-color;
  cursor: default;
  --hover-bg: #{$bg-color}A6;
}

.badge- {
  &valid, &granted, &active, &alive, &success, &invalid, &inactive, &danger, &unknown, &grey, &info, &classification, &brand, &pending, &warning, &dead, &greyed {
    border-radius: $radius-sm;
    height: $spacing-system-spacing-xx-big !important;
    padding: ($spacing-system-spacing-xxx-s - $tag_item_border) $spacing-system-spacing-xx-s !important;
    display: inline;
    text-align: center;

    &:not(.no-hover) {
      &:hover, &:active {
        background-color: var(--hover-bg) !important;
      }
    }

    &.badge-legal-status-fixed {
      width: 5.5rem;
    }

    @extend .content-label-small;
  }

  &valid, &success, &active, &granted, &alive {
    @include badge-variant(
      $colours-content-content-success,
      $colours-border-success,
      $colours-background-bg-success
    );
  }

  &invalid, &danger, &dead, &inactive {
    @include badge-variant(
      $colours-content-content-danger,
      $colours-border-danger,
      $colours-background-bg-danger
    );
  }

  &unknown, &grey {
    @include badge-variant(
      $colours-content-content-secondary,
      $colours-border-bold,
      $colours-background-bg-secondary
    );
  }

  &pending, &warning {
    @include badge-variant(
      $colours-content-content-warning,
      $colours-border-warning,
      $colours-background-bg-warning
    );
  }

  &info, &classification {
    @include badge-variant(
      $colours-content-content-active,
      $colour-blue-brand-300,
      #F1FBFE
    );
  }

  &brand {
    @include badge-variant(
      $colours-content-content-active,
      transparent,
      $colours-background-bg-brand
    );
  }
  &greyed {
    color: $colours-content-content-secondary;
    border: none;
    background-color: $colours-background-bg-tertiary;
    cursor: default;
    --hover-bg: #{$colours-background-bg-tertiary}A6;
  }

  &circle {
    padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-xx-s;
    border-radius: 35rem;
  }

  &small {
    padding: 0.0625rem $spacing-system-spacing-xx-s !important;
    height: $spacing-system-spacing-x-big !important;
    line-height: $spacing-system-spacing-big;
    display: inline-block;
    @extend .content-label-xsmall;
  }
}
