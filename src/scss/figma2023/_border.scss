$borders: (
  "soft": $colours-border-soft,
  "subtle": $colours-border-subtle,
  "moderate": $colours-border-moderate,
  "minimal": $colours-border-minimal,
  "bold": $colours-border-bold,
  "contrast": $colours-border-contrast,
  "brand": $colours-border-brand,
  "warning": $colours-border-warning,
  "danger": $colours-border-danger,
  "success": $colours-border-success,
  "info": $colours-border-info,
  "secondary": $colours-border-secondary,
);

$thickness: (
  "0-5": 0.5px,
  "1": 1px,
  "1-5": 1.5px,
  "2": 2px,
  "2-5": 2.5px,
  "3": 3px,
  "3-5": 3.5px,
  "4": 4px
);

.border {
  @each $name, $size in $thickness {
    &-#{$name} {
      border-width: #{$size} !important;
      border-style: solid !important;
    }
    &-l-#{$name} {
      border-left-width: #{$size} !important;
      border-style: solid !important;
    }
    &-t-#{$name} {
      border-top-width: #{$size} !important;
      border-style: solid !important;
    }
    &-r-#{$name} {
      border-right-width: #{$size} !important;
      border-style: solid !important;
    }
    &-b-#{$name} {
      border-bottom-width: #{$size} !important;
      border-style: solid !important;
    }
    &-x-#{$name} {
      border-left-width: #{$size} !important;
      border-right-width: #{$size} !important;
      border-style: solid !important;
    }
    &-y-#{$name} {
      border-top-width: #{$size} !important;
      border-bottom-width: #{$size} !important;
      border-style: solid !important;
    }
  }

  @each $name, $color in $borders {
    &-#{$name} {
      border-color: $color !important;
    }
    &-l-#{$name} {
      border-left-color: $color !important;
    }
    &-t-#{$name} {
      border-top-color: $color !important;
    }
    &-r-#{$name} {
      border-right-color: $color !important;
    }
    &-b-#{$name} {
      border-bottom-color: $color !important;
    }
    &-x-#{$name} {
      border-left-color: $color !important;
      border-right-color: $color !important;
    }
    &-y-#{$name} {
      border-top-color: $color !important;
      border-bottom-color: $color !important;
    }
  }
}
