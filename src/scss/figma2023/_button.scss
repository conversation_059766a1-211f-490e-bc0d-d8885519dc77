@import './variables';

$buttons: (
  button: (
    main-primary: (color: $colour-grey-050, bg: $colour-blue-brand-500, border: 2px solid transparent, pressed-color: $colour-grey-050, pressed-bg: $colour-blue-brand-700, hover-color: $colour-grey-050, hover-bg: $colour-blue-brand-600, focus-border: 2px solid $colour-blue-brand-200, hover-border: inherit),
    main-secondary: (color: $colour-blue-brand-500, bg: $colour-blue-brand-050, border: 1px solid $colour-blue-brand-200, pressed-color: inherit, pressed-bg: $colour-blue-brand-200, hover-color: inherit, hover-bg: $colour-blue-brand-100, focus-border: 2px solid $colour-blue-brand-200, hover-border: inherit),
    main-secondary-grey: (color: $colour-grey-900, bg: $colour-grey-050, border: 1px solid $colour-grey-400, pressed-color: inherit, pressed-bg: $colour-grey-400, hover-color: inherit, hover-bg: $colour-grey-300, focus-border: 2px solid $colour-grey-400, hover-border: inherit),
    main-tertiary: (color: $colour-blue-brand-500, bg: unset, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-blue-brand-200, hover-color: inherit, hover-bg: $colour-blue-brand-100, focus-border: 2px solid $colour-info-200, hover-border: inherit),
    main-tertiary-grey: (color: $colour-grey-900, bg: unset, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-grey-400, hover-color: inherit, hover-bg: $colour-grey-300, focus-border: 2px solid $colour-grey-400, hover-border: inherit),
    main-ghost: (color: $colour-blue-brand-500, bg: unset, border: 2px solid transparent, pressed-color: $colour-blue-brand-700, pressed-bg: none, hover-color: $colour-blue-brand-600, hover-bg: none, focus-border: 2px solid $colour-blue-brand-200, hover-border: 1px solid $colour-blue-brand-600),
    main-link: (color: $colour-blue-brand-500, bg: unset, border: 2px solid transparent, pressed-color: $colour-blue-brand-700, pressed-bg: unset, hover-color: $colour-blue-brand-600, hover-bg: unset, focus-border: 2px solid $colour-blue-brand-200, hover-border: inherit),
    main-link-grey: (color: $colour-grey-900, bg: unset, border: 2px solid transparent, pressed-color: inherit, pressed-bg: unset, hover-color: inherit, hover-bg: unset, focus-border: 2px solid $colour-grey-200, hover-border: inherit),
    destructive-primary: (color: $colour-red-050, bg: $colour-red-500, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-red-700, hover-color: inherit, hover-bg: $colour-red-600, focus-border: 2px solid $colour-red-200, hover-border: inherit),
    destructive-secondary: (color: $colour-red-500, bg: $colour-red-050, border: 1px solid $colour-red-200, pressed-color: inherit, pressed-bg: $colour-red-200, hover-color: inherit, hover-bg: $colour-red-100, focus-border: 2px solid $colour-red-200, hover-border: inherit),
    destructive-tertiary: (color: $colour-red-500, bg: unset, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-red-200, hover-color: inherit, hover-bg: $colour-red-100, focus-border: 2px solid $colour-red-200, hover-border: inherit),
    destructive-ghost: (color: $colour-red-500, bg: unset, border: 2px solid transparent, pressed-color: $colour-red-700, pressed-bg: none, hover-color: $colour-red-600, hover-bg: none, focus-border: 2px solid $colour-red-200, hover-border: inherit),
    destructive-link: (color: $colour-red-500, bg: unset, border: 2px solid transparent, pressed-color: $colour-red-700, pressed-bg: unset, hover-color: $colour-red-600, hover-bg: unset, focus-border: 2px solid $colour-red-200, hover-border: inherit),
    info-primary: (color: $colour-info-050, bg: $colour-info-600, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-info-800, hover-color: inherit, hover-bg: $colour-info-700, focus-border: 2px solid $colour-info-200, hover-border: inherit),
    info-secondary: (color: $colour-info-600, bg: $colour-info-050, border: 1px solid $colour-info-200, pressed-color: inherit, pressed-bg: $colour-info-200, hover-color: inherit, hover-bg: $colour-info-100, focus-border: 2px solid $colour-info-200, hover-border: inherit),
    info-tertiary: (color: $colour-info-600, bg: unset, border: 2px solid transparent, pressed-color: inherit, pressed-bg: $colour-info-200, hover-color: inherit, hover-bg: $colour-info-100, focus-border: 2px solid $colour-info-200, hover-border: inherit),
    info-ghost: (color: $colour-info-600, bg: unset, border: 2px solid transparent, pressed-color: $colour-info-800, pressed-bg: none, hover-color: $colour-info-700, hover-bg: none, focus-border: 2px solid $colour-info-200, hover-border: inherit),
    info-link: (color: $colour-info-600, bg: unset, border: 2px solid transparent, pressed-color: $colour-info-800, pressed-bg: unset, hover-color: $colour-info-700, hover-bg: unset, focus-border: 2px solid $colour-info-200, hover-border: inherit)
  ),
  size: (
    xlarge: (padding: $spacing-system-spacing-big $spacing-system-spacing-xx-big, height: $spacing-system-spacing-xxx-lg),
    large: (padding: $spacing-system-spacing-md $spacing-system-spacing-x-big, height: $spacing-system-spacing-xx-lg),
    medium: (padding: $spacing-system-spacing-sm $spacing-system-spacing-big, height: $spacing-system-spacing-x-lg),
    small: (padding: $spacing-system-spacing-x-s $spacing-system-spacing-md, height: $spacing-system-spacing-xxx-big),
  ),
  color: (
    main-primary: $colour-grey-050,
    main-secondary: $colour-blue-brand-900,
    main-secondary-grey: $colour-grey-900,
    main-tertiary: $colour-blue-brand-600,
    main-tertiary-grey: $colour-grey-900,
    main-ghost: $colour-blue-brand-600,
    destructive-primary: $colour-red-050,
    destructive-secondary: $colour-red-500,
    destructive-tertiary: $colour-red-500,
    destructive-ghost: $colour-red-500,
    info-primary: $colour-info-050,
    info-secondary: $colour-info-600,
    info-tertiary: $colour-info-600,
    info-ghost: $colour-info-600,
  )
);

@each $name, $properties in map-get($buttons, 'button') {
  .button-#{$name} {
    @include add-button(
        map-get($properties, color),
        map-get($properties, bg),
        map-get($properties, border),
        map-get($properties, pressed-color),
        map-get($properties, pressed-bg),
        map-get($properties, hover-color),
        map-get($properties, hover-bg),
        map-get($properties, focus-border),
        map-get($properties, hover-border)
    );
    &.color-#{$name}{
      color: map-get(map-get($buttons, color), $name);
    }
    @if (str-index($name, '-link')) {
      padding: 0;
      display: inline;
      &:hover{
        text-decoration: underline;
      }
    }
  }
}

@each $size, $properties in map-get($buttons, 'size') {
  .button-#{$size} {
    @include add-properties($properties);
  }
}
.button-pill {
  border-radius: $radius-xx-huge;
  &.button-xlarge{
    padding: $spacing-system-spacing-big $spacing-system-spacing-xxx-big;
  }
}
.button-square {
  &.button-xlarge{
    padding: $spacing-system-spacing-big;
    height: $spacing-system-spacing-xxx-lg;
    width: $spacing-system-spacing-xxx-lg;
  }
  &.button-large{
    padding: $spacing-system-spacing-md;
    height: $spacing-system-spacing-xx-lg;
    width: $spacing-system-spacing-xx-lg;
  }
  &.button-medium{
    padding: $spacing-system-spacing-sm;
    height: $spacing-system-spacing-x-lg;
    width: $spacing-system-spacing-x-lg;
  }
  &.button-small{
    padding: $spacing-system-spacing-x-s;
    height: $spacing-system-spacing-xxx-big;
    width: $spacing-system-spacing-xxx-big;
  }
  &.button-xsmall{
    padding: $spacing-system-spacing-xxx-s;
    height: $spacing-system-spacing-xx-big;
    width: $spacing-system-spacing-xx-big;
  }
}
.button-circle {
  border-radius: 62.4375rem;
  &.button-xlarge{
    padding: $spacing-system-spacing-big;
  }
  &.button-large{
    padding: $spacing-system-spacing-md;
  }
  &.button-medium{
    padding: $spacing-system-spacing-sm;
  }
  &.button-small{
    padding: $spacing-system-spacing-x-s;
  }
}
