$colors: (
  "green": (
    "050": $colour-green-050,
    "100": $colour-green-100,
    "200": $colour-green-200,
    "300": $colour-green-300,
    "400": $colour-green-400,
    "500": $colour-green-500,
    "600": $colour-green-600,
    "700": $colour-green-700,
    "800": $colour-green-800,
    "900": $colour-green-900,
  ),
  "red": (
    "050": $colour-red-050,
    "100": $colour-red-100,
    "200": $colour-red-200,
    "300": $colour-red-300,
    "400": $colour-red-400,
    "500": $colour-red-500,
    "600": $colour-red-600,
    "700": $colour-red-700,
    "800": $colour-red-800,
    "900": $colour-red-900,
  ),
  "yellow": (
    "050": $colour-yellow-050,
    "100": $colour-yellow-100,
    "200": $colour-yellow-200,
    "300": $colour-yellow-300,
    "400": $colour-yellow-400,
    "500": $colour-yellow-500,
    "600": $colour-yellow-600,
    "700": $colour-yellow-700,
    "800": $colour-yellow-800,
    "900": $colour-yellow-900,
  ),
  "grey": (
    "050": $colour-grey-050,
    "100": $colour-grey-100,
    "200": $colour-grey-200,
    "300": $colour-grey-300,
    "400": $colour-grey-400,
    "500": $colour-grey-500,
    "600": $colour-grey-600,
    "700": $colour-grey-700,
    "800": $colour-grey-800,
    "900": $colour-grey-900,
  ),
  "info": (
    "050": $colour-info-050,
    "100": $colour-info-100,
    "200": $colour-info-200,
    "300": $colour-info-300,
    "400": $colour-info-400,
    "500": $colour-info-500,
    "600": $colour-info-600,
    "700": $colour-info-700,
    "800": $colour-info-800,
    "900": $colour-info-900,
  ),
  "blue-brand": (
    "050": $colour-blue-brand-050,
    "100": $colour-blue-brand-100,
    "200": $colour-blue-brand-200,
    "300": $colour-blue-brand-300,
    "400": $colour-blue-brand-400,
    "500": $colour-blue-brand-500,
    "600": $colour-blue-brand-600,
    "700": $colour-blue-brand-700,
    "800": $colour-blue-brand-800,
    "900": $colour-blue-brand-900,
  ),
  "orange-brand": (
    "050": $colour-orange-brand-050,
    "100": $colour-orange-brand-100,
    "200": $colour-orange-brand-200,
    "300": $colour-orange-brand-300,
    "400": $colour-orange-brand-400,
    "500": $colour-orange-brand-500,
    "600": $colour-orange-brand-600,
    "700": $colour-orange-brand-700,
    "800": $colour-orange-brand-800,
    "900": $colour-orange-brand-900,
  ),
  "base": (
    "white": $colour-base-white,
    "black": $colour-base-black,
  ),
  "transparency": (
    "8blue-brand": $colour-transparency-8blue-brand,
    "12blue-brand": $colour-transparency-12blue-brand,
    "30blue-brand": $colour-transparency-30blue-brand,
    "50blue-brand": $colour-transparency-50blue-brand,
    "80blue-brand": $colour-transparency-80blue-brand,
    "8grey": $colour-transparency-8grey,
    "12grey": $colour-transparency-12grey,
    "30grey": $colour-transparency-30grey,
    "50grey": $colour-transparency-50grey,
    "80grey": $colour-transparency-80grey,
    "8red": $colour-transparency-8red,
    "12red": $colour-transparency-12red,
    "30red": $colour-transparency-30red,
    "50red": $colour-transparency-50red,
    "80red": $colour-transparency-80red,
    "8info": $colour-transparency-8info,
    "12info": $colour-transparency-12info,
    "30info": $colour-transparency-30info,
    "50info": $colour-transparency-50info,
    "80info": $colour-transparency-80info,
  ),
  "global": (
    "octimine-primary": $colour-global-octimine-primary,
    "octimine-secondary": $colour-global-octimine-secondary,
    "dennemeyer-dark": $colour-global-dennemeyer-dark,
    "dennemeyer-medium": $colour-global-dennemeyer-medium,
    "dennemeyer-subtle": $colour-global-dennemeyer-subtle,
    "dennemeyer-orange": $colour-global-dennemeyer-orange,
  )
);

$background: (
  "contrast": $colours-background-bg-contrast,
  "primary": $colours-background-bg-primary,
  "transition": $colours-background-bg-transition,
  "secondary": $colours-background-bg-secondary,
  "tertiary": $colours-background-bg-tertiary,
  "disable": $colours-background-bg-disable,
  "brand": $colours-background-bg-brand,
  "orange": $colours-background-bg-orange,
  "info": $colours-background-bg-info,
  "warning": $colours-background-bg-warning,
  "danger": $colours-background-bg-danger,
  "success": $colours-background-bg-success,
  "reversed": $colours-background-bg-reversed,
);

.color {
  @each $name, $variants in $colors {
    &-#{$name} {
      @each $variant, $value in $variants {
        &-#{$variant} {
          color: $value;
        }
      }
    }
  }
}

.figma-bg, .bg {
  @each $name, $variants in $colors {
    &-#{$name} {
      @each $variant, $value in $variants {
        &-#{$variant} {
          background-color: $value !important;
        }
      }
    }
  }

  @each $name, $value in $background {
    &-#{$name} {
      background-color: $value !important;
    }
  }
}

