.figma {
  &-dropdown {
    position: relative;
    transition: all ease-in-out .200s;

    &-btn {
      display: flex;
      align-items: center;
      gap: $spacing-system-spacing-sm;
      align-self: stretch;
      border-radius: $spacing-system-spacing-xx-s;
      cursor: pointer;
      &-border{
        border: 1px solid $colours-border-subtle;
      }
      &-hover{
        &:hover,&:focus{
          background: $colours-background-bg-tertiary;
        }
        &:focus{
          border-width: 2px;
        }
        &:active, &.active{
          border-color: $colours-border-bold;
        }
      }

      &-sm{
        padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
        gap: $spacing-system-spacing-x-s;
        height: $spacing-system-spacing-xxx-big;
      }

      &-md{
        padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
        gap: $spacing-system-spacing-x-s;
        height: $spacing-system-spacing-x-lg;
      }

      &-lg{
        padding: $spacing-system-spacing-md $spacing-system-spacing-md;
        gap: $spacing-system-spacing-x-s;
        height: $spacing-system-spacing-xx-lg;
      }
    }

    &-content {
      display: none;
      position: absolute;
      background: $colours-background-bg-primary;
      box-shadow: $spacing-system-spacing-none $spacing-system-spacing-sm $spacing-system-spacing-md $spacing-system-spacing-sm rgba(40, 40, 40, 0.24);
      padding: $spacing-system-spacing-sm;
      color: $colours-content-content-secondary;
      z-index: 1;
      width: 100%;
      max-height: 70vh;
      overflow: auto;

      &-right {
        right: 0;
        width: max-content;
      }

      &-left {
        left: 0;
        width: max-content;
      }

      .menu-item-right {
        float: right;
      }

      a {
        color: $colours-content-content-secondary;
      }
    }

    &-item {
      cursor: pointer;
      padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
      border-radius: $radius-sm;
      color: $colours-content-content-primary;

      &:not(:last-child) {
        margin-bottom: $spacing-system-spacing-xx-s;
      }

      &-divider {
        width: 100%;
        border-bottom: 0.09375rem solid $colours-border-subtle;
        height: 0.09375rem;
      }

      &-hover{
        &:hover {
          background-color: $colours-background-bg-secondary;
        }

        &:focus-within, &:active{
          input{
            border-color: $colours-content-content-primary !important;
          }
        }
      }

      &.active {
        background-color: $colours-background-bg-secondary;
        color: $colours-content-content-primary;
        font-weight: 700;

        a {
          color: $colours-content-content-active;
        }
      }
      &.disabled{
        color: $colours-content-content-disabled !important;
        cursor: not-allowed;
        pointer-events: none;
      }

      &.figma-dropdown-item-check {
        padding: $spacing-system-spacing-sm $spacing-system-spacing-lg $spacing-system-spacing-sm $spacing-system-spacing-md;
        display: flex;
        justify-content: space-between;

        &.active {
          &::after {
            font-family: "Font Awesome 6 Pro";
            font-weight: 400;
            box-sizing: border-box;
            content: "\f00c";
            position: absolute;
            right: 1.5rem;
          }
        }
      }
    }

    &-label {
      padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
      margin-bottom: $spacing-system-spacing-big;
      border-radius: $radius-sm;
    }
    .dropdown-icon{
      top: 0;
    }

    &.disabled{
      color: $colours-content-content-disabled !important;
      cursor: not-allowed;
      opacity: .6;
      * {
        pointer-events:none;
      }
    }
  }

  &-dropdown.dropdown-hover:hover &-dropdown-content,
  &-dropdown.show &-dropdown-content, &-dropdown.show-on-hover:hover &-dropdown-content {
    display: block;
  }

  &-dropdown.dropdown-hover:hover,
  &-dropdown.show {
    .invert-icon {
      &::before {
        content: "\f00d" !important;
      }
    }

    .dropdown-icon, .figma-dropdown-icon {
      transition: transform .800s;
      transform: rotate(180deg);
    }
  }
}
