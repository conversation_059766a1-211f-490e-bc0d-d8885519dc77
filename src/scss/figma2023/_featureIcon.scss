.feature {
  &-container {
    border-radius: 6.25rem;
    border: 0.75rem solid $colour-blue-brand-100;
    background-color: $colour-blue-brand-200;
    display: inline-flex;
    align-items: center;
    gap: 0.625rem;


    border-color: $colour-blue-brand-100;
    background-color: $colour-blue-brand-200;
    color: $colours-content-content-active;
  }

  &-icon {
    text-align: center;
  }
  &-destructive{
    border-color: $colour-red-050;
    background-color: $colour-red-100;
    color: $colours-content-content-danger;
  }
  &-warning{
    border-color: $colour-yellow-050;
    background-color: $colour-yellow-200;
    color: $colours-content-content-warning;
  }
  &-success{
    border-color: $colour-green-050;
    background-color: $colour-green-200;
    color: $colours-content-content-success;
  }

  &-xl{
    .feature-icon{
      /* width: 3.75rem;
      height: 3.75rem; */
      font-size: $spacing-system-spacing-xx-lg;
      padding: $spacing-system-spacing-xx-big;
      @media screen and (max-height: 750px) {
        font-size: $spacing-system-spacing-xx-big;
        padding: $spacing-system-spacing-md;
      }
      @include small-screen() {
        font-size: $spacing-system-spacing-xx-big;
        padding: $spacing-system-spacing-md;
      }
    }
  }
  &-l{
    .feature-icon{
      width: $spacing-system-spacing-x-lg;
      height: $spacing-system-spacing-x-lg;
      font-size: $spacing-system-spacing-xxx-big;
      padding: $spacing-system-spacing-x-big;
    }
  }
  &, &-m {
    .feature-icon{
      /* width: 1.875rem;
      height: 1.875rem; */
      font-size: $spacing-system-spacing-xx-big;
      padding: $spacing-system-spacing-big;
    }
  }
  &-s{
    .feature-icon{
      /* width: $spacing-system-spacing-x-big;
      height: $spacing-system-spacing-x-big; */
      font-size: 1.125rem;
      padding: $spacing-system-spacing-x-s;
    }
  }
  &-xs{
    .feature-icon{
      /* width: $spacing-system-spacing-big;
      height: $spacing-system-spacing-big; */
      font-size: $spacing-system-spacing-md;
      padding: $spacing-system-spacing-xx-s;
    }
  }
}

