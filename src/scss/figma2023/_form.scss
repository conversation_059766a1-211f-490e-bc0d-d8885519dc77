@import "scss/layout2021/mixins";

$sizes: (
  large: (padding: $spacing-system-spacing-sm $spacing-system-spacing-md, height: $spacing-system-spacing-xx-lg),
  medium: (padding: $spacing-system-spacing-md $spacing-system-spacing-md, height: $spacing-system-spacing-x-lg)
);

.figma-form {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  align-content: flex-start;
  gap: $spacing-system-spacing-big;

  .figma-form-control {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    align-content: flex-start;
    gap: $spacing-system-spacing-xx-s;

    label {
      @include add-properties(map-deep-get($typography, label, medium), true);
    }

    input, textarea, select, .ng-select .ng-select-container {
      border: 1px solid $colours-border-subtle;
      background: $colours-background-bg-secondary;
      color: $colours-content-content-primary;
      @include border-radius($radius-sm);

      @include placeholder {
        color: $colours-content-content-disabled !important;
        font-weight: normal;
      }

      &:focus {
        background: $colours-background-bg-primary;
        border: 1px solid $colours-content-content-primary;
        outline: none !important;
      }
    }

    .ng-select {
      .ng-select-container {
        .ng-value-container {
          padding: 0 !important;
          margin: 0 !important;
          background: $colours-background-bg-secondary;
          color: $colours-content-content-primary;

          .ng-placeholder {
            @include add-properties(map-deep-get($typography, body, medium), true);
            color: $colours-content-content-disabled !important;
          }

          .ng-value {
            padding: 0 !important;
            margin: 0 !important;
            background: $colours-background-bg-secondary !important;
            color: $colours-content-content-primary !important;
            @include border-radius(unset !important);
            @include add-properties(map-deep-get($typography, body, medium), true);
          }
        }

        .ng-arrow {
          font-family: "Font Awesome 6 Pro" !important;
          -webkit-font-smoothing: antialiased;
          text-rendering: auto;
          display: inline-block;
          border-color: inherit !important;
          border-width: 0 !important;

          &:before {
            content: "\f078";
            font-weight: 400;
          }
        }
      }

      &.ng-select-disabled {
        .ng-select-container {
          background: $colours-background-bg-tertiary !important;

          .ng-value-container {
            background: $colours-background-bg-tertiary !important;

            .ng-value {
              color: $colours-content-content-disabled !important;
              border-color: $colours-border-subtle !important;
              background: $colours-background-bg-tertiary !important;
            }
          }

          .ng-arrow {
            &:before {
              color: $colours-content-content-disabled !important;
            }
          }
        }
      }

      &.ng-select-opened {
        .ng-select-container {
          border: 1px solid $colours-border-contrast !important;
          background: $colours-background-bg-primary !important;

          .ng-value-container {
            background: $colours-background-bg-primary !important;

            .ng-value {
              background: $colours-background-bg-primary !important;
            }
          }

          .ng-arrow {
            &:before {
              content: "\f077";
            }
          }
        }
      }

      .ng-dropdown-panel {
        padding: $spacing-system-spacing-sm;
        box-shadow: 0px 0px 10px 0px #2828283D;
        background: $colours-background-bg-primary !important;
        margin-top: $spacing-system-spacing-xxx-s !important;
        @include border-radius($radius-big);

        .ng-dropdown-panel-items .ng-option {
          color: $colours-content-content-primary !important;
          height: $spacing-system-spacing-x-lg !important;
          padding: $spacing-system-spacing-sm $spacing-system-spacing-big !important;
          @include border-radius($radius-sm);
          @include add-properties(map-deep-get($typography, body, medium), true);

          &:hover {
            background: $colours-background-bg-secondary !important;
          }

          &.ng-option-selected {
            background: $colours-background-bg-primary !important;
            font-weight: bold !important;
          }

          &.ng-option-marked:not(.ng-option-selected):not(:hover) {
            background: $colours-background-bg-primary !important;
          }
        }
      }
    }

    .figma-datepicker {
      position: relative;

      .toggle-icon {
        position: absolute;
        right: $spacing-system-spacing-md;
        top: calc(50% - 7px);
        font-weight: map-deep-get($typography, body, medium, font-weight) !important;
      }
    }

    .custom-radio-button {

      input[type="radio"] {
        position: absolute;
        opacity: 0;

        + .custom-radio-label {
          cursor: pointer;
          display: flex;
          align-content: center;
          flex-direction: row;
          align-items: flex-start;

          &:before {
            font-family: "Font Awesome 6 Pro";
            font-weight: 400;
            box-sizing: border-box;
            content: "\f111";
            border-radius: 100%;
            display: inline-block;
            position: relative;
            top: 0;
            width: 1rem;
            height: 1rem;
            font-size: 1rem;
            margin-right: $spacing-system-spacing-sm;
            vertical-align: middle;
            cursor: pointer;
            text-align: center;
            transition: all 250ms ease;
            outline: none;
            color: $colours-content-content-primary;
          }
        }

        &:checked {
          + .custom-radio-label {
            &:before {
              content: "\f058";
            }
          }

          &:disabled {
            + .custom-radio-label {
              cursor: default;

              &:before {
                cursor: default;
                color: $colours-content-content-disabled;
              }
            }
          }
        }

        &:focus, &:hover {
          + .custom-radio-label {
            &:before {
              color: $colours-content-content-tertiary;
            }
          }
        }

        + .custom-radio-label {
          &:empty {
            &:before {
              margin-right: 0;
            }
          }
        }
      }
    }
  }

  @each $size, $style in $sizes {
    &.figma-form-#{$size} {
      input, select, .ng-select .ng-select-container {
        padding: map-get($style, padding);
        height: map-get($style, height);
      }

      textarea {
        padding: map-get($style, padding);
      }
    }
  }
}

.figma{
  &-checkbox{
      display: flex;
      gap: $spacing-system-spacing-sm;
      cursor: pointer;
      input[type="checkbox"]{
          padding: $spacing-system-spacing-xxx-s;
          border: 1.5px solid $colours-border-moderate;
          background-color: $colours-background-bg-primary;
          color: $colour-grey-050;
          border-radius: $radius-sm;
          -webkit-appearance: initial;
          appearance: initial;
          width: 24px;
          height: 24px;
          display: flex;
          justify-content: center;
          align-items: center;
          &:after{
            font-family: 'Font Awesome 6 Pro', serif;
            font-size: 1rem;
            font-weight: 900;
          }
          &:hover{
            border-color: $colours-border-bold;
          }
          &:disabled, &.disabled{
            border-color: $colours-border-moderate;
            background-color: $colours-background-bg-disable;
            box-shadow: 0px 0px 0px 0px #135A9A1F;
          }
          &:checked{
              border-color: $colours-content-content-active !important;
              background-color: $colours-content-content-active !important;;
              color: $colour-grey-050;
              &::after{
                content: "\f00c";
              }
              &:hover{
                border-color: $colour-blue-brand-600 !important;;
              }
              &:disabled, &.disabled{
                background-color: $colours-content-content-disabled !important;;
                border-color: $colours-content-content-disabled !important;;
              }
          }
      }
      &.active{
          background-color: $colours-background-bg-brand;
      }
  }
}

.dropdown {
  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled):focus-within {
    .figma-checkbox {
      input {
        border-color: $colours-content-content-active !important;
        &:not(:checked) {
          border-color: $colours-border-moderate !important;
          &:hover {
            border-color: $colours-border-bold !important;
          }
        }
      }
    }
  }

  .figma-checkbox {
    input:active,
    input.active,
    input:focus {
      border-color: $colours-content-content-active !important;
    }
  }
}
