::ng-deep {
  .tooltip-inner, .popper-inner {
    &:has(.inline-modal-container) {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}

.inline-modal-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;
  width: 100%;

  .inline-modal-block {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start;
    width: 100%;
    gap: $spacing-system-spacing-sm;
    padding: $spacing-system-spacing-md;

    &.gap-spacing-md {
      gap: $spacing-system-spacing-md;
    }

    &.gap-spacing-x-s {
      gap: $spacing-system-spacing-x-s;
    }

    &.gap-spacing-xx-s {
      gap: $spacing-system-spacing-xx-s;
    }

    &.justify-content-end {
      justify-content: flex-end;
    }

    &.flex-row {
      flex-direction: row;
    }

    &.p-y-spacing-big {
      padding-top: $spacing-system-spacing-big;
      padding-bottom: $spacing-system-spacing-big;
    }

    &.p-t-spacing-big {
      padding-top: $spacing-system-spacing-big;
    }

    &.p-b-spacing-big {
      padding-bottom: $spacing-system-spacing-big;
    }
  }
}
