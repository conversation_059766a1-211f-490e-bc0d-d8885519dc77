.input-dialog {
  display: flex;
  flex-direction: column;
  width: 100%;
  &-container{
    display: flex;
    flex-grow: 1;
  }

  &-body {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    border: 1px solid $colours-border-subtle;
    border-radius: $radius-sm;
    background-color: $colours-background-bg-secondary;
    &:focus {
      border: 1px solid $colours-border-contrast;
    }
    &:hover {
      background-color: $colours-background-bg-primary;
      border: 1px solid $colours-border-contrast;
    }
  }

  &-textarea {
    position: relative;
    padding: $spacing-system-spacing-sm;
    cursor: text;
    .text-editor {
      outline: none !important;
      border: none;
      width: 100%;
      min-height: 1rem;
      resize: none;
      display: block;
      background: transparent;
      max-height: 7rem;
      overflow: auto;
      flex-wrap: nowrap;

      /* &:focus {
        background-color: $colours-background-bg-primary;
      } */

      &[contenteditable][data-placeholder]:empty:before{
        content: attr(data-placeholder);
        color: $colours-content-content-disabled;
        pointer-events: none;
        display: block; /* For Firefox */
      }
    }

    &.remaining-counter {
      padding-right: $spacing-system-spacing-xx-lg;
      color: $colours-content-content-tertiary;
    }
  }

  &-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-system-spacing-sm;
  }

  .show-remaining-count {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: $spacing-system-spacing-big;
  }

  .input-contorl {
    margin-top: $spacing-system-spacing-sm;
    display: flex;
    justify-content: flex-end;
  }

  .editor-avatar{
    display: flex;
  }

  .show-on-focus {
    display: none;
  }
  app-user-avatar{
    padding-left: $spacing-system-spacing-md;
  }

  &-focused {
    padding-bottom: $spacing-system-spacing-md;

    &.outer-border{
      border: 1px solid $colours-border-subtle;
      padding: $spacing-system-spacing-md;
      border-radius: $radius-big;
    }

    .input-dialog-body {
      padding: $spacing-system-spacing-md;
      flex-direction: column;
      align-items: stretch;
      border-color: $colours-border-contrast;
    }
    .input-dialog-textarea {
      margin-top: $spacing-system-spacing-sm;
      padding: 0;
    }

    app-user-avatar{
      padding-left: 0;
    }

    .show-on-focus {
      display: flex;
    }

    .hide-on-focus {
      display: none;
    }
  }
  &-reply{
    &.input-dialog-focused{
      border: 1px solid $colours-border-subtle;
      padding: $spacing-system-spacing-md;
      border-radius: $radius-big;
    }
  }
}

.comments-user-mentioned{
  .lcd-tagged-user,
  .lcd-tagged-group {
    outline: none !important;
    border: none !important;
    border-radius: 3px;
    background: transparent;
    padding: 0;
    color: $colours-content-content-active;
    &::before{
      content: "\40";
    }
  }
}
