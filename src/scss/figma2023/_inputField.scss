.input-field {
  position: relative;
  width: 100%;

  .icon {

    &-left,
    &-right {
      position: absolute;
      top: $spacing-system-spacing-md;
    }

    &-left {
      left: $spacing-system-spacing-sm;
    }

    &-right {
      right: $spacing-system-spacing-sm;
    }
  }

  &-text {
    border-radius: $radius-sm;
    border: 1px solid;
    width: 100%;
    padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
    background-color: transparent;
    border-color: $colours-border-subtle;
    color: $colours-content-content-primary;
    &::placeholder {
      color: $colours-content-content-disabled;
    }

    &:hover,
    &:focus {
      border-color: $colours-border-contrast;
      background-color: $colours-background-bg-primary;
    }

    &-error {
      border-color: $colours-border-danger;
    }

    &-icon {
      &-left {
        padding-left: $spacing-system-spacing-xxx-big;
      }

      &-right {
        padding-right: $spacing-system-spacing-xxx-big;
      }
    }
  }

  &-small {

    .input-field-text,
    .input-field-helper {
      padding: $spacing-system-spacing-x-s $spacing-system-spacing-md;
    }
    .input-field-text-icon-left{padding-left: $spacing-system-spacing-xx-big;}
    .input-field-text-icon-right{padding-right: $spacing-system-spacing-xx-big;}

    .icon-left {
      left: $spacing-system-spacing-x-s;
    }

    .icon-right {
      right: $spacing-system-spacing-x-s;
    }
  }

  &-large {

    .input-field-text,
    .input-field-helper {
      padding: $spacing-system-spacing-md;
    }
    .input-field-text-icon-left{padding-left: $spacing-system-spacing-lg;}
    .input-field-text-icon-right{padding-right: $spacing-system-spacing-lg;}
    .icon-left, .icon-right {
      top: $spacing-system-spacing-big;
    }

    .icon-left {
      left: $spacing-system-spacing-md;
    }

    .icon-right {
      right: $spacing-system-spacing-md;
    }
  }

  &-helper {
    margin: $spacing-system-spacing-sm 0;
    border-radius: $radius-sm;
    color: $colours-content-content-tertiary;
    &.invalid {
      color: $colours-content-content-danger;
      padding: $spacing-system-spacing-sm $spacing-system-spacing-md;
      background-color: $colours-background-bg-danger;
    }
  }

  &.disabled {
    .input-field-text,
    .input-field-helper,
    ::placeholder,
    .icon-left,
    .icon-right
     {
      color: $colours-content-content-disabled;
      border-color: $colours-content-content-disabled;
    }

    .input-field-text{
      background-color: $colours-background-bg-tertiary;
    }
  }
}
