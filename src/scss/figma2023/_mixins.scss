

@mixin transition($property, $duration, $easing: linear) {
    transition: $property $duration $easing;
    -webkit-transition: $property $duration $easing;
    -moz-transition: $property $duration $easing;
}
@mixin box-sizing($value: border-box) {
    -webkit-box-sizing: $value;
    -moz-box-sizing: $value;
    box-sizing: $value;
}

@mixin add-properties($properties, $important: false) {
    @each $p, $v in $properties {
        #{$p}: $v if($important, !important, null);
    }
}

@mixin add-ellipsis($lines: 1) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin add-button($color, $bg, $border, $pressed-color, $pressed-bg, $hover-color, $hover-bg, $focus-border, $hover-border) {
    display: inline-flex;
    justify-content: center;
    align-items: center ;
    cursor: pointer;
    gap: $spacing-system-spacing-xx-s;
    border-radius: $radius-sm;
    padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
    color: $color;
    border: $border;
    background-color: $bg;
    @include box-sizing();
    @include transition(all, $transition-delay-time, ease-in-out);

    &.no-hover {
      cursor: default;
    }

    &:not(.no-hover) {
      &:active, &.active, &.pressed {
        @if $pressed-bg == inherit {
          background-color: $bg;
        } @else {
          background-color: $pressed-bg;
        }
        @if $pressed-color == inherit {
          color: $color;
        } @else {
          color: $pressed-color;
        }
      }

      &:hover {
        @if $hover-bg == inherit {
          background-color: $bg;
        } @else {
          background-color: $hover-bg;
        }
        @if $hover-color == inherit {
          color: $color;
        } @else {
          color: $hover-color;
        }
        @if $hover-border == inherit {
          border: $border;
        } @else {
          border-bottom: $hover-border;
        }
      }

      &:focus, &.focus {
        outline: none;
        @if $focus-border == inherit {
          border: $border;
        } @else {
          border: $focus-border;
        }
      }
    }
    &.disabled,
    &:disabled {
      pointer-events: none;
      cursor: not-allowed;
      opacity: .3;
    }
}

@function map-deep-get($map, $keys...) {
    @each $key in $keys {
        $map: map-get($map, $key);
    }
    @return $map;
}

@mixin large-screen {
    @media screen and (min-width: map-get($spacing-container-max-widths, 'lg')) {
        @content;
    }
}

@mixin medium-screen {
    @media screen and (min-width: map-get($spacing-container-max-widths, 'sm')) and (max-width: calc(map-get($spacing-container-max-widths, 'lg') - 1px)) {
        @content;
    }
}

@mixin small-screen {
    @media screen and (min-width: map-get($spacing-container-max-widths, 'xs')) and (max-width: calc(map-get($spacing-container-max-widths, 'sm') - 1px)) {
        @content;
    }
}

@mixin mobile-screen {
    @media screen and (max-width: calc(map-get($spacing-container-max-widths, 'xs') - 1px)) {
        @content;
    }
}

@mixin add-tab($color, $bg, $border, $pressed-color, $pressed-bg, $hover-color, $hover-bg, $focus-border) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  gap: $spacing-system-spacing-xx-s;
  border-radius: $radius-sm;
  padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
  color: $color;
  border: $border;
  background-color: $bg;
  @include box-sizing();
  @include transition(all, $transition-delay-time, ease-in-out);
  &:active, &.active, &.pressed {
    @if $pressed-bg == inherit {
      background-color: $bg;
    } @else {
      background-color: $pressed-bg;
    }
    @if $pressed-color == inherit {
      color: $color;
    } @else {
      color: $pressed-color;
    }
  }
  &:hover {
    @if $hover-bg == inherit {
      background-color: $bg;
    } @else {
      background-color: $hover-bg;
    }
    @if $hover-color == inherit {
      color: $color;
    } @else {
      color: $hover-color;
    }
  }
  &:active:hover, &.active:hover {
    @if $pressed-bg == inherit {
      background-color: $bg;
    } @else {
      background-color: $pressed-bg;
    }
    @if $pressed-color == inherit {
      color: $color;
    } @else {
      color: $pressed-color;
    }
  }
  &:focus, &.focus {
    @if $focus-border == inherit {
      border: $border;
    } @else {
      border: $focus-border;
    }
  }
  &.disabled,
  &:disabled {
    pointer-events: none;
    cursor: not-allowed;
    opacity: .3;
  }
}

@mixin loading-box-style() {
  background: $colour-grey-200;
  border-radius: $radius-sm;
}

$typography: (
  color: (
    base: (color: $colours-content-content-primary),
    primary: (color: $colours-content-content-primary),
    secondary: (color: $colours-content-content-secondary),
    tertiary: (color: $colours-content-content-tertiary),
    info: (color: $colours-content-content-info),
    danger: (color: $colours-content-content-danger),
    success: (color: $colours-content-content-success),
    warning: (color: $colours-content-content-warning),
    active: (color: $colours-content-content-active),
    link: (color: $colours-content-content-link),
    disabled: (color: $colours-content-content-disabled),
    quartary: (color: $colours-content-content-quartary),
    enable: (color: $colours-content-content-enable),
    hover: (color: $colours-content-content-hover),
    reversed-grey: (color: $colours-content-content-reversed-grey),
  ),
  body: (
    large: (font-family: $font-family-base, font-size: $font-size-x-md, font-style: normal, font-weight: 400, line-height: $font-size-x-big),
    medium: (font-family: $font-family-base, font-size: $font-size-md, font-style: normal, font-weight: 400, line-height: 1.625rem),
    small: (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 400, line-height: $font-size-xx-md),
    xsmall: (font-family: $font-family-base, font-size: $font-size-xs, font-style: normal, font-weight: 400, line-height: $font-size-md)
  ),
  display: (
    '01': (font-family: $font-family-base, font-size: $font-size-x-huge, font-style: normal, font-weight: 300, line-height: $font-size-xx-huge),
    '02': (font-family: $font-family-base, font-size: $font-size-x-xlg, font-style: normal, font-weight: 400, line-height: $font-size-x-huge),
    '03': (font-family: $font-family-base, font-size: $font-size-xx-lg, font-style: normal, font-weight: 400, line-height: $font-size-huge)
  ),
  heading: (
    h1: (font-family: $font-family-base, font-size: $font-size-lg, font-style: normal, font-weight: 800, line-height: $font-size-xlg),
    h2: (font-family: $font-family-base, font-size: $font-size-xx-big, font-style: normal, font-weight: 700, line-height: $font-size-x-lg),
    h3: (font-family: $font-family-base, font-size: $font-size-big, font-style: normal, font-weight: 700, line-height: $font-size-xx-big),
    h4: (font-family: $font-family-base, font-size: $font-size-xx-md, font-style: normal, font-weight: 700, line-height: $font-size-x-big),
    h5: (font-family: $font-family-base, font-size: $font-size-md, font-style: normal, font-weight: 700, line-height: $font-size-big),
    h6: (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 600, line-height: $font-size-xx-md),
    h7: (font-family: $font-family-base, font-size: $font-size-xs, font-style: normal, font-weight: 600, line-height: $font-size-xx-md),
  ),
  label: (
    xlarge: (font-family: $font-family-base, font-size: $font-size-x-md, font-style: normal, font-weight: 700, line-height: $font-size-big),
    large: (font-family: $font-family-base, font-size: $font-size-md, font-style: normal, font-weight: 700, line-height: $font-size-big),
    medium: (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 600, line-height: $font-size-xx-md),
    small: (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 600, line-height: $font-size-xx-md),
    xsmall: (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 600, line-height: $font-size-md),
    xxs: (font-family: $font-family-base, font-size: $font-size-xs, font-style: normal, font-weight: 600, line-height: $font-size-x-md)
  ),
  quotes: (
    '01': (font-family: $font-family-base, font-size: $font-size-md, font-style: italic, font-weight: 400, line-height: $font-size-big),
    '02': (font-family: $font-family-base, font-size: $font-size-sm, font-style: italic, font-weight: 400, line-height: $font-size-xx-md)
  ),
  overline: (
    '01': (font-family: $font-family-base, font-size: $font-size-md, font-style: normal, font-weight: 600, line-height: $font-size-big, text-transform: uppercase),
    '02': (font-family: $font-family-base, font-size: $font-size-sm, font-style: normal, font-weight: 600, line-height: $font-size-xx-md, text-transform: uppercase),
    '03': (font-family: $font-family-base, font-size: $font-size-xs, font-style: normal, font-weight: 400, line-height: $font-size-md, text-transform: uppercase),
  ),
  code: (
    '01': (font-family: $font-family-code, font-size: $font-size-sm, font-style: normal, font-weight: 400, line-height: $font-size-xx-md),
    '02': (font-family: $font-family-code,font-size: $font-size-xs,font-style: normal,font-weight: 400,line-height: $font-size-md)
  ),
  style: (
    light: (font-style: normal, font-weight: 300),
    light-italic: (font-style: italic, font-weight: 300),
    regular: (font-style: normal, font-weight: 400),
    regular-italic: (font-style: italic, font-weight: 400),
    semi-bold: (font-style: normal, font-weight: 600),
    semi-bold-italic: (font-style: italic, font-weight: 600),
    bold: (font-style: normal, font-weight: 700),
    bold-italic: (font-style: italic, font-weight: 700),
  )
);
