@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

.filter-results-dialog {
app-filter-list-dialog{
    display: contents;
}
.modal-dialog {
    max-width: 100% !important;
    margin: 0 !important;
    height: 100%;
    width: 100%;

    .modal-content {

    .modal-body {
        padding: 5px 65px 5px 65px;
        overflow-y: auto;
    }

    @include media-breakpoint-down(xl, $grid-breakpoints) {
        height: 90vh;
        width: 90vw;
        left: 5vw;
        top: 5vh;
    }

    @include media-breakpoint-up(xl, $grid-breakpoints) {
        height: 70vh;
        width: 70vw;
        left: 15vw;
        top: 5vh;
    }
    }
}
}
