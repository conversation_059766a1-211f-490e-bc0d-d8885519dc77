@import "scss/layout2021/mixins";

$sizes: (
  'xs': $radius-xs,
  'sm': $radius-sm,
  'md': $radius-md,
  'big': $radius-big,
  'lg': $radius-lg,
  'xlg': $radius-xlg,
  'huge': $radius-huge,
  'x-huge': $radius-x-huge,
  'xx-huge': $radius-xx-huge,
  'none': unset
);


.radius {
  @each $name, $size in $sizes {
    &-#{$name} {
      @include border-radius($size);
    }
    &-top-left-#{$name} {
      @include border-top-left-radius($size);
    }
    &-top-right-#{$name} {
      @include border-top-right-radius($size);
    }
    &-bottom-left-#{$name} {
      @include border-bottom-left-radius($size);
    }
    &-bottom-right-#{$name} {
      @include border-bottom-right-radius($size);
    }
    &-top-#{$name} {
      @include border-top-radius($size);
    }
    &-right-#{$name} {
      @include border-right-radius($size);
    }
    &-bottom-#{$name} {
      @include border-bottom-radius($size);
    }
    &-left-#{$name} {
      @include border-left-radius($size);
    }
  }
}
