.scrollbar-2024, .scrollbar-2024-sm {
  ::-webkit-scrollbar {
    min-width: 8px;
    min-height: 8px;
    background-color: transparent;
    border-radius: 3px;
  }
  &-sm {
    ::-webkit-scrollbar {
      min-width: 4px;
      min-height: 4px;
    }
  }
  ::-webkit-scrollbar-thumb {
    background-color: $colours-border-moderate;
    cursor: pointer;
    height: 30px;
    border-radius: 5px;

    &:hover {
      background-color: $colours-border-bold;
    }
  }

  @-moz-document url-prefix() {
    * {
      scrollbar-color: $colours-border-moderate transparent;
      scrollbar-gutter: stable both-edges;
      scrollbar-width: auto;
    }
  }
}