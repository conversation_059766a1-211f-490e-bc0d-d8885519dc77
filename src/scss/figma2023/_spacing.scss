$spacing: (
  "spacing-none": $spacing-system-spacing-none,
  "spacing-xxx-s": $spacing-system-spacing-xxx-s,
  "spacing-xx-s": $spacing-system-spacing-xx-s,
  "spacing-x-s": $spacing-system-spacing-x-s,
  "spacing-sm": $spacing-system-spacing-sm,
  "spacing-md": $spacing-system-spacing-md,
  "spacing-big": $spacing-system-spacing-big,
  "spacing-x-big": $spacing-system-spacing-x-big,
  "spacing-xx-big": $spacing-system-spacing-xx-big,
  "spacing-xxx-big": $spacing-system-spacing-xxx-big,
  "spacing-lg": $spacing-system-spacing-lg,
  "spacing-x-lg": $spacing-system-spacing-x-lg,
  "spacing-xx-lg": $spacing-system-spacing-xx-lg,
  "spacing-xxx-lg": $spacing-system-spacing-xxx-lg,
  "spacing-xlg": $spacing-system-spacing-xlg,
  "spacing-x-xlg": $spacing-system-spacing-x-xlg,
  "spacing-xx-xlg": $spacing-system-spacing-xx-xlg,
  "spacing-xxx-xlg": $spacing-system-spacing-xxx-xlg,
  "spacing-huge": $spacing-system-spacing-huge,
  "spacing-x-huge": $spacing-system-spacing-x-huge,
  "spacing-xx-huge": $spacing-system-spacing-xx-huge
);
.p {
  @each $name, $value in $spacing {
    &-#{$name} {
      padding: $value !important;
    }
    &-l-#{$name} {
      padding-left: $value !important;
    }
    &-t-#{$name} {
      padding-top: $value !important;
    }
    &-r-#{$name} {
      padding-right: $value !important;
    }
    &-b-#{$name} {
      padding-bottom: $value !important;
    }
    &-x-#{$name} {
      padding-left: $value !important;
      padding-right: $value !important;
    }
    &-y-#{$name} {
      padding-top: $value !important;
      padding-bottom: $value !important;
    }
  }
}
.m {
  @each $name, $value in $spacing {
    &-#{$name} {
      margin: $value;
    }
    &-l-#{$name} {
      margin-left: $value !important;
    }
    &-t-#{$name} {
      margin-top: $value !important;
    }
    &-r-#{$name} {
      margin-right: $value !important;
    }
    &-b-#{$name} {
      margin-bottom: $value !important;
    }
    &-x-#{$name} {
      margin-left: $value !important;
      margin-right: $value !important;
    }
    &-y-#{$name} {
      margin-top: $value !important;
      margin-bottom: $value !important;
    }
  }
}
.h {
  @each $name, $value in $spacing {
    &-#{$name} {
      height: $value !important;
    }
  }
}
.min-h {
  @each $name, $value in $spacing {
    &-#{$name} {
      min-height: $value !important;
    }
  }
}
.max-h {
  @each $name, $value in $spacing {
    &-#{$name} {
      max-height: $value !important;
    }
  }
}
.gap {
  @each $name, $value in $spacing {
    &-#{$name} {
      gap: $value;
    }
  }
}

@each $name, $value in $spacing-container-max-widths {
  .figma-container-#{$name} {
    max-width: $value;
    display: block;
    margin: 0 auto;
  }

  @media screen and (max-width: $value) {
    .figma-hide-#{$name} {
      display: none;
    }
  }
}
