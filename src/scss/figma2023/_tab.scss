@import './variables';

$tabs: (
  pill: (color: $colour-grey-800, bg: $colour-base-white, border: none, pressed-color: $colour-blue-brand-500, pressed-bg: $colour-blue-brand-100, hover-color: $colour-grey-900, hover-bg: $colour-grey-200, focus-border: none),
  underline: (color: $colour-grey-800, bg: $colour-base-white, border: none, pressed-color: $colour-blue-brand-500, pressed-bg: $colour-base-white, hover-color: $colour-grey-900, hover-bg: $colour-blue-brand-100, focus-border: none),
);

@each $name, $properties in $tabs {
  .tab-#{$name} {
    @include add-tab(map-get($properties, color), map-get($properties, bg), map-get($properties, border), map-get($properties, pressed-color), map-get($properties, pressed-bg), map-get($properties, hover-color), map-get($properties, hover-bg), map-get($properties, focus-border));
  }
}

.tab-pill, .tab-underline {
  padding: $spacing-system-spacing-sm $spacing-system-spacing-big;
  height: $spacing-system-spacing-x-lg;
}
