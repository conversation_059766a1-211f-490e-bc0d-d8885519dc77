.tag-label{
    &-primary, &-secondary, &-destructive, &-warning, &-success, &-semantic, &-boolean, &-citation, &-monitor, &-manual, &-multiple, &-unknown{
        display: inline-flex;
        justify-content: center;
        align-items: center ;
        gap: $spacing-system-spacing-x-s;
        border-radius: $spacing-system-spacing-xx-s;
        padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-x-s;
    }
    &-small{
        gap: $spacing-system-spacing-xx-s;
        padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-xx-s;
    }
    &-rounded{
        border-radius: $radius-xx-huge;
    }
    &-primary{
        color: $colours-content-content-active;
        background-color: $colours-background-bg-brand;
        &:hover, &.active{
            background-color: $colour-blue-brand-200;
        }
        &-outline{
            border: 1px solid $colours-border-brand;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30blue-brand;
            border-color: $colour-transparency-30blue-brand;
        }
    }

    &-secondary{
        color: $colours-content-content-secondary;
        background-color: $colours-background-bg-secondary;
        &:hover, &.active{
            background-color: $colours-background-bg-tertiary;
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-destructive{
        color: $colours-content-content-danger;
        background-color: $colours-background-bg-danger;
        &:hover, &.active{
            background-color: $colour-red-200;
        }
        &-outline{
            border: 1px solid $colours-border-danger;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30red;
            border-color: $colour-transparency-30red;
        }
    }

    &-warning{
        color: $colours-content-content-warning;
        background-color: $colours-background-bg-warning;
        &:hover, &.active{
            background-color: $colour-yellow-200;
        }
        &-outline{
            border: 1px solid $colours-border-warning;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: #7254084D;
            border-color: #7254084D;
        }
    }

    &-success{
        color: $colours-content-content-success;
        background-color: $colours-background-bg-success;
        &:hover, &.active{
            background-color: $colour-green-200;
        }
        &-outline{
            border: 1px solid $colours-border-success;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: #1B76264D;
            border-color: #1B76264D;
        }
    }

    &-semantic{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Lavender;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Lavender), green($colours-tags-Lavender), blue($colours-tags-Lavender), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-boolean{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Cream;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Cream), green($colours-tags-Cream), blue($colours-tags-Cream), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-citation{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Light-blue;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Light-blue), green($colours-tags-Light-blue), blue($colours-tags-Light-blue), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-monitor{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Olive;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Olive), green($colours-tags-Olive), blue($colours-tags-Olive), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-manual{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Yellow;
        cursor: unset !important;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Yellow), green($colours-tags-Yellow), blue($colours-tags-Yellow), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-multiple{
        color: $colours-content-content-secondary;
        background-color: $colours-tags-Light-blue;
        &:hover, &.active{
            background-color:  rgba(red($colours-tags-Light-blue), green($colours-tags-Light-blue), blue($colours-tags-Light-blue), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }

    &-unknown{
        color: $colours-content-content-secondary;
        background-color: $colours-background-bg-secondary;
        &:hover, &.active{
            background-color:  rgba(red($colours-background-bg-secondary), green($colours-background-bg-secondary), blue($colours-background-bg-secondary), 0.75);
        }
        &-outline{
            border: 1px solid $colours-border-bold;
        }
        &-disabled{
            cursor: not-allowed;
            pointer-events: all !important;
            color: $colour-transparency-30grey;
            border-color: $colour-transparency-30grey;
        }
    }
}
