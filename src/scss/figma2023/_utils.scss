$ellipsis: 1, 2, 3, 4, 5, 6;

@each $size in $ellipsis {
  .ellipsis-text-#{$size} {
    @include add-ellipsis($size);
  }
}

.overflow{
  &-none{
    overflow: unset;
  }
  &-scroll{
    overflow: scroll;
  }
  &-auto{
    overflow: auto;
  }
}

.no-link{
  pointer-events: none !important;
}

.horizontal-divider {
  width: 100%;
  border-bottom: 1px solid $colours-border-subtle;
  height: 1px;
}

.octi-icon{
  background-image: url('/assets/images/octi_ai.svg');
  background-repeat: no-repeat; // don't repeat background
  background-position: center;
  display: inline-block;
}
.octi-icon-v2{
  background-image: url('/assets/images/octi_ai_v2.svg');
  background-repeat: no-repeat; // don't repeat background
  background-position: center;
  display: inline-block;
}
.octi-icon-v3{
  background-image: url('/assets/images/octi_ai_v3.svg');
  background-repeat: no-repeat; // don't repeat background
  background-position: center;
  display: inline-block;
}
.octi-icon-v5{
  background-image: url('/assets/images/octi_ai_v5.svg');
  background-repeat: no-repeat; // don't repeat background
  background-position: center;
  display: inline-block;
}

.super-z-index{
  z-index: 2147483004;
}
.transition-all{
  transition: all ease-in .2s;
}
.opacity-0{
  opacity: 0;
}
