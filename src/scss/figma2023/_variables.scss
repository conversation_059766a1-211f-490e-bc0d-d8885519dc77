/* Customization */

$radius-xs: 0.125rem;
$radius-sm: 0.25rem;
$radius-md: 0.375rem;
$radius-big: 0.5rem;
$radius-lg: 0.75rem;
$radius-xlg: 1rem;
$radius-huge: 1.25rem;
$radius-x-huge: 2.0rem;
$radius-xx-huge: 2.25rem;
$radius-xxx-huge: 2.5rem;

$spacing-system-spacing-none: 0rem;
$spacing-system-spacing-xxx-s: 0.125rem;
$spacing-system-spacing-xx-s: 0.25rem;
$spacing-system-spacing-x-s: 0.375rem;
$spacing-system-spacing-sm: 0.5rem;
$spacing-system-spacing-md: 0.75rem;
$spacing-system-spacing-big: 1rem;
$spacing-system-spacing-x-big: 1.25rem;
$spacing-system-spacing-xx-big: 1.5rem;
$spacing-system-spacing-xxx-big: 2rem;
$spacing-system-spacing-lg: 2.25rem;
$spacing-system-spacing-x-lg: 2.5rem;
$spacing-system-spacing-xx-lg: 3rem;
$spacing-system-spacing-xxx-lg: 3.5rem;
$spacing-system-spacing-xlg: 4rem;
$spacing-system-spacing-x-xlg: 4.5rem;
$spacing-system-spacing-xx-xlg: 5rem;
$spacing-system-spacing-xxx-xlg: 5.5rem;
$spacing-system-spacing-huge: 6rem;
$spacing-system-spacing-x-huge: 8rem;
$spacing-system-spacing-xx-huge: 10rem;

/* Copy these variables from Figma app using variables2css plugin */
/* Blue theme colors */
$colour-base-black: #000000;
$colour-base-white: #ffffff;
$colour-blue-brand-050: #f1fbfe;
$colour-blue-brand-100: #e0f7ff;
$colour-blue-brand-200: #a8def8;
$colour-blue-brand-300: #79bee7;
$colour-blue-brand-400: #3d8dc8;
$colour-blue-brand-500: #135a9a;
$colour-blue-brand-600: #0a4982;
$colour-blue-brand-700: #092d67;
$colour-blue-brand-800: #041b4a;
$colour-blue-brand-900: #00133a;
$colour-global-dennemeyer-dark: #0f2c35;
$colour-global-dennemeyer-medium: #4b5d66;
$colour-global-dennemeyer-orange: #fb6f09;
$colour-global-dennemeyer-subtle: #698a95;
$colour-global-octimine-primary: #041b4a;
$colour-global-octimine-secondary: #246eb0;
$colour-green-050: #e7fcde;
$colour-green-100: #d5f6c6;
$colour-green-200: #b3f1a6;
$colour-green-300: #7ad673;
$colour-green-400: #48ae4b;
$colour-green-500: #1b7626;
$colour-green-600: #146626;
$colour-green-700: #0d5523;
$colour-green-800: #094521;
$colour-green-900: #073b1c;
$colour-grey-050: #fffefd;
$colour-grey-100: #fafafa;
$colour-grey-200: #f6f6f6;
$colour-grey-300: #ebebeb;
$colour-grey-400: #dad8d6;
$colour-grey-500: #ceccca;
$colour-grey-600: #969696;
$colour-grey-700: #707070;
$colour-grey-800: #434343;
$colour-grey-900: #282828;
$colour-info-050: #eaf7ff;
$colour-info-100: #d9f0ff;
$colour-info-200: #abdcff;
$colour-info-300: #6fb6f4;
$colour-info-400: #4899ea;
$colour-info-500: #126edd;
$colour-info-600: #0c55be;
$colour-info-700: #093f9f;
$colour-info-800: #062c7f;
$colour-info-900: #04205c;
$colour-orange-brand-050: #fff3ea;
$colour-orange-brand-100: #ffe1cc;
$colour-orange-brand-200: #fbc7a2;
$colour-orange-brand-300: #fa9e5b;
$colour-orange-brand-400: #ff8227;
$colour-orange-brand-500: #fb6f09;
$colour-orange-brand-600: #e76303;
$colour-orange-brand-700: #bb5205;
$colour-orange-brand-800: #954003;
$colour-orange-brand-900: #733000;
$colour-red-050: #ffebe7;
$colour-red-100: #ffd2c9;
$colour-red-200: #fdbdaf;
$colour-red-300: #f0827a;
$colour-red-400: #c04446;
$colour-red-500: #951522;
$colour-red-600: #760e13;
$colour-red-700: #580a0e;
$colour-red-800: #3b070a;
$colour-red-900: #1d0305;
$colour-transparency-8black: #00000014;
$colour-transparency-12red: #9515221f;
$colour-transparency-12blue-brand: #135a9a1f;
$colour-transparency-12grey: #9696961f;
$colour-transparency-12info: #0c55be1f;
$colour-transparency-30blue-brand: #135a9a4d;
$colour-transparency-30grey: #9696964d;
$colour-transparency-30info: #0c55be4d;
$colour-transparency-30red: #9515224d;
$colour-transparency-30green: #1b76264d;
$colour-transparency-30yellow: #7254084d;
$colour-transparency-50blue-brand: #135a9a80;
$colour-transparency-50grey: #96969680;
$colour-transparency-50info: #0c55be80;
$colour-transparency-50red: #95152280;
$colour-transparency-8blue-brand: #135a9a14;
$colour-transparency-8grey: #96969614;
$colour-transparency-8info: #0c55be14;
$colour-transparency-8red: #95152214;
$colour-transparency-80blue-brand: #135a9acc;
$colour-transparency-80grey: #969696cc;
$colour-transparency-80info: #0c55becc;
$colour-transparency-80red: #951522cc;
$colour-transparency-15grey: #28282826;
$colour-transparency-65green: #d5f6c6a6;
$colour-yellow-050: #ffebb9;
$colour-yellow-100: #fde5a8;
$colour-yellow-200: #f9d885;
$colour-yellow-300: #fbd166;
$colour-yellow-400: #f5c341;
$colour-yellow-500: #eeb521;
$colour-yellow-600: #c79310;
$colour-yellow-700: #9c740c;
$colour-yellow-800: #725408;
$colour-yellow-900: #493604;

$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-md: 1rem;
$font-size-x-md: 1.125rem;
$font-size-xx-md: 1.25rem;
$font-size-big: 1.5rem;
$font-size-x-big: 1.75rem;
$font-size-xx-big: 2rem;
$font-size-lg: 2.5rem;
$font-size-x-lg: 2.625rem;
$font-size-xx-lg: 3rem;
$font-size-xlg: 3.25rem;
$font-size-x-xlg: 3.75rem;
$font-size-huge: 4rem;
$font-size-x-huge: 4.875rem;
$font-size-xx-huge: 6.5rem;

$font-family-base: 'Open Sans';
$font-family-code: 'Courier Prime Regular';

/* content-colors */
$colours-content-content-primary: $colour-grey-900;
$colours-content-content-secondary: $colour-grey-800;
$colours-content-content-tertiary: $colour-grey-700;
$colours-content-content-info: $colour-info-600;
$colours-content-content-danger: $colour-red-500;
$colours-content-content-success: $colour-green-500;
$colours-content-content-warning: $colour-yellow-800;
$colours-content-content-active: $colour-blue-brand-500;
$colours-content-content-link: $colour-blue-brand-700;
$colours-content-content-disabled: $colour-grey-500;
$colours-content-content-quartary: $colour-grey-600;
$colours-content-content-enable: $colour-info-600;
$colours-content-content-hover: $colour-blue-brand-600;
$colours-content-content-reversed-grey: $colour-grey-050;

/* content-border-color */
$colours-border-soft: $colour-grey-200;
$colours-border-subtle: $colour-grey-400;
$colours-border-moderate: $colour-grey-500;
$colours-border-minimal: $colour-grey-300;
$colours-border-bold: $colour-grey-700;
$colours-border-contrast: $colour-grey-900;
$colours-border-brand: $colour-blue-brand-300;
$colours-border-warning: $colour-yellow-300;
$colours-border-danger: $colour-red-300;
$colours-border-success: $colour-green-300;
$colours-border-info: $colour-info-300;
$colours-border-secondary: $colour-orange-brand-300;

/* content-background-colors */
$colours-background-bg-contrast: $colour-base-white;
$colours-background-bg-primary: $colour-grey-050;
$colours-background-bg-transition: $colour-grey-100;
$colours-background-bg-secondary: $colour-grey-200;
$colours-background-bg-tertiary: $colour-grey-300;
$colours-background-bg-disable: $colour-grey-400;
$colours-background-bg-brand: $colour-blue-brand-100;
$colours-background-bg-orange: $colour-orange-brand-100;
$colours-background-bg-info: $colour-info-100;
$colours-background-bg-warning: $colour-yellow-050;
$colours-background-bg-danger: $colour-red-100;
$colours-background-bg-success: $colour-green-100;
$colours-background-bg-reversed: $colour-grey-900;

/* content-tag-color */
$colours-tags-Silver: #E0E4E5;
$colours-tags-Cream: #F1CDBE;
$colours-tags-Orange: $colour-orange-brand-200;
$colours-tags-Brown: #D0B493;
$colours-tags-Yellow: $colour-yellow-100;
$colours-tags-Bright-yellow: #F1FC7B;
$colours-tags-Lemon: #E6EBAC;
$colours-tags-Olive: #A6DCB2;
$colours-tags-Fluorescent: #93FF97;
$colours-tags-Arctic: #BDFEF2;
$colours-tags-Light-blue: #A6E9F8;
$colours-tags-Sapphire: #B0D5DD;
$colours-tags-Blue: $colour-blue-brand-300;
$colours-tags-Iris: #B9C8FF;
$colours-tags-Purple: #D2AFFF;
$colours-tags-Lavender: #F4D6FF;
$colours-tags-Magenta: #E7B8DC;
$colours-tags-Pink: #FFD7EA;
$colours-tags-Red-ruby: #FDA3A3;
$colours-tags-Scarlet: #DA9C9C;

/* container-for -responsive */
$spacing-container-max-widths: ( xs: 360px, sm: 834px, md: 950px, lg: 1024px, xl: 1228px, xxl: 1440px);

/* transition-time */
$transition-delay-time: .2s;

/* tooltip */
$tooltip-bg: $colours-background-bg-primary;
$tooltip-color: $colours-content-content-primary;
$tooltip-link-color: $colour-blue-brand-600;


/* button hove */
$colours-buttons-main-tertiary-grey-bg-hover: $colour-grey-200;
$colours-buttons-main-tertiary-grey-content: $colour-grey-900;
$colours-buttons-main-tertiary-grey-content-disabled: rgba(150, 150, 150, 0.50);

/* button disabled */
$colours-buttons-main-tertiary-blue-content-disabled: rgba(19, 90, 154, 0.3);