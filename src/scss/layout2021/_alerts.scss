$message-types: ( info: ( $alert-info-background, $alert-info-border-color, $alert-info-text, $alert-info-close), warning: ($alert-warning-background, $alert-warning-border-color, $alert-warning-text, $alert-warning-close), success: ($alert-success-background, $alert-success-border-color, $alert-success-text, $alert-success-close), danger: ($alert-danger-background, $alert-danger-border-color, $alert-danger-text, $alert-danger-close));
@each $type,
$colors in $message-types {
    .alert-#{$type} {
        background: nth($colors, 1);
        border-color: nth($colors, 2);
        color: nth($colors, 3);
        font-size: $alert-title-size;
        font-weight: bold;
        font-family: $font-open-sans-regular;
        .close {
            color: nth($colors, 4);
            cursor: pointer;
        }
        ol, ul, dl, p {
          margin: 0;
          font-size: $alert-text-size;
          font-weight: normal;
        }
    }
}

.alert{
    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        text-shadow: 0 1px 0 #fff;
        opacity: .5
    }
    
    .close:hover {
        color: #000;
        text-decoration: none
    }
    
    .close:not(:disabled):not(.disabled):hover,.close:not(:disabled):not(.disabled):focus {
        opacity: .75
    }
}

