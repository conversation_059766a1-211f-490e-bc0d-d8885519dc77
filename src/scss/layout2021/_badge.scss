.badge {
  font-family: $font-open-sans-light;
}

.badge-primary {
  @include button-variant($brand-green, darken($brand-green, 7.5%), $color-text-01, $brand-green-hover, lighten($brand-green, 75%), $color-text-01, $brand-green-pressed, darken($brand-green, 30%));

  background-color: $brand-green;
  border: 1px solid $brand-green !important;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-green-pressed;
    border: 1px solid $brand-green-pressed !important;
  }

  &.badge-icon {
    color: $button-color-pressed;
  }
}

.badge-primary-outline {
  @include button-outline-variant($brand-green);

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-green-pressed;
    border: 1px solid $brand-green-pressed !important;
    color: $button-color-pressed;
  }

  &.badge-icon {
    color: $brand-green-pressed;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }
}

.badge-secondary {
  @include button-variant($brand-orange, darken($brand-orange, 7.5%), $color-text-01, $brand-orange-hover, lighten($brand-orange, 75%), $color-text-01, $brand-orange-pressed, darken($brand-orange, 30%));

  background-color: $brand-orange;
  border: 1px solid $brand-orange !important;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-orange-pressed;
    border: 1px solid $brand-orange-pressed !important;
  }

  &.badge-icon {
    color: $button-color-pressed;
  }
}

.badge-secondary-outline {
  @include button-outline-variant($brand-orange);
  background-color: $brand-orange;
  border: 1px solid $brand-orange !important;
  color: $button-color-pressed;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-orange-pressed;
    border: 1px solid $brand-orange-pressed !important;
    color: $button-color-pressed;
  }

  &.badge-icon {
    color: $brand-orange-pressed;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }
}

