.btn {
  border-radius: 3px;
  opacity: 1;
  font-size: $button-size;

  &:not(:disabled):not(.disabled):not(.btn-ghost):not(.btn-secondary-ghost):hover,
  &:not(:disabled):not(.disabled):focus {
    box-shadow: $button-border-shadow !important;
  }

  &:disabled,
  &.disabled {
    background-color: $disabled-color !important;
    box-shadow: none !important;
    border: none !important;
    color: $button-color-disabled !important;
    cursor: auto;
  }
}

.btn-sm {
  font-size: $button-sm-size;
  height: 32px;

  span, i {
    width: 16px;
    height: 16px;
    line-height: 16px;
  }

  .icon-left {
    margin-right: 8px;
  }

  .icon-right {
    margin-left: 8px;
  }

  &:not(.btn-icon) {
    padding: 7px 15px;
    min-width: 100px;
  }

  &.btn-icon {
    width: 32px !important;
    min-width: 32px;
    padding: 0;
    margin: 0;

    span, i {
      padding: 0;
      margin: 7px;
      border: 0;
    }
  }
}

.btn-md {
  font-size: $button-md-size;
  height: 40px;

  span, i {
    width: 16px;
    height: 16px;
    line-height: 16px;
  }

  .icon-left {
    margin-right: 8px;
  }

  .icon-right {
    margin-left: 8px;
  }

  &:not(.btn-icon) {
    padding: 9px 15px;
    min-width: 100px;
  }

  &.btn-icon {
    width: 40px !important;
    min-width: 40px;
    padding: 0;
    margin: 0;

    span, i {
      padding: 0;
      margin: 11px;
      border: 0;
    }
  }
}

.btn-lg {
  font-size: $button-lg-size;
  height: 48px;

  span, i {
    width: 16px;
    height: 16px;
  }

  .icon-left {
    margin-right: 8px;
  }

  .icon-right {
    margin-left: 8px;
  }

  &:not(.btn-icon) {
    padding: 12px 15px;
    min-width: 110px;
  }

  &.btn-icon {
    width: 48px !important;
    min-width: 48px;
    padding: 0;
    margin: 0;

    span, i {
      padding: 0;
      margin: 15px;
      border: 0;
    }
  }
}

.btn-xl {
  font-size: $button-xl-size;
  height: 70px;

  span,
  i {
    width: 18px;
    height: 18px;
  }

  .icon-left {
    margin-right: 8px;
  }

  .icon-right {
    margin-left: 8px;
  }

  &:not(.btn-icon) {
    padding: 22px 15px;
    min-width: 170px;
  }

  &.btn-icon {
    width: 70px !important;
    min-width: 70px;
    padding: 0;
    margin: 0;

    span, i {
      padding: 0;
      margin: 25px;
      border: 0;
    }
  }
}

.btn-primary {
  @include button-variant($brand-green, darken($brand-green, 7.5%), $color-text-01, $brand-green-hover, lighten($brand-green, 75%), $color-text-01, $brand-green-pressed, darken($brand-green, 30%));
  border: none;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-green-pressed;
    border: 1px solid $brand-green-pressed !important;
  }

  &.btn-icon {
    color: $button-color-pressed;
  }
}

.btn-primary-outline {
  @include button-outline-variant($brand-green, #fff);

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-green-pressed;
    border: 1px solid $brand-green-pressed !important;
    color: $button-color-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: $button-background-outline-disabled !important;
    box-shadow: none !important;
    border: $button-border-outline-disabled !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-green;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }
}

.btn-secondary {
  @include button-variant($brand-orange, darken($brand-orange, 7.5%), $color-text-01, $brand-orange-hover, lighten($brand-orange, 75%), $color-text-01, $brand-orange-pressed, darken($brand-orange, 30%));
  border: none;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-orange-pressed;
    border: 1px solid $brand-orange-pressed !important;
  }

  &.btn-icon {
    color: $button-color-pressed;
  }
}

.btn-secondary-outline {
  @include button-outline-variant($brand-orange);

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-orange-pressed;
    border: 1px solid $brand-orange-pressed !important;
    color: $button-color-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: $button-background-outline-disabled !important;
    box-shadow: none !important;
    border: $button-border-outline-disabled !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-orange-pressed;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }
}

.btn-ghost {
  @include button-outline-variant($brand-green);
  border: none;

  &:hover {
    background-color: transparent;
    color: $brand-green-hover;
  }

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $brand-green-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-green;

    &:not(:disabled):not(.disabled):hover {
      color: $brand-green-hover;
    }

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }

  &.btn-outline {
    border: 1px solid $brand-green;
    &:hover {
      background-color: transparent;
      border-color: $brand-green-hover;
    }
  }
}

.btn-secondary-ghost {
  @include button-outline-variant($brand-orange);
  border: none;

  &:hover {
    background-color: transparent;
    color: $brand-orange-hover;
  }

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $brand-orange-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-orange;

    &:not(:disabled):not(.disabled):hover {
      color: $brand-orange-hover;
    }

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }

  &.btn-outline {
    border: 1px solid $brand-orange;
    &:hover {
      background-color: transparent;
      border-color: $brand-orange-hover;
    }
  }
}

.btn-gray-ghost {
  @include button-outline-variant($brand-gray);
  border: none;

  &:hover {
    background-color: transparent;
    color: $brand-gray-hover;
  }

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $brand-gray-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: transparent !important;
    box-shadow: none !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-gray;

    &:not(:disabled):not(.disabled):hover {
      color: $brand-gray-hover;
    }

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }

  &.btn-outline {
    border: 1px solid $brand-gray;
    &:hover {
      background-color: transparent;
      border-color: $brand-gray-hover;
    }
  }
}

.btn-danger {
  @include button-variant($red, darken($red, 7.5%), $color-text-01, $red, lighten($red, 75%), $color-text-01, $red, darken($red, 30%));
  border: none;

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $red;
    border: 1px solid $red !important;
  }

  &.btn-icon {
    color: $red;
  }
}



.btn-blue-outline {
  @include button-outline-variant($brand-blue);

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  &:not(:disabled):not(.disabled):focus {
    background-color: $brand-blue-pressed;
    border: 1px solid $brand-blue-pressed !important;
    color: $button-color-pressed;
  }

  &:disabled,
  &.disabled {
    background-color: $button-background-outline-disabled !important;
    box-shadow: none !important;
    border: $button-border-outline-disabled !important;
    color: $button-color-outline-disabled !important;
  }

  &.btn-icon {
    color: $brand-blue;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled).active,
    &:not(:disabled):not(.disabled):hover,
    &:not(:disabled):not(.disabled):focus {
      color: $button-color-pressed;
    }
  }
}
