.check-on-off {
    position: relative;
    height: 19px;
    input {
        cursor: pointer;
        -webkit-appearance: none;
        appearance: none;
        background-color: $check-on-off-background-color;
        width: 30px;
        height: 18px;
        border-radius: 10px;
        border: 2px solid $check-on-off-border-color;
        &:focus {
            outline: none;
        }
        &::after {
            content: '';
            margin-top: -2px;
            display: block;
            width: 18px;
            height: 18px;
            background-color: $check-on-off-background-color-off;
            border-radius: 50%;
            position: absolute;
            transition: left .3s ease-out;
            -webkit-transition: left .3s ease-out;
            right: 11px;
        }
    }
    &:not(:disabled):not(.disabled)input.left:checked {
        border-color: $check-on-off-background-color-on;
        &::after {
            left: 11px;
            background: $check-on-off-background-color-on;
        }
    }
    &:not(:disabled):not(.disabled)input.right:checked {
        border-color: $check-on-off-background-color-on;
        &::after {
            right: 1px;
            background: $check-on-off-background-color-on;
        }
    }
    span {
        position: relative;
        padding-left: 7px;
        padding-right: 7px;
        cursor: pointer;
        line-height: 18px;
        top: -3px;
    }
    &.disabled,
    &:disabled {
        span {
            cursor: default;
            color: $disabled-color;
            &::after {
                border-color: $disabled-color;
            }
        }
        input {
            cursor: default;
            border-color: $disabled-color;
            &::after {
                background: $disabled-color;
            }
        }
    }
}
