.checkbox {
    input {
        display: none
    }
    span {
        font-size: 1rem;
        color: $input-color;
        position: relative;
        padding-left: 30px;
        cursor: pointer;
        display: inline-block;
      &.no-text {
        padding-left: 24px !important;
      }
      &.left {
          padding-left: 0px !important;
      }
    }
    span:before {
        content: "";
        position: absolute;
        top: 0.275rem;
        left: 0.1875rem;
        width: 18px;
        height: 18px;
        border: $checkbox-border;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
    }
    span {
        &.left::before {
            left: 4.1875rem !important;
        }
    }
    &:not(:disabled):not(.disabled)input[type=checkbox]:checked+span:after {
        content: "\f00c";
        font-family: 'Font Awesome 6 Pro', serif;
        background: $checkbox-background-color-checked;
        color: $checkbox-color-checked;
        position: absolute;
        top: 0.275rem;
        left: 0.1875rem;
        width: 18px;
        height: 18px;
        font-size: 0.875rem;
        font-weight: 900;
        padding-left: 0.125rem;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        line-height: 20px;
    }
    &:not(:disabled):not(.disabled)input[type=checkbox]:checked+span {
        &.left::after {
            left: 4.1875rem !important;
        }
    }

    &.disabled,
    &:disabled {
        span {
            cursor: default;
            color: $disabled-color;
            &::before {
                border-color: $disabled-color;
                background-color: $disabled-color;
            }
        }
        input {
            display: none;
        }
        input[type=checkbox]:checked+span:before {
          content: "\f00c";
          font-family: 'Font Awesome 6 Pro', serif;
          color: $checkbox-color-checked;
          position: absolute;
          top: 0.275rem;
          left: 0.1875rem;
          width: 18px;
          height: 18px;
          font-size: 0.875rem;
          font-weight: 900;
          padding-left: 0.125rem;
          -webkit-border-radius: 3px;
          -moz-border-radius: 3px;
          border-radius: 3px;
          background: $disabled-color;
        }
    }

    &:not(:disabled):not(.disabled)input[type=checkbox]:checked+span.type-2:after {
        content: "\f0c8";
        background: transparent;
        color: $checkbox-background-color-checked;
        width: 14px;
        height: 14px;
        padding: 0;
        top: 3px;
        left: 6px;
    }
}
