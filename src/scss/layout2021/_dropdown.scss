.dropdown {
    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled):focus-within {
        border-color: $dropdown-border-color !important;
        outline: 0;
        input {
            border-color: $dropdown-border-active-color !important;
        }
    }
    input:active,
    input.active,
    input:focus {
        border-color: $dropdown-border-active-color !important;
    }
    &.disabled {
        border-color: $dropdown-border-color !important;
        outline: 1px solid transparent !important;
        color: $disabled-color;
        input::placeholder,
        .dropdown-icon::after {
            color: $disabled-color;
        }
        input {
            pointer-events: none;
        }
    }

    .form-control {
      &[readonly] {
        background-color: $input-bg;
        border-color: $dropdown-border-color !important;
      }
    }
    .caret-off{
        &::after{ display: none;}
    }
    &.show {
        .dropdown-icon::after {
            content: '\f077';
        }
    }
    .dropdown-toggle::after {
        display: none;
    }
}

.dropdown-icon {
    font-weight: 900;
    font-family: "Font Awesome 6 Pro";
    transition: all .2s;
    position: absolute;
    width: 30px;
    background: transparent;
    right: 2px;
    top: 2px;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 4px 4px 0;
    border: 0;
    &:active {
        background: transparent;
    }
    &::after {
        content: '\f078';
        color: $dropdown-icon-color;
    }
}

.dropdown-menu {
    box-shadow: 0px 15px 30px #0F2C3519;
}

.dropdown-item {
    &:hover {
        background: $dropdown-item-background;
        color: $dropdown-link-hover-color;
    }
    &.active,
    &:active {
      background-color: $dropdown-link-active-background;
      color: $dropdown-link-active-color;
      &:hover {
        background: $dropdown-item-background;
        color: $dropdown-link-hover-color;
      }
    }
    cursor: pointer;
}

:not(ngb-datepicker).dropdown-menu.show {
  max-height: 40vh;
  overflow-y: auto;
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  box-sizing:border-box;
  z-index: 9999;
}
