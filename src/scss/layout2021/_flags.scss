/**
  Generate sprite image using https://www.toptal.com/developers/css/sprite-generator
 */
$countries: 'ap', 'bx', 'cs', 'dd', 'ea', 'em', 'ep', 'gc', 'oa', 'su', 'upc', 'wo', 'yu';

$sizes:
  'sm' 12px 19.59px,
  'sm-2' 14px 22.2px,
  'md' 16px 24.79px,
  'lg' 24px 35.2px,
  'xl' 28px 40.4px;

@each $code in $countries {
  .fi-#{$code} {
    background-image: url('/assets/images/flag/flags.png') !important;
    background-repeat: no-repeat;
    background-position: 0 ((1 - index($countries, $code)) * 18px);
    background-size: 24px (length($countries) * 18px);

    @each $size, $height, $width in $sizes {
      &.fi-flag-#{$size} {
        background-position: 0 ((1 - index($countries, $code)) * $height);
        background-size: $width (length($countries) * $height) !important;
      }
    }
  }
}
