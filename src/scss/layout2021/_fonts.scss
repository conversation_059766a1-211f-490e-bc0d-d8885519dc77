@font-face {
    font-family: 'Open Sans Regular';
    src: url('/assets/fonts/OpenSans/OpenSans-Regular.ttf');
}

@font-face {
    font-family: 'Open Sans Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-Bold.ttf');
}

@font-face {
    font-family: 'Open Sans Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-BoldItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Extra Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-ExtraBold.ttf');
}

@font-face {
    font-family: 'Open Sans Extra Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-ExtraBoldItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-Italic.ttf');
}

@font-face {
    font-family: 'Open Sans Light';
    src: url('/assets/fonts/OpenSans/OpenSans-Light.ttf');
}

@font-face {
    font-family: 'Open Sans Light Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-LightItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Semi Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-SemiBold.ttf');
}

@font-face {
    font-family: 'Open Sans Semi Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-SemiBoldItalic.ttf');
}

@font-face {
    font-family: 'Lato';
    src: url('/assets/fonts/Lato/Lato-Regular.ttf');
}

@font-face {
    font-family: 'Open Sans Regular';
    src: url('/assets/fonts/OpenSans/OpenSans-Regular.ttf');
}

@font-face {
    font-family: 'Open Sans Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-Bold.ttf');
}

@font-face {
    font-family: 'Open Sans Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-BoldItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Extra Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-ExtraBold.ttf');
}

@font-face {
    font-family: 'Open Sans Extra Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-ExtraBoldItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-Italic.ttf');
}

@font-face {
    font-family: 'Open Sans Light';
    src: url('/assets/fonts/OpenSans/OpenSans-Light.ttf');
}

@font-face {
    font-family: 'Open Sans Light Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-LightItalic.ttf');
}

@font-face {
    font-family: 'Open Sans Semi Bold';
    src: url('/assets/fonts/OpenSans/OpenSans-SemiBold.ttf');
}

@font-face {
    font-family: 'Open Sans Semi Bold Italic';
    src: url('/assets/fonts/OpenSans/OpenSans-SemiBoldItalic.ttf');
}

@font-face {
    font-family: 'Lato';
    src: url('/assets/fonts/Lato/Lato-Regular.ttf');
}

@font-face {
  font-family: "Courier Prime Bold Italic";
  src: url("/assets/fonts/CourierPrime/CourierPrime-BoldItalic.ttf");
}

@font-face {
  font-family: "Courier Prime Bold";
  src: url("/assets/fonts/CourierPrime/CourierPrime-Bold.ttf");
}

@font-face {
  font-family: "Courier Prime Italic";
  src: url("/assets/fonts/CourierPrime/CourierPrime-Italic.ttf");
}

@font-face {
  font-family: "Courier Prime Regular";
  src: url("/assets/fonts/CourierPrime/CourierPrime-Regular.ttf");
}

.font{
  &-open{
    &-sans{
      &-bold{
        font-family: $font-open-sans-bold;
      }
      &-semi-bold{
        font-family: $font-open-sans-semi-bold;
      }
      &-regular{
        font-family: $font-open-sans-regular;
      }
      &-light{
        font-family: $font-open-sans-light;
      }
    }
  }
}
