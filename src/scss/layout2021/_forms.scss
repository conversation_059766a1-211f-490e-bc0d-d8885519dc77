/* form-control */

.form-control {
    &.is-invalid {
        color: $invalid-color !important;
        border-color: $invalid-color !important;
        background-image: none;
    }
    &:focus,
    &.is-invalid:focus {
        box-shadow: none;
    }

    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus {
      font-family: $input-font-family !important;
      @include font-size($input-font-size);
    }

    &:-webkit-autofill,
    &:-webkit-autofill::first-line {
      height: $input-height;
      font-family: $input-font-family !important;
      @include font-size($input-font-size);
      font-weight: $input-font-weight !important;
      line-height: $input-line-height !important;
      color: $input-color !important;
      text-shadow: 0 0 0 $input-color !important;
    }

  @keyframes autofill {
    100% {
      height: $input-height;
      font-family: $input-font-family !important;
      @include font-size($input-font-size);
      font-weight: $input-font-weight !important;
      line-height: $input-line-height !important;
      color: $input-color !important;
      text-shadow: 0 0 0 $input-color !important;
    }
  }

  @-webkit-keyframes autofill {
    100% {
      height: $input-height;
      font-family: $input-font-family !important;
      @include font-size($input-font-size);
      font-weight: $input-font-weight !important;
      line-height: $input-line-height !important;
      color: $input-color !important;
      text-shadow: 0 0 0 $input-color !important;
    }
  }
}

.invalid-feedback {
    color: $invalid-color;
}


/* input */

input::placeholder {
    font-size: $placeholder-size;
}


/* control-label */

.control-label {
    color: $label-color;
    font-size: $label-size;
    padding-bottom: 0px;
    margin-bottom: 0px;
}

.required .control-label:after {
    margin-left: 3px;
    content: '*';
    color: $red;
}

.form-check-input {
  &.is-invalid ~ .form-check-label {
    color: $invalid-color;
  }
}

/* ng-select-start */
.ng-value-container {
  input {
    height: 28px;
  }

  .ng-value {
    margin: 0 5px;
    font-family: $font-open-sans-regular;
    font-size: $button-size !important;
    line-height: 24px;
    background-color: $brand-green !important;
    color: $color-text-01 !important;
    overflow: hidden;

    .ng-tag-value{
      margin: 0;
      &.invalid,
      &.invalid:hover,
      &.invalid:focus,
      &.invalid:active {
        background-color: $alert-danger-text !important;
      }
    }

    &:hover,
    &:focus {
      background-color: $brand-green-hover !important;
    }

    &:active {
      background-color: $brand-green-pressed !important;
    }

    border-radius: 24px !important;
  }

  .ng-value-label {
    padding: 0 5px;
    cursor: pointer;
  }

  .ng-value-icon {
    padding: 0 8px;
    border-radius: 24px;
    display: inline-block;
    float: right;
  }

  .ng-value-icon-right {
    padding-left: 5px !important;
  }
}

.ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container {
  border-color: #389A85;
  box-shadow: none;
}

.ng-select .ng-has-value .ng-placeholder {
  display: none;
}

/* ng-select-end */
