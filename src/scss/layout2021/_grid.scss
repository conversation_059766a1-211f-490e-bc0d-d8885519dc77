.col {
    @include media-breakpoint-up(xs) {
        min-width: $grid-sm-size;
        padding: 0 7px;
    }
    @include media-breakpoint-up(md) {
        min-width: $grid-md-size;
        padding: 0 15px;
    }
    @include media-breakpoint-up(lg) {
        min-width: $grid-lg-size;
    }
}

@for $i from 1 through $grid-columns {
    .col-#{$i} {
        @include media-breakpoint-up(xs) {
            min-width: $grid-sm-size * $i;
            @if $i>=$columns-sm {
                min-width: 100%;
            }
            padding: 0 7px;
        }
        @include media-breakpoint-up(md) {
            min-width: $grid-md-size * $i;
            @if $i>=$columns-md {
                min-width: 100%;
            }
            padding: 0 15px;
        }
        @include media-breakpoint-up(lg) {
            min-width: $grid-lg-size * $i;
        }
    }
}

$border-shadow: 0px 5px 15px #0F2C353F;

@media screen and (max-width: 1600px) and (min-width: 1200px) {
  .container-fluid {
    width: 1170px;
    max-width: none;
  }
}

@media screen and (max-width: 1199px) and (min-width: 992px) {
  .container-fluid {
    width: 950px;
    max-width: none;
  }
}

.container-fluid {
  width:100%;
  max-width: 1500px;
  padding-right: 15px;
  padding-left: 15px;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  margin-right: auto;
  margin-left: auto;
}
