
.highcharts-tooltip > span {
  opacity: 1;
  z-index: 9999 !important;
  background: #F5F7FAFF !important;
  padding: 8px !important;
  border-radius: 3px;
}

.ipc-cpc-tooltip {
  &.show {
    opacity: 1;
  }

  .tooltip-inner {
    max-width: 500px;
    max-height: 500px;
    overflow-y: auto;
    background-color: #FFFEFD !important;
    color: #434343 !important;
    box-shadow: 0px 10px 15px -3px rgba(40, 40, 40, 0.10), 0px 0px 6px 0px rgba(40, 40, 40, 0.25) !important;
    opacity: 1;

    ul {
      &:first-child {
        margin-inline: 0;
        padding-inline: 20px;
      }

      &:not(:first-child) {
        margin-inline: 0;
        padding-inline: 10px;
      }
    }

    li {
      list-style-type: square !important;
      text-align: left;
      margin-inline: 5px;
      padding-inline: 5px;
    }
  }

  &.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #FFFEFD !important;
  }

  &.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #FFFEFD !important;
  }

  &.bs-tooltip-left, &.bs-tooltip-start {
    .tooltip-arrow::before {
      border-left-color: #FFFEFD !important;
    }
  }

  &.bs-tooltip-right, &.bs-tooltip-end {
    .tooltip-arrow::before {
      border-right-color: #FFFEFD !important;
    }
  }
}
