.menu {
    &.navbar-collapse {
        display: flex !important;
        flex-basis: auto;
        flex-grow: 1;
        align-items: center;
    }
    .navbar-nav {
        flex-direction: row;
        display: flex;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
        a {
            &:not(:disabled):not(.disabled):hover {
                color: $brand-orange;
            }
            &:not(:disabled):not(.disabled):focus,
            &:not(:disabled):not(.disabled).active,
            &:not(:disabled):not(.disabled):active {
                color: $brand-orange-pressed;
            }
            font-size: $menu-size;
            color: $menu-color;
            margin-left: 20px;
            text-decoration: none !important;
            &:disabled,
            &.disabled {
                color: $disabled-color;
                pointer-events: none;
            }
        }
    }
}
