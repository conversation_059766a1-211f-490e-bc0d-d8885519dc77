@mixin placeholder {
  &::-webkit-input-placeholder {
    @content
  }
  &:-moz-placeholder {
    @content
  }
  &::-moz-placeholder {
    @content
  }
  &:-ms-input-placeholder {
    @content
  }
}

@mixin box-shadow($value) {
  box-shadow: $value;
  -webkit-box-shadow: $value;
  -moz-box-shadow: $value;
}

@mixin transition($property, $duration, $easing: linear) {
  transition: $property $duration $easing;
  -webkit-transition: $property $duration $easing;
  -moz-transition: $property $duration $easing;
}

@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  border-radius: $radius;
}

@mixin border-radii($top-left:10px, $top-right:null, $bottom-right:null, $bottom-left:null){
  -webkit-border-radius: $top-left $top-right $bottom-right $bottom-left;
  -moz-border-radius: $top-left $top-right $bottom-right $bottom-left;
  -ms-border-radius: $top-left $top-right $bottom-right $bottom-left;
  border-radius: $top-left $top-right $bottom-right $bottom-left;
}

@mixin border-top-left-radius($radius) {
  -webkit-border-top-left-radius: $radius;
  -moz-border-top-left-radius: $radius;
  -ms-border-top-left-radius: $radius;
  border-top-left-radius: $radius;
}

@mixin border-top-right-radius($radius) {
  -webkit-border-top-right-radius: $radius;
  -moz-border-top-right-radius: $radius;
  -ms-border-top-right-radius: $radius;
  border-top-right-radius: $radius;
}

@mixin border-bottom-left-radius($radius) {
  -webkit-border-bottom-left-radius: $radius;
  -moz-border-bottom-left-radius: $radius;
  -ms-border-bottom-left-radius: $radius;
  border-bottom-left-radius: $radius;
}

@mixin border-bottom-right-radius($radius) {
  -webkit-border-bottom-right-radius: $radius;
  -moz-border-bottom-right-radius: $radius;
  -ms-border-bottom-right-radius: $radius;
  border-bottom-right-radius: $radius;
}

@mixin border-top-radius($radius) {
  @include border-top-left-radius($radius);
  @include border-top-right-radius($radius);
}

@mixin border-right-radius($radius) {
  @include border-top-right-radius($radius);
  @include border-bottom-right-radius($radius);
}

@mixin border-bottom-radius($radius) {
  @include border-bottom-left-radius($radius);
  @include border-bottom-right-radius($radius);
}

@mixin border-left-radius($radius) {
  @include border-top-left-radius($radius);
  @include border-bottom-left-radius($radius);
}

@mixin outline-color($color) {
  -moz-outline-color: $color;
  -webkit-outline-color: $color;
  -ms-outline-color: $color;
  outline-color: $color;
}

@mixin keyframes($name) {
  @keyframes #{$name} {
    @content;
  }

  /* Firefox < 16 */
  @-moz-keyframes #{$name} {
    @content;
  }

  /* Safari, Chrome and Opera > 12.1 */
  @-webkit-keyframes #{$name} {
    @content;
  }

  /* Internet Explorer */
  @-ms-keyframes #{$name} {
    @content;
  }
}

@mixin animate($animation, $duration, $method, $times) {
  -webkit-animation: $animation $duration $method $times; /* Safari, Chrome and Opera > 12.1 */
  -moz-animation: $animation $duration $method $times; /* Firefox < 16 */
  -ms-animation: $animation $duration $method $times; /* Internet Explorer */
  -o-animation: $animation $duration $method $times; /* Opera < 12.1 */
  animation: $animation $duration $method $times;
}

@mixin animate-fade-in($time: 0.5s) {
  @include keyframes(fade-in) {
    from { opacity: 0; }
    to   { opacity: 1; }
  }

  @include animate(fade-in, $time, linear, 1);
}

@mixin none-select() {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
