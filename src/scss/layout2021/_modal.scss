.modal{ z-index: 1055;}
.modal-header {
  border-bottom: none;
  border-bottom: none;
  padding: 0;
  margin: 0;
  min-height: 55px;

  .modal-title {
    font-family: $modal-title-family;
    font-size: $modal-title-size;
    width: 100%;
    padding: 45px $modal-body-padding $modal-body-padding $modal-body-padding;
    text-align: center;
  }

  .close {
    position: absolute;
    right: 15px;
    top: 15px;
    margin: 0;
    padding: 0;
    width: 24px;
    height: 24px;
    border: none;
    border-color: #FFFFFF;
    background: url('/assets/images/icon-outline-remove_circle-blue.svg') no-repeat top right;
  }
}

.modal-footer {
  background-color: $modal-footer-background;
  border-radius: 0 0 6px 6px;
  .modal-hint {
    font-size: 0.75rem;
    color: #0F2C35;
  }
  img {
    height: 40px;
  }
}

.right-side-modal {

  .modal-dialog {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh !important;
    max-height: 100vh !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .modal-content {
    width: 30vw;
    position: fixed;
    z-index: 1;
    top: 0;
    right: 0;
    height: 100vh;
    margin: 0;
    padding: 0;
    max-height: 100vh;
  }

  .modal-body {
    overflow-y: hidden !important;
    padding: 0 0 0 15px;
    margin: 0;

    &.modal-has-footer {
      height: calc(100vh - 100px);
    }

    &.modal-no-footer {
      height: 100vh;
    }
  }
}

.modal-pdf-viewer {
  .modal-content {
    width: 80vw;
    height: 90vh;
    position: fixed;
    z-index: 1;
    top: calc(10vh / 2);
    right: calc(20vw / 2);
    margin: 0;
    padding: 0;
    max-height: 90vh;

    app-pdf-viewer-dialog {
      height: 100%;
      display: flex !important;
      flex-direction: column !important;

      .modal-body {
        padding: 0;
        margin: 0 0 3px 0;
      }

      iframe {
        width: 100%;
      }
    }
  }
}

.translate-language-dialog {
  .modal-content {
    width: 39.188rem;
  }
}
