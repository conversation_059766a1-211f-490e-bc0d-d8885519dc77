.search-result-bar {
  background-color: $chart-section-background;
  border-bottom: 1px solid $input-border-color;
  border-top: 1px solid $input-border-color;
  .nav {
    min-height: 52px;
    background-color: transparent;
    margin-bottom: 0;
  }
  .nav-tabs {
    border: none;
    .nav-link {
      padding: 0.8rem 0.8rem 0.8rem 38px;
      &.no-icon{ padding-left: 0.8rem;}
      color: $brand-dark;
      border-radius: 0px;
      font-family: 'Open Sans Semi Bold';
      background-repeat: no-repeat;
      border-bottom: 1px solid transparent;
      border-top: 3px solid transparent;
      background-position: 15px;
      background-size: 15px;
      transition: all ease .2s;
      &.active, &:hover {
        border-bottom-color: white;
        border-top-color: $brand-green;
        color: $brand-green;
        background-color: #FFF;
      }
    }
  }

  .ms-auto {
    margin-left: auto!important;
  }
  .list-nav-icon{
    background-image: url('/assets/images/layout2022/icon-tab-list.svg');
    &.active, &:hover{
      background-image: url('/assets/images/layout2022/icon-tab-list-hover.svg');
    }
  }
  .visual-analysis-nav-icon{
    background-image: url('/assets/images/layout2022/icon-tab-visual-analysis.svg');
    &.active, &:hover{
      background-image: url('/assets/images/layout2022/icon-tab-visual-analysis-hover.svg');
    }
  }
  .combined-nav-icon{
    background-image: url('/assets/images/layout2022/icon-tab-combined.svg');
    &.active, &:hover{
      background-image: url('/assets/images/layout2022/icon-tab-combined-hover.svg');
    }
  }
  .competitive-nav-icon{
    &:before{
      font-family: "Font Awesome 6 Pro";
      font-weight: 300;
      content: "\e522";
      box-sizing: border-box;
    }    
  }

}

.psr-control-bar, .mr-control-bar, .lcp-control-bar, .csr-control-bar, .bsr-control-bar, .me-control-bar {
  background-color: #FFF;
  border-bottom: 1px solid $input-border-color;
}

.search-results-container { background-color: #f8fbfcb5; }
