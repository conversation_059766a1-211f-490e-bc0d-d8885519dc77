.nav {
  margin-bottom: 1rem;
  background-color: $nav-background-color;
  padding: 0 1rem;

  &-1{
    border-bottom: none !important;
    .nav-link {
        color: $brand-green;
        background-color: unset !important;
        border-top: none !important;
        border-left: none !important;
        border-right: none !important;
      }
  }
  &-2 {
    background-color: unset;
    padding-left: 0;
    margin-bottom: 0;

    >li {
      margin-right: 24px;
    }

    .nav-link {
      color: $color-text-04;
      font-family: $font-open-sans-bold;
      border-top: none !important;
      border-left: none !important;
      border-right: none !important;

      &:not(:disabled):not(.disabled).active {
        border-bottom-color: $link-color;
        color: $link-color;
      }
    }
  }

  min-height: 58px;
}

.nav-item {
  padding: $nav-link-padding-y $nav-link-padding-x 0 $nav-link-padding-y;
  cursor: pointer;
}

.nav-link {
    padding: $nav-link-padding-y 0;
    padding-bottom: 1rem;
    cursor: pointer;
    &:hover  {
        border-bottom: solid 2px $link-hover-color;
    }
    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled):focus {
        color: $brand-green-pressed;
        border-bottom: solid 2px $brand-green-pressed;
    }
    &:not(:disabled):not(.disabled).active {
        color: $brand-green-selected;
        border-bottom: solid 2px $brand-green-selected;
    }
}

.bg-gray {
  background-color: $nav-background-color;
}
