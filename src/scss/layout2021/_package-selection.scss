.package-selection {
    &:not(:disabled):not(.disabled)input {
        display: none;
        &:checked {
            +div {
                background: $brand-orange-selected;
                border-color: $brand-orange;
                &:hover {
                    border-width: 2px;
                }
            }
        }
    }
    div {
        width: $package-width;
        height: $package-height;
        border: $package-border;
        background: $package-background;
        margin: 8px;
        padding: 8px;
        outline: none;
    }
    &:not(:disabled):not(.disabled)div:hover {
        border: 2px solid $brand-green-hover;
    }
    &.disabled,
    &:disabled {
        div {
            cursor: default;
            color: $disabled-color;
            border-color: $disabled-color;
        }
        input {
            display: none;
        }
        pointer-events: none;
    }
}
