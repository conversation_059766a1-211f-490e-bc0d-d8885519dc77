pagination-template .ngx-pagination, .pagination {
    display: inline-block;
    padding: 0;
    margin: 20px 0;
    >li {
        display: inline-block;
        font-size: $pagination-size;
        font-weight: bold;
        cursor: pointer;
        >a {
            position: relative;
            float: left;
            padding-top: 9px;
            margin-left: -1px;
            line-height: 1.42857143;
            color: $pagination-color !important;
            background-color: $pagination-background;
            border: none !important;
            width: 40px;
            height: 40px;
            text-decoration: none;
            text-align: center;
            &:hover {
                outline: none !important;
                color: $pagination-color-hover !important;
                background-color: $pagination-background-hover;
            }
            &:focus,
            &:active {
                color: $pagination-color-focus !important;
                background-color: $pagination-background-focus !important;
            }
        }
        >span {
            float: left;
            padding-top: 9px;
            margin-left: -1px;
            line-height: 1.42857143;
            width: 40px;
            height: 40px;
            text-align: center;
        }
        &:last-of-type {
          margin-right: 0;
        }
    }
    .current {
        color: $pagination-color;
        background-color: $pagination-current-background;
        padding: 0;
        >span {
            color: $pagination-current-color;
            background-color: $pagination-current-background;
            width: 40px;
            height: 40px;
            text-align: center;
            padding-top: 9px;
            &:hover {
                outline: none !important;
                color: $pagination-color-hover;
                background-color: $pagination-background-hover;
            }
            &:focus,
            &:active {
                color: $pagination-color-focus !important;
                background-color: $pagination-background-focus !important;
            }
        }
    }
}
