.radio-card {
    &:not(:disabled):not(.disabled)input {
        display: none;
        &:checked {
            +div {
                background: $brand-green;
                color: #FFF;
            }
        }
    }
    div {
        width: 150px;
        height: 156px;
        border: 1px solid $border-default;
        color: $brand-green;
        background: #FFF;
        margin: 8px;
        padding: 8px;
        outline: none;
    }
    &:not(:disabled):not(.disabled)div:hover {
        border: 2px solid $brand-green;
    }
    &.disabled,
    &:disabled {
        div {
            cursor: default;
            color: $disabled-color;
            border-color: $disabled-color;
        }
        input {
            display: none;
        }
        pointer-events: none;
    }
}
