.radio-button {
    font-size: 1rem;
    margin-bottom: 5px;
    display: block;
    &:not(:disabled):not(.disabled)input {
        display: none;
        &[type="radio"]:checked {
            +span {
                &::before {
                    color: #389A85;
                }
                &::after {
                    content: "";
                    font-family: Fontawesome;
                    background: #389a85;
                    position: absolute;
                    top: 6px;
                    left: 4px;
                    width: 10px;
                    height: 10px;
                    -webkit-border-radius: 50%;
                    -moz-border-radius: 50%;
                    border-radius: 50%;
                }
            }
            &~span {
                &::before {
                    border-color: #389a85;
                }
            }
        }
    }
    span {
        position: relative;
        padding-left: 25px;
        cursor: pointer;
        line-height: 18px;
        &::before {
            content: "";
            position: absolute;
            top: 2px;
            left: 0;
            width: 18px;
            height: 18px;
            border: 1px solid #666;
            -webkit-border-radius: 50%;
            -moz-border-radius: 50%;
            border-radius: 50%;
        }
    }
    &.disabled,
    &:disabled {
        span {
            cursor: default;
            color: $disabled-color;
            &::before {
                border-color: $disabled-color;
            }
        }
        input {
            display: none;
        }
    }
}
