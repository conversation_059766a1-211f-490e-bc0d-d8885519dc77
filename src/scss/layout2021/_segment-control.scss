.segment-control {
    padding: 40px 0;
    margin: 0 auto;
    font-size: $segment-control-size;
    li {
        display: inline-block;
        text-align: center;
        a {
            color: $segment-control-color !important;
            text-decoration: none !important;
            display: inline-block;
            width: 100%;
            padding: 10px;
        }
        cursor: pointer;
        &:first-child {
            border-left: $segment-control-border;
        }
        border-top: $segment-control-border;
        border-bottom: $segment-control-border;
        border-right: $segment-control-border;
        &:not(:disabled):not(.disabled):hover {
            background: $segment-control-background-hover;
            a {
                color: $segment-control-color-hover !important;
            }
        }
        &:not(:disabled):not(.disabled).active {
            background: $segment-control-background-active;
            a {
                color: $segment-control-color-active !important;
            }
        }
        &:not(:disabled):not(.disabled):active,
        &:not(:disabled):not(.disabled):focus-within {
            background-color: $segment-control-background-focus;
            a {
                color: $segment-control-color-focus !important;
            }
        }
        &:disabled,
        &.disabled {
            border-color: $disabled-color;
            a {
                color: $disabled-color !important;
            }
        }
    }
}
