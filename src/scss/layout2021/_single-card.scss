@import 'scss/figma2023/variables';

.cards-wrap {
  display: -ms-flex;
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.single-card {
  position: relative;
  border: 1px solid $colours-border-subtle;
  border-radius: $radius-sm;
  background: $single-card-background-color;
  width: calc(25% - 30px);
  margin: 15px;
  padding-bottom: 47px;

  .single-card-header,
  .single-card-body,
  .single-card-footer {
    padding: 20px;
  }

  .single-card-header {
    top: 0;

    .sc-checkbox-container {
      .checkbox:not(.checked) {
        visibility: hidden;
      }
    }
  }

  .single-card-body {
    width: 100%;
    padding-top: 0;
    padding-bottom: 10px;

    .single-card-text {
      color: $single-card-body-text-color;
      font-size: 1rem;
    }

    .single-card-desc {
      color: $single-card-body-text-color;
      font-size: 0.75rem;
      margin-bottom: 0.5rem;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }

  .single-card-footer {
    background: $single-card-footer-background;
    position: absolute;
    bottom: 0;
    width: 100%;
    height: $single-card-footer-height;
    font-family: $font-open-sans-bold;
    font-size: 12px;
    color: $color-text-02;
    padding-top: 15px;
    padding-bottom: 15px;
    border-bottom-left-radius: $radius-sm;
    border-bottom-right-radius: $radius-sm;
  }

  .card-title {
    font-family: $font-open-sans-bold;
    color: $brand-green;
    font-size: 18px;
    width: calc(100% - 30px);
    max-height: 45px;
    padding-right: 30px;
    line-height: 22px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none !important;
    margin-bottom: 0;
    white-space: initial;
    overflow-wrap: break-word;
  }

  .icon {
    display: inline-block;
    cursor: pointer;
    position: absolute;
    color: $single-card-icon-color;
    top: 3px;
    right: 5px;
    padding: 2px 11px;
    border-radius: 50%;
    transition:.3s ease all;
    border: $single-card-icon-border-hover;
    border-color: $single-card-background-color;

    &:hover {
      border-color: $single-card-icon-color;
    }

    &:focus,
    &:active {
      color: $single-card-icon-color-focus;
      background: $single-card-icon-background;
    }
  }

  .dropdown.abs {
    top: 17px;
    outline: unset !important;
    right: 15px;
    position: absolute;

    &:not(:disabled):not(.disabled):active,
    &:not(:disabled):not(.disabled):focus-within{
      outline: none !important;
    }

    .dropdown-toggle::after {
      display: none;
    }

    .dropdown-menu {
      min-width: 151px;
      border: $single-card-border;
      a {
        padding: 8px;
        color: #273239;
        font-size: 14px;
        cursor: pointer;
        &:hover{
            text-decoration: none;
        }

        i {
          color: $brand-gray;
          margin-right: 8px;

          &.fa-trash {
            color: $red;
          }
        }
      }

      li{
        transition: .3s background-color;
        &:last-child {
          border-top: 1px solid $brand-gray;
        }
      }
    }
  }

  &:hover {
    border-color: $colours-border-bold;

    .single-card-header {
      top: 0;

      .sc-checkbox-container {
        .checkbox {
          visibility: visible;
        }
      }
    }
  }
}
.landscape-patent-stats {
  p {
    font-family: $font-open-sans-bold;
    font-size: 12px;
    text-transform: capitalize;
    color: #273239;
    margin-bottom: 0px;

    span {
      &:first-child {
        min-height: 34px;
        display: block;
        margin-bottom: 2px;
        color: $color-text-02;
      }

      &:last-child {
        font-family: $font-open-sans-bold;
        font-size: 18px;
        text-transform: uppercase;
        color: $brand-orange;
        margin-bottom: 0px;
        display: block;
      }
    }
  }
}

.card-nav-bar {
  border: 1px solid #F2F2F2;
  background: #F9F9F9;
  -webkit-border-radius: 6px 6px 0px 0px;
  border-radius: 6px 6px 0px 0px;
  margin-bottom: 25px;
  padding: 15px 30px;

  .filter-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  a.sort-order {
    color: inherit;
    &.inactive{
      color: $color-text-02;
    }
  }

  .search-box {
    display: inline-block;
  }

  .filter-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: calc(100% - 417px);

    select, input{
      height: 48px;
      border-color: #999999;
    }
    select {
      font-family: $font-open-sans-regular;
      max-width: 221px;
      margin-right: 12px;
      margin-left: 10px;
      color: #273239;
      font-size: 13px;
      text-transform: uppercase;
      -webkit-appearance: none;
      background-image: url(/assets/images/round-chevron_right-24px.png) !important;
      background-repeat: no-repeat !important;
      background-position: 97% center !important;
      padding-right: 24px;
    }
  }

  .filter-right {
    justify-content: flex-end;
    font-size: 13px;

    a,
    span {
      margin-left: 10px;
      vertical-align: middle;
    }
  }

  .count-stat {
    color: #999999;
  }
}


.info-cards-wrap {
  margin: 0px -15px;
  flex-wrap: wrap;
}

.single-info-card {
  padding: 0px 15px;
  width: 25%;
  margin-bottom: 30px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  .content {
    -webkit-border-radius: 3px;
    border-radius: 3px;
    padding: 30px 35px;
    background: #FBFBFB;
    border: 1px solid #ECECEC;
    height: 100%;
  }
  .info-title, .info-description{
    font-family: $font-open-sans-bold;
    word-break: break-word;
    text-transform: capitalize;
  }
  .info-title {
    margin-bottom: 15px;
    font-size: 16px;
  }
  .info-description{
    color: $brand-orange;
    font-size: 20px;
  }
}

