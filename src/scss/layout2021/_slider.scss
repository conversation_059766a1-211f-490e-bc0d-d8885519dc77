.ngx-slider {
  .ngx-slider-pointer {
    cursor: pointer;
    width: 18px !important;
    height: 18px !important;
    top: -7px !important;
    background: $slider-background !important;
    z-index: 3;
    border-radius: 16px;
    outline: none;

    &::after {
      width: 12px !important;
      height: 12px !important;
      position: absolute;
      top: 3px !important;
      left: 3px !important;
      border-radius: 6px !important;
      background: $slider-circle !important;
    }

    &:hover::after {
      background: $slider-circle-hover !important;
      box-shadow: 0px 3px 6px #00000026;
    }

    &:focus::after {
      width: 10px !important;
      height: 10px !important;
      top: 4px !important;
      left: 4px !important;
      background: $slider-circle-focus !important;
    }
  }

  .ngx-slider-tick {
    left: 4px !important;
    right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .ngx-slider-bar-wrapper{
    margin-top: -12px;
    margin-bottom: 12px;
    height: 24px;
  }
}

.chart-slider {
  .label-slider {
    color: $color-text-01 !important;
  }

  .ngx-slider {

    .ngx-slider-limit{ display: none; }
    .ngx-slider-bubble {
      color: $color-text-01 !important;
      font-size: .750rem;
      padding-top: 0;
      padding-bottom: 0;
    }
    .ngx-slider-model-value, .ngx-slider-model-high {
      color: $slider-circle !important;
    }
  }
}
