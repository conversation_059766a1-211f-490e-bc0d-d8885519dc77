@import "scss/layout2021/variables";

.highlight-container {
  box-shadow: 0 5px 15px #00000029;
  padding: 5px 5px 5px 10px;
  position: relative;

  .highlight-box-container {
    margin-top: 7px;
    margin-right: 11px;

    .highlight-box {
      width: 9px;
      height: 9px;
      outline: 1px solid #fff;

      &:not(:last-child) {
        border-bottom: 1px solid white;
      }

      &.highlight-none {
        background-color: #F2F2F2;
      }

      @for $i from 1 through 5 {
        &.highlight-#{$i} {
          @if $i == 5 {
            background-color: #FF6700 !important;
          } @else {
            background-color: #FF6700#{$i * 20} !important;
          }
        }
      }
    }
  }

  .highlight-information {
    position: absolute;
    top: -34px;
    left: 0;
    padding: 5px;
    background: #FF6700;
    border-radius: 3px 3px 0 0;
    font: normal normal 600 12px/24px Open Sans;
    color: #fff;
    display: none;
  }

  &:hover {
    .highlight-information {
      display: inline-block;
    }
  }
}

.section-content {
  > *:not(.highlight-container) {
    padding-left: 30px;
  }
}

.smart-highlight-item {
  position: relative;
  cursor: pointer;

  .highlight-container {
    box-shadow: none;
    max-height: 112px;
    overflow: hidden;
  }

  .smart-highlight-item-content {
    box-shadow: none;
    overflow-y: clip;
    position: relative;
  }

  .highlight-paragraph {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .highlight-information {
    position: absolute;
    top: -34px;
    left: 0;
    padding: 5px;
    background: #FF6700;
    border-radius: 3px 3px 0 0;
    font: normal normal 600 12px/24px Open Sans;
    color: #fff;
    display: none;
  }

  &:hover {
    max-height: 140px;

    .highlight-container {
      max-height: none;
      z-index: 99;
      overflow-y: overlay;
      background: #fff;
    }

    .smart-highlight-item-content {
      max-height: none;
      box-shadow: 0 5px 15px #00000029;
      overflow-y: overlay;
      background: #fff;
      z-index: 99;
    }

    .highlight-information {
      display: inline-block;
      z-index: 999;
    }
  }
}
