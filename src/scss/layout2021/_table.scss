@import "bootstrap/scss/variables";

.table {
  thead > tr > th {
    min-height: $table-header-height;
    background: $table-header-background;
    color: $table-header-color;
    font-size: $table-header-size;
    border-top: none;
    border-bottom: none;
    vertical-align: middle;
  }

  tbody > tr {
    &:hover {
      transition:  background-color .2s;
    }
    > td {
      height: $table-body-height;
      color: $table-body-color;
      font-size: $table-body-size;
      letter-spacing: 0;
      border-top: none;
      border-bottom: 1px solid $table-row-border-color;
      background-color:white;
    }
  }
  tbody+tbody {
    border-top: 2px solid #dee2e6
  }

  .patent-detail {
    background-color: white;
    border-top: 1px solid #dee2e6;
    .detail-title {
      color: #0F2C35;
      font-size: 18px;
      font-family: $font-open-sans-bold;
      text-align: start;
    }

    .detail-content {
      width: 100%;
      color: #4B5D66;

      &.ipc-content, &.cpc-content, &.inventors-content {
        width: 100%;
        word-break: break-word;
      }

      &.family-content {
        word-break: break-word;
        width: 100%;
        color: #389A85;

        a {
          color: #389A85;
        }

        .download-pdf-icon {
          color: #BD4A54;
          font-size: 1em;
          margin-left: 3px;
          cursor: pointer;
        }

        .spinner-pdf {
          margin-left: 3px;
          max-width: 12px;
        }
      }
    }

    .abstract-source {
      color: #BCCACE;
      font-size: 14px;
      padding-top: 5px;
    }
  }

  &.table-hover {
    > tbody > tr:hover > * {
      --#{$prefix}table-accent-bg: #F8F8F8;
    }
  }
}
