@import 'scss/figma2023/variables';

.green-patent-description-tooltip {
  .tooltip-inner {
    max-width: 900px !important;
    font-size: 13px;
  }

  ul {
    padding-left: 15px;
  }

  .categories-text {
    display: flex;
    border-top: 1px solid;
    padding-top: .5rem;
  }
}

.tooltip {
  &.show {
    opacity: 1;
    z-index: 9999;
  }

  .tooltip-inner {
    padding: 0.5rem;
    opacity: 1;
    background-color: #FFFEFD !important;
    color: #434343 !important;
    box-shadow: 0px 10px 15px -3px rgba(40, 40, 40, 0.10), 0px 0px 6px 0px rgba(40, 40, 40, 0.25) !important;
  }

  &.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: #FFFEFD !important;
  }

  &.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: #FFFEFD !important;
  }

  &.bs-tooltip-left, &.bs-tooltip-start {
    .tooltip-arrow::before {
      border-left-color: #FFFEFD !important;
    }
  }

  &.bs-tooltip-right, &.bs-tooltip-end {
    .tooltip-arrow::before {
      border-right-color: #FFFEFD !important;
    }
  }

  &.tooltip-text-only {
    .tooltip-inner {
      font-size: 0.75rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1rem;
      padding: 0.5rem;
    }
  }

  &.tooltip-large {
    .tooltip-inner {
      min-width: 25rem !important;
      max-width: 35rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }

  &.tooltip-medium {
    .tooltip-inner {
      min-width: 22rem !important;
      max-width: 22rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }

  &.tooltip-modal {
    .tooltip-inner {
      width: 21.5rem !important;
      max-width: 21.5rem !important;
      max-height: 19.5rem !important;
      padding: $spacing-system-spacing-big $spacing-system-spacing-md;
      display: block;
    }
  }
}
