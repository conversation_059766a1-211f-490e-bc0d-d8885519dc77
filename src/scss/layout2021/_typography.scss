.open-sans-regular {
  font-family: $font-open-sans-regular;
}

.open-sans-bold {
  font-family: $font-open-sans-bold;
}

.open-sans-boldItalic {
  font-family: $font-open-sans-bold-italic;
}

.open-sans-extra-bold {
  font-family: $font-open-sans-extra-bold;
}

.open-sans-extra-bold-italic {
  font-family: $font-open-sans-extra-bold-italic;
}

.open-sans-extra-italic {
  font-family: $font-open-sans-italic;
}

.open-sans-extra-light {
  font-family: $font-open-sans-light;
}

.open-sans-extra-light-italic {
  font-family: $font-open-sans-light-italic;
}

.open-sans-semi-bold {
  font-family: $font-open-sans-semi-bold;
}

.open-sans-semi-bold-italic {
  font-family: $font-open-sans-semi-bold-italic;
}


/* Define Headers */

h1,
.headline-1 {
  font-family: $font-open-sans-semi-bold;
  font-size: 3rem;
  line-height: 72px;
  margin-bottom: 72px;
  color: $color-text-04;
}

h2,
.headline-2 {
  font-family: $font-open-sans-semi-bold;
  font-size: 2rem;
  line-height: 48px;
  margin-bottom: 48px;
  color: $color-text-04;
}

h3,
.headline-3 {
  font-family: $font-open-sans-light;
  font-size: 2.25rem;
  line-height: 54px;
  margin-bottom: 54px;
  color: $color-text-04;
}

h4,
.headline-4 {
  font-family: $font-open-sans-bold;
  font-size: 1.5rem;
  line-height: 36px;
  margin-bottom: 36px;
  color: $color-text-04;
}

h5,
.headline-5 {
  font-family: $font-open-sans-semi-bold;
  font-size: 1.25rem;
  line-height: 30px;
  margin-bottom: 30px;
  color: $color-text-04;
}

h6,
.headline-6 {
  font-family: $font-open-sans-regular;
  font-size: 1.125rem;
  line-height: 27px;
  margin-bottom: 27px;
  color: $color-text-04;
}


/* Paragraphs */

.paragraph-1 {
  font-family: $font-open-sans-regular;
  font-size: 1.125rem;
  line-height: 27px;
  margin-bottom: 27px;
  color: $color-text-04;
}

.paragraph-2 {
  font-family: $font-open-sans-regular;
  font-size: 1rem;
  line-height: 24px;
  margin-bottom: 24px;
  color: $color-text-04;
}

.paragraph-3 {
  font-family: $font-open-sans-regular;
  font-size: 0.875rem;
  line-height: 21px;
  margin-bottom: 21px;
  color: $color-text-04;
}

.caption-1 {
  font-family: $font-open-sans-regular;
  font-size: 0.875rem;
  line-height: 21px;
  margin-bottom: 21px;
  color: $color-text-04;
}

.caption-2 {
  font-family: $font-open-sans-regular;
  font-size: 0.75rem;
  line-height: 18px;
  margin-bottom: 18px;
  color: $color-text-04;
}

/* Custom headline */
.underline-heading {
  font-family: $font-open-sans-light;
  font-size: 36px;
  line-height: 100%;
  padding-bottom: 20px;
  border-bottom: 1px solid $brand-gray;
  margin-bottom: 20px;
  color: $color-text-03;
}

.text {
  &-green {
    color: $brand-green;
  }

  &-orange {
    color: $brand-orange;
  }

  &-blue {
    color: $brand-blue;
  }

  &-gray {
    color: $brand-gray;
  }
}

.color- {
  &default {
    color: $font-color-default;
  }

  &1 {
    color: $color-text-01;
  }

  &2 {
    color: $color-text-02;
  }

  &3 {
    color: $color-text-03;
  }

  &4 {
    color: $color-text-04;
  }

  &5 {
    color: $color-text-05;
  }
}

.font {
  &-size {
    &-1 {
      font-size: 3rem;
      line-height: 3rem;
    }

    &-2 {
      font-size: 2rem;
      line-height: 2rem;
    }

    &-3 {
      font-size: 2.25rem;
      line-height: 2.25rem;
    }

    &-4 {
      font-size: 1.5rem;
      line-height: 1.5rem;
    }

    &-5 {
      font-size: 1.25rem;
      line-height: 1.25rem;
    }

    &-6 {
      font-size: 1.125rem;
      line-height: 1.125rem;
    }

    &-7 {
      font-size: 1rem;
      line-height: 1rem;
    }

    &-8 {
      font-size: .875rem;
      line-height: .875rem;
    }
  }
}

.new-layout-headline {
  color: $brand-dark;
  font-family: $font-open-sans-semi-bold;
  font-size: 1.125rem;
  margin: 1.6rem 0 1rem;
}
