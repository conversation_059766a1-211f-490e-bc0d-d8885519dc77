.cursor-pointer {
  cursor: pointer !important;
}
.cursor-default {
  cursor: default !important;
}

.dot {
  width: 14px;
  height: 14px;
  display: inline-block;
  border-radius: 50%;
  border: 2px solid;

  &-primary {
    background: #00D464;
    border-color: #00D464;

    &-1 {
      background: #00D46460;
      border-color: #00D464;
    }
  }

  &-danger {
    background: #FF4D55;
    border-color: #FF4D55;

    &-1 {
      background: #FF4D5560;
      border-color: #FF4D55;
    }
  }

  &-light {
    background: #95A5AB;
    border-color: #95A5AB;

    &-1 {
      background: #95A5AB20;
      border-color: #95A5AB;
    }
  }

  &-mix {
    display: inline-block;
    border-radius: 50%;
    border-right-color: #00D464;
    border-top-color: #FF4D55;
    border-bottom-color: #00D464;
    border-left-color: #FF4D55;
    border-width: 7px;
    border-style: solid;
    height: 0px;
    width: 0px;
    transform: rotate(-90deg);
  }

  &-pending {
    background: #FFEBB9;
    border-color: #FBD166;
  }
}

.background-light-gray {
  background-color: $chart-section-background;
}

.height-auto {
  height: auto;
}

.border-b-1 {
  border-bottom: 1px solid #CBDCE2;
}

.combined-mode-layout {
  display: block;
  max-width: 1738px;
  margin: 0 auto;

  .tools-bar-as-dropdown {
    .form-control {
      color: $light-green-color;
    }
  }

  .combined-list-section, .combined-chart-section {
    vertical-align: top;
    display: inline-block;
  }

  .combined-list-section {
    width: 66%;
  }

  .combined-chart-section {
    width: 34%;

    .charts-container {
      display: block;
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 5px;
      min-height: 550px;
    }
  }

  .chart-item {
    .chart-header {
      background-color: $scrollbar-background;
    }
  }

  .big-chart-container {
    flex-direction: column;

    > .controls {
      order: unset;
      padding-top: 0;
      width: 100%;
    }
  }

  @media (max-width: 1600px) {
    .chart-content {
      min-width: unset;
    }
  }
  @media (max-width: 1200px) {
    &, .table tbody > tr > td, .table thead > tr > th {
      font-size: 90%;
    }
    .table th, .table td {
      padding: .25rem;
      vertical-align: middle;
    }
  }
}

$values: 'x', 'y';

.overflow-{
  @each $v in $values {
    &#{$v}-auto {
      overflow-#{$v}: auto;
    }
  }
}

.text-ellipsis {
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  &.d-block {
    display: block !important;
  }

  @for $i from 1 through 10 {
    &-#{$i} {
      -webkit-line-clamp: $i;
    }
  }
}

$text-alignments: 'start', 'end', 'left', 'right', 'justify', 'center';
@each $v in $text-alignments {
  .text-#{$v} {
    text-align: #{$v};
  }
}
