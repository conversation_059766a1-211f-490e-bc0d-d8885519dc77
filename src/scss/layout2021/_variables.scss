
@use "sass:math";

@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
/* New variables */

// Gird system
$columns-sm: 4;
$columns-md: 8;
$columns-lg: 12;
$grid-sm-size: math.div(100, $columns-sm) * 1%;
$grid-md-size: math.div(100, $columns-md) * 1%;
$grid-lg-size: math.div(100, $columns-lg) * 1%;
// Fonts
$font-open-sans-regular: 'Open Sans Regular';
$font-open-sans-bold: 'Open Sans Bold';
$font-open-sans-bold-italic: 'Open Sans Bold Italic';
$font-open-sans-extra-bold: 'Open Sans Extra Bold';
$font-open-sans-extra-bold-italic: 'Open Sans Extra Bold Italic';
$font-open-sans-italic: 'Open Sans Italic';
$font-open-sans-light: 'Open Sans Light';
$font-open-sans-light-italic: 'Open Sans Light Italic';
$font-open-sans-semi-bold: 'Open Sans Semi Bold';
$font-open-sans-semi-bold-italic: 'Open Sans Semi Bold Italic';
$font-color-default: #698A95;
$font-family-base: $font-open-sans-regular;
// Text colors
$color-text-01: #FFF;
$color-text-02: #738993;
$color-text-03: #4B5D66;
$color-text-04: #0F2C35;
$color-text-05: #FF6700;
// Brand colors
$brand-green: #00a083;
$brand-green-hover:#2F8A76;
$brand-green-pressed: #236B5B;
$brand-green-selected: #4B5D66;
$brand-orange: #FF6700;
$brand-orange-hover: #FF5122;
$brand-orange-pressed: #FF5122;
$brand-orange-selected: #FF670026;
$brand-blue: #005372;
$brand-blue-hover: #004964;
$brand-blue-pressed: #003A50;
$brand-dark: #0F2C35;
$brand-gray: #BCCACE;
$brand-gray-hover:#a4b2b6;
$brand-gray-pressed: #738993;
$brand-gray-selected: #4B5D66;
$disabled-color: #BCCACE;
$border-default: #BCCACE;

$primary: $brand-green;

/* Customization */

$container-max-widths: ( xs: 360px, sm: 640px, md: 720px, lg: 960px, xl: 1140px );
$font-family-base: 'Open Sans Regular';
$red: #FF4D55;
$blue-light: #4698b4;
$link-decoration: none;
$link-color: #00A083;
$link-hover-color: #2F8A76;
$input-border-color: #CBDCE2;
$input-focus-border-color: #80bdff;
$input-placeholder-color: #698A95;
$input-color: #0F2C35;
$input-background-color: #F5F7FA;
$input-font-size: 16px;
$dropdown-link-hover-color:#389A85 !important;
$autocomplete-spinner-color:#389A85;

/* Components */

// Alerts
$alert-info-background: #00C4FF26;
$alert-info-border-color: #00C4FF;
$alert-info-text: #006D8E;
$alert-info-close: $brand-green-pressed;
$alert-warning-background: #FF944D26;
$alert-warning-border-color: #FF944D;
$alert-warning-text: #FF944D;
$alert-warning-close: $brand-green;
$alert-success-background: #00D46426;
$alert-success-border-color: #00D464;
$alert-success-text: $brand-green-pressed;
$alert-success-close: $brand-green-pressed;
$alert-danger-background: #FF4D5526;
$alert-danger-border-color: #FF4D55;
$alert-danger-text: #FF4D55;
$alert-danger-close: $brand-green-hover;
$alert-title-size: 1.125rem;
$alert-text-size: 0.875rem;
// Buttons
$button-color-disabled: #FFF;
$button-color-pressed: #FFF;
$button-background-outline-disabled: transparent;
$button-color-outline-disabled: $disabled-color;
$button-border-outline-disabled: 1px solid $disabled-color;
$button-border-shadow: 0px 5px 15px #0F2C353F;
$button-size: 0.75rem;
$button-sm-size: 0.75rem;
$button-md-size: 0.875rem;
$button-lg-size: 1rem;
$button-xl-size: 1.125rem;
// Carousel
$carousel-box-shadow: 0px 3px 9px #00000008;
$carousel-border-default: 1px solid #ECECEC;
$carousel-border-active: 2px solid $brand-green-pressed;
$carousel-border-hover: 2px solid $brand-green;
$carousel-border-radius: 3px;
// Check On/Off
$check-on-off-background-color: #FFF;
$check-on-off-border-color: #738993;
$check-on-off-background-color-off: #738993;
$check-on-off-background-color-on: #389A85;
// Checkbox
$checkbox-border: 1px solid #738993;
$checkbox-background-color-checked: #389a85;
$checkbox-color-checked: #FFF;
// Dropdown
$dropdown-border-color: #CED4DA;
$dropdown-border-active-color: #389A85;
$dropdown-outline: 1px solid #389A85;
$dropdown-icon-color: $input-color;
$dropdown-item-background: #F5F7FA;
$dropdown-link-hover-color:#389A85 !important;
$dropdown-link-active-color: #F5F7FA;
$dropdown-link-active-background: #389A85;
// Forms
$label-color: #698A95;
$label-size: 0.875rem;
$invalid-color: $red;
$placeholder-size: 1rem;
// Menu
$menu-color: #F5F5F5;
$menu-size: 1.1rem;
//hr
$hr-border-color: rgba(0,0,0,.1);
// Modal
$modal-title-family: $font-open-sans-bold;
$modal-title-size: 1.5rem;
$modal-footer-background: #F5F7FA;
$modal-body-padding: $modal-inner-padding;
// Nav
$nav-background-color: #F1F1F1;
$navbar-padding-x: 1rem;
// Package selection
$package-width: 260px;
$package-height: 535px;
$package-border: 1px solid $border-default;
$package-background: #FFF;
// Pagination
$pagination-color: #389A85;
$pagination-background: #FFF;
$pagination-color-hover: #FFF;
$pagination-background-hover: #389A85;
$pagination-current-color: #FFF;
$pagination-current-background: #BCCACE;
$pagination-color-focus: #FFF;
$pagination-background-focus: $brand-green-pressed;
$pagination-size: 1rem;
// Scrollbar
$scrollbar-background: #E8F0F3;
$scrollbar-background-thumb: #BCCACE;
$scrollbar-background-hover: #698A95;
// Segment control
$segment-control-color: $font-color-default;
$segment-control-border: #698A95 1px solid;
$segment-control-background-hover: $brand-green-hover;
$segment-control-color-hover: #FFF;
$segment-control-background-active: $brand-green;
$segment-control-color-active: #FFF;
$segment-control-background-focus: $brand-green-pressed;
$segment-control-color-focus: #FFF;
$segment-control-size: 0.875rem;
// Single card
$single-card-height: 250px;
$single-card-border: 1px solid rgba($border-default, .5);
$single-card-background-color: #FFF;
$single-card-icon-color: $brand-green;
$single-card-icon-border-hover: 1px solid $brand-green;
$single-card-icon-color-focus: #FFF;
$single-card-icon-background: $brand-green-pressed;
$single-card-footer-height: 47px;
$single-card-footer-background: #F5F7FA;
$single-card-body-text-color: #698A95;
// Slider
$slider-background: #FFF;
$slider-circle: $brand-green;
$slider-circle-hover: $brand-green-hover;
$slider-circle-focus: $brand-green-pressed;
// Table
$table-header-background: #E8F0F3;
$table-header-color: #0F2C35;
$table-header-size: 1rem;
$table-header-height: 49px;
$table-body-color: #4B5D66;
$table-body-size: 1rem;
$table-body-height: 52px;
$table-row-border-color: #BCCACE;

// Color palette
$light-orange-color: #cf7900;
$dark-orange-color: #af6800;
$light-blue-color: #4696b2;
$lighter-blue-color: #69B9D5;
$dark-blue-color : #01526f;
$light-green-color: #389A85;
$light-gray-color: #B3BAC5;
$light-red-color: #f53d00;
$chart-section-background: #F8FBFC;

// colors
$theme-colors: (
  "primary":    $brand-green,
  "secondary":  $brand-orange,
  "success":    $success,
  "info":       $info,
  "warning":    $warning,
  "danger":     $danger,
  "light":      $light,
  "dark":       $dark
);

$colors: (
  "blue":       $blue,
  "indigo":     $indigo,
  "purple":     $purple,
  "pink":       $pink,
  "red":        $red,
  "orange":     $orange,
  "yellow":     $yellow,
  "green":      $green,
  "teal":       $teal,
  "cyan":       $cyan,
  "black":      $black,
  "white":      $white,
  "gray":       $gray-600,
  "gray-dark":  $gray-800
)
