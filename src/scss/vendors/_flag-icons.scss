@import 'scss/figma2023/index';

.fi{
    line-height: 1.125rem;
}

.flag-icon-border{
    border-radius: 2px;
    background-size: cover;
    border: 0.5px solid $colours-border-subtle;
    width: 1.5rem;
    height: 1.125rem;
}

.flag-authority-group{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: $spacing-system-spacing-x-s;
}

.flag-tag-authority{
    border: 0.5px solid $colours-border-subtle;
    border-radius: $radius-sm;
    padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-sm;
    background-color: $colours-background-bg-secondary;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: $spacing-system-spacing-xx-s;
    cursor: default;
    &:hover{
        border-color: $colours-border-bold;
        background-color: $colours-background-bg-tertiary;
    }

    @extend .content-label-small;
}