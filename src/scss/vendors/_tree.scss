@import '@ali-hm/angular-tree-component/css/angular-tree-component.css';
@import 'scss/layout2021/variables';

.modal-body:not(.multiple-node-selection) .node-content-wrapper,
.modal-body:not(.multiple-node-selection) .tree-children,
.modal-body:not(.multiple-node-selection) .tree-node {
  position: relative;
}

.angular-tree-component {
  position: relative;
  overflow: hidden;
  padding-left: 25px;
}

.tree-node {

  &::before,
  &::after {
    content: "";
    position: absolute;
    left: -10px;
  }

  &.tree-node-level-1 {
    &::after,
    &::before {
      display: none;
    }
  }

  &.tree-node-expanded.tree-node-has-children > tree-node-wrapper > .node-wrapper > .node-content-wrapper {
    &::before {
      content: "";
      position: absolute;
      border-left: 1px solid #BCCACE;
      left: -11px;
      height: 100%;
      top: 18px;
    }
  }

  &::before {
    border-left: 1px solid #BCCACE;
    width: 1px;
    height: 100%;
    top: -5px;
  }

  tree-node:last-child > .tree-node::before {
    height: 18px;
  }

  &::after {
    border-bottom: 1px solid #BCCACE;
    width: 12px;
    top: 12px;
  }

  .toggle-children-wrapper-collapsed {
    .toggle-children {
      background-image: none;
      width: 15px;
      height: 15px;
      content: url("/assets/images/plus-square-solid.svg");
    }
  }

  .toggle-children-wrapper-expanded {
    .toggle-children {
      background-image: none;
      width: 15px;
      height: 15px;
      transform: none;
      content: url("/assets/images/minus-square-regular.svg");
    }
  }

  tree-node-collection > div {
    margin-top: 5px !important;
  }

  .tree-children {
    padding-left: 18px;
  }
}
.multiple-select{
  position: absolute;
  left: 0;
  span{
    padding-left: 18px !important;
    &:before {
      content: "";
      position: absolute;
      top: 0.275rem;
      left: 0.1875rem;
      width: 18px;
      height: 18px;
      border: $checkbox-border;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px;
    }
    &.completely-selected:after, &.partially-selected:after {
      font-family: 'Font Awesome 6 Pro', serif;
      position: absolute;
      font-size: 0.875rem;
      font-weight: 900;
      -webkit-border-radius: 3px;
      -moz-border-radius: 3px;
      border-radius: 3px
    }
    &.completely-selected:after {
        content: "\f00c";
        background: $checkbox-background-color-checked;
        color: $checkbox-color-checked;
        top: 0.275rem;
        left: 0.1875rem;
        width: 18px;
        height: 18px;
        padding-left: 0.125rem;
    }
    &.partially-selected:after {
      content: " ";
      background: $checkbox-background-color-checked;
      width: 10px;
      height: 10px;
      padding: 0;
      top: 8px;
      left: 7px;
    }
  }
}
tree-node-wrapper.selected-node-wrapper .node-content-wrapper{
  background-color: rgba(188, 202, 206, .2);
  border-radius: 5px;
}
