import { ComponentFixture } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

/**
 * Jest testing utilities for Angular components
 */

/**
 * Get element by CSS selector
 */
export function getElement<T>(fixture: ComponentFixture<T>, selector: string): HTMLElement | null {
  return fixture.nativeElement.querySelector(selector);
}

/**
 * Get all elements by CSS selector
 */
export function getAllElements<T>(fixture: ComponentFixture<T>, selector: string): HTMLElement[] {
  return Array.from(fixture.nativeElement.querySelectorAll(selector));
}

/**
 * Get debug element by CSS selector
 */
export function getDebugElement<T>(fixture: ComponentFixture<T>, selector: string): DebugElement | null {
  return fixture.debugElement.query(By.css(selector));
}

/**
 * Get all debug elements by CSS selector
 */
export function getAllDebugElements<T>(fixture: ComponentFixture<T>, selector: string): DebugElement[] {
  return fixture.debugElement.queryAll(By.css(selector));
}

/**
 * Click element by selector
 */
export function clickElement<T>(fixture: ComponentFixture<T>, selector: string): void {
  const element = getElement(fixture, selector);
  if (element) {
    element.click();
    fixture.detectChanges();
  }
}

/**
 * Set input value
 */
export function setInputValue<T>(fixture: ComponentFixture<T>, selector: string, value: string): void {
  const input = getElement(fixture, selector) as HTMLInputElement;
  if (input) {
    input.value = value;
    input.dispatchEvent(new Event('input'));
    fixture.detectChanges();
  }
}

/**
 * Trigger event on element
 */
export function triggerEvent<T>(fixture: ComponentFixture<T>, selector: string, eventType: string, eventData?: any): void {
  const element = getElement(fixture, selector);
  if (element) {
    const event = new Event(eventType);
    if (eventData) {
      Object.assign(event, eventData);
    }
    element.dispatchEvent(event);
    fixture.detectChanges();
  }
}

/**
 * Wait for async operations to complete
 */
export async function waitForAsync<T>(fixture: ComponentFixture<T>): Promise<void> {
  fixture.detectChanges();
  await fixture.whenStable();
  fixture.detectChanges();
}

/**
 * Mock component for testing
 */
export function createMockComponent(selector: string, inputs: string[] = [], outputs: string[] = []) {
  const mockComponent = class MockComponent {};
  
  // Add component decorator properties
  Object.defineProperty(mockComponent, 'selector', { value: selector });
  Object.defineProperty(mockComponent, 'template', { value: '' });
  Object.defineProperty(mockComponent, 'inputs', { value: inputs });
  Object.defineProperty(mockComponent, 'outputs', { value: outputs });
  
  return mockComponent;
}

/**
 * Create spy object with methods
 */
export function createSpyObj(baseName: string, methodNames: string[]): any {
  const obj: any = {};
  methodNames.forEach(methodName => {
    obj[methodName] = jest.fn();
  });
  return obj;
}

/**
 * Advance timers for testing
 */
export function advanceTimers(ms: number): void {
  jest.advanceTimersByTime(ms);
}

/**
 * Mock HTTP response
 */
export function mockHttpResponse(data: any, status: number = 200) {
  return {
    status,
    body: data,
    headers: new Headers(),
    ok: status >= 200 && status < 300,
    statusText: status === 200 ? 'OK' : 'Error',
    url: '',
    type: 'basic' as ResponseType,
    redirected: false,
    clone: jest.fn(),
    arrayBuffer: jest.fn(),
    blob: jest.fn(),
    formData: jest.fn(),
    json: jest.fn().mockResolvedValue(data),
    text: jest.fn().mockResolvedValue(JSON.stringify(data)),
  };
}

/**
 * Common test setup for Angular components
 */
export interface TestSetup<T> {
  component: T;
  fixture: ComponentFixture<T>;
  element: HTMLElement;
}

export function setupComponent<T>(componentClass: any, testBed: any): TestSetup<T> {
  const fixture = testBed.createComponent(componentClass);
  const component = fixture.componentInstance;
  const element = fixture.nativeElement;
  
  fixture.detectChanges();
  
  return { component, fixture, element };
}
