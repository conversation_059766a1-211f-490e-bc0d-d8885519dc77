{"compileOnSave": false, "compilerOptions": {"baseUrl": "src", "downlevelIteration": true, "importHelpers": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2020", "moduleResolution": "node", "experimentalDecorators": true, "target": "ES2022", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "typeRoots": ["node_modules/@types"], "lib": ["es2020", "dom"], "paths": {"@api/*": ["app/api/*"], "@applicant-aliases/*": ["app/applicant-aliases/*"], "@auth/*": ["app/auth/*"], "@boolean/*": ["app/boolean/*"], "@citation/*": ["app/citation/*"], "@collections/*": ["app/collections/*"], "@core": ["app/core"], "@core/*": ["app/core/*"], "@error/*": ["app/error/*"], "@help/*": ["app/help/*"], "@history-saves/*": ["app/history-saves/*"], "@landscape/*": ["app/landscape/*"], "@monitor/*": ["app/monitor/*"], "@patent/*": ["app/patent/*"], "@profile/*": ["app/profile/*"], "@search/*": ["app/search/*"], "@subscription/*": ["app/subscription/*"], "@users/*": ["app/users/*"], "@shared/*": ["app/shared/*"]}, "useDefineForClassFields": false}}